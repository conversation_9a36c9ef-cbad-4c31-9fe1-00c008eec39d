# 人力资源服务平台

## 安装依赖

-   使用 npm install 不要使用 yarn 会导致 360、搜狗等浏览器弹窗无法使用

## 发版
### 测试环境 
- 发版 node build/deploy.ts integration

### 正式环境
- 修改src/config/index.ts的isEncrypt为true
- 打包 npm run build-prod 
- 将打包后的文件夹 `hr-admin-prod` 中 config 的 window.config = getConfig['DEV_3_4_6'] 改成 window.config = getConfig['RELEASE']  打包成压缩包发给后端 让后端发版
1. 登录到服务器，rz 压缩包上传到 /opt/hr/server/ 目录下 
2. unzip hr-admin-prod.zip 
3. mv admin admin-bak
4. mv hr-admin-prod admin 

### 重要提醒
-   要更改发版的后端服务地址只需要更改 `.env.integration` 文件下的 `VITE_BASE_URL` 即可
-   `.env.integration` 文件下的 `VITE_OUT_DIR` 属性为打包后的文件名 正式版的打包文件名不要更改
    因为 ：小程序要访问 hr-admin-prod 文件夹下的页面 所以：修改 config.js 文件

### start
-   yarn bootstrap
-   yarn dev

### build

-   yarn build

### 主要技术栈

-   [Vue3](https://v3.vuejs.org/) 其中部分 SFC 使用到了[ setup sugar ](https://v3.vuejs.org/api/sfc-script-setup.html)如果编译器用的 vscode 需要 卸载 vetur 安装插件 [volar](https://marketplace.visualstudio.com/items?itemName=johnsoncodehk.volar)
-   [Vite](https://vitejs.dev/) 下一代前端开发与构建工具
-   UI框架：https://2x.antdv.com/docs/vue/getting-started-cn/
### 发版正式

--- build 打完包 后更改 3.4 的 config.js 的路径 window.config = getConfig['DEV_3_4'] 改成 RELEASE
