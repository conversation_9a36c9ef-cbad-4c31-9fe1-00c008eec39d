<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="google" content="notranslate">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <META HTTP-EQUIV="pragma" CONTENT="no-cache">
  <META HTTP-EQUIV="Cache-Control" CONTENT="no-cache, must-revalidate">
  <META HTTP-EQUIV="expires" CONTENT="0">
  <title>核验结果</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .body {
      margin-top: 200px;
    }

    .text {
      text-align: center;
      font-size: 13px;
      line-height: 30px;
    }

    .promptPicture {
      width: 80vw;
      margin-bottom: 30px;
      font-size: 16px;
    }
  </style>
</head>

<body class="body">
  <div class="text">
    <img class="promptPicture" src="./img/promptPicture.png" />
    <p>请返回小程序，点击验证按钮</p>
  </div>
  <script src="../upload/jiaoben2645/js/jquery-2.0.3.min.js" type="text/javascript"></script>
  <script src="../config.js"></script>
  <script>
    $(document).ready(function () {
      var url = location.href //https://www.xxxx.html?param=aaa&signId=11211
      params = url.split("?")[1].split("&")
      var dic_param = {}
      for (var index = 0; index < params.length; index++) {
        var kv = params[index].split("=")
        dic_param[kv[0]] = kv[1]
      }
      console.log(dic_param)
      trace(dic_param)
      // trace("初始化方法进入");
    });
//dev-1.0
    function getUrl() {
      var lastStr = '/api/save-face-verify-info'
      // alert(window.config.baseUrl)
      // if (window.config.baseUrl) {
        return window.config.baseUrl + lastStr
      // } else return 'https://hr-server.cas-air.cn/dev-1.0' + lastStr
    }

    function trace(obj) {
      $.ajax({
        url: getUrl(),
        type: 'POST',
        async: false, //同步请求，否则内部嵌套的ajax不会运行
        data: JSON.stringify(obj),
        dataType: 'json',
        contentType: 'application/json;charset=UTF-8',
        processData: false,
        dataType: 'json',
        headers: {
          'isEncrypt': "no",
        },
        success: function (data) {
          //更新数据库添入附件的url
          console.log('url地址：' + data)
        },
        error: function (err) {
          console.log('这是请求失败的', err)
        },
      })
    }
  </script>
</body>

</html>