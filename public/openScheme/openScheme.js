function getUrlScheme(miniPath) {
    const sessionCookie = getCookie('uuid')
    let uuid = ''
    if (!sessionCookie) {
        uuid = localStorage.getItem('uuid')
        if (!uuid) {
            uuid = uuidV4() // uuid
            localStorage.setItem('uuid', uuid)
        }
        const effectiveTimeLen = 20 * 24 * 60 * 60 * 1000 // cookie过期时间20天
        const expiresTime = new Date(Date.now() + effectiveTimeLen)
        const cookieDomain = window.location.hostname.split('.').slice(-2).join('.')
        document.cookie = `uuid=${uuid};expires=${expiresTime};path=/;domain=.${cookieDomain}`
    }

    // 请求后台接口获取URL Scheme
    return new Promise((resolve, reject) => {
        // 正式版 release 体验版 trial 开发版 develop
        $.ajax({
            url: `${window?.config?.baseUrl}/api/get-applet-scheme-code?envVersion=release`,
            type: 'GET',
            async: false,
            headers: {
                isEncrypt: 'no',
            },
            data: {},
            dataType: 'json',
            success: function (data) {
                resolve(data)
            },
            error: function (err) {
                reject(err)
            },
        })
    })
}

function uuidV4() {
    var t = Date.now()
    return (
        'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = (Math.random() * 16) | 0,
                v = c == 'x' ? r : (r & 0x3) | 0x8
            return v.toString(16)
        }) +
        '-' +
        t
    )
}

const getCookie = (name) => {
    const strCookie = document.cookie
    const arrCookie = strCookie.split('; ')
    for (let i = 0; i < arrCookie.length; i++) {
        const arr = arrCookie[i].split('=')
        if (arr[0] === name) {
            return arr[1]
        }
    }
    return ''
}
