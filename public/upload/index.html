<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>上传文件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .body {
            margin-top: 200px;
        }

        .text {
            text-align: center;
            font-size: 13px;
            line-height: 30px;
        }

        .head-btn {
            text-align: center;
            padding-top: 20px;
        }

        .file {
            position: relative;
            display: inline-block;
            background: #d0eeff;
            border: 1px solid #99d3f5;
            border-radius: 10px;
            padding: 10px 70px;
            overflow: hidden;
            color: #1e88c7;
            text-decoration: none;
            text-indent: 0;
            line-height: 20px;
            font-size: 16px;
        }

        .file input {
            position: absolute;
            /* font-size: 200px; */
            right: 0;
            top: 0;
            opacity: 0;
            height: 40px;
            width: 200px;
        }

        .file:hover {
            background: #aadffd;
            border-color: #78c3f3;
            color: #004974;
            text-decoration: none;
        }

        .determine {
            color: #ffffff;
            background-color: #007aff;
            display: inline-block;
            font-size: 20px;
            border-radius: 5px;
            padding: 8px 24px;
        }

        .showFileName {
            display: inline-block;
            height: 30px;
            min-width: 300px;
        }

        .btn {
            display: block;
            margin: 10px auto;
            padding: 5px;
            background-color: #007aff;
            border: 0;
            color: #ffffff;
            height: 40px;
            width: 200px;
            border-radius: 5px;
        }

        .btn-red {
            background-color: #dd524d;
        }

        .btn-yellow {
            background-color: #f0ad4e;
        }

        .desc {
            padding: 10px;
            color: #999999;
        }

        .body {
            background-image: url(../../static/timg.jpg);
        }

        #shclKeyframesBox {
            width: 100vw;
            height: 100vh;
            position: fixed;
            top: 0;
            background-color: rgba(9, 9, 9, .1);
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none;
        }

        #shclKeyframes {
            width: 100px;
            height: 100px;
        }
    </style>
</head>

<body class="body">
    <div id="shclKeyframesBox" style="display: none;" onclick="shclKeyframesBoxFun">
        <div id="shclKeyframes"></div>
    </div>

    <div class="text">
        <p>文件最大不能超过50MB</p>
        <p>文件将上传到云端处理，请保持网络流畅</p>
    </div>
    <div class="head-btn">
        <form action="" method="post">
            <a href="javascript:;" class="file">选择文件
                <input type="file" name="uploadFile" id="uploadFile" />
            </a>
        </form>
        <p class="showFileName"></p>
    </div>
    <div>
        <button class="btn" type="button" data-action="redirectTo">确定</button>
    </div>

    <script src="jiaoben2645/js/jquery-2.0.3.min.js" type="text/javascript"></script>
    <script src="jiaoben2645/js/jquery.shCircleLoader.js" type="text/javascript"></script>
    <!-- <script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script> -->
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
    <script src="../config.js"></script>
    <script>
        var url = location.href //https://www.xxxx.html?param=aaa&signId=11211
        var dic_param = {}
        if (params = url.split("?")[1]) {
            params = url.split("?")[1].split("&")
            for (let index = 0; index < params.length; index++) {
                var kv = params[index].split("=")
                dic_param[kv[0]] = kv[1]
            }
        }

        function shclKeyframesBoxFun(e) {
            if (window.event) {
                e.cancelBubble = true;// ie下阻止冒泡
            } else {
                //e.preventDefault();
                e.stopPropagation();// 其它浏览器下阻止冒泡
            }
        }
        $('.file').on('change', "input[type='file']", function () {
            let files = $('#uploadFile')[0].files[0]
            // let filePath = $(this).val()
            // console.log(filePath);
            // localStorage.setItem('fileAddress', filePath)
            let lastname = files?.name
            if (lastname != '') {
                $('.showFileName').html(lastname)
            } else {
                $('.showFileName').html('')
            }
        })
        $('.btn').click(function (evt) {

            $("#shclKeyframes").shCircleLoader();
            var ajaxbg = $("#shclKeyframesBox");
            ajaxbg.show();  //显示
            let files = $('#uploadFile')[0].files[0]
            console.log(files)
            if (!$('#uploadFile')[0].files[0]) {
                alert('请先选择要上传的文件！')
                ajaxbg.hide();   //隐藏
                return
            }

            let specificFileType = dic_param.specificFileType ? JSON.parse(decodeURIComponent(dic_param.specificFileType)) : []
            if (specificFileType.length) {
                if (!specificFileType.includes(files.name.substring(files.name.lastIndexOf('.') + 1))) {
                    let typeName = this.fileType == 'all' ? '文件' : '图片'
                    let message = `您上传的文件类型不符合要求！`
                    alert(message)
                    ajaxbg.hide();   //隐藏
                    return;
                }
            }
            var formdata = new FormData() // 创建一个form类型的数据
            formdata.append('file', files) // 获取上传文件的数据
            $.ajax({
                url: (window?.config?.baseUrl ?? 'https://hr-server.cas-air.cn/dev-3.0') + '/api/hr-appendixes/upload-single-file',
                type: 'POST',
                async: false, //同步请求，否则内部嵌套的ajax不会运行
                processData: false,
                contentType: false,
                data: formdata,
                dataType: 'json',
                headers: {
                    'isEncrypt': "no",
                },
                success: function (data) {
                    //更新数据库添入附件的url
                    console.log('url地址：' + data)
                    ajaxbg.hide();   //隐藏
                    window.wx.miniProgram.getEnv(function (res) {
                        if (res.miniprogram) {
                            //如果当前是小程序环境
                            window.wx.miniProgram.postMessage({
                                data: data
                            })
                            // contractTemplate
                            window.wx.miniProgram.navigateBack()
                        }
                    })
                },
                error: function (err) {
                    ajaxbg.hide();   //隐藏
                    console.log('这是请求失败的')
                },
            })
        })

    </script>
</body>

</html>