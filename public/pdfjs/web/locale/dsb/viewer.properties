# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=PjerwjejÅ¡ny bok
previous_label=SlÄdk
next.title=PÅiducy bok
next_label=Dalej

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Bok
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=z {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} z {{pagesCount}})

zoom_out.title=PÃ³mjeÅÅ¡yÅ
zoom_out_label=PÃ³mjeÅÅ¡yÅ
zoom_in.title=PÃ³wÄtÅ¡yÅ
zoom_in_label=PÃ³wÄtÅ¡yÅ
zoom.title=SkalÄrowanje
presentation_mode.title=Do prezentaciskego modusa pÅejÅ
presentation_mode_label=Prezentaciski modus
open_file.title=Dataju wÃ³cyniÅ
open_file_label=WÃ³cyniÅ
print.title=ÅiÅ¡ÄaÅ
print_label=ÅiÅ¡ÄaÅ
download.title=ZeÅÄgnuÅ
download_label=ZeÅÄgnuÅ
bookmark.title=Aktualny naglÄd (kopÄrowaÅ abo w nowem woknje wÃ³cyniÅ)
bookmark_label=Aktualny naglÄd

# Secondary toolbar and context menu
tools.title=RÄdy
tools_label=RÄdy
first_page.title=K prÄdnemu bokoju
first_page.label=K prÄdnemu bokoju
first_page_label=K prÄdnemu bokoju
last_page.title=K slÄdnemu bokoju
last_page.label=K slÄdnemu bokoju
last_page_label=K slÄdnemu bokoju
page_rotate_cw.title=WobwjertnuÅ ako Å¡pÄra Åºo
page_rotate_cw.label=WobwjertnuÅ ako Å¡pÄra Åºo
page_rotate_cw_label=WobwjertnuÅ ako Å¡pÄra Åºo
page_rotate_ccw.title=WobwjertnuÅ nawopaki ako Å¡pÄra Åºo
page_rotate_ccw.label=WobwjertnuÅ nawopaki ako Å¡pÄra Åºo
page_rotate_ccw_label=WobwjertnuÅ nawopaki ako Å¡pÄra Åºo

cursor_text_select_tool.title=RÄd za wubÄranje teksta zmÃ³Å¾niÅ
cursor_text_select_tool_label=RÄd za wubÄranje teksta
cursor_hand_tool.title=Rucny rÄd zmÃ³Å¾niÅ
cursor_hand_tool_label=Rucny rÄd

scroll_vertical.title=Wertikalne suwanje wuÅ¾ywaÅ
scroll_vertical_label=Wertikalnje suwanje
scroll_horizontal.title=Horicontalne suwanje wuÅ¾ywaÅ
scroll_horizontal_label=Horicontalne suwanje
scroll_wrapped.title=PÃ³zlaÅ¾ke suwanje wuÅ¾ywaÅ
scroll_wrapped_label=PÃ³zlaÅ¾ke suwanje

spread_none.title=Boki njezwÄzaÅ
spread_none_label=Å½eden dwÃ³jny bok
spread_odd.title=Boki zachopinajucy z njerownymi bokami zwÄzaÅ
spread_odd_label=Njerowne boki
spread_even.title=Boki zachopinajucy z rownymi bokami zwÄzaÅ
spread_even_label=Rowne boki

# Document properties dialog box
document_properties.title=Dokumentowe kakosÄiâ¦
document_properties_label=Dokumentowe kakosÄiâ¦
document_properties_file_name=MÄ dataje:
document_properties_file_size=WjelikosÄ dataje:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bajtow)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bajtow)
document_properties_title=Titel:
document_properties_author=Awtor:
document_properties_subject=Tema:
document_properties_keywords=Klucowe sÅowa:
document_properties_creation_date=Datum napÃ³ranja:
document_properties_modification_date=Datum zmÄny:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Awtor:
document_properties_producer=PDF-gÃ³towaÅ:
document_properties_version=PDF-wersija:
document_properties_page_count=Licba bokow:
document_properties_page_size=WjelikosÄ boka:
document_properties_page_size_unit_inches=col
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=wusoki format
document_properties_page_size_orientation_landscape=prÄcny format
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Fast Web View:
document_properties_linearized_yes=Jo
document_properties_linearized_no=NÄ
document_properties_close=ZacyniÅ

print_progress_message=Dokument pÅigÃ³tujo se za ÅiÅ¡Äanjeâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=PÅetergnuÅ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=BÃ³cnicu pokazaÅ/schowaÅ
toggle_sidebar_notification.title=Bocnicu pÅeÅ¡altowaÅ (dokument wopÅimujo pÅeglÄd/pÅipiski)
toggle_sidebar_notification2.title=Bocnicu pÅeÅ¡altowaÅ (dokument rozrÄdowanje/pÅipiski/warstwy wopÅimujo)
toggle_sidebar_label=BÃ³cnicu pokazaÅ/schowaÅ
document_outline.title=Dokumentowe naraÅºenje pokazaÅ (dwÃ³jne kliknjenje, aby se wÅ¡ykne zapiski pokazali/schowali)
document_outline_label=Dokumentowa struktura
attachments.title=PÅidanki pokazaÅ
attachments_label=PÅidanki
layers.title=Warstwy pokazaÅ (klikniÅo dwÃ³jcy, aby wÅ¡ykne warstwy na standardny staw slÄdk stajiÅ)
layers_label=Warstwy
thumbs.title=Miniatury pokazaÅ
thumbs_label=Miniatury
current_outline_item.title=Aktualny rozrÄdowaÅski zapisk pytaÅ
current_outline_item_label=Aktualny rozrÄdowaÅski zapisk
findbar.title=W dokumenÅe pytaÅ
findbar_label=PytaÅ

additional_layers=DalÅ¡ne warstwy
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Bok {{page}}
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Bok {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Bok {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatura boka {{page}}

# Find panel button title and messages
find_input.title=PytaÅ
find_input.placeholder=W dokumenÅe pytaÅâ¦
find_previous.title=PjerwjejÅ¡ne wustupowanje pytaÅskego wuraza pytaÅ
find_previous_label=SlÄdk
find_next.title=PÅidujuce wustupowanje pytaÅskego wuraza pytaÅ
find_next_label=Dalej
find_highlight=WÅ¡ykne wuzwignuÅ
find_match_case_label=Na wjelikopisanje ÅºiwaÅ
find_entire_word_label=CeÅe sÅowa
find_reached_top=ZachopjeÅk dokumenta dostany, pÃ³kÅ¡acujo se z kÃ³Åcom
find_reached_bottom=KÃ³Åc dokumenta dostany, pÃ³kÅ¡acujo se ze zachopjeÅkom
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} z {{total}} wÃ³tpowÄdnika
find_match_count[two]={{current}} z {{total}} wÃ³tpowÄdnikowu
find_match_count[few]={{current}} z {{total}} wÃ³tpowÄdnikow
find_match_count[many]={{current}} z {{total}} wÃ³tpowÄdnikow
find_match_count[other]={{current}} z {{total}} wÃ³tpowÄdnikow
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=WÄcej ako {{limit}} wÃ³tpowÄdnikow
find_match_count_limit[one]=WÄcej ako {{limit}} wÃ³tpowÄdnik
find_match_count_limit[two]=WÄcej ako {{limit}} wÃ³tpowÄdnika
find_match_count_limit[few]=WÄcej ako {{limit}} wÃ³tpowÄdniki
find_match_count_limit[many]=WÄcej ako {{limit}} wÃ³tpowÄdnikow
find_match_count_limit[other]=WÄcej ako {{limit}} wÃ³tpowÄdnikow
find_not_found=PytaÅski wuraz njejo se namakaÅ

# Error panel labels
error_more_info=WÄcej informacijow
error_less_info=Mjenjej informacijow
error_close=ZacyniÅ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=PowÄÅºenka: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=LisÄina zawoÅanjow: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Dataja: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=SmuÅ¾ka: {{line}}
rendering_error=PÅi zwobraznjanju boka jo zmÃ³lka nastaÅa.

# Predefined zoom values
page_scale_width=Å yrokosÄ boka
page_scale_fit=WjelikosÄ boka
page_scale_auto=Awtomatiske skalÄrowanje
page_scale_actual=Aktualna wjelikosÄ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ZmÃ³lka

# Loading indicator messages
loading=Zacytujo seâ¦
loading_error=PÅi zacytowanju PDF jo zmÃ³lka nastaÅa.
invalid_file_error=NjepÅaÅiwa abo wobÅ¡kÃ³Åºona PDF-dataja.
missing_file_error=Felujuca PDF-dataja.
unexpected_response_error=NjewÃ³cakane serwerowe wÃ³tegrono.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Typ pÅipiskow: {{type}}]
password_label=ZapÃ³dajÅo gronidÅo, aby PDF-dataju wÃ³cyniÅ.
password_invalid=NjepÅaÅiwe gronidÅo. PÅ¡osym wopytajÅo hyÅ¡Äi raz.
password_ok=W pÃ³rÄÅºe
password_cancel=PÅetergnuÅ

printing_not_supported=Warnowanje: ÅiÅ¡Äanje njepÃ³dpÄra se poÅnje pÅez toÅ ten wobglÄdowak.
printing_not_ready=Warnowanje: PDF njejo se za ÅiÅ¡Äanje dopoÅnje zacytaÅ.
web_fonts_disabled=Webpisma su znjemÃ³Å¾njone: njejo mÃ³Å¾no, zasajÅºone PDF-pisma wuÅ¾ywaÅ.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=ToÅ ten PDF-dokument digitalne signatury wopÅimujo. PÅeglÄdowanje signaturow se njepÃ³dpÄra.
