# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Î ÏÎ¿Î·Î³Î¿ÏÎ¼ÎµÎ½Î· ÏÎµÎ»Î¯Î´Î±
previous_label=Î ÏÎ¿Î·Î³Î¿ÏÎ¼ÎµÎ½Î·
next.title=ÎÏÏÎ¼ÎµÎ½Î· ÏÎµÎ»Î¯Î´Î±
next_label=ÎÏÏÎ¼ÎµÎ½Î·

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Î£ÎµÎ»Î¯Î´Î±
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Î±ÏÏ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Î±ÏÏ {{pagesCount}})

zoom_out.title=Î£Î¼Î¯ÎºÏÏÎ½ÏÎ·
zoom_out_label=Î£Î¼Î¯ÎºÏÏÎ½ÏÎ·
zoom_in.title=ÎÎµÎ³Î­Î¸ÏÎ½ÏÎ·
zoom_in_label=ÎÎµÎ³Î­Î¸ÏÎ½ÏÎ·
zoom.title=ÎÎ¿ÏÎ¼
presentation_mode.title=ÎÎ½Î±Î»Î»Î±Î³Î® ÏÎµ Î»ÎµÎ¹ÏÎ¿ÏÏÎ³Î¯Î± ÏÎ±ÏÎ¿ÏÏÎ¯Î±ÏÎ·Ï
presentation_mode_label=ÎÎµÎ¹ÏÎ¿ÏÏÎ³Î¯Î± ÏÎ±ÏÎ¿ÏÏÎ¯Î±ÏÎ·Ï
open_file.title=ÎÎ½Î¿Î¹Î³Î¼Î± Î±ÏÏÎµÎ¯Î¿Ï
open_file_label=ÎÎ½Î¿Î¹Î³Î¼Î±
print.title=ÎÎºÏÏÏÏÏÎ·
print_label=ÎÎºÏÏÏÏÏÎ·
download.title=ÎÎ®ÏÎ·
download_label=ÎÎ®ÏÎ·
bookmark.title=Î¤ÏÎ­ÏÎ¿ÏÏÎ± ÏÏÎ¿Î²Î¿Î»Î® (Î±Î½ÏÎ¹Î³ÏÎ±ÏÎ® Î® Î¬Î½Î¿Î¹Î³Î¼Î± ÏÎµ Î½Î­Î¿ ÏÎ±ÏÎ¬Î¸ÏÏÎ¿)
bookmark_label=Î¤ÏÎ­ÏÎ¿ÏÏÎ± ÏÏÎ¿Î²Î¿Î»Î®

# Secondary toolbar and context menu
tools.title=ÎÏÎ³Î±Î»ÎµÎ¯Î±
tools_label=ÎÏÎ³Î±Î»ÎµÎ¯Î±
first_page.title=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÏÏÏÎ· ÏÎµÎ»Î¯Î´Î±
first_page.label=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÏÏÏÎ· ÏÎµÎ»Î¯Î´Î±
first_page_label=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÏÏÏÎ· ÏÎµÎ»Î¯Î´Î±
last_page.title=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÎµÎ»ÎµÏÏÎ±Î¯Î± ÏÎµÎ»Î¯Î´Î±
last_page.label=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÎµÎ»ÎµÏÏÎ±Î¯Î± ÏÎµÎ»Î¯Î´Î±
last_page_label=ÎÎµÏÎ¬Î²Î±ÏÎ· ÏÏÎ·Î½ ÏÎµÎ»ÎµÏÏÎ±Î¯Î± ÏÎµÎ»Î¯Î´Î±
page_rotate_cw.title=ÎÎµÎ¾Î¹ÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®
page_rotate_cw.label=ÎÎµÎ¾Î¹ÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®
page_rotate_cw_label=ÎÎµÎ¾Î¹ÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®
page_rotate_ccw.title=ÎÏÎ¹ÏÏÎµÏÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®
page_rotate_ccw.label=ÎÏÎ¹ÏÏÎµÏÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®
page_rotate_ccw_label=ÎÏÎ¹ÏÏÎµÏÏÏÏÏÎ¿ÏÎ· ÏÎµÏÎ¹ÏÏÏÎ¿ÏÎ®

cursor_text_select_tool.title=ÎÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÎµÏÎ³Î±Î»ÎµÎ¯Î¿Ï ÎµÏÎ¹Î»Î¿Î³Î®Ï ÎºÎµÎ¹Î¼Î­Î½Î¿Ï
cursor_text_select_tool_label=ÎÏÎ³Î±Î»ÎµÎ¯Î¿ ÎµÏÎ¹Î»Î¿Î³Î®Ï ÎºÎµÎ¹Î¼Î­Î½Î¿Ï
cursor_hand_tool.title=ÎÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÎµÏÎ³Î±Î»ÎµÎ¯Î¿Ï ÏÎµÏÎ¹Î¿Ï
cursor_hand_tool_label=ÎÏÎ³Î±Î»ÎµÎ¯Î¿ ÏÎµÏÎ¹Î¿Ï

scroll_vertical.title=Î§ÏÎ®ÏÎ· ÎºÎ¬Î¸ÎµÏÎ·Ï ÎºÏÎ»Î¹ÏÎ·Ï
scroll_vertical_label=ÎÎ¬Î¸ÎµÏÎ· ÎºÏÎ»Î¹ÏÎ·
scroll_horizontal.title=Î§ÏÎ®ÏÎ· Î¿ÏÎ¹Î¶ÏÎ½ÏÎ¹Î±Ï ÎºÏÎ»Î¹ÏÎ·Ï
scroll_horizontal_label=ÎÏÎ¹Î¶ÏÎ½ÏÎ¹Î± ÎºÏÎ»Î¹ÏÎ·
scroll_wrapped.title=Î§ÏÎ®ÏÎ· ÎºÏÎºÎ»Î¹ÎºÎ®Ï ÎºÏÎ»Î¹ÏÎ·Ï
scroll_wrapped_label=ÎÏÎºÎ»Î¹ÎºÎ® ÎºÏÎ»Î¹ÏÎ·

spread_none.title=ÎÎ± Î¼Î·Î½ Î³Î¯Î½ÎµÎ¹ ÏÏÎ½Î´ÎµÏÎ· ÎµÏÎµÎºÏÎ¬ÏÎµÏÎ½ ÏÎµÎ»Î¯Î´ÏÎ½
spread_none_label=Î§ÏÏÎ¯Ï ÎµÏÎµÎºÏÎ¬ÏÎµÎ¹Ï
spread_odd.title=Î£ÏÎ½Î´ÎµÏÎ· ÎµÏÎµÎºÏÎ¬ÏÎµÏÎ½ ÏÎµÎ»Î¯Î´ÏÎ½ Î¾ÎµÎºÎ¹Î½ÏÎ½ÏÎ±Ï Î±ÏÏ ÏÎ¹Ï Î¼Î¿Î½Î­Ï ÏÎµÎ»Î¯Î´ÎµÏ
spread_odd_label=ÎÎ¿Î½Î­Ï ÎµÏÎµÎºÏÎ¬ÏÎµÎ¹Ï
spread_even.title=Î£ÏÎ½Î´ÎµÏÎ· ÎµÏÎµÎºÏÎ¬ÏÎµÏÎ½ ÏÎµÎ»Î¯Î´ÏÎ½ Î¾ÎµÎºÎ¹Î½ÏÎ½ÏÎ±Ï Î±ÏÏ ÏÎ¹Ï Î¶ÏÎ³Î­Ï ÏÎµÎ»Î¯Î´ÎµÏ
spread_even_label=ÎÏÎ³Î­Ï ÎµÏÎµÎºÏÎ¬ÏÎµÎ¹Ï

# Document properties dialog box
document_properties.title=ÎÎ´Î¹ÏÏÎ·ÏÎµÏ ÎµÎ³Î³ÏÎ¬ÏÎ¿Ïâ¦
document_properties_label=ÎÎ´Î¹ÏÏÎ·ÏÎµÏ ÎµÎ³Î³ÏÎ¬ÏÎ¿Ïâ¦
document_properties_file_name=ÎÎ½Î¿Î¼Î± Î±ÏÏÎµÎ¯Î¿Ï:
document_properties_file_size=ÎÎ­Î³ÎµÎ¸Î¿Ï Î±ÏÏÎµÎ¯Î¿Ï:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Î¤Î¯ÏÎ»Î¿Ï:
document_properties_author=Î£ÏÎ³Î³ÏÎ±ÏÎ­Î±Ï:
document_properties_subject=ÎÎ­Î¼Î±:
document_properties_keywords=ÎÎ­Î¾ÎµÎ¹Ï ÎºÎ»ÎµÎ¹Î´Î¹Î¬:
document_properties_creation_date=ÎÎ¼ÎµÏÎ¿Î¼Î·Î½Î¯Î± Î´Î·Î¼Î¹Î¿ÏÏÎ³Î¯Î±Ï:
document_properties_modification_date=ÎÎ¼ÎµÏÎ¿Î¼Î·Î½Î¯Î± ÏÏÎ¿ÏÎ¿ÏÎ¿Î¯Î·ÏÎ·Ï:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ÎÎ·Î¼Î¹Î¿ÏÏÎ³ÏÏ:
document_properties_producer=Î Î±ÏÎ±Î³ÏÎ³ÏÏ PDF:
document_properties_version=ÎÎºÎ´Î¿ÏÎ· PDF:
document_properties_page_count=ÎÏÎ¹Î¸Î¼ÏÏ ÏÎµÎ»Î¯Î´ÏÎ½:
document_properties_page_size=ÎÎ­Î³ÎµÎ¸Î¿Ï ÏÎµÎ»Î¯Î´Î±Ï:
document_properties_page_size_unit_inches=Î¯Î½ÏÏÎµÏ
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=ÎºÎ±ÏÎ±ÎºÏÏÏÏÎ±
document_properties_page_size_orientation_landscape=Î¿ÏÎ¹Î¶ÏÎ½ÏÎ¹Î±
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÎÏÎ¹ÏÏÎ¿Î»Î®
document_properties_page_size_name_legal=Î¤ÏÏÎ¿Ï Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Î¤Î±ÏÎµÎ¯Î± ÏÏÎ¿Î²Î¿Î»Î® Î¹ÏÏÎ¿Ï:
document_properties_linearized_yes=ÎÎ±Î¹
document_properties_linearized_no=ÎÏÎ¹
document_properties_close=ÎÎ»ÎµÎ¯ÏÎ¹Î¼Î¿

print_progress_message=Î ÏÎ¿ÎµÏÎ¿Î¹Î¼Î±ÏÎ¯Î± ÏÎ¿Ï ÎµÎ³Î³ÏÎ¬ÏÎ¿Ï Î³Î¹Î± ÎµÎºÏÏÏÏÏÎ·â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÎÎºÏÏÏÏÎ·

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=(ÎÏ)ÎµÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÏÎ»ÎµÏÏÎ¹ÎºÎ®Ï ÏÏÎ®Î»Î·Ï
toggle_sidebar_notification.title=(ÎÏ)ÎµÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÏÎ»ÎµÏÏÎ¹ÎºÎ®Ï ÏÏÎ®Î»Î·Ï (ÏÎ¿ Î­Î³Î³ÏÎ±ÏÎ¿ ÏÎµÏÎ¹Î­ÏÎµÎ¹ ÏÎµÏÎ¯Î³ÏÎ±Î¼Î¼Î±/ÏÏÎ½Î·Î¼Î¼Î­Î½Î±)
toggle_sidebar_notification2.title=(ÎÏ)ÎµÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÏÎ»ÎµÏÏÎ¹ÎºÎ®Ï ÏÏÎ®Î»Î·Ï (ÏÎ¿ Î­Î³Î³ÏÎ±ÏÎ¿ ÏÎµÏÎ¹Î­ÏÎµÎ¹ ÏÎµÏÎ¯Î³ÏÎ±Î¼Î¼Î±/ÏÏÎ½Î·Î¼Î¼Î­Î½Î±/ÎµÏÎ¯ÏÎµÎ´Î±)
toggle_sidebar_label=(ÎÏ)ÎµÎ½ÎµÏÎ³Î¿ÏÎ¿Î¯Î·ÏÎ· ÏÎ»ÎµÏÏÎ¹ÎºÎ®Ï ÏÏÎ®Î»Î·Ï
document_outline.title=ÎÎ¼ÏÎ¬Î½Î¹ÏÎ· Î´Î¹Î¬ÏÎ¸ÏÏÏÎ·Ï ÎµÎ³Î³ÏÎ¬ÏÎ¿Ï (Î´Î¹ÏÎ»Ï ÎºÎ»Î¹Îº Î³Î¹Î± Î±Î½Î¬ÏÏÏÎ¾Î·/ÏÏÎ¼ÏÏÏÎ¾Î· ÏÎ»ÏÎ½ ÏÏÎ½ ÏÏÎ¿Î¹ÏÎµÎ¯ÏÎ½)
document_outline_label=ÎÎ¹Î¬ÏÎ¸ÏÏÏÎ· ÎµÎ³Î³ÏÎ¬ÏÎ¿Ï
attachments.title=Î ÏÎ¿Î²Î¿Î»Î® ÏÏÎ½Î·Î¼Î¼Î­Î½ÏÎ½
attachments_label=Î£ÏÎ½Î·Î¼Î¼Î­Î½Î±
layers.title=ÎÎ¼ÏÎ¬Î½Î¹ÏÎ· ÎµÏÎ¹ÏÎ­Î´ÏÎ½ (Î´Î¹ÏÎ»Ï ÎºÎ»Î¹Îº Î³Î¹Î± ÎµÏÎ±Î½Î±ÏÎ¿ÏÎ¬ ÏÎ»ÏÎ½ ÏÏÎ½ ÎµÏÎ¹ÏÎ­Î´ÏÎ½ ÏÏÎ·Î½ ÏÏÎ¿ÎµÏÎ¹Î»ÎµÎ³Î¼Î­Î½Î· ÎºÎ±ÏÎ¬ÏÏÎ±ÏÎ·)
layers_label=ÎÏÎ¯ÏÎµÎ´Î±
thumbs.title=Î ÏÎ¿Î²Î¿Î»Î® Î¼Î¹ÎºÏÎ¿Î³ÏÎ±ÏÎ¹ÏÎ½
thumbs_label=ÎÎ¹ÎºÏÎ¿Î³ÏÎ±ÏÎ¯ÎµÏ
current_outline_item.title=ÎÏÏÎµÏÎ· ÏÏÎ­ÏÎ¿Î½ÏÎ¿Ï ÏÏÎ¿Î¹ÏÎµÎ¯Î¿Ï Î´Î¹Î¬ÏÎ¸ÏÏÏÎ·Ï
current_outline_item_label=Î¤ÏÎ­ÏÎ¿Î½ ÏÏÎ¿Î¹ÏÎµÎ¯Î¿ Î´Î¹Î¬ÏÎ¸ÏÏÏÎ·Ï
findbar.title=ÎÏÏÎµÏÎ· ÏÏÎ¿ Î­Î³Î³ÏÎ±ÏÎ¿
findbar_label=ÎÏÏÎµÏÎ·

additional_layers=ÎÏÎ¹ÏÏÏÏÎ¸ÎµÏÎ± ÎµÏÎ¯ÏÎµÎ´Î±
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Î£ÎµÎ»Î¯Î´Î± {{page}}
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Î£ÎµÎ»Î¯Î´Î± {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Î£ÎµÎ»Î¯Î´Î± {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÎÎ¹ÎºÏÎ¿Î³ÏÎ±ÏÎ¯Î± ÏÎ·Ï ÏÎµÎ»Î¯Î´Î±Ï {{page}}

# Find panel button title and messages
find_input.title=ÎÏÏÎµÏÎ·
find_input.placeholder=ÎÏÏÎµÏÎ· ÏÏÎ¿ Î­Î³Î³ÏÎ±ÏÎ¿â¦
find_previous.title=ÎÏÏÎµÏÎ· ÏÎ·Ï ÏÏÎ¿Î·Î³Î¿ÏÎ¼ÎµÎ½Î·Ï ÎµÎ¼ÏÎ¬Î½Î¹ÏÎ·Ï ÏÎ·Ï ÏÏÎ¬ÏÎ·Ï
find_previous_label=Î ÏÎ¿Î·Î³Î¿ÏÎ¼ÎµÎ½Î¿
find_next.title=ÎÏÏÎµÏÎ· ÏÎ·Ï ÎµÏÏÎ¼ÎµÎ½Î·Ï ÎµÎ¼ÏÎ¬Î½Î¹ÏÎ·Ï ÏÎ·Ï ÏÏÎ¬ÏÎ·Ï
find_next_label=ÎÏÏÎ¼ÎµÎ½Î¿
find_highlight=ÎÏÎ¹ÏÎ®Î¼Î±Î½ÏÎ· ÏÎ»ÏÎ½
find_match_case_label=Î£ÏÎ¼ÏÏÎ½Î¯Î± ÏÎµÎ¶ÏÎ½/ÎºÎµÏÎ±Î»Î±Î¯ÏÎ½
find_entire_word_label=ÎÎ»ÏÎºÎ»Î·ÏÎµÏ Î»Î­Î¾ÎµÎ¹Ï
find_reached_top=ÎÎ»ÎµÏÏÎ· ÏÏÎ·Î½ Î±ÏÏÎ® ÏÎ¿Ï ÎµÎ³Î³ÏÎ¬ÏÎ¿Ï, ÏÏÎ½Î­ÏÎµÎ¹Î± Î±ÏÏ ÏÎ¿ ÏÎ­Î»Î¿Ï
find_reached_bottom=ÎÎ»ÎµÏÏÎ· ÏÏÎ¿ ÏÎ­Î»Î¿Ï ÏÎ¿Ï ÎµÎ³Î³ÏÎ¬ÏÎ¿Ï, ÏÏÎ½Î­ÏÎµÎ¹Î± Î±ÏÏ ÏÎ·Î½ Î±ÏÏÎ®
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Î±ÏÏ {{total}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯Î±
find_match_count[two]={{current}} Î±ÏÏ {{total}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count[few]={{current}} Î±ÏÏ {{total}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count[many]={{current}} Î±ÏÏ {{total}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count[other]={{current}} Î±ÏÏ {{total}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count_limit[one]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯Î±
find_match_count_limit[two]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count_limit[few]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count_limit[many]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_match_count_limit[other]=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ Î±ÏÏ {{limit}} Î±Î½ÏÎ¹ÏÏÎ¿Î¹ÏÎ¯ÎµÏ
find_not_found=Î ÏÏÎ¬ÏÎ· Î´ÎµÎ½ Î²ÏÎ­Î¸Î·ÎºÎµ

# Error panel labels
error_more_info=Î ÎµÏÎ¹ÏÏÏÏÎµÏÎµÏ ÏÎ»Î·ÏÎ¿ÏÎ¿ÏÎ¯ÎµÏ
error_less_info=ÎÎ¹Î³ÏÏÎµÏÎµÏ ÏÎ»Î·ÏÎ¿ÏÎ¿ÏÎ¯ÎµÏ
error_close=ÎÎ»ÎµÎ¯ÏÎ¹Î¼Î¿
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÎÎ®Î½ÏÎ¼Î±: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Î£ÏÎ¿Î¯Î²Î±: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ÎÏÏÎµÎ¯Î¿: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÎÏÎ±Î¼Î¼Î®: {{line}}
rendering_error=Î ÏÎ¿Î­ÎºÏÏÎµ ÏÏÎ¬Î»Î¼Î± ÎºÎ±ÏÎ¬ ÏÎ·Î½ Î±Î½Î¬Î»ÏÏÎ· ÏÎ·Ï ÏÎµÎ»Î¯Î´Î±Ï.

# Predefined zoom values
page_scale_width=Î Î»Î¬ÏÎ¿Ï ÏÎµÎ»Î¯Î´Î±Ï
page_scale_fit=ÎÎ­Î³ÎµÎ¸Î¿Ï ÏÎµÎ»Î¯Î´Î±Ï
page_scale_auto=ÎÏÏÏÎ¼Î±ÏÎ¿ Î¶Î¿ÏÎ¼
page_scale_actual=Î ÏÎ±Î³Î¼Î±ÏÎ¹ÎºÏ Î¼Î­Î³ÎµÎ¸Î¿Ï
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Î£ÏÎ¬Î»Î¼Î±

# Loading indicator messages
loading=Î¦ÏÏÏÏÏÎ·â¦
loading_error=Î ÏÎ¿Î­ÎºÏÏÎµ Î­Î½Î± ÏÏÎ¬Î»Î¼Î± ÎºÎ±ÏÎ¬ ÏÎ· ÏÏÏÏÏÏÎ· ÏÎ¿Ï PDF.
invalid_file_error=ÎÎ· Î­Î³ÎºÏÏÎ¿ Î® ÎºÎ±ÏÎµÏÏÏÎ±Î¼Î¼Î­Î½Î¿ Î±ÏÏÎµÎ¯Î¿ PDF.
missing_file_error=ÎÎµÎ¯ÏÎµÎ¹ Î±ÏÏÎµÎ¯Î¿ PDF.
unexpected_response_error=ÎÎ· Î±Î½Î±Î¼ÎµÎ½ÏÎ¼ÎµÎ½Î· Î±ÏÏÎºÏÎ¹ÏÎ· Î±ÏÏ ÏÎ¿ Î´Î¹Î±ÎºÎ¿Î¼Î¹ÏÏÎ®.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Î£ÏÏÎ»Î¹Î¿]
password_label=ÎÎ¹ÏÎ±Î³ÏÎ³Î® ÎºÏÎ´Î¹ÎºÎ¿Ï Î³Î¹Î± ÏÎ¿ Î¬Î½Î¿Î¹Î³Î¼Î± ÏÎ¿Ï PDF Î±ÏÏÎµÎ¯Î¿Ï.
password_invalid=ÎÎ· Î­Î³ÎºÏÏÎ¿Ï ÎºÏÎ´Î¹ÎºÏÏ. Î ÏÎ¿ÏÏÎ±Î¸ÎµÎ¯ÏÏÎµ Î¾Î±Î½Î¬.
password_ok=OK
password_cancel=ÎÎºÏÏÏÏÎ·

printing_not_supported=Î ÏÎ¿ÎµÎ¹Î´Î¿ÏÎ¿Î¯Î·ÏÎ·: Î ÎµÎºÏÏÏÏÏÎ· Î´ÎµÎ½ ÏÏÎ¿ÏÏÎ·ÏÎ¯Î¶ÎµÏÎ±Î¹ ÏÎ»Î®ÏÏÏ Î±ÏÏ ÏÎ¿ ÏÏÏÎ³ÏÎ±Î¼Î¼Î± ÏÎµÏÎ¹Î®Î³Î·ÏÎ·Ï.
printing_not_ready=Î ÏÎ¿ÎµÎ¹Î´Î¿ÏÎ¿Î¯Î·ÏÎ·: Î¤Î¿ PDF Î´ÎµÎ½ ÏÎ¿ÏÏÏÎ¸Î·ÎºÎµ ÏÎ»Î®ÏÏÏ Î³Î¹Î± ÎµÎºÏÏÏÏÏÎ·.
web_fonts_disabled=ÎÎ¹ Î³ÏÎ±Î¼Î¼Î±ÏÎ¿ÏÎµÎ¹ÏÎ­Ï Web Î±ÏÎµÎ½ÎµÏÎ³Î¿ÏÎ¿Î¹Î·Î¼Î­Î½ÎµÏ: Î±Î´ÏÎ½Î±Î¼Î¯Î± ÏÏÎ®ÏÎ·Ï ÏÏÎ½ ÎµÎ½ÏÏÎ¼Î±ÏÏÎ¼Î­Î½ÏÎ½ Î³ÏÎ±Î¼Î¼Î±ÏÎ¿ÏÎµÎ¹ÏÏÎ½ PDF.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=ÎÏÏÏ ÏÎ¿ Î­Î³Î³ÏÎ±ÏÎ¿ PDF ÏÎµÏÎ¹Î­ÏÎµÎ¹ ÏÎ·ÏÎ¹Î±ÎºÎ­Ï ÏÏÎ¿Î³ÏÎ±ÏÎ­Ï. ÎÎµÎ½ ÏÏÎ¿ÏÏÎ·ÏÎ¯Î¶ÎµÏÎ±Î¹ Î· ÎµÏÎ±Î»Î®Î¸ÎµÏÏÎ· ÏÏÎ¿Î³ÏÎ±ÏÏÎ½.
