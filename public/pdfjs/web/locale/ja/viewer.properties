# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=åã®ãã¼ã¸ã¸æ»ãã¾ã
previous_label=åã¸
next.title=æ¬¡ã®ãã¼ã¸ã¸é²ã¿ã¾ã
next_label=æ¬¡ã¸

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ãã¼ã¸
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=è¡¨ç¤ºãç¸®å°ãã¾ã
zoom_out_label=ç¸®å°
zoom_in.title=è¡¨ç¤ºãæ¡å¤§ãã¾ã
zoom_in_label=æ¡å¤§
zoom.title=æ¡å¤§/ç¸®å°
presentation_mode.title=ãã¬ã¼ã³ãã¼ã·ã§ã³ã¢ã¼ãã«åãæ¿ãã¾ã
presentation_mode_label=ãã¬ã¼ã³ãã¼ã·ã§ã³ã¢ã¼ã
open_file.title=ãã¡ã¤ã«ãéãã¾ã
open_file_label=éã
print.title=å°å·ãã¾ã
print_label=å°å·
download.title=ãã¦ã³ã­ã¼ããã¾ã
download_label=ãã¦ã³ã­ã¼ã
bookmark.title=ç¾å¨ã®ãã¥ã¼ã® URL ã§ã (ã³ãã¼ã¾ãã¯æ°ããã¦ã£ã³ãã¦ã«éã)
bookmark_label=ç¾å¨ã®ãã¥ã¼

# Secondary toolbar and context menu
tools.title=ãã¼ã«
tools_label=ãã¼ã«
first_page.title=æåã®ãã¼ã¸ã¸ç§»åãã¾ã
first_page.label=æåã®ãã¼ã¸ã¸ç§»å
first_page_label=æåã®ãã¼ã¸ã¸ç§»å
last_page.title=æå¾ã®ãã¼ã¸ã¸ç§»åãã¾ã
last_page.label=æå¾ã®ãã¼ã¸ã¸ç§»å
last_page_label=æå¾ã®ãã¼ã¸ã¸ç§»å
page_rotate_cw.title=ãã¼ã¸ãå³ã¸åè»¢ãã¾ã
page_rotate_cw.label=å³åè»¢
page_rotate_cw_label=å³åè»¢
page_rotate_ccw.title=ãã¼ã¸ãå·¦ã¸åè»¢ãã¾ã
page_rotate_ccw.label=å·¦åè»¢
page_rotate_ccw_label=å·¦åè»¢

cursor_text_select_tool.title=ãã­ã¹ãé¸æãã¼ã«ãæå¹ã«ãã
cursor_text_select_tool_label=ãã­ã¹ãé¸æãã¼ã«
cursor_hand_tool.title=æã®ã²ããã¼ã«ãæå¹ã«ãã
cursor_hand_tool_label=æã®ã²ããã¼ã«

scroll_vertical.title=ç¸¦ã¹ã¯ã­ã¼ã«ã«ãã
scroll_vertical_label=ç¸¦ã¹ã¯ã­ã¼ã«
scroll_horizontal.title=æ¨ªã¹ã¯ã­ã¼ã«ã«ãã
scroll_horizontal_label=æ¨ªã¹ã¯ã­ã¼ã«
scroll_wrapped.title=æãè¿ãã¹ã¯ã­ã¼ã«ã«ãã
scroll_wrapped_label=æãè¿ãã¹ã¯ã­ã¼ã«

spread_none.title=è¦éãã«ããªã
spread_none_label=è¦éãã«ããªã
spread_odd.title=å¥æ°ãã¼ã¸éå§ã§è¦éãã«ãã
spread_odd_label=å¥æ°ãã¼ã¸è¦éã
spread_even.title=å¶æ°ãã¼ã¸éå§ã§è¦éãã«ãã
spread_even_label=å¶æ°ãã¼ã¸è¦éã

# Document properties dialog box
document_properties.title=ææ¸ã®ãã­ããã£...
document_properties_label=ææ¸ã®ãã­ããã£...
document_properties_file_name=ãã¡ã¤ã«å:
document_properties_file_size=ãã¡ã¤ã«ãµã¤ãº:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=ã¿ã¤ãã«:
document_properties_author=ä½æè:
document_properties_subject=ä»¶å:
document_properties_keywords=ã­ã¼ã¯ã¼ã:
document_properties_creation_date=ä½ææ¥:
document_properties_modification_date=æ´æ°æ¥:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ã¢ããªã±ã¼ã·ã§ã³:
document_properties_producer=PDF ä½æ:
document_properties_version=PDF ã®ãã¼ã¸ã§ã³:
document_properties_page_count=ãã¼ã¸æ°:
document_properties_page_size=ãã¼ã¸ãµã¤ãº:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=ç¸¦
document_properties_page_size_orientation_landscape=æ¨ª
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ã¬ã¿ã¼
document_properties_page_size_name_legal=ãªã¼ã¬ã«
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ã¦ã§ãè¡¨ç¤ºç¨ã«æé©å:
document_properties_linearized_yes=ã¯ã
document_properties_linearized_no=ããã
document_properties_close=éãã

print_progress_message=ææ¸ã®å°å·ãæºåãã¦ãã¾ã...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ã­ã£ã³ã»ã«

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ãµã¤ããã¼è¡¨ç¤ºãåãæ¿ãã¾ã
toggle_sidebar_notification.title=ãµã¤ããã¼è¡¨ç¤ºãåãæ¿ãã¾ã (ææ¸ã«å«ã¾ããã¢ã¦ãã©ã¤ã³ / æ·»ä»)
toggle_sidebar_notification2.title=ãµã¤ããã¼è¡¨ç¤ºãåãæ¿ãã¾ã (ææ¸ã«å«ã¾ããã¢ã¦ãã©ã¤ã³ / æ·»ä» / ã¬ã¤ã¤ã¼)
toggle_sidebar_label=ãµã¤ããã¼ã®åãæ¿ã
document_outline.title=ææ¸ã®ç®æ¬¡ãè¡¨ç¤ºãã¾ã (ããã«ã¯ãªãã¯ã§é ç®ãééãã¾ã)
document_outline_label=ææ¸ã®ç®æ¬¡
attachments.title=æ·»ä»ãã¡ã¤ã«ãè¡¨ç¤ºãã¾ã
attachments_label=æ·»ä»ãã¡ã¤ã«
layers.title=ã¬ã¤ã¤ã¼ãè¡¨ç¤ºãã¾ã (ããã«ã¯ãªãã¯ã§ãã¹ã¦ã®ã¬ã¤ã¤ã¼ãåæç¶æã«æ»ãã¾ã)
layers_label=ã¬ã¤ã¤ã¼
thumbs.title=ç¸®å°çãè¡¨ç¤ºãã¾ã
thumbs_label=ç¸®å°ç
current_outline_item.title=ç¾å¨ã®ã¢ã¦ãã©ã¤ã³é ç®ãæ¤ç´¢
current_outline_item_label=ç¾å¨ã®ã¢ã¦ãã©ã¤ã³é ç®
findbar.title=ææ¸åãæ¤ç´¢ãã¾ã
findbar_label=æ¤ç´¢

additional_layers=è¿½å ã¬ã¤ã¤ã¼
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas={{page}} ãã¼ã¸
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark={{page}} ãã¼ã¸
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} ãã¼ã¸
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} ãã¼ã¸ã®ç¸®å°ç

# Find panel button title and messages
find_input.title=æ¤ç´¢
find_input.placeholder=ææ¸åãæ¤ç´¢...
find_previous.title=ç¾å¨ããåã®ä½ç½®ã§æå®æå­åãç¾ããé¨åãæ¤ç´¢ãã¾ã
find_previous_label=åã¸
find_next.title=ç¾å¨ããå¾ã®ä½ç½®ã§æå®æå­åãç¾ããé¨åãæ¤ç´¢ãã¾ã
find_next_label=æ¬¡ã¸
find_highlight=ãã¹ã¦å¼·èª¿è¡¨ç¤º
find_match_case_label=å¤§æå­/å°æå­ãåºå¥
find_entire_word_label=åèªä¸è´
find_reached_top=ææ¸åé ­ã«å°éããã®ã§æ«å°¾ããç¶ãã¦æ¤ç´¢ãã¾ã
find_reached_bottom=ææ¸æ«å°¾ã«å°éããã®ã§åé ­ããç¶ãã¦æ¤ç´¢ãã¾ã
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} ä»¶ä¸­ {{current}} ä»¶ç®
find_match_count[two]={{total}} ä»¶ä¸­ {{current}} ä»¶ç®
find_match_count[few]={{total}} ä»¶ä¸­ {{current}} ä»¶ç®
find_match_count[many]={{total}} ä»¶ä¸­ {{current}} ä»¶ç®
find_match_count[other]={{total}} ä»¶ä¸­ {{current}} ä»¶ç®
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_match_count_limit[one]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_match_count_limit[two]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_match_count_limit[few]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_match_count_limit[many]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_match_count_limit[other]={{limit}} ä»¶ä»¥ä¸ä¸è´
find_not_found=è¦ã¤ããã¾ããã§ãã

# Error panel labels
error_more_info=è©³ç´°æå ±
error_less_info=è©³ç´°æå ±ãé ã
error_close=éãã
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ãã«ã: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ã¡ãã»ã¼ã¸: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ã¹ã¿ãã¯: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ãã¡ã¤ã«: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=è¡: {{line}}
rendering_error=ãã¼ã¸ã®ã¬ã³ããªã³ã°ä¸­ã«ã¨ã©ã¼ãçºçãã¾ããã

# Predefined zoom values
page_scale_width=å¹ã«åããã
page_scale_fit=ãã¼ã¸ã®ãµã¤ãºã«åããã
page_scale_auto=èªåãºã¼ã 
page_scale_actual=å®éã®ãµã¤ãº
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ã¨ã©ã¼

# Loading indicator messages
loading=èª­ã¿è¾¼ã¿ä¸­...
loading_error=PDF ã®èª­ã¿è¾¼ã¿ä¸­ã«ã¨ã©ã¼ãçºçãã¾ããã
invalid_file_error=ç¡å¹ã¾ãã¯ç ´æãã PDF ãã¡ã¤ã«ã
missing_file_error=PDF ãã¡ã¤ã«ãè¦ã¤ããã¾ããã
unexpected_response_error=ãµã¼ãã¼ããäºæãã¬å¿ç­ãããã¾ããã

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} æ³¨é]
password_label=ãã® PDF ãã¡ã¤ã«ãéãããã®ãã¹ã¯ã¼ããå¥åãã¦ãã ããã
password_invalid=ç¡å¹ãªãã¹ã¯ã¼ãã§ããããä¸åº¦ããç´ãã¦ãã ããã
password_ok=OK
password_cancel=ã­ã£ã³ã»ã«

printing_not_supported=è­¦å: ãã®ãã©ã¦ã¶ã¼ã§ã¯å°å·ãå®å¨ã«ãµãã¼ãããã¦ãã¾ããã
printing_not_ready=è­¦å: PDF ãå°å·ããããã®èª­ã¿è¾¼ã¿ãçµäºãã¦ãã¾ããã
web_fonts_disabled=ã¦ã§ããã©ã³ããç¡å¹ã«ãªã£ã¦ãã¾ã: åãè¾¼ã¾ãã PDF ã®ãã©ã³ããä½¿ç¨ã§ãã¾ããã
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=ãã® PDF ãã­ã¥ã¡ã³ãã¯ããã¸ã¿ã«ç½²åãå«ã¾ãã¦ãã¾ããç½²åã®æ¤è¨¼ã¯ãµãã¼ãããã¦ãã¾ããã
