# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=PredchÃ¡dzajÃºca strana
previous_label=PredchÃ¡dzajÃºca
next.title=NasledujÃºca strana
next_label=NasledujÃºca

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Strana
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=z {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} z {{pagesCount}})

zoom_out.title=ZmenÅ¡iÅ¥ veÄ¾kosÅ¥
zoom_out_label=ZmenÅ¡iÅ¥ veÄ¾kosÅ¥
zoom_in.title=ZvÃ¤ÄÅ¡iÅ¥ veÄ¾kosÅ¥
zoom_in_label=ZvÃ¤ÄÅ¡iÅ¥ veÄ¾kosÅ¥
zoom.title=Nastavenie veÄ¾kosti
presentation_mode.title=PrepnÃºÅ¥ na reÅ¾im prezentÃ¡cie
presentation_mode_label=ReÅ¾im prezentÃ¡cie
open_file.title=OtvoriÅ¥ sÃºbor
open_file_label=OtvoriÅ¥
print.title=TlaÄiÅ¥
print_label=TlaÄiÅ¥
download.title=StiahnuÅ¥
download_label=StiahnuÅ¥
bookmark.title=AktuÃ¡lne zobrazenie (kopÃ­rovaÅ¥ alebo otvoriÅ¥ v novom okne)
bookmark_label=AktuÃ¡lne zobrazenie

# Secondary toolbar and context menu
tools.title=NÃ¡stroje
tools_label=NÃ¡stroje
first_page.title=PrejsÅ¥ na prvÃº stranu
first_page.label=PrejsÅ¥ na prvÃº stranu
first_page_label=PrejsÅ¥ na prvÃº stranu
last_page.title=PrejsÅ¥ na poslednÃº stranu
last_page.label=PrejsÅ¥ na poslednÃº stranu
last_page_label=PrejsÅ¥ na poslednÃº stranu
page_rotate_cw.title=OtoÄiÅ¥ v smere hodinovÃ½ch ruÄiÄiek
page_rotate_cw.label=OtoÄiÅ¥ v smere hodinovÃ½ch ruÄiÄiek
page_rotate_cw_label=OtoÄiÅ¥ v smere hodinovÃ½ch ruÄiÄiek
page_rotate_ccw.title=OtoÄiÅ¥ proti smeru hodinovÃ½ch ruÄiÄiek
page_rotate_ccw.label=OtoÄiÅ¥ proti smeru hodinovÃ½ch ruÄiÄiek
page_rotate_ccw_label=OtoÄiÅ¥ proti smeru hodinovÃ½ch ruÄiÄiek

cursor_text_select_tool.title=PovoliÅ¥ vÃ½ber textu
cursor_text_select_tool_label=VÃ½ber textu
cursor_hand_tool.title=PovoliÅ¥ nÃ¡stroj ruka
cursor_hand_tool_label=NÃ¡stroj ruka

scroll_vertical.title=PouÅ¾Ã­vaÅ¥ zvislÃ© posÃºvanie
scroll_vertical_label=ZvislÃ© posÃºvanie
scroll_horizontal.title=PouÅ¾Ã­vaÅ¥ vodorovnÃ© posÃºvanie
scroll_horizontal_label=VodorovnÃ© posÃºvanie
scroll_wrapped.title=PouÅ¾iÅ¥ postupnÃ© posÃºvanie
scroll_wrapped_label=PostupnÃ© posÃºvanie

spread_none.title=NezdruÅ¾ovaÅ¥ strÃ¡nky
spread_none_label=Å½iadne zdruÅ¾ovanie
spread_odd.title=ZdruÅ¾Ã­ strÃ¡nky a umiestni nepÃ¡rne strÃ¡nky vÄ¾avo
spread_odd_label=ZdruÅ¾iÅ¥ strÃ¡nky (nepÃ¡rne vÄ¾avo)
spread_even.title=ZdruÅ¾Ã­ strÃ¡nky a umiestni pÃ¡rne strÃ¡nky vÄ¾avo
spread_even_label=ZdruÅ¾iÅ¥ strÃ¡nky (pÃ¡rne vÄ¾avo)

# Document properties dialog box
document_properties.title=Vlastnosti dokumentuâ¦
document_properties_label=Vlastnosti dokumentuâ¦
document_properties_file_name=NÃ¡zov sÃºboru:
document_properties_file_size=VeÄ¾kosÅ¥ sÃºboru:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} kB ({{size_b}} bajtov)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bajtov)
document_properties_title=NÃ¡zov:
document_properties_author=Autor:
document_properties_subject=Predmet:
document_properties_keywords=KÄ¾ÃºÄovÃ© slovÃ¡:
document_properties_creation_date=DÃ¡tum vytvorenia:
document_properties_modification_date=DÃ¡tum Ãºpravy:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Vytvoril:
document_properties_producer=Tvorca PDF:
document_properties_version=Verzia PDF:
document_properties_page_count=PoÄet strÃ¡n:
document_properties_page_size=VeÄ¾kosÅ¥ strÃ¡nky:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=na vÃ½Å¡ku
document_properties_page_size_orientation_landscape=na Å¡Ã­rku
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=List
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=RÃ½chle Web View:
document_properties_linearized_yes=Ãno
document_properties_linearized_no=Nie
document_properties_close=ZavrieÅ¥

print_progress_message=PrÃ­prava dokumentu na tlaÄâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ZruÅ¡iÅ¥

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=PrepnÃºÅ¥ boÄnÃ½ panel
toggle_sidebar_notification.title=PrepnÃºÅ¥ boÄnÃ½ panel (dokument obsahuje osnovu/prÃ­lohy)
toggle_sidebar_notification2.title=PrepnÃºÅ¥ boÄnÃ½ panel (dokument obsahuje osnovu/prÃ­lohy/vrstvy)
toggle_sidebar_label=PrepnÃºÅ¥ boÄnÃ½ panel
document_outline.title=ZobraziÅ¥ osnovu dokumentu (dvojitÃ½m kliknutÃ­m rozbalÃ­te/zbalÃ­te vÅ¡etky poloÅ¾ky)
document_outline_label=Osnova dokumentu
attachments.title=ZobraziÅ¥ prÃ­lohy
attachments_label=PrÃ­lohy
layers.title=ZobraziÅ¥ vrstvy (dvojitÃ½m kliknutÃ­m uvediete vÅ¡etky vrstvy do pÃ´vodnÃ©ho stavu)
layers_label=Vrstvy
thumbs.title=ZobraziÅ¥ miniatÃºry
thumbs_label=MiniatÃºry
current_outline_item.title=NÃ¡jsÅ¥ aktuÃ¡lnu poloÅ¾ku v osnove
current_outline_item_label=AktuÃ¡lna poloÅ¾ka v osnove
findbar.title=HÄ¾adaÅ¥ v dokumente
findbar_label=HÄ¾adaÅ¥

additional_layers=ÄalÅ¡ie vrstvy
# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Strana {{page}}
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Strana {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Strana {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=MiniatÃºra strany {{page}}

# Find panel button title and messages
find_input.title=HÄ¾adaÅ¥
find_input.placeholder=HÄ¾adaÅ¥ v dokumenteâ¦
find_previous.title=VyhÄ¾adaÅ¥ predchÃ¡dzajÃºci vÃ½skyt reÅ¥azca
find_previous_label=PredchÃ¡dzajÃºce
find_next.title=VyhÄ¾adaÅ¥ ÄalÅ¡Ã­ vÃ½skyt reÅ¥azca
find_next_label=ÄalÅ¡ie
find_highlight=ZvÃ½razniÅ¥ vÅ¡etky
find_match_case_label=RozliÅ¡ovaÅ¥ veÄ¾kosÅ¥ pÃ­smen
find_entire_word_label=CelÃ© slovÃ¡
find_reached_top=Bol dosiahnutÃ½ zaÄiatok strÃ¡nky, pokraÄuje sa od konca
find_reached_bottom=Bol dosiahnutÃ½ koniec strÃ¡nky, pokraÄuje sa od zaÄiatku
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}}. z {{total}} vÃ½sledku
find_match_count[two]={{current}}. z {{total}} vÃ½sledkov
find_match_count[few]={{current}}. z {{total}} vÃ½sledkov
find_match_count[many]={{current}}. z {{total}} vÃ½sledkov
find_match_count[other]={{current}}. z {{total}} vÃ½sledkov
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Viac neÅ¾ {{limit}} vÃ½sledkov
find_match_count_limit[one]=Viac neÅ¾ {{limit}} vÃ½sledok
find_match_count_limit[two]=Viac neÅ¾ {{limit}} vÃ½sledky
find_match_count_limit[few]=Viac neÅ¾ {{limit}} vÃ½sledky
find_match_count_limit[many]=Viac neÅ¾ {{limit}} vÃ½sledkov
find_match_count_limit[other]=Viac neÅ¾ {{limit}} vÃ½sledkov
find_not_found=VÃ½raz nebol nÃ¡jdenÃ½

# Error panel labels
error_more_info=ÄalÅ¡ie informÃ¡cie
error_less_info=Menej informÃ¡ciÃ­
error_close=ZavrieÅ¥
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (zostavenie: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=SprÃ¡va: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ZÃ¡sobnÃ­k: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=SÃºbor: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Riadok: {{line}}
rendering_error=Pri vykresÄ¾ovanÃ­ strÃ¡nky sa vyskytla chyba.

# Predefined zoom values
page_scale_width=Na Å¡Ã­rku strany
page_scale_fit=Na veÄ¾kosÅ¥ strany
page_scale_auto=AutomatickÃ¡ veÄ¾kosÅ¥
page_scale_actual=SkutoÄnÃ¡ veÄ¾kosÅ¥
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}} %

# Loading indicator messages
loading_error_indicator=Chyba

# Loading indicator messages
loading=NaÄÃ­tava saâ¦
loading_error=PoÄas naÄÃ­tavania dokumentu PDF sa vyskytla chyba.
invalid_file_error=NeplatnÃ½ alebo poÅ¡kodenÃ½ sÃºbor PDF.
missing_file_error=ChÃ½bajÃºci sÃºbor PDF.
unexpected_response_error=NeoÄakÃ¡vanÃ¡ odpoveÄ zo servera.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[AnotÃ¡cia typu {{type}}]
password_label=Ak chcete otvoriÅ¥ tento sÃºbor PDF, zadajte jeho heslo.
password_invalid=Heslo nie je platnÃ©. SkÃºste to znova.
password_ok=OK
password_cancel=ZruÅ¡iÅ¥

printing_not_supported=Upozornenie: tlaÄ nie je v tomto prehliadaÄi plne podporovanÃ¡.
printing_not_ready=Upozornenie: sÃºbor PDF nie je plne naÄÃ­tanÃ½ pre tlaÄ.
web_fonts_disabled=WebovÃ© pÃ­sma sÃº vypnutÃ©: nie je moÅ¾nÃ© pouÅ¾iÅ¥ pÃ­sma vloÅ¾enÃ© do sÃºboru PDF.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
unsupported_feature_signatures=Tento dokument PDF obsahuje digitÃ¡lne podpisy. Overenie podpisov nie je podporovanÃ©.
