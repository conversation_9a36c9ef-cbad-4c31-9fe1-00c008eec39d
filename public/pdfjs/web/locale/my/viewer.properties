# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=á¡áááº áá¬áá»ááºáá¾á¬
previous_label=á¡áááºáá±áá¬
next.title=áá¾á±á· áá¬áá»ááºáá¾á¬
next_label=áá±á¬ááºááá¯

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=áá¬áá»ááºáá¾á¬
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} á
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pagesCount}} á {{pageNumber}})

zoom_out.title=áá»á¯á¶á·áá«
zoom_out_label=áá»á¯á¶á·áá«
zoom_in.title=áá»á²á·áá«
zoom_in_label=áá»á²á·áá«
zoom.title=áá»á¯á¶á·/áá»á²á·áá«
presentation_mode.title=áá½á±á¸áá½á±á¸áááºáá¼ááááºáá­á¯á· áá°á¸áá¼á±á¬ááºá¸áá«
presentation_mode_label=áá½á±á¸áá½á±á¸áááºáá¼ááááº
open_file.title=áá­á¯ááºá¡á¬á¸áá½áá·áºáá«á
open_file_label=áá½ááºá·áá«
print.title=áá¯á¶áá¾á­á¯ááºáá«
print_label=áá¯á¶áá¾á­á¯ááºáá«
download.title=áá°á¸áá½á²
download_label=áá°á¸áá½á²
bookmark.title=áááºáá¾á­ áá¼ááºáá½ááºá¸ (áááºá¸áá­á¯á¸á¡áááºáá¾á¬ áá°á¸áá« áá­á¯á·ááá¯ááº áá½áá·áºáá«)
bookmark_label=áááºáá¾á­ áá¼ááºáá½ááºá¸

# Secondary toolbar and context menu
tools.title=áá­áá­áá¬áá»á¬á¸
tools_label=áá­áá­áá¬áá»á¬á¸
first_page.title=ááá áá¬áá»ááºáá¾á¬áá­á¯á·
first_page.label=ááá áá¬áá»ááºáá¾á¬áá­á¯á·
first_page_label=ááá áá¬áá»ááºáá¾á¬áá­á¯á·
last_page.title=áá±á¬ááºáá¯á¶á¸ áá¬áá»ááºáá¾á¬áá­á¯á·
last_page.label=áá±á¬ááºáá¯á¶á¸ áá¬áá»ááºáá¾á¬áá­á¯á·
last_page_label=áá±á¬ááºáá¯á¶á¸ áá¬áá»ááºáá¾á¬áá­á¯á·
page_rotate_cw.title=áá¬áá®áááºáá¶ á¡áá­á¯ááºá¸
page_rotate_cw.label=áá¬áá®áááºáá¶ á¡áá­á¯ááºá¸
page_rotate_cw_label=áá¬áá®áááºáá¶ á¡áá­á¯ááºá¸
page_rotate_ccw.title=áá¬áá®áááºáá¶ áá¼á±á¬ááºá¸áá¼ááº
page_rotate_ccw.label=áá¬áá®áááºáá¶ áá¼á±á¬ááºá¸áá¼ááº
page_rotate_ccw_label=áá¬áá®áááºáá¶ áá¼á±á¬ááºá¸áá¼ááº




# Document properties dialog box
document_properties.title=áá¾ááºáááºá¸áá¾ááºáá¬ áá¯ááºááá¹áá­áá»á¬á¸
document_properties_label=áá¾ááºáááºá¸áá¾ááºáá¬ áá¯ááºááá¹áá­áá»á¬á¸
document_properties_file_name=áá­á¯ááº :
document_properties_file_size=áá­á¯ááºáá­á¯ááº :
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} áá®áá­á¯áá­á¯ááº ({{size_b}}áá­á¯ááº)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=áá±á«ááºá¸áááºâ -
document_properties_author=áá±á¸áá¬á¸áá°:
document_properties_subject=á¡áá¼á±á¬ááºá¸á¡áá¬:\u0020
document_properties_keywords=áá±á¬á·áá»ááº áá¬áá¯á¶á¸:
document_properties_creation_date=áá¯ááºáá¯ááºáááºáá½á²:
document_properties_modification_date=áá¼ááºáááºáááºáá½á²:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=áááºáá®á¸áá°:
document_properties_producer=PDF áá¯ááºáá¯ááºáá°:
document_properties_version=PDF áá¬á¸áá¾ááºá¸:
document_properties_page_count=áá¬áá»ááºáá¾á¬á¡áá±á¡áá½ááº:
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_close=áá­ááº

print_progress_message=Preparing document for printingâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=áááºâáá»ááºáá«

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=áá±á¸áááºá¸áá½ááºá·áá­ááº
toggle_sidebar_notification.title=áá±á¸áá¬á¸áááºá¸áá­á¯ á¡áá½áá·áº/á¡áá­ááº áá¯ááºáááº (áá¬áááºá¸áá½ááº outline/attachments áá«áááºáá­á¯ááºáááº)
toggle_sidebar_label=áá½ááºá·áá­ááº ááá­á¯ááºáá«
document_outline.title=áá¬áááºá¸á¡áá»ááºá¸áá»á¯ááºáá­á¯ áá¼áá« (áá¬áááºá¸á¡á¬á¸áá¯á¶á¸áá­á¯ áá»á¯á¶á·/áá»á²á·áááº ááááºáá¾ááºáá»ááºáá¾á­ááºáá«)
document_outline_label=áá¬áááºá¸á¡áá»ááºá¸áá»á¯ááº
attachments.title=áá½á²áá»ááºáá»á¬á¸ áá¼áá«
attachments_label=áá½á²áá¬á¸áá»ááºáá»á¬á¸
thumbs.title=áá¯á¶áá­ááºáááºáá»á¬á¸áá­á¯ áá¼áá«
thumbs_label=áá¯á¶áá­ááºáááºáá»á¬á¸
findbar.title=Find in Document
findbar_label=áá¾á¬áá½á±áá«

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=áá¬áá»ááºáá¾á¬ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=áá¬áá»ááºáá¾á¬áá²á· áá¯á¶áá­ááºáááº {{page}}

# Find panel button title and messages
find_input.title=áá¾á¬áá½á±áá«
find_input.placeholder=áá¬áááºá¸áá²áá½ááº áá¾á¬áá½á±áááºâ¦
find_previous.title=ááá¬á¸áá¯áá²á· á¡áááº âáá¼ááºáá½á¬á¸áá¾á¯áá­á¯ áá¾á¬áá½á±áá«
find_previous_label=áá±á¬ááºáá­á¯á·
find_next.title=ááá¬á¸áá¯áá²á· áá±á¬ááºáááº âáá¼ááºáá½á¬á¸áá¾á¯áá­á¯ áá¾á¬áá½á±áá«
find_next_label=áá¾á±á·áá­á¯á·
find_highlight=á¡á¬á¸áá¯á¶á¸áá­á¯ áá»ááºá¸áá¬á¸áá«
find_match_case_label=áá¬áá¯á¶á¸ áá­á¯ááºáá­á¯ááºáá«
find_reached_top=áá¬áá»ááºáá¾á¬áá­ááº áá±á¬ááºáá±áá¼á®á á¡áá¯á¶á¸ááá± áá¼ááºááá«
find_reached_bottom=áá¬áá»ááºáá¾á¬á¡áá¯á¶á¸ áá±á¬ááºáá±áá¼á®á áá­ááºááá± áá¼ááºááá«
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_not_found=ááá¬á¸áá¯ ááá½á±á·ááá°á¸

# Error panel labels
error_more_info=áá±á¬ááºáááºá¡áá»ááºá¡áááºáá»á¬á¸
error_less_info=á¡áááºá¸áááºáá»á¾áá±á¬ ááááºá¸á¡áá»ááºá¡áááº
error_close=áá­ááº
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=áááºáá±á· - {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=á¡áááº - {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=áá­á¯ááº {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=áá­á¯ááºá¸ - {{line}}
rendering_error=áá¬áá»ááºáá¾á¬áá­á¯ áá¯á¶áá±á¬áºáá±áá»á­ááºáá¾á¬ á¡áá¾á¬á¸áááºáá¯áá½á±á·ááá«áááºá

# Predefined zoom values
page_scale_width=áá¬áá»ááºáá¾á¬ á¡áá»ááº
page_scale_fit=áá¬áá»ááºáá¾á¬ áá½ááºáá­
page_scale_auto=á¡áá­á¯á¡áá»á±á¬ááº áá»á¯á¶á·áá»á²á·
page_scale_actual=á¡áá¾ááºááááºáá¾á­áá²á· á¡áá½ááº
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=á¡áá¾á¬á¸
loading_error=PDF áá­á¯ááº áá­á¯áá½á²áááºáá±áá»á­ááºáá¾á¬ á¡áá¾á¬á¸áááºáá¯áá½á±á·ááá«áááºá
invalid_file_error=áááá±á¬ áá­á¯á· áá»ááºáá±áá±á¬ PDF áá­á¯ááº
missing_file_error=PDF áá»á±á¬ááºáá¯á¶á¸
unexpected_response_error=ááá»á¾á±á¬áºáááºá·áá¬á¸áá±á¬ áá¬áá¬áá¾ áá¼ááºáá¼á¬á¸áá»ááº

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} á¡áá­áá¹áá¬ááºáá½áá·áºáá­á¯áá»ááº]
password_label=ááá¯ PDF áá­á¯ áá½áá·áºáááº ááá¬á¸áá¾ááºáá­á¯ áá­á¯ááºáá«á
password_invalid=áá¬áá¾ááº áá¾á¬á¸áááºá áááºáá¼á­á¯á¸áá¬á¸áá¼áá·áºáá«á
password_ok=OK
password_cancel=áááºâáá»ááºáá«

printing_not_supported=ááá­áá±á¸áá»ááºááááá·áºáá¯ááºáá¼ááºá¸áá­á¯á¤ááá±á¬ááºáá¬áááº áá¼áá·áºááá½á¬áá±á¬ááºáá¶á·ááá¬á¸áá« á
printing_not_ready=ááá­áá±á¸áá»ááº: ááá¯ PDF áá­á¯ááºáááº áá¯á¶áá¾á­ááºáááº ááá¼áá·áºáá¯á¶áá«
web_fonts_disabled=Web fonts are disabled: unable to use embedded PDF fonts.
