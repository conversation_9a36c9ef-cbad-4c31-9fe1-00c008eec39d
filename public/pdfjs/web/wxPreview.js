var url = location.href //https://www.xxxx.html?param=aaa&signId=11211
params = url.split("?")[1].split("&")
var dic_param = {}
for (let index = 0; index < params.length; index++) {
  var kv = params[index].split("=")
  dic_param[kv[0]] = kv[1]
}
console.log(dic_param)
window.wx.miniProgram.getEnv(function (res) {
  if (res.miniprogram) {
    var viewerContainer = document.getElementById("viewerContainer")
    if (dic_param.tip) {
      var wxtip = document.getElementById("wxtip")
      wxtip.innerText = decodeURIComponent(dic_param.tip)

      wxtip.style.display = "block"

      viewerContainer.style.top = "12vh";
    } else {
      viewerContainer.style.top = "0";
    }
    if (dic_param.isSign == '1') {
      var wxPreview = document.getElementById("wxPreview")
      wxPreview.style.display = "block"
    }

    var toolbarContainer = document.getElementById("toolbarContainer")
    toolbarContainer.style.display = "none"

    var viewer = document.getElementById("viewer")
    viewer.style.marginBottom = "15vh"
  }
})
function wxPreviewChange() {
  var radio = document.getElementById("wxPreview").getElementsByClassName('radio');
  var button = document.getElementById("wxPreview").getElementsByClassName('button');
  /*用ByName是为了取到所有的radio*/
  if (radio[0].checked) {
    button[0].disabled = '';
  }
}

function wxPreviewClick() {
  window.wx.miniProgram.getEnv(function (res) {
    if (res.miniprogram) {
      //如果当前是小程序环境
      // contractTemplate
      window.wx.miniProgram.redirectTo({
        url: `/packageContract/contract/signList?contractTemplate=${dic_param.contractTemplate}&appendixPath=${dic_param.file}`,  //跳转页面的路径，可带参数 ？隔开，不同参数用 & 分隔；相对路径，不需要.wxml后缀
      })
    }
  })


}