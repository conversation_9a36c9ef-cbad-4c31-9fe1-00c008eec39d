// page
var interval = setInterval('loadPdf()', 1000)
var viewerContainerPage = []
// document.getElementById("viewer")
var url = location.href
params = url.split('?')[1].split('&')
var dic_param = {}
for (let index = 0; index < params.length; index++) {
    var kv = params[index].split('=')
    dic_param[kv[0]] = kv[1]
}
var viewerContainer = document.getElementById('viewerContainer')
function loadPdf() {
    if (PDFViewerApplication.pdfDocument == null) {
        console.info('Loading...')
    } else {
        clearInterval(interval)
        viewerContainer = document.getElementById('viewerContainer')
        viewerContainerPage = viewerContainer.getElementsByClassName('page')
        //pdf中input与textarea只读
        getPageNumberExternal(PDFViewerApplication.page)
        //绑定点击事件-;
        if (parent?.window?.sealUrlPosition) {
            // console.log(document.getElementById("sealUrl"))
            for (let i = 0; i < viewerContainerPage.length; i++) {
                viewerContainerPage[i].addEventListener('dblclick', function (e) {
                    viewerContainerPageClick(e, viewerContainerPage[i])
                })
            }
        }
    }
}

function setViewDefaultValue(domList, info) {
    const dateKeys = [
        'contractStartDate',
        'contractEndDate',
        'sendStartDate',
        'probationStartDate',
        'probationEndDate',
        // 'firstPartSignatureDate',
        // 'closingDate',
        'promiseDate',
    ]
    info.forEach((item) => {
        let docItems = domList.filter((i) => i.name == item.name)

        if (item.type == 'date' && item.value) {
            if (dateKeys.includes(item.name)) {
                const arr = ['Year', 'Month', 'Day']
                const dateArr = item.value.split('-')
                arr.forEach((str, index) => {
                    const dateDoms = domList.filter((i) => i.name == `${item.name}${str}`)
                    if (dateDoms.length) {
                        if (item.value) {
                            dateDoms.forEach((el) => {
                                el.value = dateArr[index]
                            })
                        } else {
                            dateDoms.forEach((el) => {
                                el.value = ''
                            })
                        }
                    }
                })
            } else {
                if (docItems.length) {
                    if (item.value) {
                        docItems.forEach((el) => {
                            el.value = item.value
                        })
                    } else {
                        docItems.forEach((el) => {
                            el.value = ''
                        })
                    }
                }
            }
        } else if (item.type == 'change') {
            if (docItems.length) {
                if (item.name != 'seal') {
                    if (item.value) {
                        docItems.forEach((ele) => {
                            ele.value = item.options.find((el) => (el.value = item.value)).label || ''
                        })
                    } else {
                        docItems.forEach((el) => {
                            el.value = ''
                        })
                    }
                }
            }
        } else {
            if (docItems.length) {
                if (item.value) {
                    docItems.forEach((el) => {
                        el.value = item.value
                    })
                } else {
                    docItems.forEach((el) => {
                        el.value = ''
                    })
                }
            }
        }
    })
}

function getPageNumberExternal(num) {
    viewerContainerPage = viewerContainerPage
        ? viewerContainerPage
        : document.getElementById('viewerContainer').getElementsByClassName('page')
    let input = viewerContainerPage[num - 1]?.getElementsByTagName('input')
    for (var i = 0; i < input?.length; i++) {
        input[i].disabled = true
        input[i].autocomplete = 'off'
        input[i].placeholder = ''
    }
    let textarea = viewerContainerPage[num - 1]?.getElementsByTagName('textarea')
    for (var i = 0; i < textarea?.length; i++) {
        textarea[i].disabled = true
        textarea[i].autocomplete = 'off'
        textarea[i].placeholder = ''
    }
    if (dic_param.fillInfo) {
        var fillInfo = JSON.parse(decodeURIComponent(dic_param.fillInfo))
        setViewDefaultValue([...(input || []), ...(textarea || [])], fillInfo)
    }
}
function viewerContainerPageClick(e, dom) {
    //获取缩放比例

    parent.window.sealUrlDele = sealUrlDele
    if (!parent?.window?.sealUrl) {
        alert('请先选择印章')

        return
    }
    createDom(dom, e, parent?.window?.sealUrl)
}
var sealUrlDom = document.createElement('div')
function sealUrlDele() {
    sealUrlDom.remove()
}
//创建dom
function createDom(dom, e, sealUrl) {
    var width = 166 * PDFViewerApplication.scale
    var height = 166 * PDFViewerApplication.scale

    if (e.target.getAttribute('class') != 'textLayer') {
        return
    }
    if (sealUrlDom) {
        sealUrlDom.remove()
    }
    function sealUrlDele() {
        sealUrlDom.remove()
    }
    sealUrlDom.innerHTML = `<div id="sealUrl"  draggable="true" style="position: absolute;cursor:pointer;">
<p class="sealUrlDele">&times;</p>
<img width="${width}px" height="${height}px" src="${sealUrl}" alt="">
</div>`

    dom.appendChild(sealUrlDom, e)

    let box = document.getElementById('sealUrl')
    box.getElementsByClassName('sealUrlDele')[0].addEventListener('click', function () {
        sealUrlDom.remove()
        parent?.window?.sealUrlPosition({ x: 0, y: 0 }, NaN)
    })
    let wrap = dom

    var boxScrollTop = 0
    var scrollposition = function (boxLeft, boxTop) {
        let newBoxTop = boxTop - boxScrollTop
        //限制范围
        let maxLeft = wrap.offsetWidth - box.offsetWidth - 18
        let maxTop = wrap.offsetHeight - box.offsetHeight - 14
        let minLeft = 0
        let minTop = 0
        if (boxLeft < minLeft) {
            boxLeft = minLeft
        }
        if (boxLeft > maxLeft) {
            boxLeft = maxLeft
        }
        if (newBoxTop < minTop) {
            newBoxTop = minTop
        }
        if (newBoxTop > maxTop) {
            newBoxTop = maxTop
        }
        //设置left，top值
        box.style.left = boxLeft + 'px'
        box.style.top = newBoxTop + 'px'
        parent?.window?.sealUrlPosition(
            {
                x: boxLeft + width * 0.75,
                y: newBoxTop + height * 0.25,
                width: parseInt(dom.style.width),
                height: parseInt(dom.style.height),
            },
            dom.getAttribute('data-page-number'),
        )
    }
    scrollposition(e.layerX - width * 0.5, e.layerY - height * 0.5)

    //给绿色的div添加onmousedown事件
    box.onmousedown = function (ev) {
        let scrollTop = viewerContainer.scrollTop

        let e = ev || window.event
        //计算出鼠标按下的点到box的左侧边缘和上侧边缘
        let restLeft = e.clientX - wrap.offsetLeft - box.offsetLeft
        let restTop = e.clientY - wrap.offsetTop - box.offsetTop

        var boxLeft = 0
        var boxTop = 0

        function viewerContainerScroll() {
            boxScrollTop = scrollTop - viewerContainer.scrollTop
            scrollposition(boxLeft, boxTop)
        }
        viewerContainer.addEventListener('scroll', viewerContainerScroll)

        // 针对低版本IE的方法，setCapture方法在oBox元素和下面的元素中间添加了一个透明层
        if (box.setCapture) {
            box.setCapture()
        }

        //移动的时候计算left和top值，每次移动都会生效
        document.onmousemove = function (ev) {
            let e = ev || window.event
            //计算出left值和top值
            boxLeft = e.clientX - restLeft - wrap.offsetLeft
            boxTop = e.clientY - restTop - wrap.offsetTop

            scrollposition(boxLeft, boxTop)
        }

        document.onmouseup = function () {
            //销毁onmousemove和onmouseup事件
            document.onmousemove = null
            document.onmouseup = null
            viewerContainer.removeEventListener('scroll', viewerContainerScroll)
            // 去掉透明层
            if (box.releaseCapture) {
                box.releaseCapture()
            }
        }

        if (parent.document) {
            parent.document.onmouseup = function () {
                //销毁onmousemove和onmouseup事件
                document.onmousemove = null
                document.onmouseup = null

                viewerContainer.removeEventListener('scroll', viewerContainerScroll)
                // 去掉透明层
                if (box.releaseCapture) {
                    box.releaseCapture()
                }
            }
        }

        //阻止浏览器默认行为
        return false
    }
}
