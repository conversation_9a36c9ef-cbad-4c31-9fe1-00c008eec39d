import type { ConfigEnv, UserConfig } from 'vite'

import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import icons from './src/utils/icons'
import { resolve } from 'path'
import { loadEnv } from 'vite'

function pathResolve(dir) {
    return resolve(__dirname, '.', dir)
}

export default ({ mode }: ConfigEnv): UserConfig => {
    const root = process.cwd()
    const { VITE_OUT_DIR, VITE_DROP_CONSOLE } = loadEnv(mode, root) as any
    return {
        root,
        base: './',
        build: {
            target: 'es2015',
            outDir: VITE_OUT_DIR,
            terserOptions: {
                compress: {
                    keep_infinity: true,
                    // Used to delete console in production environment
                    drop_console: VITE_DROP_CONSOLE,
                },
            },
            // Turning off brotliSize display can slightly reduce packaging time
            brotliSize: false,
            chunkSizeWarningLimit: 2000,
        },
        plugins: [vue(), vueJsx()],
        server: {
            // port: 3001,
            host: true,
            open: true,
            // Load proxy configuration from .env
            // proxy: createProxy(VITE_PROXY),
        },
        resolve: {
            alias: [
                // /@/xxxx => src/xxxx
                {
                    find: /\/@\//,
                    replacement: pathResolve('src') + '/',
                },
                // /#/xxxx => types/xxxx
                {
                    find: /\/#\//,
                    replacement: pathResolve('types') + '/',
                },
            ],
        },
        // 配置Dep优化行为
        optimizeDeps: {
            include: ['lodash', '@ant-design/colors', 'moment/dist/locale/zh-cn', ...icons],
        },
        css: {
            preprocessorOptions: {
                less: {
                    modifyVars: {
                        hack: `true; @import (reference) "${resolve('src/styles/index.less')}";`,
                    },
                    javascriptEnabled: true,
                },
            },
        },
    }
}
