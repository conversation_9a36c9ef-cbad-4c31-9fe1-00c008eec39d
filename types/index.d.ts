declare interface Fn<T = any, R = T> {
    (...arg: T[]): R
}

declare type Recordable<T = any> = Record<string, T>

declare type RefLabelValueOptions = { value: LabelValueOption[]; [key?: string]: any }
declare type LabelValueOptions = LabelValueOption[]

declare type LabelValueOption = {
    label: string | number
    value: any
    [key?: string]: any
}

declare type inObject = {
    [key: string]: any
}
