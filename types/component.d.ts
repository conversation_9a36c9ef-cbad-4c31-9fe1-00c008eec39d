export interface EditModalOption {
    label: string
    name: string // 字段
    default?: string | boolean | number
    ruleType?: string
    type?: 'string' | 'textarea' | 'number' | 'select' | 'datetime' | 'change' | 'switch' | 'SelectDic' | 'month'
    option?: LabelValueOptions | RefLabelValueOptions // select option
    multiple?: boolean // select 是否可多选
    placeholder?: string
    required?: Boolean // 表单验证 是否必需 默认为 true
    changedByField?: string // select option 跟随其他字段值改变
    changedMethod?: (val) => {} // 获取option的方法
    show?: Boolean
    [key: string]: any
    attr?: Recordable
    getMethod?: () => Promise<any>
}

export interface SearchBarOption {
    label: string
    key: string
    type?:
        | 'string'
        | 'number'
        | 'numberrange'
        | 'select'
        | 'date'
        | 'datetime'
        | 'daterange'
        | 'monthrange'
        | 'datetimerange'
        | 'month'
        | 'slot'
        | 'selectSlot'
        | 'clientSelectTree'
        | 'defaultmonthrange'
        | 'yearrange'
        | 'quterSelect'
        | 'year'
    options?: LabelValueOptions | RefLabelValueOptions | String[]
    allowClear?: Boolean
    multiple?: Boolean
    placeholder?: string | string[]
    changedBy?: string
    show?: Boolean | { value: Boolean; [key?: string]: any }
    size?: 'large' | 'default' | 'small'
    maxTag?: number | string //最多显示个数
    changedMethod?: (val) => {}
    getMethod?: () => {}
    [key: string]: any
}

export interface valuesAndRules {
    label: string //名称汉字
    name: string //字段名称
    type?:
        | 'string'
        | 'select'
        | 'change'
        | 'number'
        | 'datetime'
        | 'date'
        | 'switch'
        | 'textarea'
        | 'SelectDic'
        | 'isTrue'
        | 'slots'
        | any
    trigger?: string //失焦或回车的触发条件
    pattern?: any //正则
    validator?: any //自定义校验
    default?: any //默认值

    ruleType?: string //数据类型
    option?: LabelValueOptions | RefLabelValueOptions // select option
    multiple?: boolean | { value: Boolean; [key?: string]: any } // select 是否可多选
    placeholder?: string
    required?: Boolean | { value: Boolean; [key?: string]: any } // 表单验证 是否必需 默认为 true
    changedByField?: string // select option 跟随其他字段值改变
    changedMethod?: (val) => {} // 获取option的方法
    show?: Boolean | { value: Boolean; [key?: string]: any }

    onBlur?: any
    onChange?: any

    disabled?: Boolean | { value: Boolean; [key?: string]: any } //只读
    [key: string]: any
}

export interface FileItem {
    id: string
    name: string
    originName: string
    fileUrl: string
    fileType: string
    filePath: string
}
