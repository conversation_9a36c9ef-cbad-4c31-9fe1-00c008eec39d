{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": false, "noUnusedParameters": false, "experimentalDecorators": true, "lib": ["dom", "esnext"], "types": ["vite/client"], "typeRoots": ["./node_modules/@types/", "./types"], "noImplicitAny": false, "skipLibCheck": true, "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "types/*.d.ts", "types/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}