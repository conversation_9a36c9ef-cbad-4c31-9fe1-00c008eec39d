{"name": "hr-platform-admin", "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build && node build/writeFile.ts", "build-prod": "vite build --mode production && node build/writeFile.ts", "deploy": "node build/deploy.ts integration", "bootstrap": "yarn install", "reinstall": "rimraf yarn.lock && rimraf package-lock.json && rimraf node_modules && yarn bootstrap"}, "dependencies": {"@layui/layer-vue": "^1.4.2", "@surely-vue/table": "^2.0.5", "ant-design-vue": "2.2.8", "axios": "^0.24.0", "crypto-js": "^4.1.1", "echarts": "^5.2.0", "file-saver": "^2.0.5", "js-base64": "^3.7.2", "jszip": "^3.7.1", "moment": "^2.29.1", "number-precision": "^1.5.1", "pinia": "^2.0.0", "sortablejs": "^1.14.0", "uuid": "^9.0.0", "vue": "3.2.21", "vue-router": "4.0.12", "wangeditor": "^4.7.8", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "@vitejs/plugin-vue": "^1.9.4", "@vitejs/plugin-vue-jsx": "^1.2.0", "@vue/compiler-sfc": "3.2.21", "cross-env": "^7.0.3", "eslint": "^8.1.0", "eslint-config-prettier": "^8.3.0", "eslint-define-config": "^1.1.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.1", "prettier": "^2.4.1", "typescript": "^4.4.4", "vite": "2.6.13", "vue-eslint-parser": "^8.0.1"}}