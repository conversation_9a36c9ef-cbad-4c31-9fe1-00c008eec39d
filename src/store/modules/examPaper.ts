import { defineStore } from 'pinia'
import store from '/@/store'
import dictionaryDataStore from './dictionaryData'
import { SectionToChinese } from '/@/utils/index'

interface examPaperState {
    // 试卷
    examPaper: inObject
    stepAndModule: inObject // 弹窗
    topicList: Array<inObject>
    randomTopic: inObject | null
    exchangeTopic: inObject
    innerContent: string
    handleType: string
    exchangeVisible: boolean
    exchangeType: number
    editStationIds: inObject
    questionTypeList: Array<inObject>
    questionProList: Array<inObject>
    scoreList: Array<inObject>
}

export const examPaperStore = defineStore({
    id: 'examPaper',
    state: (): examPaperState => ({
        examPaper: {},
        stepAndModule: {
            step: 0,
            mode: '',
            title: '',
            last: '',
        },
        topicList: [],
        randomTopic: null,
        exchangeTopic: {},
        innerContent: '',
        handleType: '',
        exchangeVisible: false,
        exchangeType: 1, // 1 随机更换 2 编辑更换
        editStationIds: [],
        questionTypeList: [],
        questionProList: [],
        scoreList: [],
    }),
    getters: {
        getStepAndModule(): examPaperState['stepAndModule'] {
            return this.stepAndModule
        },
        getExamPaper(): examPaperState['examPaper'] {
            return this.examPaper
        },
        getTopicList(): examPaperState['topicList'] {
            return this.topicList
        },
        getRandomTopic(): examPaperState['randomTopic'] {
            return this.randomTopic
        },
        getInnerContent(): examPaperState['innerContent'] {
            return this.innerContent
        },
        getHandleType(): examPaperState['handleType'] {
            return this.handleType
        },
        getExchangeVisible(): examPaperState['exchangeVisible'] {
            return this.exchangeVisible
        },
        getExchangeType(): examPaperState['exchangeType'] {
            return this.exchangeType
        },
        getEditStationIds(): examPaperState['editStationIds'] {
            return this.editStationIds
        },
        getExchangeTopic(): examPaperState['exchangeTopic'] {
            return this.exchangeTopic
        },
        getQuestionTypeList(): examPaperState['questionTypeList'] {
            return this.questionTypeList
        },
        getQuestionProList(): examPaperState['questionProList'] {
            return this.questionProList
        },
        getScoreList(): examPaperState['scoreList'] {
            return this.scoreList
        },
    },
    actions: {
        changeTopicOrder(payload) {
            const val = this.topicList[payload.oldIndex]
            this.topicList.splice(payload.oldIndex, 1)
            this.topicList.splice(payload.newIndex, 0, val)
        },
        setExchangeVisible(payload: inObject) {
            this.exchangeVisible = payload.flag
            this.exchangeType = payload.type
        },
        setEditStationIds(payload: inObject) {
            this.editStationIds = payload
        },
        setStepAndModule(obj: inObject) {
            this.stepAndModule = { ...obj }
        },
        setExamPaper(obj: inObject) {
            this.examPaper = { ...obj }
        },
        setTopicList(arr: Array<inObject>) {
            if (arr?.length > 0) this.topicList = this.topicList.concat(arr)
            else this.topicList = arr ?? []
        },
        changeTopic(obj: inObject) {
            const ind = this.topicList.findIndex((el) => {
                return el.id == this.exchangeTopic.id
            })
            this.topicList.splice(ind, 1, obj)
        },
        setRandomTopic(obj: inObject | null) {
            this.randomTopic = obj
        },
        removeTopic(id: string) {
            const ind = this.topicList.findIndex((item) => {
                return item.id == id
            })
            this.topicList.splice(ind, 1)
        },
        // 生成富文本内容
        generateInnerContent() {
            const topicArr: any[] = []
            this.questionTypeList.forEach((el, index) => {
                const filterArr = this.topicList.filter((ele) => {
                    return ele.questionType == el.value
                })
                if (filterArr.length) topicArr.push(filterArr)
            })
            const title = this.examPaper.paperName
            const time = this.examPaper.testDuration == 0 ? '无限制' : this.examPaper.timeLimit + '分钟'
            let topic = ''
            // 单选0 多选1 判断2 填空3 简答4 论述5 材料写作6
            for (let i = 0; i < topicArr.length; i++) {
                const topicType: any = this.questionTypeList.find((el) => {
                    return el.value == topicArr[i][0].questionType
                })
                const totalScore = topicArr[i].reduce((pre, cur) => {
                    return (pre += cur.score)
                }, 0)
                topic += `<div><b><font size="4">
                ${SectionToChinese(i + 1)}、${topicType.label}题：${topicArr[i].length} 道 共 ${totalScore} 分</font></b></div>`
                for (let j = 0; j < topicArr[i].length; j++) {
                    // 单选&多选
                    if (topicArr[i][0].questionType == 0 || topicArr[i][0].questionType == 1) {
                        const options = JSON.parse(topicArr[i][j].questionCont)
                        let [temp, str] = ['', '<div><font size="3" style="display:flex;flex-wrap:wrap;">']
                        options.forEach((ele, index) => {
                            temp += `${ele.prefix}.${ele.content}`
                        })
                        options.forEach((ele, index) => {
                            if (temp.length < 50) {
                                // 放在一行上
                                if (index !== options.length - 1) {
                                    str += `<p style="width:${100 / options.length}%;">${ele.prefix}.${ele.content}</p>`
                                } else
                                    str += `<p style="width:${100 / options.length}%;">${ele.prefix}.
                                    ${ele.content}</p></font></p></div>`
                            } else if (temp.length < 100 && temp.length >= 50) {
                                // 两个一行
                                if (index !== options.length - 1) str += `<p style="width:50%;">${ele.prefix}.${ele.content}</p>`
                                else str += `<p style="width:50%;">${ele.prefix}.${ele.content}</p></font></p></div>`
                            } else {
                                // 一个一行
                                if (index !== options.length - 1) str += `<p style="width:100%;">${ele.prefix}.${ele.content}</p>`
                                else str += `<p style="width:100%;">${ele.prefix}.${ele.content}</p></font></p></div>`
                            }
                        })
                        topic += `<div><font size="3" > ${j + 1}、（${topicArr[i][j].score} 分）${topicArr[i][j].title}
                        (&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; )</font></div>${str}`
                    } else if (topicArr[i][0].questionType == 2) {
                        // 判断
                        topic += `<div><font size="3" > ${j + 1}、（${topicArr[i][j].score} 分）${topicArr[i][j].title}
                        (&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; )</font></div>`
                    } else if (topicArr[i][0].questionType == 3) {
                        const str = topicArr[i][j].title.replace(/#{2,}/g, ' ______________ ')
                        // 填空
                        topic += `<div><font size="3" > ${j + 1}、（${topicArr[i][j].score} 分）${str}
                        </font></div>`
                    } else if (topicArr[i][0].questionType == 4 || topicArr[i][0].questionType == 5 || topicArr[i][0].questionType == 7) {
                        // 简答&论述
                        topic += `<div><font size="3" > ${j + 1}、（${topicArr[i][j].score} 分）${topicArr[i][j].title}
                        </font></div><div style="height:200px;"></div>`
                    } else {
                        // 材料写作
                        topic += `<div><font size="3" > ${j + 1}、（${topicArr[i][j].score} 分）${topicArr[i][j].title}
                        </font></div><div style="height:500px;"></div>`
                    }
                }
            }
            this.innerContent = `<h1 id="cmdsl" style="text-align:center;"><b>${title}</b></h1>
            <div><br></div>
            <div><b><font size="3"><i>考试时间：${time}</i></font></b></div>
            <div><br></div>
            ${topic}`
        },
        // 设置富文本内容
        setInnerContent(str: string) {
            this.innerContent = str
        },
        // 设置富文本确定按钮要执行的操作 ADD EDIT COPY
        setHandleType(str: string) {
            this.handleType = str
        },
        setExchangeTopic(obj: inObject) {
            this.exchangeTopic = obj
        },
        removeExamTopic(obj: inObject) {
            const ind = this.topicList.findIndex((el) => {
                return el.id == obj.id
            })
            this.topicList.splice(ind, 1)
        },
        insertExamTopic(payload: inObject) {
            this.topicList = this.topicList.concat(payload)
        },
        typeListInit() {
            // 获取题目类型
            dictionaryDataStore()
                .setDictionaryData('questionType', '', 'get', true)
                .then((data: inObject[]) => {
                    this.questionTypeList = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 获取题目属性
            dictionaryDataStore()
                .setDictionaryData('prolist', '/api/hr-questions/prolist', 'get', true)
                .then((data: inObject[]) => {
                    this.questionProList = data.map((item) => {
                        return { label: item.questionPro, value: item.questionPro }
                    })
                })
            // 获取题目分值
            dictionaryDataStore()
                .setDictionaryData('score', '/api/hr-paper-managements-indexscore', 'post', true)
                .then((data: inObject[]) => {
                    this.scoreList = data.map((item) => {
                        return { label: item.score, value: item.score }
                    })
                })
        },
    },
})

const useExamPaperStore = () => {
    return examPaperStore(store)
}

export default useExamPaperStore
