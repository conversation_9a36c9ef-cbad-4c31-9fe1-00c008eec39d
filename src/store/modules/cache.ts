import { defineStore } from 'pinia'
import { menuRoutes } from '/@/router/routers'
import { AppRouteRecordRaw } from '/@/router/types'
import store from '/@/store'

interface cacheState {
    // 权限白名单
    whiteList: {
        menus: string[]
        buttons: string[]
    }
    accessRoutes: AppRouteRecordRaw[]
}

export const cacheStore = defineStore({
    id: 'cache',
    state: (): cacheState => ({
        whiteList: {
            menus: [],
            buttons: [],
        },
        accessRoutes: [],
    }),
    getters: {
        getAccessRoutes(): cacheState['accessRoutes'] {
            return this.accessRoutes
        },
        getWhiteList(): cacheState['whiteList'] {
            return this.whiteList
        },
    },
    actions: {
        getAccessMenuByAuth(list: AppRouteRecordRaw[], whiteList: string[], result: AppRouteRecordRaw[] = []) {
            list.forEach((item) => {
                if (whiteList.includes(item.name)) {
                    const cell = { ...item }
                    if (item.children && item.children.length) {
                        cell.children = this.getAccessMenuByAuth(item.children, whiteList)
                    }
                    result.push(cell)
                }
            })
            return result
        },
        setWhiteList(value: cacheState['whiteList']) {
            this.whiteList = value
            this.accessRoutes = this.getAccessMenuByAuth(menuRoutes, value.menus)
        },
    },
})

const useCacheStore = () => {
    return cacheStore(store)
}

export default useCacheStore
