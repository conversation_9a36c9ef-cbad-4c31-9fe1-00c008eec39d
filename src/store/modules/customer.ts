import { defineStore } from 'pinia'
import store from '/@/store'
import request from '/@/utils/request'
interface CustomerState {
    protocolViewType: string
    customerProtocol: inObject

    customerInfo: inObject
    socialSecurityTypeOptions: LabelValueOptions
    providentFundTypeOptions: LabelValueOptions

    socialSecurityAccountOptions: LabelValueOptions
    medicalInsuranceAccountOptions: LabelValueOptions
    providentFundAccountOptions: LabelValueOptions
    payrollAccountOptions: LabelValueOptions
}
export const customer = defineStore({
    id: 'customer',
    state: (): CustomerState => ({
        protocolViewType: '',
        customerProtocol: {},

        customerInfo: {},
        socialSecurityTypeOptions: [],
        providentFundTypeOptions: [],

        socialSecurityAccountOptions: [],
        medicalInsuranceAccountOptions: [],
        providentFundAccountOptions: [],
        payrollAccountOptions: [],
    }),
    getters: {
        getViewType(): CustomerState['protocolViewType'] {
            return this.protocolViewType
        },
        // 客户协议
        getCustomerProtocol(): CustomerState['customerProtocol'] {
            return this.customerProtocol
        },
        getCustomerInfo(): CustomerState['customerInfo'] {
            return this.customerInfo
        },
        //社保类型
        getSocialSecurityTypeOptions(): CustomerState['socialSecurityTypeOptions'] {
            return this.socialSecurityTypeOptions
        },
        getProvidentFundTypeOptions(): CustomerState['providentFundTypeOptions'] {
            return this.providentFundTypeOptions
        },

        getSocialSecurityAccountOptions(): CustomerState['socialSecurityAccountOptions'] {
            return this.socialSecurityAccountOptions
        },
        getMedicalInsuranceAccountOptions(): CustomerState['medicalInsuranceAccountOptions'] {
            return this.medicalInsuranceAccountOptions
        },
        getProvidentFundAccountOptions(): CustomerState['providentFundAccountOptions'] {
            return this.providentFundAccountOptions
        },
        getPayrollAccountOptions(): CustomerState['payrollAccountOptions'] {
            return this.payrollAccountOptions
        },
    },
    actions: {
        setViewType(viewType: string) {
            this.protocolViewType = viewType
        },
        fetchCustomerProtocol(info: any, hasFetch = false) {
            if (hasFetch) {
                request.get('/api/hr-clients/selectclientse', { id: info.id }).then((res) => {
                    if (res.length && res[0].protocolId) {
                        request
                            .get('/api/hr-protocols', { id: res[0].protocolId })
                            .then((data) => {
                                this.customerProtocol = data
                            })
                            .catch((err) => {
                                console.log(err)
                            })
                    } else {
                        this.customerProtocol = {}
                    }
                })
            } else {
                this.customerProtocol = info
            }
        },
        setCustomerInfo(info: any) {
            this.customerInfo = info
        },
        updateCustomerInfo(info: any) {
            this.customerInfo = { ...this.customerInfo, ...info }
        },
        setSocialSecurityTypeOptions() {
            //社保类型
            return new Promise(async (resolve, reject) => {
                request
                    .get('/api/hr-social-securitie/select')
                    .then((res) => {
                        this.socialSecurityTypeOptions = res.map((item: inObject) => {
                            return { label: item.socialSecurityName, value: item.id, ...item }
                        })
                        resolve(this.socialSecurityTypeOptions)
                    })
                    .catch((res) => {
                        reject(res)
                    })
            })
        },
        setProvidentFundTypeOptions() {
            //公积金类型
            return new Promise(async (resolve, reject) => {
                request
                    .get('/api/hr-social-accumulation/select')
                    .then((res) => {
                        this.providentFundTypeOptions = res.map((item: inObject) => {
                            return { label: item.typeName, value: item.id, ...item }
                        })
                        resolve(this.providentFundTypeOptions)
                    })
                    .catch((res) => {
                        reject(res)
                    })
            })
        },

        setPlatformAccountOptions() {
            //缴纳账号
            return new Promise(async (resolve, reject) => {
                request
                    .get('/api/hr-PlatformAccount/select')
                    .then((res) => {
                        let socialSecurityAccount: LabelValueOptions = [] //社保
                        let medicalInsuranceAccount: LabelValueOptions = [] //医保
                        let providentFundAccount: LabelValueOptions = [] //公积金
                        let payrollAccount: LabelValueOptions = [] //工资

                        res.forEach((item: inObject) => {
                            switch (item.platformType) {
                                case '1':
                                    if (item.accountNumber != null) {
                                        socialSecurityAccount.push({ label: item.accountNumber, value: item.id, ...item })
                                    }
                                    break
                                case '2':
                                    if (item.accountNumber != null) {
                                        medicalInsuranceAccount.push({ label: item.accountNumber, value: item.id, ...item })
                                    }
                                    break
                                case '3':
                                    if (item.accountNumber != null) {
                                        providentFundAccount.push({ label: item.accountNumber, value: item.id, ...item })
                                    }
                                    break
                                case '4':
                                    if (item.accountNumber != null) {
                                        payrollAccount.push({ label: item.accountNumber, value: item.id, ...item })
                                    }
                                    break
                            }
                        })
                        this.socialSecurityAccountOptions = socialSecurityAccount
                        this.medicalInsuranceAccountOptions = medicalInsuranceAccount
                        this.providentFundAccountOptions = providentFundAccount
                        this.payrollAccountOptions = payrollAccount
                        console.log(this.payrollAccountOptions)
                        resolve({
                            socialSecurityAccountOptions: socialSecurityAccount,
                            medicalInsuranceAccountOptions: medicalInsuranceAccount,
                            providentFundAccountOptions: providentFundAccount,
                            payrollAccountOptions: payrollAccount,
                        })
                    })
                    .catch((res) => {
                        reject(res)
                    })
            })
        },
    },
})

export default function useCustomer() {
    return customer(store)
}
