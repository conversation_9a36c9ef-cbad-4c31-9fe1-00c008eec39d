import { defineStore } from 'pinia'
import store from '/@/store'
import dictionaryDataStore from './dictionaryData'

interface recruitmentBriefState {
    examModeList: LabelValueOptions
    stationList: LabelValueOptions
    recruitmentTemplateList: inObject[]
    modalViewType: string
    exportList: string[]
    currentData: inObject | null
    noticeData: inObject | null
    isOuter: boolean
    draftObj: object
}

export const recruitmentBriefStore = defineStore({
    id: 'recruitmentBrief',
    state: (): recruitmentBriefState => ({
        examModeList: [],
        stationList: [],
        recruitmentTemplateList: [],
        modalViewType: '',
        exportList: [],
        currentData: null,
        noticeData: null,
        isOuter: true,
        draftObj: {
            visible: false,
            title: '',
            content: '',
        },
    }),
    getters: {
        getExamModeList(): recruitmentBriefState['examModeList'] {
            return this.examModeList
        },
        getStationList(): recruitmentBriefState['stationList'] {
            return this.stationList
        },
        getRecruitmentTemplateList(): recruitmentBriefState['recruitmentTemplateList'] {
            return this.recruitmentTemplateList
        },
        getModalViewType(): recruitmentBriefState['modalViewType'] {
            return this.modalViewType
        },
        getExportList(): recruitmentBriefState['exportList'] {
            return this.exportList
        },
        getCurrentData(): recruitmentBriefState['currentData'] {
            return this.currentData
        },
        getNoticeData(): recruitmentBriefState['noticeData'] {
            return this.noticeData
        },
        getIsOuter(): recruitmentBriefState['isOuter'] {
            return this.isOuter
        },
        getDraftVisible(): recruitmentBriefState['draftObj'] {
            return this.draftObj['visible'] || false
        },
        getDraftTitle(): recruitmentBriefState['draftObj'] {
            return this.draftObj['title'] || ''
        },
        getDraftContent(): recruitmentBriefState['draftObj'] {
            return this.draftObj['content'] || ''
        },
    },
    actions: {
        typeListInit() {
            // 获取考试形式类型
            dictionaryDataStore()
                .setDictionaryData('examFormat', '', 'get', true)
                .then((data: inObject[]) => {
                    this.examModeList = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 报名模板
            dictionaryDataStore()
                .setDictionaryData('', '/api/hr-recruitment-brochures/recruitment-template', 'get', true)
                .then((data: inObject[]) => {
                    this.recruitmentTemplateList = data
                })
            // 岗位
            dictionaryDataStore()
                .setDictionaryData('', '/api/hr-stations/list', 'get', true)
                .then((data: inObject[]) => {
                    this.stationList = data.map((item) => {
                        return { label: item.professionName, value: item.id }
                    })
                })
        },
        setModalViewType(str: string) {
            this.modalViewType = str
        },
        setExportList(payload) {
            this.exportList = payload
        },
        setCurrentData(payload) {
            this.currentData = payload
        },
        setNoticeData(payload) {
            this.noticeData = payload
        },
        setIsOuter(payload) {
            this.isOuter = payload
        },
        setDraftObj(payload) {
            this.draftObj = payload
        },
    },
})

const useRecruitmentBriefStore = () => {
    return recruitmentBriefStore(store)
}

export default useRecruitmentBriefStore
