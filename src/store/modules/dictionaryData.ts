import { defineStore } from 'pinia'
import store from '/@/store'
import request from '/@/utils/request'
import { ArrToTree, getMaxlevel } from '/@/utils/index'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
interface dictionaryDataState {
    dictionaryData: inObject
    dictionaryTreeData: TreeDataItem[]
    dictionaryTreeObj: inObject
    dictionaryTreeDataMaxlevel: Number
}

export const dictionaryData = defineStore({
    id: 'dictionaryData',
    state: (): dictionaryDataState => ({
        dictionaryData: {},
        dictionaryTreeData: [],
        dictionaryTreeObj: {},
        dictionaryTreeDataMaxlevel: 0,
    }),
    getters: {
        getDictionaryData(): dictionaryDataState['dictionaryData'] {
            return this.dictionaryData
        },
    },
    actions: {
        // clients客户
        // hr-stations岗位
        //唯一标识，非字典项的请求地址，请求方式，是否强制刷新
        setDictionaryData(value: string, url?, mode = 'get', refresh = false) {
            return new Promise(async (resolve, reject) => {
                if (!refresh && this.dictionaryData[value]) {
                    resolve(this.dictionaryData[value])
                    return
                }
                request[mode](url ? url : `/api/com-code-tables/getCodeTableByInnerName/${value}`, {})
                    .then((res) => {
                        const data = url
                            ? res
                            : res.map((item) => {
                                  return { label: item.itemName, value: item.itemValue, ...item }
                              })
                        this.dictionaryData[value] = data
                        resolve(data)
                    })
                    .catch((res) => {
                        reject(res)
                    })
            })
        },
        setSelectclients(
            clients = 'clients',
            refresh = false,
            customApi = '/api/hr-selectclients',
            method = 'get',
            searchKey: object | string = '',
        ) {
            return new Promise(async (resolve, reject) => {
                if (!refresh && this.dictionaryData[clients]) {
                    this.dictionaryTreeData = ArrToTree(this.dictionaryData[clients], { id: 'id', pid: 'parentId' }, (obj) => {
                        this.dictionaryTreeObj = obj
                    })
                    this.dictionaryTreeDataMaxlevel = getMaxlevel(this.dictionaryTreeData) as any
                    resolve(this.dictionaryData[clients])
                    return
                }
                let fn
                if (method == 'post') fn = request.post(customApi, { ...(searchKey as object) })
                else fn = request.get(customApi, searchKey ? { clientName: searchKey } : undefined)

                fn.then((res) => {
                    const data = res.map((item) => {
                        return { ...item, label: item.clientName, value: item.id, title: item.clientName, key: item.id }
                    })
                    this.dictionaryData[clients] = data.sort((a, b) => {
                        const regExp = new RegExp('[\\u4E00-\\u9FFF]', 'g')
                        const aIsCha = regExp.test(a.title)
                        const bIsCha = regExp.test(b.title)
                        if (aIsCha != bIsCha) {
                            return aIsCha ? 1 : -1
                        }
                        return a.title.localeCompare(b.title, 'zh')
                    })
                    this.dictionaryTreeData = ArrToTree(data, { id: 'id', pid: 'parentId' }, (obj) => {
                        this.dictionaryTreeObj = obj
                    })
                    this.dictionaryTreeDataMaxlevel = getMaxlevel(this.dictionaryTreeData) as any
                    resolve(data)
                }).catch((res) => {
                    reject(res)
                })
            })
        },
        emptyDictionaryData() {
            this.dictionaryData = {}
        },
    },
})

export default function dictionaryDataStore() {
    return dictionaryData(store)
}
