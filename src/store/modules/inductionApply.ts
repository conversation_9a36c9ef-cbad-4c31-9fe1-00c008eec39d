import { defineStore } from 'pinia'
import store from '/@/store'
interface inductionApplyState {
    employedStaffList: inObject[]
}
export const inductionApply = defineStore({
    id: 'inductionApply',
    state: (): inductionApplyState => ({
        employedStaffList: [],
    }),
    getters: {
        getEmployedStaffList(): inductionApplyState['employedStaffList'] {
            return this.employedStaffList
        },
    },
    actions: {
        setEmployedStaffList(info: any) {
            this.employedStaffList = info
        },
        setEmployedStaffListPush(info: inObject) {
            this.employedStaffList.push(info)
        },
        setEmployedStaffListReplace(info: inObject, index: number) {
            this.employedStaffList.splice(index, 1, info)
        },
        setEmployedStaffListDele(index: number, type) {
            if (type == 'edit') {
                this.employedStaffList[index].isDelete = true
            } else {
                this.employedStaffList.splice(index, 1)
            }
        },
    },
})

export default function inductionApplyStore() {
    return inductionApply(store)
}
