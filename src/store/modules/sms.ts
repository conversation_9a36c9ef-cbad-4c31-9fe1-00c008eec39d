import { defineStore } from 'pinia'
import store from '/@/store'
import moment from 'moment'

interface smsState {
    // 短信
    sms: inObject
    receiverOptions: inObject
    errorData: inObject
}

export const smsStore = defineStore({
    id: 'sms',
    state: (): smsState => ({
        sms: {},
        receiverOptions: [],
        errorData: [],
    }),
    getters: {
        getSms(): smsState['sms'] {
            return this.sms
        },
        getReceiverOptions(): smsState['receiverOptions'] {
            return this.receiverOptions
        },
        getErrorData(): smsState['errorData'] {
            return this.errorData
        },
        getSMSContent(): smsState['sms'] {
            return this.sms['smsContent']
        },
    },
    actions: {
        setSmsState(obj: inObject) {
            if (obj.parameterContent !== null) {
                const parameterArr = JSON.parse(obj.parameterContent)
                obj.parameterContent = parameterArr
            } else {
                obj.parameterContent = []
                const { length } = obj.sendContent.split('{')
                for (let i = 0; i < length - 1; i++) {
                    obj.parameterContent.push('')
                }
            }
            let lastStr = obj.sendContent
            if (obj.dateIndex) {
                const date = moment().add(obj.dateNumber, 'days').format('YYYY年MM月DD日')
                obj.smsContent = lastStr.replace(`{${obj.dateIndex}}`, date)
            } else {
                obj.parameterContent.forEach((item, ind) => {
                    lastStr = lastStr.replace(`{${ind + 1}}`, item)
                })
                obj.smsContent = lastStr
            }

            this.sms = { ...obj }
        },
        setSmsStateObj(obj: inObject) {
            this.sms = { ...obj }
        },
        setReceiverOptions(payload: any) {
            this.receiverOptions.push(...payload)
        },
        setErrorData(payload: any) {
            this.errorData = payload
        },
        removeSomeReceiverOptions(payload: any) {
            payload.forEach((el) => {
                const index = this.receiverOptions.findIndex((ele) => {
                    return ele.phone == el.phone
                })
                this.receiverOptions.splice(index, 1)
            })
        },
        resetReceiverOptions() {
            this.receiverOptions = []
        },
    },
})

const useSmsStore = () => {
    return smsStore(store)
}

export default useSmsStore
