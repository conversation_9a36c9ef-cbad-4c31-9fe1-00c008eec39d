import { defineStore } from 'pinia'
// import useCacheStore from './cache'
import store from '/@/store'
// import request from '/@/utils/request'
// import useUserStore from '/@/store/modules/user'

interface accountNumState {
    permission: inObject
}

export const permission = defineStore({
    id: 'permission',
    state: (): accountNumState => ({
        permission: {
            staffState: true,
            customerService: false, // 专管员
            roleKey: '',
        },
    }),
    getters: {
        getPermission(): accountNumState['permission'] {
            return this.permission
        },
    },
    actions: {
        setPermission(roles: any[]) {
            this.permission = {
                staffState: true,
                customerService: false, // 专管员
            }
            this.permission.roleKey = roles[0].roleKey
            if (roles[0].roleKey == 'client') {
                //客户
                this.permission.staffState = false
            } else if (roles[0].roleKey == 'customer_service_staff') {
                // 专管员
                this.permission.customerService = true
            }
        },
    },
})

export default function permissionStore() {
    return permission(store)
}
