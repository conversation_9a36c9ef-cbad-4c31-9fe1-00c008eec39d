import { defineStore } from 'pinia'
import store from '/@/store'

interface staffState {
    staff: inObject
}

export const staff = defineStore({
    id: 'staff',
    state: (): staffState => ({
        staff: {
            staffId: '',
            staffList: [],
        },
    }),
    getters: {
        getStaff(): staffState['staff'] {
            return this.staff
        },
    },
    actions: {
        setStaff(id: any) {
            this.staff.staffId = id
        },
        setStaffList(list: any) {
            this.staff.staffList = list
        },
    },
})

export default function staffStore() {
    return staff(store)
}
