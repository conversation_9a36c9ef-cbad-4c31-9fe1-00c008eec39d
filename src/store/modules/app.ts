import { defineStore } from 'pinia'
import store from '/@/store'
import type { RouteLocationNormalized } from 'vue-router'

interface AppState {
    // 菜单是否折叠
    collapsed: boolean
    // 当前路由
    currentRoute: RouteLocationNormalized | null
    // 主题模式
    themeMode: 'dark' | 'light'
    // 已读消息
    readCount: number
}

export const appStore = defineStore({
    id: 'app',
    state: (): AppState => ({
        collapsed: false,
        currentRoute: null,
        themeMode: 'light',
        readCount: 0,
    }),
    getters: {
        getThemeMode(): AppState['themeMode'] {
            return this.themeMode
        },
        getCollapsed(): boolean {
            return this.collapsed
        },
        getCurrentRoute(): any {
            return this.currentRoute
        },
    },
    actions: {
        setReadCount() {
            this.readCount += 1
        },
        changeThemeMode(mode?: 'light' | 'dark') {
            this.themeMode = mode || this.themeMode == 'dark' ? 'light' : 'dark'
            localStorage.themeMode = this.themeMode
        },
        setCollapsed(status: boolean) {
            this.collapsed = status
        },
        setCurrentRoute(route: RouteLocationNormalized) {
            this.currentRoute = route
        },
    },
})

export default function useAppStore() {
    return appStore(store)
}
