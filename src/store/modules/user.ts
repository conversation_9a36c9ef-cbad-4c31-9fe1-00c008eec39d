import { defineStore } from 'pinia'
import useCacheStore from './cache'
import permissionStore from './permission'
import dictionaryDataStore from './dictionaryData'
import store from '/@/store'
import request from '/@/utils/request'
import router from '/@/router'
import config from '/@/config'
interface UserState {
    userInfo: any
}

export const userStore = defineStore({
    id: 'user',
    state: (): UserState => ({
        userInfo: null,
    }),
    getters: {
        getUserInfo(): UserState['userInfo'] {
            return this.userInfo
        },
    },
    actions: {
        setUserInfo(info: any) {
            this.userInfo = info
        },
        loginAction({ account, password, loginType, phone, code }) {
            return new Promise(async (resolve, reject) => {
                try {
                    localStorage.removeItem('token')
                    const res = await request.post(
                        `/api/authenticate`,
                        { loginName: account, password, loginType, phone, code },
                        { loading: false },
                    )
                    localStorage.token = res?.token || ''
                    resolve(res)
                } catch (error) {
                    reject(new Error('loginAction error'))
                }
            })
        },
        getUserInfoAction() {
            dictionaryDataStore().emptyDictionaryData()
            return new Promise(async (resolve, reject) => {
                const cacheStore = useCacheStore()
                const permission = permissionStore()
                try {
                    const res = await request.get(`/api/user-info`, {}, { loading: false })

                    this.setUserInfo(res.userInfo)

                    const whiteList = {
                        buttons: res.auth.buttons,
                        menus: res.auth.menus,
                    }

                    cacheStore.setWhiteList(whiteList)
                    permission.setPermission(res.userInfo.roles)
                    resolve(1)
                } catch (error) {
                    this.logoutAction()
                    router.push(config.loginPath)
                    window.location.reload()

                    reject(new Error('getUserInfoAction error:' + error))
                }
            })
        },
        logoutAction() {
            // 清空用户数据
            this.userInfo = null
            localStorage.removeItem('token')
            // 清空权限数据
            useCacheStore().setWhiteList({
                menus: [],
                buttons: [],
            })
        },
    },
})

export default function useUserStore() {
    return userStore(store)
}
