import { defineStore } from 'pinia'
import store from '/@/store'
interface GlobalLodingState {
    show: boolean
}
export const globalLoding = defineStore({
    id: 'globalLoding',
    state: (): GlobalLodingState => ({
        show: false,
    }),
    getters: {
        getGlobalLodingShow(): GlobalLodingState['show'] {
            return this.show
        },
    },
    actions: {
        setGlobalLodingShow(show: boolean) {
            this.show = show
        },
    },
})

export default function globalLodingStore() {
    return globalLoding(store)
}
