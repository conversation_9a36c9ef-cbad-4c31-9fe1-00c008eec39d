import { defineStore } from 'pinia'
import useCacheStore from './cache'
import store from '/@/store'
import request from '/@/utils/request'

interface signState {
    sign: inObject
}

export const sign = defineStore({
    id: 'sign',
    state: (): signState => ({
        sign: {
            tabs: '1', // 第一页的tab切换
            clientId: '', // 客户id
            staffContactList: [], // 第一页合同模板ids
            templateData: {}, // 第三页列表数据

            mustAttachmentList: [], // 第二页数据   必填
            optionalAttachmentList: [], // 第二页数据   可选
            contractStartDate: '', // 合同开始日期
            contractEndDate: '', // 合同结束日期
            formFieldData: {}, // 一键生成后的数据
        },
    }),
    getters: {
        getSign(): signState['sign'] {
            return this.sign
        },
    },
    actions: {
        setTabs(value: any) {
            this.sign.tabs = value
        },
        setClientId(value: any) {
            this.sign.clientId = value
        },
        setTemplateData(value: any) {
            this.sign.templateData = value
        },
        setTemplateDataByKey(value: any, key: any) {
            this.sign.templateData[key] = value
        },
        setContractTemplate(value: any) {
            this.sign.staffContactList = value
        },
        setMustData(value: any) {
            this.sign.mustAttachmentList = value
        },
        setMoreData(value: any) {
            this.sign.optionalAttachmentList = value
        },
        setStartDate(value: any) {
            this.sign.contractStartDate = value
        },
        setEndDate(value: any) {
            this.sign.contractEndDate = value
        },
        setFormFieldData(value: any) {
            this.sign.formFieldData = value
        },
    },
})

export default function signStore() {
    return sign(store)
}
