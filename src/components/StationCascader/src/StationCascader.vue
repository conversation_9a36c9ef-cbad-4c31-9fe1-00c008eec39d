<template>
    <div style="width: 100%; display: flex">
        <Cascader
            ref="refCascader"
            class="myCascader"
            v-model:value="dataValue"
            :options="treeData"
            :allow-clear="allowClear"
            :expand-trigger="expandTrigger"
            :size="size"
            showSearch
            :getPopupContainer="getPopupContainer"
            :displayRender="displayRender"
            :placeholder="
                placeholder ??
                (itemForm.placeholder ||
                    ((disabled ? disabled : itemForm?.disabled) ? `暂无${itemForm.label}` : `请选择${itemForm?.label}`))
            "
            :disabled="disabled ? disabled : itemForm?.disabled"
            @change="changeEvent"
        />
    </div>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, computed, onBeforeMount } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
interface CascaderDataItem {
    label?: string
    value?: string | number
    parentID?: string | number
    children?: CascaderDataItem[]
}
export default defineComponent({
    name: 'StationCascader',
    props: {
        size: {
            type: String,
            default: 'default',
            validator: (value: string) => {
                return ['small', 'large', 'default'].includes(value)
            },
        },
        expandTrigger: {
            type: String,
            default: 'hover',
        },
        allowClear: {
            type: Boolean,
            default: false,
        },
        value: {
            type: String,
            default: '',
        },
        itemForm: {
            type: Object,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        placeholder: String,
    },
    emits: ['update:value', 'update:itemForm', 'change', 'labelChange'],
    setup(props, { emit }) {
        const { value, itemForm } = toRefs<any>(props)

        const treeData = ref<CascaderDataItem[]>([])
        let options = ref([])
        onBeforeMount(() => {
            dictionaryDataStore()
                .setDictionaryData('hr-stations', '/api/hr-stations/list')
                .then((res: any) => {
                    options.value = res.map((item) => {
                        return { label: item.professionName, value: item.id, ...item }
                    })
                    if (itemForm.value?.options) itemForm.value.options = options.value

                    emit('update:itemForm', itemForm.value)
                    let industryTypeObj = {}
                    let professionTypeObj = {}
                    res.forEach((element) => {
                        let industryTypeId = element.industryType + 'industryType'
                        industryTypeObj[industryTypeId] = {
                            label: element.industryTypeName,
                            value: industryTypeId,
                            children: [],
                        }

                        let professionTypeId = element.industryType + '' + element.professionType + 'professionType'
                        if (!professionTypeObj[professionTypeId]) {
                            professionTypeObj[professionTypeId] = {
                                label: element.professionTypeName,
                                value: professionTypeId,
                                children: [],
                                parentID: industryTypeId,
                            }
                        }
                        let professionId = element.id
                        professionTypeObj[professionTypeId].children.push({
                            label: element.professionName,
                            value: professionId,
                        })
                    })

                    for (let key in professionTypeObj) {
                        industryTypeObj[professionTypeObj[key].parentID].children.push(professionTypeObj[key])
                    }
                    treeData.value = Object.values(industryTypeObj)
                })
        })
        const getIDArr = (id) => {
            let temp: any = options.value?.find((el: inObject) => {
                return el.id == id
            })
            if (!temp) return []
            else
                return [
                    temp?.industryType + 'industryType',
                    temp?.industryType + '' + temp?.professionType + 'professionType',
                    id,
                ]
        }
        const dataValue = computed<any>({
            get: () => {
                let data: any = value.value

                return getIDArr(data)
            },
            set: (val) => {
                let data: any = val
                emit('update:value', data[data.length - 1])
                emit('change', data[data.length - 1])
                if (itemForm.value?.onChange) {
                    itemForm.value?.onChange(
                        data[data.length - 1],
                        options.value?.find((el: inObject) => {
                            return el.id == data[data.length - 1]
                        }),
                    )
                }
            },
        })

        const changeEvent = (value, label) => {
            emit('labelChange', label)
        }
        const displayRender = ({ labels }: { labels: string[] }) => {
            return labels[labels.length - 1]
        }
        return {
            displayRender,
            treeData,
            changeEvent,
            dataValue,
            getPopupContainer: (triggerNode) => {
                return triggerNode
            },
        }
    },
})
</script>
<style scoped lang="less">
:deep(.myCascader) {
    width: 100% !important;
}
</style>
