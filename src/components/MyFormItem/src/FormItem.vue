<template>
    <FormItem :style="'width:' + width" v-bind="$attrs" :label="item.label" :name="item.name" v-if="item.show !== false">
        <!-- 默认为string input -->
        <Input
            v-if="!item.type || item.type == 'string'"
            v-model:value="dataValue"
            :placeholder="placeholder"
            @blur="item.onBlur"
            @click="item.onClick"
            @change="item.onChange"
            :disabled="disabled ? disabled : item.disabled"
        />
        <template v-else>
            <Select
                v-if="item.type == 'select'"
                v-model:value="dataValue"
                showSearch
                :mode="item.multiple ? 'multiple' : undefined"
                :options="item.options"
                :maxTagCount="2"
                @change="item.onChange"
                :disabled="disabled ? disabled : item.disabled"
                optionFilterProp="label"
                :allowClear="item.allowClear === false ? false : true"
                :placeholder="placeholder"
                :getPopupContainer="getPopupContainer"
            />
            <Select
                v-else-if="item.type == 'change'"
                v-model:value="dataValue"
                showSearch
                :options="item.options"
                @change="item.onChange"
                :disabled="disabled ? disabled : item.disabled"
                optionFilterProp="label"
                :allowClear="item.allowClear === false ? false : true"
                :placeholder="placeholder"
                :getPopupContainer="getPopupContainer"
            />
            <InputNumber
                v-else-if="item.type == 'number'"
                v-model:value="dataValue"
                :placeholder="placeholder"
                :disabled="disabled ? disabled : item.disabled"
                :max="item.max"
                :min="item.min"
                :formatter="item.integerOnly ? (value) => limitNumber(value) : item?.formatter"
                :parser="item.integerOnly ? (value) => limitNumber(value) : item?.parser"
                @change="item.onChange"
            />
            <DatePicker
                v-else-if="item.type == 'datetime'"
                v-model:value="dataValue"
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="placeholder"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                :showTime="item.type == 'datetime'"
                :disabled="disabled ? disabled : item.disabled"
                :disabled-date="item.disabledDate"
                :getCalendarContainer="getPopupContainer"
                @change="item.onChange"
            />
            <DatePicker
                v-else-if="item.type == 'date'"
                v-model:value="dataValue"
                format="YYYY-MM-DD"
                :placeholder="placeholder"
                valueFormat="YYYY-MM-DD"
                :disabled="disabled ? disabled : item.disabled"
                :getCalendarContainer="getPopupContainer"
                :disabled-date="item.disabledDate"
                @change="item.onChange"
            />
            <MonthPicker
                v-else-if="item.type == 'month'"
                class="input"
                v-bind="item.attrs"
                :allowClear="item.allowClear === false ? false : true"
                v-model:value="dataValue"
                :placeholder="placeholder"
                valueFormat="YYYY-MM"
                :getCalendarContainer="getPopupContainer"
                :disabled="disabled ? disabled : item.disabled"
            />
            <RangePicker
                v-else-if="item.type == 'rangePicker'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                v-model:value="dataValue"
                valueFormat="YYYY-MM-DD"
                :getCalendarContainer="getPopupContainer"
                :disabled="disabled ? disabled : item.disabled"
                :disabled-date="item.disabledDate"
                @change="item.onChange"
            />
            <RangePicker
                v-else-if="item.type == 'monthrange'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="dataValue"
                :getCalendarContainer="getPopupContainer"
                :placeholder="[item.label, item.label]"
                valueFormat="YYYY-MM"
                :mode="['month', 'month']"
                format="YYYY-MM"
                :size="item.size"
                @change="item.onChange"
                @panelChange="item.onChange"
            />
            <!-- <MonthPicker
                v-else-if="item.type == 'month'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                v-model:value="dataValue"
                :placeholder="item.placeholder || (item.disabled ? `暂无${item.label}` : `请选择${item.label}`)"
                valueFormat="YYYY-MM"
                :getPopupContainer="getPopupContainer"
            /> -->
            <Switch
                v-else-if="item.type == 'switch'"
                v-model:checked="dataValue"
                :checked-children="item.checkText ?? '启用'"
                :un-checked-children="item.unCheckText ?? '禁用'"
                :checkedValue="item.reverse ? false : true"
                :unCheckedValue="item.reverse ? true : false"
                :disabled="disabled ? disabled : item.disabled"
                :getPopupContainer="getPopupContainer"
            />
            <Textarea
                v-else-if="item.type == 'textarea'"
                v-model:value="dataValue"
                :rows="3"
                allowClear
                :placeholder="placeholder"
                valueFormat="YYYY-MM-DD 00:00:00"
                :disabled="disabled ? disabled : item.disabled"
                @click="item.onClick"
            />
            <SelectDic
                v-else-if="item.type == 'SelectDic'"
                v-model:value="dataValue"
                :disType="item.disType"
                :disParentId="item.disParentId"
                :title="item.title"
                :disabled="disabled ? disabled : item.disabled"
                optionFilterProp="label"
                showSearch
                :placeholder="placeholder"
                :getPopupContainer="getPopupContainer"
            />
            <Switch
                v-else-if="item.type == 'isTrue'"
                v-model:checked="dataValue"
                checked-children="是"
                un-checked-children="否"
                :checkedValue="item.reverse ? false : true"
                :unCheckedValue="item.reverse ? true : false"
                :disabled="disabled ? disabled : item.disabled"
                @change="item.onChange"
            />
            <template v-for="slotsItem in Object.keys($slots)" :key="slotsItem">
                <slot v-if="item.slots == slotsItem" :name="slotsItem"></slot>
            </template>
        </template>
        <span class="unit">{{ item.unit }}</span>
    </FormItem>
    <p class="linefeed" v-if="item.showbr"></p>
</template>
<script lang="ts">
import { defineComponent, toRefs, computed, watch, unref } from 'vue'
// import type { valuesAndRules } from '/#/component'
import { MonthPicker } from 'ant-design-vue'
export default defineComponent({
    name: 'MyFormItem',
    components: { MonthPicker },
    props: {
        width: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            required: true,
        },
        value: {
            type: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:value'],
    setup(props, { emit }) {
        const { value, item, disabled } = toRefs<any>(props)
        const dataValue = computed({
            get: () => {
                if (item.value?.type == 'select' || item.value?.type == 'change') {
                    let myOptions = unref(item.value?.options || [])
                    if (
                        !myOptions.find((el) => {
                            return value.value == el.value
                        })
                    ) {
                        return null
                    }
                }
                return value.value
            },
            set: (val) => {
                let data = val
                if (val == undefined) {
                    data = null
                }
                emit('update:value', data)
            },
        })
        const placeholder = computed(() => {
            let placeholderPrefix = '请输入'
            if (['select', 'change', 'datetime', 'date', 'month', 'SelectDic'].includes(item.value?.type)) {
                placeholderPrefix = '请选择'
            }
            if (value.value) {
                if (dataValue.value != value.value) {
                    return item.value.disabled || disabled.value
                        ? `该${item.value?.label}已被删除`
                        : `该${item.value?.label}已被删除,请重新选择`
                }
            }

            return (
                item.value.placeholder ||
                (item.value.disabled || disabled.value ? `暂无${item.value.label}` : `${placeholderPrefix}${item.value.label}`)
            )
        })
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }

        return {
            placeholder,

            dataValue,
            limitNumber,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.linefeed {
    width: 100%;
    padding: 0;
    margin: 0;
}
.unit {
    line-height: 32px;
}
:deep(.ant-form-item-control-input-content) {
    display: flex;
}
:deep(.ant-form-item-control) {
    // width: 100%;
    flex-grow: 1;
    flex-shrink: 1;
    width: inherit;
}
:deep(.ant-input-number) {
    width: 100%;
}
:deep(.ant-calendar-picker) {
    width: 100%;
}
:deep(.ant-row) {
    flex-flow: row;
}
</style>
