<template>
    <div class="box">
        <div
            style="position: fixed; top: 0; bottom: 0; left: 0; right: 0; background: rgba(0, 0, 0, 0); z-index: 999"
            v-if="showSeason"
            @click.stop="showSeason = false"
        ></div>
        <div>
            <Input :placeholder="placeholder" v-model:value="dataValue" style="width: 190px" @click="click($event)" />
            <Card
                class="box-card"
                style="min-width: 230px; padding: 0 3px 20px; position: absolute; z-index: 9999"
                v-if="showSeason"
            >
                <div class="ant-calendar-month-panel-header">
                    <a role="button" @click="prev" class="ant-calendar-month-panel-prev-year-btn"></a>
                    <a role="button" title="选择年份" class="ant-calendar-month-panel-year-select">
                        <span class="ant-calendar-month-panel-year-select-content">{{ year }}年</span>
                    </a>
                    <a role="button" @click="next" class="ant-calendar-month-panel-next-year-btn"></a>
                </div>
                <div class="item-box">
                    <Button
                        v-for="(it, index) in itemForm.options"
                        type="text"
                        :class="{ item: true, select: idx == index }"
                        @click="selectSeason(it, index)"
                        :key="index"
                    >
                        {{ it }}
                    </Button>
                </div>
            </Card>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, defineComponent, ref, toRefs, toRef, watch, onMounted, defineProps } from 'vue'
import { Card, message, notification } from 'ant-design-vue'

const props = defineProps({
    // options: {
    //     type: Array,
    // },
    value: {
        type: String,
        default: '',
    },
    itemForm: {
        type: Object,
        default: () => {},
    },
    placeholder: {
        type: String,
        default: '请选择季度',
    },
    // show:Boolean,
})
const emit = defineEmits(['update:value', 'change', 'update:itemForm', 'pencelChange'])
const { value, placeholder, itemForm } = toRefs(props)

const showSeason = ref(false)
const idx = ref(-1)
const year = ref(new Date().getFullYear())
const showValue = ref('')
const zhi = ref('')

const dataValue = computed({
    get: (val) => {
        // if (value.value.includes('-')) {
        //     let arr = value.value.split('-')
        //     let mon = Number(arr[1])
        //     let str = temp(mon)
        //     year.value = Number(arr[0])
        //     if (itemForm.value.options.length == 4) {
        //         return year.value + '年' + str
        //     } else if (itemForm.value.options.length == 2) {
        //         if (1 <= mon && mon <= 6) {
        //             return year.value + '年' + itemForm.value.options[0]
        //         } else if (7 <= mon && mon <= 12) {
        //             return year.value + '年' + itemForm.value.options[1]
        //         }
        //     }
        // } else {
        return value.value
        // }
    },
    set: (val) => {
        emit('update:value', val)
        emit('change', val)
    },
})
onMounted(() => {
    emit('update:itemForm', itemForm.value)
})
const temp = (mon) => {
    if (1 <= mon && mon <= 3) {
        idx.value = 0
        return '第一季度'
    } else if (4 <= mon && mon <= 6) {
        idx.value = 1
        return '第二季度'
    } else if (7 <= mon && mon <= 9) {
        idx.value = 2
        return '第三季度'
    } else if (10 <= mon && mon <= 12) {
        idx.value = 3
        return '第四季度'
    }
}

const click = (e) => {
    e.target.blur()
    showSeason.value = !showSeason.value
}
const one = () => {
    showSeason.value = false
}
const prev = () => {
    year.value = year.value * 1 - 1
}
const next = () => {
    year.value = year.value * 1 + 1
}
const selectSeason = (item, i) => {
    idx.value = i
    zhi.value = item
    showValue.value = `${year.value}年${zhi.value}`
    // console.log('点击@@@#@#@=>', showValue.value)
    showSeason.value = false
    dataValue.value = showValue.value
    emit('pencelChange')
}
</script>
<style lang="less" scoped>
.box {
    position: relative;
    min-width: 200px;
}
.box-card {
    background-color: #fff;
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}
.item-box {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}
.item {
    width: 40%;
    color: #606266;
    margin-top: 15px;
}
.select {
    background-color: #f0f7ff;
}
:deep(.ant-card-body) {
    padding: 0;
}
</style>
