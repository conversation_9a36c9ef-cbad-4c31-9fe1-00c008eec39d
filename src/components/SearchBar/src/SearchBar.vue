<template>
    <div class="search-bar">
        <template v-for="(item, index) in myOptions" :key="item.key">
            <Input
                v-if="!item.type || item.type == 'string'"
                class="input"
                :value="modelValue[item.key]"
                :allowClear="item.allowClear === false ? false : true"
                @change="inputHandle($event, item)"
                @pressEnter="inputChange($event, item)"
                @blur="inputChange($event, item)"
                :placeholder="item.label"
                v-show="item.show === false ? false : true"
                :size="item.size"
                @paste="inputPaste($event, item)"
            />
            <InputNumber
                v-else-if="item.type == 'number'"
                class="input"
                :value="modelValue[item.key]"
                :placeholder="item.label"
                :max="item.max"
                :min="item.min"
                v-show="item.show === false ? false : true"
                :size="item.size"
                @change="inputHandle($event, item)"
                @pressEnter="inputChange($event, item)"
                @blur="inputChange($event, item)"
                :formatter="item.integerOnly ? (value) => limitNumber(value) : undefined"
                :parser="item.integerOnly ? (value) => limitNumber(value) : undefined"
            />
            <NumberRange
                v-else-if="item.type == 'numberrange'"
                :item="item"
                :value="modelValue[item.key]"
                @change="inputChange"
            />
            <Select
                v-else-if="item.type == 'select'"
                v-bind="isMultiple(item)"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                showSearch
                optionFilterProp="label"
                :maxTagCount="0"
                :value="modelValue[item.key]"
                :options="item.options"
                @change="selectChange($event, item)"
                :placeholder="item.label"
                v-show="item.show === false ? false : true"
                :size="item.size"
                :getPopupContainer="item.renderBody === true ? getPopupContainer : (triggerNode) => triggerNode.parentNode"
            />
            <DatePicker
                v-else-if="item.type == 'date' || item.type == 'datetime'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @change="dateChange($event, $event, item)"
                :placeholder="item.label"
                :showTime="item.type == 'datetime'"
                :valueFormat="item.type == 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                v-show="item.show === false ? false : true"
                :size="item.size"
            />
            <DatePicker
                v-else-if="item.type == 'year'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @panelChange="yearChange($event, $event, item)"
                @openChange="openChange($event)"
                :placeholder="item.label"
                mode="year"
                :valueFormat="'YYYY'"
                :format="'YYYY'"
                v-show="item.show === false ? false : true"
                :size="item.size"
            />
            <RangePicker
                v-else-if="item.type == 'daterange' || item.type == 'datetimerange'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @ok="dateChange($event, $event, item)"
                @change="dateChange($event, $event, item)"
                :placeholder="[item.label, item.label]"
                :showTime="item.type == 'datetimerange'"
                :valueFormat="item.type == 'daterange' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                :style="{ width: item.type == 'daterange' ? '300px' : '320px' }"
                :size="item.size"
                v-show="item.show === false ? false : true"
            />
            <RangePicker
                v-else-if="item.type == 'monthrange'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @panelChange="panelChange($event, $event, item, 'month')"
                @change="dateChange($event, $event, item)"
                @openChange="openChange($event)"
                :placeholder="item.placeholder ? item.placeholder : [item.label, item.label]"
                :valueFormat="item.valueFormat ? item.valueFormat : 'YYYY-MM'"
                :mode="['month', 'month']"
                :format="item.valueFormat ? item.valueFormat : 'YYYY-MM'"
                :size="item.size"
                v-show="item.show === false ? false : true"
            />
            <RangePicker
                v-else-if="item.type == 'yearrange'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @panelChange="(value, mode) => panelChange(value, mode, item, 'year')"
                @change="dateChange($event, $event, item)"
                @openChange="openChange($event)"
                :placeholder="[item.label, item.label]"
                :valueFormat="'YYYY'"
                :mode="['year', 'year']"
                format="YYYY"
                :size="item.size"
                v-show="item.show === false ? false : true"
            />
            <MonthPicker
                v-else-if="item.type == 'month'"
                class="input"
                :allowClear="item.allowClear === false ? false : true"
                :value="modelValue[item.key]"
                @ok="dateChange($event, $event, item)"
                @change="dateChange($event, $event, item)"
                :placeholder="item.label"
                valueFormat="YYYY-MM"
                :size="item.size"
                v-show="item.show === false ? false : true"
            />
            <ClientSelectTree
                v-else-if="item.type == 'clientSelectTree'"
                style="margin-right: 10px"
                :style="{ width: item?.width || '190px' }"
                v-bind="item"
                v-model:itemForm="myOptions[index]"
                :value="modelValue[item.key]"
                @change="selectChange($event, item)"
                :multiple="!!item.multiple"
                :checkStrictly="item.checkStrictly !== false"
                v-show="item.show === false ? false : true"
            />
            <QuarterPicker
                v-else-if="item.type == 'quterSelect'"
                v-model:value="modelValue[item.key]"
                v-model:itemForm="myOptions[index]"
                :placeholder="item.placeholder"
                @change="datChange($event, item)"
                v-show="item.show === false ? false : true"
            />

            <template v-for="itemSlot in Object.keys($slots)" :key="itemSlot">
                <slot :name="itemSlot" v-bind="{ item, index }" v-if="itemSlot == item.key"></slot>
            </template>
        </template>
        <slot name="textCont"></slot>
    </div>
    <div class="select-bar" v-if="showSelectedLine">
        <div class="select-item" v-for="i in selectItems" :key="i.label">
            <template v-if="i.showSelect">
                <div class="label">{{ i.label }}：</div>
                <div class="value">
                    <div class="tag" v-for="v in i.values" :key="v.str">
                        {{ v.str }}
                        <span class="close" @click="closeTag(i, v)" v-if="i.allowClear">
                            <CloseOutlined />
                        </span>
                    </div>
                </div>
            </template>
        </div>
        <span
            v-if="
                selectItems &&
                selectItems?.some((itrm) => {
                    return itrm.allowClear
                })
            "
            class="btn"
            @click="closeAllTag"
            >清除筛选条件</span
        >
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, ref, toRefs, nextTick, isRef, toRef, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import { CloseOutlined } from '@ant-design/icons-vue'
import { RangePicker, MonthPicker, DatePicker } from 'ant-design-vue'
import QuarterPicker from './quarterPicker.vue'
export default defineComponent({
    name: 'SearchBar',
    components: { CloseOutlined, RangePicker, MonthPicker, QuarterPicker },
    props: {
        showSelectedLine: {
            type: Boolean,
            default: true,
        },
        modelValue: {
            type: Object as PropType<{}>,
            default: () => {},
        },
        options: {
            type: Array as PropType<SearchBarOption[]>,
            default: () => [],
        },
    },
    emits: ['update:modelValue', 'change'],
    setup(props, { emit }) {
        const { modelValue } = toRefs(props)
        const myOptions = ref(props.options)
        const inputPaste = (e, item) => {
            setTimeout(() => {
                console.log(e.target.value, item)
                inputChange(e, item)
            })
        }
        const datChange = (val, item) => {
            changeHandle({
                ...item,
                value: val ?? undefined,
            })
        }
        const inputHandle = (e, item) => {
            const newVal = { ...modelValue.value }
            newVal[item.key] = item.type == 'number' ? e : e.target.value
            emit('update:modelValue', newVal)
        }

        const inputChange = (e, item) => {
            // e && e.target.blur()
            changeHandle({
                ...item,
                value: item.type == 'numberrange' ? e ?? undefined : e.target.value ?? undefined,
            })
        }
        const selectChange = (val, item) => {
            changeHandle({
                ...item,
                value: val ?? undefined,
            })
        }
        const yearChange = (date, dateString, item) => {
            // console.log('fggfgfgyear===>?', date, dateString, date.format('YYYY'))
            let newDate = date.format('YYYY')

            changeHandle(
                {
                    ...item,
                    value: newDate ?? undefined,
                },
                false,
            )
        }
        const dateChange = (date, dateString, item) => {
            // console.log('fggfgfgyear===>?', date, dateString, item)
            changeHandle({
                ...item,
                value: dateString ?? undefined,
            })
        }
        const panelChange = (date, mode, item, type) => {
            // console.log("date, mode, item, type=>",date, mode, item, type)
            let newDate =
                type == 'month'
                    ? [date[0].format('YYYY-MM'), date[1].format('YYYY-MM')]
                    : [date[0].format('YYYY'), date[1].format('YYYY')]
            changeHandle(
                {
                    ...item,
                    value: newDate ?? undefined,
                },
                false,
            )
        }
        const openChange = (date) => {

            if (date == false) {
                const newValue = { ...modelValue.value }
                emit('change', newValue)
            }
        }
        const isMultiple = (item) => (item.multiple ? { mode: 'multiple' } : {})

        const changeHandle = async (
            item: { label: string; key: string; value: any; [key: string]: any },
            needEmitChange = true,
        ) => {
            const newValue = { ...modelValue.value }

            newValue[item.key] = item.value

            // 有根据这个字段变化的选择框 根据该字段获取数据
            let indexList: number[] = []
            myOptions.value.forEach((i, idx) => {
                i.changedBy && i.changedBy === item.key && indexList.push(idx)
            })

            if (!indexList.length) {
                // 放到所有modelValue处理完再触发change
                // console.log('search change', item, newValue)
                emit('update:modelValue', newValue)
                nextTick(() => {
                    if (item.type == 'numberrange') {
                        item.value?.length == 2 && needEmitChange && emit('change', newValue)
                    } else needEmitChange && emit('change', newValue)
                })
                return
            }

            for (const i in indexList) {
                const idx = indexList[i]
                // 初始化该字段 值
                const key = props.options[idx].key
                newValue[key] = Array.isArray(props.modelValue[key]) ? [] : undefined
                // 根据该字段改变值 获取数据
                const requestMethod = props.options[idx].changedMethod as Function
                // 暂时只支持单选获取数据
                myOptions.value[idx].options = item.value || item.value === 0 ? await requestMethod(newValue) : []
            }

            // 放到所有modelValue处理完再触发change
            emit('update:modelValue', newValue)

            nextTick(() => {
                needEmitChange && emit('change', newValue)
            })
        }

        const selectItems = computed(() =>
            Object.keys(modelValue.value)
                .filter((i) =>
                    Array.isArray(modelValue.value[i])
                        ? !!modelValue.value[i].length
                        : !!modelValue.value[i] || modelValue.value[i] === 0,
                )
                .map((i) => {
                    // console.log('@@@=>', i, modelValue.value[i])
                    const option = myOptions.value.find((j) => j.key == i)
                    if (option?.type == 'select' || option?.type == 'selectSlot' || option?.type == 'clientSelectTree') {
                        let values: Array<{ key: any; str: any }> = []
                        if (Array.isArray(modelValue.value[i])) {
                            values = modelValue.value[i].map((v) => ({
                                key: v,
                                str: option.options?.find((f) => f.value == v)?.label,
                            }))
                        } else {
                            values = [
                                {
                                    key: null,
                                    str: option.options?.find((f) => f.value == modelValue.value[i])?.label,
                                },
                            ]
                        }
                        return {
                            label: option.label,
                            key: i,
                            allowClear: option?.allowClear == false ? false : true,
                            showSelect: option?.showSelect == false ? false : true,
                            values,
                        }
                    }
                    // 单个年度专用
                    if (option?.type == 'year') {
                        return {
                            label: option.label,
                            key: i,
                            allowClear: option?.allowClear == false ? false : true,
                            showSelect: option?.showSelect == false ? false : true,
                            values: [
                                {
                                    key: null,
                                    str: modelValue.value[i] + '年',
                                },
                            ],
                        }
                    }
                    return {
                        label: option?.label,
                        type: option?.type,
                        key: i,
                        allowClear: option?.allowClear == false ? false : true,
                        showSelect: option?.showSelect == false ? false : true,
                        values: [
                            {
                                key: null,
                                str: option?.type?.includes('range')
                                    ? `${modelValue.value[i][0]} ~ ${modelValue.value[i][1]}`
                                    : modelValue.value[i],
                            },
                        ],
                    }
                }),
        )

        const temp = (mon) => {
            if (1 <= mon && mon <= 3) {
                return '第一季度'
            } else if (4 <= mon && mon <= 6) {
                return '第二季度'
            } else if (7 <= mon && mon <= 9) {
                return '第三季度'
            } else if (10 <= mon && mon <= 12) {
                return '第四季度'
            }
        }
        const closeTag = (item, cell) => {
            const newVal = { ...modelValue.value }

            cell.key || cell.key == 0
                ? newVal[item.key].splice(newVal[item.key].indexOf(cell.key), 1)
                : (newVal[item.key] = undefined)

            emit('update:modelValue', newVal)
            nextTick(() => {
                emit('change', newVal)
            })
        }
        const closeAllTag = () => {
            const newVal = { ...modelValue.value }
            let selectItemsObg = {}
            selectItems.value?.forEach((item) => {
                selectItemsObg[item.key] = item
            })
            Object.keys(newVal).forEach((i) => {
                if (!selectItemsObg[i]?.allowClear) {
                    return
                }
                newVal[i] = Array.isArray(newVal[i]) ? [] : undefined
            })
            emit('update:modelValue', newVal)
            nextTick(() => {
                emit('change', newVal)
            })
        }

        //  setup
        const setupHandle = () => {
            // 初始化数据
            myOptions.value.forEach(async (i, idx) => {
                if (i.getMethod) {
                    myOptions.value[idx].options = (await i.getMethod()) as LabelValueOptions
                }
            })
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        setupHandle()

        return {
            inputPaste,
            closeTag,
            closeAllTag,
            inputHandle,
            inputChange,
            selectChange,
            dateChange,
            panelChange,
            isMultiple,
            changeHandle,
            myOptions,
            selectItems,
            limitNumber,
            datChange,
            yearChange,

            openChange,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>

<style scoped lang="less">
.search-bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    .input {
        width: 190px;
        margin-right: 10px;
    }
    & > * {
        margin: 5px 0;
    }
}
.select-bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin: 10px 0;
    .btn {
        margin-left: 5px;
        color: #5aa4ff;
        cursor: pointer;
        opacity: 0.8;
        font-size: 13px;
        padding: 3px 5px;
        &:hover {
            opacity: 1;
            background: white;
        }
    }
    .select-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .label {
            color: #adaeae;
            margin-right: 5px;
        }
        .value {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .tag {
                min-width: 50px;
                background: #f7f7f7;
                padding: 1px 5px 1px 8px;
                border-radius: 2px;
                margin-right: 8px;
                border: 1px solid #ddd;
                display: flex;
                justify-content: space-between;
                align-items: center;
                span {
                    margin-left: 4px;
                    color: #727989;
                    cursor: pointer;
                    &:hover {
                        color: #222;
                    }
                }
            }
        }
    }
}
</style>
