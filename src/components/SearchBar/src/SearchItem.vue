<template>
    <div class="searchItem">
        <div class="name">{{ label }}</div>
        <Input
            v-if="type == 'input'"
            class="input"
            :allowClear="true"
            @change="inputChange"
            @pressEnter="inputPressEnter"
            :placeholder="label"
        />
        <Select
            v-if="type == 'select'"
            class="input"
            :allowClear="allowClear"
            showSearch
            :value="value"
            :options="options"
            @change="selectChange"
            :placeholder="label"
        />
        <Select
            v-if="type == 'multiple'"
            class="input"
            :allowClear="allowClear"
            showSearch
            :value="value"
            :options="options"
            @change="selectChange"
            :placeholder="label"
            :mode="mode"
        />
        <RangePicker
            v-if="type == 'date'"
            class="input"
            :allowClear="allowClear"
            :value="value"
            @change="dateChange"
            style="width: 230px"
        />
        <DatePicker
            v-if="type == 'datePicker'"
            class="input"
            :allowClear="allowClear"
            :value="value"
            :placeholder="label"
            @change="datePickerChange"
        />
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'SearchItem',
    props: {
        allowClear: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'input',
        },
        value: {
            type: [String, Array],
            default: '',
        },
        label: {
            type: String,
            default: '',
        },
        options: {
            type: Array,
            default: () => [],
        },
        mode: {
            type: String,
            default: 'multiple',
        },
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
        const inputChange = (e) => {
            emit('update:value', e.target.value)
        }
        const inputPressEnter = (e) => {
            emit('change', {
                label: props.label,
                value: e.target.value,
            })
        }
        const selectChange = (data, option) => {
            emit('update:value', data)
            emit('change', {
                label: props.label,
                value: data,
                option,
            })
        }
        const dateChange = (date, dateString) => {
            emit('change', dateString)
        }
        const datePickerChange = (date, dateString) => {
            console.log(dateString)
            emit('change', dateString)
        }
        return {
            inputChange,
            inputPressEnter,
            selectChange,
            dateChange,
            datePickerChange,
        }
    },
})
</script>
<style scoped lang="less">
.searchItem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
}
</style>
