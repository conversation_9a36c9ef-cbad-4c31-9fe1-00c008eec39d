<template>
    <BasicEditModalSlot :visible="visible" :title="title" :closable="false" :zIndex="1009">
        <Steps class="stepsMain" :current="nowIndex">
            <Step title="上传文件" />
            <Step title="导入数据" />
            <Step title="完成" />
        </Steps>
        <div v-if="nowIndex == 0" class="stepOne">
            <p class="tip">
                1、按照要求填写模板文件
                <a download @click.prevent="downloadTem">下载模板文件</a>
            </p>
            <p style="margin: 20px 0 0 20px">模板的表头名称不能修改，顺序不能更改，表头不能删除</p>

            <p style="font-size: 14px; font-weight: bold; margin-top: 20px">2、选择导入文件</p>
            <div style="margin: 20px 0 0 20px">
                <Upload :before-upload="handleUpload" action :showUploadList="false">
                    <Button type="primary">{{ currentFile?.name ? '重新选取文件' : '点击选取文件' }}</Button>
                    <span style="margin-left: 10px">{{ currentFile?.name }}</span>
                </Upload>
            </div>
        </div>

        <div v-if="nowIndex == 1" style="padding: 20px; width: 100%; height: 260px">
            <div style="margin-top: 90px">
                <Progress hide-info stroke-color="#69CC66" :stroke-width="20" :percent="percent" status="active" />
                <p style="text-align: center; margin-top: 20px">正在导入...</p>
            </div>
        </div>

        <div v-if="nowIndex == 2" style="padding: 20px; width: 100%; height: 260px">
            <div style="text-align: right; font-size: 14px; margin-right: 150px">
                <p style="margin-top: 20px">
                    本次导入数据共
                    <span style="color: #0078d7; font-weight: bold; margin: 0 10px">
                        {{ resData.successCount + resData.errorCount + resData.duplicateCount }}
                    </span>
                    条
                </p>
                <p style="margin-top: 20px">
                    导入成功数据共
                    <span style="color: #3fd028; font-weight: bold; margin: 0 10px">{{ resData.successCount }} </span>
                    条
                </p>
                <p style="margin-top: 20px">
                    错误数据共
                    <span style="color: #ff0000; font-weight: bold; margin: 0 10px">{{ resData.errorCount }}</span>
                    条
                </p>
                <p style="margin-top: 20px">
                    重复数据共
                    <span style="color: #ff0000; font-weight: bold; margin: 0 10px">{{ resData.duplicateCount }} </span>
                    条
                </p>
                <a v-if="resData.failureFileUrl" :href="resData.failureFileUrl" download target="_blank">
                    <Button style="margin-top: 20px" danger type="primary">下载错误数据</Button>
                </a>
            </div>
        </div>

        <template #footer>
            <div v-if="nowIndex == 0">
                <Button @click="importCancel">取消</Button>
                <Button :disabled="!currentFile?.name" @click="startUpload" type="primary">开始上传</Button>
            </div>
            <div v-if="nowIndex == 1">
                <Button @click="stopUpload" disabled type="primary">导入中</Button>
            </div>
            <div v-if="nowIndex == 2">
                <Button @click="importCancel" type="primary">完成</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, PropType } from 'vue'
import request from '/@/utils/request'
import downFile from '/@/utils/downFile'
import { Steps, Step, Upload, message } from 'ant-design-vue'

export default defineComponent({
    name: 'ImportModal',
    components: {
        Steps,
        Step,
        Upload,
    },
    props: {
        downTempMethod: {
            type: String as PropType<'get' | 'post'>,
            default: 'get',
        },
        title: {
            type: String,
            default: '导入',
        },
        visible: Boolean,
        temUrl: String,
        temParams: {
            type: Object,
            default: () => {},
        },
        importUrl: {
            type: String,
            default: '',
        },
        importUrlParam: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['importComplete', 'update:visible', 'getResData'],
    setup(props, context) {
        const resData = ref<inObject>({})
        const nowIndex = ref<Number>(0)

        const percent = ref(0)

        const currentFile = ref<any>(null)
        const { title, temUrl, importUrl, importUrlParam, temParams } = toRefs(props)

        const handleUpload = (file) => {
            currentFile.value = file
            return false
        }

        const importCancel = () => {
            currentFile.value = null
            nowIndex.value = 0
            context.emit('update:visible')
            context.emit('importComplete')
        }

        const downloadTem = () => {
            downFile(props.downTempMethod, temUrl.value as string, '', temParams.value)
        }

        const startUpload = async () => {
            nowIndex.value = 1
            const formdata = new FormData()
            formdata.append('file', currentFile.value)
            console.log(importUrlParam.value)
            if (importUrlParam.value !== undefined) {
                // 导入参数
                Object.keys(importUrlParam.value).forEach((key) => {
                    formdata.append(key, importUrlParam.value[key])
                })
            }
            const setConfig = {
                onUploadProgress: (progressEvent) => {
                    // //'|'或与在此用于取整
                    let complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0
                    percent.value = Math.round(complete * 0.1)
                },
            }
            request
                .post(importUrl.value, formdata, { setConfig, loading: false })
                .then((redisKey) => {
                    if (typeof redisKey == 'string') {
                        speedOfProgress(redisKey)
                    } else {
                        resData.value = redisKey
                        nowIndex.value = 2
                        context.emit('getResData', resData.value)
                        console.log(resData.value)
                    }
                })
                .catch(() => {
                    nowIndex.value = 0
                })
        }

        const speedOfProgress = async (redisKey) => {
            request
                .get('/api/progress-bar/' + redisKey, {}, { loading: false })
                .then((res) => {
                    if (res.code == 200) {
                        if (res?.result) {
                            let result = JSON.parse(res?.result || '{}')
                            nowIndex.value = 2
                            resData.value = result
                            context.emit('getResData', result)
                        } else {
                            percent.value = Math.round((res.progress ? res.progress : 0) * 0.9 + 10)
                            setTimeout(async () => {
                                speedOfProgress(redisKey)
                            }, 500)
                        }
                    } else {
                        nowIndex.value = 0
                        if (res?.errorMsg) {
                            message.error(res.errorMsg)
                        }
                    }
                })
                .catch(() => {
                    nowIndex.value = 0
                })

            // Math.round((res.data ? res.data : 0) * 0.9 + 10)
            // console.log(this.percent)
        }
        const stopUpload = () => {
            nowIndex.value = 0
        }

        return {
            percent,

            resData,
            nowIndex,
            currentFile,
            startUpload,
            stopUpload,
            downloadTem,
            importCancel,
            handleUpload,
        }
    },
})
</script>

<style scoped lang="less">
.stepsMain {
    width: 94%;
    margin: 0 auto;
}
.stepOne {
    padding: 20px;
    width: 100%;
    height: 260px;
    .tip {
        font-size: 14px;
        font-weight: bold;
        margin-top: 20px;
    }
}
.seasonSelectView {
    display: flex;
    margin-top: 20px;
    align-items: center;
}
:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title) {
    color: #6894fe;
}
</style>
