<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" v-bind="$attrs" :width="`${width}px`" :footer="null">
        <img class="image" :src="imageUrl" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'ImgModal',
    components: {},
    props: {
        width: {
            type: String,
            default: '600',
        },
        imageUrl: {
            type: String,
            defalut: '',
        },
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        // cancel handle
        const cancel = () => {
            emit('cancel')
        }

        return {
            cancel,
        }
    },
})
</script>
<style scoped lang="less">
.image {
    width: 100%;
    height: 100%;
}
</style>
