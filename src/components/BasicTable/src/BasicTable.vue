<template>
    <Table
        class="basicTable"
        :class="classNames"
        style="width: 100%"
        :size="size"
        :columns="myColumns"
        :data-source="tableData"
        :bordered="bordered"
        :row-key="(record, index) => record.id ?? index"
        :pagination="pagination"
        :loading="loading"
        :scroll="scroll"
        :indentSize="30"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        :row-selection="
            rowSelectionShow
                ? {
                      selectedRowKeys: customSelectedKeys.length ? customSelectedKeys : selectedRowKeysArr,
                      onChange: onSelectChange,
                      getCheckboxProps: checkboxProps,
                      type: rowSelectionType,
                      hideDefaultSelections: rowSelectionType == 'checkbox',
                  }
                : null
        "
        @change="tableChange"
        @expand="expandChange"
        :expandedRowKeys="expandedRowKeys"
    >
        <template #[item]="data" v-for="item in Object.keys($slots)" :key="item">
            <slot :name="item" v-bind="data"></slot>
        </template>
    </Table>
</template>

<script lang="ts">
import { computed, defineComponent, h, onMounted, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { Modal, message } from 'ant-design-vue'
import downFile from '/@/utils/downFile'
import { toUnderline } from '/@/utils/index'

interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}

export default defineComponent({
    name: 'BasicTable',
    props: {
        classNames: Array,
        size: String,
        scroll: {
            type: Object,
            default: () => ({
                x: '100',
            }),
        },
        useIndex: {
            type: Boolean,
            default: false,
        },
        sorter: {
            type: Boolean,
            default: true,
        },
        isPage: {
            type: Boolean,
            default: true,
        },
        isSaverPage: {
            type: Boolean,
            default: true,
        },
        columns: {
            type: Array,
            default: () => [],
        },
        params: {
            type: Object,
            default: () => {},
        },
        api: {
            type: String,
            default: '',
        },
        deleteApi: {
            type: String,
            default: '',
        },
        exportUrl: {
            type: String,
            default: '',
        },
        bordered: {
            type: Boolean,
            default: true,
        },
        isExpand: {
            type: Boolean,
            default: false,
        },
        tableDataList: {
            type: Array,
            default: () => [],
        },
        tableDataFormat: {
            type: Function,
            default: (item) => {
                return item
            },
        },
        getChildrenMethod: {
            type: Function,
        },
        rowSelectionShow: {
            type: Boolean,
            default: true,
        },
        pageSize: {
            type: Number,
            default: 10,
        },
        //table中的checkbox 业务规则
        checkboxProps: {
            type: Function,
            default: (item) => {
                return {}
            },
        },
        rowSelectionType: {
            type: String,
            default: 'checkbox',
        },
        customSelectedKeys: {
            type: Array,
            default: () => [],
        },
        sortered: {
            type: Function,
            default: () => {
                return ''
            },
        },
    },
    emits: ['getData', 'selectedRowsArr', 'getTableData', 'getData2', 'expand'],
    setup(props: any, content) {
        const { api, params, tableDataList, tableDataFormat, deleteApi, exportUrl, columns, sortered } = toRefs(props)
        const tableData = ref([])
        const sortParams = ref({
            field: undefined,
            order: undefined,
        })
        const expandedRowKeys = ref<any[]>([])
        console.log(expandedRowKeys.value)
        watch(tableDataList, () => {
            setTableData()
        })

        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])

        const pagination = ref<any>({
            current: 1,
            pageSize: props.pageSize,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
            total: 0,
            // onChange: (current) => {
            //     pagination.value.current = current
            //     getTableData()
            // },
            // onShowSizeChange: (_current, size) => {
            //     pagination.value.pageSize = size
            //     getTableData()
            // },
        })
        const setTableData = () => {
            if (!api.value) {
                tableData.value = tableDataList.value
                pagination.value = false
            }
            if (props.isExpand) {
                console.log('isExpandisExpand')

                tableData.value = tableDataList.value
            }
        }
        const loading = ref(false)
        const getTableData = async () => {
            return new Promise(async (resolve, reject) => {
                loading.value = true
                try {
                    if (api.value) {
                        // console.log(pagination.value)
                        // const fn = () =>
                        const data = await request.post(
                            props.isPage
                                ? `${api.value}?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`
                                : api.value,
                            {
                                ...params.value,
                                ...{
                                    field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                    order:
                                        sortParams.value.order === 'ascend'
                                            ? 'DESC'
                                            : sortParams.value.order === 'descend'
                                            ? 'ASC'
                                            : undefined,
                                },
                            },
                            { loading: false },
                        )
                        tableData.value = data.records || data || []
                        content.emit('getData2', tableData.value)
                        tableData.value = tableDataFormat.value(tableData.value)
                        // console.log(tableDataFormat.value(tableData.value))
                        pagination.value.total = data.total || 0
                        // 复选框置空
                        selectedRowsArr.value = []
                        selectedRowKeysArr.value = []
                        content.emit('selectedRowsArr', [])
                        content.emit('getTableData', data)
                        if (props.getChildrenMethod) {
                            // 展开关闭
                            expandedRowKeys.value = []
                        }
                        resolve(data)
                    } else {
                        setTableData()
                        resolve({})
                    }
                } catch (err) {
                    reject(err)
                } finally {
                    loading.value = false
                }
            })
        }

        onMounted(() => {
            getTableData()
        })

        const refresh = (page = pagination.value.current) => {
            pagination.value.current = page
            return getTableData()
        }

        content.emit('getData', tableData.value)

        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            console.log('selectedRows', selectedRows)

            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
            content.emit('selectedRowsArr', selectedRows, selectedRowKeys)
        }

        // 复选框置空
        const checkboxReset = () => {
            selectedRowsArr.value = []
            selectedRowKeysArr.value = []
            content.emit('selectedRowsArr', [])
        }

        //删除
        const deleteRow = (id?: string | number, showTip = true) => {
            let ids: (string | number)[] = []
            if (id) {
                ids = [id]
            } else {
                ids = selectedRowsArr.value.map((item: DataItem) => {
                    return item.id
                })
            }

            return new Promise(function (resolve, reject) {
                if (ids.length == 0) {
                    reject({ massage: '请先选择一条数据' })
                    message.error('请先选择一条数据!')
                    return false
                }
                if (ids.length > 100) {
                    reject({ massage: '最多选择100条数据' })
                    message.error('最多选择100条数据!')
                    return false
                }
                Modal.confirm({
                    title: '确认',
                    content: '是否确认删除所选内容？',
                    onOk() {
                        request
                            .post(deleteApi.value, ids, { loading: true })
                            .then((ref) => {
                                refresh(1)
                                resolve(ref)
                                if (showTip) {
                                    message.success('删除成功!')
                                }
                            })
                            .catch((ref) => {
                                // console.log(ref)
                                reject(ref)
                            })
                    },
                    onCancel() {
                        reject({ massage: '用户取消删除' })
                    },
                })
            })
        }

        // 下载
        const exportRow = async (
            key,
            str,
            filter,
            useCustomParams = false,
            customParams?: { keys: Array<string>; params: Array<string> },
        ) => {
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (str.indexOf('选中') != -1) {
                let body = {}
                if (useCustomParams && customParams) {
                    customParams.keys.forEach((el: string, index) => {
                        body[customParams.params[index]] = selectedRowsArr.value.map((item: DataItem) => {
                            return item[el]
                        })
                    })
                } else {
                    body[key] = selectedRowsArr.value.map((item: DataItem) => {
                        return item.id
                    })
                }
                await downFile('post', exportUrl.value, '', body)
            } else if (str.indexOf('筛选') != -1) await downFile('post', exportUrl.value, '', { ...filter })
            else await downFile('post', exportUrl.value, '', {})
        }

        // 自定义列展示
        const myColumns = computed(() =>
            props.useIndex
                ? [
                      {
                          title: '序号',
                          dataIndex: 'index',
                          align: 'center',
                          customRender: (record) =>
                              pagination.value
                                  ? (pagination.value.current - 1) * pagination.value.pageSize + record.index + 1
                                  : record.index + 1,
                          width: 80,
                      },
                      ...columns.value.map((i) => ({
                          ...i,
                          sorter: props.sorter === false ? false : i.title == '操作' || i.sorter === false ? false : true,
                          align: i.align || 'center',
                      })),
                  ]
                : columns.value.map((i) => ({
                      ...i,
                      sorter: props.sorter === false ? false : i.title == '操作' || i.sorter === false ? false : true,
                      align: i.align || 'center',
                  })),
        )

        const tableChange = ({ current, pageSize }, _filters, sorter: any = { field: undefined, order: undefined }) => {
            // console.log('tableChange', sorter.field === sortParams.value.field && sorter.order === sortParams.value.order)
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            if (!props.isSaverPage) {
                return
            }

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field:
                              sorter.column?.sortField && sorter.column?.sortField != sorter.field
                                  ? sorter.column?.sortField
                                  : sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }
            let changeSortedMethod = sortered.value
            changeSortedMethod(sortParams.value)
            pagination.value.current = current
            pagination.value.pageSize = pageSize
            getTableData()
        }
        const expandChange = async (expanded, record) => {
            if (expanded) {
                expandedRowKeys.value.push(record.id)
                if (props.getChildrenMethod) {
                    if (record.children.length > 0) {
                        return
                    }
                    try {
                        loading.value = true
                        record.children = props.getChildrenMethod ? await props.getChildrenMethod(record) : record.children
                    } catch (error) {
                        record.children = null
                    } finally {
                        loading.value = false
                    }
                }
            } else {
                expandedRowKeys.value = expandedRowKeys.value.filter((i) => i !== record.id)
            }
        }

        return {
            //方法
            loading,
            tableChange,
            expandChange,
            // 数据
            expandedRowKeys,
            tableData,
            myColumns,
            pagination,
            // 给外部使用方法
            refresh,
            deleteRow,
            exportRow,
            onSelectChange,
            selectedRowsArr,
            selectedRowKeysArr,
            checkboxReset,
        }
    },
})
</script>
<style scoped>
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
