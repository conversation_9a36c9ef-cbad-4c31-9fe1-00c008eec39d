<template>
    <div v-if="!disabled" class="replace">
        <Button type="primary" @click="openStaffChoice">选择员工</Button>
    </div>
    <FormItem label="姓名" :name="formItemNames[0]">
        <!-- <Input v-model:value="nameData" placeholder="请输入姓名" @blur="inputBlur" :disabled="disabled" /> -->
        <Input v-model:value="nameData" placeholder="请输入姓名" :disabled="true" />
    </FormItem>
    <FormItem label="身份证号" :name="formItemNames[1]">
        <Input v-model:value="cardData" placeholder="请输入身份证号" :disabled="true" />
        <!-- <Select
            v-model:value="cardData"
            show-search
            allowClear
            placeholder="请输入身份证号"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            :options="data"
            @search="handleSearch"
            @change="handleChange"
            @focus="handleSearch(cardData)"
            :getPopupContainer="getPopupContainer"
            :disabled="disabled"
        /> -->
    </FormItem>
    <StaffChoiceList
        v-model:visible="staffChoiceShow"
        :clientId="clientId"
        @confirm="staffChoiceConfirm"
        :defaultParams="{ name: nameData, certificateNum: cardData }"
    />
</template>
<script lang="ts">
import { computed, defineComponent, ref, toRefs } from 'vue'
import request from '/@/utils/request'
import StaffChoiceList from './staffChoiceList.vue'
export default defineComponent({
    name: 'EmployeeMatching',
    components: {
        StaffChoiceList,
    },
    props: {
        formItemNames: {
            type: Array,
            default: () => ['name', 'certificateNum'],
        },
        clientId: {
            type: String,
            default: '',
        },
        name: {
            type: String,
            default: null,
        },
        card: {
            type: String,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['changeCertificateNum', 'update:card', 'update:name'],
    setup(props, { emit }) {
        const data = ref<any[]>([])
        const { card, name, clientId } = toRefs<any>(props)
        const cardData = computed({
            get: () => card.value,
            set: (val) => {
                emit('update:card', val)
            },
        })
        const nameData = computed({
            get: () => name.value,
            set: (val) => {
                emit('update:name', val)
            },
        })
        let timeout: any
        let currentValue = ''
        const fetch = (certificateNum: string, callback: any) => {
            if (timeout) {
                clearTimeout(timeout)
                timeout = null
            }
            currentValue = certificateNum
            function fake() {
                request
                    .post('/api/hr-staff/search-data', {
                        clientId: clientId.value,
                        name: name.value,
                        certificateNum: certificateNum,
                    })
                    .then((res) => {
                        if (currentValue === certificateNum) {
                            const data: any[] = []
                            res.forEach((el: any) => {
                                data.push({
                                    value: el.certificateNum,
                                    label: el.certificateNum,
                                    ...el,
                                })
                            })
                            callback(data)
                        }
                    })
            }

            //节流300毫秒
            timeout = setTimeout(fake, 300)
        }

        const handleSearch = (val: string) => {
            fetch(val, (d: any[]) => (data.value = d))
        }
        const handleChange = (val: string, option: inObject = {}) => {
            cardData.value = val
            nameData.value = option?.name
            emit('changeCertificateNum', option)
        }
        const inputBlur = () => {
            cardData.value = null
            console.log(cardData.value)
            // fetch('', (d: any[]) => (data.value = d))
        }
        const staffChoiceShow = ref(false)
        const openStaffChoice = () => {
            staffChoiceShow.value = true
        }
        const staffChoiceConfirm = (data) => {
            if (data) {
                cardData.value = data?.certificateNum
                nameData.value = data?.name
                emit('changeCertificateNum', data)
            }
        }
        return {
            openStaffChoice,
            staffChoiceShow,
            staffChoiceConfirm,

            // handleSearch,
            // handleChange,
            // inputBlur,
            // data,
            cardData,
            nameData,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>

<style scoped lang="less">
.replace {
    width: 100%;
    :deep(.ant-btn) {
        // width: 130px;
        padding: 0 10px;
        margin: -5px 0 15px -10px;
    }
}
</style>
