<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" title="选择员工" width="1000px">
        <div class="search-wrapper">
            <Input
                v-model:value="search.name"
                placeholder="姓名"
                @blur="filterTableData"
                @pressEnter="filterTableData"
                @paste="filterTableData"
            />
            <Input
                v-model:value="search.certificateNum"
                placeholder="身份证号"
                @blur="filterTableData"
                @pressEnter="filterTableData"
                @paste="filterTableData"
            />
        </div>
        <Table
            ref="tableRef"
            :dataSource="tableData"
            :columns="columns"
            :scroll="{ x: '100' }"
            bordered
            :row-key="(record) => record"
            :rowSelection="selectionRowConfig"
            rowSelectionType="radio"
            :pagination="pagination"
            @change="tableChange"
        />
        <template #footer>
            <div>
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
export default defineComponent({
    name: 'StaffChoiceList',
    props: {
        clientId: {
            type: String,
            default: '',
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        defaultParams: {
            type: Object,
            default: () => {},
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { visible, defaultParams, viewType, clientId } = toRefs<any>(props)
        console.log(viewType.value)
        //筛选
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
        ]
        const params = ref<inObject>({})
        const onSelectChange = (selectedRowKeys: (string | number)[]) => {
            selectedRowsArr.value = selectedRowKeys
            console.log(selectedRowsArr.value)
        }
        const selectionRowConfig = computed(() => {
            return {
                selectedRowKeys: selectedRowsArr,
                onChange: onSelectChange,
                type: 'radio',
                getCheckboxProps: (record: inObject) => {
                    let defaultChecked = false
                    return {
                        defaultChecked: defaultChecked,
                        id: record.id + '',
                    }
                },
            }
        })
        watch(visible, (val) => {
            if (val) {
                // params.value = Object.assign(params.value, defaultParams.value)
                getDetail()
            }
        })

        // 搜索
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const tableData = ref<inObject[]>([])
        const getDetail = async () => {
            await request
                .post(`/api/hr-talent-staffs/page?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`, {
                    ...params.value,
                    name: search.value.name ?? '',
                    certificateNum: search.value.certificateNum ?? '',
                    izDefault: false,
                    clientId: clientId.value || null,
                    staffStatusList: viewType.value == 'regularWorker' ? [3] : [3, 4, 7, 8, 9, 10, 11, 12],
                })
                .then((res) => {
                    console.log(res)
                    pagination.value.total = res.total
                    tableData.value = res.records
                })
        }
        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            console.log(current)
            pagination.value.current = current
            pagination.value.pageSize = pageSize
            getDetail()
        }
        //表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
            },
            {
                title: '合同起始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
            },
        ]

        //选择行
        const selectedRowsArr = ref<any[]>([])

        const funSelectedRowsArr = (selected) => {
            if (selected?.length) {
                // hasItBeenSelected.value = true
                selectedRowsArr.value = selected
            }
        }

        const checkboxProps = (record: inObject) => {
            let defaultChecked = false
            defaultChecked = record.certificateNum == selectedRowsArr.value[0]?.certificateNum
            return {
                defaultChecked: defaultChecked,
                id: record.id + '',
            }
        }
        const search = ref({
            name: '',
            certificateNum: '',
        })
        const pagination = ref({
            current: 1,
            pageSize: 5,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
            total: 0,
        })
        const filterTableData = () => {
            let filterOdds
            if (!search.value.name && !search.value.certificateNum) {
                pagination.value.total = tableData.value.length
                getDetail()
                return
            } else if (!search.value.name && search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.certificateNum.includes(search.value.certificateNum)
                }
            } else if (search.value.name && !search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name)
                }
            } else {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name) && el.certificateNum.includes(search.value.certificateNum)
                }
            }
            tableData.value = tableData.value.filter((el) => {
                return filterOdds(el)
            })
            getDetail()
            pagination.value.current = 1
            pagination.value.total = tableData.value.length
        }
        const onCancel = () => {
            selectedRowsArr.value = []
            emit('update:visible', false)
            emit('cancel', true)
        }
        const onConfirm = () => {
            console.log(selectedRowsArr.value)
            if (selectedRowsArr.value?.length) {
                emit('confirm', selectedRowsArr.value[0])
                onCancel()
            } else {
                message.error('请选择一位员工！')
            }
        }

        return {
            checkboxProps,
            columns,
            tableRef,
            funSelectedRowsArr,
            selectedRowsArr,
            options,
            params,
            searchData,
            onCancel,
            onConfirm,
            tableData,
            search,
            pagination,
            selectionRowConfig,
            filterTableData,
            tableChange,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
.search-wrapper {
    display: flex;
    margin: 10px 0;
    & > input {
        width: 230px;
        margin-right: 20px;
    }
}
</style>
