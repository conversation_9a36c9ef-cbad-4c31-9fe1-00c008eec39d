<template>
    <Modal
        :visible="visible"
        :title="title"
        @ok="confirm"
        @cancel="cancel"
        destroyOnClose
        :width="`${width}px`"
        :wrapClassName="domName"
    >
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            style="min-height: 250px"
        >
            <template v-for="(i, idx) in myOptions" :key="idx">
                <FormItem :label="i.label" :name="i.name" v-show="i.show != false">
                    <!-- 默认为string input -->
                    <Input
                        v-if="!i.type || i.type == 'string'"
                        v-model:value="formData[i.name]"
                        :placeholder="i.placeholder || `请输入${i.label}`"
                    />
                    <template v-else>
                        <Select
                            v-if="i.type == 'select'"
                            v-model:value="formData[i.name]"
                            showSearch
                            :mode="i.multiple ? 'multiple' : undefined"
                            :options="i?.option || []"
                            :placeholder="i.placeholder || `请输入${i.label}`"
                            :maxTagCount="2"
                            @change="selectChanged(i.name, formData[i.name])"
                        />
                        <Select
                            v-else-if="i.type == 'change'"
                            v-model:value="formData[i.name]"
                            showSearch
                            :options="i?.option || []"
                            :placeholder="i.placeholder || `请输入${i.label}`"
                        />
                        <InputNumber
                            v-else-if="i.type == 'number'"
                            v-model:value="formData[i.name]"
                            :placeholder="i.placeholder || `请输入${i.label}`"
                            style="width: 100%"
                        />
                        <DatePicker
                            v-else-if="i.type == 'datetime'"
                            v-model:value="formData[i.name]"
                            format="YYYY-MM-DD HH:mm:ss"
                            :placeholder="i.placeholder || `请输入${i.label}`"
                            valueFormat="YYYY-MM-DD HH:mm:ss"
                        />
                        <MonthPicker
                            v-else-if="i.type == 'month'"
                            v-bind="i.attrs"
                            v-model:value="formData[i.name]"
                            format="YYYY-MM"
                            :placeholder="i.placeholder || `请输入${i.label}`"
                            valueFormat="YYYY-MM"
                        />
                        <Switch
                            v-else-if="i.type == 'switch'"
                            v-model:checked="formData[i.name]"
                            checked-children="启用"
                            un-checked-children="禁用"
                            :checkedValue="true"
                            :unCheckedValue="false"
                        />
                        <SelectDic
                            v-else-if="i.type == 'SelectDic'"
                            v-model:value="formData[i.name]"
                            :disType="i.disType"
                            :disParentId="i.disParentId"
                        />
                        <Textarea
                            v-else
                            v-model:value="formData[i.name]"
                            :rows="3"
                            allowClear
                            :placeholder="i.placeholder || `请输入${i.label}`"
                            valueFormat="YYYY-MM-DD 00:00:00"
                        />
                    </template>
                </FormItem>
            </template>
        </Form>
    </Modal>
</template>
<script lang="ts">
import { message, MonthPicker } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, PropType, nextTick } from 'vue'
import type { EditModalOption } from '/#/component'
import request from '/@/utils/request'
import { dragModal, getInitValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'EditModal',
    components: { MonthPicker },
    props: {
        api: {
            type: String,
            defalut: '',
        },
        width: {
            type: String,
            default: '600',
        },
        options: {
            type: Array as PropType<EditModalOption[]>,
            default: () => [],
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: String,
        item: {
            type: Object,
        },
        defaultParams: {
            type: Object,
        },
        beforeConfirm: {
            type: Function,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit, attrs }) {
        // deep copy 方便修改
        const myOptions = ref([...props.options])
        // Form 实例
        const formInline = ref(null) as any
        // 响应式 props
        const { item, visible, title, defaultParams } = toRefs(props)
        // FormData rules 初始值
        const { values: initFormData, rules } = getInitValuesAndRules(props.options)
        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        // open modal 赋值
        watch(visible, () => {
            formData.value =
                visible.value && item.value
                    ? { ...item.value }
                    : defaultParams.value
                    ? { ...defaultParams.value, ...initFormData }
                    : { ...initFormData }
        })
        // confirm handle
        const confirm = async () => {
            try {
                await formInline.value.validate()
                let res
                if (props.beforeConfirm) {
                    //自定义的确认操作
                    res = await props.beforeConfirm(formData.value)
                } else if (props.api) {
                    if (title.value?.includes('新增')) {
                        res = await request.post(props.api, formData.value)
                        message.success('新增成功!')
                    } else {
                        res = await request.put(props.api, formData.value)
                        message.success('编辑成功!')
                    }
                }
                // 表单关闭后的其它操作 如刷新表
                emit('confirm', formData.value, res)
                resetFormData()
            } catch (error) {
                console.log('表单验证失败', error)
            }
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        const selectChanged = async (key, val, clear = true) => {
            const options = props.options
            let indexList: number[] = []
            options.forEach((i, idx) => {
                i.changedByField && i.changedByField == key && indexList.push(idx)
            })
            if (!indexList.length) {
                return
            }
            for (const i in indexList) {
                const idx = indexList[i]
                const requestMethod = options[idx].changedMethod as Function
                // 赋给 子组件变量
                myOptions.value[idx].option = val || val == 0 ? await requestMethod(val) : []
                if (clear) {
                    // 点编辑时主动触发的不用清除formData数据
                    formData.value[options[idx].name] = initFormData[options[idx].name]
                }
            }
        }
        const fn_Guid = () => {
            function s4() {
                return Math.floor((1 + Math.random()) * 0x10000)
                    .toString(16)
                    .substring(1)
            }
            return s4() + '-' + s4() + '-' + s4()
        }
        const domName = ref(`edit_modal-wrapper_${fn_Guid()}`)
        watch(
            () => props.visible,
            (newV) => {
                if (newV) {
                    nextTick(() => {
                        dragModal(domName.value, attrs.hasOwnProperty('centered'), true)
                    })
                }
            },
        )
        //  setup
        const setupHandle = () => {
            // 初始化数据
            myOptions.value.forEach(async (i, idx) => {
                if (i.getMethod) {
                    myOptions.value[idx].option = (await i.getMethod()) as LabelValueOptions
                }
            })
        }
        setupHandle()
        return {
            domName,
            myOptions,
            selectChanged,
            rules,
            formInline,
            confirm,
            cancel,
            formData,
        }
    },
})
</script>
