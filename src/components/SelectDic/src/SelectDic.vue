<template>
    <div style="width: 100%">
        <Select v-model:value="disValue" v-bind="$attrs" @change="change" :options="DictionariesList" :disabled="disabled">
            <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
                <Divider style="margin: 4px 0" />
                <div style="padding: 4px 8px; cursor: pointer" @mousedown="(e) => e.preventDefault()" @click="addItem">
                    <plus-outlined />
                    添加字段
                </div>
            </template>
        </Select>
        <BasicEditModalSlot :visible="showModal" @cancel="cancel" @confirm="confirm" :title="title">
            <Form ref="formSelectDic" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
                <template v-for="item in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                        <template #img>111</template>
                    </MyFormItem>
                </template>
            </Form>
        </BasicEditModalSlot>
    </div>
</template>
<script lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue'
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import request from '/@/utils/request'
import { message } from 'ant-design-vue'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'SelectDic',
    components: {
        PlusOutlined,
        VNodes: (_, { attrs }) => {
            return attrs.vnodes
        },
    },
    props: {
        disType: String,
        disParentId: Number,
        value: [String, Number],
        title: {
            type: String,
            default: '添加字典表',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
        const { disType, disParentId, value, title } = toRefs(props)
        const change = (value, item) => {
            emit('change', value, item)
        }
        const disValue = computed({
            get: () => {
                return value.value
            },
            set: (val) => {
                emit('update:value', val)
            },
        })
        let DictionariesList = ref<any>([])
        const getDictionariesList = () => {
            if (title.value == '添加日程类型') {
                request.get('/api/hr-schedule-types/list').then((ref) => {
                    DictionariesList.value = ref.map((item: inObject) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            } else {
                request.get(`/api/com-code-tables/getCodeTableByInnerName/${disType.value}`).then((ref) => {
                    DictionariesList.value = ref.map((item: inObject) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            }
        }
        watch(
            disType,
            () => {
                getDictionariesList()
            },
            { immediate: true },
        )

        const addItem = () => {
            showModal.value = true
            if (title.value == '添加日程类型') {
                myOptions.value = []
                myOptions.value.push(
                    { label: '日程类型名称', name: 'scheduleTypeName' },
                    { label: '日程类型值', name: 'scheduleTypeId', disabled: true },
                )
            }
        }
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '字典名称',
                name: 'itemName',
            },
            // {
            //     label: '字典值',
            //     name: 'itemValue',
            //     required: false,
            // },
        ])
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        const formData = ref<any>(initFormData)

        const showModal = ref(false)
        const formSelectDic = ref()
        const confirm = () => {
            formSelectDic.value
                .validate()
                .then(async () => {
                    if (title.value == '添加日程类型') {
                        await request.post('/api/hr-schedule-types', { ...formData.value })
                    } else {
                        await request.post('/api/com-code-tables', { ...formData.value, parentId: disParentId.value })
                    }
                    message.success('添加成功!')
                    getDictionariesList()
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })

            // 151
        }

        const cancel = () => {
            showModal.value = false
        }
        return {
            DictionariesList,
            disValue,
            addItem,

            myOptions,
            showModal,
            confirm,
            cancel,
            change,

            rules,
            formData,
            //ref
            formSelectDic,
        }
    },
})
</script>
