<template>
    <div>
        <!-- <Button
                type="primary"
                size="small"
                :disabled="operationItems.list[0]?.disabled ?? false"
                :danger="operationItems.list[0]?.neme == '删除'"
                @click="myOperationClick(operationItems.list[0])"
            >
                {{ operationItems.list[0]?.neme }}
            </Button> -->
        <template v-for="(item, index) in operationItems" :key="index">
            <Button
                type="primary"
                size="small"
                :disabled="item?.disabled ?? false"
                :danger="item?.neme == '删除'"
                :style="{ backgroundColor: item.backColor, border: 'none' }"
                @click="myOperationClick(item)"
            >
                {{ item?.neme }}
            </Button>
            &nbsp;
        </template>
        <template v-if="operationItemsMore.length">
            <Dropdown :getPopupContainer="getPopupContainer">
                <template #overlay>
                    <Menu>
                        <template v-for="(item, i) in operationItemsMore" :key="i">
                            <MenuItem :disabled="item.disabled ?? false" @click="myOperationClick(item)">
                                {{ item?.neme }}
                            </MenuItem>
                        </template>
                    </Menu>
                </template>
                <Button size="small">
                    更多
                    <DownOutlined />
                </Button>
            </Dropdown>
        </template>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { useAuth } from '/@/utils/hooks'
import { DownOutlined } from '@ant-design/icons-vue'
export default defineComponent({
    name: 'MyOperation',
    components: { DownOutlined },
    props: {
        record: Object,
        myOperation: Array,
    },
    emits: ['myOperationClick'],
    setup(props, { emit }) {
        const { record, myOperation } = toRefs<any>(props)
        const operationItems = ref<inObject[]>([])
        const operationItemsMore = ref<inObject[]>([])
        const funOperationItems = () => {
            let items: inObject[] = []
            myOperation.value.forEach((element) => {
                let show = typeof element.show == 'boolean' ? element.show : element.show(record.value)
                // if (useAuth.value.buttons.find((i) => i == element.auth || !element.auth)) {
                if (show) {
                    items.push(element)
                }
                // }
            })
            return items
        }
        function group(myArray: any[], subGroupLength) {
            let newArray: any[] = []
            newArray.push(myArray.slice(0, subGroupLength))
            newArray.push(myArray.slice(subGroupLength))
            return newArray
        }
        watch(
            record,
            () => {
                let newOperationItems = funOperationItems()
                // console.log(newOperationItems)
                // operationItems.value
                if (newOperationItems.length > 3) {
                    let groupedArray = group(newOperationItems, 2)
                    operationItems.value = groupedArray[0]
                    operationItemsMore.value = groupedArray[1]
                } else {
                    operationItems.value = newOperationItems
                    operationItemsMore.value = []
                }
            },
            { immediate: true },
        )
        const myOperationClick = (item) => {
            emit('myOperationClick', item, record.value)
        }

        return {
            operationItems,
            operationItemsMore,
            myOperationClick,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
