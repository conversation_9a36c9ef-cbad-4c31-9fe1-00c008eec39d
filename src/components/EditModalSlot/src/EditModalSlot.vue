<template>
    <Modal
        v-bind="$attrs"
        :visible="visible"
        :title="title"
        @ok="confirm"
        @cancel="cancel"
        :width="width"
        :maskClosable="false"
        :keyboard="false"
        :bodyStyle="bodyStyle"
        :wrapClassName="domName"
        destroyOnClose
    >
        <slot></slot>
        <template #footer>
            <slot name="footer"></slot>
        </template>
    </Modal>
</template>
<script lang="ts">
import { defineComponent, watch, ref, nextTick } from 'vue'
import { dragModal } from '/@/utils'

export default defineComponent({
    name: 'EditModalSlot',
    props: {
        width: {
            type: [String, Number],
            default: '600px',
        },

        visible: {
            type: Boolean,
            default: false,
        },
        title: String,
        modalHeight: {
            type: String,
            default: '75vh',
        },
        isOverflow:String,
    },
    emits: ['confirm', 'cancel', 'ok'],
    setup(props, { emit, attrs }) {
        // confirm handle
        const confirm = () => {
            // 表单关闭后的其它操作 如刷新表
            emit('confirm')
            emit('ok')
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
        }
        const fn_Guid = () => {
            function s4() {
                return Math.floor((1 + Math.random()) * 0x10000)
                    .toString(16)
                    .substring(1)
            }
            return s4() + '-' + s4() + '-' + s4()
        }
        const domName = ref(`basic_modal-wrapper_${fn_Guid()}`)
        watch(
            () => props.visible,
            (newV) => {
                if (newV) {
                    nextTick(() => {
                        dragModal(domName.value, attrs.hasOwnProperty('centered'), true)
                    })
                }
            },
        )

        return {
            confirm,
            cancel,
            domName,
            bodyStyle: { maxHeight: props.modalHeight, overflow:props.isOverflow},
        }
    },
})
</script>
<style lang="less">
.ant-modal-body {
    // max-height: 75vh;
    overflow-y: auto;
}
.ant-modal {
    max-width: 98vw !important;
}
</style>
