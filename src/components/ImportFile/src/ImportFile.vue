<template>
    <div>
        <div v-if="readOnly && !fileList?.length">暂无附件</div>
        <Upload
            :action="action"
            list-type="picture"
            v-model:file-list="fileList"
            class="upload-list-inline"
            :before-upload="beforeUpload"
            @change="handleChange"
            :headers="headers"
            :show-upload-list="false"
            :multiple="multiple"
            :accept="accept"
            :data="actionData"
        >
            <slot>
                <Button v-if="!readOnly && (fileList?.length || 0) < count">
                    <upload-outlined />
                    {{ btnTitle }}
                </Button>
            </slot>
        </Upload>
        <div class="file-list-boxs" v-if="listType == 'fileBox'" :key="Math.random()">
            <div class="file-list-box" v-for="(item, index) in fileList" :key="item.uid">
                <Tooltip placement="top" :key="Math.random()">
                    <template #title>
                        <span>{{ item.name || '' }}</span>
                    </template>
                    <div class="file-list-name" @click="myPreviewFile(item)">
                        <loading-outlined v-if="item.status == 'uploading'" />
                        <template v-else>
                            <img
                                v-if="['jpg', 'png', 'jpeg'].includes(item.name.split('.')[1])"
                                :key="Math.random()"
                                class="img"
                                :src="item.url || item?.response?.fileUrl"
                                fallback="data:image/png;base64,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"
                            />
                            <p v-else class="txt">{{ item.name.split('.')[0] }}</p>
                        </template>
                    </div>
                </Tooltip>
                <p class="file-list-title">
                    {{ item.name }}
                </p>
                <div class="file-list-button" :style="(readOnly ? readOnly : noDel) ? 'justify-content: center;' : ''">
                    <Button
                        size="small"
                        type="primary"
                        danger
                        @click="deleteUrl(item, index)"
                        v-if="!(readOnly ? readOnly : noDel)"
                        ><DeleteOutlined />删除</Button
                    >
                    <Button size="small" type="primary" @click="downUrl(item, index)"><DownloadOutlined />下载</Button>
                </div>
            </div>
        </div>
        <div class="file-list-boxs1" v-if="listType == 'fileList'" :key="Math.random()">
            <div class="file-list-box1" v-for="(item, index) in fileList" :key="item.uid">
                <div class="file-list-name">
                    {{ item.name.split('.')[0] }}
                </div>
                <div class="file-list-button">
                    <EyeOutlined class="See" @click="myPreviewFile(item)" />
                    <DownloadOutlined class="Download" @click="downUrl(item, index)" />
                    <DeleteOutlined class="Dele" v-if="!(readOnly ? readOnly : noDel)" @click="deleteUrl(item, index)" />
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { UploadOutlined, LoadingOutlined, DownloadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import downFile from '/@/utils/downFile'
import appConfig from '/@/config'
import { previewFile } from '/@/utils/index'
interface FileItem {
    uid: string
    id?: string
    name?: string
    originName?: string
    status?: string
    response?: any
    url?: string
    type?: string
    size?: number
    originFileObj?: any
}
interface FileInfo {
    file: FileItem
    fileList: FileItem[]
}
export default defineComponent({
    components: {
        UploadOutlined,
        LoadingOutlined,
        DownloadOutlined,
        DeleteOutlined,
        EyeOutlined,
    },
    props: {
        listType: {
            type: String,
            default: 'fileBox',
        },
        fileUrls: {
            type: Array,
            default: () => [],
        },
        multiple: {
            type: Boolean,
            default: true,
        },
        count: {
            type: Number,
            default: 99,
        },
        accept: {
            type: String,
            default: '',
        },
        api: {
            type: String,
            default: '/api/hr-appendixes/upload-single-file',
        },
        actionData: {
            type: Object,
            default: () => {},
        },
        noDel: {
            type: Boolean,
            default: false,
        },
        readOnly: {
            type: Boolean,
            default: false,
        },
        btnTitle: {
            type: String,
            default: '附件上传',
        },
    },
    //虽然做了双向数据绑定，但是不能直接作为参数传与后台
    //建议使用 getFileUrls 方法
    emits: ['update:fileUrls', 'delChange', 'changeResponse'],
    setup(props, { emit }) {
        const { fileUrls, multiple, accept, api } = toRefs<any>(props)
        const action = appConfig.baseUrl + api.value
        const headers = {
            Authorization: 'Bearer ' + (localStorage.token || '').trim().replace(/^Bearer\s+/, ''),
            isEncrypt: 'no',
        }

        const fileList = computed({
            get: () => {
                return setFileUrls(fileUrls.value)
            },
            set: (val) => {
                emit('update:fileUrls', val || [])
            },
        })
        const setFileUrls = (fileUrl: any[]) => {
            return fileUrl?.map((item, index) => {
                let newItem = item
                if (item.originName) {
                    newItem.name = item.originName
                }
                if (item.uid) {
                    return newItem
                }
                return { uid: `-${index}`, status: 'done', url: item.fileUrl, ...newItem }
            })
        }
        //传参时的参数获取
        const getFileUrls = () => {
            // console.log(fileList.value)
            return (
                fileList.value
                    ?.filter((item) => {
                        return item.status == 'done'
                    })
                    ?.map((item) => {
                        return {
                            name: item.name,
                            url: item.url || item?.response?.fileUrl || '',
                            id: item.id || item?.response?.id || item.uid,
                        }
                    }) || []
            )
        }

        const handleChange = (info: FileInfo) => {
            // console.log(info)
            if (info.file.status === 'uploading') {
                return
            }
            if (info.file.status === 'done') {
                // console.log(info.file)
                info.file.url = info.file.response.path
                info.file.id = info.file.response.id
                emit('changeResponse', info.file.response)
                // console.log(info.file, info.file.response)
                // Get this url from response in real world.
                // emit('update:imageUrl', info.file.response.path)
                // imageUrl.value = info.file.response.path
            }
            if (info.file.status === 'error') {
                message.error('upload error')
            }
        }

        const beforeUpload = (file: FileItem) => {
            return new Promise((resolve, reject) => {
                // const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                // if (!isJpgOrPng) {
                //     message.error('请上传 JPG/PNG 格式图片!');
                //     return reject(false);
                // }
                // const isLt2M = (file?.size || 0) / 1024 / 1024 < 1024
                // if (!isLt2M) {
                //     message.error('请上传 ≤ 1GB 以内的文件')
                //     return reject(false)
                // }
                if (multiple.value == false && fileList.value.length > 0) {
                    message.error('只能上传一个文件!')
                    return reject(false)
                }
                return resolve(true)
            })
        }
        const deleteUrl = (file: FileItem, index: number) => {
            emit('delChange', file)
            fileList.value = fileList.value.filter((item) => {
                return file.uid != item.uid
            })
            return
        }
        const downUrl = (file: FileItem, index: number) => {
            downFile('get', file.url || file?.response?.fileUrl || '', file.name || '', {})
            // downFile('open',  {})
            return
        }
        const myPreviewFile = (file) => {
            previewFile(file.url || file?.response?.fileUrl || '')
        }
        return {
            action,
            headers,
            fileList,
            handleChange,
            beforeUpload,

            //方法
            getFileUrls,
            deleteUrl,
            downUrl,

            myPreviewFile,
        }
    },
})
</script>
<style scoped lang="less">
.img {
    width: 100%;
    height: 100%;
}
/* tile uploaded pictures */
.upload-list-inline :deep(.ant-upload-list-item) {
    float: left;
    width: 200px;
    margin-right: 8px;
}
.upload-list-inline :deep(.ant-upload-animate-enter) {
    animation-name: uploadAnimateInlineIn;
}
.upload-list-inline :deep(.ant-upload-animate-leave) {
    animation-name: uploadAnimateInlineOut;
}
:deep(.anticon.anticon-loading) {
    color: #fff;
}
.file-list-boxs {
    display: flex;
    flex-wrap: wrap;
    .file-list-box {
        padding: 10px;
        &:hover {
            background: #f5f5f5;
        }
        .file-list-name {
            width: 142px;
            height: 142px;
            background-color: #597ef7;
            text-align: center;
            // line-height: 142px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            .txt {
                color: #fff;
                width: 100px;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                overflow: hidden;
                -webkit-box-orient: vertical;
            }
        }
        .file-list-title {
            cursor: pointer;
            width: 142px;
            text-align: center;
            font-weight: 600;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            overflow: hidden;
            -webkit-box-orient: vertical;
        }
        .file-list-button {
            display: flex;
            justify-content: space-between;
        }
        // display: flex;
    }
}
.file-list-boxs1 {
    .file-list-box1 {
        display: flex;
        justify-content: space-between;
        max-width: 300px;
        line-height: 32px;
        &:hover {
            background: #f5f5f5;
        }
        .file-list-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: calc(100% - 80px);
        }
        .file-list-button {
            width: 80px;
            text-align: right;
            .See {
                width: 25px;
                line-height: 28px;
                color: #597ef7;
                vertical-align: inherit;
                &:hover {
                    background: rgba(89, 126, 247, 0.1);
                }
            }
            .Download {
                width: 25px;
                line-height: 28px;
                color: #597ef7;
                vertical-align: inherit;
                &:hover {
                    background: rgba(89, 126, 247, 0.1);
                }
            }
            .Dele {
                width: 25px;
                line-height: 28px;
                color: red;
                vertical-align: inherit;
                &:hover {
                    background: rgba(255, 0, 0, 0.1);
                }
            }
        }
    }
}
</style>
