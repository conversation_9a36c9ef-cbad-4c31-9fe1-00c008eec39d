<template>
    <div class="wrapper" ref="refTreeSelectBox">
        <TreeSelect
            ref="refTreeSelect"
            class="myTreeSelect"
            :value="dataValue"
            :tree-data="treeData"
            :treeCheckStrictly="true"
            :allow-clear="allowClear"
            showSearch
            :tree-checkable="multiple"
            :multiple="multiple"
            :maxTagCount="itemForm?.maxTag ? +itemForm?.maxTag : 0"
            treeNodeFilterProp="title"
            treeDefaultExpandAll
            :getPopupContainer="getPopupContainer"
            :show-checked-strategy="SHOW_ALL"
            :placeholder="
                itemForm.placeholder ||
                ((disabled ? disabled : itemForm.disabled) ? `暂无${itemForm.label}` : `请选择${itemForm.label}`)
            "
            :disabled="disabled ? disabled : itemForm.disabled"
            @change="changeEvent"
            @select="selectEvent"
            :autoClearSearchValue="false"
            :dropdownClassName="dropdownClassName"
            @focus="treeExpand"
            :filterTreeNode="filterTreeNode"
        />
        <Button @click="changeCheck" v-if="isAll && !disabled" style="margin-left: 8px">
            {{ isCheckedAll ? '取消选择' : '选择全部' }}
        </Button>
    </div>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, onMounted, watchEffect, nextTick, PropType } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { ArrToTree } from '/@/utils/index'
import { TreeSelect } from 'ant-design-vue'
export default defineComponent({
    name: 'ClientSelectTree',
    props: {
        allowClear: {
            type: Boolean,
            default: false,
        },
        isAll: {
            type: Boolean,
            default: false,
        },
        checkStrictly: {
            type: Boolean,
            default: true,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        value: {
            type: [Array, Object, String, Number],
            default: () => [],
        },
        selectData: {
            type: Array,
            default: () => [],
        },
        itemForm: {
            type: Object,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        defaultAll: {
            type: Boolean,
            default: false,
        },
        customDictionaryKey: { type: String },
        customApi: { type: String },
        requestMethod: { type: String, default: 'get' },
        requestParams: { type: [Object, String] as PropType<object | string>, default: '' },
        renderInBody: { type: Boolean, default: false },
    },
    emits: ['update:value', 'update:itemForm', 'change', 'labelChange','labelSelect'],
    setup(props, { emit }) {
        const { value, itemForm, multiple, checkStrictly, defaultAll } = toRefs<any>(props)
        const isCheckedAll = ref(false)
        const treeData = ref<TreeDataItem[]>([])
        const clientsData = ref<inObject>({})
        const data = ref<any[]>([])
        console.log(props.customApi)
        const refTreeSelectBox = ref()
        const refTreeSelect = ref()
        function randomString(e) {
            e = e || 32
            let t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
                a = t.length,
                n = ''
            for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
            return n
        }
        const dropdownClassName = ref('')
        onMounted(() => {
            dictionaryDataStore().setSelectclients(
                props.customDictionaryKey || 'clients',
                false,
                props.customApi || '/api/hr-selectclients',
                props.requestMethod,
                props.requestParams,
            )
            dropdownClassName.value = randomString(5)
        })

        const treeExpand = () => {
            setTimeout(() => {
                let dropdownClassNameDom: any = document.getElementsByClassName(dropdownClassName.value)[0]
                if (
                    dropdownClassNameDom?.offsetWidth + parseInt(dropdownClassNameDom?.style?.left) >
                    document.documentElement.offsetWidth
                ) {
                    let left =
                        parseInt(dropdownClassNameDom.style.left) +
                        refTreeSelectBox.value.offsetWidth -
                        dropdownClassNameDom.offsetWidth +
                        'px'
                    dropdownClassNameDom.style.left = left
                }
            }, 150)
        }

        watchEffect(() => {
            data.value = dictionaryDataStore().getDictionaryData[props.customDictionaryKey || 'clients'] || []
        })
        watch(
            data,
            () => {
                if (!data.value?.length) {
                    return
                }
                nextTick(() => {
                    itemForm.value.options = data.value
                    emit('update:itemForm', itemForm.value)
                    treeData.value = dictionaryDataStore().dictionaryTreeData
                    clientsData.value = dictionaryDataStore().dictionaryTreeObj

                    console.log('treeData', treeData.value, clientsData.value)
                    data.value?.forEach((item) => {
                        clientsData.value[item.id].labelList = getLabelList(item)
                    })
                })
                if (defaultAll.value) {
                    nextTick(() => {
                        setDataValue(Object.values(clientsData.value))
                        isCheckedAll.value = true
                    })
                }
            },
            { immediate: true },
        )
        function getLabelList(data) {
            // let level = 0
            // let v = this
            let labelList = [data.label]
            function loop(id) {
                let cData: any = clientsData.value[id]
                if (cData?.parentId == cData?.id) return
                if (cData?.parentId) {
                    labelList.push(cData.label)
                    loop(cData.parentId)
                }
            }
            loop(data.parentId)
            return labelList
        }

        const dataValue = computed<any>({
            get: () => {
                let data: any = value.value
                // if (multiple.value && checkStrictly.value) {
                //     data =
                //         value.value.map((item) => {
                //             return clientsData.value[item]
                //         }) || []
                // }
                if (multiple.value) {
                    data =
                        value.value.map((item) => {
                            return clientsData.value[item]
                        }) || []
                }
                return data
            },
            set: (val) => {
                // let data: any = val
                // // console.log(val, 955)
                // if (multiple.value) {
                //     data = val.map((item) => item.value)
                // }
                // // emit('update:value', !checkStrictly.value ? val : data)
                // // emit('change', !checkStrictly.value ? val : data)
                // emit('update:value', data)
                // emit('change', data)
                // if (itemForm.value?.onChange) {
                //     if (multiple.value) {
                //         itemForm.value?.onChange(data, val)
                //     } else {
                //         itemForm.value?.onChange(data, clientsData.value[val])
                //     }
                // }
            },
        })
        function getIdList(treeData) {
            let idList: any[] = []
            function loop(data) {
                data.forEach((item: any) => {
                    idList.push(item.id)
                    if ('children' in item) {
                        if (item.children.length > 0) {
                            loop(item.children)
                        }
                    }
                })
            }
            loop(treeData)
            return idList
        }
        const setDataValue = (val, label?, extra?) => {
            let data: any = val
            if (multiple.value) {
                data = val.map((item) => item.value)

                if (!checkStrictly.value) {
                    if (extra?.checked && extra?.triggerValue) {
                        data.push(...getIdList([clientsData.value[extra.triggerValue]]))
                        data = Array.from(new Set(data))
                    }
                }
            }
            emit('update:value', data)
            emit('change', data)
            if (itemForm.value?.onChange) {
                if (multiple.value) {
                    itemForm.value?.onChange(data, val)
                } else {
                    itemForm.value?.onChange(data, clientsData.value[val])
                }
            }
        }
        const changeCheck = () => {
            setDataValue(isCheckedAll.value ? [] : Object.values(clientsData.value))
            isCheckedAll.value = !isCheckedAll.value
        }
        const changeEvent = (value, label, extra) => {
            setDataValue(value, label, extra)
            if (multiple.value) {
                emit(
                    'labelChange',
                    value?.map((i) => i.label),
                )
            } else {
                emit('labelChange', label, value, extra)
            }
        }
        const selectEvent = (value, label, extra) => {
            emit('labelSelect', label, value, extra)
        }
        const filterTreeNode = (inputValue: string, treeNode) => {
            return !!clientsData.value[treeNode.key]?.labelList?.find((item) => {
                return item.indexOf(inputValue) != -1
            })
        }
        return {
            isCheckedAll,
            treeData,
            changeEvent,
            dataValue,
            changeCheck,
            getPopupContainer: (triggerNode) => {
                return props.renderInBody ? document.body : triggerNode
            },
            SHOW_ALL: TreeSelect.SHOW_ALL,

            refTreeSelectBox,
            refTreeSelect,
            dropdownClassName,
            treeExpand,
            filterTreeNode,

            selectEvent,
        }
    },
})
</script>
<style scoped lang="less">
:deep(.myTreeSelect) {
    width: 100% !important;
}
.wrapper {
    width: 100%;
    display: flex;
    align-items: center;
}
</style>
