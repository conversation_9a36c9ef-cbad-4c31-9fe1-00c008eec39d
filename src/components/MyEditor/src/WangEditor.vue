<template>
    <div class="editor-wrapper" :style="{ width: width, height: height }">
        <div :id="editorId"></div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, onMounted, onBeforeUnmount } from 'vue'
import Editor from 'wangeditor'
import appConfig from '/@/config'
let oneOf = (val, arr) => {
    return arr.some((el) => el == val)
}
export default defineComponent({
    name: 'WangEditor',
    props: {
        width: {
            type: String,
            default: '100%',
        },
        height: { type: String, default: '' },
        value: {
            type: String,
            default: '',
        },
        /**
         * 绑定的值的类型, enum: ['html', 'text']
         */
        valueType: {
            type: String,
            default: 'html',
            validator: (val) => {
                return oneOf(val, ['html', 'text'])
            },
        },
        /**
         * @description 设置change事件触发时间间隔
         */
        changeInterval: {
            type: Number,
            default: 200,
        },
        /**
         * @description 是否开启本地存储
         */
        cache: {
            type: Boolean,
            default: false,
        },
        editorConfig: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['input', 'on-change'],
    setup(props, { emit }) {
        const { value, cache, changeInterval, valueType, editorConfig } = toRefs<any>(props)
        let editor = ref<any>(null)
        const setHtml = (val) => {
            editor.value.txt.html(val)
        }
        function randomString(len) {
            len = len || 32
            var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            var maxPos = $chars.length
            var pwd = ''
            for (let i = 0; i < len; i++) {
                pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
            }
            return pwd
        }
        const editorId = `editor${randomString(5)}`

        onMounted(() => {
            editor.value = new Editor(`#${editorId}`)

            editor.value.config.onchange = (html) => {
                let text = editor.value.txt.text()
                if (cache.value) localStorage.editorCache = html
                emit('input', valueType.value === 'html' ? html : text)
                emit('on-change', html, text)
            }
            editor.value.config.onchangeTimeout = changeInterval.value
            editor.value.config.zIndex = 100
            editor.value.config.height = editorConfig.value?.height || 300
            if (editorConfig.value?.menus) editor.value.config.menus = editorConfig.value?.menus
            editor.value.config.showFullScreen = editorConfig.value?.showFullScreen ?? true
            editor.value.config.showLinkImg = editorConfig.value?.showLinkImg ?? true
            editor.value.config.pasteIgnoreImg = editorConfig.value?.pasteIgnoreImg ?? false
            editor.value.config.pasteTextHandle = editorConfig.value?.pasteTextHandle || undefined
            editor.value.config.pasteFilterStyle = editorConfig.value?.pasteFilterStyle ?? true
            editor.value.config.placeholder = editorConfig.value?.placeholder ?? '请输入正文'

            // create这个方法一定要在所有配置项之后调用
            editor.value.create()

            //baze64上传图片
            // this.editor.config.uploadImgShowBase64 = true

            editor.value.config.uploadImgHeaders = {
                Authorization: 'Bearer ' + (localStorage.token || '').trim().replace(/^Bearer\s+/, ''), // 设置请求头
            }
            editor.value.config.uploadImgServer = appConfig.baseUrl + '/api/hr-appendixes/upload-single-file'
            editor.value.config.uploadFileName = 'file'

            editor.value.config.uploadImgHooks.customInsert = function (insertImgFn, result) {
                // result 即服务端返回的接口
                console.log('result', result)

                // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
                insertImgFn(result.fileUrl)
            }

            // Editor
            // 如果本地有存储加载本地存储内容
            let html = value.value || localStorage.editorCache
            if (html) editor.value.txt.html(html)
        })
        onBeforeUnmount(() => {
            // 销毁编辑器
            editor.value.destroy()
            editor.value = null
        })
        // 禁用编辑器
        const disable = () => {
            editor.value.disable()
        }

        // 解除禁用
        const enable = () => {
            editor.value.enable()
        }

        // 获取Editor内容
        const getEditorContent = (type) => {
            if (type == 'html') {
                return editor.value.txt.html()
            } else return editor.value.txt.text()
        }

        return {
            editor,
            setHtml,
            editorId,
            disable,
            enable,
            getEditorContent,
        }
    },
})
</script>

<style scoped lang="less">
.editor-wrapper {
    margin-top: 15px;
}
</style>
