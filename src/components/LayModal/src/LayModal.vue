<template>
    <lay-layer
        v-model="visible"
        v-bind="$attrs"
        :title="title"
        :move="true"
        :shade="false"
        :shadeClose="false"
        :area="[width, height]"
    >
        <div class="content">
            <slot></slot>
        </div>
        <div class="footer">
            <slot name="footer">
                <Button @click="cancelHandle" class="btn" :style="cancelStyle">{{ cancelText }}</Button>
                <Button @click="confirmHandle" type="primary" class="btn" :style="okStyle">{{ okText }}</Button>
            </slot>
        </div>
    </lay-layer>
</template>
<script lang="ts" setup>
const props = defineProps({
    width: {
        type: [String, Number],
        default: '600px',
    },
    height: {
        type: [String, Number],
        default: '600px',
    },
    visible: {
        type: Boolean,
        default: false,
    },
    title: String,
    okText: {
        type: String,
        default: '确定',
    },
    cancelText: {
        type: String,
        default: '取消',
    },
    cancelStyle: {
        type: Object,
        default: () => {},
    },
    okStyle: {
        type: Object,
        default: () => {},
    },
})
const emit = defineEmits(['confirm', 'cancel', 'ok'])
// confirm handle
const confirmHandle = () => {
    // 表单关闭后的其它操作 如刷新表
    emit('confirm')
    emit('ok')
}
// cancel handle
const cancelHandle = () => {
    emit('cancel')
}
</script>
<style lang="less" scoped>
.layui-layer-content {
    overflow: hidden;
}
.content {
    padding: 24px;
    overflow: auto;
    box-sizing: border-box;
    height: calc(100% - 53px);
}
.footer {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px 16px;
    text-align: right;
    background: transparent;
    border-top: 1px solid #f0f0f0;
    :deep(button + button) {
        margin-left: 15px;
    }
}
</style>
