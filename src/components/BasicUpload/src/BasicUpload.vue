<template>
    <div class="basicUpload">
        <Upload :showUploadList="false" :beforeUpload="beforeUpload">
            <div class="item uploadItem">
                <CloudUploadOutlined />
                点击上传
            </div>
        </Upload>
        <div class="item" v-for="(i, idx) in dataList" :key="i.id">
            <img v-if="['.jpg', '.png', '.jpeg'].includes(i.fileType)" class="img" :src="i.fileUrl" alt="" />
            <div v-else class="txt">{{ i.originName }}</div>
            <div class="cover">
                <div @click="previewFile(i.fileUrl)"></div>
                <div @click="removeFile(idx)"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType, toRefs } from 'vue'
import { Upload } from 'ant-design-vue'
import { CloudUploadOutlined } from '@ant-design/icons-vue'
import { FileItem } from '/#/component'
import { uploadRecordFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'

export default defineComponent({
    name: 'BasicUpload',
    components: { CloudUploadOutlined, Upload },
    props: {
        type: {
            type: String,
            default: 'single',
        },
        dataList: {
            type: Array as PropType<FileItem[]>,
            default: () => [],
        },
    },
    emits: ['upload:dataList'],
    setup(props, { emit }) {
        const { dataList } = toRefs(props)

        const beforeUpload = async (file) => {
            const res = await uploadRecordFile(file)
            emit('upload:dataList', [...dataList.value, res])
            return false
        }

        const removeFile = (index) => {
            emit('upload:dataList', [...dataList.value.splice(index, 1)])
        }

        return {
            removeFile,
            previewFile,
            beforeUpload,
        }
    },
})
</script>

<style scoped lang="less"></style>
