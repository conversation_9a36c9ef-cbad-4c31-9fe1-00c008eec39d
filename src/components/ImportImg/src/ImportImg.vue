<template>
    <div>
        <Upload
            v-model:file-list="fileList"
            name="file"
            list-type="picture-card"
            class="avatar-uploader"
            :show-upload-list="false"
            :action="action"
            :before-upload="beforeUpload"
            @change="handleChange"
            :headers="headers"
            :accept="accept"
            :disabled="disabled"
        >
            <div class="imageBox">
                <loading-outlined v-if="loading" />
                <template v-if="imageUrl">
                    <img :src="imageUrl" class="image" alt="avatar" />
                </template>
                <template v-else>
                    <template v-if="uploadText">
                        <!--                        <plus-outlined v-if="!loading"></plus-outlined>-->
                        <div class="ant-upload-text">点击替换</div>
                    </template>
                    <template v-else>
                        <plus-outlined v-if="!loading" />
                        <div class="ant-upload-text">Upload</div>
                    </template>
                </template>
            </div>
        </Upload>
    </div>
</template>
<script lang="ts">
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { defineComponent, ref, toRefs } from 'vue'
import appConfig from '/@/config'
interface FileItem {
    uid: string
    name?: string
    status?: string
    response?: any
    url?: string
    type?: string
    size: number
    originFileObj: any
}

interface FileInfo {
    file: FileItem
    fileList: FileItem[]
}

export default defineComponent({
    name: 'ImportImg',
    components: {
        LoadingOutlined,
        PlusOutlined,
    },
    props: {
        imageUrl: {
            type: String,
            default: '',
        },
        uploadText: {
            type: String,
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        accept: {
            type: String,
            default: '',
        },
        size: {
            type: Number,
            default: 10,
        },
        limitWH: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['update:imageUrl', 'fetchImgInfo'],
    setup(props, { emit }) {
        const { size, limitWH } = toRefs(props)
        const action = appConfig.baseUrl + '/api/hr-appendixes/upload-single-file'
        const headers = {
            Authorization: 'Bearer ' + (localStorage.token || '').trim().replace(/^Bearer\s+/, ''),
            isEncrypt: 'no',
        }
        console.log(headers)
        const fileList = ref([])
        const loading = ref<boolean>(false)
        // const imageUrl = ref<string>('')

        const handleChange = (info: FileInfo) => {
            console.log(info)
            if (info.file.status === 'uploading') {
                loading.value = true
                return
            }
            if (info.file.status === 'done') {
                // Get this url from response in real world.
                emit('update:imageUrl', info.file.response.fileUrl)
                emit('fetchImgInfo', info.file.response)
                // imageUrl.value = info.file.response.path
                loading.value = false
            }
            if (info.file.status === 'error') {
                loading.value = false
                message.error('upload error')
            }
        }
        const getImgWidth = (file) => {
            return new Promise((resolve) => {
                let url = window.URL || window.webkitURL
                let img = new Image() //手动创建一个Image对象
                img.src = url.createObjectURL(file) //创建Image的对象的url
                img.onload = function () {
                    resolve(img)
                }
            })
        }

        const beforeUpload = (file: FileItem) => {
            return new Promise(async (resolve, reject) => {
                const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
                if (!isJpgOrPng) {
                    message.error('请上传正确的图片格式!')
                    reject(false)
                }

                const isLtSize = file.size / 1024 / 1024 < size.value
                if (!isLtSize) {
                    message.error(`图片不能大于${size.value}MB!`)
                    reject(false)
                }

                if (limitWH.value?.width) {
                    let img: any = await getImgWidth(file)
                    if (limitWH.value?.width != img.width) {
                        message.error('您上传的图片尺寸不符合要求，请重新上传!')
                        reject(false)
                    } else if (limitWH.value?.height != img.height) {
                        message.error('您上传的图片尺寸不符合要求，请重新上传!')
                        reject(false)
                    }
                }
                if (loading.value == true) {
                    message.error('上一张图片未上传完成，请稍等!')
                    reject(false)
                }
                resolve(true)
            })
        }

        return {
            action,

            fileList,
            loading,
            // imageUrl,
            handleChange,
            beforeUpload,
            headers,
        }
    },
})
</script>
<style scoped lang="less">
.avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
}
.ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
}
.imageBox {
    width: 128px;
    height: 128px;
    position: relative;
    :deep(.anticon) {
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: @primary-color;
    }
    :deep(.ant-upload-text) {
        position: absolute;
        top: 55%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .image {
        width: 128px;
        height: 128px;
    }
}
</style>
