<template>
    <span class="wrapper">
        <InputNumber
            class="input"
            :value="value[0]"
            :placeholder="item.label"
            :min="item.canNegative ? undefined : 0"
            v-show="item.show === false ? false : true"
            :size="item.size"
            @change="inputHandle($event, 0)"
            @pressEnter="inputChange($event, item, 0)"
            @blur="inputChange($event, item, 0)"
            :formatter="item.integerOnly ? (value) => limitNumber(value) : item.formatter"
            :parser="item.integerOnly ? (value) => limitNumber(value) : item.parser"
        />
        <span class="symbol">~</span>
        <InputNumber
            class="input"
            :value="value[1]"
            :placeholder="item.label"
            :max="item.max"
            :min="item.canNegative ? undefined : 0"
            v-show="item.show === false ? false : true"
            :size="item.size"
            @change="inputHandle($event, 1)"
            @pressEnter="inputChange($event, item, 1)"
            @blur="inputChange($event, item, 1)"
            :formatter="item.integerOnly ? (value) => limitNumber(value) : item.formatter"
            :parser="item.integerOnly ? (value) => limitNumber(value) : item.parser"
        />
    </span>
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue'
export default defineComponent({
    name: 'NumberRange',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['update:value', 'change'],
    setup(props, { emit }) {
        const { value } = toRefs<any>(props)

        const inputHandle = (e, index) => {
            let newVal = [...value.value]
            if (newVal.length < 2) newVal = [!e && e !== 0 ? undefined : Number(e), !e && e !== 0 ? undefined : Number(e)]
            else newVal = compareValue(newVal, Number(e), index)
            emit(
                'update:value',
                newVal.filter((el) => {
                    return el || el == 0
                }),
            )
        }

        const inputChange = (e, item, index) => {
            let newVal = [...value.value]
            if (newVal.length < 2)
                newVal = [
                    !e.target.value && e.target.value !== 0 ? undefined : Number(e.target.value),
                    !e.target.value && e.target.value !== 0 ? undefined : Number(e.target.value),
                ]
            else newVal = compareValue(newVal, Number(e.target.value), index)
            emit(
                'change',
                newVal.filter((el) => {
                    return el || el == 0
                }),
                item,
            )
        }

        const compareValue = (value, e, index) => {
            if (index == 0 && e > value[1]) {
                value[0] = value[1]
            } else if (index == 1 && e < value[0]) {
                value[1] = value[0]
            } else value[index] = Number(e)

            return value
        }

        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '0') : '0'
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '0') : '0'
            } else {
                return '0'
            }
        }

        return {
            inputHandle,
            inputChange,
            limitNumber,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    margin-right: 10px;
    .input {
        width: 150px;
    }
    .symbol {
        margin: 0 5px;
    }
}
</style>
