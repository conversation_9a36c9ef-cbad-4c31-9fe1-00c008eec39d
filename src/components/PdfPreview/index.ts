import PdfPreview from './src/PdfPreview.vue'
import PreviewFile from './src/PreviewFile.vue'

import { createVNode, render } from 'vue'

interface PreviewConfig {
    urls: string[]
    index?: Number
    cancel?: Function
}
export function createPreview({ urls, index, cancel }: PreviewConfig) {
    const container = document.createElement('div')
    const vm = createVNode(PreviewFile, {
        urls,
        index,
        cancel,
        onCancel: () => {
            document.body.removeChild(container)
        },
    })
    render(vm, container)
    document.body.append(container)
}

export default PdfPreview
