<template>
    <section class="previewFileModal">
        <header class="header">
            <div class="left">
                {{ currentFile.name }}
            </div>
            <div class="right">
                <RedoOutlined />
                <VerticalAlignBottomOutlined @click="download" title="下载" />
                <CloseOutlined @click="cancel" title="关闭" />
            </div>
        </header>
        <main class="main">
            <img
                v-if="['png', 'jpg', 'jpeg', 'svg'].includes(currentFile.type)"
                :src="urls[currentIndex]"
                alt=""
                style="max-width: 100%; max-height: 100%"
            />
            <iframe
                v-else-if="['xlsx', 'xls', 'doc', 'docx', 'csv', 'ppt', 'pptx'].includes(currentFile.type)"
                :src="prefix + encode(urls[currentIndex])"
                frameborder="0"
            ></iframe>
            <iframe v-else :src="urls[currentIndex]" frameborder="0"></iframe>
        </main>
    </section>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, ref } from 'vue'
import downFile from '/@/utils/downFile'
import { VerticalAlignBottomOutlined, CloseOutlined, RedoOutlined } from '@ant-design/icons-vue'
import config from '/@/config'
import { Base64 } from 'js-base64'

export default defineComponent({
    name: 'PreviewFile',
    components: { VerticalAlignBottomOutlined, CloseOutlined, RedoOutlined },
    props: {
        urls: {
            type: Array as PropType<string[]>,
            default: () => [],
            required: true,
        },
        index: {
            type: Number,
            default: 0,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        // TODO 做成可切换的列表
        const currentIndex = ref(props.index)
        const cancel = () => {
            emit('cancel')
        }
        const download = () => {
            downFile('get', props.urls[currentIndex.value], currentFile.value.name)
        }

        const currentFile = computed(() => {
            const s = props.urls[currentIndex.value].split('.')
            return {
                name: props.urls[currentIndex.value].split('/').slice(-1)[0],
                type: s[s.length - 1],
            }
        })

        // @ts-ignore
        const encode = (url) => Base64.encode(url)

        return {
            prefix: config.prefix,
            encode,
            currentFile,
            currentIndex,
            cancel,
            download,
            microsoftOfficePrefix: `https://view.officeapps.live.com/op/view.aspx?src=`,
        }
    },
})
</script>

<style scoped lang="less">
.previewFileModal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2001;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    .header {
        width: 100%;
        height: 50px;
        background: #000000;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            padding-left: 10px;
            width: 50%;
        }
        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            & > * {
                width: 50px;
                height: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 21px;
                &:hover {
                    background: #444;
                }
            }
        }
    }
    .main {
        width: 100%;
        flex: 1;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        iframe {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
