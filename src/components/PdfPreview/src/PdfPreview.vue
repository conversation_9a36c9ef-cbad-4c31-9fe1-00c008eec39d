<template>
    <div>
        <iframe name="iframe" :src="pdfUrls" frameborder="0" style="width: 100%; height: 100%"></iframe>
    </div>
</template>
<script lang="ts">
import { defineComponent, toRefs, computed, onMounted } from 'vue'

export default defineComponent({
    name: 'PdfPreview',
    props: {
        pdfUrl: String,
        urlParams: {
            type: Object,
            default: () => {},
        },
        windowParams: {
            type: Object,
            default: () => {},
        },
    },
    setup(props, { emit }) {
        const getParams = (params: any) => {
            let body = ''
            if (params) {
                body = ''
                for (const key in params) {
                    if (typeof params[key] != undefined) {
                        body += '&' + key + '=' + params[key]
                    } else {
                        body += '&' + key + '='
                    }
                }
            }
            return body
        }
        const { pdfUrl, urlParams, windowParams } = toRefs<any>(props)
        const pdfUrls = computed(() => {
            return './pdfjs/web/viewer.html?file=' + encodeURIComponent(pdfUrl.value) + getParams(urlParams.value)
        })
        onMounted(() => {
            console.log((window.frames['iframe'].windowParams = windowParams.value))
        })

        return { pdfUrls }
    },
})
</script>
