<template>
    <div class="globalLoding" v-show="spinning">
        <Spin class="mySpin" size="large" tip="Loading..." />
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import { Spin } from 'ant-design-vue'
import globalLodingStore from '/@/store/modules/globalLoding'
export default defineComponent({
    name: 'GlobalLoding',
    components: { Spin },
    props: {},
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
        const spinning = computed(() => {
            return globalLodingStore().getGlobalLodingShow
        })
        return {
            spinning: spinning,
        }
    },
})
</script>
<style scoped lang="less">
.globalLoding {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1000000;
    .mySpin {
        position: relative;
        left: 50vw;
        top: 50vh;
    }
}
</style>
