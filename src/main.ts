import '/@/styles/index.less'

import { createApp } from 'vue'
import App from './App.vue'
import { setupStore } from './store'
import router, { setupRouter } from './router'
import { setupRouterGuard } from './router/guide'
import { registerGlobComp } from './utils/component'
import setupDirective from '/@/utils/directive'

async function bootstrap() {
    const app = createApp(App)
    // 注册路由
    setupRouter(app)
    // 路由守卫
    setupRouterGuard(router)
    // 全局组件
    registerGlobComp(app)
    // 仓库
    setupStore(app)
    // customize directive
    setupDirective(app)
    // router ready
    await router.isReady()
    // mount
    app.mount('#app')
}

bootstrap()
