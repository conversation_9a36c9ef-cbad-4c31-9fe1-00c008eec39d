<template>
    <div id="root" class="root">
        <ConfigProvider :locale="zhCN" :getPopupContainer="getPopupContainer">
            <router-view />
            <GlobalLoding />
        </ConfigProvider>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import moment from 'moment'
import { ConfigProvider } from 'ant-design-vue'
import GlobalLoding from './components/GlobalLoding'
import 'moment/dist/locale/zh-cn'
moment.locale('cn')

export default defineComponent({
    name: 'App',
    components: { ConfigProvider, GlobalLoding },
    setup() {
        const getPopupContainer = (node) => {
            return node && node.parentNode ? node.parentNode : document.body
        }
        return { zhCN, getPopupContainer }
    },
})
</script>

<style scoped>
.root {
    width: 100vw;
    height: 100vh;
}
</style>
