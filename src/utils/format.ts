import { RuleObject } from 'ant-design-vue/es/form/interface'
const regId = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
const email = /^(\w+\.?)*\w+@(?:\w+\.)\w+$/
// const tel = /^1[345789]\d{9}$/
const tel = /^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
// const fax = /^(\d{3,4}-)?\d{7,8}$/
// const proportion = /^100[%]$|^[*********][0-9][%]$|^[0-9][%]$|[0-9]+\.[0-9]{1,2}[%]$|100[%]$/
const proportion = /^0(\.\d{1,2})?[%]$|^[1-9](\.\d{1,2})?[%]$|^[1-9]\d(\.\d{1,2})?[%]$|100[%]$/
const height = /^([1,2][0-9]{2})$|^([1,2][0-9][0-9](\.\d{1,2}))$/
const account = /^[0-9a-zA-Z]{1,}$/
const userName = /^[0-9a-zA-Z]{1,}$/

// 校验数学公式是否正确
export function checkFormula(rule, value, callback?) {
    const regex1 = /\((.+)\)/g
    // 错误情况，空字符串
    if (!value || value == '') {
        return Promise.reject('请将公式填写完整')
    }

    // 错误情况，运算符连续
    if (/[\+\-\*\/]{2,}/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 空括号
    if (/\(\)/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 错误情况，(后面是运算符
    if (/\([\+\-\*\/]/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 错误情况，最后一位是运算符
    if (/[\+\-\*\/]$/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 错误情况，)前面是运算符
    if (/[\+\-\*\/]\)/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 错误情况，(前面不是运算符
    if (/[^\+\-\*\/]\(/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    // 错误情况，)后面不是运算符
    if (/\)[^\+\-\*\/]/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    /* // 错误情况，%开头 %前是运算符
    if (/^\%/.test(value) || /[\+\-\*\/\(\)]\%/.test(value)) {
        return Promise.reject('请输入正确的公式')
    } */

    // 错误情况，小数点前后是运算符
    if (/^\./.test(value) || /\.$/.test(value) || /[\+\-\*\/\(\)]\./.test(value) || /\.[\+\-\*\/\(\)]/.test(value)) {
        return Promise.reject('请输入正确的公式')
    }

    if (value.match(regex1)) {
        const newStr = value.match(regex1)[0].slice(1, -1)
        if (!/[\+\-\*\/]/.test(newStr)) return Promise.reject('请输入正确的公式')
    }

    /* // 错误情况
    let flag = false
    flag = loopTest(value)
    function loopTest(str) {
        console.log(str.match(regex1))
        if (str.match(regex1)) {
            const newStr = str.match(regex1)[0].slice(1, -1)
            // loopTest(newStr)
            return /[\+\-\*\/]/.test(newStr)
        }
    }
    if (!flag) return Promise.reject('请输入正确的公式')
 */
    // 错误情况，括号不配对
    const stack: any = []
    for (let i = 0, item; i < value.length; i++) {
        item = value.charAt(i)
        if ('(' === item) {
            stack.push('(')
        } else if (')' === item) {
            if (stack.length > 0) {
                stack.pop()
            } else {
                return Promise.reject('请输入正确的公式')
            }
        }
    }
    if (0 !== stack.length) {
        return Promise.reject('请输入正确的公式')
    }

    return callback ? callback() : Promise.resolve()
}

// 邮箱的验证规则
export function validateEmail(rule, value, callback) {
    if (!value) {
        return callback(new Error('邮箱不能为空'))
    }
    if (!email.test(value)) {
        callback(new Error('请输入正确的邮箱!'))
    } else {
        callback()
    }
}

// 身份证的验证规则
export function validateID(rule, value, callback) {
    if (!value) {
        return callback(new Error('身份证不能为空'))
    }
    if (!regId.test(value)) {
        callback(new Error('请输入正确的二代身份证号码'))
    } else {
        callback()
    }
}

// 电话号码的验证
export async function validatePhone(rule: RuleObject, value, callback?) {
    if (rule.required) {
        if (!value) {
            return Promise.reject('请输入手机号')
        }
    }
    if (value) {
        if (!tel.test(value)) {
            return Promise.reject('请正确填写手机号')
        } else {
            return callback ? callback() : Promise.resolve()
        }
    }
}

// 设置身份证号的验证规则
export async function idCardValidity(rule, code, callback?) {
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入身份证号')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
    const city = {
        11: '北京',
        12: '天津',
        13: '河北',
        14: '山西',
        15: '内蒙古',
        21: '辽宁',
        22: '吉林',
        23: '黑龙江 ',
        31: '上海',
        32: '江苏',
        33: '浙江',
        34: '安徽',
        35: '福建',
        36: '江西',
        37: '山东',
        41: '河南',
        42: '湖北 ',
        43: '湖南',
        44: '广东',
        45: '广西',
        46: '海南',
        50: '重庆',
        51: '四川',
        52: '贵州',
        53: '云南',
        54: '西藏 ',
        61: '陕西',
        62: '甘肃',
        63: '青海',
        64: '宁夏',
        65: '新疆',
        71: '台湾',
        81: '香港',
        82: '澳门',
        91: '国外 ',
    }
    let tip = ''
    let pass = true
    if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
        tip = '身份证号格式错误'
        pass = false
    } else if (!city[code.substr(0, 2)]) {
        // tip = "地址编码错误"
        tip = '身份证输入错误'
        pass = false
    } else {
        // 18位身份证需要验证最后一位校验位
        if (code.length === 18) {
            code = code.split('')
            // ∑(ai×Wi)(mod 11)
            // 加权因子
            const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
            // 校验位
            const parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
            let sum = 0
            let ai = 0
            let wi = 0
            for (let i = 0; i < 17; i++) {
                ai = code[i]
                wi = factor[i]
                sum += ai * wi
            }
            // const last = parity[sum % 11]
            if (parity[sum % 11] != code[17]) {
                // tip = "校验位错误"
                tip = '身份证输入错误'
                pass = false
            }
        }
    }
    if (!pass) {
        return Promise.reject(new Error(tip))
    } else {
        return callback ? callback() : Promise.resolve()
    }
}

// 设置士官证的校验规则
export async function officerCardValidity(rule, code) {
    // 规则： 军/兵/士/文/职/广/（其他中文） + "字第" + 4到8位字母或数字 + "号"
    // 样本： 军字第2001988号, 士字第P011816X号
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入士官证号')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
    const reg = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/
    if (reg.test(code) === false) {
        return Promise.reject('士官证号不合规')
    } else {
        return Promise.resolve()
    }
}

// 设置学生证的校验规则
export async function studentCardValidity(rule, code) {
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入学生证号')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
}

// 设置驾驶证的校验规则
export async function driverCardValidity(rule, code) {
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入驾驶证号')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
    const reg = /^[1-8]\d{11}$/
    if (reg.test(code) === false) {
        return Promise.reject('驾驶证号不合规')
    } else {
        return Promise.resolve()
    }
}

// 设置护照的校验规则
export async function passPortCardValidity(rule, code) {
    // 规则： 1.普通护照：E+8 位数字编号

    // 2.公务电子护照，公务护照又分为公务、公务普通和外交护照三个类别

    // 公务护照：SE+7 位数字编码

    // 外交护照：DE+7 位数字编码

    // 公务普通护照：PE+7 位数字编码

    // 3.澳门特别行政区护照：MA+7 位编号

    // 4.香港特别行政区护照：K+8 位编号
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入护照号码')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
    const reg = /^([EK]\d{8}|(SE|DE|PE|MA)\d{7})$/
    if (reg.test(code) === false) {
        return Promise.reject('护照号码不合规')
    } else {
        return Promise.resolve()
    }
}

// 设置港澳通行证的校验规则
export async function HKCardValidity(rule, code) {
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入港澳通行证号码')
        }
    }
    if (!code) {
        return Promise.resolve()
    }
}

//比例校验
export async function validateProportion(rule: RuleObject, value) {
    if (!value) {
        return Promise.reject('请输入比例')
    }
    if (!proportion.test(value)) {
        return Promise.reject('请正确填写比例')
    } else {
        return Promise.resolve()
    }
}

//身高校验
export async function validateHeight(rule: RuleObject, value) {
    if (!value) {
        return Promise.resolve()
    }
    if (!height.test(value)) {
        return Promise.reject('请正确填写身高')
    } else {
        return Promise.resolve()
    }
}

//账号校验
export async function validateAccount(rule: RuleObject, value) {
    // if (!value) {
    //     return Promise.reject('请输入账号')
    // }
    if (!account.test(value)) {
        return Promise.reject('请正确填写账号')
    } else {
        return Promise.resolve()
    }
}

//用户名校验
export async function validateUserName(rule: RuleObject, value) {
    if (!value) {
        return Promise.reject('请输入用户名')
    }
    if (!userName.test(value)) {
        return Promise.reject('请正确填写用户名')
    } else {
        return Promise.resolve()
    }
}

// 银行卡号校验
export const bankCardNoValidity = async (rule, code, callback?) => {
    //银行卡号Luhn校验算法
    //luhn校验规则：16位银行卡号（19位通用）:
    //1.将未带校验位的 15（或18）位卡号从右依次编号 1 到 15（18），位于奇数位号上的数字乘以 2。
    //2.将奇位乘积的个十位全部相加，再加上所有偶数位上的数字。
    //3.将加法和加上校验位能被 10 整除。

    //bankno为银行卡号
    function luhnCheck(bankno) {
        const lastNum = bankno.substr(bankno.length - 1, 1) //取出最后一位（与luhn进行比较）

        const first15Num = bankno.substr(0, bankno.length - 1) //前15或18位
        const newArr: inObject = []
        for (let i = first15Num.length - 1; i > -1; i--) {
            //前15或18位倒序存进数组
            newArr.push(first15Num.substr(i, 1))
        }
        const arrJiShu: inObject = [] //奇数位*2的积 <9
        const arrJiShu2: inObject = [] //奇数位*2的积 >9

        const arrOuShu: inObject = [] //偶数位数组
        for (let j = 0; j < newArr.length; j++) {
            if ((j + 1) % 2 == 1) {
                //奇数位
                if (parseInt(newArr[j]) * 2 < 9) arrJiShu.push(parseInt(newArr[j]) * 2)
                else arrJiShu2.push(parseInt(newArr[j]) * 2)
            } //偶数位
            else arrOuShu.push(newArr[j])
        }

        const jishu_child1: inObject = [] //奇数位*2 >9 的分割之后的数组个位数
        const jishu_child2: inObject = [] //奇数位*2 >9 的分割之后的数组十位数
        for (let h = 0; h < arrJiShu2.length; h++) {
            jishu_child1.push(parseInt(arrJiShu2[h]) % 10)
            jishu_child2.push(parseInt(arrJiShu2[h]) / 10)
        }

        let sumJiShu = 0 //奇数位*2 < 9 的数组之和
        let sumOuShu = 0 //偶数位数组之和
        let sumJiShuChild1 = 0 //奇数位*2 >9 的分割之后的数组个位数之和
        let sumJiShuChild2 = 0 //奇数位*2 >9 的分割之后的数组十位数之和
        let sumTotal = 0
        for (let m = 0; m < arrJiShu.length; m++) {
            sumJiShu = sumJiShu + parseInt(arrJiShu[m])
        }

        for (let n = 0; n < arrOuShu.length; n++) {
            sumOuShu = sumOuShu + parseInt(arrOuShu[n])
        }

        for (let p = 0; p < jishu_child1.length; p++) {
            sumJiShuChild1 = sumJiShuChild1 + parseInt(jishu_child1[p])
            sumJiShuChild2 = sumJiShuChild2 + parseInt(jishu_child2[p])
        }
        //计算总和
        sumTotal =
            parseInt(String(sumJiShu)) +
            parseInt(String(sumOuShu)) +
            parseInt(String(sumJiShuChild1)) +
            parseInt(String(sumJiShuChild2))

        //计算luhn值
        const k = parseInt(String(sumTotal)) % 10 == 0 ? 10 : parseInt(String(sumTotal)) % 10
        const luhn = 10 - k

        if (lastNum == luhn) {
            return callback ? callback() : Promise.resolve()
        } else {
            return Promise.reject('银行卡号必须符合规范')
        }
    }

    const bankno = code.replace(/\s/g, '')
    if (rule.required) {
        if (!code) {
            return Promise.reject('请输入银行卡号')
        }
    }
    if (!code) {
        return callback ? callback() : Promise.resolve()
    }
    if (bankno.length < 16 || bankno.length > 19) {
        return Promise.reject('银行卡号长度必须在16到19之间')
    }
    const num = /^\d*$/ //全数字
    if (!num.exec(bankno)) {
        return Promise.reject('银行卡号必须全为数字')
    }
    //开头6位
    const strBin = '10,18,30,35,37,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,58,60,62,65,68,69,84,87,88,94,95,98,99'
    if (strBin.indexOf(bankno.substring(0, 2)) == -1) {
        return Promise.reject('银行卡号开头6位不符合规范')
    }
    //Luhn校验
    luhnCheck(bankno)
}
