import { App, Directive, DirectiveBinding } from 'vue'
import { useAuth } from './hooks'

export const isAuth = (key) => useAuth.value.buttons.find((i) => i == key)

// 使用 v-auth="'btnKey'"
const authDirective: Directive = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        !isAuth(binding.value) && el.parentNode?.removeChild(el)
    },
    // updated(el: HTMLElement, binding: DirectiveBinding) {},
}

const setupDirective = (app: App) => {
    app.directive('auth', authDirective)
}

export default setupDirective
