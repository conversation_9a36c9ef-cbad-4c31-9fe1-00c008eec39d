// const CryptoJS = require('crypto-js') //引用AES源码js
import CryptoJS from 'crypto-js'
const key = CryptoJS.enc.Utf8.parse('UMartaxiun35huyC') //十六位十六进制数作为密钥

//解密方法
export function Decrypt(word) {
    const decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    try {
        return JSON.parse(decryptedStr)
    } catch (error) {
        console.log(error)
        return decryptedStr
    }
}

//加密方法
export function Encrypt(word) {
    const srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(word))
    const encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
    const encryptedHexStr = CryptoJS.enc.Hex.parse(encrypted.ciphertext.toString().toUpperCase())
    return CryptoJS.enc.Base64.stringify(encryptedHexStr)
}
export default {
    Decrypt,
    Encrypt,
}
