import type { App } from 'vue'
import {
    Button,
    Input,
    InputNumber,
    DatePicker,
    RangePicker,
    Checkbox,
    CheckboxGroup,
    Select,
    Switch,
    Modal,
    Radio,
    RadioGroup,
    RadioButton,
    Table,
    Form,
    Textarea,
    Tree,
    InputSearch,
    Row,
    Col,
    Upload,
    Divider,
    Tabs,
    TabPane,
    Steps,
    Step,
    Cascader,
    TreeSelect,
    TimePicker,
    Progress,
    Dropdown,
    Menu,
    MenuItem,
    Tooltip,
    MonthPicker,
    Alert,
} from 'ant-design-vue'

import STable from '@surely-vue/table'
import layer from '@layui/layer-vue'
import '@layui/layer-vue/lib/index.css'
import BasicTable from '/@/components/BasicTable'
import EditModal from '/@/components/EditModal'
import EditModalSlot from '/@/components/EditModalSlot'
import LayModal from '/@/components/LayModal'
import { SearchBar, SearchItem } from '/@/components/SearchBar'
import MyFormItem from '/@/components/MyFormItem'
import ImportModal from '/@/components/ImportModal'
import ImportImg from '/@/components/ImportImg'
import ImportFile from '/@/components/ImportFile'
import SelectDic from '/@/components/SelectDic'
import ImgModal from '/@/components/ImgModal'
import PdfPreview from '/@/components/PdfPreview'
import MyOperation from '/@/components/MyOperation'
import WangEditor from '/@/components/MyEditor'
import { BasicUpload } from '/@/components/BasicUpload'
import ClientSelectTree from '/@/components/ClientSelectTree'
import EmployeeMatching from '/@/components/EmployeeMatching'
import NumberRange from '/@/components/NumberRange'
import StationCascader from '/@/components/StationCascader'
// 注册全局组件
export function registerGlobComp(app: App) {
    app.use(STable)
    app.use(layer)
    const antdCompList = [
        Button,
        Input,
        InputNumber,
        DatePicker,
        RangePicker,
        Checkbox,
        CheckboxGroup,
        Select,
        Switch,
        Modal,
        Radio,
        RadioGroup,
        RadioButton,
        Table,
        Form,
        Form.Item,
        Textarea,
        Tree,
        InputSearch,
        Row,
        Col,
        Upload,
        Divider,
        TabPane,
        Tabs,
        Steps,
        Step,
        Cascader,
        TreeSelect,
        TimePicker,
        Progress,
        Dropdown,
        Menu,
        MenuItem,
        Tooltip,
        MonthPicker,
        Alert,
    ]

    antdCompList.forEach((comp) => {
        console.log('com', comp)
        if (comp.name[0] == 'A') {
            app.component(comp.name.slice(1), comp)
        } else {
            app.component(comp.name, comp)
        }
    })

    app.component('MyOperation', MyOperation)

    app.component('BasicTable', BasicTable)
    app.component('BasicEditModal', EditModal)
    app.component('BasicEditModalSlot', EditModalSlot)
    app.component('LayModal', LayModal)
    app.component('WangEditor', WangEditor)
    app.component('SearchBar', SearchBar)
    app.component('SearchItem', SearchItem)
    app.component('MyFormItem', MyFormItem)
    app.component('ImportModal', ImportModal)
    app.component('ImportImg', ImportImg)
    app.component('ImportFile', ImportFile)
    app.component('SelectDic', SelectDic)
    app.component('ImgModal', ImgModal)
    app.component('PdfPreview', PdfPreview)
    app.component('BasicUpload', BasicUpload)
    app.component('ClientSelectTree', ClientSelectTree)
    app.component('EmployeeMatching', EmployeeMatching)
    app.component('NumberRange', NumberRange)
    app.component('StationCascader', StationCascader)
}
