import request from './request'
import { divide } from 'number-precision'
import { notification } from 'ant-design-vue'
import router from '../router'

// 一些常用的请求

/**
 * 获取全部的户口所在地
 * @returns domicilePlaceList
 */
export const getDomicilePlaceList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-talent-staffs/domicile-place-list`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i,
                        value: i,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取全部的操作事由
 * @returns domicilePlaceList
 */
export const getctProposesList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/operationReason`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.itemName,
                        value: i.itemValue,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取全部社保类型
 * @returns
 */
export const getSocialSecurityTypeList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/employee-welfare/social-security-type-list`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.socialSecurityName,
                        value: i.id,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取全部公积金类型
 * @returns
 */
export const getAccumulationFundTypeList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/employee-welfare/accumulation-fund-type-list`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.typeName,
                        value: i.id,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取字典表中的员工状态
 * @returns
 */
export const getStaffStatusList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/staffStates`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.itemName,
                        value: i.itemValue,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取字典表中的员工类型
 * @returns
 */
export const getStaffTypeList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/staffType`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.itemName,
                        value: i.itemValue,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取专管员列表
 * @returns
 */
export const getSpecialList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-clients-specialized/selectuser`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.realName,
                        value: i.id,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 发票内容列表
 * @returns
 */
export const getContentList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/INVOICE_CONTENT`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.itemName,
                        value: i.itemValue?.toString(),
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}
/**
 * 税率列表
 * @returns
 */
export const getTaxRateList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/TAX_RATE`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.itemName,
                        value: i.itemName.includes('%') ? divide(Number(i.itemName?.replace('%', '')), 100) : i.itemValue,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 开票审核角色时列表
 * @returns
 */
export const getNoticeRoleList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-bill-invoices/notice-roles`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.roleName,
                        value: i.id?.toString(),
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 报销内容类型列表
 * @returns
 */
export const getReimbursementContentList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/com-code-tables/getCodeTableByInnerName/REIMBURSEMENT_CONTENT`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        ...i,
                        label: i.itemName,
                        value: i.itemValue,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 报销通知角色列表
 * @returns
 */
export const getReimbursementNoticeRoleList = () => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-bill-reimbursement-applies/getNoticeRoles`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.roleName,
                        value: i.id?.toString(),
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 催办人员列表
 * @returns
 */
export const getUrgeUserList = (applyId) => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-bill-reimbursement-applies/getUrgeUsers/${applyId}`)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.userRoleName,
                        value: i.id,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取薪酬账单费用项
 * @returns
 */
export const getBillExpenseManage = (pams) => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-bill-dynamic-fieldses/getBillExpenseManage`, pams)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.value,
                        value: i.key,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}

export const getFinalBillStatement = (prams) => {
    return new Promise((resolve, reject) => {
        request
            .get(`/api/hr-bill-dynamic-fieldses/getBillExpenseManage`, prams)
            .then((res) => {
                resolve(
                    res.map((i) => ({
                        label: i.value,
                        value: i.key,
                    })) || [],
                )
            })
            .catch((err) => {
                reject(err)
            })
    })
}
export const getToDoList = () => {
    return new Promise(() => {
        request.get(`/api/hr-upcomings/warn`, {}, { loading: false }).then((res) => {
            res.length &&
                notification.info({
                    message: '待办提醒',
                    description: '您今天有' + res.length + '条待办！',
                    duration: 5 * 60,
                    onClick: () => {
                        router.push({
                            name: 'toDoList',
                        })
                        notification.destroy()
                    },
                })
        })
    })
}

export const delay = (num = 200) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(1)
        }, num)
    })
}

/**
 *
 * @params 费用内容 1代发工资 2代缴社保 3代缴公积金 4其他 5代缴医保
 * @return 获取缴纳账户
 */
export const postCostAccount = (data) => {
    return new Promise((resolve, reject) => {
        request
            .post(`/api/hr-PlatformAccount/selectaccount`, data)
            .then((res) => {
                if (res.length == 0) {
                    resolve([])
                } else {
                    resolve([
                        {
                            title: '全部',
                            value: '1',
                            key: '1',
                            children: res.map((i) => {
                                return {
                                    ...i,
                                    title: i.accountNumber,
                                    value: i.id,
                                    key: i.id,
                                }
                            }),
                        },
                    ])
                }
            })
            .catch((err) => {
                reject(err)
            })
    })
}

/**
 * 获取特殊客户列表
 * @returns
 */
 export const  getSpecialCustomer=()=> {
    return new Promise((resolve, reject) => {
        request
            .get('/api/hr-clients/special', {})
            .then((res) => {
                resolve(res)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

