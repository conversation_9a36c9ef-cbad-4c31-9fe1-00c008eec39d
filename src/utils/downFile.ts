import { message, message as Message } from 'ant-design-vue'
import request from '/@/utils/request'

import J<PERSON><PERSON><PERSON> from 'jszip'
import { saveAs } from 'file-saver'
import moment from 'moment'

// import XLSX, { WritingOptions } from 'xlsx'

import { Decrypt, Encrypt } from './secret'
import { getMaxlevel } from './index'
import { table } from 'console'

/** 下载文件
 * @param {string} type 请求类型
 * @param {string} restUrl 请求路径
 * @param {string} fileName 文件名
 * @returns {void} 无返回值
 * @param {string | object | array} data 请求参数
 */
async function downFile(type: 'get' | 'post' | 'open', restUrl: string, fileName: string, data?: any, isSelf?: boolean) {
    if (type == 'open') {
        setTimeout(() => {
            window.open(restUrl)
            console.log(restUrl)
        }, 10)

        return
    }
    if (!isSelf)
        Message.loading({
            content: '正在导出，请稍后...',
            duration: 0,
        })
    if (type == 'post') {
        try {
            let pesponse: inObject = {}
            const setConfig = {
                headers: {
                    Authorization: 'Bearer ' + localStorage.token,
                    isEncrypt: 'no',
                },
                responseType: 'blob',
                getResponse: function (ref) {
                    pesponse = ref
                    console.log(ref)
                    return ref
                },
                // secret: false,
            }
            const fre = await request.post(restUrl, data, { setConfig, loading: false })

            try {
                let content: any
                if (pesponse?.config?.headers?.isEncrypt == 'no') {
                    content = await fre.text()
                } else {
                    content = Decrypt(await fre.text())
                }

                if (content.substring(0, 4) == 'http') {
                    downFile('get', content, fileName, data, true)
                    return
                }
            } catch (error: any) {}

            const href = URL.createObjectURL(
                new Blob([fre], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
            )
            const disposition = pesponse?.headers['content-disposition'] || ''
            let newFileName = decodeURI(disposition.split('=')[1])

            if (fileName) {
                const type = fileName?.substring(fileName?.lastIndexOf('.'))
                const newType = newFileName?.substring(newFileName?.lastIndexOf('.'))

                if (type == newType) {
                    newFileName = fileName
                } else {
                    newFileName = newType == 'undefined' || !newType ? fileName : fileName + newType
                }
            }
            createLinkNode(newFileName, href, !!fileName)
        } catch (error: any) {
            // console.log('error', error.response)

            const fileReader = new FileReader()
            fileReader.onloadend = () => {
                const res = JSON.parse((fileReader.result || '') as string)
                // console.log('json', res)
                Message.error(res.message || '导出错误')
            }
            fileReader.readAsText(error.response.data)
        } finally {
            Message.destroy()
        }
    } else {
        try {
            let pesponse: inObject = {}
            const setConfig = {
                headers: {
                    isEncrypt: 'no',
                },
                responseType: 'blob',
                getResponse: function (ref) {
                    pesponse = ref
                    return ref
                },
                // secret: false,
            }
            const fre = await request.get(restUrl, '', { setConfig, loading: false })

            try {
                let content: any
                if (pesponse?.config?.headers?.isEncrypt == 'no') {
                    content = await fre.text()
                } else {
                    content = Decrypt(await fre.text())
                }
                // const content: any = Decrypt(await fre.text())
                if (content.substring(0, 4) == 'http') {
                    downFile('get', content, fileName, data, true)
                    return
                }
            } catch (error: any) {}

            const href = URL.createObjectURL(
                new Blob([fre], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
            )
            const disposition = pesponse?.headers['content-disposition'] || ''
            let newFileName = decodeURI(fileName)
            if (disposition.split('=')[1]) {
                newFileName = decodeURI(disposition.split('=')[1])
                if (fileName) {
                    const type = fileName?.substring(fileName?.lastIndexOf('.'))
                    const newType = newFileName?.substring(newFileName?.lastIndexOf('.'))
                    if (type == newType) {
                        newFileName = fileName
                    } else {
                        newFileName = fileName + newType
                    }
                }
            } else {
                const type = restUrl?.substring(restUrl?.lastIndexOf('.'))
                const fileNameType = fileName?.substring(fileName.length - type.length)
                newFileName = fileName
                if (fileNameType != type) {
                    newFileName = fileName ? fileName + type : restUrl.substring(restUrl.lastIndexOf('/') + 1)
                }
            }
            createLinkNode(newFileName, href, !!fileName)
            // const type = restUrl?.split('.')?.slice(-1)
        } catch (error: any) {
            // console.log('error', error.response)

            const fileReader = new FileReader()
            fileReader.onloadend = () => {
                const res = JSON.parse((fileReader.result || '') as string)
                // console.log('json', res)
                Message.destroy()
                Message.error(res.message || '导出错误')
            }
            fileReader.readAsText(error.response.data)
        } finally {
            Message.destroy()
        }
    }
}
export default downFile

/**
 * 打包下载多个文件
 * @param srcs 资源url 必须带后缀
 * @param names 对应的名称
 */
export async function downMultFile(zipName: string, srcs: string[] | inObject, names?: string[], isSingleFolder?: boolean) {
    Message.loading({
        content: '正在导出，请稍后...',
        duration: 0,
    })
    if (srcs?.length < 1) {
        Message.destroy()
        Message.warning('暂无下载信息!')

        return
    }
    const zip = new JSZip()
    let i = 0
    if (isSingleFolder) {
        while (srcs[i]) {
            const dom = zip.folder(srcs[i].folderName)
            for (let j = 0; j < srcs[i].srcs.length; j++) {
                const item = srcs[i].srcs[j]
                try {
                    const type = item.url.split('.').slice(-1) // 获取文件类型
                    const base64 = await src2Base64(item.url)
                    const path = item.name
                        ? item.name.includes(`.${type}`)
                            ? item.name
                            : `${item.name}.${type}`
                        : `${i}.${type}`
                    dom?.file(path, base64, { base64: true })
                } catch (error) {
                    Message.destroy()
                }
            }
            i++
        }
    } else {
        while (srcs[i]) {
            try {
                const type = srcs[i].split('.').slice(-1) // 获取文件类型
                const base64 = await src2Base64(srcs[i])
                const path =
                    names && names[i] ? (names[i].includes(`.${type}`) ? names[i] : `${names[i]}.${type}`) : `${i}.${type}`
                zip.file(path, base64, { base64: true })
            } catch (error) {
                Message.destroy()
            }
            i++
        }
    }
    const zipfile = await zip.generateAsync({ type: 'blob' })
    saveAs(zipfile, `${zipName}_${moment().format('YYYYMMDDHHmmss')}.zip`)
    Message.destroy()
}

/**
 * 获取文件base64
 * @param {string} src 文件地址
 */
async function src2Base64(src) {
    const fre = await fetch(src)
    const blob = await fre.blob()
    return new Promise<string | ArrayBuffer | Blob>((suc) => {
        const fs = new FileReader()
        fs.onload = (e: any) => {
            // console.log(e.target.result)
            suc(e.target.result.replace(/^.*base64,/, ''))
        }
        fs.readAsDataURL(blob)
    })
}

type Merge = {
    // 开始行列
    s: {
        r: number
        c: number
    }
    // 结束行列
    e: {
        r: number
        c: number
    }
}

export const exportTable = (
    columns,
    tableData,
    name,
    {
        isDown = true,
        rowIndex = -1 as any,
        cellStyle = {},
        widthCols = [] as any,
        exceptCols = [] as Array<string>,
        headerText = '',
        isSettlement = false,
    } = {},
) => {
    console.log(tableData)
    if (headerText.includes('中石化')) {
        tableData.slice(-1)[0].index = '所有费用合计'
        tableData.slice(-2)[0].index = '结算费用合计'
    } else {
        tableData.slice(-1)[0].index = '合计'
    }
    if (isDown == true) {
        message.loading('正在导出，请稍后...')
    }
    const firHeader: any[] = []
    const secHeader: any[] = []
    const thirdHeader: any[] = []
    const merge: any[] = []
    const tableDataList: any[] = []
    const judgeHasChildren = (arr) => {
        for (let i = 0; i < arr.length; i++) {
            const element = arr[i]
            if ('children' in element) {
                return true
            }
        }
        return false
    }
    const getSinopecMerge = () => {
        const maxLevel = getMaxlevel(columns)
        columns.forEach((item) => {
            if (item.children?.length) {
                if (judgeHasChildren(item.children))
                    merge.push({
                        s: { r: 3, c: firHeader.length },
                        e: { r: 3, c: firHeader.length - 1 + item.children?.length - 1 + 2 },
                    })
                else {
                    merge.push({
                        s: { r: 3, c: firHeader.length },
                        e: { r: 3, c: firHeader.length - 1 + item.children?.length },
                    })
                }
                item.children.forEach((element) => {
                    if (element.children?.length) {
                        merge.push({
                            s: { r: 4, c: firHeader.length },
                            e: { r: 4, c: firHeader.length - 1 + element.children?.length },
                        })
                        element.children.forEach((ele) => {
                            firHeader.push(item.title)
                            secHeader.push(element.title)
                            thirdHeader.push(ele.title)
                            tableDataList.push(ele.dataIndex)
                        })
                    } else {
                        firHeader.push(item.title)
                        secHeader.push(element.title)
                        thirdHeader.push(element.title)
                        tableDataList.push(element.dataIndex)
                        merge.push({
                            s: { r: 4, c: firHeader.length - 1 },
                            e: { r: 3 + maxLevel - 1, c: firHeader.length - 1 },
                        })
                    }
                })
            } else {
                firHeader.push(item.title)
                secHeader.push(item.title)
                thirdHeader.push(item.title)
                merge.push({
                    s: { r: 3, c: firHeader.length - 1 },
                    e: { r: 3 + maxLevel - 1, c: firHeader.length - 1 },
                })
                tableDataList.push(item.dataIndex)
            }
        })
        merge.push({
            s: { r: 0, c: 0 },
            e: { r: 1, c: firHeader.length - 1 },
        })
    }

    const getDefaultMerge = async () => {
        columns.forEach((item) => {
            if (item.children?.length) {
                merge.push({
                    s: { r: isSettlement ? 2 : 0, c: firHeader.length },
                    e: { r: isSettlement ? 2 : 0, c: firHeader.length - 1 + item.children?.length },
                })
                item.children.forEach((element) => {
                    firHeader.push(item.title)
                    secHeader.push(element.title)
                    tableDataList.push(element.dataIndex)
                })
            } else {
                firHeader.push(item.title)
                secHeader.push(item.title)
                merge.push({
                    s: { r: isSettlement ? 2 : 0, c: firHeader.length - 1 },
                    e: { r: isSettlement ? 3 : 1, c: firHeader.length - 1 },
                })
                tableDataList.push(item.dataIndex)
            }
        })
        if (isSettlement) {
            merge.push(
                {
                    s: { r: 0, c: 0 },
                    e: { r: 1, c: firHeader.length - 1 },
                },
                {
                    s: { r: tableData.length + 2, c: 0 },
                    e: { r: tableData.length + 2, c: 2 },
                },
            )
        }
    }

    //结算单中石化专用
    const settlementSinopec = () => {
        const maxLevel = getMaxlevel(columns)
        if (maxLevel == 2) {
            columns.forEach((item) => {
                if (item.children?.length) {
                    merge.push({
                        s: { r: 2, c: firHeader.length },
                        e: { r: 2, c: firHeader.length - 1 + item.children?.length },
                    })
                    item.children.forEach((element) => {
                        firHeader.push(item.title)
                        secHeader.push(element.title)
                        tableDataList.push(element.dataIndex)
                    })
                } else {
                    firHeader.push(item.title)
                    secHeader.push(item.title)
                    merge.push({
                        s: { r: isSettlement ? 2 : 0, c: firHeader.length - 1 },
                        e: { r: isSettlement ? 3 : 1, c: firHeader.length - 1 },
                    })
                    tableDataList.push(item.dataIndex)
                }
            })
            if (isSettlement) {
                merge.push(
                    {
                        s: { r: 0, c: 0 },
                        e: { r: 1, c: firHeader.length - 1 },
                    },
                    {
                        s: { r: tableData.length + 2, c: 0 },
                        e: { r: tableData.length + 2, c: 3 },
                    },
                    {
                        s: { r: tableData.length + 1, c: 0 },
                        e: { r: tableData.length + 1, c: 3 },
                    },
                )
            }
        } else if (maxLevel == 3) {
            columns.forEach((item) => {
                if (item.children?.length) {
                    if (judgeHasChildren(item.children))
                        merge.push({
                            s: { r: 2, c: firHeader.length },
                            e: { r: 2, c: firHeader.length - 1 + item.children?.length - 1 + 2 },
                        })
                    else {
                        merge.push({
                            s: { r: 2, c: firHeader.length },
                            e: { r: 2, c: firHeader.length - 1 + item.children?.length },
                        })
                    }
                    item.children.forEach((element) => {
                        if (element.children?.length) {
                            merge.push({
                                s: { r: 3, c: firHeader.length },
                                e: { r: 3, c: firHeader.length - 1 + element.children?.length },
                            })
                            element.children.forEach((ele) => {
                                firHeader.push(item.title)
                                secHeader.push(element.title)
                                thirdHeader.push(ele.title)
                                tableDataList.push(ele.dataIndex)
                            })
                        } else {
                            firHeader.push(item.title)
                            secHeader.push(element.title)
                            thirdHeader.push(element.title)
                            tableDataList.push(element.dataIndex)
                            merge.push({
                                s: { r: 3, c: firHeader.length - 1 },
                                e: { r: 2 + maxLevel - 1, c: firHeader.length - 1 },
                            })
                        }
                    })
                } else {
                    firHeader.push(item.title)
                    secHeader.push(item.title)
                    thirdHeader.push(item.title)
                    merge.push({
                        s: { r: 2, c: firHeader.length - 1 },
                        e: { r: 2 + maxLevel - 1, c: firHeader.length - 1 },
                    })
                    tableDataList.push(item.dataIndex)
                }
            })

            if (isSettlement) {
                merge.push(
                    {
                        s: { r: 0, c: 0 },
                        e: { r: 1, c: firHeader.length - 1 },
                    },
                    {
                        s: { r: tableData.length + maxLevel - 1, c: 0 },
                        e: { r: tableData.length + maxLevel - 1, c: 3 },
                    },
                    {
                        s: { r: tableData.length + maxLevel, c: 0 },
                        e: { r: tableData.length + maxLevel, c: 3 },
                    },
                )
            }
        }
    }

    let sheet: any = []

    if (headerText && !isSettlement) {
        getSinopecMerge()
    } else {
        if (headerText?.includes('中石化')) {
            settlementSinopec()
        } else {
            getDefaultMerge()
        }
    }
    const sheetData = tableData?.map((item) => {
        return tableDataList?.map((element) => {
            return isSettlement ? (typeof item[element] == 'number' ? item[element] ?? 0 : item[element] ?? '') : item[element]
        })
    })
    if (headerText && !isSettlement) {
        sheet = [
            ...sheetData.slice(0, 2),
            ...sheetData.slice(1, 2),
            firHeader,
            secHeader,
            thirdHeader,
            ...sheetData.slice(2, sheetData.length),
        ]
        console.log(sheet)
    } else if (headerText && isSettlement) {
        if (headerText?.includes('中石化')) {
            const maxLe = getMaxlevel(columns)
            if (maxLe == 2) {
                sheet = [...sheetData.slice(0, 1), ...sheetData.slice(0, 1), firHeader, secHeader, ...sheetData.slice(1)]
            } else {
                sheet = [
                    ...sheetData.slice(0, 1),
                    ...sheetData.slice(0, 1),
                    firHeader,
                    secHeader,
                    thirdHeader,
                    ...sheetData.slice(1),
                ]
            }
        } else {
            sheet = [...sheetData.slice(0, 1), ...sheetData.slice(0, 1), firHeader, secHeader, ...sheetData.slice(1)]
        }
    } else {
        sheet = [firHeader, secHeader, ...sheetData]
    }

    const blob = json2xlsx(sheet, name, merge, rowIndex, cellStyle, widthCols, exceptCols, headerText, isSettlement)
    if (isDown == true) {
        createLinkNode(`${name}.xlsx`, URL.createObjectURL(blob))
    }
    message.destroy()
    return blob
}

/**
 * js生成xlsx
 * @param data 二维数组 第一项为表头
 * @param name xlsx 名称
 * @param merge 合并单元格
 */

export const json2xlsx = (
    data: (string | number)[][],
    name,
    merge: Merge[],
    rowIndex: number,
    cellStyle: any,
    widthCols: Array<{ [key: string]: number }>,
    exceptCols: Array<string>,
    headerText: string,
    isSettlement: boolean,
) => {
    const worksheet = XLSX.utils.aoa_to_sheet(data)
    if (merge.length) {
        // 合并单元格
        worksheet['!merges'] = merge
    }
    if (widthCols && widthCols.length) {
        worksheet['!cols'] = widthCols
    }
    const defaultStyle = {
        font: {
            name: '宋体',
            sz: 11,
            color: {
                auto: 1,
            },
        },
        border: {
            color: {
                auto: 1,
            },
            top: {
                style: 'thin',
            },
            bottom: {
                style: 'thin',
            },
            left: {
                style: 'thin',
            },
            right: {
                style: 'thin',
            },
        },
        alignment: {
            // 自动换行
            wrapText: 1,
            // 居中
            horizontal: 'center',
            vertical: 'center',
            indent: 0,
        },
        numFmt: 'General',
    }
    const range = XLSX.utils.decode_range(worksheet['!ref'])
    // 设置表格样式
    for (let i = range.s.c; i < range.e.c + 1; i++) {
        for (let j = range.s.r; j < range.e.r + 1; j++) {
            const cell_address = {
                c: i,
                r: j,
            }
            const column = XLSX.utils.encode_cell(cell_address)
            if (worksheet[column]) {
                if (headerText) {
                    worksheet['A1'].s = {
                        font: { name: '微软雅黑', sz: 24 },
                        alignment: {
                            // 居中
                            horizontal: 'center',
                            vertical: 'center',
                            indent: 0,
                        },
                    }
                    if (column.length == 2 && ['1', '2', '3'].includes(column.substr(column.length - 1, 1)) && !isSettlement) {
                        worksheet[column]['s'] = {
                            font: { name: '微软雅黑', sz: 10 },
                            alignment: {
                                // 居中
                                horizontal: 'center',
                                vertical: 'center',
                                indent: 0,
                            },
                        }
                    } else {
                        worksheet[column]['s'] = Object.keys(cellStyle).length === 0 ? defaultStyle : cellStyle
                    }
                } else {
                    worksheet[column]['s'] = Object.keys(cellStyle).length === 0 ? defaultStyle : cellStyle
                    worksheet[column]['t'] = 's'
                    worksheet[column].z = 'General'
                }
            }
        }
    }
    if (rowIndex != -1) {
        let str
        let aStr: any[] = []
        let accounStr
        let peopleNumArr
        for (const i in worksheet) {
            if (!exceptCols.length) {
                if (Number(i.slice(1) || -1) == rowIndex) {
                    worksheet[i].z = '0.00'
                    worksheet[i].t = 'n'
                } else {
                    if (!Number.isNaN(Number(i.slice(1)))) {
                        worksheet[i].z = 'General'
                        worksheet[i].t = 's'
                    }
                }
            }
            if (exceptCols.length && !exceptCols.includes(i.slice(0, 1))) {
                if (Number(i.slice(1) || -1) >= rowIndex) {
                    let _IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
                    let _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/

                    // 校验身份证：
                    if (_IDRe18.test(worksheet[i].v) || _IDre15.test(worksheet[i].v)) {
                        worksheet[i].z = '@'
                        worksheet[i].t = 's'
                    } else {
                        if (typeof worksheet[i].v == 'string') {
                            worksheet[i].z = 'General'
                            worksheet[i].t = 's'
                        } else {
                            worksheet[i].z = '0.00'
                            worksheet[i].t = 'n'
                        }
                    }
                    if (worksheet[i].v == '身份证号') {
                        str = i.slice(0, i.search(/\d/))
                    }
                    const re = new RegExp('^' + str) // re为/^\d+bl$/gim
                    if (str && re.test(i)) {
                        worksheet[i].t = 's'
                    }
                } else {
                    if (!Number.isNaN(Number(i.slice(1)))) {
                        worksheet[i].z = 'General'
                        worksheet[i].t = 's'
                    }
                }
            }
            if (exceptCols.length && exceptCols.includes(i.slice(0, 1))) {
                if (!Number.isNaN(Number(i.slice(1)))) {
                    worksheet[i].z = 'General'
                    worksheet[i].t = 's'
                }
            }
            if (worksheet[i].v == '人数') {
                peopleNumArr = i.slice(0, i.search(/\d/))
            }
            const res = new RegExp('^' + peopleNumArr) // re为/^\d+bl$/gim
            if (res.test(i)) {
                worksheet[i].z = '0'
                worksheet[i].t = 'n'
            }
            if (worksheet[i].v == '人数') {
                worksheet[i].z = 'General'
                worksheet[i].t = 's'
            }
        }
        for (const key in worksheet) {
            if (worksheet[key].v == '住房公积金') {
                aStr.push(key.slice(0, key.search(/\d/)))
            }
            aStr.forEach((el) => {
                const res = new RegExp('^' + el) // re为/^\d+bl$/gim
                if (res.test(key)) {
                    worksheet[key].z = 'General'
                }
            })
            if (worksheet[key].v == '公积金总金额') {
                accounStr = key.slice(0, key.search(/\d/))
            }
            const res = new RegExp('^' + accounStr) // re为/^\d+bl$/gim
            if (res.test(key)) {
                worksheet[key].z = 'General'
            }
        }
    }

    const workbook = XLSX.utils.book_new()

    XLSX.utils.book_append_sheet(workbook, worksheet, name.length > 30 ? '' : name)
    const workbookBlob = workbook2blob(workbook)

    return workbookBlob
}

function workbook2blob(workbook) {
    // 生成excel的配置项
    const wopts = {
        // 要生成的文件类型
        bookType: 'xlsx',
        // // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
        bookSST: false,
        type: 'binary',
    }
    const wbout = XLSX.write(workbook, wopts)
    // 将字符串转ArrayBuffer
    function s2ab(s) {
        const buf = new ArrayBuffer(s.length)
        const view = new Uint8Array(buf)
        for (let i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
        return buf
    }
    const blob = new Blob([s2ab(wbout)], {
        type: 'application/octet-stream',
    })
    return blob
}

export function createLinkNode(download, href, isStamp = true) {
    const a = document.createElement('a')
    a.download = `${isStamp ? moment().format('YYYYMMDDHHmmss') + '_' : ''}${download}`
    a.href = href
    a.id = 'createLinkNode' + Math.random() * 100
    document.body.appendChild(a)
    a.click()
    Message.destroy()
    URL.revokeObjectURL(href)
    document.body.removeChild(document.getElementById(a.id) as HTMLElement)
}

export function exportFile(file, name) {
    const url = window.URL.createObjectURL(new Blob([file]))
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', name)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link) // 下载完成移除元素
    window.URL.revokeObjectURL(url) // 释放掉blob对象
}
