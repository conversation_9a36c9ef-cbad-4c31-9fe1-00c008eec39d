/* eslint-disable no-unused-vars */
import axios from 'axios'
import type { AxiosRequestConfig, AxiosInstance, AxiosResponse } from 'axios'
import type { RequestOptions, Result } from 'types/axios'
import appConfig from '/@/config'
import { Decrypt, Encrypt } from './secret'

import { message as Message } from 'ant-design-vue'
import router from '/@/router'
import useUserStore from '../store/modules/user'
import globalLodingStore from '/@/store/modules/globalLoding'
import { debounce } from '/@/utils'
export interface CreateAxiosOptions extends AxiosRequestConfig {
    baseUrl: string
    transform?: AxiosTransform
    requestOptions?: RequestOptions
    getResponse?: Function
    // secret?: Boolean
}

interface myAxiosInstance extends AxiosInstance {
    defaults: any
}

let needLoadingRequestCount = 0
let loading: null | object = null

abstract class AxiosTransform {
    /**
     * @description: 默认请求之前的拦截器
     */
    requestInterceptors?: (config: AxiosRequestConfig, options: CreateAxiosOptions) => AxiosRequestConfig

    /**
     * @description: 请求之后的拦截器
     */
    responseInterceptors?: (res: AxiosResponse<any>) => AxiosResponse<any>

    /**
     * @description: 请求之前的拦截器错误处理
     */
    requestInterceptorsCatch?: (error: Error) => void

    /**
     * @description: 请求之后的拦截器错误处理
     */
    responseInterceptorsCatch?: (error: Error) => void

    /**
     * @description: 自定义请求前的hook
     */
    beforeRequestHook?: (config: CreateAxiosOptions, options: RequestOptions) => CreateAxiosOptions
}

export class Axios {
    private axiosInstance: myAxiosInstance
    private readonly options: CreateAxiosOptions

    constructor(options: CreateAxiosOptions) {
        this.options = options
        this.axiosInstance = axios.create(options)
        this.axiosInstance.defaults.timeout= 30 * 60 * 1000
        this.setupInterceptors()
    }

    private getTransform() {
        const { transform } = this.options
        return transform
    }
    /**
     * @description: 初始化配置
     */
    private setupInterceptors() {
        const transform = this.getTransform()
        if (!transform) {
            return
        }
        const { requestInterceptors, requestInterceptorsCatch, responseInterceptors, responseInterceptorsCatch } = transform

        // 请求拦截器配置处理
        this.axiosInstance.interceptors.request.use((config: AxiosRequestConfig) => {
            if (requestInterceptors) {
                config = requestInterceptors(config, this.options)
            }
            return config
        }, undefined)

        // 请求拦截器
        requestInterceptorsCatch && this.axiosInstance.interceptors.request.use(undefined, requestInterceptorsCatch)

        // 返回拦截器配置处理
        this.axiosInstance.interceptors.response.use((res: AxiosResponse<any>) => {
            if (responseInterceptors) {
                res = responseInterceptors(res)
            }
            return res
        }, undefined)

        // 返回拦截器
        responseInterceptorsCatch && this.axiosInstance.interceptors.response.use(undefined, responseInterceptorsCatch)
    }

    get<T = any>(restUrl: string, params?: Object | Array<T>, options?: RequestOptions): Promise<T> {
        return this.request({ method: 'GET', url: restUrl + getParams(params) }, options)
    }

    post<T = any>(restUrl: string, data: Object | Array<T>, options?: RequestOptions): Promise<T> {
        return this.request({ method: 'POST', url: restUrl, data: resParams(data) }, options)
    }

    put<T = any>(restUrl: string, data: Object | Array<T>, options?: RequestOptions): Promise<T> {
        return this.request({ method: 'PUT', url: restUrl, data: resParams(data) }, options)
    }

    delete<T = any>(restUrl: string, data?: Object | Array<T>, options?: RequestOptions): Promise<T> {
        return this.request({ method: 'DELETE', url: restUrl, data: resParams(data) }, options)
    }
    request<T = any>(config: AxiosRequestConfig, options?: RequestOptions): Promise<T> {
        console.log(options?.loading)
        if (options?.loading !== false) {
            showLoading()
        }
        const { requestOptions, baseUrl } = this.options
        const opt: RequestOptions = Object.assign({}, requestOptions, options)
        let conf: CreateAxiosOptions = { ...config, baseUrl, requestOptions: opt }

        const { beforeRequestHook } = this.getTransform() || {}
        conf = beforeRequestHook ? beforeRequestHook(conf, opt) : conf

        return new Promise((resolve, reject) => {
            this.axiosInstance
                .request<any, AxiosResponse<Result>>(conf)
                .then((res: AxiosResponse<Result>) => {
                    if (conf?.getResponse) {
                        conf?.getResponse(res)
                    }
                    let newres = res?.data
                    try {
                        newres = JSON.parse(res?.data as any)
                    } catch (error) {
                        newres = res?.data
                    }
                    if (res?.config?.headers?.isEncrypt == 'no') {
                        resolve(newres as unknown as Promise<T>)
                    } else {
                        resolve(Decrypt(newres) as unknown as Promise<T>)
                    }
                    // if (conf?.secret == false || !appConfig.isEncrypt) {
                    //     resolve(res?.data as unknown as Promise<T>)
                    // } else {
                    //     resolve(Decrypt(res?.data) as unknown as Promise<T>)
                    // }
                })
                .catch((e: Error) => {
                    reject(e)
                })
                .finally(() => {
                    if (options?.loading !== false) {
                        hideLoading()
                    }
                })
        })
    }
}

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
    /**
     * @description: 请求拦截器处理
     */
    requestInterceptors: (config, options) => {
        // 请求之前处理config
        const token = getToken()
        if (!config?.headers?.Authorization && token) {
            // 如果没自定义token 设置token
            config.headers = {
                ...config.headers,
                Authorization: `Bearer ${token}`,
            }
        }
        if (appConfig.isEncrypt == false) {
            ;(config.headers as any).isEncrypt = 'no'
        }
        // 这里没做处理 直接加上baseUrl 需要其它逻辑直接改
        if (options.url?.includes('http')) {
            return config
        }
        config.baseURL = options.baseUrl

        return config
    },

    /**
     * @description: 请求错误拦截器处理
     */
    requestInterceptorsCatch: (error: any) => {
        console.log('requestInterceptorsCatch', error)
    },

    /**
     * @description: 响应拦截器处理
     */
    responseInterceptors: (res: AxiosResponse<any>) => {
        return res
    },

    /**
     * @description: 响应错误处理
     */
    responseInterceptorsCatch: async (error: any) => {
        // console.log('response err ==', error.response)
        if (error.response.status == 401) {
            useUserStore().logoutAction()
            router.push(appConfig.loginPath)
            window.location.reload()
        } else {
            // Message.destroy()
            if (error.response.data) {
                if (error.response.request.responseType != 'blob')
                    error.response.data = appConfig.isEncrypt ? Decrypt(error.response.data) : error.response.data
                if (error.response.data?.message && error.response.data?.message.indexOf('请重新登录') != -1) {
                    useUserStore().logoutAction()
                    router.push(appConfig.loginPath)
                    window.location.reload()
                }
            }
            Message.error(error.response.data?.message)
        }
        return Promise.reject(error)
    },
    /**
     * @description 自定义请求前的hook
     */
    beforeRequestHook: (config: CreateAxiosOptions, option: RequestOptions) => {
        if (option.setConfig) {
            Object.keys(option.setConfig).forEach((i) => {
                //@ts-ignore
                config[i] = option?.setConfig[i]
            })
        }

        return config
    },
}

const getToken = () => {
    return localStorage.token || ''
}

const { baseUrl } = appConfig
const request = new Axios({ transform, baseUrl })

// 显示loading
const showLoading = () => {
    if (needLoadingRequestCount === 0 && !loading) {
        loading = {}
        globalLodingStore().setGlobalLodingShow(true)
    }
    needLoadingRequestCount++
}

// 关闭loading
const hideLoading = () => {
    needLoadingRequestCount--
    needLoadingRequestCount = Math.max(needLoadingRequestCount, 0)
    if (needLoadingRequestCount === 0) {
        debounce(() => {
            globalLodingStore().setGlobalLodingShow(false)
            loading = null
        }, 100)()
    }
}

//get 请求参数处理
const getParams = (params: any) => {
    let body = ''
    if (params && Object.keys(params).length !== 0) {
        body = '?'
        for (const key in params) {
            if (typeof params[key] != undefined) {
                body += '&' + key + '=' + params[key]
            } else {
                body += '&' + key + '='
            }
        }
    }
    return body
}
//去除null与undefined
const resParams = function <T>(params: any): Object | Array<T> {
    return params
    if (Array.isArray(params)) {
        return params
    }
    if (params instanceof FormData) {
        return params
    }
    const body = {}
    for (const key in params) {
        if (params[key] !== null && params[key] !== undefined) {
            body[key] = params[key]
        }
    }
    return body
}
export default request
