import { computed, watch } from 'vue'
import useAppStore from '/@/store/modules/app'
import { EditModalOption, valuesAndRules } from '/#/component'
import type { Menu as MenuType } from '/@/router/types'
import { createPreview } from '/@/components/PdfPreview'
import { notification } from 'ant-design-vue'
import config from '/@/config'
import { useAuth } from '/@/utils/hooks'
interface TreeHelperConfig {
    id: string
    children: string
    pid: string
}

const DEFAULT_CONFIG: TreeHelperConfig = {
    id: 'id',
    children: 'children',
    pid: 'pid',
}

const getConfig = (config: Partial<TreeHelperConfig>) => Object.assign({}, DEFAULT_CONFIG, config)

export function findPath<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T | T[] | null {
    config = getConfig(config)
    const path: T[] = []
    const list = [...tree]
    const visitedSet = new Set()
    const { children } = config
    while (list.length) {
        const node = list[0]
        if (visitedSet.has(node)) {
            path.pop()
            list.shift()
        } else {
            visitedSet.add(node)
            node[children!] && list.unshift(...node[children!])
            path.push(node)
            if (func(node)) {
                return path
            }
        }
    }
    return null
}

export function getAllParentPath<T = Recordable>(treeData: T[], name: string) {
    const menuList = findPath(treeData, (n) => n.name === name) as MenuType[]
    return (menuList || []).map((item) => item.name)
}

/**
 * 监听路由变化
 * @param callback 回调函数
 */
export function listenRouteChange(callback: (route: any) => void) {
    const route = computed(() => useAppStore().getCurrentRoute)
    watch(route, (newRoute) => {
        callback(newRoute)
    })
}

/**
 * 获取EditModal formData rules 初始值
 * @param editOptionArray
 * @returns Object Result
 */
export const getInitValuesAndRules = (editOptionArray: EditModalOption[]) => {
    const values = {}
    const rules = {}
    editOptionArray.forEach((i) => {
        // TODO 其它默认逻辑
        // 设置默认值
        values[i.name] = ''
        if (isEmptyNull(i.default)) {
            values[i.name] = i.type == 'number' ? 0 : i.type == 'select' || i.type == 'change' ? null : ''
        } else {
            values[i.name] = i.default
        }

        // select 多选时值为 array
        if (i.multiple) {
            values[i.name] = []
        }

        // 设置默认校验规格
        // required 默认必需 true, 指定false为非必需
        rules[i.name] =
            i.required == false
                ? {
                      required: false,
                      message: `请输入${i.label}`,
                      trigger: 'blur',
                  }
                : {
                      required: true,
                      message: `请输入${i.label}`,
                      trigger: 'blur',
                  }
        // 可以指定校验类型
        if (i.ruleType) {
            rules[i.name].type = i.ruleType
            return
        }
        // switch boolean类型
        if (i.type == 'switch') {
            rules[i.name].type = 'boolean'
        }
        // select 多选时值为 array
        if (i.multiple) {
            rules[i.name].type = 'array'
        }
        // number 类型
        if (i.type == 'number') {
            rules[i.name].type = 'number'
        }
    })
    return {
        values,
        rules,
    }
}
/**
 * 获取EditModal formData rules 初始值
 * @param editOptionArray
 * @returns Object Result
 */

export const getValuesAndRules = (editOptionArray: valuesAndRules[]) => {
    const values = {}

    const rules = []
    editOptionArray.forEach((i) => {
        rules[i.name] = {
            required: true,
            message: '',
            type: 'string',
            trigger: ['change', 'blur'],
            label: i.label,
            whitespace: true,
        }
        // | 'string'
        // | 'select'
        // | 'change'
        // | 'number'
        // | 'datetime'
        // | 'date'
        // | 'switch'
        // | 'textarea'
        // | 'SelectDic'
        // | 'isTrue'
        // | 'slots'
        if (i.trigger) {
            rules[i.name].trigger = i.trigger
        }

        if (isEmptyNull(i.type) || i.type == 'string') {
            values[i.name] = ''
        }
        values[i.name] = i.default
        if (isEmptyNull(i.default)) {
            values[i.name] = null
        }

        if (['select', 'change', 'datetime', 'date', 'SelectDic'].includes(i.type)) {
            rules[i.name].message = `请选择${i.label}`
        } else {
            rules[i.name].message = `请输入${i.label}`
        }
        if (i.message) {
            rules[i.name].message = i.message
        }
        if (i.uploadmessage) {
            rules[i.name].message = i.uploadmessage
        }
        if (i.ruleType) {
            rules[i.name].type = i.ruleType
        }
        if (i.validator) {
            rules[i.name].validator = i.validator
            rules[i.name].message = undefined
        }
        if (i.required != null && i.required != undefined) {
            rules[i.name].required = i.required
        }
    })
    return {
        values,
        rules,
    }
}
/**
 * 扁平数据结构转Tree
 * @param items data
 * @returns tree data
 */
export const arrayToTree = (items, option = { id: 'id', pid: 'pid' }) => {
    const result: any = [] // 存放结果集
    const itemMap = {} //
    for (const item of items) {
        const id = item[option.id]
        const pid = item[option.pid]

        if (!itemMap[id]) {
            itemMap[id] = {
                children: [],
            }
        }

        itemMap[id] = {
            ...item,
            children: itemMap[id]['children'],
        }

        const treeItem = itemMap[id]
        if (!pid || pid == '0') {
            result.push(treeItem)
        } else {
            if (!itemMap[pid]) {
                itemMap[pid] = {
                    children: [],
                }
            }
            itemMap[pid].children.push(treeItem)
        }
    }
    console.log(result)
    return result
}
/**
 * 取Tree的最大层级
 * @param items Tree
 * @returns num
 */
export function getMaxlevel(treeData) {
    let maxLevel = 0
    function loop(data, level) {
        data.forEach((item) => {
            item.level = level
            if (level > maxLevel) {
                maxLevel = level
            }
            if ('children' in item) {
                if (item.children.length > 0) {
                    loop(item.children, level + 1)
                }
            }
        })
    }
    loop(treeData, 1)
    return maxLevel
}

export function ArrToTree(data, option = { id: 'id', pid: 'pid' }, callback?: Function) {
    const dataMap = {}
    data.forEach(function (node) {
        node.children = []
        dataMap[node[option.id]] = node
    })

    const tree: any[] = []

    data.forEach(function (node: any) {
        const parent = dataMap[node[option.pid]]

        if (parent) {
            // create child array if it doesn't exist
            ;(parent.children || (parent.children = [])).push(node)
        } else {
            // parent is null or missing
            tree.push(node)
        }
    })
    if (callback) {
        callback(dataMap)
    }
    return tree
}

export function pushBillIds(list, record, isEdit) {
    // console.log('record===', record)

    if (record.children) {
        record.children.forEach((i) => {
            pushBillIds(list, i, isEdit)
        })
    }
    // 本条数据自身是否有真账单
    if (record.billNo && record.title) {
        if (isEdit) {
            if (!record.billState) {
                list.push(record.id)
            }
        } else {
            list.push(record.id)
        }
    }
}
/**
 * 判断是否为空
 * @param v any
 * @returns boolean
 */
export const isEmpty = (v: any) => {
    switch (typeof v) {
        case 'undefined':
            return true
        case 'string':
            if (v.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true
            break
        case 'boolean':
            if (!v) return true
            break
        case 'number':
            if (isNaN(v)) return true
            break
        case 'object':
            if (null === v || v.length === 0) return true
            for (const i in v) {
                return false
            }
            return true
    }
    return false
}
/**
 * 判断是否为空
 * @param v any
 * @returns boolean
 */
export const isEmptyNull = (v: any) => {
    if (v == null || v == undefined) {
        return true
    }
    return false
}

/**
 * 获取首页常用功能icons
 */
export const useIcons = (name: string) => {
    // const url = import.meta.globEager(`/src/assets/icons/*`)[`/src/assets/icons/${name}.png`].default
    // console.log('url', url)

    return config.iconPath + name + '.png'
}

/**
 * 小驼峰转下划线
 * @param str
 * @returns
 */
export const toUnderline = (str) => {
    return str.replace(/\B([A-Z])/g, '_$1').toLowerCase()
}

/**
 * 月份转年
 * @param data
 * @returns
 */
export const monthToYears = (data) => {
    if (!data) {
        return ''
    }
    // return str.replace(/\B([A-Z])/g, '_$1').toLowerCase()
    if (data >= 12) {
        data = Math.round(data / 12) + '年'
    } else {
        data = data + '月'
    }
    return data
}

/**
 * 预览附件
 * @param url 附件地址
 */
export const previewFile = (url) => {
    if (!url) {
        return
    }
    createPreview({
        urls: [url],
    })
    // const part = url.split('.')
    // const type = part && part.length ? part[part.length - 1] : null

    // // office 预览文件
    // const microsoftOfficePrefix = `https://view.officeapps.live.com/op/view.aspx?src=`

    // if (['xlsx', 'xls', 'doc', 'docx', 'csv', 'ppt', 'pptx'].includes(type)) {
    //     window.open(microsoftOfficePrefix + url, '_blank')
    // } else {
    //     window.open(url, '_blank')
    // }
}

export const SectionToChinese = (num) => {
    if (!/^\d*(\.\d*)?$/.test(num)) {
        return 'Number is wrong!'
    }
    const AA = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    const BB = ['', '十', '百', '千', '万', '亿', '点', '']
    const a: any[] = ('' + num).replace(/(^0*)/g, '').split('.')
    let k = 0
    let re = ''
    for (let i = a[0].length - 1; i >= 0; i--) {
        switch (k) {
            case 0:
                re = BB[7] + re
                break
            case 4:
                if (!new RegExp('0{4}\\d{' + (a[0].length - i - 1) + '}$').test(a[0])) re = BB[4] + re
                break
            case 8:
                re = BB[5] + re
                BB[7] = BB[5]
                k = 0
                break
        }
        if (k % 4 == 2 && a[0].charAt(i + 2) != 0 && a[0].charAt(i + 1) == 0) re = AA[0] + re
        if (a[0].charAt(i) != 0) re = AA[a[0].charAt(i)] + BB[k % 4] + re
        k++
    }
    if (a.length > 1) {
        //加上小数部分(如果有小数部分)
        re += BB[6]
        for (let i = 0; i < a[1].length; i++) re += AA[a[1].charAt(i)]
    }
    return num >= 10 && num < 20 ? re.slice(1, re.length) : re
}
//信息提示
export const openNotification = (tip, name?) => {
    if (!tip) {
        return
    }
    notification.open({
        message: name ? name : '信息处理提示',
        description: tip,
        onClick: () => {
            console.log('Notification Clicked!')
        },
    })
}
// 深拷贝
export function deepClone(target) {
    // 定义一个变量
    let result
    // 如果当前需要深拷贝的是一个对象的话
    if (typeof target === 'object') {
        // 如果是一个数组的话
        if (Array.isArray(target)) {
            result = [] // 将result赋值为一个数组，并且执行遍历
            for (const i in target) {
                // 递归克隆数组中的每一项
                result.push(deepClone(target[i]))
            }
            // 判断如果当前的值是null的话；直接赋值为null
        } else if (target === null) {
            result = null
            // 判断如果当前的值是一个RegExp对象的话，直接赋值
        } else if (target.constructor === RegExp) {
            result = target
        } else {
            // 否则是普通对象，直接for in循环，递归赋值对象的所有值
            result = {}
            for (const i in target) {
                result[i] = deepClone(target[i])
            }
        }
        // 如果不是对象的话，就是基本数据类型，那么直接赋值
    } else {
        result = target
    }
    // 返回最终结果
    return result
}

/**
 * @description 数字转中文数码
 * @param {Number|String}   num     数字[正整数]
 * @param {String}          type    文本类型，lower|upper，默认upper
 * @example number2text(100000000) => "壹亿元整"
 */
export const number2text = (number, type = 'upper') => {
    if (!number) {
        return ''
    }
    // 配置
    const confs = {
        lower: {
            num: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
            unit: ['', '十', '百', '千', '万'],
            level: ['', '万', '亿'],
        },
        upper: {
            num: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'],
            unit: ['', '拾', '佰', '仟'],
            level: ['', '万', '亿'],
        },
        decimal: {
            unit: ['分', '角'],
        },
        maxNumber: 999999999999.99,
    }

    // 过滤不合法参数
    if (Number(number) > confs.maxNumber) {
        console.error(`The maxNumber is ${confs.maxNumber}. ${number} is bigger than it!`)
        return false
    }

    const conf = confs[type]
    const numbers = String(Number(number).toFixed(2)).split('.')
    const integer = numbers[0].split('')
    const decimal = Number(numbers[1]) === 0 ? [] : numbers[1].split('')

    // 四位分级
    const levels = integer.reverse().reduce((pre: any, item, idx) => {
        const level = pre[0] && pre[0].length < 4 ? pre[0] : []
        const value = item === '0' ? conf.num[item] : conf.num[item] + conf.unit[idx % 4]
        level.unshift(value)

        if (level.length === 1) {
            pre.unshift(level)
        } else {
            pre[0] = level
        }

        return pre
    }, [])

    // 整数部分
    const _integer = levels.reduce((pre, item: any, idx) => {
        let _level = conf.level[levels.length - idx - 1]
        let _item = item.join('').replace(/(零)\1+/g, '$1') // 连续多个零字的部分设置为单个零字

        // 如果这一级只有一个零字，则去掉这级
        if (_item === '零') {
            _item = ''
            _level = ''

            // 否则如果末尾为零字，则去掉这个零字
        } else if (_item[_item.length - 1] === '零') {
            _item = _item.slice(0, _item.length - 1)
        }

        return pre + _item + _level
    }, '')

    // 小数部分
    const _decimal = decimal
        .map((item, idx) => {
            const unit = confs.decimal.unit
            const _unit = item !== '0' ? unit[unit.length - idx - 1] : ''

            return `${conf.num[item]}${_unit}`
        })
        .join('')

    // 如果是整数，则补个整字
    return `${_integer}元` + (_decimal || '整')
}

/**
 * @description 操作按钮权限处理
 * @param {inObject[]}   按钮数组
 * @param {inObject[]}   处理后的按钮数组
 * @example
 */
export const getHaveAuthorityOperation = (operationList) => {
    const newMyOperation = operationList.filter((element) => {
        return useAuth.value.buttons.find((i) => i == element.auth || !element.auth)
    })
    return newMyOperation
}

// 防抖
export const debounce = (func: Function, time: number, immediate = false) => {
    let timer: number | null = null
    return (...args: any) => {
        if (timer) clearInterval(timer)
        if (immediate) {
            if (!timer) func.apply(this, args)
            timer = window.setTimeout(() => {
                timer = null
            }, time)
        } else {
            timer = window.setTimeout(() => {
                func.apply(this, args)
            }, time)
        }
    }
}

// 节流
export const throttle = (func: Function, time: number, immediate = false) => {
    if (immediate) {
        let prevTime = 0
        return (...args: any) => {
            const nowTime = Date.now()
            if (nowTime - prevTime >= time) {
                func.apply(this, args)
                prevTime = nowTime
            }
        }
    } else {
        let timer: number | null = null
        return (...args: any) => {
            if (!timer) {
                func.apply(this, args)
                timer = window.setTimeout(() => {
                    if (timer) clearInterval(timer)
                    timer = null
                }, time)
            }
        }
    }
}

// 批量操作文字 全部内容 选中内容 筛选内容
export const getDynamicText = (str, filter, selectedArr) => {
    let exportStr = ''
    if (
        Object.values(filter).filter((el) => {
            if (Array.isArray(el)) return el.length > 0
            else return !isEmptyNull(el) && el !== ''
        }).length > 0
    ) {
        exportStr = `${str}筛选内容`
        if (selectedArr.length > 0) exportStr = `${str}选中内容`
    } else if (selectedArr.length > 0) exportStr = `${str}选中内容`
    else exportStr = `${str}全部内容`
    return exportStr
}

export const getDynamicBankText = (str, filter, selectedArr) => {
    let exportStr = ''
    if (
        Object.values(filter).filter((el) => {
            if (Array.isArray(el)) return el.length > 0
            else return !isEmptyNull(el) && el !== ''
        }).length > 0
    ) {
        exportStr = `${str}筛选银行报盘`
        if (selectedArr.length > 0) exportStr = `${str}选中银行报盘`
    } else if (selectedArr.length > 0) exportStr = `${str}选中银行报盘`
    else exportStr = `${str}全部银行报盘`
    return exportStr
}

export const dragModal = (dom, isCenter = false, flag = false) => {
    const dialogHeaderEl = <HTMLImageElement>document.querySelector(`.${dom} .ant-modal-header`)
    const dragDom = <HTMLImageElement>document.querySelector(`.${dom} .ant-modal`)
    const styDom = <HTMLImageElement>document.querySelector(`.${dom} .ant-modal`)
    const sty = styDom.style
    dialogHeaderEl.style.cursor = 'move'
    if (!flag) {
        dragDom.style.left = '0px'
        dragDom.style.top = isCenter ? '0px' : '100px'
        dragDom.style.transition = 'all 0.5s linear'
        return
    }
    dialogHeaderEl.onmousedown = (e) => {
        // 鼠标按下，计算当前元素距离可视区的距离
        const X = e.clientX // 鼠标位置
        const Y = e.clientY
        dragDom.style.transition = ''
        dragDom.style.transform = 'translate(0px, 0px)'
        // 获取到的值带px 正则匹配替换
        let styL, styT

        // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
        if (sty.left.includes('%')) {
            styL = +document.body.clientWidth * (+sty.left.replace(/%/g, '') / 100)
            styT = +document.body.clientHeight * (+sty.top.replace(/%/g, '') / 100)
        } else {
            styL = +sty.left.replace(/px/g, '')
            styT = +sty.top.replace(/px/g, '')
            styT = isCenter ? 0 : styT === 0 ? 100 : styT
        }
        document.onmousemove = function (e) {
            e.preventDefault()
            // 通过事件委托，计算移动的距离
            const l = e.clientX - X
            const t = e.clientY - Y
            // 移动当前元素
            dragDom.style.left = `${l + styL}px`
            // dragDom.style.top = `${t + styT}px`
            dragDom.style.top = `${t + styT < 0 ? 0 : t + styT}px`
        }

        document.onmouseup = function (e) {
            document.onmousemove = null
            document.onmouseup = null
        }
    }
}
