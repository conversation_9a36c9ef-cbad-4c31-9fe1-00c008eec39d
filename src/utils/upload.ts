import request from './request'

interface FileItem {
    id: string
    name: string
    originName: string
    fileUrl: string
    fileType: string
    filePath: string
}

/**
 * 上传档案附件
 * @param file
 * @returns FileItem
 */
export const uploadRecordFile = (file) => {
    return new Promise<FileItem>(async (resolve, reject) => {
        const formdata = new FormData()
        formdata.append('file', file)
        try {
            const res: FileItem = await request.post(`/api/hr-appendixes/upload-archives-manage`, formdata)
            resolve(res)
        } catch (error) {
            reject(new Error('上传失败' + error))
        }
    })
}

/**
 * 上传普通附件
 * @param file
 * @returns FileItem
 */
export const uploadFile = (file) => {
    return new Promise<FileItem>(async (resolve, reject) => {
        const formdata = new FormData()
        formdata.append('file', file)
        try {
            const res: FileItem = await request.post(`/api/hr-appendixes/upload-single-file`, formdata)
            resolve(res)
        } catch (error) {
            reject(new Error('上传失败' + error))
        }
    })
}
