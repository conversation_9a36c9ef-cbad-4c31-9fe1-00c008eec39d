import { InputNumber } from 'ant-design-vue'
import { h } from 'vue'

export const formatDynamicCols = (cols, callback, showText = false, pagination) => {
    return cols.map((i) => ({
        title: i.value,
        dataIndex: i.key,
        children: i.children && i.children.length ? formatDynamicCols(i.children, callback, showText, pagination) : undefined,
        width: i.children && i.children.length ? i.children.length * 110 : 120,
        align: 'center',
        customRender: ({ record, text, index }) => {
            record[i.key] = record.hrBillDetailItemsList?.find((v) => v.expenseName == i.key)?.amount || 0
            return showText
                ? text
                : h(InputNumber, {
                      value: text,
                      onChange: (e) => {
                          callback(e, (pagination.value.current - 1) * pagination.value.pageSize + index, i.key, true)
                      },
                      style: {
                          width: '100%',
                      },
                  })
        },
    }))
}

//formatCompareDynamicCols 递归children
export const recursionChild = (item, color) => {
    return item.map((item, key) => ({
        //    columnChildrenTitleList.value.push({
        title: item.title,
        dataIndex: item.dataIndex,
        key: item.key,
        align: 'center',
        width: item.children?.length ? item.children?.length * 110 : 120,
        customHeaderCell: (column) => {
            return {
                style: {
                    //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                    color: item.color ? '#FFFFFF' : '#000000', //如果color
                    'background-color': item.color ? item.color : '#FFFFFF',
                },
            }
        },
        // 只有children 的数据才显示red
        customRender: (record, row, index) => {
            if (color == '#eb3333') {
                return h(
                    'div',
                    {
                        style: {
                            color: !isNaN(record.text) && parseFloat(record.text) < 0 ? 'red' : 'black',
                        },
                    },
                    record.text,
                )
            } else {
                return h(
                    'div',
                    {
                        style: {
                            color: 'black',
                        },
                    },
                    record.text,
                )
            }
        },
        // slots: { customRender: 'redText' },
        children: item.children && item.children?.length ? recursionChild(item.children, color) : undefined,
        //    })
    }))
}
export const formatDynamicColns = (res, columnList) => {
    columnList.value = []
    res.dynamicHeaders.map((item, key) => {
        columnList.value.push({
            title: item.title,
            dataIndex: item.dataIndex,
            key: item.key,
            align: 'center',
            fixed: key < 4 ? 'left' : '',
            width: item.children?.length ? item.children?.length * 110 : 120,
            customHeaderCell: (column) => {
                return {
                    style: {
                        //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                        color: item.color ? '#FFFFFF' : '#000000', //如果color
                        'background-color': item.color ? item.color : '#FFFFFF',
                    },
                }
            },
            // slots: { customRender: 'redText' },
            children: item.children && item.children?.length ? recursionChild(item.children, item.color) : undefined,
        })
    })
}

export const formatCompareDynamicCols = (cols, data) => {
    const res: Recordable[] = []
    cols.forEach((i, idx) => {
        data[i.key] = i.value
        res.push({
            title: idx === 0 ? '动态表头列' : '',
            dataIndex: i.key,
            colSpan: idx === 0 ? cols.length : 0,
            width: 120,
        })
    })
    return {
        columns: res,
        data,
    }
}

export const formatDynamicHeaders = (res) => {}
