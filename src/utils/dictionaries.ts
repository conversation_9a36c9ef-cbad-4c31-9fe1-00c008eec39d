export const customerTypeOptions = <LabelValueOptions>[
    {
        value: 0,
        label: '过期客户',
    },
    { value: 1, label: '异常客户' },
    { value: 2, label: '正常客户' },
    { value: 3, label: '潜在客户' },
    { value: 4, label: '待期客户' },
    { value: 5, label: '多协议客户' },
]
export const stateOptions = <LabelValueOptions>[
    {
        value: 0,
        label: '生效中',
    },
    { value: 1, label: '即将过期(已续签)' },
    { value: 5, label: '即将过期(未续签)' },
    { value: 2, label: '已过期(未续签)' },
    { value: 3, label: '已终止' },
    { value: 4, label: '未生效' },
]

// 新协议
export const oldStateOption = [
    { value: 2, label: '已过期(未续签)' },
    { value: 6, label: '已过期(已续签)' },
]

// // 服务费类型
// export const serviceFeeType = <LabelValueOptions>[
//     {
//         value: 1,
//         label: '每人每月',
//     },
//     { value: 2, label: '打包价' },
//     { value: 3, label: '半年支付' },
// ]

export const userStatusList = <LabelValueOptions>[
    {
        label: '禁用',
        value: 0,
    },
    {
        label: '启用',
        value: 1,
    },
]

// 档案类型

export const archivesTypeList = <LabelValueOptions>[
    {
        label: '员工档案',
        value: '0',
    },
    {
        label: '客户档案',
        value: '1',
    },
]
// // 服务费类型
// export const serviceFeeType = [
//     {
//         label: '每人每月',
//         value: '1',
//     },
//     {
//         label: '打包价',
//         value: '2',
//     },
//     {
//         label: '半年支付',
//         value: '3',
//     },
// ]

// 档案状态
export const archivesStatusList = <LabelValueOptions>[
    {
        label: '未归档',
        value: '0',
    },
    {
        label: '已归档',
        value: '1',
    },
    {
        label: '外借中',
        value: '2',
    },
    {
        label: '未提档',
        value: '3',
    },
    {
        label: '已提档',
        value: '4',
    },
]

// 文件类型
export const fileTypeList = <LabelValueOptions>[
    {
        label: '不限',
        value: '0',
    },
    {
        label: '图片',
        value: '1',
    },
    {
        label: '文件',
        value: '2',
    },
]

export const staffTypeOptions = <LabelValueOptions>[
    { label: '正式工', value: 1 },
    { label: '实习工', value: 2 },
    { label: '试用工', value: 3 },
]
// 短信-模板类型
export const templateTypeOptions = <LabelValueOptions>[
    {
        label: '短信通知/推广短信',
        value: 0,
    },
    {
        label: '短信验证码',
        value: 1,
    },
]

// 短信-状态
export const statusOptions = <LabelValueOptions>[
    {
        label: '启用',
        value: 0, // false
    },
    {
        label: '禁用',
        value: 1, // true
    },
]

// 试卷管理-出题形式
export const modeOptions = <LabelValueOptions>[
    { label: '自定义', value: '0' },
    { label: '系统随机', value: '1' },
]

// 试卷管理-时间限制
export const durationOptions = <LabelValueOptions>[
    { label: '不限', value: '0' },
    { label: '限制', value: '1' },
]

// 档案操作类型
export const ctTypeList = <LabelValueOptions>[
    {
        label: '调出',
        value: 0,
    },
    {
        label: '调入',
        value: 1,
    },
    {
        label: '归还',
        value: 2,
    },
]

// 福利状态
export const izInsuredList = <LabelValueOptions>[
    {
        label: '未参保',
        value: 0,
    },
    {
        label: '已参保',
        value: 1,
    },
    {
        label: '停保',
        value: 2,
    },
]

// 账单类型
export const billTypeList = <LabelValueOptions>[
    {
        label: '薪酬账单',
        value: 0,
    },
    {
        label: '保障账单',
        value: 1,
    },
    {
        label: '其他账单',
        value: 2,
    },
    {
        label: '中石化费用统计账单',
        value: 3,
    },
]
// 账单状态
export const billStateList = <LabelValueOptions>[
    {
        label: '未锁定',
        value: 0,
    },
    {
        label: '已锁定',
        value: 1,
    },
]

// 性别
export const sexList = <LabelValueOptions>[
    {
        label: '男',
        value: 1,
    },
    {
        label: '女',
        value: 2,
    },
]

// 材料类型
export const materialTypeList = <LabelValueOptions>[
    {
        label: '电子件',
        value: 0,
    },
    {
        label: '实体件',
        value: 1,
    },
]
// 费用审核 0是待审核  1是审核通过  2是审核拒绝
export const feeReviewStatus = <LabelValueOptions>[
    {
        label: '待审核',
        value: 0,
    },
    {
        label: '审核通过',
        value: 1,
    },
    {
        label: '审核拒绝',
        value: 2,
    },
    {
        label: '待确认作废',
        value: 3,
    },
    {
        label: '已作废',
        value: 4,
    },
]

// 导盘类型
export const exportTypeList = [
    {
        label: '社保',
        value: 0,
    },
    {
        label: '医保',
        value: 1,
    },
    {
        label: '公积金',
        value: 2,
    },
    {
        label: '个税',
        value: 3,
    },
    {
        label: '第三方账单',
        value: 4,
    },
]

// 差额类型
export const differenceTypeList = [
    { label: '差额<0', value: 2 },
    { label: '差额=0', value: 0 },
    { label: '差额>0', value: 1 },
]

// 审批流程
export const approveStatusList = [
    {
        label: '未进入审批节点',
        value: 0,
    },
    {
        label: '客服经理待审批',
        value: 1,
    },
    {
        label: '会计待审批',
        value: 2,
    },
    /* {
      label: '总经理待审批',
      value: 3,
  },
  {
      label: '会计待确认',
      value: 4,
  }, */
    {
        label: '审批通过',
        value: 5,
    },
    {
        label: '审批未通过',
        value: 6,
    },
    {
        label: '已作废',
        value: 7,
    },
]
// 发票状态
export const invoiceStatusList = [
    {
        label: '未开票',
        value: 0,
    },
    {
        label: '已开票',
        value: 1,
    },
]
// 凭证状态
export const accountingVoucherStatusList = [
    {
        label: '未开',
        value: 0,
    },
    {
        label: '成功',
        value: 1,
    },
    {
        label: '失败',
        value: 2,
    },
]
// 发票性质
export const invoiceTypeList = [
    {
        label: '纸质',
        value: 0,
    },
    {
        label: '电子',
        value: 1,
    },
]
export const dataModificationApplyTypeList = [
    { label: '基本信息', value: 1 },
    { label: '工作经历', value: 2 },
    { label: '教育经历', value: 3 },
    { label: '应试经历', value: 4 },
    { label: '家庭成员', value: 5 },
    { label: '紧急联系人', value: 6 },
    { label: '语言能力', value: 7 },
    { label: '专业能力', value: 8 },
    { label: '职业(工种)资格', value: 9 },
    { label: '职业技术能力', value: 10 },
    { label: '证书', value: 11 },
    { label: '工资卡', value: 12 },
]
export const formItemTypeList = [
    {
        label: '单行输入类',
        value: 'string',
    },
    {
        label: '单项选择类',
        value: 'change',
    },
    {
        label: '多行输入类',
        value: 'textarea',
    },
    {
        label: '开关选择类',
        value: 'switch',
    },
    {
        label: '日期区间选择类',
        value: 'rangePicker',
    },
    {
        label: '日期选择类',
        value: 'date',
    },
    {
        label: '表格输入类',
        value: 'table',
    },
    {
        label: '附件上传类',
        value: 'appendix',
    },
    {
        label: '图片上传类',
        value: 'img',
    },
]
// 体检/考察结果
export const resultList = [
    {
        label: '不合格',
        value: 0,
    },
    {
        label: '合格',
        value: 1,
    },
]
// 报销申请状态列表
export const reimbursementApproveStatusList = [
    {
        label: '未发起',
        value: 0,
    },
    {
        label: '待客服经理审批',
        value: 1,
    },
    /* {
      label: '待总经理审批',
      value: 2,
  },
  {
      label: '待财务负责人审批',
      value: 3,
  },
  {
      label: '待总裁审批',
      value: 4,
  },
  {
      label: '待监事会主席审批',
      value: 5,
  },
  {
      label: '待董事长审批',
      value: 6,
  }, */
    {
        label: '待会计确认',
        value: 7,
    },
    {
        label: '审批通过',
        value: 8,
    },
    {
        label: '审批拒绝',
        value: 9,
    },
    {
        label: '已作废',
        value: 10,
    },
]
// 试卷管理-试卷类型
export const paperOptions = <LabelValueOptions>[
    { label: '笔试', value: 0 },
    { label: '面试', value: 1 },
]

// 客户协议-服务费类型
export const serviceChargeOptions = <LabelValueOptions>[
    { label: '固定服务费', value: 0 },
    { label: '浮动服务费', value: 1 },
]

// 客户协议-服务费计算公式
export const calculationOptions = <LabelValueOptions>[
    { label: '应发工资', value: 0 },
    { label: '总金额', value: 1 },
    { label: '应发工资+单位社保小计+单位公积金小计-单位社保补差', value: 2 },
]

// 员工合同服务-状态
export const contractLookTypeOptions = <LabelValueOptions>[
    { label: '查看', value: 1 },
    { label: '下载', value: 2 },
]

// 续签服务-状态
export const renewalStateOptions = <LabelValueOptions>[
    { label: '待发合同', value: 0 },
    { label: '等待员工续签', value: 1 },
    { label: '续签待审核', value: 2 },
    { label: '续签失败', value: 3 },
    { label: '续签成功', value: 4 },
]

// 客户协议-开票内容类型
export const invoiceTypeOptions = <LabelValueOptions>[
    { label: '按分项金额开票', value: 1 },
    { label: '按总金额开票', value: 2 },
]

// 生育服务-生育津贴管理-状态
export const childbirthAllowanceStateOptions = <LabelValueOptions>[
    { label: '待审核', value: 1 },
    { label: '审核未通过', value: 3 },
    { label: '办理中', value: 4 },
    { label: '办理完成', value: 5 },
]

export const costContent = <LabelValueOptions>[
    { label: '代缴社保', value: '1' },
    { label: '代缴医保', value: '2' },
    { label: '代缴公积金', value: '3' },
    { label: '代发工资', value: '4' },
]

export const salaryPostStatus = <LabelValueOptions>[
    { label: '已发放', value: '1' },
    // { label: '部分发放', value: '2' },
    { label: '未发放', value: '3' },
]

// 客商类型
export const merchantstype = <LabelValueOptions>[
    { label: '客户', value: 1 },
    { label: '供应商', value: 2 },
    { label: '客商', value: 3 },
]

// 对账锁定状态
export const lockStatusList = <LabelValueOptions>[
    { label: '已锁定', value: 1 },
    { label: '未锁定', value: 0 },
]
// 客户协议-归档状态
export const agreementArchiveStatusList = <LabelValueOptions>[
    { label: '未归档', value: 0 },
    { label: '已归档', value: 1 },
]
export const staffStatus = <LabelValueOptions>[
    {
        label: '待入职',
        value: 1,
    },
    {
        label: '入职中',
        value: 2,
    },
    {
        label: '实习中',
        value: 3,
    },
    {
        label: '在职',
        value: 4,
    },
    {
        label: '离职',
        value: 5,
    },
    {
        label: '退休',
        value: 6,
    },
    {
        label: '产假',
        value: 7,
    },
    {
        label: '医疗期',
        value: 8,
    },
    {
        label: '试用中',
        value: 9,
    },
    {
        label: '待确认离职',
        value: 10,
    },
    {
        label: '离职中',
        value: 11,
    },
    {
        label: '借调中',
        value: 12,
    },
]
