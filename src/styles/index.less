@import 'ant-design-vue/dist/antd.less';
@import './const.less';
@import '@surely-vue/table/dist/index.css';

#app {
    width: 100vw;
    height: 100vh;
}

p {
    margin: 0;
    padding: 0;
}

.flexStart {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.searchBar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.searchItem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;

    &>.name {
        margin-right: 10px;
    }

    &>.input {
        width: 200px;
    }
}

.searchBtn {
    margin-bottom: 10px;
}

// 表格上方按钮
.btns {
    margin: 10px 0;

    &>button {
        margin-right: 10px;
    }
}

// 主题class
.lightTheme {
    background-color: @light-color  !important;
    color: rgba(0, 0, 0, 0.85) !important;
    border-color: #eeeeee !important;
}

.darkTheme {
    background-color: @dark-color  !important;
    color: white !important;
    border-color: #195185 !important;
}

// 滚动条样式
.scrollView {
    &::-webkit-scrollbar {
        width: 6px;
    }

    /* 滚动槽 */
    &::-webkit-scrollbar-track {
        box-shadow: inset006pxrgba(0, 0, 0, 0.3);
        -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
        border-radius: 6px;
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.1);
        box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
    }

    &::-webkit-scrollbar-thumb:window-inactive {
        background: #eeeeee;
    }
}

.ant-btn.ant-btn-dangerous {
    background-color: @dangerous-color;
    border-color: @dangerous-color;
}

.ant-btn.yellow {
    background-color: @warning-color;
    border-color: @warning-color;
}

.ant-btn.green {
    background-color: @upload-color;
    border-color: @upload-color;
}

.ant-table-fixed {
    .ant-table-thead>tr>th {
        background-color: @primary-color;
        color: #fff;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
        background-color: @upload-color;
        border-color: @upload-color;
    }
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
    padding: 10px;
}

.smallTable .ant-table-thead>tr>th,
.smallTable .ant-table-tbody>tr>td {
    padding: 5px;
}

// antd Drawer ant-drawer-body
.ant-drawer-body {
    padding: 0 24px;
}

.ant-fullcalendar-cell {
    padding: 0;
}

.ant-fullcalendar-fullscreen .ant-fullcalendar-column-header {
    text-align: center;
    font-size: 16px;
    padding-bottom: 20px;
}

// 文字不可选择
.ant-menu-title-content,
.noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

// other color btn
.success-btn {
    background: @success-color;
    color: white;
    border-color: @success-color;

    &:hover,
    &:focus {
        background: @success-color;
        border-color: @success-color;
        color: white;
        opacity: 0.8;
    }

    &:active {
        opacity: 1;
    }
}

.warning-btn {
    background: @warning-color;
    color: white;
    border-color: @warning-color;

    &:hover,
    &:focus {
        background: @warning-color;
        border-color: @warning-color;
        color: white;
        opacity: 0.8;
    }

    &:active {
        opacity: 1;
    }
}

// 排序时表头标题的背景色
.ant-table-thead>tr>th.ant-table-column-has-actions.ant-table-column-has-sorters:hover {
    background: @primary-color;
    opacity: 0.9;
}

.ant-table-thead>tr>th .ant-table-column-sorter .ant-table-column-sorter-inner {
    color: white;
}


.uploadBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

    .item {
        width: 110px;
        height: 110px;
        // border: 1px solid #ccc;
        border-radius: 6px;
        cursor: pointer;
        color: white;
        margin-right: 10px;
        margin-bottom: 34px;
        text-align: center;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        position: relative;

        .img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 4px;
            border: 1px solid #ccc;
            background: white;
        }

        .after {
            position: absolute;
            top: 105%;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            // background: #eee;
        }

        .txt {
            width: 100px;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
        }

        &:hover {
            .icon {
                transform: scale(1);
            }
        }

        .icon {
            font-size: 40px;
            transform: scale(0.8);
            // color: #eee;
            transition: all 0.3s;
        }
    }
}

.ant-select-tree-dropdown {
    max-height: 200px !important;
}

.ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.redText {
    color: @dangerous-color !important;
}

.lightRow {
    background: white;
}

.darkRow {
    background: #eeeeee;
}

.noHover tr:hover td {
    background: transparent !important;
}

.surely-table-header-cell {
    background-color: @primary-color  !important;
    color: white !important;
}

.surely-table-cell-group-title {
    background-color: @primary-color  !important;
}