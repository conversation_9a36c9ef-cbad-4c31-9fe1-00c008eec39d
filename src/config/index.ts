export default {
    // Document Title
    title: '人力资源服务平台',
    // Base Url
    baseUrl: getBaseUrl(),
    // home path
    homePath: '/home',
    // login path
    loginPath: '/login',
    // home common function icon base path
    iconPath: import.meta.env.MODE == 'production' ? import.meta.env.VITE_ICON_PATH : 'https://hr-minio.cas-air.cn/menu-icon/',
    // left side menu iconPath base path
    menuPath: import.meta.env.MODE == 'production' ? import.meta.env.VITE_MENU_PATH : 'https://hr-minio.cas-air.cn/icon/',
    // preview file prefix
    prefix: 'https://hr-server.hdqhr.com:10443/kkfileview/onlinePreview?url=',
    //是否进行数据加密
    isEncrypt: import.meta.env.MODE == 'production',
}

export function getBaseUrl() {
    const envs = {
        MIAO: 'http://172.16.0.187:10023',
        XIAO: 'http://172.16.1.18:10023',
        QIAO: 'http://172.16.0.194:10023',
        DEV_1: 'https://hr-server.cas-air.cn/dev-1.0',
        DEV_2: 'https://hr-server.cas-air.cn/dev-2.0',
        DEV_3: 'https://hr-server.cas-air.cn/dev-3.0',
        DEV_3_4_2: 'https://hr-server.cas-air.cn/dev-1.0',
        DEV_3_4_3: 'https://hr-server.cas-air.cn/dev-2.0',
        DEV_3_4_4: 'https://hr-server.cas-air.cn/dev-3.0',
        DEV_3_4_5: 'https://hr-server.cas-air.cn/dev-1.0',
        // DEV_3_4_6: 'https://hr-server.cas-air.cn/dev-2.0',
        DEV_3_4_6: 'http://localhost:10023',
        // RELEASE: `https://hr-server.hdqhr.com:10443/server`,   //禁用
    }
    return import.meta.env.MODE == 'production' ? import.meta.env.VITE_BASE_URL : envs['DEV_3_4_6']
    // envs['DEV_3_4_2']
}
