<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <WangEditor :value="formData.myEditor" @on-change="handleChange" ref="editorRef" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'

export default defineComponent({
    name: 'RichTextModal',
    components: {},
    props: {
        title: String,
        item: {
            type: String,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs(props)

        // Form Data
        const formData = ref<any>({})
        const editorRef = ref()
        watch(visible, () => {
            if (visible.value) {
                nextTick(() => {
                    // console.log(editorRef.value)
                    editorRef.value.setHtml(item.value)
                })
            }
        })

        const handleChange = (html) => {
            // console.log(html)
            formData.value.myEditor = html
        }

        // confirm handle
        const confirm = () => {
            cancel()
            emit('confirm', formData.value.myEditor)
            // console.log(formData.value.myEditor)
        }
        // cancel handle
        const cancel = () => {
            editorRef.value.setHtml('')
            emit('cancel')
        }

        return {
            cancel,
            confirm,
            handleChange,
            formData,
            editorRef,
        }
    },
})
</script>
