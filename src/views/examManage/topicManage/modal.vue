<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'900px'">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules" class="form-flex">
            <template v-for="i in myOptions" :key="i">
                <MyFormItem :item="i" v-model:value="formData[i.name]" :width="i.width">
                    <template #select>
                        <PostTree v-model:value="formData.applicablePostList" :itemForm="i" />
                    </template>
                    <template #title>
                        <div class="titleSpan">
                            <Input v-model:value="formData.title" style="width: 100%; margin-right: 10px" />
                            <Tooltip placement="top" :getPopupContainer="getPopupContainer" v-if="formData.questionType == 3">
                                <template #title>
                                    <span>请在填空处输入“##”，相邻两个填空之间请以“，”隔开，如：##，##</span>
                                </template>
                                <QuestionCircleOutlined />
                            </Tooltip>
                        </div>
                    </template>
                    <template #option>
                        <!-- 选项 -->
                        <div class="other-slots">
                            <Button v-if="formData.questionType !== 2" type="primary" @click="addOptions">新增</Button>
                            <div
                                class="other-info"
                                v-for="(optionsDataItem, index) in optionsDataList"
                                :key="index"
                                :style="formData.questionType !== 2 ? 'margin-top: 24px' : 'margin-bottom: 24px'"
                            >
                                <!-- <template v-if="formData.questionType !== 2"> -->
                                <Input
                                    class="other-inputLeft"
                                    v-model:value="optionsDataItem.prefix"
                                    :readonly="formData.questionType == 2"
                                />
                                <Input
                                    class="other-inputMiddle"
                                    v-model:value="optionsDataItem.content"
                                    :readonly="formData.questionType == 2"
                                />
                                <Select
                                    style="width: 15%"
                                    :options="optionsList"
                                    v-model:value="optionsDataItem.isCorrect"
                                    placeholder="请选择"
                                />
                                <MinusCircleOutlined style="margin-left: 10px; color: #ef5959" @click="delOptions(index)" />
                                <!-- </template> -->
                            </div>
                        </div>
                    </template>
                    <template #answer>
                        <!-- 答案 -->
                        <div class="other-slots">
                            <template v-if="formData.questionType !== 2">
                                <Button type="primary" @click="addAnswer">新增</Button>
                                <div
                                    class="other-info"
                                    v-for="(answerDataItem, index) in answerDataList"
                                    :key="index"
                                    style="margin-top: 24px"
                                >
                                    <InputNumber
                                        class="other-inputLeft"
                                        style="width: 55px"
                                        v-model:value="answerDataItem.prefix"
                                        :min="1"
                                        :max="10"
                                    />
                                    <Input class="other-inputMiddle" v-model:value="answerDataItem.content" />
                                    <MinusCircleOutlined style="margin-left: 10px; color: #ef5959" @click="delAnswer(index)" />
                                </div>
                            </template>
                            <!-- 判断 -->
                            <template v-else>
                                <Select
                                    style="width: 15%"
                                    :options="optionsList"
                                    v-model:value="optionsDataList[0].isCorrect"
                                    placeholder="请选择"
                                />
                            </template>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <!-- 富文本弹窗 -->
        <RichTextModal
            :visible="showRichTextEdit"
            :title="modalRichTextTitle"
            :item="currentRichTextValue"
            @cancel="modalRichTextCancel"
            @confirm="modalRichTextConfirm"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { MinusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import RichTextModal from './richTextModal.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'

export default defineComponent({
    name: 'TopicMangeModal',
    components: { MinusCircleOutlined, RichTextModal, PostTree, QuestionCircleOutlined },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        questionTypeList: {
            type: Array,
        },
        // questionProList: {
        //     type: Array,
        // },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-questions'
        const { item, visible, questionTypeList } = toRefs(props)
        //显示选项
        const showAnswer = ref<Boolean>(true)
        //显示填空、判断答案
        const showOption = ref<Boolean>(true)
        //显示简答答案
        const showCorrect = ref<Boolean>(true)
        const selectChanged = (value: string, option: any, data: string) => {
            console.log(value, option, data)
            if (value == '0' || value == '1') {
                console.log('单选多选')
                showAnswer.value = true
                showOption.value = false
                showCorrect.value = false
                console.log(formData.value.questionCont)
                if (formData.value?.questionCont && formData.value?.questionCont != '[]') {
                    let strToObj = JSON.parse(formData.value.questionCont)
                    optionsDataList.value = strToObj
                } else {
                    optionsDataList.value = []
                    optionsDataList.value.push(
                        { prefix: 'A', content: '', isCorrect: 0 },
                        { prefix: 'B', content: '', isCorrect: 0 },
                    )
                    // console.log(optionsDataList.value)
                }
                // console.log(optionsDataList.value)
                optionsDataList.value?.map((val) => {
                    // console.log(formData.value?.correct)
                    if (formData.value?.correct?.indexOf(val.prefix) != -1) {
                        val.isCorrect = 1 //正确
                    } else {
                        val.isCorrect = 0 //错误
                    }
                })
            } else if (value == '2') {
                console.log('判断')
                showAnswer.value = false
                showOption.value = true
                showCorrect.value = false
                optionsDataList.value = []
                optionsDataList.value.push(
                    // { prefix: 'A', content: '正确', isCorrect: 0 },
                    // { prefix: 'B', content: '错误', isCorrect: 0 },
                    { isCorrect: 0 },
                )
                if (data == '正确') {
                    optionsDataList.value[0].isCorrect = 1
                    // optionsDataList.value[1].isCorrect = 0
                } else {
                    // optionsDataList.value[1].isCorrect = 1
                    optionsDataList.value[0].isCorrect = 0
                }
            } else if (value == '3') {
                console.log('填空')
                showAnswer.value = false
                showOption.value = true
                showCorrect.value = false
                if (formData.value?.questionCont && formData.value?.questionCont != '[]') {
                    let strToObj = JSON.parse(formData.value.questionCont)
                    answerDataList.value = strToObj
                    // console.log(answerDataList.value)
                    // let list = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
                    // let myStrToObj = strToObj?.map((item) => {
                    //     let myPrefix = list.findIndex((value) => value == item.prefix)
                    //     console.log(myPrefix)
                    //     return {
                    //         prefix: myPrefix + 1,
                    //         content: item.content,
                    //     }
                    // })
                    // // console.log(myStrToObj)
                    // answerDataList.value = myStrToObj
                } else {
                    answerDataList.value = []
                    answerDataList.value.push(
                        { prefix: '1', content: '', isCorrect: 0 },
                        { prefix: '2', content: '', isCorrect: 0 },
                    )
                }
            } else if (value == '4' || value == '5' || value == '6' || value == '7') {
                console.log('简答论述材料写作面试')
                showAnswer.value = false
                showOption.value = false
                showCorrect.value = true
            }
            formValidateOptional(['questionType'])
        }

        // 富文本弹窗
        const showRichTextEdit = ref(false)
        const modalRichTextTitle = ref('题目名称')
        const currentRichTextValue = ref(null)
        //点击题目名称输入框
        const clickTitle = () => {
            showRichTextEdit.value = true
            modalRichTextTitle.value = '题目名称'
            currentRichTextValue.value = formData.value.title
        }
        //点击答案解析输入框
        const clickAnalysis = () => {
            showRichTextEdit.value = true
            modalRichTextTitle.value = '答案解析'
            currentRichTextValue.value = formData.value.answerAnalysis
        }
        const modalRichTextCancel = () => {
            showRichTextEdit.value = false
            currentRichTextValue.value = null
        }
        const modalRichTextConfirm = (data) => {
            if (modalRichTextTitle.value.includes('题目名称')) {
                formData.value.title = data
            } else {
                formData.value.answerAnalysis = data
            }
        }

        const myOptions = ref([
            {
                label: '题目类型',
                name: 'questionType',
                ruleType: 'number',
                type: 'change',
                options: questionTypeList,
                onChange: selectChanged,
            },
            {
                label: '题目属性',
                name: 'questionPro',
                // type: 'change',
                // options: questionProList,
            },
            {
                label: '适用岗位',
                name: 'applicablePostList',
                slots: 'select',
                default: [],
                type: 'slots',
                ruleType: 'array',
                maxTag: 1,
            },
            {
                label: '题目名称',
                name: 'title',
                width: '100%',
                type: 'slots',
                slots: 'title',
                // onClick: clickTitle,
            },
            {
                label: '分值',
                name: 'score',
                type: 'number',
                ruleType: 'number',
                showbr: true, //换行
            },
            {
                label: '选项',
                name: 'option',
                type: 'slots',
                slots: 'option',
                required: false,
                width: '100%',
                show: showAnswer,
            },
            {
                label: '答案',
                name: 'answer',
                type: 'slots',
                slots: 'answer',
                required: false,
                width: '100%',
                show: showOption,
            },
            {
                label: '答案',
                name: 'correct',
                type: 'textarea',
                width: '100%',
                show: showCorrect,
            },
            {
                label: '答案解析',
                name: 'answerAnalysis',
                required: false,
                type: 'textarea',
                width: '100%',
                // onClick: clickAnalysis,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                console.log(formData.value)
                if (formData.value.applicablePost) {
                    let myApplicablePostList = formData.value?.applicablePost.split(',')
                    formData.value.applicablePostList = [...myApplicablePostList]
                    formData.value.answerAnalysis = formData.value?.answerAnalysis
                        ?.replace(/<[^<>]+>/g, '')
                        .replace(/&nbsp;/gi, '')
                }

                selectChanged(formData.value?.questionType, {}, formData.value.correct)
            }
        })
        // 选项
        const optionsDataList = ref<any>([
            {
                prefix: 'A',
                content: '',
                isCorrect: 0,
            },
            {
                prefix: 'B',
                content: '',
                isCorrect: 0,
            },
        ])
        // 答案
        const answerDataList = ref<any>([])
        //新增选项
        const addOptions = () => {
            console.log(optionsDataList.value)
            let myLength: any = optionsDataList.value.length
            let a: any = String.fromCharCode('A'.charCodeAt(0) + myLength)
            if (optionsDataList.value.length < 10) {
                optionsDataList.value.push({ prefix: a, content: '', isCorrect: 0 })
            } else {
                message.error('最多只能有10个选项')
            }
        }
        //删除选项
        const delOptions = (index) => {
            // console.log(index)
            if (optionsDataList.value.length > 2) {
                optionsDataList.value.splice(index, 1)
            } else {
                message.error('至少有两个选项')
            }
        }
        //新增答案
        const addAnswer = () => {
            console.log(answerDataList.value)
            if (answerDataList.value.length < 10) {
                answerDataList.value.push({ prefix: answerDataList.value.length + 1, content: '', isCorrect: 0 })
            } else {
                message.error('最多只能有10个答案')
            }
        }
        //删除答案
        const delAnswer = (index) => {
            if (answerDataList.value.length > 1) {
                answerDataList.value.splice(index, 1)
            } else {
                message.error('至少有一个答案')
            }
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    let myList: any = []
                    let isCorrectList = optionsDataList.value.filter((val) => val.isCorrect == 1)
                    // console.log(isCorrectList)
                    if (formData.value.questionType == 0) {
                        // 单选
                        if (isCorrectList.length == 0) {
                            return message.error('至少有一个正确答案')
                        } else if (isCorrectList.length > 1) {
                            return message.error('题目类型为单选时只能有一个正确答案')
                        }
                        formData.value.correct = ''
                        for (let i = 0; i < isCorrectList.length; i++) {
                            formData.value.correct += isCorrectList[i].prefix + ','
                        }
                        // console.log(formData.value.correct)
                        myList = [...optionsDataList.value]
                    } else if (formData.value.questionType == 1) {
                        //多选
                        if (isCorrectList.length == 0) {
                            return message.error('至少有一个正确答案')
                        }
                        formData.value.correct = ''
                        for (let i = 0; i < isCorrectList.length; i++) {
                            formData.value.correct += isCorrectList[i].prefix + ','
                        }
                        // console.log(formData.value.correct)
                        myList = [...optionsDataList.value]
                    } else if (formData.value.questionType == 2) {
                        //判断
                        // if (isCorrectList.length == 0) {
                        //     return message.error('至少有一个正确答案')
                        // } else if (isCorrectList.length > 1) {
                        //     return message.error('题目类型为判断时只能有一个正确答案')
                        // }
                        // for (let i = 0; i < isCorrectList.length; i++) {
                        //     formData.value.correct = isCorrectList[i].content
                        // }
                        formData.value.correct = optionsDataList.value[0]?.isCorrect ? '正确' : '错误'
                        console.log(formData.value.correct)
                    } else if (formData.value.questionType == 3) {
                        //填空
                        // console.log(formData.value.title.match(/#{2,}/g)?.length)
                        let isTrue = formData.value.title.match(/#{2,}/g)?.length
                        if (isTrue !== answerDataList.value?.length) {
                            return message.error('填空处设置错误，请按规则设置')
                        }
                        myList = [...answerDataList.value]
                    }
                    // console.log(optionsDataList.value)
                    let array: any = []
                    for (let i = 0; i < myList.length; i++) {
                        if (myList[i].prefix == '' || myList[i].content == '') {
                            return message.error('选项不能为空')
                        }
                        if (array.indexOf(myList[i].prefix) === -1) {
                            array.push(myList[i].prefix)
                        }
                        // console.log(array)
                    }
                    // console.log(array)
                    if (array.length != myList.length) {
                        return message.error('不能有重复选项')
                    }
                    console.log(myList)
                    formData.value.questionItemDTOList = myList
                    await request.put(api || '', { ...formData.value })
                    message.success('编辑成功!')
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            optionsDataList,
            answerDataList,
            addAnswer,
            delAnswer,
            optionsList: [
                {
                    label: '正确',
                    value: 1,
                },
                {
                    label: '错误',
                    value: 0,
                },
            ],
            delOptions,
            addOptions,
            showOption,
            showAnswer,
            showCorrect,
            getPopupContainer: () => {
                return document.body
            },

            // 富文本
            showRichTextEdit,
            modalRichTextTitle,
            currentRichTextValue,
            modalRichTextCancel,
            modalRichTextConfirm,
            formValidateOptional,
        }
    },
})
</script>
<style lang="less">
.titleSpan {
    width: 100%;
    display: flex;
    align-items: center;
}
.other-slots {
    // display: block;
    width: 100%;
    .other-info {
        // margin-top: 24px;
        .other-inputLeft {
            width: 42px;
            margin-right: 9px;
        }
        .other-inputMiddle {
            width: 70%;
            margin-right: 27px;
        }
        .flex-input {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            .input-key {
                margin-top: 10px;
                margin-right: 10px;
            }
        }
    }
}
</style>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        // .ant-form-item-control {
        //     max-width: calc(100% - 130px) !important;
        // }
    }
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
</style>
