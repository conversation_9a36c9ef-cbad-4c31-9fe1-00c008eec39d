<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #applicablePostList="itemForm">
            <PostTree
                v-model:value="params.applicablePostList"
                v-model:itemForm="options[itemForm.index]"
                style="width: 190px"
                @change="searchData"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="ImportData">导入</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-questions/page"
        deleteApi="/api/hr-questions/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :questionTypeList="questionTypeList"
    />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchResData" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'

import modal from './modal.vue'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'TopicManage',
    components: { MyModal: modal, PostTree },
    setup() {
        let questionTypeList = ref<LabelValueOptions>([]) // 获取题目类型
        let questionProList = ref<LabelValueOptions>([]) // 获取题目属性

        // 获取题目属性
        function getProlist() {
            dictionaryDataStore()
                .setDictionaryData('prolist', '/api/hr-questions/prolist', 'get', true)
                .then((data: inObject[]) => {
                    console.log(data)
                    questionProList.value = data.map((item) => {
                        // console.log(item)
                        return { label: item.questionPro, value: item.questionPro }
                    })
                })
        }
        onMounted(() => {
            // 获取题目类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/questionType', {}).then((res) => {
                questionTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // // 获取题目属性
            getProlist()
        })
        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '题目名称',
                key: 'title',
            },
            {
                type: 'select',
                label: '题目类型',
                key: 'questionTypeList',
                options: questionTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '题目属性',
                key: 'questionPro',
                // options: questionProList,
                // multiple: true,
            },
            {
                type: 'selectSlot',
                label: '适用岗位',
                key: 'applicablePostList',
                placeholder: '适用岗位',
                maxTag: 1,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = () => {
            tableRef.value.refresh(1)
        }
        const searchResData = () => {
            getProlist()
            searchData()
        }
        //表格数据
        const columns = [
            {
                title: '题目名称',
                dataIndex: 'title',
                align: 'center',
                customRender: ({ text }) => {
                    return text.replace(/#{2,}/g, '()')
                },
                width: 400,
            },
            {
                title: '题目类型',
                dataIndex: 'questionType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.questionTypeName
                },
                width: 150,
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                width: 150,
            },
            {
                title: '适用岗位',
                dataIndex: 'applicablePost',
                align: 'center',
                customRender: ({ record }) => {
                    return record.applicablePostName
                },
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                getProlist()
                // console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('编辑题目')
        // 当前编辑的数据
        const currentValue = ref(null)
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑题目'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '编辑题目'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-questions/template'
        const importUrl = '/api/hr-questions/import'
        const exportUrl = '/api/hr-questions/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }

        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            selectedRowsArr,
            exportText,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            editRow,
            deleteRow,
            ImportData,
            exportData,
            questionTypeList,
            questionProList,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            searchResData,
        }
    },
})
</script>

<style scoped lang="less"></style>
