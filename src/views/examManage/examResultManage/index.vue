<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <!-- <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="options[itemForm.index]"
                style="width: 190px"
                @change="searchData"
            />
        </template>
        <template #clientIdList="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                v-model:value="params.clientIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
            />
        </template> -->
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-exams/page"
        deleteApi="/api/hr-exams/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :isPaperId="isPaperId"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import { message, Modal } from 'ant-design-vue'
// import request from '/@/utils/request'
// import PostTree from '/@/views/user/postManage/postTree.vue'
// import ClientSelectTree from '/@/components/ClientSelectTree/index'
import request from '/@/utils/request'
import modal from './modal.vue'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'TopicManage',
    components: { MyModal: modal },
    setup() {
        // let questionTypeList = ref<LabelValueOptions>([]) // 获取题目类型
        let paperList = ref<LabelValueOptions>([]) // 获取试卷名称

        onMounted(() => {
            // 获取题目类型
            // request.get('/api/com-code-tables/getCodeTableByInnerName/questionType', {}).then((res) => {
            //     questionTypeList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            // 获取试卷名称
            request.get('/api/hr-exam-results/paper', {}).then((res) => {
                paperList.value = Object.keys(res).map((val) => ({
                    label: val,
                    value: res[val],
                }))
                console.log(paperList.value)
            })
        })
        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '试卷名称',
                key: 'paperIdList',
                options: paperList,
                multiple: true,
            },
            {
                type: 'string',
                label: '考试名称',
                key: 'examName',
                // options: paperList,
                // multiple: true,
            },
            {
                type: 'string',
                label: '岗位名称',
                key: 'professionName',
                // options: paperList,
                // multiple: true,
            },
            // {
            //     type: 'selectSlot',
            //     label: '使用客户',
            //     key: 'clientIdList',
            //     placeholder: '使用客户',
            // },
            // {
            //     type: 'selectSlot',
            //     label: '适用岗位',
            //     key: 'stationIdList',
            //     placeholder: '适用岗位',
            // },
        ]
        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const selectedRowsArr = ref<any>([])
        //表格数据
        const columns = [
            {
                title: '试卷名称',
                dataIndex: 'paperId',
                align: 'center',
                customRender: ({ record }) => {
                    // return record.paperName
                    // console.log(record.examsType)
                    if (record.examsType && record.brochurePaper) {
                        return record.paperName + ',' + record.brochurePaper
                    } else {
                        return record.paperName
                    }
                },
                width: 150,
            },
            // {
            //     title: '使用客户',
            //     dataIndex: 'clientid',
            //     align: 'center',
            //     customRender: ({ record }) => {
            //         return record.clientName
            //     },
            // },
            {
                title: '考试名称',
                dataIndex: 'examName',
                align: 'center',
                // customRender: ({ record }) => {
                //     return record.clientName
                // },
                width: 150,
            },
            {
                title: '岗位名称',
                dataIndex: 'professionName',
                align: 'center',
                // customRender: ({ record }) => {
                //     return record.professionName
                // },
                width: 150,
            },
            {
                title: '试卷及格线',
                dataIndex: 'passLine',
                align: 'center',
                customRender: ({ record }) => {
                    return record.passLine
                },
                width: 100,
                sorter: false,
            },
            {
                title: '考试人数',
                dataIndex: 'examsNumber',
                align: 'center',
                customRender: ({ record }) => {
                    return record.examsNumber
                },
                width: 100,
            },
            {
                title: '笔试通过率',
                dataIndex: 'examsPassingRate',
                align: 'center',
                customRender: ({ record }) => {
                    return record.examsPassingRate ? (record.examsPassingRate * 100).toFixed(1) + '%' : record.examsPassingRate
                },
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            if (selectedRowsArr.value.length <= 0) {
                return message.error('请先选择一条数据')
            }
            let recruitName: any = []
            let examinationName = selectedRowsArr.value?.filter((item) => {
                if (item.examsType == 1) {
                    let name: any = item.brochurePaper ? item.brochurePaper + ',' + item.paperName : item.paperName
                    recruitName.push(name)
                }
                return item.examsType != 1
            })
            let correctIds = examinationName?.map((val) => {
                return val.id
            })
            Modal.confirm({
                title: '确认',
                content: '是否确认删除所选内容？',
                onOk() {
                    if (recruitName.length > 0) {
                        message.warning(`试卷名称 ${recruitName?.join()}，为招聘简章的试卷不能被删除`)
                    }
                    if (correctIds.length > 0) {
                        request
                            .post('/api/hr-exams/deletes', correctIds, { loading: true })
                            .then((ref) => {
                                tableRef.value.refresh(1)
                                message.success('删除成功!')
                            })
                            .catch((ref) => {})
                    }
                },
                onCancel() {
                    console.log('取消')
                },
            })
            // tableRef.value.deleteRow(correctIds).then((ref) => {
            //     console.log(ref)
            // })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增考试结果')
        // 当前编辑的数据
        const currentValue = ref(null)
        const isPaperId = ref<any>([])
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增考试结果'
            currentValue.value = null
            // console.log(tableRef.value.tableData)
            isPaperId.value = tableRef.value.tableData.map((item) => {
                return item.paperId
            })
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑考试结果'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增考试结果'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            showEdit.value = false
            modalTitle.value = '新增'
            currentValue.value = null
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-questions/template'
        const importUrl = '/api/hr-questions/import'
        const exportUrl = '/api/hr-exams/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }

        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            exportText,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            isPaperId,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            ImportData,
            exportData,
            selectedRowsArr,
            //事件
            changeRoleId,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
        }
    },
})
</script>

<style scoped lang="less"></style>
