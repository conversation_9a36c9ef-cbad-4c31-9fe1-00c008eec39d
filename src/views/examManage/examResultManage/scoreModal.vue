<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" title="面试考官打分">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" class="form-flex">
            <!-- <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :className="item.slots">
                    <template #img>111</template>
                </MyFormItem>
                
            </template> -->
            <template v-for="(item, index) in myDataList" :key="item">
                <FormItem :label="'面试考官' + myIndex[index]" name="interviewScore">
                    <InputNumber style="width: 100%" v-model:value="item.interviewScore" placeholder="请输入分数" :min="0" />
                </FormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'ScoreModal',
    props: {
        // title: String,
        item: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //请求
        const api = '/api/hr-stations'
        const { item, visible } = toRefs(props)
        let professionList = ref<LabelValueOptions>([])
        onMounted(() => {})

        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                type: 'number',
                label: '面试考官一',
                name: 'interviewScore',
                // required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        const myDataList: any = ref([])

        // FormData rules 初始值
        // const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>({})
        const myIndex = ref<any>(['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'])
        watch(visible, () => {
            if (visible.value) {
                let dataList: any = []
                dataList = item.value?.split(',').map((val) => {
                    return {
                        interviewScore: val,
                    }
                })
                // console.log(dataList)
                myDataList.value = dataList
            }
        })

        // reset formData
        const resetFormData = () => {
            // formData.value = initFormData
            // formInline.value.resetFields()
            myDataList.value = []
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    console.log(myDataList.value)
                    let myScore = ''
                    myDataList.value.map((val) => {
                        console.log(val.interviewScore)
                        let myInterviewScore = val.interviewScore ? val.interviewScore : 0
                        myScore = myScore + ',' + myInterviewScore
                    })
                    // console.log(myScore)
                    console.log(myScore.substr(1))
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', myScore.substr(1))
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            // rules,
            formData,
            myOptions,
            formInline,
            // industryList,
            professionList,
            myDataList,
            myIndex,
        }
    },
})
</script>
