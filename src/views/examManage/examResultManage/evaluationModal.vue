<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" title="应试评价" :width="'800px'">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" :rules="rules" class="form-flex">
            <div class="detail-wrapper">
                <div class="item-flex">
                    <span class="label">姓名：</span>
                    <span>{{ item?.studentsName }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">岗位：</span>
                    <span>{{ item?.professionName }}</span>
                </div>
            </div>
            <FormItem label="应试环节" name="evaluationStatus" style="width: 50%">
                <Select
                    v-model:value="formData.evaluationStatus"
                    showSearch
                    :options="linkOptions"
                    optionFilterProp="label"
                    allowClear
                    placeholder="请选择应试环节"
                    @change="onChange"
                    :getPopupContainer="getPopupContainer"
                />
            </FormItem>
            <FormItem label="评价内容" name="evaluation" style="width: 100%">
                <Textarea v-model:value="formData.evaluation" :rows="3" allowClear placeholder="请输入评价内容" />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'ScoreModal',
    props: {
        // title: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //请求
        const api = '/api/hr-stations'
        const { item, visible } = toRefs(props)
        const linkOptions = ref<inObject[]>([])
        onMounted(() => {})

        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '应试环节',
                name: 'evaluationStatus',
                type: 'select',
                ruleType: 'number',
            },
            {
                label: '评价内容',
                name: 'evaluation',
                required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(
            item,
            (val, old) => {
                console.log(item.value)
                if (val) {
                    linkOptions.value = []
                    // 获取应试环节
                    dictionaryDataStore()
                        .setDictionaryData('interviewLink', '', 'get', true)
                        .then((data: inObject[]) => {
                            data.forEach((item) => {
                                if (item.itemValue == 1 && val.score)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 2 && val.interviewScoreResult)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 6 && val.addResult)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 7 && val.examResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 8 && val.physicalExaminationResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                            })
                        })
                    console.log(linkOptions.value)
                }
            },
            {
                deep: true,
            },
        )
        const myData: any = ref({})
        const onChange = (val) => {
            // console.log(val)
            myData.value = item.value.hrRegistrationEvaluationList.find((el) => {
                return el.evaluationStatus == val
            })
            formData.value.evaluation = myData.value?.evaluation || ''
            // console.log(myData.value)
            // item.value.hrRegistrationEvaluationList.find((el) => {
            //     return el.evaluationStatus == val
            // })?.evaluation || ''
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    // 表单关闭后的其它操作 如刷新表
                    emit(
                        'confirm',
                        {
                            detailsId: item.value.id,
                            staffId: item.value.staffId,
                            studentsName: item.value.studentsName,
                            evaluation: formData.value.evaluation,
                            evaluationStatus: formData.value.evaluationStatus,
                            id: myData.value?.id || null,
                        },
                        myData.value,
                    )
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            linkOptions,
            getPopupContainer: () => {
                return document.body
            },
            onChange,
            resetFormData,
        }
    },
})
</script>
<style scoped lang="less">
.detail-wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .item-flex {
        margin: 5px 0px;
        display: flex;
    }
    .label {
        text-align: right;
        width: 80px;
        color: rgba(153, 153, 153, 1);
    }
}
</style>
