<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" @confirm="confirm" width="1200px">
        <div class="importFileBox">
            <ImportFile v-model:fileUrls="fileUrls" ref="refImportFile" :multiple="false" />
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { SearchBarOption } from '/#/component'

export default defineComponent({
    name: 'AnswerDetailsModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, visible } = toRefs(props)
        const refImportFile = ref()
        const fileUrls = ref<inObject[]>([])
        watch(
            visible,
            () => {
                if (visible.value) {
                    // getAppendixs(item.value?.id)
                    console.log(item.value?.id)
                }
            },
            { immediate: true },
        )
        const confirm = async () => {
            let myUrl = refImportFile.value.getFileUrls().map((item) => {
                return item.url
            })
            console.log(myUrl.join())
            await request.put('/api/hr-exam-results', {
                appendixUrl: myUrl.join(),
                id: item.value?.id,
            })
            message.success('修改成功!')
            emit('confirm')
            cancel()
        }
        const cancel = () => {
            fileUrls.value = []
            emit('cancel')
        }
        return {
            confirm,
            cancel,
            fileUrls,
            refImportFile,
        }
    },
})
</script>
