<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'1200px'">
        <div class="details-flex" v-show="title == '编辑考试结果'">
            <div class="flex-span">
                <span>试卷名称：</span>
                <div class="flex-text" :title="formData.paperName">{{ formData.paperName }}</div>
            </div>
            <div class="flex-span">
                <span>考试名称：</span>
                <div class="flex-text" :title="formData.examName">{{ formData.examName }}</div>
            </div>
            <div class="flex-span">
                <span>岗位名称：</span>
                <div class="flex-text" :title="formData.professionName">{{ formData.professionName }}</div>
            </div>
            <div class="flex-span">
                <span>试卷及格线：</span>
                <div class="flex-text" :title="formData.passLine">{{ formData.passLine }}</div>
            </div>
            <div class="flex-span">
                <span>考试人数：</span>
                <div class="flex-text" :title="formData.examsNumber">{{ formData.examsNumber }}</div>
            </div>
            <div class="flex-span">
                <span>笔试通过率：</span>
                <div
                    class="flex-text"
                    :title="formData.examsPassingRate ? (formData.examsPassingRate * 100).toFixed(1) + '%' : '0'"
                >
                    {{ formData.examsPassingRate ? (formData.examsPassingRate * 100).toFixed(1) + '%' : '0' }}
                </div>
            </div>
            <div class="flex-line"></div>
        </div>
        <SearchBar v-model="params" :options="options" @change="searchData" :showSelectedLine="isShowSelectedLine">
            <!-- <template #professionName="itemForm"> -->
            <!-- <div v-show="title.includes('编辑')"> -->
            <!-- <PostTree
                    style="width: 190px; margin-right: 10px"
                    :isAll="false"
                    v-model:value="params.professionName"
                    v-model:itemForm="options[itemForm.index]"
                    @change="searchData"
                /> -->
            <!-- </div> -->
            <!-- </template> -->
        </SearchBar>
        <div class="btns">
            <Button type="primary" @click="ImportData" :disabled="myDisabled">导入</Button>
            <Button danger type="primary" @click="deleteRow" :disabled="myDisabled">批量删除</Button>
        </div>

        <BasicTable
            ref="tableRef"
            api="/api/hr-exam-results/page"
            deleteApi="/api/hr-exam-results/deletes"
            :params="{ ...params, paperId: myPaperId, professionName: myProfessionName, examName: myExamName }"
            :columns="columns"
            @getTableData="getTableData"
        >
            <template #score="{ record }">
                <InputNumber
                    :disabled="myDisabled"
                    :style="record.score >= formData.passLine ? 'color: red' : ''"
                    v-model:value="record.score"
                />
            </template>
            <template #interviewScore="{ record }">
                <Button :disabled="myDisabled" type="link" size="small" @click="interviewScoreClick(record)">{{
                    record.interviewScore ? record.interviewScore : ''
                }}</Button>
            </template>
            <!-- <template #interviewScoreResult="{ record }">
                <InputNumber v-model:value="record.interviewScoreResult" />
            </template> -->
            <template #finalResult="{ record }">
                <InputNumber :disabled="myDisabled" v-model:value="record.finalResult" />
            </template>
            <template #addResult="{ record }">
                <InputNumber :disabled="myDisabled" v-model:value="record.addResult" />
            </template>
            <template #examResult="{ record }">
                <Select
                    :disabled="myDisabled"
                    style="width: 100%"
                    :options="resultList"
                    v-model:value="record.examResult"
                    placeholder="请选择"
                />
            </template>
            <template #physicalExaminationResult="{ record }">
                <Select
                    :disabled="myDisabled"
                    style="width: 100%"
                    :options="resultList"
                    v-model:value="record.physicalExaminationResult"
                    placeholder="请选择"
                />
            </template>
            <template #operation="{ record }">
                <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            </template>
            <!-- <template #operation="{ record }">
                <Button
                    type="primary"
                    class="btn"
                    size="small"
                    @click="upload(record)"
                    v-if="record.examType == 0 && !record.appendixUrl"
                    >上传</Button
                >
                <Button
                    type="link"
                    size="small"
                    @click="editRow(record)"
                    v-if="record.examType == 1 || (record.examType == 0 && record.appendixUrl)"
                    >答题详情</Button
                >
            </template> -->
        </BasicTable>
    </BasicEditModalSlot>

    <AnswerDetails
        :visible="showEditDetails"
        :title="modalTitleDetails"
        :item="currentValueDetails"
        :modalType="modalType"
        @cancel="modalCancelDetails"
        @confirm="modalConfirmDetails"
    />
    <ImportModal
        v-model:visible="importVisible"
        :temUrl="importTemUrl"
        :importUrl="importUrl"
        :importUrlParam="importParam"
        @getResData="searchData"
    />
    <UploadAttachments
        :visible="showUpload"
        :title="modalTitleUpload"
        :item="currentValueUpload"
        @cancel="modalCancelUpload"
        @confirm="modalConfirmUpload"
    />
    <ImgModal :visible="imgShowEdit" :title="imgModalTitle" :imageUrl="imageUrl" @cancel="imgModalCancel" :width="'900'" />
    <!-- 面试官打分弹窗 -->
    <ScoreModal :visible="scoreShowEdit" :item="currentValueScore" @cancel="scoreModalCancel" @confirm="scoreModalConfirm" />
    <!-- 应试评价弹窗 -->
    <EvaluationModal
        ref="evaluationModal"
        :visible="evaluationShowEdit"
        :item="evaluationValueScore"
        @cancel="evaluationModalCancel"
        @confirm="evaluationModalConfirm"
    />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted, nextTick } from 'vue'
import request from '/@/utils/request'
import { SearchBarOption } from '/#/component'
import AnswerDetails from './answerDetails.vue'
import UploadAttachments from './uploadAttachments.vue'
import scoreModal from './scoreModal.vue'
import evaluationModal from './evaluationModal.vue'
import { getHaveAuthorityOperation, previewFile } from '/@/utils/index'
// import PostTree from '/@/views/user/postManage/postTree.vue'

export default defineComponent({
    name: 'TopicMangeModal',
    components: { AnswerDetails, UploadAttachments, ScoreModal: scoreModal, EvaluationModal: evaluationModal },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        isPaperId: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { title, item, visible, isPaperId } = toRefs(props)

        let myShowTrue = ref<any>(true)
        let myShowFalse = ref<any>(false)
        let paperList: any = ref<LabelValueOptions>([]) // 试卷名称
        let professionName: any = ref<LabelValueOptions>([]) // 岗位名称
        let resultList: any = ref([
            {
                label: '合格',
                value: 1,
            },
            {
                label: '不合格',
                value: 0,
            },
        ])
        onMounted(() => {
            // 获取岗位名称
            request.get('/api/hr-stations/list', {}).then((res) => {
                professionName.value = res.map((item) => {
                    return { label: item.professionName, value: item.professionName }
                })
            })
        })
        // 获取试卷名称
        const getPaperList = () => {
            let myPaperList = ref<any>([])
            request.get('/api/hr-exam-results/paper', {}).then((res) => {
                myPaperList.value = Object.keys(res).map((val) => ({
                    label: val,
                    value: res[val],
                }))
                for (let i = 0; i < myPaperList.value.length; i++) {
                    // console.log(myPaperList.value[i])
                    // if (isPaperId.value?.indexOf(myPaperList.value[i].value) !== -1) {
                    //     myPaperList.value[i].disabled = true
                    // }
                    if (myPaperList.value[i].label == '入职考试') {
                        myPaperList.value[i].disabled = true
                    }
                }
                paperList.value = [...myPaperList.value]

                // myPaperList.value = Object.keys(res).map((val) => ({
                //     label: val,
                //     value: res[val],
                // }))
                // paperList.value = [...myPaperList.value]
                // console.log(paperList.value)
            })
        }
        // Form Data
        const formData = ref<any>({})
        //筛选
        const params = ref<any>({})
        //试卷id
        const myPaperId = ref<any>('')
        //考试名称
        const myExamName = ref<any>('')
        //岗位名称
        const myProfessionName = ref<any>('')
        //是否显示筛选条件
        const isShowSelectedLine = ref<any>(true)

        watch(visible, () => {
            if (visible.value) {
                if (title.value?.includes('新增')) {
                    isShowSelectedLine.value = false
                    nextTick(() => {
                        getPaperList()
                    })
                } else {
                    isShowSelectedLine.value = true
                    nextTick(() => {
                        tableRef.value.refresh(1)
                        getPaperList()
                    })
                }

                formData.value = { ...item?.value }
                // console.log(params.value)
                myPaperId.value = formData.value?.paperId
                myExamName.value = formData.value?.examName
                myProfessionName.value = formData.value?.professionName
                myDisabled.value = formData.value?.examsType ? true : false

                myShowTrue.value = title.value?.includes('新增')
                myShowFalse.value = !title.value?.includes('新增')
            }
        })

        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '试卷名称',
                key: 'paperId',
                options: paperList,
                show: myShowTrue,
            },
            {
                type: 'string',
                label: '考试名称',
                key: 'examName',
                show: myShowTrue,
            },
            {
                type: 'string',
                label: '单位名称',
                key: 'clientName',
                show: myShowFalse,
            },
            {
                type: 'select',
                label: '岗位名称',
                key: 'professionName',
                options: professionName,
                show: myShowTrue, //新增
            },
            {
                type: 'string',
                label: '考生名称',
                key: 'studentsName',
                show: myShowFalse,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'card',
                show: myShowFalse,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
                show: myShowFalse,
            },
            // {
            //     type: 'daterange',
            //     label: '考试时间',
            //     key: 'examDateList',
            //     show: myShowFalse,
            // },
            // {
            //     type: 'date',
            //     label: '考试结束时间',
            //     key: 'endExamDate;',
            //     show: myShowFalse,
            // },
        ]
        //表格dom
        const tableRef = ref()
        const myDisabled = ref(false)
        const searchData = async () => {
            if (title.value?.includes('新增')) {
                myPaperId.value = params.value.paperId
                myExamName.value = params.value?.examName
                myProfessionName.value = params.value?.professionName
                params.value.isSave = 1
                if (params.value.paperId && params.value.examName && params.value.professionName) {
                    nextTick(() => {
                        tableRef.value
                            .refresh(1)
                            .then((data) => {
                                myDisabled.value = false
                            })
                            .catch((error) => {
                                myDisabled.value = true
                            })
                    })
                }
            } else {
                myPaperId.value = formData.value?.paperId
                myExamName.value = formData.value?.examName
                myProfessionName.value = formData.value?.professionName
                nextTick(() => {
                    tableRef.value.refresh(1)
                })
            }
            // params.value = { ...params.value, ...myPaperId.value }
            // console.log({ ...params.value, ...myPaperId.value })
        }
        //表格数据
        const columns = [
            {
                title: '单位名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 100,
            },
            {
                title: '岗位名称',
                dataIndex: 'professionName',
                align: 'center',
                width: 100,
            },
            {
                title: '考生名称',
                dataIndex: 'studentsName',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'card',
                align: 'center',
                width: 100,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 100,
            },
            // {
            //     title: '考试时间',
            //     dataIndex: 'examDate',
            //     align: 'center',
            //     width: 100,
            // },
            {
                title: '笔试成绩',
                dataIndex: 'score',
                align: 'center',
                slots: { customRender: 'score' },
                width: 120,
            },
            {
                title: '面试考官打分',
                dataIndex: 'interviewScore',
                align: 'center',
                slots: { customRender: 'interviewScore' },
                width: 120,
            },
            {
                title: '面试平均成绩',
                dataIndex: 'interviewScoreResult',
                align: 'center',
                // slots: { customRender: 'interviewScoreResult' },
                customRender: ({ text }) => {
                    return text?.toFixed(2)
                },
                width: 120,
            },
            {
                title: '最终成绩',
                dataIndex: 'finalResult',
                align: 'center',
                slots: { customRender: 'finalResult' },
                width: 120,
            },
            {
                title: '加试成绩',
                dataIndex: 'addResult',
                align: 'center',
                slots: { customRender: 'addResult' },
                width: 120,
            },
            {
                title: '考察结果',
                dataIndex: 'examResult',
                align: 'center',
                slots: { customRender: 'examResult' },
                width: 120,
            },
            {
                title: '体检结果',
                dataIndex: 'physicalExaminationResult',
                align: 'center',
                slots: { customRender: 'physicalExaminationResult' },
                width: 120,
            },
            // {
            //     title: '评价',
            //     dataIndex: 'evaluation',
            //     align: 'center',
            //     slots: { customRender: 'evaluation' },
            //     width: 300,
            // },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]
        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-exam-results/template'
        const importUrl = '/api/hr-exam-results/import'
        const importParam = ref<inObject>({})
        const getTableData = (data) => {
            // console.log(data, 460000000)
        }
        // 导入
        const ImportData = () => {
            if (title.value?.includes('新增')) {
                if (!params.value.paperId) {
                    return message.error('试卷名称不能为空')
                }
                if (!params.value.examName) {
                    return message.error('考试名称不能为空')
                }
                if (!params.value.professionName) {
                    return message.error('岗位名称不能为空')
                }
                importParam.value = {
                    paperId: params.value.paperId,
                    examName: params.value.examName,
                    professionName: params.value.professionName,
                }
            } else {
                importParam.value = {
                    paperId: myPaperId.value,
                    examName: myExamName.value || '',
                    professionName: myProfessionName.value || '',
                }
            }
            importVisible.value = true
        }

        //新增
        const apiAdd = '/api/hr-exams'
        //编辑
        const apiUpdate = '/api/hr-exam-results/list'

        // confirm handle
        const confirm = async () => {
            if (title.value?.includes('新增')) {
                if (!params.value.paperId) {
                    return message.error('试卷名称不能为空')
                }
                if (!params.value.examName) {
                    return message.error('考试名称不能为空')
                }
                if (!params.value.professionName) {
                    return message.error('岗位名称不能为空')
                }

                let myParams: any = {}
                myParams.paperId = params.value.paperId
                myParams.professionName = params.value.professionName
                myParams.examName = params.value.examName
                myParams.hrExamResultList = [...tableRef.value.tableData]
                await request.post(apiAdd || '', { ...myParams })
                message.success('新增成功!')
            } else {
                await request.put(apiUpdate || '', [...tableRef.value.tableData])
                message.success('编辑成功!')
            }
            // cancel()
            tableRef.value.tableData = []
            params.value = {}
            // 表单关闭后的其它操作 如刷新表
            emit('confirm')
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            tableRef.value.tableData = []
            if (title.value?.includes('新增')) {
                deletePaper()
            }
            params.value = {}
            importParam.value.paperId = ''
            myDisabled.value = false
            // resetFormData()
        }

        const deletePaper = () => {
            if (importParam.value.paperId && importParam.value.examName && importParam.value.professionName) {
                request
                    .post('/api/hr-exam-results/remove', {
                        paperId: importParam.value.paperId,
                        examName: importParam.value.examName,
                        professionName: importParam.value.professionName,
                    })
                    .then((res) => {
                        console.log(res)
                    })
            }
        }

        // 答题详情
        const showEditDetails = ref(false)
        const modalTitleDetails = ref('答题详情')
        // 当前编辑的数据
        const currentValueDetails = ref(null)
        const modalType = ref('')
        //查看图片
        const imgShowEdit = ref(false)
        const imgModalTitle = ref('答题详情')
        const imageUrl = ref()
        const editRow = (record) => {
            console.log(record.examType)
            if (record.examType == 0) {
                //线下

                const part = record.appendixUrl.split('.')
                const type = part && part.length ? part[part.length - 1] : null
                console.log(type)
                if (['xlsx', 'xls', 'doc', 'docx', 'csv', 'ppt', 'pptx'].includes(type)) {
                    previewFile(record.appendixUrl)
                } else if (['jpg', 'png', 'jpeg'].includes(type)) {
                    console.log(11111)
                    imgShowEdit.value = true
                    imgModalTitle.value = '答题详情'
                    imageUrl.value = record.appendixUrl
                } else if (['pdf'].includes(type)) {
                    modalType.value = 'pdf'
                    showEditDetails.value = true
                    modalTitleDetails.value = '答题详情'
                }
            } else {
                //线上
                modalType.value = 'table'
                showEditDetails.value = true
                modalTitleDetails.value = '答题详情'
                currentValueDetails.value = { ...record }
            }
        }
        //应试评价
        const evaluationModal = ref()
        const evaluationShowEdit = ref(false)
        const evaluationValueScore = ref({})
        const evaluation = (record) => {
            evaluationShowEdit.value = true
            evaluationValueScore.value = record
        }
        const evaluationModalCancel = () => {
            evaluationShowEdit.value = false
        }
        const evaluationModalConfirm = (info, data) => {
            if (data) {
                //编辑
                request
                    .put('/api/hr-registration-details-evaluations', info)
                    .then((res) => {
                        // console.log(res)
                        message.success('评价成功')
                        evaluationModal.value.resetFormData()
                        evaluationModalCancel()
                        console.log(tableRef.value)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                //新增
                request
                    .post('/api/hr-registration-details-evaluations', info)
                    .then((res) => {
                        // console.log(res)
                        message.success('评价成功')
                        // console.log(evaluationModal.value)
                        // evaluationModal.value.resetData()
                        evaluationModalCancel()
                        console.log(tableRef.value)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const imgModalCancel = () => {
            imgShowEdit.value = false
            imgModalTitle.value = '答题详情'
        }

        const modalCancelDetails = () => {
            showEditDetails.value = false
            modalTitleDetails.value = '答题详情'
            currentValueDetails.value = null
        }

        const modalConfirmDetails = async () => {
            if (modalTitleDetails.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        const showUpload = ref(false)
        const currentValueUpload = ref(null)
        const modalTitleUpload = ref('上传附件')
        // 上传
        const upload = (record) => {
            showUpload.value = true
            modalTitleUpload.value = '上传附件'
            currentValueUpload.value = { ...record }
        }
        const modalCancelUpload = () => {
            showUpload.value = false
            modalTitleUpload.value = '上传附件'
            currentValueUpload.value = null
        }

        const modalConfirmUpload = () => {
            showUpload.value = false
            tableRef.value.refresh(1)
        }
        //面试官打分弹窗
        const scoreShowEdit = ref(false)
        const currentValueScore = ref('')
        const myData: any = ref({})
        const interviewScoreClick = (data) => {
            // console.log(data.interviewScore)
            myData.value = data
            scoreShowEdit.value = true
            currentValueScore.value = data.interviewScore
        }
        const scoreModalCancel = () => {
            scoreShowEdit.value = false
        }
        const scoreModalConfirm = (data) => {
            console.log(data, '615')
            myData.value.interviewScore = data
            let myInterviewScoreResult: any = null
            let newData = data.split(',')
            newData.map((val) => {
                myInterviewScoreResult = myInterviewScoreResult + parseFloat(val)
            })
            console.log(newData.length)
            myData.value.interviewScoreResult = myInterviewScoreResult / newData.length
            scoreShowEdit.value = false
        }
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '上传',
                    auth: '',
                    show: (record) => {
                        return record.examType == 0 && !record.appendixUrl
                    },
                    click: (record) => upload(record),
                },
                {
                    neme: '答题详情',
                    auth: '',
                    show: (record) => {
                        return record.examType == 1 || (record.examType == 0 && record.appendixUrl)
                    },
                    click: (record) => editRow(record),
                },
                {
                    neme: '应试评价',
                    auth: '',
                    show: true,
                    click: (record) => evaluation(record),
                },
            ]),
        )

        return {
            formData,
            params,
            myPaperId,
            myExamName,
            myProfessionName,
            options,
            tableRef,
            searchData,
            columns,
            confirm,
            cancel,
            ImportData,
            deleteRow,
            editRow,
            evaluation,
            getPaperList,
            myOperation,
            isShowSelectedLine,
            getTableData,

            upload,
            showUpload,
            modalTitleUpload,
            currentValueUpload,
            modalCancelUpload,
            modalConfirmUpload,

            previewFile,

            resultList,
            interviewScoreClick,
            // 面试官打分
            scoreShowEdit,
            currentValueScore,
            scoreModalCancel,
            scoreModalConfirm,

            // 应试评价
            evaluationModal,
            evaluationShowEdit,
            evaluationValueScore,
            evaluationModalCancel,
            evaluationModalConfirm,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            importParam,
            myDisabled,

            // 答案详情
            modalCancelDetails,
            modalConfirmDetails,
            showEditDetails,
            modalTitleDetails,
            currentValueDetails,
            modalType,
            imgShowEdit,
            imgModalTitle,
            imgModalCancel,
            imageUrl,
        }
    },
})
</script>
<style lang="less">
.other-slots {
    // display: block;
    width: 100%;
    .other-info {
        margin-top: 24px;
        .other-inputLeft {
            width: 36px;
            margin-right: 12px;
        }
        .other-inputMiddle {
            width: 70%;
            margin-right: 27px;
        }
        .flex-input {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            .input-key {
                margin-top: 10px;
                margin-right: 10px;
            }
        }
    }
}
</style>
<style scoped lang="less">
.details-flex {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // margin-top: 20px;
    padding-left: 5px;
    .flex-span {
        width: 25%;
        margin-bottom: 10px;
        display: flex;
        span:first-child {
            color: #999;
        }
        .flex-text {
            width: 190px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
    .flex-line {
        width: 100%;
        height: 3px;
        margin: 22px 0;
        box-shadow: 0px 4px 5px 0px rgba(234, 240, 255, 100);
    }
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        // .ant-form-item-control {
        //     max-width: calc(100% - 130px) !important;
        // }
    }
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.btn {
    background: @upload-color;
    border: none;
}
</style>
