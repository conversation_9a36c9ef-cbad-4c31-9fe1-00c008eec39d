<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'900px'" :footer="null">
        <template v-if="modalType == 'table'">
            <div class="details-flex">
                <div class="flex-span">
                    <span>参考人：</span>
                    <span>{{ formData.studentsName }}</span>
                </div>
                <div class="flex-span">
                    <span>身份证号：</span>
                    <span>{{ formData.card }}</span>
                </div>
                <div class="flex-span">
                    <span>联系方式：</span>
                    <span>{{ formData.phone }}</span>
                </div>
                <div class="flex-span">
                    <span>参考时间：</span>
                    <span>{{ formData.examDate }}</span>
                </div>
                <div class="flex-span">
                    <span>得分：</span>
                    <span>{{ formData.score }}</span>
                </div>
            </div>
            <BasicTable
                ref="tableRef"
                api="/api/hr-exam-results/detail"
                :params="params"
                :columns="columns"
                :useIndex="true"
                :rowSelectionShow="false"
            >
                <template #richTextTitle="{ record }">
                    <p v-html="record.title"></p>
                </template>
                <template #studentAnswer="{ record }">
                    <span :style="record.correct.includes(record.studentAnswer) ? '' : 'color:#FF0202'">{{
                        record.studentAnswer
                    }}</span>
                </template>
                <template #correct="{ record }">
                    <span :style="record.correct.includes(record.studentAnswer) ? '' : 'color:#245DFE'">{{
                        record.correct
                    }}</span>
                </template>

                <template #richTextAnalysis="{ record }">
                    <p v-html="record.answerAnalysis"></p>
                </template>
            </BasicTable>
        </template>
        <template v-else-if="modalType == 'pdf'" class="pdf-box">
            <PdfPreview class="pdf" :pdfUrl="formData?.appendixUrl" />
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import PdfPreview from '/@/components/PdfPreview/src/PdfPreview.vue'

export default defineComponent({
    name: 'AnswerDetailsModal',
    components: { PdfPreview },
    props: {
        title: String,
        modalType: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        questionTypeList: {
            type: Array,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item, visible, questionTypeList } = toRefs(props)

        //筛选
        const params = ref<any>({})
        //表格dom
        const tableRef = ref()
        //表格数据
        const columns = [
            {
                title: '题目名称',
                dataIndex: 'title',
                align: 'center',
                slots: { customRender: 'richTextTitle' },
                sorter: false,
            },
            {
                title: '题目类型',
                dataIndex: 'questionTypeName',
                align: 'center',
                sorter: false,
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                sorter: false,
            },
            {
                title: '分值',
                dataIndex: 'score',
                align: 'center',
                sorter: false,
            },
            {
                title: '考生答案',
                dataIndex: 'studentAnswer',
                align: 'center',
                slots: { customRender: 'studentAnswer' },
                sorter: false,
            },
            {
                title: '正确答案',
                dataIndex: 'correct',
                align: 'center',
                slots: { customRender: 'correct' },
                sorter: false,
            },
            {
                title: '答案解析',
                dataIndex: 'answerAnalysis',
                align: 'center',
                slots: { customRender: 'richTextAnalysis' },
                sorter: false,
            },
        ]

        // 富文本弹窗
        const showRichTextEdit = ref(false)
        const modalRichTextTitle = ref('题目名称')
        const currentRichTextValue = ref(null)
        //点击题目名称输入框
        const clickTitle = () => {
            showRichTextEdit.value = true
            modalRichTextTitle.value = '题目名称'
            currentRichTextValue.value = formData.value.title
        }
        //点击答案解析输入框
        const clickAnalysis = () => {
            showRichTextEdit.value = true
            modalRichTextTitle.value = '答案解析'
            currentRichTextValue.value = formData.value.answerAnalysis
        }
        const modalRichTextCancel = () => {
            showRichTextEdit.value = false
            currentRichTextValue.value = null
        }
        const modalRichTextConfirm = (data) => {
            if (modalRichTextTitle.value.includes('题目名称')) {
                formData.value.title = data
            } else {
                formData.value.answerAnalysis = data
            }
        }

        const myOptions = ref([
            {
                label: '题目类型',
                name: 'questionTypeName',
                ruleType: 'number', //暂时
                type: 'change',
                options: questionTypeList,
            },
            {
                label: '题目属性',
                name: 'questionPro',
            },
            {
                label: '适用岗位',
                name: 'applicablePostName',
                slots: 'select',
                default: [],
                type: 'slots',
                ruleType: 'array',
            },
            {
                label: '题目名称',
                name: 'title',
                width: '100%',
                onClick: clickTitle,
            },
            {
                label: '分值',
                name: 'score',
                type: 'number',
                ruleType: 'number',
                showbr: true, //换行
            },
            {
                label: '考生答案',
                name: 'studentAnswer',
                // type: 'slots',
                // slots: 'option',
                // required: false,
                // width: '100%',
            },
            {
                label: '正确答案',
                name: 'correct',
                // type: 'slots',
                // slots: 'option',
                // required: false,
                // width: '100%',
            },
            {
                label: '答案解析',
                name: 'answerAnalysis',
                required: false,
                type: 'textarea',
                width: '100%',
                onClick: clickAnalysis,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                console.log(formData.value)

                params.value.paperId = formData.value.paperId
                params.value.examResultId = formData.value.id
                // console.log(params.value)
                if (formData.value.examType == 1) {
                    //线上
                    nextTick(() => {
                        tableRef.value.refresh(1)
                    })
                }
            }
        })

        // cancel handle
        const cancel = () => {
            emit('cancel')
            // resetFormData()
        }

        return {
            params,
            tableRef,
            columns,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,

            // 富文本
            showRichTextEdit,
            modalRichTextTitle,
            currentRichTextValue,
            modalRichTextCancel,
            modalRichTextConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.details-flex {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // margin-top: 20px;
    padding-left: 5px;
    .flex-span {
        width: 33.3%;
        margin-bottom: 10px;
        span:first-child {
            color: #999;
        }
    }
}
.pdf-box {
    height: 600px;
    width: 100%;
    .pdf {
        height: 600px;
    }
}
</style>
