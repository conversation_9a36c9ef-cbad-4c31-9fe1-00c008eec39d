<template>
    <BasicEditModalSlot :visible="visible" @cancel="lastStep" :title="title" :width="'850px'" destroyOnClose>
        <WangEditor :value="innerContent" @on-change="handleChange" ref="editorRef" :editorConfig="editorConfig" />
        <template #footer>
            <div class="footBtns">
                <Button @click="lastStep" type="primary" class="btn">上一步</Button>
                <Button @click="confirm" type="primary" class="btn">确定</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import useExamPaperStore from '/@/store/modules/examPaper'
export default defineComponent({
    name: 'InsertIndex',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['lastStep', 'confirm'],
    setup(props, { emit }) {
        const { setStepAndModule } = useExamPaperStore()
        const stepAndModule = computed(() => {
            return useExamPaperStore().getStepAndModule
        })
        const innerContent = computed(() => {
            return useExamPaperStore().getInnerContent
        })
        const { visible } = toRefs(props)
        const editorRef = ref()
        //请求
        const handleChange = (html) => {
            useExamPaperStore().setInnerContent(html)
        }

        // lastStep handle
        const lastStep = () => {
            switch (stepAndModule.value.last) {
                case 'ADD_2':
                    setStepAndModule({ step: 2, mode: 'add', title: '新建试卷' })
                    break
                case 'ADD_6':
                    setStepAndModule({ step: 6, mode: 'add', title: '新建试卷' })
                    break
                case 'EDIT_1':
                    setStepAndModule({ step: 1, mode: 'edit', title: '编辑试卷' })
                    break
                case 'EDIT_2':
                    setStepAndModule({ step: 2, mode: 'edit', title: '复制试卷' })
                    break
            }

            emit('lastStep')
        }

        // confirm handle
        const confirm = () => {
            setStepAndModule({ step: 0, mode: '', title: '' })
            emit('confirm')
        }

        const editorConfig = { height: 480 }

        return {
            lastStep,
            confirm,
            editorRef,
            innerContent,
            handleChange,
            editorConfig,
        }
    },
})
</script>
<style scoped lang="less">
.footBtns {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
