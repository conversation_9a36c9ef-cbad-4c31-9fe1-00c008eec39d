<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'850px'" destroyOnClose>
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" :rules="rules" class="form-flex">
            <template v-for="(ele, index) in myOptions" :key="ele.name + 'editModal'">
                <MyFormItem v-if="!ele.external" :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #clientTree>
                        <ClientSelectTree
                            :isAll="true"
                            multiple
                            v-model:value="formData[ele.name]"
                            v-model:itemForm="myOptions[index]"
                            :disabled="examPaper.isPreset"
                        />
                    </template>
                    <template #stationTree>
                        <PostTree
                            v-model:value="formData[ele.name]"
                            v-model:itemForm="myOptions[index]"
                            :disabled="examPaper.isPreset"
                            :allowClear="false"
                        />
                    </template>
                    <template #mode>
                        <RadioGroup v-model:value="formData[ele.name]" :options="modeOptions" @change="ele.onChange" />
                    </template>
                </MyFormItem>
                <template v-else>
                    <template v-if="ele.name == 'testDuration'">
                        <FormItem
                            :style="'width:' + ele.width"
                            :label="ele.label"
                            name="testDuration"
                            :rules="validateDuration"
                            v-if="ele.show != false"
                        >
                            <span class="duration">
                                <RadioGroup
                                    v-model:value="formData[ele.name]"
                                    :options="durationOptions"
                                    @change="ele.onChange"
                                />
                                <span v-show="timeLimitVisible">
                                    <InputNumber
                                        v-model:value="formData.timeLimit"
                                        :placeholder="ele.label"
                                        :formatter="(value) => limitNumber(value)"
                                        :parser="(value) => limitNumber(value)"
                                        style="width: 80px"
                                    />
                                    <span class="unit">分钟</span>
                                </span>
                            </span>
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'paperStatus'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="paperStatus">
                            <span class="duration">
                                <RadioGroup
                                    v-model:value="formData[ele.name]"
                                    :options="paperOptions"
                                    @change="ele.onChange"
                                    :disabled="true"
                                />
                            </span>
                        </FormItem>
                    </template>
                </template>
            </template>
        </Form>
        <div class="addBtn" v-if="examPaper.isPreset">
            <Button type="primary" @click="showInsertModal"> <PlusOutlined />新增</Button>
        </div>
        <AdjustTopic useCoustom :isPreset="examPaper.isPreset" />
        <PresetExamAdd :visible="insertVisible" @cancel="closeInsertModal" />
        <template #footer>
            <Button key="back" @click="cancel" class="btn">取消</Button>
            <Button key="submit" @click="nextStep" type="primary" class="btn">下一步</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import PostTree from '/@/views/user/postManage/postTree.vue'
import AdjustTopic from '../insertModal/system/adjustTopic.vue'
import PresetExamAdd from './presetExamAdd.vue'
import { durationOptions, modeOptions, paperOptions } from '/@/utils/dictionaries'
import useExamPaperStore from '/@/store/modules/examPaper'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'EditModal',
    components: { ClientSelectTree, PostTree, AdjustTopic, PlusOutlined, PresetExamAdd },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: [],
    setup(props, { emit }) {
        const { setStepAndModule, setExamPaper, setTopicList, generateInnerContent, setEditStationIds } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        const stepAndModule = computed(() => {
            return useExamPaperStore().getStepAndModule
        })
        let timeLimitVisible = ref<boolean>(false)
        let insertVisible = ref<boolean>(false)
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '试卷名称',
                name: 'paperName',
                required: true,
                width: '100%',
            },
            {
                label: '适用客户',
                name: 'clientIdList',
                required: true,
                width: '100%',
                type: 'slots',
                default: [],
                ruleType: 'array',
                multiple: true,
                slots: 'clientTree',
                disabled: examPaper.value.isPreset,
            },
            {
                label: '适用岗位',
                name: 'stationIdList',
                required: true,
                width: '100%',
                type: 'slots',
                default: [],
                multiple: true,
                ruleType: 'array',
                slots: 'stationTree',
                disabled: examPaper.value.isPreset,
            },
            {
                label: '考试时长',
                name: 'testDuration',
                width: '50%',
                default: '0',
                trigger: 'blur',
                external: true,
                onChange: ({ target: { value } }) => {
                    if (value == '0') {
                        formData.value.timeLimit = 30
                        timeLimitVisible.value = false
                    } else timeLimitVisible.value = true
                },
            },
            {
                label: '试卷类型',
                name: 'paperStatus',
                width: '50%',
                default: 0,
                ruleType: 'number',
                trigger: 'blur',
                external: true,
            },
            {
                label: '时长限制',
                name: 'timeLimit',
                external: true,
                default: 30,
                ruleType: 'number',
                show: false,
            },
            {
                label: '及格线',
                name: 'passLine',
                trigger: 'blur',
                type: 'number',
                default: 60,
                ruleType: 'number',
                required: true,
                integerOnly: true,
                showbr: true,
            },
        ])
        //请求
        const { visible } = toRefs(props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(
            examPaper,
            () => {
                if (visible.value) {
                    formData.value = Object.assign({}, initFormData, examPaper.value)
                    if (examPaper.value.isPreset) {
                        formData.value.stationIdList = ['全部']
                    }
                    formData.value.testDuration = formData.value.testDuration + ''
                    if (formData.value.testDuration == '0') timeLimitVisible.value = false
                    else timeLimitVisible.value = true
                    if (!formData.value.timeLimit) formData.value.timeLimit = 0
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        watch(
            () => formData.value.stationIdList,
            (val, old) => {
                if (val.length > 0) {
                    setEditStationIds(val)
                } else {
                    setEditStationIds([])
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        // cancel handle
        const cancel = () => {
            setExamPaper({})
            setTopicList([])
            setStepAndModule({ step: 0, mode: '', title: '' })
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const showInsertModal = () => {
            insertVisible.value = true
        }
        const closeInsertModal = () => {
            insertVisible.value = false
        }

        const validateDuration = {
            required: true,
            trigger: ['change', 'blur'],
            validator: () => {
                let formDataItem = formData.value?.testDuration
                if (formDataItem == '1') {
                    if (!formData.value?.timeLimit) {
                        return Promise.reject('请输入考试时长')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        // confirm handle
        const nextStep = () => {
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    // 弹窗跳转
                    setExamPaper(formData.value)
                    generateInnerContent()
                    setStepAndModule({
                        step: 2,
                        mode: 'preview',
                        title: stepAndModule.value.step == '1' ? '编辑试卷' : '复制试卷',
                        last: stepAndModule.value.step == '1' ? 'EDIT_1' : 'EDIT_2',
                    })
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            insertVisible,
            showInsertModal,
            closeInsertModal,
            cancel,
            nextStep,
            rules,
            formData,
            myOptions,
            formInline,

            modeOptions,
            durationOptions,
            paperOptions,

            limitNumber,
            validateDuration,
            timeLimitVisible,
            examPaper,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        .ant-radio-group,
        .ant-radio-wrapper {
            display: flex;
            align-items: center;
        }
    }
    .duration {
        display: flex;
        align-items: center;
        .unit {
            margin: 0 5px;
        }
    }
}
.addBtn {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}
</style>
