<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'850px'" :footer="false">
        <div v-html="contentText"></div>
        <!-- <template #footer> </template> -->
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import useExamPaperStore from '/@/store/modules/examPaper'
export default defineComponent({
    name: 'PreviewModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: [],
    setup(props, { emit }) {
        const { setStepAndModule, setTopicList, setExamPaper } = useExamPaperStore()
        let innerContent = computed(() => {
            return useExamPaperStore().getInnerContent
        })
        //请求
        let contentText = ref('')
        // Form 实例
        watch(
            innerContent,
            (val, old) => {
                contentText.value = val
            },
            {
                immediate: true,
                deep: true,
            },
        )

        // cancel handle
        const cancel = () => {
            setExamPaper({})
            setStepAndModule({ step: 0, mode: '', title: '' })
            setTopicList([])
        }

        return {
            contentText,
            cancel,
        }
    },
})
</script>
<style scoped lang="less">
//
</style>
