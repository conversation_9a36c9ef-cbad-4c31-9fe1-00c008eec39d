<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :zIndex="1009" :width="'850px'">
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRef"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="myColumns"
            :data-source="tableData"
            :rowKey="(record) => record.id"
            :pagination="pagination"
            :loading="loading"
            :row-selection="selectionRowConfig"
            @change="tableChange"
        />
        <template #footer>
            <Button @click="cancel" class="btn">取消</Button>
            <Button @click="exchangeTopic" type="primary" class="btn">更换</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
import { ref, defineComponent, computed, toRefs, watch } from 'vue'
import { toUnderline } from '/@/utils/index'
import { message } from 'ant-design-vue'
import useExamPaperStore from '/@/store/modules/examPaper'
import request from '/@/utils/request'
export default defineComponent({
    name: 'ExchangeTopic',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const { setExchangeVisible, changeTopic } = useExamPaperStore()
        const { visible } = toRefs(props)
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topic = computed(() => {
            return useExamPaperStore().getExchangeTopic
        })
        let exchangeType = computed(() => {
            return useExamPaperStore().getExchangeType
        })
        let editStationIds = computed(() => {
            return useExamPaperStore().getEditStationIds
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let questionTypeList = computed(() => {
            return useExamPaperStore().getQuestionTypeList
        })
        //表格数据
        const columns = [
            {
                title: '题目名称',
                dataIndex: 'title',
                align: 'center',
            },
            {
                title: '题目类型',
                dataIndex: 'questionType',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    return questionTypeList.value.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                width: 120,
            },
            {
                title: '分值',
                dataIndex: 'score',
                align: 'center',
                width: 100,
            },
        ]

        const sortParams = ref({})
        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const tableRef = ref()
        const tableParams = computed(() => {
            if (exchangeType.value == 1) return { ...topic.value, stationIdList: examPaper.value.stationIdList }
            else return { ...topic.value, stationIdList: editStationIds.value }
        })
        // 自定义列展示
        const myColumns = columns.map((i) => ({
            ...i,
            sorter: i.title == '操作' || i.sorter === false ? false : true,
            align: i.align || 'center',
        }))
        const tableData = ref([])
        const pagination = ref<any>({})

        let tableDataFormat = (data) => {
            data.map((item) => {
                item.disabled = !!topicList.value.find((el) => {
                    return el.id == item.id
                })
                return item
            })

            return data
        }
        watch(visible, (val) => {
            if (val) {
                sortParams.value = {
                    field: undefined,
                    order: undefined,
                }
                pagination.value = {
                    current: 1,
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条`,
                    pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
                    total: 0,
                }
                getTableData()
            }
        })

        const loading = ref(false)

        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field: sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }

            getTableData()
        }
        const getTableData = async () => {
            loading.value = true
            try {
                let fn
                if (examPaper.value.isPreset) {
                    fn = () =>
                        request.post(
                            `/api/hr-paper-managements-single-choice?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`,
                            {
                                ...{
                                    field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                    order:
                                        sortParams.value.order === 'ascend'
                                            ? 'DESC'
                                            : sortParams.value.order === 'descend'
                                            ? 'ASC'
                                            : undefined,
                                },
                            },
                        )
                } else {
                    fn = () =>
                        request.post(
                            `/api/hr-paper-questionss/page/replace?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`,
                            {
                                ...tableParams.value,
                                ...{
                                    field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                    order:
                                        sortParams.value.order === 'ascend'
                                            ? 'DESC'
                                            : sortParams.value.order === 'descend'
                                            ? 'ASC'
                                            : undefined,
                                },
                            },
                        )
                }
                const data = await fn()
                tableData.value = data.records || data || []
                tableData.value = tableDataFormat(tableData.value)

                pagination.value.total = data.total || 0
                // 复选框置空
                selectedRowsArr.value = []
                selectedRowKeysArr.value = []
            } finally {
                loading.value = false
            }
        }
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            type: 'radio',
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }

        const cancel = () => {
            setExchangeVisible({ flag: false, type: 1 })
        }
        const exchangeTopic = () => {
            if (selectedRowsArr.value.length > 0) {
                setExchangeVisible({ flag: false, type: 1 })
                changeTopic(selectedRowsArr.value[0])
            } else {
                message.warning('请选择要更换的题目')
            }
        }

        return {
            loading,
            columns,
            myColumns,
            tableParams,
            tableRef,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            pagination,
            selectionRowConfig,

            tableDataFormat,
            cancel,
            exchangeTopic,
            tableChange,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
