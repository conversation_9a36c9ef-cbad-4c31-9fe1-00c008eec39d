<template>
    <Table
        class="mySortTable"
        style="width: 100%"
        ref="tableRef"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        size="small"
        bordered
        :indentSize="30"
        :scroll="{ x: '100' }"
        :columns="columns"
        :data-source="tableData"
        :row-key="(record) => record.id"
        :pagination="false"
        :loading="loading"
    >
        <template #operation="{ record }">
            <div class="optBtns">
                <Button type="primary" size="small" @click="exchangeTopic(record)">更换</Button>
                <Button v-if="isPreset" type="primary" size="small" class="delBtn" @click="removeTopic(record)">删除</Button>
                <span class="drag">
                    <DragOutlined />
                </span>
            </div>
        </template>
    </Table>
</template>

<script lang="ts">
import { ref, defineComponent, onMounted, watch, computed, h, toRefs } from 'vue'
import { DragOutlined } from '@ant-design/icons-vue'
import Sortable from 'sortablejs'
import useExamPaperStore from '/@/store/modules/examPaper'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'
export default defineComponent({
    name: 'AdjustTopic',
    components: { DragOutlined },
    props: {
        useCoustom: {
            type: Boolean,
            default: false,
        },
        isPreset: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const { useCoustom } = toRefs(props)
        const { setTopicList, setExchangeVisible, setExchangeTopic, changeTopicOrder, removeExamTopic } = useExamPaperStore()
        let mySortable = ref<inObject>({})
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let randomTopic = computed(() => {
            return useExamPaperStore().getRandomTopic
        })
        let questionTypeList = computed(() => {
            return useExamPaperStore().getQuestionTypeList
        })
        let editStationIds = computed(() => {
            return useExamPaperStore().getEditStationIds
        })
        const loading = ref(false)
        let tableData = ref<any[]>([])
        const getTableData = async () => {
            loading.value = true
            try {
                let fn = () =>
                    request.post('/api/hr-paper-questionss/page/random', {
                        ...params.value,
                    })
                const data = await fn()
                setTopicList(data.records || data || [])
            } finally {
                loading.value = false
            }
        }
        //表格数据
        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                sorter: false,
                customRender: (record) => {
                    return h('span', record.index + 1)
                },
                width: 80,
            },
            {
                title: '题目名称',
                dataIndex: 'title',
                align: 'center',
                sorter: false,
            },
            {
                title: '题目类型',
                dataIndex: 'questionType',
                align: 'center',
                sorter: false,
                customRender: ({ text }) => {
                    return questionTypeList.value.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                width: 120,
                sorter: false,
            },
            {
                title: '分值',
                dataIndex: 'score',
                align: 'center',
                width: 100,
                sorter: false,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
            },
        ]
        onMounted(() => {
            !useCoustom.value && getTableData()
            initSortable()
        })
        const params = ref({ stationIdList: examPaper.value.stationIdList, ...randomTopic.value })
        const tableRef = ref()
        const initSortable = () => {
            let el: any = document.querySelector('.mySortTable .ant-table-tbody')
            //创建拖拽对象
            mySortable.value = Sortable.create(el, {
                //  指定父元素下可被拖拽的子元素
                draggable: '.ant-table-row',
                sort: true, //是否可进行拖拽排序
                animation: 150,
                forceFallback: true, // 拖动时移动鼠标
                handle: '.drag',
                //拖拽完成，移除拖拽之前的位置上的元素，在拖拽之后的位置上添加拖拽元素
                onEnd: ({ newIndex, oldIndex }) => {
                    changeTopicOrder({ newIndex: newIndex, oldIndex: oldIndex })
                },
            })
        }
        const fetchRandomTopic = async () => {
            tableRef.value.refresh(1)
        }
        watch(
            topicList,
            (val) => {
                if (useCoustom) tableData.value = val
            },
            {
                immediate: true,
                deep: true,
            },
        )
        const exchangeTopic = (record) => {
            if (useCoustom.value) {
                if (!editStationIds.value.length) {
                    message.error('请选择适用岗位！')
                    return
                } else {
                    setExchangeVisible({ flag: true, type: 2 })
                    setExchangeTopic(record)
                }
            } else {
                setExchangeVisible({ flag: true, type: 1 })
                setExchangeTopic(record)
            }
        }
        const removeTopic = (record) => {
            if (topicList.value.length > 1) {
                removeExamTopic(record)
            } else message.error('至少保留一个题目！')
        }
        return {
            loading,
            columns,
            params,
            tableData,

            removeTopic,
            exchangeTopic,
            fetchRandomTopic,
            getTableData,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.mySortTable :deep(.table-striped) {
    background-color: #fafafa;
}
.delBtn {
    background-color: @dangerous-color;
    border: none;
}
.optBtns {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .drag {
        font-size: 20px;
        cursor: pointer;
    }
}
</style>
