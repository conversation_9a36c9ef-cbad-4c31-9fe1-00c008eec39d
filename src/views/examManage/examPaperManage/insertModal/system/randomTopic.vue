<template>
    <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" :rules="rules" class="form-flex">
        <template v-for="ele in myOptions" :key="ele.name + 'randomTopic'">
            <template v-if="ele.name == 'topicSetting'">
                <template v-for="(topicSettingItem, index) in formData.topicSetting" :key="index + 'topicSetting'">
                    <FormItem
                        :class="index == 0 ? '' : 'hide'"
                        style="width: 100%"
                        :label="ele.label"
                        :name="['topicSetting', index]"
                        :rules="validateTopic"
                    >
                        <Select
                            class="inputClass"
                            v-model:value="topicSettingItem.questionType"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            placeholder="请选择题目类型"
                            :options="questionTypeList"
                            :disabled="examPaper.paperStatus == 1"
                            :getPopupContainer="getPopupContainer"
                            @change="fetchTopicNum(topicSettingItem, ['topicSetting', index])"
                        />
                        <Select
                            class="inputClass"
                            v-model:value="topicSettingItem.score"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            placeholder="请选择分值"
                            :options="scoreList"
                            :getPopupContainer="getPopupContainer"
                            @change="fetchTopicNum(topicSettingItem, ['topicSetting', index])"
                        />
                        <InputNumber
                            class="inputClass"
                            v-model:value="topicSettingItem.scoreSum"
                            allowClear
                            :placeholder="topicSettingItem.maxScoreNum === 0 ? '请更改题型或分值' : '请输入题目数量'"
                            :disabled="!topicSettingItem.maxScoreNum"
                            :formatter="(value) => limitNumber(value)"
                            :parser="(value) => limitNumber(value)"
                            :max="topicSettingItem.maxScoreNum"
                            @change="formValidateOptional(['topicSetting', index])"
                        />
                        <span
                            v-if="
                                !isEmpty(topicSettingItem.maxScoreNum) &&
                                !isEmpty(topicSettingItem.questionType) &&
                                !isEmpty(topicSettingItem.score)
                            "
                        >
                            <span class="maxScoreClass">{{ topicSettingItem.maxScoreNum }}</span>
                            <Tooltip placement="topRight" arrowPointAtCenter>
                                <template #title>
                                    <p style="width: 240px">
                                        {{
                                            `题库中题型为 <${getCurrentTopic(topicSettingItem, 'type')}>
                                            分值为 <${getCurrentTopic(topicSettingItem, 'score')}> 的
                                            共 ${topicSettingItem.maxScoreNum} 道`
                                        }}
                                    </p>
                                </template>
                                <QuestionCircleOutlined :style="{ color: '#999999' }" />
                            </Tooltip>
                        </span>
                        <span style="width: 24px; margin-left: 30px; display: inline-block">
                            <MinusCircleOutlined
                                v-if="index != 0"
                                class="dynamic-delete-button"
                                @click="deleDomain('topicSetting', index)"
                            />
                        </span>
                    </FormItem>
                </template>
                <Button type="dashed" @click="addDomain('topicSetting')" class="addRow">
                    <PlusOutlined />
                </Button>
            </template>
            <template v-else>
                <span v-if="!formData.preference.length" class="preferenceLabel">属性偏好：</span>
                <template v-else v-for="(preferenceItem, index) in formData.preference" :key="index + 'preference'">
                    <FormItem
                        class="preferenceClass"
                        :class="index == 0 ? '' : 'hide'"
                        :label="ele.label"
                        style="width: 100%"
                        :name="['preference', index]"
                        :rules="validateQuestionPro"
                    >
                        <Select
                            v-model:value="preferenceItem.questionPro"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            style="width: 250px"
                            :options="questionProList"
                            placeholder="请选择属性偏好"
                            :getPopupContainer="getPopupContainer"
                            @change="formValidateOptional(['preference', index])"
                        />
                        <InputNumber
                            v-model:value="preferenceItem.percentage"
                            style="width: 250px"
                            allowClear
                            placeholder="请输入百分比"
                            :min="0"
                            :max="100"
                            :formatter="(value) => limitPercentage(value)"
                            :parser="(value) => value.replace('%', '')"
                        />
                        <span style="width: 24px; margin-left: 5px; display: inline-block">
                            <MinusCircleOutlined class="dynamic-delete-button" @click="deleDomain('preference', index)" />
                        </span>
                    </FormItem>
                </template>
                <Button type="dashed" @click="addDomain('preference')" class="addRow">
                    <PlusOutlined />
                </Button>
            </template>
        </template>
    </Form>
    <div class="totalTip">
        <div class="topic_wrapper">
            <img src="~//@/assets/bulb.png" alt="" class="tipImg" />
            <div style="margin-right: 15px">当前已选</div>
            <div v-if="examPaper.paperStatus == 0">
                <div>
                    单选题 <span class="topic">{{ singleNum }}</span> 道，多选题
                    <span class="topic">{{ multipleNum }}</span> 道，判断题 <span class="topic">{{ judgeNum }}</span> 道
                </div>
                <div>
                    填空题 <span class="topic">{{ blankNum }}</span> 道，简答题
                    <span class="topic">{{ shortAnswerNum }}</span> 道，论述题
                    <span class="topic"> {{ discussNum }}</span> 道，材料写作题
                    <span class="topic">{{ materialsWritingNum }}</span> 道
                </div>
            </div>
            <div v-else>
                <div>
                    面试题 <span class="topic">{{ interviewNum }}</span> 道
                </div>
            </div>
        </div>
        <span class="total">
            总分值：<span>{{ totalScore }}</span>
        </span>
    </div>
</template>

<script lang="ts">
import { divide, times } from 'number-precision'
import { MinusCircleOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, watch, computed, reactive, toRefs, nextTick } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import useExamPaperStore from '/@/store/modules/examPaper'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import { message } from 'ant-design-vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
export default defineComponent({
    name: 'RandomTopic',
    components: { MinusCircleOutlined, PlusOutlined, QuestionCircleOutlined },
    setup(props, { emit }) {
        const { setStepAndModule, setRandomTopic } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let questionTypeList = computed(() => {
            if (examPaper.value.paperStatus == 0) {
                return useExamPaperStore().getQuestionTypeList.filter((el) => {
                    return el.value != 7
                })
            } else
                return useExamPaperStore().getQuestionTypeList.filter((el) => {
                    return el.value == 7
                })
        })
        let questionProList = computed(() => {
            return useExamPaperStore().getQuestionProList
        })
        let scoreList = computed(() => {
            return useExamPaperStore().getScoreList
        })
        let randomTopic = computed(() => {
            return useExamPaperStore().getRandomTopic
        })
        const stepAndModule = computed(() => {
            return useExamPaperStore().getStepAndModule
        })
        let topic = reactive({
            singleNum: 0,
            interviewNum: 0,
            multipleNum: 0,
            judgeNum: 0,
            blankNum: 0,
            shortAnswerNum: 0,
            discussNum: 0,
            materialsWritingNum: 0,
            totalScore: 0,
        })
        let {
            singleNum,
            interviewNum,
            multipleNum,
            judgeNum,
            blankNum,
            shortAnswerNum,
            discussNum,
            materialsWritingNum,
            totalScore,
        } = toRefs(topic)
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '题型设置',
                name: 'topicSetting',
                ruleType: 'array',
                required: true,
                default: [
                    {
                        questionType: examPaper.value.paperStatus == 0 ? 0 : 7,
                    },
                ],
            },
            {
                label: '属性偏好',
                name: 'preference',
                ruleType: 'array',
                required: false,
                default: [],
            },
        ])
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        watch(
            formData,
            (val, old) => {
                singleNum.value = 0
                interviewNum.value = 0
                multipleNum.value = 0
                judgeNum.value = 0
                blankNum.value = 0
                shortAnswerNum.value = 0
                totalScore.value = 0
                val.topicSetting.forEach((ele) => {
                    if (ele.questionType == 0) singleNum.value += ele.scoreSum || 0
                    if (ele.questionType == 1) multipleNum.value += ele.scoreSum || 0
                    if (ele.questionType == 2) judgeNum.value += ele.scoreSum || 0
                    if (ele.questionType == 3) blankNum.value += ele.scoreSum || 0
                    if (ele.questionType == 4) shortAnswerNum.value += ele.scoreSum || 0
                    if (ele.questionType == 5) discussNum.value += ele.scoreSum || 0
                    if (ele.questionType == 6) materialsWritingNum.value += ele.scoreSum || 0
                    if (ele.questionType == 7) interviewNum.value += ele.scoreSum || 0
                    totalScore.value += (Number(ele.scoreSum) || 0) * (Number(ele.score) || 0)
                })
            },
            {
                deep: true,
            },
        )

        watch(
            stepAndModule,
            (val, old) => {
                if (val && !old) {
                    if (val.mode == 'add' && val.step == 5) {
                        if (randomTopic.value) {
                            formData.value.topicSetting = randomTopic.value.hrPagerSetUpDTO
                            formData.value.preference = randomTopic.value.hrPagerQuestionProDTO
                            formData.value.preference.forEach((el) => {
                                el.percentage = times(Number(el.percentage), 100)
                            })
                        }
                    }
                }
            },
            { deep: true, immediate: true },
        )

        const addDomain = (type) => {
            let obj =
                type == 'preference'
                    ? {
                          percentage: '',
                      }
                    : {
                          questionType: examPaper.value.paperStatus == 0 ? 0 : 7,
                      }
            if (formData.value?.[type]) {
                formData.value?.[type]?.push({
                    ...obj,
                })
            } else {
                formData.value[type] = [
                    {
                        ...obj,
                    },
                ]
            }
        }
        const deleDomain = (type, index) => {
            let myArray = JSON.parse(JSON.stringify(formData.value[type]))
            myArray.splice(index, 1)
            formData.value[type] = myArray
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        /* 限制数字输入框只能输入整数 */
        const limitPercentage = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? `${value.replace(/^(0+)|[^\d]/g, '')}%` : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? `${String(value).replace(/^(0+)|[^\d]/g, '')}%` : ''
            } else {
                return ''
            }
        }

        const getCurrentTopic = (item, type) => {
            if (type == 'type') {
                return questionTypeList.value.find((el) => {
                    return el.value == item.questionType
                })?.label
            } else {
                return scoreList.value.find((el) => {
                    return el.value == item.score
                })?.label
            }
        }

        const fetchTopicNum = (item, nameList: string[]) => {
            formValidateOptional(nameList)
            if (!isEmpty(item.questionType) && item.score) {
                request
                    .post(
                        '/api/hr-paper-questionss/sum/random',
                        {
                            applicablePostList: examPaper.value.stationIdList,
                            questionType: item.questionType,
                            score: item.score,
                        },
                        { loading: false },
                    )
                    .then((res) => {
                        item.maxScoreNum = res
                        if (!res) item.scoreSum = undefined
                        formValidateOptional(nameList)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }
        const validateTopic = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.topicSetting[rule.field.split('.')[1]]
                if (!formDataItem?.scoreSum || !formDataItem?.score) {
                    return Promise.reject('请将题型设置完整')
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validateQuestionPro = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'object',
            validator: async (rule: RuleObject, value: string) => {
                let flag = true
                if (!value['questionPro']) flag = false
                else flag = true

                if (!flag) return Promise.reject('请选择属性偏好')
                else return Promise.resolve()
            },
        }
        const paramsFormat = () => {
            let preference = formData.value.preference
            preference.forEach((el) => {
                el.percentage = divide(Number(el.percentage), 100)
            })
            let obj = {
                single: singleNum.value,
                interview: interviewNum.value,
                multiple: multipleNum.value,
                judge: judgeNum.value,
                blank: blankNum.value,
                shortAnswer: shortAnswerNum.value,
                discuss: discussNum.value,
                materialsWriting: materialsWritingNum.value,
                totalscoreSum:
                    Number(singleNum.value) +
                    Number(interviewNum.value) +
                    Number(multipleNum.value) +
                    Number(judgeNum.value) +
                    Number(blankNum.value) +
                    Number(shortAnswerNum.value) +
                    Number(discussNum.value) +
                    Number(materialsWritingNum.value),
                scores: totalScore.value,
                hrPagerSetUpDTO: formData.value.topicSetting,
                hrPagerQuestionProDTO: preference,
            }
            return obj
        }
        // /api/hr-paper-questionss/page/random
        const nextStep = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (formData.value.preference.length > 0) {
                        let sum = 0
                        formData.value.preference.forEach((el) => {
                            sum += el.percentage
                        })
                        if (sum == 100) {
                            setRandomTopic(paramsFormat())
                            setStepAndModule({ step: 6, mode: 'add', title: '新建试卷' })
                        } else message.warning('属性偏好百分比总值应为100%')
                    } else {
                        setRandomTopic(paramsFormat())
                        setStepAndModule({ step: 6, mode: 'add', title: '新建试卷' })
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            examPaper,
            addDomain,
            deleDomain,
            nextStep,

            myOptions,
            formInline,
            formData,
            rules,
            questionTypeList,
            questionProList,
            scoreList,
            validateTopic,
            validateQuestionPro,
            limitNumber,
            limitPercentage,
            singleNum,
            interviewNum,
            multipleNum,
            judgeNum,
            blankNum,
            shortAnswerNum,
            discussNum,
            materialsWritingNum,
            totalScore,

            formValidateOptional,
            fetchTopicNum,
            getCurrentTopic,
            isEmpty,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    .inputClass {
        width: 200px;
        margin-right: 15px;
    }
    .maxScoreClass {
        font-weight: bold;
        margin-right: 8px;
    }
    :deep(.ant-form-item) {
        .ant-form-item-control {
            width: calc(100% - 80px) !important;
        }
    }
    .preferenceClass {
        :deep(.ant-form-item-control-input-content) {
            display: flex;
            justify-content: space-between;
        }
    }
    .hide {
        :deep(.ant-form-item-required) {
            display: none;
        }
        :deep(.ant-col.ant-form-item-label) {
            label {
                color: rgba(0, 0, 0, 0) !important;
            }
        }
    }
    .preferenceLabel {
        position: relative;
        top: 15px;
        right: -12px;
        transform: translateY(50%);
    }
}

.totalTip {
    margin-top: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .topic_wrapper {
        display: flex;
        align-items: center;
        .tipImg {
            width: 26px;
            height: 26px;
            margin-right: 10px;
        }
    }
    .topic {
        margin: 0 8px;
        color: @primary-color;
    }
    .total {
        margin: 0 30px;
        & > span {
            color: @dangerous-color;
        }
    }
}
.dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 4px;
    font-size: 20px;
    color: #999;
    transition: all 0.3s;

    color: @dangerous-color;
}
.dynamic-delete-button:hover {
    color: @dangerous-color;
}
.dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}
.addRow {
    width: calc(100% - 80px);
    margin: 0 0 30px 80px;
    float: right;
}
</style>
