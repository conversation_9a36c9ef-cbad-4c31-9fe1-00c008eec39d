<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'850px'">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" :rules="rules" class="form-flex">
            <template v-for="(ele, index) in myOptions" :key="ele.name + 'configModal'">
                <MyFormItem v-if="!ele.external" :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #clientTree>
                        <ClientSelectTree
                            :isAll="true"
                            multiple
                            :checkStrictly="false"
                            v-model:value="formData[ele.name]"
                            v-model:itemForm="myOptions[index]"
                        />
                    </template>
                    <template #stationTree>
                        <PostTree v-model:value="formData[ele.name]" v-model:itemForm="myOptions[index]" />
                    </template>
                    <template #mode>
                        <RadioGroup v-model:value="formData[ele.name]" :options="modeOptions" @change="ele.onChange" />
                    </template>
                </MyFormItem>
                <template v-else>
                    <template v-if="ele.name == 'testDuration'">
                        <FormItem
                            :style="'width:' + ele.width"
                            :label="ele.label"
                            name="testDuration"
                            :rules="validateDuration"
                            v-if="ele.show != false"
                        >
                            <span class="duration">
                                <RadioGroup
                                    v-model:value="formData[ele.name]"
                                    :options="durationOptions"
                                    @change="ele.onChange"
                                />
                                <span v-show="timeLimitVisible">
                                    <InputNumber
                                        v-model:value="formData.timeLimit"
                                        :placeholder="ele.label"
                                        :formatter="(value) => limitNumber(value)"
                                        :parser="(value) => limitNumber(value)"
                                        style="width: 80px"
                                    />
                                    <span class="unit">分钟</span>
                                </span>
                            </span>
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'paperStatus'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="paperStatus">
                            <span class="duration">
                                <RadioGroup v-model:value="formData[ele.name]" :options="paperOptions" @change="ele.onChange" />
                            </span>
                        </FormItem>
                    </template>
                </template>
            </template>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel" class="btn">取消</Button>
            <Button key="submit" @click="confirm" type="primary" class="btn">下一步</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import PostTree from '/@/views/user/postManage/postTree.vue'
import request from '/@/utils/request'
import { durationOptions, modeOptions, paperOptions } from '/@/utils/dictionaries'
import useExamPaperStore from '/@/store/modules/examPaper'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'InsertIndex',
    components: { ClientSelectTree, PostTree },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm'],
    setup(props, { emit }) {
        const { setStepAndModule, setExamPaper, setTopicList } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let timeLimitVisible = ref<boolean>(false)
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '试卷名称',
                name: 'paperName',
                required: true,
                width: '100%',
            },
            {
                label: '适用客户',
                name: 'clientIdList',
                required: true,
                width: '100%',
                type: 'slots',
                default: [],
                ruleType: 'array',
                multiple: true,
                slots: 'clientTree',
            },
            {
                label: '适用岗位',
                name: 'stationIdList',
                required: true,
                width: '100%',
                type: 'slots',
                default: [],
                multiple: true,
                ruleType: 'array',
                slots: 'stationTree',
            },
            {
                label: '考试时长',
                name: 'testDuration',
                width: '50%',
                default: '0',
                trigger: 'blur',
                external: true,
                onChange: ({ target: { value } }) => {
                    if (value == '0') {
                        timeLimitVisible.value = false
                        formData.value.timeLimit = 30
                    } else timeLimitVisible.value = true
                },
            },
            {
                label: '试卷类型',
                name: 'paperStatus',
                width: '50%',
                default: 0,
                ruleType: 'number',
                trigger: 'blur',
                external: true,
            },
            {
                label: '时长限制',
                name: 'timeLimit',
                external: true,
                default: 30,
                ruleType: 'number',
                show: false,
            },
            {
                label: '及格线',
                name: 'passLine',
                trigger: 'blur',
                type: 'number',
                default: 60,
                ruleType: 'number',
                required: true,
                integerOnly: true,
                showbr: true,
            },
            {
                label: '出题形式',
                name: 'questionMode',
                type: 'slots',
                slots: 'mode',
                required: true,
                default: '0',
                width: '100%',
            },
        ])
        //请求
        const { visible } = toRefs(props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                if (examPaper.value) {
                    formData.value = Object.assign({}, initFormData, examPaper.value)
                } else {
                    formData.value = Object.assign({}, initFormData)
                }
            }
        })

        // cancel handle
        const cancel = () => {
            resetData()
            setExamPaper({})
            setTopicList([])
            setStepAndModule({ step: 0, mode: '', title: '' })
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const validateDuration = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'number',
            validator: (rule, value) => {
                let formDataItem = formData.value?.testDuration
                if (formDataItem == '1') {
                    if (!formData.value?.timeLimit) {
                        return Promise.reject('请输入考试时长')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        // confirm handle
        const confirm = () => {
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    // 弹窗跳转
                    setExamPaper(formData.value)
                    if (formData.value.questionMode == '0') {
                        // 自定义
                        setStepAndModule({ step: 2, mode: 'add', title: '新建试卷' })
                    } else {
                        // 系统随机
                        setStepAndModule({ step: 5, mode: 'add', title: '新建试卷' })
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            cancel,
            confirm,
            rules,
            formData,
            myOptions,
            formInline,
            validateDuration,
            timeLimitVisible,

            modeOptions,
            durationOptions,
            paperOptions,

            limitNumber,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        .ant-radio-group,
        .ant-radio-wrapper {
            display: flex;
            align-items: center;
        }
    }
    .duration {
        display: flex;
        align-items: center;
        .unit {
            margin: 0 5px;
        }
    }
}
</style>

