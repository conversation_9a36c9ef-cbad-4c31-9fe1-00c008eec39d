<template>
    <BasicEditModalSlot :visible="visible" @cancel="lastStep" :title="title" :width="'900px'">
        <InsertTopic ref="insertTopic" v-if="stepAndModule.step == 2" />
        <SelectTopic ref="selectTopic" v-else-if="stepAndModule.step == 3" />
        <RandomTopic ref="randomTopic" v-else-if="stepAndModule.step == 5" />
        <AdjustTopic ref="adjustTopic" v-else-if="stepAndModule.step == 6" />
        <!-- <component :is="currentComponent" /> -->
        <template #footer>
            <div class="footBtns" v-if="stepAndModule.step == 2">
                <Button key="back" @click="lastStep" type="primary" class="btn">上一步</Button>
                <Button key="submit" @click="nextStep" type="primary" class="btn">下一步</Button>
            </div>
            <div class="footBtns" v-if="stepAndModule.step == 3">
                <Button @click="selectLastStep" type="primary" class="btn">上一步</Button>
                <Button @click="addToPaper" type="primary" class="btn">添加到试卷</Button>
            </div>
            <div class="footBtns" v-if="stepAndModule.step == 5">
                <Button @click="randomLastStep" type="primary" class="btn">上一步</Button>
                <Button @click="randomNextStep" type="primary" class="btn">下一步</Button>
            </div>
            <div class="footBtns" v-if="stepAndModule.step == 6">
                <Button @click="adjustLastStep" type="primary" class="btn">上一步</Button>
                <Button @click="adjustNextStep" type="primary" class="btn">下一步</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, toRefs, watch, computed, ref } from 'vue'
import InsertTopic from './customer/insertTopic.vue'
import SelectTopic from './customer/selectTopic.vue'
import RandomTopic from './system/randomTopic.vue'
import AdjustTopic from './system/adjustTopic.vue'
import useExamPaperStore from '/@/store/modules/examPaper'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'InsertNext',
    components: { InsertTopic, SelectTopic, RandomTopic, AdjustTopic },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm'],
    setup(props, { emit }) {
        const { setTopicList, setStepAndModule, generateInnerContent, setRandomTopic } = useExamPaperStore()
        const stepAndModule = computed(() => {
            return useExamPaperStore().getStepAndModule
        })
        const topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let currentComponent = ref('')
        let insertTopic = ref()
        let selectTopic = ref()
        let randomTopic = ref()
        let adjustTopic = ref()
        //请求
        const { visible } = toRefs(props)

        const addToPaper = () => {
            selectTopic.value.addToPaper()
        }
        const selectLastStep = () => {
            setStepAndModule({ step: 2, mode: 'add', title: '新建试卷' })
        }
        // cancel handle
        const lastStep = () => {
            setTopicList([])
            setRandomTopic(null)
            setStepAndModule({ step: 1, mode: 'add', title: '新建试卷' })
        }
        const randomLastStep = () => {
            setTopicList([])
            setRandomTopic(null)
            setStepAndModule({ step: 1, mode: 'add', title: '新建试卷' })
        }
        const randomNextStep = () => {
            randomTopic.value.nextStep()
        }
        const adjustLastStep = () => {
            setTopicList([])
            setStepAndModule({ step: 5, mode: 'add', title: '新建试卷' })
        }
        const adjustNextStep = () => {
            if (!topicList.value.length) {
                message.error('请添加至少一道试题')
            } else {
                generateInnerContent()
                setStepAndModule({ step: 2, mode: 'preview', title: '新建试卷', last: 'ADD_6' })
            }
        }

        // confirm handle
        const nextStep = () => {
            if (!topicList.value.length) {
                message.error('请添加至少一道试题')
            } else {
                generateInnerContent()
                setStepAndModule({ step: 2, mode: 'preview', title: '新建试卷', last: 'ADD_2' })
            }
        }

        return {
            lastStep,
            nextStep,
            addToPaper,
            selectLastStep,
            randomLastStep,
            randomNextStep,
            adjustLastStep,
            adjustNextStep,

            stepAndModule,
            currentComponent,
            insertTopic,
            selectTopic,
            randomTopic,
            adjustTopic,
        }
    },
})
</script>
<style scoped lang="less">
.footBtns {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
