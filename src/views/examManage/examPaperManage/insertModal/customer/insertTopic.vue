<template>
    <div class="btns">
        <Button type="primary" @click="addTopic">添加题目</Button>
    </div>
    <BasicTable
        class="mySortTable"
        :tableDataList="topicList"
        ref="tableRef"
        :params="params"
        :columns="columns"
        :rowSelectionShow="false"
        :scroll="{ x: '100%', y: 500 }"
    >
        <template #operation="{ record }">
            <div class="optBtns">
                <Button type="primary" size="small" class="delBtn" @click="delOne(record)">删除</Button>
                <span class="drag">
                    <DragOutlined />
                </span>
            </div>
        </template>
    </BasicTable>
    <div class="totalTip">
        <div class="topic_wrapper">
            <img src="~//@/assets/bulb.png" alt="" class="tipImg" />
            <div style="margin-right: 15px">当前已选</div>
            <div v-if="examPaper.paperStatus == 0">
                <div>
                    单选题 <span class="topic">{{ singleNum }}</span> 道，多选题
                    <span class="topic">{{ multipleNum }}</span> 道，判断题 <span class="topic">{{ judgeNum }}</span> 道
                </div>
                <div>
                    填空题 <span class="topic">{{ blankNum }}</span> 道，简答题
                    <span class="topic">{{ shortAnswerNum }}</span> 道，论述题
                    <span class="topic"> {{ discussNum }}</span> 道，材料写作题
                    <span class="topic">{{ materialsWritingNum }}</span> 道
                </div>
            </div>
            <div v-else>
                <div>
                    面试题 <span class="topic">{{ interviewNum }}</span> 道
                </div>
            </div>
        </div>
        <div class="total">
            总分值：<span>{{ totalScore }}</span>
        </div>
    </div>
</template>

<script lang="ts">
import { ref, defineComponent, watch, onMounted, computed, h, toRefs, reactive } from 'vue'
import { DragOutlined } from '@ant-design/icons-vue'
import Sortable from 'sortablejs'
import useExamPaperStore from '/@/store/modules/examPaper'
export default defineComponent({
    name: 'InsertTopic',
    components: { DragOutlined },
    setup(props, { emit }) {
        const { setStepAndModule, removeTopic } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let questionTypeList = computed(() => {
            return useExamPaperStore().getQuestionTypeList
        })
        //表格数据
        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                sorter: false,
                customRender: (record) => {
                    return h('span', record.index + 1)
                },
                width: 80,
            },
            {
                title: '题目类型',
                dataIndex: 'questionType',
                align: 'center',
                width: 100,
                sorter: false,
                customRender: ({ text }) => {
                    return questionTypeList.value.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                sorter: false,
                width: 100,
            },
            {
                title: '题目名称',
                dataIndex: 'title',
                sorter: false,
                align: 'center',
            },
            {
                title: '分值',
                dataIndex: 'score',
                align: 'center',
                sorter: false,
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 130,
                align: 'center',
                slots: { customRender: 'operation' },
            },
        ]
        onMounted(() => {
            initSortable()
        })
        const params = ref({})
        const tableRef = ref()
        let topic = reactive({
            singleNum: 0,
            interviewNum: 0,
            multipleNum: 0,
            judgeNum: 0,
            blankNum: 0,
            shortAnswerNum: 0,
            discussNum: 0,
            materialsWritingNum: 0,
            totalScore: 0,
        })
        const addTopic = () => {
            setStepAndModule({ step: 3, mode: 'add', title: '选择题目' })
        }
        let {
            singleNum,
            interviewNum,
            multipleNum,
            judgeNum,
            blankNum,
            shortAnswerNum,
            discussNum,
            materialsWritingNum,
            totalScore,
        } = toRefs(topic)
        watch(
            topicList,
            (val, old) => {
                interviewNum.value = 0
                singleNum.value = 0
                multipleNum.value = 0
                judgeNum.value = 0
                blankNum.value = 0
                shortAnswerNum.value = 0
                discussNum.value = 0
                materialsWritingNum.value = 0
                totalScore.value = 0
                val.forEach((el) => {
                    if (el.questionType == 0) singleNum.value += 1
                    if (el.questionType == 1) multipleNum.value += 1
                    if (el.questionType == 2) judgeNum.value += 1
                    if (el.questionType == 3) blankNum.value += 1
                    if (el.questionType == 4) shortAnswerNum.value += 1
                    if (el.questionType == 5) discussNum.value += 1
                    if (el.questionType == 6) materialsWritingNum.value += 1
                    if (el.questionType == 7) interviewNum.value += 1
                    totalScore.value += el.score
                })
            },
            {
                deep: true,
                immediate: true,
            },
        )
        const delOne = (record) => {
            removeTopic(record.id)
        }
        const initSortable = () => {
            let el: any = document.querySelector('.mySortTable .ant-table-tbody')
            //创建拖拽对象
            Sortable.create(el, {
                //  指定父元素下可被拖拽的子元素
                draggable: '.ant-table-row',
                sort: true, //是否可进行拖拽排序
                animation: 150,
                forceFallback: true, // 拖动时移动鼠标
                handle: '.drag',
                //拖拽完成，移除拖拽之前的位置上的元素，在拖拽之后的位置上添加拖拽元素
                onEnd: ({ newIndex, oldIndex }) => {
                    const val = topicList.value[oldIndex]
                    topicList.value.splice(oldIndex, 1)
                    topicList.value.splice(newIndex, 0, val)
                },
            })
        }

        return {
            columns,
            params,
            tableRef,
            topicList,
            singleNum,
            interviewNum,
            multipleNum,
            judgeNum,
            blankNum,
            shortAnswerNum,
            discussNum,
            materialsWritingNum,
            totalScore,
            examPaper,

            addTopic,
            delOne,
        }
    },
})
</script>
<style scoped lang="less">
.totalTip {
    margin-top: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .topic_wrapper {
        display: flex;
        align-items: center;
        font-size: 0;
        div {
            font-size: 14px;
        }
        .tipImg {
            width: 26px;
            height: 26px;
            margin-right: 10px;
        }
    }
    .topic {
        margin: 0 8px;
        color: @primary-color;
    }
    .total {
        margin: 0 30px;
        & > span {
            color: @dangerous-color;
        }
    }
}
.optBtns {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .drag {
        font-size: 20px;
        cursor: pointer;
    }
}
</style>
