<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <Table
        class="basicTable"
        style="width: 100%"
        ref="tableRef"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        size="small"
        bordered
        :indentSize="30"
        :scroll="{ x: '100' }"
        :columns="myColumns"
        :data-source="tableData"
        :row-key="(record) => record.id"
        :pagination="pagination"
        :loading="loading"
        :row-selection="selectionRowConfig"
        @change="tableChange"
    />
</template>

<script lang="ts">
import { ref, defineComponent, onMounted, computed } from 'vue'
import { getValuesAndRules, toUnderline } from '/@/utils/index'
import { message } from 'ant-design-vue'
import useExamPaperStore from '/@/store/modules/examPaper'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'

interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
export default defineComponent({
    name: 'SelectTopic',
    setup(props, { emit }) {
        const { setTopicList, setStepAndModule } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let questionTypeList = computed(() => {
            if (examPaper.value.paperStatus == 0) {
                return useExamPaperStore().getQuestionTypeList.filter((el) => {
                    return el.value != 7
                })
            } else
                return useExamPaperStore().getQuestionTypeList.filter((el) => {
                    return el.value == 7
                })
        })
        let questionProList = computed(() => {
            return useExamPaperStore().getQuestionProList
        })
        let scoreList = computed(() => {
            return useExamPaperStore().getScoreList
        })
        const sortParams = ref({
            field: undefined,
            order: undefined,
        })
        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        //表格数据
        const columns = [
            {
                title: '题目名称',
                dataIndex: 'title',
                align: 'center',
            },
            {
                title: '题目类型',
                dataIndex: 'questionType',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    return questionTypeList.value.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '题目属性',
                dataIndex: 'questionPro',
                align: 'center',
                width: 120,
            },
            {
                title: '分值',
                dataIndex: 'score',
                align: 'center',
                width: 100,
            },
        ]
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '题目名称',
                key: 'title',
                size: 'small',
            },
            {
                type: 'select',
                label: '题目类型',
                key: 'questionTypeList',
                multiple: true,
                size: 'small',
                renderBody: true,
                options: questionTypeList,
                show: examPaper.value.paperStatus != 1,
            },
            {
                type: 'select',
                label: '题目属性',
                key: 'questionProList',
                size: 'small',
                multiple: true,
                renderBody: true,
                options: questionProList,
            },
            {
                type: 'select',
                label: '分值',
                key: 'scoreList',
                multiple: true,
                size: 'small',
                renderBody: true,
                options: scoreList,
            },
        ]
        const tableData = ref([])
        const params = ref({})
        const tableParams = computed(() => {
            if (examPaper.value.paperStatus == 1)
                return {
                    stationIdList: examPaper.value.stationIdList,
                    ...params.value,
                    questionTypeList: [7],
                }
            else
                return {
                    stationIdList: examPaper.value.stationIdList,
                    ...params.value,
                }
        })
        // 自定义列展示
        const myColumns = columns.map((i) => ({
            ...i,
            sorter: i.title == '操作' || i.sorter === false ? false : true,
            align: i.align || 'center',
        }))

        const tableRef = ref()
        const searchData = async () => {
            refresh(1)
        }
        const refresh = (page = pagination.value.current) => {
            pagination.value.current = page
            getTableData()
        }
        let tableDataFormat = (data) => {
            data.map((item) => {
                item.disabled = !!topicList.value.find((el) => {
                    return el.id == item.id
                })
                return item
            })

            return data
        }
        onMounted(() => {
            getTableData()
        })
        const loading = ref(false)
        const pagination = ref<any>({
            current: 1,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
            total: 0,
        })
        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field: sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }

            getTableData()
        }
        const getTableData = async () => {
            loading.value = true
            try {
                const fn = () =>
                    request.post(
                        `/api/hr-paper-questionss/page?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`,
                        {
                            ...tableParams.value,
                            ...{
                                field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                order:
                                    sortParams.value.order === 'ascend'
                                        ? 'DESC'
                                        : sortParams.value.order === 'descend'
                                        ? 'ASC'
                                        : undefined,
                            },
                        },
                    )
                const data = await fn()
                tableData.value = data.records || data || []
                tableData.value = tableDataFormat(tableData.value)

                pagination.value.total = data.total || 0
                // 复选框置空
                selectedRowsArr.value = []
                selectedRowKeysArr.value = []
            } finally {
                loading.value = false
            }
        }
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }
        let addToPaper = () => {
            if (selectedRowsArr.value.length > 0) {
                setStepAndModule({ step: 2, mode: 'add', title: '新建试卷' })
                setTopicList(selectedRowsArr.value)
            } else {
                message.warning('请选择要添加的题目')
                return
            }
        }

        return {
            loading,
            options,
            columns,
            myColumns,
            params,
            tableParams,
            tableRef,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            pagination,
            selectionRowConfig,

            searchData,
            tableDataFormat,
            addToPaper,
            onSelectChange,
            tableChange,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
