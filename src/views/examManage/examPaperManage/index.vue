<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="showModal(null, { step: 1, mode: 'add', title: '新建试卷' })">新建</Button>
        <Button type="primary" @click="batchDownload" class="downloadBtn">{{ exportText }}</Button>
        <Button type="primary" @click="batchDel" class="delBtn">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-paper-managements/page"
        deleteApi="/api/hr-paper-managements/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <div class="btns">
                <Button
                    type="primary"
                    size="small"
                    @click="showModal(record, { step: 1, mode: 'edit', title: '编辑试卷' })"
                    v-if="record.usageSum < 1 || record.isPreset"
                >
                    编辑
                </Button>
                <Button
                    type="primary"
                    size="small"
                    @click="showModal(record, { step: 2, mode: 'edit', title: '复制试卷' })"
                    v-else
                >
                    复制
                </Button>
                <Button type="primary" size="small" @click="showModal(record, { step: 1, mode: 'preview', title: '预览试卷' })">
                    预览
                </Button>
            </div>
        </template>
    </BasicTable>
    <InsertIndex ref="insertModal" :visible="addIndexVisible" :title="modalTitle" />
    <InsertNext :visible="nextVisible" :title="modalTitle" />
    <EditModal ref="editModal" :visible="editIndexVisible" :title="modalTitle" />
    <ExchangeTopic :visible="exchangeVisible" :title="modalTitle" />
    <PreviewModal :visible="previewVisible" :title="modalTitle" />
    <PaperEditor :visible="paperEditorVisible" :title="modalTitle" @confirm="paperConfirm" />
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import request from '/@/utils/request'

import { message } from 'ant-design-vue'
import InsertIndex from './insertModal/insertIndex.vue'
import InsertNext from './insertModal/insertNext.vue'
import EditModal from './editModal/index.vue'
import PreviewModal from './previewModal/index.vue'
import PaperEditor from './components/paperEditor.vue'
import ExchangeTopic from './insertModal/system/exchangeTopic.vue'
import useExamPaperStore from '/@/store/modules/examPaper'
import downFile, { downMultFile } from '/@/utils/downFile'
import moment from 'moment'
import { SearchBarOption } from '/#/component'
import { paperOptions } from '/@/utils/dictionaries'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'ExamPaperManage',
    components: { InsertIndex, InsertNext, EditModal, PreviewModal, PaperEditor, PostTree, ExchangeTopic },
    setup() {
        const { setExamPaper, setInnerContent, setTopicList, setHandleType, typeListInit } = useExamPaperStore()
        let examPaper = computed(() => {
            return useExamPaperStore().getExamPaper
        })
        let topicList = computed(() => {
            return useExamPaperStore().getTopicList
        })
        let innerContent = computed(() => {
            return useExamPaperStore().getInnerContent
        })
        let exchangeVisible = computed(() => {
            return useExamPaperStore().getExchangeVisible
        })
        const { setStepAndModule } = useExamPaperStore()
        const stepAndModule = computed(() => {
            return useExamPaperStore().getStepAndModule
        })
        const handleType = computed(() => {
            return useExamPaperStore().getHandleType
        })
        onMounted(() => {
            typeListInit()
        })
        watch(
            stepAndModule,
            (val) => {
                modalToChange(val)
            },
            {
                deep: true,
            },
        )
        // modal选择显示
        // mode '' | 'add' | 'edit' | 'preview'
        // '' 0 不显示弹窗
        // add 1 添加弹窗首页
        // add 2 添加弹窗-自定义-添加题目
        // add 3 添加弹窗-自定义-选择题目
        // preview 2 富文本
        // add 5 添加弹窗-系统随机-添加题目
        // add 6 添加弹窗-系统随机-选择题目
        // edit 1 编辑弹窗-编辑
        // edit 2 编辑弹窗-复制
        // preview 1 预览弹窗
        const modalToChange = (val) => {
            modalTitle.value = val.title
            if (val.mode == 'preview') {
                addIndexVisible.value = false
                nextVisible.value = false
                editIndexVisible.value = false
                switch (val.step) {
                    case 1:
                        previewVisible.value = true
                        paperEditorVisible.value = false
                        break
                    case 2:
                        previewVisible.value = false
                        paperEditorVisible.value = true
                        break
                }
            } else if (val.mode == 'add') {
                previewVisible.value = false
                paperEditorVisible.value = false
                switch (val.step) {
                    case 1:
                        addIndexVisible.value = true
                        nextVisible.value = false
                        break
                    case 2:
                        addIndexVisible.value = false
                        nextVisible.value = true
                        break
                    case 3:
                        addIndexVisible.value = false
                        nextVisible.value = true
                        modalTitle.value = '选择题目'
                        break
                    case 5:
                        addIndexVisible.value = false
                        nextVisible.value = true
                        break
                    case 6:
                        addIndexVisible.value = false
                        nextVisible.value = true
                        break
                }
            } else if (val.mode == 'edit') {
                previewVisible.value = false
                paperEditorVisible.value = false
                addIndexVisible.value = false
                nextVisible.value = false
                editIndexVisible.value = true
                switch (val.step) {
                    case 1:
                        modalTitle.value = '编辑试卷'
                        break
                    case 2:
                        modalTitle.value = '复制试卷'
                        break
                }
            } else {
                addIndexVisible.value = false
                nextVisible.value = false
                previewVisible.value = false
                editIndexVisible.value = false
                paperEditorVisible.value = false
            }
        }

        const addIndexVisible = ref(false) // 新建
        const editIndexVisible = ref(false) // 编辑
        const nextVisible = ref(false) // 自定义
        const previewVisible = ref(false) // 预览
        const paperEditorVisible = ref(false) // 预览
        const modalTitle = ref('') // modal标题

        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '试卷名称',
                key: 'paperName',
            },
            {
                label: '适用客户',
                key: 'clientIdList',
                placeholder: '适用客户',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'daterange',
                label: '生成时间',
                key: 'contractStartDateQuery',
            },
            {
                type: 'select',
                label: '试卷类型',
                key: 'paperStatusList',
                multiple: true,
                options: paperOptions,
            },
            {
                type: 'selectSlot',
                label: '适用岗位',
                key: 'stationIdList',
                placeholder: '适用岗位',
                maxTag: '0',
            },
        ]
        //表格数据
        const columns = [
            {
                title: '试卷名称',
                dataIndex: 'paperName',
                align: 'center',
                width: 300,
            },
            {
                title: '适用客户',
                dataIndex: 'clientName',
                align: 'center',
                width: 250,
                ellipsis: true,
            },
            {
                title: '适用岗位',
                dataIndex: 'stationName',
                align: 'center',
                width: 250,
                ellipsis: true,
                customRender: ({ record }) => {
                    if (record.isPreset) return '全部'
                    else return record.stationName
                },
            },
            {
                title: '试卷类型',
                dataIndex: 'paperStatus',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    if (record.paperStatus) return '面试'
                    else return '笔试'
                },
            },
            {
                title: '生成时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 150,
            },
            {
                title: '使用次数',
                dataIndex: 'usageSum',
                align: 'center',
                width: 80,
                customRender: ({ text }) => {
                    return (text && text) || Number(text)
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 140,
                align: 'center',
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]

        const params = ref({})
        const selectedRowsArr = ref<any[]>([])
        const tableData = ref<any>([])

        const tableRef = ref()
        const insertModal = ref()
        const editModal = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selectedRowsArr.value)
        })
        // 批量下载
        const batchDownload = () => {
            let ids: any[] = []
            let body = {}
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (exportText.value.indexOf('选中') != -1) {
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            request
                .post('/api/hr-paper-managements/download', body)
                .then((res) => {
                    if (res.length > 1) {
                        downMultFile(
                            '多试卷',
                            res.map((el) => {
                                return el.fileUrL
                            }),
                            res.map((el) => {
                                return `${el.fileUrLName}${moment().format('YYYYMMDDHHmmss')}.doc`
                            }),
                        )
                    } else {
                        downFile('get', res[0].fileUrL, res[0].fileUrLName, null)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 批量删除
        const batchDel = (row) => {
            if (
                selectedRowsArr.value.some((item) => {
                    return item.isPreset
                })
            ) {
                message.error('预置试卷不允许删除！')
                tableRef.value.checkboxReset()
            } else {
                if (
                    selectedRowsArr.value.some((item) => {
                        return item.usageSum > 0
                    })
                ) {
                    message.error('所选试卷中包含已使用试卷，不允许删除！')
                    tableRef.value.checkboxReset()
                } else {
                    tableRef.value.deleteRow().then((ref) => {
                        tableRef.value.checkboxReset()
                    })
                }
            }
        }

        const showModal = (record, modal) => {
            switch (modal.title) {
                case '新建试卷':
                    setHandleType('ADD')
                    break
                case '复制试卷':
                    setHandleType('COPY')
                    break
                case '编辑试卷':
                    setHandleType('EDIT')
                    break
            }
            setStepAndModule(modal)
            if (modal.mode == 'edit' || (modal.mode == 'preview' && modal.step == 1)) {
                // 查询单张试卷 /api/hr-paper-managements get {id:}
                request
                    .get('/api/hr-paper-managements', {
                        id: record.id,
                    })
                    .then((res) => {
                        // let obj = Object.assign({}, res, record.value)
                        setExamPaper(res)
                        setTopicList(res.questionDTO)
                        setInnerContent(res.preview)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const paperConfirm = () => {
            // ADD EDIT COPY
            let obj: any = {
                ...examPaper.value,
                questionIdDTO: topicList.value.map((el, index) => {
                    return { id: el.id, ordersum: index + 1 }
                }),
                preview: innerContent.value,
            }
            if (handleType.value == 'ADD') {
                delete obj.id
                // 创建试卷
                request
                    .post('/api/hr-paper-managements', obj)
                    .then((res) => {
                        console.log(res)
                        message.success('新建试卷成功')
                        tableRef.value.refresh(1)
                        insertModal.value.cancel()
                    })
                    .catch((err) => {
                        console.log(err)
                        insertModal.value.cancel()
                    })
            } else if (handleType.value == 'COPY') {
                // 复制试卷
                request
                    .post('/api/hr-paper-managements-copy', obj)
                    .then((res) => {
                        console.log(res)
                        message.success('复制试卷成功')
                        tableRef.value.refresh(1)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                // 更新试卷
                if (obj.isPreset) {
                    request
                        .post('/api/hr-paper-managements-single-update', {
                            id: obj.id,
                            paperName: obj.paperName,
                            passLine: obj.passLine,
                            questionIdDTO: obj.questionIdDTO,
                            testDuration: obj.testDuration,
                            timeLimit: obj.timeLimit,
                            preview: obj.preview,
                            isPreset: obj.isPreset,
                        })
                        .then((res) => {
                            console.log(res)
                            message.success('修改成功')
                            tableRef.value.refresh()
                            editModal.value.cancel()
                        })
                        .catch((err) => {
                            console.log(err)
                            editModal.value.cancel()
                        })
                } else {
                    delete obj.isPreset
                    request
                        .put('/api/hr-paper-managements', obj)
                        .then((res) => {
                            console.log(res)
                            message.success('修改成功')
                            tableRef.value.refresh()
                            editModal.value.cancel()
                        })
                        .catch((err) => {
                            console.log(err)
                            editModal.value.cancel()
                        })
                }
            }
        }

        return {
            tableData,
            exportText,
            options,
            columns,
            tableRef,
            selectedRowsArr,
            insertModal,
            editModal,

            params,
            exchangeVisible,

            searchData,
            batchDownload,
            batchDel,

            showModal,

            paperConfirm,

            addIndexVisible,
            editIndexVisible,
            nextVisible,
            previewVisible,
            paperEditorVisible,
            modalTitle,
        }
    },
})
</script>

<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
