<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
        <Button danger type="primary" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-schedules/page"
        deleteApi="/api/hr-schedules/deletes"
        :params="params"
        :columns="columns"
    >
        <!--        有效期-->
        <template #validTime="{ record }">
            <span v-if="record.repetitionType != '3'"> {{ record.validStarTime }} 至 {{ record.validEndTime }} </span>
            <span v-else>
                {{ record.validStarTime }}
            </span>
        </template>
        <!--        重复日期-->
        <template #repetitionCont="{ record }">
            <span v-if="record.repetitionType == '3'"> 不重复 </span>
            <span v-else-if="record.repetitionType == '2'"> 每天 </span>
            <span v-else-if="record.repetitionType == '4'"> 每月末 </span>
            <span v-else>
                {{ record.repetitionCont }}
            </span>
        </template>

        <template #operation="{ record }">
            <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp;
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :repetitionTypeList="repetitionTypeList"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import modal from './modal.vue'
export default defineComponent({
    name: 'Schedule',
    components: { MyModal: modal },
    setup() {
        // 获取重复类型
        let repetitionTypeList = ref<LabelValueOptions>([])
        // 获取类型
        let typeList = ref<LabelValueOptions>([])

        // 获取优先级
        let priorityTypeList = ref<LabelValueOptions>([
            {
                label: '高',
                value: 0,
            },
            {
                label: '中',
                value: 1,
            },
            {
                label: '低',
                value: 2,
            },
        ])

        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/repetitionType', {}).then((res) => {
                repetitionTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            request.get('/api/hr-schedule-types/list', {}).then((res) => {
                typeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        //筛选
        const params = ref<{}>({
            // industryType: null,
            // professionType: null,
            // professionName: null,
        })
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
            console.log(1111)
        }

        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '类型',
                key: 'typeList',
                options: typeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '标题',
                key: 'title',
            },
            // {
            //     type: 'date',
            //     label: '有效开始日期',
            //     key: 'startTime',
            //     allowClear: true,
            // },
            {
                type: 'daterange',
                label: '有效期',
                key: 'validTimeList',
                allowClear: true,
            },
            {
                type: 'select',
                label: '重复类型',
                key: 'repetitionTypeList',
                options: repetitionTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '优先级',
                key: 'priorityList',
                options: priorityTypeList,
                multiple: true,
            },
        ]

        //表格数据
        const columns = [
            {
                title: '类型',
                dataIndex: 'type',
                align: 'center',
                customRender: ({ record }) => {
                    return record.typeName
                },
                width: 100,
            },
            {
                title: '标题',
                dataIndex: 'title',
                align: 'center',
                width: 120,
            },
            {
                title: '有效期',
                dataIndex: 'validStarTime',
                align: 'center',
                slots: { customRender: 'validTime' },
                width: 220,
            },
            {
                title: '重复日期',
                dataIndex: 'repetitionCont',
                align: 'center',
                slots: { customRender: 'repetitionCont' },
                width: 150,
            },
            {
                title: '提醒时间',
                dataIndex: 'warnTime',
                align: 'center',
                width: 150,
            },
            {
                title: '优先级',
                dataIndex: 'priority',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 0) {
                        text = '高'
                    } else if (text == 1) {
                        text = '中'
                    } else {
                        text = '低'
                    }
                    return text
                },
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        const modalTitle = ref('新增日程')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增日程'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑日程'
            currentValue.value = { ...record }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增日程'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        return {
            repetitionTypeList,
            typeList,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            priorityTypeList,

            //事件
            // changeIndustry,
            // changeProfession,
        }
    },
})
</script>

<style scoped lang="less"></style>
