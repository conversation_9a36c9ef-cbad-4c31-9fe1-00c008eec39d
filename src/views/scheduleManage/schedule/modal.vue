<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'800px'">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules" class="form-flex">
            <template v-for="item in myOptions" :key="item">
                <MyFormItem
                    :width="item.width"
                    :item="item"
                    v-if="item.show != false"
                    v-model:value="formData[item.name]"
                    :class="item.slots"
                >
                    <template #izMilitDate v-if="validTimes">
                        <RangePicker
                            :allowClear="true"
                            v-model:value="formData.izMilitDate"
                            @change="dateChange"
                            :disabled-date="disabledDate"
                        />
                    </template>

                    <template #validStarTime v-if="validStarTime">
                        <DatePicker
                            :allowClear="true"
                            v-model:value="formData.validStarTime"
                            @change="dateChange"
                            :disabled-date="disabledDate"
                        />
                    </template>

                    <template #TimePicker>
                        <TimePicker
                            v-model:value="formData.warnTime"
                            valueFormat="HH:mm:ss"
                            style="width: 100%"
                            @change="warnTimeChange"
                        />
                    </template>

                    <template #img>111</template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import moment, { Moment } from 'moment'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'ScheduleModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        repetitionTypeList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/hr-schedules'
        const showRepetCont = ref<Boolean>(true) //是否显示重复日期
        const validTimes = ref<Boolean>(true) //是否显示有效开始结束日期
        const validStarTime = ref<Boolean>(true) //是否显示有效开始日期
        const validWarnTime = ref<Boolean>(true) //是否提醒
        const repetitionContList = ref<object[]>([]) // 获取重复日期
        const monthly = ref<object[]>([]) // 每月重复日期
        const weekly = ref<object[]>([
            // 每周重复日期
            {
                label: '星期日',
                value: '星期日',
            },
            {
                label: '星期一',
                value: '星期一',
            },
            {
                label: '星期二',
                value: '星期二',
            },
            {
                label: '星期三',
                value: '星期三',
            },
            {
                label: '星期四',
                value: '星期四',
            },
            {
                label: '星期五',
                value: '星期五',
            },
            {
                label: '星期六',
                value: '星期六',
            },
        ])
        // 每月重复日期
        const getMonthly = () => {
            monthly.value = []
            let myNumber = ''
            for (let i = 1; i <= 31; i++) {
                myNumber = i + ''

                if (myNumber.length == 1) {
                    myNumber = '0' + myNumber
                }
                monthly.value.push({ label: myNumber, value: myNumber })
            }
            repetitionContList.value = monthly.value
        }
        const { title, item, visible, repetitionTypeList } = toRefs(props)
        // 修改重复类型
        const selectChanged = (value: string, option: object) => {
            formData.value.repetitionType = value
            formData.value.repetitionContNew = []
            formData.value.izMilitDate = []
            formData.value.validStarTime = ''

            if (value == '3') {
                // 选择 不重复时

                showRepetCont.value = false
                validTimes.value = false
                validStarTime.value = true
            } else if (value == '2' || value == '4') {
                // 选择 每天重复 月底重复

                showRepetCont.value = false
                validStarTime.value = false
                validTimes.value = true
            } else {
                showRepetCont.value = true
                validStarTime.value = false
                validTimes.value = true

                if (value == '0') {
                    //每月重复
                    getMonthly()
                } else if (value == '1') {
                    //每周重复
                    repetitionContList.value = weekly.value
                }
            }
        }
        // 修改重复日期
        const changedRepetCont = (value: string, option: any) => {
            let myLabel = option.map((item) => {
                return item.label
            })
            formData.value.repetitionCont = myLabel.join(',')
        }
        // 优先级数据
        const priorityList = ref([
            {
                label: '高',
                value: 0,
            },
            {
                label: '中',
                value: 1,
            },
            {
                label: '低',
                value: 2,
            },
        ])
        // 修改优先级
        const changedPriority = (value: string, option: object) => {
            // console.log(value, option)
            formData.value.priority = value
        }
        // 修改是否提醒
        const changeIsWarn = (item) => {
            if (item == true) {
                validWarnTime.value = true
            } else {
                validWarnTime.value = false
                formData.value.warnTime = ''
            }
        }
        // const rules: Array<Object> = []
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '类型',
                name: 'type',
                type: 'SelectDic',
                disType: 'department',
                title: '添加日程类型',
                ruleType: 'number',
            },
            {
                label: '标题',
                name: 'title',
            },
            {
                label: '重复类型',
                name: 'repetitionType',
                type: 'change',

                options: repetitionTypeList,
                onChange: selectChanged,
                ruleType: 'number',
            },
            {
                label: '重复日期',
                name: 'repetitionContNew',
                show: showRepetCont, //true显示
                showbr: true,
                type: 'select',
                options: repetitionContList,
                ruleType: 'array',
                multiple: true,
                default: [],
                onChange: changedRepetCont,
            },
            {
                label: '有效期',
                name: 'izMilitDate',
                type: 'slots',
                slots: 'izMilitDate',
                default: [],
                ruleType: 'array',
                show: validTimes,
                showbr: true,
            },
            {
                label: '有效期',
                name: 'validStarTime',
                type: 'slots',
                slots: 'validStarTime',
                show: validStarTime,
                showbr: true,
            },
            {
                label: '优先级',
                name: 'priority',
                type: 'change',
                ruleType: 'number',

                options: priorityList,
                onChange: changedPriority,
                showbr: true,
            },
            {
                label: '是否提醒',
                name: 'isWarn',
                ruleType: 'boolean',
                type: 'isTrue',
                default: true,
                showbr: true,
                onChange: changeIsWarn,
                required: false,
            },
            {
                label: '提醒时间',
                name: 'warnTime',
                type: 'slots',
                slots: 'TimePicker',

                showbr: true,
                show: validWarnTime,
            },
            {
                label: '备注',
                name: 'remark',
                required: false,
                width: '100%',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }

                if (item.value?.repetitionCont) {
                    let repetitionCont = item.value?.repetitionCont.split(',')
                    formData.value.repetitionContNew = repetitionCont
                }

                if (formData.value.repetitionType == 0) {
                    //每月重复
                    getMonthly()
                } else if (formData.value.repetitionType == 1) {
                    //每周重复
                    repetitionContList.value = weekly.value
                }

                if (formData.value.repetitionType == 3) {
                    //重复类型为不重复
                    showRepetCont.value = false
                    validTimes.value = false
                    validStarTime.value = true
                    formData.value.validStarTime = item.value?.validStarTime
                } else if (formData.value.repetitionType == 2 || formData.value.repetitionType == 4) {
                    //重复类型为每天重复 月底重复
                    showRepetCont.value = false
                    validStarTime.value = false
                    validTimes.value = true
                    formData.value.izMilitDate[0] = item.value?.validStarTime
                    formData.value.izMilitDate[1] = item.value?.validEndTime
                } else {
                    showRepetCont.value = true
                    validStarTime.value = false
                    validTimes.value = true
                    formData.value.izMilitDate[0] = item.value?.validStarTime
                    formData.value.izMilitDate[1] = item.value?.validEndTime

                    console.log(111)
                    if (formData.value.repetitionType == '0') {
                        //每月重复
                        repetitionContList.value = monthly.value
                    } else if (formData.value.repetitionType == 1) {
                        //每周重复
                        repetitionContList.value = weekly.value
                    }
                }

                if (formData.value.isWarn == true) {
                    //是否提醒
                    validWarnTime.value = true
                } else {
                    validWarnTime.value = false
                    formData.value.warnTime = ''
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // 修改有效期
        const dateChange = (date, dateString) => {
            if (formData.value.repetitionType == 3) {
                //重复类型为不重复
                formData.value.validStarTime = dateString
                formData.value.validEndTime = dateString
            } else {
                formData.value.izMilitDate = dateString
                formData.value.validStarTime = dateString[0]
                formData.value.validEndTime = dateString[1]
            }
        }

        // 修改提醒时间
        const warnTimeChange = (time) => {
            formData.value.warnTime = time
        }

        const disabledDate = (current: Moment) => {
            return current && current < moment().subtract(1, 'days')
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            changedPriority,
            showRepetCont,
            validTimes,
            validStarTime,
            // validWarnTime,
            dateChange,
            warnTimeChange,
            changeIsWarn,
            priorityList,
            disabledDate,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
</style>
