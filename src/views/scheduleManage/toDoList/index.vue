<template>
    <div class="main">
        <Calendar class="calendar" v-model:value="currentDate" @select="dateChange" @panelChange="monthChange">
            <template #headerRender="{ value }">
                <div class="header">
                    <div class="month">
                        {{ moment(value).format('YYYY年MM月') }}
                    </div>
                    <div class="changeBtns">
                        <div @click="changeMonth('prev')">上个月</div>
                        <div @click="changeMonth('now')">今天</div>
                        <div @click="changeMonth('next')">下个月</div>
                    </div>
                </div>
            </template>
            <template #dateFullCellRender="{ current }">
                <div
                    class="cell"
                    :class="moment(currentDate).format('YYYY-MM-DD') == moment(current).format('YYYY-MM-DD') ? 'sel' : ''"
                    :style="{ background: dateList[moment(current).format('YYYY-MM-DD')]?.background }"
                >
                    <div class="top">
                        <div
                            class="date"
                            :style="{
                                color:
                                    moment(current).format('YYYY-MM') == moment(currentDate).format('YYYY-MM') ? '#111' : '#aaa',
                            }"
                        >
                            {{ moment(current).format('DD') }}
                        </div>
                        <div class="nums" v-if="dateList[moment(current).format('YYYY-MM-DD')]">
                            <div class="dangerous" v-if="dateList[moment(current).format('YYYY-MM-DD')].urgentNum">
                                {{ dateList[moment(current).format('YYYY-MM-DD')].urgentNum }}
                            </div>
                            <div class="warning" v-if="dateList[moment(current).format('YYYY-MM-DD')].delayNum">
                                {{ dateList[moment(current).format('YYYY-MM-DD')].delayNum }}
                            </div>
                            <div class="primary" v-if="dateList[moment(current).format('YYYY-MM-DD')].toDoNum">
                                {{ dateList[moment(current).format('YYYY-MM-DD')].toDoNum }}
                            </div>
                            <div class="done" v-if="dateList[moment(current).format('YYYY-MM-DD')].doneNum">
                                {{ dateList[moment(current).format('YYYY-MM-DD')].doneNum }}
                            </div>
                        </div>
                    </div>
                    <div class="end">
                        <span v-if="moment().format('YYYY-MM-DD') == moment(current).format('YYYY-MM-DD')" style="color: #3eb889">
                            今天
                        </span>
                        <span :style="{ color: dateList[moment(current).format('YYYY-MM-DD')]?.color }">
                            {{ dateList[moment(current).format('YYYY-MM-DD')]?.txt }}
                        </span>
                    </div>
                </div>
            </template>
        </Calendar>
        <div class="detail">
            <div class="topBar">
                <div class="month">
                    {{ moment(currentDate).format('YYYY年MM月DD日') }}
                </div>
                <Button size="small" type="primary" @click="createOne">添加待办</Button>
            </div>
            <div class="list">
                <div class="item" v-for="i in toDoList" :key="i.id">
                    <div v-if="i.upcomingStatus != 3" class="finish" @click="finishRow(i)">已办</div>
                    <div class="tag" :class="[`a${i.upcomingStatus}`]">
                        {{ i.upcomingStatusName }}
                    </div>
                    <div class="title">
                        <div class="tags">
                            <Tag color="blue">{{ i.typeName }}</Tag>
                            <Tag v-if="i.priority == 0" color="#FE6868">高</Tag>
                            <Tag v-if="i.priority == 1" color="#FEC068">中</Tag>
                            <Tag v-if="i.priority == 2" color="#6894FE">低</Tag>
                            <Tag v-if="i.isTop" color="red">置顶</Tag>
                            <div class="txt">{{ i.warnTime || '' }} {{ i.title }}</div>
                        </div>
                        <div class="btn">
                            <FormOutlined @click="editOne(i)" />
                            <CloseSquareOutlined @click="removeOne(i)" style="margin-left: 10px" />
                        </div>
                    </div>
                    <div class="remark">{{ i.remark }}</div>
                </div>
            </div>
        </div>
    </div>
    <EditModal
        :visible="showModal"
        :title="modalTitle"
        :currentDate="moment(currentDate).format('YYYY-MM-DD')"
        :item="currentItem"
        @cancel="modalClose"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { Calendar, message, Modal, Tag } from 'ant-design-vue'
import moment, { Moment } from 'moment'
import { FormOutlined, CloseSquareOutlined } from '@ant-design/icons-vue'
import request from '/@/utils/request'
import EditModal from './EditModal.vue'

export default defineComponent({
    name: 'ToDoList',
    components: { Calendar, Tag, FormOutlined, CloseSquareOutlined, EditModal },
    setup() {
        const currentDate = ref<string | Moment>(moment())

        const changeMonth = (type) => {
            if (type == 'prev') {
                currentDate.value = moment(currentDate.value).subtract(1, 'months')
            } else if (type == 'now') {
                currentDate.value = moment()
            } else {
                currentDate.value = moment(currentDate.value).add(1, 'months')
            }
            monthChange(currentDate.value)
            dateChange(currentDate.value)
        }

        const monthChange = (date) => {
            getData(date)
        }

        const dateChange = (date) => {
            currentDate.value = date
            getDetailByDate(date)
        }

        const toDoList = ref<any[]>([])
        const getDetailByDate = async (date) => {
            const res = await request.post(`/api/hr-upcomings/date?date=${moment(date).format('YYYY-MM-DD')}`, {})
            toDoList.value = res
        }
        const dateList = ref({})
        const getData = async (date) => {
            const res = await request.post(`/api/hr-upcomings/calendar?date=${moment(date).format('YYYY-MM-01')}`, {})
            const data = {}
            res.forEach((i) => {
                if (data[i.upcomingDate]) {
                    data[i.upcomingDate].list.push(i)
                } else {
                    data[i.upcomingDate] = {
                        urgentNum: 0,
                        delayNum: 0,
                        toDoNum: 0,
                        doneNum: 0,
                        list: [i],
                    }
                }
            })
            Object.keys(data).forEach((i) => {
                data[i].list.forEach((j) => {
                    switch (j.upcomingStatus) {
                        case 0:
                            data[i].urgentNum += 1
                            break
                        case 1:
                            data[i].delayNum += 1
                            break
                        case 2:
                            data[i].toDoNum += 1
                            break
                        case 3:
                            data[i].doneNum += 1
                            break
                        default:
                            break
                    }
                    data[i].background = data[i].urgentNum ? '#FE6868' : ''
                    data[i].color = data[i].urgentNum
                        ? '#FE6868'
                        : data[i].delayNum
                        ? '#FEC068'
                        : data[i].toDoNum
                        ? '#6894FE'
                        : data[i].doneNum
                        ? '#C7C7C7'
                        : ''
                    data[i].txt = data[i].urgentNum
                        ? '紧急'
                        : data[i].delayNum
                        ? '延期'
                        : data[i].toDoNum
                        ? '待办'
                        : data[i].doneNum
                        ? '已办'
                        : ''
                })
            })
            dateList.value = data
            console.log('data', data)
        }

        const modalTitle = ref('新增待办')
        const showModal = ref(false)
        const currentItem = ref(undefined)
        const modalClose = () => {
            showModal.value = false
        }
        const modalConfirm = () => {
            showModal.value = false
            getData(currentDate.value)
            getDetailByDate(currentDate.value)
        }

        const createOne = () => {
            currentItem.value = undefined
            modalTitle.value = '新增待办'
            showModal.value = true
        }
        const editOne = (item) => {
            currentItem.value = { ...item }
            modalTitle.value = '编辑待办'
            showModal.value = true
        }
        const removeOne = (item) => {
            Modal.confirm({
                title: '确认',
                content: '确认删除该待办事项吗?',
                onOk: async () => {
                    await request.post(`api/hr-upcomings/deletes`, [item.id])
                    getData(currentDate.value)
                    getDetailByDate(currentDate.value)
                },
            })
        }
        const finishRow = async (item) => {
            await request.put(`/api/hr-upcomings`, { ...item, upcomingStatus: 3 })
            message.success('设置成功！')
            getData(currentDate.value)
            getDetailByDate(currentDate.value)
        }

        // setup
        getData(currentDate.value)
        getDetailByDate(currentDate.value)

        return {
            finishRow,
            currentItem,
            createOne,
            editOne,
            removeOne,
            toDoList,
            monthChange,
            changeMonth,
            dateChange,
            currentDate,
            moment,
            dateList,
            modalTitle,
            showModal,
            modalClose,
            modalConfirm,
        }
    },
})
</script>

<style scoped lang="less">
.cell {
    background: white;
    height: 130px;
    border: 1px solid #eee;
    box-sizing: border-box;
    padding: 10px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .date {
            padding-left: 5px;
            font-size: 16px;
        }
        .nums {
            padding: 2px 5px;
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
            background: #f5f7ff;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            div {
                width: 20px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                border-radius: 10px;
                color: white;
                transform: scale(0.8);
            }
            .dangerous {
                background: @dangerous-color;
            }
            .warning {
                background: @warning-color;
            }
            .primary {
                background: @primary-color;
            }
            .done {
                background: #c7c7c7;
            }
        }
    }
    .end {
        text-align: right;
        margin-right: 5px;
        color: #eee;
    }
}
.sel {
    background: @right-slider-color;
    color: #333;
    border-color: @right-slider-color;
}
.main {
    height: 100%;
    display: flex;
    justify-content: space-between;
    .calendar {
        width: 55%;
        height: 100%;
        overflow-y: auto;
        background: white;
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            // border-bottom: 1px solid #eee;

            .month {
                font-size: 16px;
                font-weight: bold;
            }
            .changeBtns {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                div {
                    padding: 5px 13px;
                    border: 1px solid #eee;
                    border-right: 0;
                    cursor: pointer;
                    &:last-child {
                        border-right: 1px solid #eee;
                    }
                    &:hover {
                        color: @primary-color;
                    }
                }
            }
        }
    }
    .detail {
        width: 44%;
        background: white;
        box-sizing: border-box;
        padding: 20px;
        .topBar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 20px;
            .month {
                font-size: 16px;
                font-weight: bold;
            }
        }
        .list {
            .item {
                margin-bottom: 10px;
                padding: 30px;
                border: 1px solid #ccc;
                position: relative;
                overflow: hidden;
                .finish {
                    position: absolute;
                    top: 0;
                    right: -80px;
                    width: 100px;
                    height: 100%;
                    background: @primary-color;
                    color: white;
                    text-align: center;
                    writing-mode: vertical-lr;
                    letter-spacing: 10px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transition: all 0.5s;
                    cursor: pointer;
                    &:hover {
                        right: 0;
                    }
                }
                .tag {
                    position: absolute;
                    top: -10px;
                    left: -32px;
                    padding: 15px 30px 5px 30px;
                    transform: rotate(-45deg);
                    color: white;
                }
                .a0 {
                    background: @dangerous-color;
                }
                .a1 {
                    background: @warning-color;
                }
                .a2 {
                    background: @primary-color;
                }
                .a3 {
                    background: #eee;
                }
                .title {
                    display: flex;
                    justify-content: space-between;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ccc;
                    .tags {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                    }
                    .btn {
                        color: #444;
                        font-size: 18px;
                        cursor: pointer;
                    }
                }
                .remark {
                    padding-top: 10px;
                }
            }
        }
    }
}
</style>
