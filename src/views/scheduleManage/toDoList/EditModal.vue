<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="700px">
        <Form ref="formInline" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :model="formData" :rules="rules">
            <FormItem label="标题" name="title">
                <Input v-model:value="formData.title" placeholder="请输入标题" />
            </FormItem>
            <Row>
                <Col span="12">
                    <FormItem :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" label="类型" name="type">
                        <Select v-model:value="formData.type" :options="typeList" placeholder="请选择类型" />
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" label="状态" name="upcomingStatus">
                        <Select v-model:value="formData.upcomingStatus" :options="upcomingStatusList" placeholder="请选择类型" />
                    </FormItem>
                </Col>
            </Row>
            <FormItem label="优先级" name="priority">
                <Select v-model:value="formData.priority" :options="priorityList" placeholder="请选择类型" />
            </FormItem>
            <FormItem label="待办日期" name="upcomingDate">
                <DatePicker v-model:value="formData.upcomingDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
            </FormItem>
            <Row>
                <Col span="12">
                    <FormItem :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" label="是否提醒" name="isWarn">
                        <Switch v-model:checked="formData.isWarn" checked-children="是" un-checked-children="否" />
                    </FormItem>
                </Col>
                <Col span="12" v-if="formData.isWarn">
                    <FormItem :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" label="提醒时间" name="warnTime">
                        <TimePicker v-model:value="formData.warnTime" format="HH:mm" valueFormat="HH:mm:ss" />
                    </FormItem>
                </Col>
            </Row>
            <FormItem label="是否置顶" name="isTop">
                <Switch v-model:checked="formData.isTop" checked-children="是" un-checked-children="否" />
            </FormItem>
            <FormItem label="备注" name="remark">
                <Textarea :rows="3" v-model:value="formData.remark" placeholder="请输入备注" />
            </FormItem>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button type="primary" @click="modalConfirm">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { Form, FormItem, message, Row, Col, Switch, TimePicker, Textarea } from 'ant-design-vue'
import request from '/@/utils/request'

export default defineComponent({
    name: 'EditModal',
    components: { Form, FormItem, Row, Col, Switch, TimePicker, Textarea },
    props: {
        visible: Boolean,
        title: String,
        item: Object,
        currentDate: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, title, item, currentDate } = toRefs(props)

        const typeList = ref([])
        const upcomingStatusList = ref([])

        watch(visible, () => {
            if (visible.value) {
                if (item.value?.id) {
                    formData.value = item.value as any
                } else {
                    formData.value.upcomingDate = currentDate.value as any
                }
            }
        })

        const formData = ref({
            title: undefined,
            type: undefined,
            upcomingStatus: undefined,
            priority: undefined,
            upcomingDate: undefined,
            isWarn: true,
            warnTime: undefined,
            isTop: false,
            remark: undefined,
        })
        const rules: any = {
            title: {
                required: true,
                message: '请输入标题',
                type: 'string',
                trigger: ['change', 'blur'],
            },
            type: {
                required: true,
                message: '请选择类型',
                type: 'number',
                trigger: ['change', 'blur'],
            },
            upcomingStatus: {
                required: true,
                message: '请选择状态',
                type: 'number',
                trigger: ['change', 'blur'],
            },
            priority: {
                required: true,
                message: '请选择优先级',
                type: 'string',
                trigger: ['change', 'blur'],
            },
            upcomingDate: {
                required: true,
                message: '请选择待办日期',
                type: 'string',
                trigger: ['change', 'blur'],
            },
            warnTime: {
                required: true,
                message: '请选择提醒时间',
                type: 'string',
                trigger: ['change', 'blur'],
            },
        }
        const formInline = ref()
        const loading = ref(false)
        const resetData = () => {
            formData.value = {
                title: undefined,
                type: undefined,
                upcomingStatus: undefined,
                priority: undefined,
                upcomingDate: undefined,
                isWarn: true,
                warnTime: undefined,
                isTop: false,
                remark: undefined,
            }
        }
        const modalClose = () => {
            resetData()
            emit('cancel')
        }
        const modalConfirm = async () => {
            try {
                await formInline.value.validate()
                loading.value = true
                if (title.value?.includes('新增')) {
                    await request.post(`/api/hr-upcomings`, formData.value)
                    message.success('新增成功!')
                } else {
                    await request.put(`/api/hr-upcomings`, formData.value)
                    message.success('编辑成功!')
                }
                resetData()
                emit('confirm', formData.value)
            } catch (error) {
                console.log('confirm', error)
            } finally {
                loading.value = false
            }
        }

        const getTypeList = async () => {
            const res = await request.get(`/api/com-code-tables/getCodeTableByInnerName/scheduleType`)
            typeList.value = res.map((i) => ({
                label: i.itemName,
                value: i.itemValue,
            }))
        }
        const getUpcomingStatusList = async () => {
            const res = await request.get(`/api/com-code-tables/getCodeTableByInnerName/upcomingStatus`)
            upcomingStatusList.value = res.map((i) => ({
                label: i.itemName,
                value: i.itemValue,
            }))
        }

        // setup
        getTypeList()
        getUpcomingStatusList()

        return {
            typeList,
            upcomingStatusList,
            priorityList: [
                {
                    label: '高',
                    value: '0',
                },
                {
                    label: '中',
                    value: '1',
                },
                {
                    label: '低',
                    value: '2',
                },
            ],
            formInline,
            rules,
            formData,
            loading,
            modalClose,
            modalConfirm,
        }
    },
})
</script>

<style scoped lang="less"></style>
