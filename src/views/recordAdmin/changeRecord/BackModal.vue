<template>
    <BasicEditModalSlot title="档案归还" :visible="visible" @cancel="modalClose" @ok="modalConfirm" width="1100px">
        <div class="row">
            <div class="col">
                <div class="tit">档案编号</div>
                <div class="val">{{ formData.archivesNum }}</div>
            </div>
            <div class="col">
                <div class="tit">档案名称</div>
                <div class="val">{{ formData.archivesName }}</div>
            </div>
            <div class="col">
                <div class="tit">档案类型</div>
                <div class="val">{{ formData.archivesTypeStr }}</div>
            </div>
            <div class="col">
                <div class="tit">操作类型</div>
                <div class="val">{{ ctTypeList.find((i) => i.value == formData.ctType)?.label || '-' }}</div>
            </div>
        </div>
        <div class="row">
            <div class="tit">档案明细</div>
            <div class="val">
                <BasicTable
                    :tableDataList="formData.archivesDetailList"
                    :columns="columns"
                    :sorter="false"
                    :rowSelectionShow="false"
                    style="width: 900px"
                >
                    <template #appendixList="{ record }">
                        <a
                            v-for="i in record.appendixList"
                            :key="i.id"
                            href="javascipt: void(0)"
                            @click="previewFile(i.fileUrl)"
                            style="margin-right: 10px"
                        >
                            {{ i.originName }}
                        </a>
                    </template>
                </BasicTable>
            </div>
        </div>
        <div class="row">
            <div class="tit">备注</div>
            <div class="val" style="width: 900px">
                <Textarea v-model:value="formData.remark" :rows="3" placeholder="备注" />
            </div>
        </div>
        <div class="row">
            <div class="tit">附件</div>
            <div class="val">
                <div class="uploadBox">
                    <div class="item" v-for="(i, idx) in formData.appendixList" :key="i.id" :title="i.originName">
                        <a href="javascript: void(0)" @click="previewFile(i.fileUrl)">
                            <img v-if="['.jpg', '.png', '.jpeg'].includes(i.fileType)" class="img" :src="i.fileUrl" alt="" />
                            <div v-else class="txt">{{ i.originName }}</div>
                        </a>
                        <div class="after">
                            <Button size="small" type="primary" @click="download(i.fileUrl, i.originName)"> 下载 </Button>
                            <Button size="small" type="primary" danger @click="formData.appendixList.splice(idx, 1)">
                                删除
                            </Button>
                        </div>
                    </div>

                    <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                        <div class="item">
                            <CloudUploadOutlined class="icon" />
                            点击上传
                        </div>
                    </Upload>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { Textarea } from 'ant-design-vue'
import { previewFile } from '/@/utils/index'
import downFile from '/@/utils/downFile'
import { ctTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { CloudUploadOutlined } from '@ant-design/icons-vue'
import { uploadRecordFile } from '/@/utils/upload'

export default defineComponent({
    name: 'BackModal',
    components: { Textarea, CloudUploadOutlined },
    props: {
        visible: Boolean,
        currentId: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, currentId } = toRefs(props)
        const formData = ref<any>({})

        watch(visible, async () => {
            if (visible.value) {
                const res = await request.get(`/api/hr-archives-brings/${currentId.value}`)
                formData.value = res
            }
        })

        const modalClose = () => {
            emit('cancel')
        }
        const modalConfirm = async () => {
            formData.value.ctRemark = formData.value.remark
            await request.post(`/api/hr-archives-brings/return`, {
                id: formData.value.id,
                ctRemark: formData.value.ctRemark,
                appendixList: formData.value.appendixList,
            })
            emit('confirm')
        }
        const download = (url, name) => {
            downFile('get', url, name, null)
        }

        const beforeUpload = async (file) => {
            const res = await uploadRecordFile(file)
            formData.value.appendixList.push(res)
            return false
        }

        return {
            beforeUpload,
            ctTypeList,
            download,
            formData,
            modalClose,
            modalConfirm,
            previewFile,
            columns: [
                {
                    title: '名称',
                    dataIndex: 'name',
                    width: 200,
                },
                {
                    title: '性质',
                    dataIndex: 'type',
                    width: 100,
                    customRender: ({ text }) => {
                        return text == 1 ? '实体件' : '电子件'
                    },
                },
                {
                    title: '状态',
                    dataIndex: 'stateStr',
                    width: 100,
                    customRender: ({ record, text }) => {
                        return record.type == 1 ? text : ''
                    },
                },
                {
                    title: '附件',
                    dataIndex: 'appendixList',
                    slots: { customRender: 'appendixList' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.row {
    padding: 10px 0;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    .tit {
        width: 110px;
        &:after {
            content: '：';
        }
    }
    .col {
        width: 25%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .tit {
            width: 40%;
            &:after {
                content: '：';
            }
        }
        .val {
            width: 55%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
