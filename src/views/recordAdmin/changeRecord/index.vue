<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'changeRecord_export'" type="primary" @click="exportExcel">{{ exportText }}</Button>
        <Button v-auth="'changeRecord_delete'" type="primary" danger @click="deleteSome">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        :params="params"
        api="/api/hr-archives-brings/page"
        exportUrl="/api/hr-archives-brings/export"
        :columns="columns"
        @selectedRowsArr="selectedRows"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 查看详情 -->
    <DetailModal :visible="showDetail" :currentId="currentValue?.id" @cancel="detailClose" />
    <!-- 档案归还 -->
    <BackModal :visible="showBack" :currentId="currentValue?.id" @cancel="backClose" @confirm="backConfirm" />
</template>

<script lang="ts">
import { computed, defineComponent, h, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import DetailModal from './DetailModal.vue'
import { archivesStatusList, archivesTypeList, ctTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { getctProposesList } from '/@/utils/api'
import downFile from '/@/utils/downFile'
import BackModal from './BackModal.vue'
import { useRoute } from 'vue-router'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'ChangeRecord',
    components: { DetailModal, BackModal },
    setup() {
        const route = useRoute()
        //  超期未归还 isOverduePayment 0 否 1 是
        const params = ref(
            route.query?.type === 'archives_borrowing'
                ? {
                      isOverduePayment: 1,
                  }
                : {},
        )
        const tableRef = ref()
        const searchData = () => {
            tableRef.value.refresh(1)
        }

        const selectArr = ref<any[]>([])
        const selectedRows = (arr) => {
            selectArr.value = arr
        }

        const showCreateModal = ref(false)
        const createRow = () => {
            showCreateModal.value = true
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectArr.value)
        })

        const exportExcel = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        const deleteRow = (record) => {
            Modal.confirm({
                title: '确认',
                content: '确认删除该条数据?',
                onOk: async () => {
                    await request.post(`/api/hr-archives-brings/deletes`, [record.id])
                    searchData()
                },
            })
        }
        const deleteSome = () => {
            if (!selectArr.value.length) {
                message.warning('请选择删除数据！')
                return
            }
            Modal.confirm({
                title: '确认',
                content: '确认删除所选数据?',
                onOk: async () => {
                    await request.post(
                        `/api/hr-archives-brings/deletes`,
                        selectArr.value.map((i: any) => i.id),
                    )
                    searchData()
                },
            })
        }

        const currentValue = ref<Recordable>({})
        const showDetail = ref(false)
        const showRow = (record) => {
            currentValue.value = { ...record }
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
        }

        const showBack = ref(false)
        const backRow = (record) => {
            currentValue.value = { ...record }
            showBack.value = true
        }
        const backClose = () => {
            showBack.value = false
        }
        const backConfirm = () => {
            showBack.value = false
            searchData()
        }

        return {
            exportText,
            showBack,
            backClose,
            backConfirm,
            currentValue,
            showRow,
            showDetail,
            detailClose,
            selectedRows,
            showCreateModal,
            params,
            tableRef,
            deleteRow,
            deleteSome,
            exportExcel,
            createRow,
            selectArr,
            searchData,
            archivesStatusList,
            myOperation: getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'changeRecord_show',
                    show: true,
                    click: showRow,
                },
                {
                    neme: '归还',
                    auth: 'changeRecord_back',
                    show: (record) => record.ctType === 0 && record.returnState === 1, // 调出且未归还
                    click: backRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'changeRecord_delete',
                //     show: true,
                //     click: deleteRow,
                // },
            ]),
            columns: [
                {
                    title: '档案编号',
                    dataIndex: 'archivesNum',
                    align: 'center',
                    width: 115,
                },
                {
                    title: '客户名称',
                    dataIndex: 'empUnit',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '档案名称',
                    dataIndex: 'archivesName',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '档案类型',
                    dataIndex: 'archivesTypeStr',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '操作类型',
                    dataIndex: 'ctTypeStr',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '操作事由',
                    dataIndex: 'ctProposesStr',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '申请理由简述',
                    dataIndex: 'ctReasons',
                    align: 'center',
                    width: 200,
                    ellipsis: true,
                },
                {
                    title: '操作时间',
                    dataIndex: 'createdDate',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    slots: { customRender: 'operation' },
                    width: 160,
                    fixed: 'right',
                },
            ],
            searchOptions: [
                {
                    label: '档案编号',
                    key: 'archivesNum',
                },
                {
                    label: '客户名称',
                    key: 'clientId',
                    type: 'clientSelectTree',
                },
                {
                    label: '档案名称',
                    key: 'archivesName',
                },
                {
                    label: '档案类型',
                    key: 'archivesType',
                    type: 'select',
                    options: archivesTypeList,
                },
                {
                    label: '操作类型',
                    key: 'ctType',
                    type: 'select',
                    options: ctTypeList,
                },
                {
                    label: '操作事由',
                    key: 'ctProposes',
                    type: 'select',
                    options: [],
                    getMethod: getctProposesList,
                },
                {
                    label: '操作时间',
                    key: 'createdDateQuery',
                    type: 'datetimerange',
                },
                {
                    label: '申请理由简述',
                    key: 'ctReasons',
                },
                {
                    label: '是否超期未还',
                    key: 'isOverduePayment',
                    type: 'select',
                    options: [
                        {
                            label: '是',
                            value: 1,
                        },
                        {
                            label: '否',
                            value: 0,
                        },
                    ],
                },
            ],
        }
    },
})
</script>

<style scoped lang="less"></style>
