<template>
    <BasicEditModalSlot
        :title="formData.ctType === 0 ? '档案调出' : formData.ctType === 1 ? '档案调入' : '档案归还'"
        :visible="visible"
        @cancel="modalClose"
        :footer="null"
        width="1200px"
    >
        <div class="detail">
            <div class="row">
                <div class="cell">
                    <div class="tit">档案编号</div>
                    <div class="val">{{ formData.archivesNum }}</div>
                </div>
                <div class="cell">
                    <div class="tit">档案名称</div>
                    <div class="val">{{ formData.archivesName }}</div>
                </div>
                <div class="cell">
                    <div class="tit">档案类型</div>
                    <div class="val">{{ formData.archivesTypeStr }}</div>
                </div>
                <div class="cell">
                    <div class="tit">操作类型</div>
                    <div class="val">{{ ctTypeList.find((i) => i.value == formData.ctType)?.label || '-' }}</div>
                </div>
            </div>
            <div class="row" v-if="formData.archivesType === 0">
                <div class="cell">
                    <div class="tit">员工编号</div>
                    <div class="val">{{ formData.systemNum }}</div>
                </div>
                <div class="cell">
                    <div class="tit">员工姓名</div>
                    <div class="val">{{ formData.archivesName }}</div>
                </div>
                <div class="cell">
                    <div class="tit">身份证号</div>
                    <div class="val">{{ formData.certificateNum }}</div>
                </div>
                <div class="cell">
                    <div class="tit">手机号</div>
                    <div class="val">{{ formData.phone }}</div>
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="tit">操作事由</div>
                    <div class="val">{{ formData.ctProposesStr }}</div>
                </div>
                <div class="cell" v-if="formData.ctType === 0">
                    <div class="tit">申请理由简述</div>
                    <div class="val">{{ formData.ctReasons }}</div>
                </div>
            </div>
            <div class="row" v-if="formData.ctType === 0">
                <div class="cell">
                    <div class="tit">申请人</div>
                    <div class="val">{{ formData.ctName }}</div>
                </div>
                <div class="cell">
                    <div class="tit">手机号</div>
                    <div class="val">{{ formData.ctPhone }}</div>
                </div>
                <div class="cell">
                    <div class="tit">身份证号</div>
                    <div class="val">{{ formData.ctIdCard }}</div>
                </div>
            </div>
            <div class="row" v-if="formData.ctType === 0">
                <div class="tit">申请理由明细</div>
                <div class="val">
                    {{ formData.ctDetail }}
                </div>
            </div>
            <div class="row">
                <div class="tit">档案明细</div>
                <div class="val">
                    <BasicTable
                        size="small"
                        :tableDataList="formData.archivesDetailList"
                        :columns="columns"
                        :sorter="false"
                        :rowSelectionShow="false"
                        style="width: 1000px"
                    >
                        <template #appendixList="{ record }">
                            <a
                                v-for="i in record.appendixList"
                                :key="i.id"
                                href="javascipt: void(0)"
                                @click="previewFile(i.fileUrl)"
                                style="margin-right: 10px"
                            >
                                {{ i.originName }}
                            </a>
                        </template>
                        <template #operation="{ record }">
                            <Button
                                v-if="record.appendixList && record.appendixList.length"
                                size="small"
                                type="primary"
                                style="margin-left: 5px"
                                @click="downloadRow(record)"
                            >
                                下载
                            </Button>
                        </template>
                    </BasicTable>
                </div>
            </div>
            <div class="row" v-if="formData.ctType === 0">
                <div class="tit">调出目的地</div>
                <div class="val">
                    {{ formData.ctDestination }}
                </div>
            </div>
            <div class="row">
                <div class="tit">备注</div>
                <div class="val">
                    {{ formData.ctRemark }}
                </div>
            </div>
            <div class="row">
                <div class="tit">附件</div>
                <div class="val">
                    <div class="uploadBox">
                        <div class="item" v-for="i in formData.appendixList" :key="i.id" :title="i.originName">
                            <a href="javascript: void(0)" @click="previewFile(i.fileUrl)">
                                <img v-if="['.jpg', '.png', '.jpeg'].includes(i.fileType)" class="img" :src="i.fileUrl" alt="" />
                                <div v-else class="txt">{{ i.originName }}</div>
                            </a>
                            <div class="after">
                                <Button size="small" type="primary" @click="download(i.fileUrl, i.originName)"> 下载 </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import downFile, { downMultFile } from '/@/utils/downFile'
import { previewFile } from '/@/utils/index'
import { ctTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'

export default defineComponent({
    name: 'DetailModal',
    props: {
        visible: Boolean,
        currentId: String,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { currentId, visible } = toRefs(props)

        watch(visible, () => {
            visible.value && currentId.value && getData()
        })

        const formData = ref<any>({})

        const getData = async () => {
            const res = await request.get(`/api/hr-archives-brings/${currentId.value}`)
            formData.value = res
        }

        const modalClose = () => {
            emit('cancel')
        }

        const download = (url, name) => {
            downFile('get', url, name, null)
        }

        const downloadRow = (record) => {
            downMultFile(
                `${record.name}附件`,
                record.appendixList.map((i) => i.fileUrl),
                record.appendixList.map((i) => i.name),
            )
        }

        return {
            downloadRow,
            download,
            formData,
            ctTypeList,
            modalClose,
            previewFile,
            columns: [
                {
                    title: '名称',
                    dataIndex: 'name',
                    width: 200,
                },
                {
                    title: '性质',
                    dataIndex: 'type',
                    width: 100,
                    customRender: ({ text }) => {
                        return text == 1 ? '实体件' : '电子件'
                    },
                },
                {
                    title: '状态',
                    dataIndex: 'stateStr',
                    width: 100,
                    customRender: ({ record, text }) => {
                        return record.type == 1 ? text : ''
                    },
                },
                {
                    title: '附件',
                    dataIndex: 'appendixList',
                    slots: { customRender: 'appendixList' },
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 120,
                    slots: { customRender: 'operation' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.record {
    width: 100%;
    min-height: 500px;
}
.detail {
    width: 100%;
    min-height: 500px;
    .row {
        padding: 10px 0;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        .tit {
            width: 110px;
            &:after {
                content: '：';
            }
        }
        .cell {
            width: 25%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .tit {
                width: 40%;
                &:after {
                    content: '：';
                }
            }
            .val {
                width: 55%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
