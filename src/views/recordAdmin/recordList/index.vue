<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'recordList_create'" type="primary" @click="createRow">新增</Button>
        <Button v-auth="'recordList_import'" type="primary" @click="importData">导入</Button>
        <Button v-auth="'recordList_export'" type="primary" @click="exportExcel">{{ exportText }}</Button>
        <Button v-auth="'recordList_update'" class="warning-btn" @click="updateSome">批量修改</Button>
        <Button v-auth="'recordList_delete'" type="primary" danger @click="deleteSome">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        :params="params"
        api="/api/hr-archives-manage/page"
        exportUrl="/api/hr-archives-manage/export"
        :columns="columns"
        @selectedRowsArr="selectedRows"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 新增档案 -->
    <CreateModal :visible="showCreateModal" @cancel="modalClose" @confirm="modalConfirm" />
    <!-- 批量修改 -->
    <BasicEditModalSlot title="批量修改" :visible="showUpdate" @cancel="updateClose" @ok="updateConfirm">
        <div style="padding: 20px 0">
            批量修改{{ selectArr.length }}份档案的状态为：
            <Select
                v-model:value="updateStatus"
                :options="archivesStatusList"
                placeholder="请选择修改状态"
                style="width: 200px"
            />
        </div>
    </BasicEditModalSlot>
    <!-- 查看详情 -->
    <DetailModal :visible="showDetail" :currentData="currentData" @cancel="detailClose" @confirm="detailConfirm" />
    <!-- 导入 -->
    <ImportModal
        title="档案导入"
        v-model:visible="showImport"
        temUrl="/api/hr-archives-manage/template"
        importUrl="/api/hr-archives-manage/import"
        @getResData="searchData"
    />
    <!-- 调入调出 -->
    <TransferModal
        :title="transferTitle"
        :visible="showTransfer"
        :currentData="currentData"
        @cancel="transferClose"
        @confirm="transferConfirm"
    />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import CreateModal from './CreateModal.vue'
import DetailModal from './DetailModal.vue'
import TransferModal from './TransferModal.vue'
import { getDomicilePlaceList } from '/@/utils/api'
import { archivesStatusList, archivesTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import downFile from '/@/utils/downFile'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'RecordList',
    components: { CreateModal, DetailModal, TransferModal },
    setup() {
        const params = ref({
            clientId: undefined,
        })
        const tableRef = ref()
        const searchData = () => {
            tableRef.value.refresh(1)
        }

        const selectArr = ref<any[]>([])
        const selectedRows = (arr) => {
            selectArr.value = arr
        }

        const showCreateModal = ref(false)
        const createRow = () => {
            showCreateModal.value = true
        }
        const modalConfirm = () => {
            showCreateModal.value = false
            searchData()
        }
        const modalClose = () => {
            showCreateModal.value = false
        }

        const showImport = ref(false)
        const importData = () => {
            showImport.value = true
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectArr.value)
        })
        const exportExcel = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        const deleteRow = (record) => {
            Modal.confirm({
                title: '确认',
                content: '确认删除该条数据?',
                onOk: async () => {
                    await request.post(`/api/hr-archives-manage/deletes`, [record.id])
                    searchData()
                },
            })
        }
        const deleteSome = () => {
            if (!selectArr.value.length) {
                message.warning('请选择删除数据！')
                return
            }
            Modal.confirm({
                title: '确认',
                content: '确认删除所选数据?',
                onOk: async () => {
                    await request.post(
                        `/api/hr-archives-manage/deletes`,
                        selectArr.value.map((i: any) => i.id),
                    )
                    searchData()
                },
            })
        }

        const showUpdate = ref(false)
        const updateSome = () => {
            if (!selectArr.value.length) {
                message.warning('请选择所要修改的数据！')
                return
            }
            showUpdate.value = true
        }
        const updateStatus = ref(undefined)
        const updateClose = () => {
            updateStatus.value = undefined
            showUpdate.value = false
        }
        const updateConfirm = async () => {
            if (!updateStatus.value && updateStatus.value !== 0) {
                message.warning('请选择修改状态！')
                return
            }
            await request.put(
                `/api/hr-archives-manage/batch?status=${updateStatus.value}`,
                selectArr.value.map((i: any) => i.id),
            )
            updateClose()
            tableRef.value.refresh()
        }

        const currentData = ref(undefined)
        const showDetail = ref(false)
        const showRow = (record) => {
            currentData.value = { ...record }
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
            tableRef.value.refresh()
        }
        const detailConfirm = (type) => {
            showDetail.value = false
            tableRef.value.refresh()
            if (type == '调入') {
                transferIn(currentData.value)
            } else if (type == '调出') {
                transferOut(currentData.value)
            } else {
                currentData.value = undefined
            }
        }

        const showTransfer = ref(false)
        const transferTitle = ref('档案调入')
        const transferIn = (record) => {
            transferTitle.value = '档案调入'
            currentData.value = { ...record }
            showTransfer.value = true
        }
        const transferOut = (record) => {
            transferTitle.value = '档案调出'
            currentData.value = { ...record }
            showTransfer.value = true
        }
        const transferClose = () => {
            showTransfer.value = false
            currentData.value = undefined
        }
        const transferConfirm = () => {
            showTransfer.value = false
            tableRef.value.refresh()
            currentData.value = undefined
        }

        return {
            exportText,
            transferTitle,
            transferClose,
            transferConfirm,
            showTransfer,
            showImport,
            currentData,
            showRow,
            showDetail,
            detailClose,
            detailConfirm,
            showUpdate,
            updateStatus,
            updateClose,
            updateConfirm,
            selectedRows,
            showCreateModal,
            modalClose,
            modalConfirm,
            params,
            tableRef,
            transferIn,
            transferOut,
            importData,
            deleteRow,
            deleteSome,
            updateSome,
            exportExcel,
            createRow,
            selectArr,
            searchData,
            archivesStatusList,
            myOperation: getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'recordList_show',
                    show: true,
                    click: showRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'recordList_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
                {
                    neme: '调入',
                    auth: 'recordList_transferIn',
                    show: true,
                    click: transferIn,
                },
                {
                    neme: '调出',
                    auth: 'recordList_transferOut',
                    show: true,
                    click: transferOut,
                },
            ]),
            columns: [
                {
                    title: '档案编号',
                    dataIndex: 'archivesNum',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '客户名称',
                    dataIndex: 'empUnit',
                    align: 'center',
                    width: 200,
                    ellipsis: true,
                },
                {
                    title: '档案名称',
                    dataIndex: 'archivesName',
                    align: 'center',
                    width: 200,
                    ellipsis: true,
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '档案类型',
                    dataIndex: 'archivesTypeStr',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '档案所处位置',
                    dataIndex: 'archivesLocal',
                    align: 'center',
                    width: 200,
                    ellipsis: true,
                },
                {
                    title: '档案状态',
                    dataIndex: 'archivesStatusStr',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '档案明细',
                    dataIndex: 'hrArchivesDetailList',
                    align: 'center',
                    width: 250,
                    ellipsis: true,
                    customRender: ({ text }) => {
                        return text?.map((i, idx) => (idx == 0 ? i.name : '、' + i.name))
                    },
                    sorter: false,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    slots: { customRender: 'operation' },
                    width: 230,
                    fixed: 'right',
                },
            ],
            searchOptions: [
                {
                    label: '档案编号',
                    key: 'archivesNum',
                },
                {
                    label: '客户名称',
                    key: 'clientIds',
                    type: 'clientSelectTree',
                    maxTag: '0',
                    multiple: true,
                    checkStrictly: false,
                },
                {
                    label: '档案名称',
                    key: 'archivesName',
                },
                {
                    label: '身份证号',
                    key: 'certificateNum',
                },
                {
                    label: '档案类型',
                    key: 'archivesType',
                    type: 'select',
                    options: archivesTypeList,
                },
                {
                    label: '档案所处位置',
                    key: 'archivesLocal',
                },
                {
                    label: '档案状态',
                    key: 'archivesStatus',
                    type: 'select',
                    options: archivesStatusList,
                },
                {
                    label: '档案明细',
                    key: 'detailName',
                },
                {
                    label: '户口所在地',
                    key: 'domicilePlace',
                    type: 'select',
                    options: [],
                    getMethod: getDomicilePlaceList,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less"></style>
