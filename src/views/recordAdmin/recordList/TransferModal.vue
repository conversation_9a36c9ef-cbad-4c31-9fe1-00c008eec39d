<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="1000px">
        <Form ref="formInline" :model="formData" :label-col="{ span: 9 }" :wrapper-col="{ span: 14 }" :rules="rules">
            <Row>
                <Col span="8">
                    <FormItem label="档案名称" name="archivesName">
                        <Input disabled v-model:value="formData.archivesName" placeholder="档案名称" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="档案编号" name="archivesNum">
                        <Input disabled v-model:value="formData.archivesNum" placeholder="档案编号" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="档案类型" name="archivesType">
                        <Select
                            disabled
                            v-model:value="formData.archivesType"
                            :options="archivesTypeList"
                            placeholder="档案类型"
                        />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                    <FormItem label="操作事由" name="ctProposes">
                        <Select
                            :disabled="viewType == 'laborContract' || viewType == 'laborContractRe'"
                            v-model:value="formData.ctProposes"
                            :options="ctProposesList"
                            placeholder="操作事由"
                        />
                    </FormItem>
                </Col>
                <Col span="8" v-if="viewType == 'laborContractRe'">
                    <FormItem label="合同开始日期" name="contractStartDate">
                        <DatePicker
                            v-model:value="formData.contractStartDate"
                            format="YYYY-MM-DD"
                            placeholder="合同开始日期"
                            valueFormat="YYYY-MM-DD"
                            :disabled="true"
                        />
                    </FormItem>
                </Col>
                <Col span="8" v-if="viewType == 'laborContractRe'">
                    <FormItem label="合同结束日期" name="contractEndDate">
                        <DatePicker
                            v-model:value="formData.contractEndDate"
                            format="YYYY-MM-DD"
                            placeholder="合同结束日期"
                            valueFormat="YYYY-MM-DD"
                            :disabled-date="(current) => current && current < moment(formData.contractStartDate).endOf('day')"
                        />
                    </FormItem>
                </Col>
                <Col span="8" v-if="title.includes('调出')">
                    <FormItem label="申请理由简述" name="ctReasons">
                        <Input :maxlength="10" v-model:value="formData.ctReasons" placeholder="最多不超过10个字" />
                    </FormItem>
                </Col>
            </Row>

            <template v-if="title.includes('调出')">
                <Row>
                    <Col span="8">
                        <FormItem label="申请人" name="ctName">
                            <Input v-model:value="formData.ctName" placeholder="申请人" />
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="申请人手机号" name="ctPhone">
                            <Input v-model:value="formData.ctPhone" placeholder="申请人手机号" />
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="申请人身份证号" name="ctIdCard">
                            <Input v-model:value="formData.ctIdCard" placeholder="申请人身份证号" />
                        </FormItem>
                    </Col>
                </Row>
                <FormItem label="申请理由明细" name="ctDetail" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }">
                    <Textarea :rows="3" v-model:value="formData.ctDetail" placeholder="申请理由明细" />
                </FormItem>
            </template>

            <FormItem
                v-if="!title.includes('调出')"
                label="档案明细"
                name="archivesDetailList"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 18 }"
            >
                <Button @click="createDetail" size="small" type="primary">添加明细</Button>
                <Card size="small" v-for="(i, idx) in formData.archivesDetailList" :key="idx" style="margin-top: 10px">
                    <template #title>
                        <div class="cardTitle">
                            <div>
                                <Tag :color="i.type == 1 ? 'green' : 'blue'"> {{ i.type == 1 ? '实体件' : '电子件' }} </Tag>
                                {{ i.name }}
                            </div>
                            <div>
                                <FormOutlined @click="editOne(i, idx)" />
                                <CloseSquareOutlined @click="removeOne(idx)" style="margin-left: 10px" />
                            </div>
                        </div>
                    </template>
                    <div class="flexStart">
                        <a v-for="f in i.appendixList" :key="f.id" :href="f.fileUrl" target="_blank">
                            {{ f.originName }}
                        </a>
                        <span v-if="!i.appendixList || !i.appendixList.length">无附件</span>
                    </div>
                </Card>
            </FormItem>
            <FormItem v-else label="档案明细" name="selDetailNames" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }">
                <Select
                    v-model:value="formData.selDetailNames"
                    mode="multiple"
                    :options="detailNamesList"
                    placeholder="请选择档案明细"
                />
            </FormItem>

            <template v-if="title.includes('调出')">
                <FormItem label="调出目的地" name="ctDestination" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }">
                    <Input :rows="3" v-model:value="formData.ctDestination" placeholder="某档案保管中心/本人提取" />
                </FormItem>
                <Row>
                    <Col span="8">
                        <FormItem label="是否需要归还" name="returnOnTime">
                            <RadioGroup v-model:value="formData.returnOnTime">
                                <Radio :value="0">是</Radio>
                                <Radio :value="1">否</Radio>
                            </RadioGroup>
                        </FormItem>
                    </Col>
                    <Col span="8" v-if="formData.returnOnTime === 0">
                        <FormItem label="预计归还时间" name="estimateReturnTime">
                            <DatePicker
                                v-model:value="formData.estimateReturnTime"
                                placeholder="预计归还时间"
                                valueFormat="YYYY-MM-DD"
                            />
                        </FormItem>
                    </Col>
                </Row>
            </template>

            <FormItem label="备注" name="ctRemark" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }">
                <Textarea :rows="3" v-model:value="formData.ctRemark" placeholder="备注" />
            </FormItem>

            <FormItem label="附件" name="appendixList" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }">
                <div class="uploadBox">
                    <div class="item" v-for="(i, idx) in formData.appendixList" :key="i.id" :title="i.originName">
                        <a :href="i.fileUrl" target="_blank">
                            <img v-if="['.jpg', '.png', '.jpeg'].includes(i.fileType)" class="img" :src="i.fileUrl" alt="" />
                            <div v-else class="txt">{{ i.originName }}</div>
                        </a>
                        <div class="after">
                            <Button size="small" type="primary" @click="download(i.fileUrl, i.originName)"> 下载 </Button>
                            <Button size="small" type="primary" danger @click="formData.appendixList.splice(idx, 1)">
                                删除
                            </Button>
                        </div>
                    </div>
                    <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                        <div class="item">
                            <CloudUploadOutlined class="icon" />
                            点击上传
                        </div>
                    </Upload>
                </div>
            </FormItem>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button type="primary" @click="modalConfirm">确定</Button>
        </template>
    </BasicEditModalSlot>
    <!-- 编辑明细 -->
    <EditDetailName
        :visible="showDetail"
        :currentValue="detailForm"
        :detailTitle="detailTitle"
        @confirm="detailConfirm"
        @cancel="detailClose"
    />
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { CloudUploadOutlined, FormOutlined, CloseSquareOutlined } from '@ant-design/icons-vue'
import request from '/@/utils/request'
import { archivesTypeList } from '/@/utils/dictionaries'
import { uploadRecordFile } from '/@/utils/upload'
import downFile from '/@/utils/downFile'
import EditDetailName from './EditDetailName.vue'
import { Card, Tag } from 'ant-design-vue'
import moment from 'moment'

export default defineComponent({
    name: 'TransferModal',
    components: { CloudUploadOutlined, FormOutlined, CloseSquareOutlined, EditDetailName, Card, Tag },
    props: {
        visible: Boolean,
        title: {
            type: String,
            default: '档案调入',
        },
        currentData: Object,
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { currentData, visible, title, viewType } = toRefs(props)

        watch(visible, () => {
            visible.value && currentData.value?.id && getData()
            if (viewType.value == 'laborContract' || viewType.value == 'laborContractRe') {
                if (!visible.value) return
                if (viewType.value == 'laborContractRe') {
                    if (currentData.value?.staffData?.contractEndDate) {
                        formData.value.contractStartDate = moment(currentData.value?.staffData?.contractEndDate)
                            .add(1, 'days')
                            .format('YYYY-MM-DD')
                        formData.value.contractEndDate = moment(currentData.value?.staffData?.contractEndDate)
                            .add(3, 'years')
                            .format('YYYY-MM-DD')
                    }
                }

                formData.value.ctProposes = 3
            }
        })

        const formData = ref<inObject>({
            archivesType: undefined,
            archivesName: '',
            detailName: '',
            archivesDetailList: [],
            remark: '',
            selDetailNames: [],
        })

        const detailNamesList = ref<any[]>([])

        const getData = async () => {
            formData.value = currentData.value as any
            formData.value.archivesStatus = currentData.value?.archivesStatus?.toString()
            formData.value.archivesType = currentData.value?.archivesType?.toString()
            formData.value.appendixList =
                currentData.value?.appendixList && currentData.value.appendixList.length ? currentData.value.appendixList : []
            if (title.value.includes('调出')) {
                // 调出 在已存在的明细里选
                const res = await request.get(`/api/hr-archives-brings/archives-detail-list/${formData.value.id}`)
                detailNamesList.value = res.map((i) => ({
                    ...i,
                    label: i.name,
                    value: i.id,
                }))
            } else {
                // 调入 手动添加明细
                formData.value.archivesDetailList = []
            }
        }

        const rules = {
            archivesDetailList: { required: true, type: 'array', message: '至少输入一条档案明细', trigger: 'change' },
            selDetailNames: { required: true, type: 'array', message: '至少选择一条档案明细', trigger: 'change' },
            ctProposes: { required: true, type: 'number', message: '请选择操作事由', trigger: 'blur' },
            ctReasons: { required: true, message: '请输入申请理由简述', trigger: 'blur' },
            ctName: { required: true, message: '请输入申请人', trigger: 'blur' },
            ctPhone: { required: true, message: '请输入申请人手机号', trigger: 'blur' },
            ctIdCard: { required: true, message: '请输入申请人身份证号', trigger: 'blur' },
            ctDestination: { required: true, message: '请输入调出目的地', trigger: 'blur' },
            returnOnTime: { required: true, type: 'number', message: '请选择是否需要按时归还', trigger: 'blur' },
            estimateReturnTime: { required: true, message: '请选择预计归还时间', trigger: 'blur' },

            contractStartDate: { required: true, message: '请选择合同开始日期', trigger: 'blur' },
            contractEndDate: { required: true, message: '请选择合同结束日期', trigger: 'blur' },
        }

        const modalClose = () => {
            emit('cancel')
            resetData()
        }
        const loading = ref(false)
        const formInline = ref()
        const modalConfirm = async () => {
            try {
                await formInline.value.validate()

                loading.value = true
                const form = { ...formData.value }
                form.archivesId = formData.value.id
                form.ctType = title.value.includes('调出') ? 0 : 1
                if (title.value.includes('调出')) {
                    form.archivesDetailList = formData.value.selDetailNames?.map((i) => ({ id: i }))
                }
                delete form.id
                delete form.createdDate
                delete form.createdBy
                delete form.lastModifiedBy
                delete form.lastModifiedDate
                delete form.isDelete

                //录入劳动合同专用
                if (viewType.value == 'laborContract' || viewType.value == 'laborContractRe') {
                    let parameter: any = { ...form, isRenewal: false }
                    if (viewType.value == 'laborContractRe') {
                        parameter = {
                            ...form,
                            clientId: currentData.value?.staffData?.clientId,
                            staffId: currentData.value?.staffData?.id,
                            isRenewal: true,
                        }
                    }
                    // currentValue.value?
                    await request.post(`/api/hr-archives-brings/enter-labor-contract`, parameter)
                } else {
                    await request.post(`/api/hr-archives-brings`, form)
                }

                resetData()
                emit('confirm')
            } finally {
                loading.value = false
            }
        }
        const resetData = () => {
            formData.value = {}
        }
        const beforeUpload = async (file) => {
            const res = await uploadRecordFile(file)
            formData.value.appendixList.push(res)
            return false
        }

        const ctProposesList = ref<any[]>([])
        const getctProposesList = async () => {
            const res = await request.get(`/api/com-code-tables/getCodeTableByInnerName/operationReason`)
            ctProposesList.value = res.map((i) => ({
                label: i.itemName,
                value: i.itemValue,
            }))
        }
        getctProposesList()

        const download = (url, name) => {
            downFile('get', url, name, {})
        }

        const detailForm = ref<inObject>({
            name: undefined,
            type: undefined,
            appendixList: [],
        })
        const detailTitle = ref('档案明细')
        const detailIndex = ref(0)
        const showDetail = ref(false)
        const createDetail = () => {
            detailTitle.value = '添加档案明细'
            detailForm.value = {
                name: undefined,
                type: undefined,
                appendixList: [],
            }
            showDetail.value = true
        }
        const editOne = (item, idx) => {
            detailTitle.value = '编辑档案明细'
            detailForm.value = { ...item }
            detailIndex.value = idx
            showDetail.value = true
        }
        const removeOne = (idx) => {
            formData.value.archivesDetailList.splice(idx, 1)
        }
        const detailClose = () => {
            showDetail.value = false
        }
        const detailConfirm = async () => {
            if (detailTitle.value.includes('添加')) {
                formData.value.archivesDetailList.push(detailForm.value)
            } else {
                formData.value.archivesDetailList[detailIndex.value] = detailForm.value
            }
            showDetail.value = false
        }

        return {
            moment,

            detailForm,
            detailTitle,
            showDetail,
            createDetail,
            editOne,
            removeOne,
            detailClose,
            detailConfirm,

            download,
            formInline,
            beforeUpload,
            rules,
            formData,
            modalClose,
            modalConfirm,
            archivesTypeList,
            detailNamesList,
            ctProposesList,
        }
    },
})
</script>

<style scoped lang="less">
.tag {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
}
.tags {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}
.cardTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
