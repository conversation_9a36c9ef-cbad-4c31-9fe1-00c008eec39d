<template>
    <BasicEditModalSlot :title="detailTitle" :visible="visible" @cancel="detailClose" @ok="detailConfirm" width="700px">
        <Form ref="detailFormRef" :rules="detailRules" :model="detailForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem label="名称" name="name">
                <Input v-model:value="detailForm.name" placeholder="名称" />
            </FormItem>
            <FormItem label="类型" name="type">
                <RadioGroup v-model:value="detailForm.type">
                    <Radio :value="0">电子件</Radio>
                    <Radio :value="1">实体件</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="附件">
                <div class="uploadBox">
                    <div class="item" v-for="(i, idx) in detailForm.appendixList" :key="idx" :title="i.fileUrl">
                        <a href="javascript: void(0)" @click="previewFile(i.fileUrl)">
                            <img v-if="['.jpg', '.png', '.jpeg'].includes(i.fileType)" class="img" :src="i.fileUrl" alt="" />
                            <div v-else class="txt">{{ i.originName }}</div>
                        </a>
                        <div class="after">
                            <Button size="small" type="primary" @click="download(i.fileUrl)"> 下载 </Button>
                            <Button size="small" type="primary" danger @click="detailForm.appendixList.splice(idx, 1)">
                                删除
                            </Button>
                        </div>
                    </div>
                    <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                        <div class="item">
                            <CloudUploadOutlined class="icon" />
                            点击上传
                        </div>
                    </Upload>
                </div>
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { CloudUploadOutlined } from '@ant-design/icons-vue'
import { Upload } from 'ant-design-vue'
import downFile from '/@/utils/downFile'
import { uploadRecordFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'

export default defineComponent({
    name: 'EditDetailName',
    components: {
        Upload,
        CloudUploadOutlined,
    },
    props: {
        visible: Boolean,
        detailTitle: String,
        currentValue: Object,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, currentValue } = toRefs(props)

        watch(visible, () => {
            if (visible.value) {
                detailForm.value = currentValue.value as any
            }
        })

        const download = (url) => {
            downFile('get', url, url, {})
        }

        const detailForm = ref<inObject>({
            name: undefined,
            type: undefined,
            appendixList: [],
        })
        const beforeUpload = async (file) => {
            const res = await uploadRecordFile(file)
            // console.log('upload', res)
            detailForm.value.appendixList.push(res)
            return false
        }
        const detailFormRef = ref()
        const detailClose = () => {
            emit('cancel')
            detailFormRef.value.resetFields()
        }
        const detailConfirm = async () => {
            try {
                await detailFormRef.value.validate()
                emit('confirm', detailForm.value)
            } catch (error) {
                console.log('表单验证失败', error)
            }
        }
        return {
            previewFile,
            detailFormRef,
            detailForm,
            download,
            beforeUpload,
            detailClose,
            detailConfirm,
            detailRules: {
                name: { required: true, message: '请输入名称', trigger: 'blur' },
                type: { required: true, type: 'number', message: '请选择类型', trigger: 'change' },
            },
        }
    },
})
</script>

<style scoped lang="less"></style>
