<template>
    <BasicEditModalSlot title="查看详情" :visible="visible" @cancel="modalClose" width="1200px">
        <Tabs v-model:activeKey="activeKey">
            <TabPane key="detail" tab="档案详情" />
            <TabPane key="record" tab="变更记录" />
        </Tabs>
        <div class="detail" v-if="activeKey == 'detail'">
            <div class="top">
                <div class="item">
                    <div class="label">档案编号</div>
                    <Input class="input" v-model:value="formData.archivesNum" :disabled="viewType == 'see'" />
                </div>
                <div class="item">
                    <div class="label">档案所处位置</div>
                    <Input class="input" v-model:value="formData.archivesLocal" :disabled="viewType == 'see'" />
                </div>
                <div class="item">
                    <div class="label">档案状态</div>
                    <Select
                        class="input"
                        v-model:value="formData.archivesStatus"
                        :options="archivesStatusList"
                        :disabled="viewType == 'see'"
                    />
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="tit">单位编号</div>
                    <div class="val">{{ formData.unitNumber }}</div>
                </div>
                <div class="cell">
                    <div class="tit">客户名称</div>
                    <div class="val">{{ formData.empUnit }}</div>
                </div>
            </div>
            <div class="row" v-if="formData.archivesType === 0">
                <div class="cell">
                    <div class="tit">员工编号</div>
                    <div class="val">{{ formData.systemNum }}</div>
                </div>
                <div class="cell">
                    <div class="tit">姓名</div>
                    <div class="val">{{ formData.archivesName }}</div>
                </div>
                <div class="cell">
                    <div class="tit">身份证号</div>
                    <div class="val">{{ formData.certificateNum }}</div>
                </div>
                <div class="cell">
                    <div class="tit">手机号</div>
                    <div class="val">{{ formData.phone }}</div>
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="tit">档案名称</div>
                    <div class="val">{{ formData.archivesName }}</div>
                </div>
                <div class="cell">
                    <div class="tit">档案类型</div>
                    <div class="val">{{ formData.archivesTypeStr }}</div>
                </div>
                <div class="cell">
                    <div class="tit">入库时间</div>
                    <div class="val">{{ formData.warehouseTime }}</div>
                </div>
                <div class="cell">
                    <div class="tit">整理人</div>
                    <div class="val">{{ formData.createdBy }}</div>
                </div>
            </div>
            <div class="row">
                <div class="tit">档案明细</div>
                <div class="val">
                    <BasicTable
                        size="small"
                        :tableDataList="formData.hrArchivesDetailList"
                        :columns="detailColumns"
                        :sorter="false"
                        :rowSelectionShow="false"
                        style="width: 1000px"
                    >
                        <template #appendixList="{ record }">
                            <a
                                v-for="i in record.appendixList"
                                :key="i.id"
                                href="javascript: void(0)"
                                @click="previewFile(i.fileUrl)"
                                style="margin-right: 10px"
                            >
                                {{ i.originName }}
                            </a>
                        </template>
                        <template #operation="{ record, index }">
                            <Upload :beforeUpload="uploadRow" :showUploadList="false">
                                <Button size="small" @click="uploadIndex = index">上传</Button>
                            </Upload>
                            <Button
                                v-if="record.appendixList && record.appendixList.length"
                                size="small"
                                type="primary"
                                style="margin-left: 5px"
                                @click="downloadRow(record)"
                            >
                                下载
                            </Button>
                        </template>
                    </BasicTable>
                </div>
            </div>
        </div>
        <div v-if="activeKey == 'record'" class="record">
            <BasicTable
                useIndex
                api="/api/hr-archives-brings/page"
                :params="{
                    archivesId: currentData?.id,
                }"
                :columns="columns"
                :scroll="{ y: '500' }"
                :sorter="false"
                :rowSelectionShow="false"
            >
                <template #operation="{ record }">
                    <Button size="small" type="primary" @click="showRow(record)">查看</Button>
                </template>
            </BasicTable>
        </div>
        <template #footer>
            <template v-if="activeKey == 'detail'">
                <Button @click="modalClose">取消</Button>
                <Button
                    v-auth="'recordList_transferIn'"
                    :loading="loading"
                    type="primary"
                    @click="modalConfirm('调入')"
                    v-if="viewType != 'see'"
                >
                    调入
                </Button>
                <Button
                    v-auth="'recordList_transferOut'"
                    :loading="loading"
                    type="primary"
                    @click="modalConfirm('调出')"
                    v-if="viewType != 'see'"
                >
                    调出
                </Button>
                <Button :loading="loading" type="primary" @click="modalConfirm" v-if="viewType != 'see'">保存</Button>
            </template>
            <span v-else></span>
        </template>
    </BasicEditModalSlot>
    <!-- 变更记录明细 -->
    <ChangeDetail :currentId="detailValue?.id" :visible="showDetail" @cancel="detailClose" />
</template>

<script lang="ts">
import { defineComponent, h, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { archivesStatusList, ctTypeList } from '/@/utils/dictionaries'
import { uploadRecordFile } from '/@/utils/upload'
import ChangeDetail from '../changeRecord/DetailModal.vue'
import downFile, { downMultFile } from '/@/utils/downFile'
import { Upload } from 'ant-design-vue'
import { previewFile } from '/@/utils'

export default defineComponent({
    name: 'DetailModal',
    components: { ChangeDetail, Upload },
    props: {
        viewType: String,
        visible: Boolean,
        currentData: Object,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { currentData, visible } = toRefs(props)

        watch(visible, () => {
            visible.value && currentData.value?.id && getData()
        })

        const activeKey = ref('detail')
        const formData = ref<any>({})

        const getData = async () => {
            // const res = await request.get(`/api/hr-archives-manage/${currentId.value}`)
            formData.value = { ...currentData.value }
            formData.value.archivesStatus = currentData.value?.archivesStatus?.toString()
            formData.value.appendixList =
                currentData.value?.appendixList && currentData.value?.appendixList?.length ? currentData.value.appendixList : []
        }

        const detailValue = ref<Recordable>({})
        const showDetail = ref(false)
        const showRow = (record) => {
            detailValue.value = { ...record }
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
        }

        const loading = ref(false)
        const modalClose = () => {
            emit('cancel')
            resetData()
        }
        const modalConfirm = async (type = '保存') => {
            try {
                loading.value = true
                const form = { ...formData.value }
                form.appendixIds = formData.value.appendixList.map((i) => i.id).join(',')
                await request.put(`/api/hr-archives-manage`, form)
                emit('confirm', type)
                resetData()
            } finally {
                loading.value = false
            }
        }
        const resetData = () => {
            activeKey.value = 'detail'
            formData.value = {}
        }

        const download = (url, name) => {
            downFile('get', url, name, {})
        }

        const uploadIndex = ref(0)
        const uploadRow = async (file) => {
            const res = await uploadRecordFile(file)
            setTimeout(async () => {
                await request.post(`/api/hr-archives-manage/save-detail-appendix`, {
                    id: formData.value.hrArchivesDetailList[uploadIndex.value]?.id,
                    appendixList: [
                        {
                            id: res.id,
                        },
                    ],
                })
                formData.value.hrArchivesDetailList[uploadIndex.value]?.appendixList?.push(res)
            }, 100)
            return false
        }

        const downloadRow = (record) => {
            downMultFile(
                `${record.name}附件`,
                record.appendixList.map((i) => i.fileUrl),
                record.appendixList.map((i) => i.name),
            )
        }

        return {
            downloadRow,
            previewFile,
            uploadIndex,
            uploadRow,
            download,
            detailClose,
            detailValue,
            showDetail,
            loading,
            showRow,
            archivesStatusList,
            formData,
            activeKey,
            modalClose,
            modalConfirm,
            detailColumns: [
                {
                    title: '名称',
                    dataIndex: 'name',
                    width: 200,
                },
                {
                    title: '性质',
                    dataIndex: 'type',
                    width: 100,
                    customRender: ({ text }) => {
                        return text == 1 ? '实体件' : '电子件'
                    },
                },
                {
                    title: '状态',
                    dataIndex: 'stateStr',
                    width: 100,
                    customRender: ({ record, text }) => {
                        return record.type == 1 ? text : ''
                    },
                },
                {
                    title: '附件',
                    dataIndex: 'appendixList',
                    slots: { customRender: 'appendixList' },
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 120,
                    slots: { customRender: 'operation' },
                },
            ],
            columns: [
                {
                    title: '操作类型',
                    dataIndex: 'ctType',
                    align: 'center',
                    customRender: ({ text }) => {
                        return h('span', ctTypeList.find((i) => i.value === text)?.label || '-')
                    },
                },
                {
                    title: '操作事由',
                    dataIndex: 'ctProposesStr',
                    align: 'center',
                },
                {
                    title: '档案明细',
                    dataIndex: 'archivesDetailList',
                    align: 'center',
                    customRender: ({ text }) => {
                        return text?.map((i) => i.name + '、')
                    },
                },
                {
                    title: '操作时间',
                    dataIndex: 'createdDate',
                    align: 'center',
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 120,
                    slots: { customRender: 'operation' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.record {
    width: 100%;
    min-height: 500px;
}
.detail {
    width: 100%;
    min-height: 500px;
    .row {
        padding: 10px 0;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        .tit {
            width: 110px;
            &:after {
                content: '：';
            }
        }
        .cell {
            width: 25%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .tit {
                width: 40%;
                &:after {
                    content: '：';
                }
            }
            .val {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        .item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 32%;
            .label {
                width: 110px;
                &:after {
                    content: '：';
                }
            }
            .input {
                width: ~'calc(100% - 150px)';
            }
        }
    }
}
</style>
