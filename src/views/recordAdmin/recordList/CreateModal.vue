<template>
    <BasicEditModalSlot
        :title="currentStep == 2 ? `选择${formData.archivesType == '0' ? '员工' : '客户'}` : '新增档案'"
        :visible="visible"
        @cancel="modalClose"
        :width="currentStep == 1 ? '600px' : currentStep == 2 ? '800px' : currentStep == 3 ? '900px' : '800px'"
    >
        <template v-if="currentStep == 1">
            <!-- 第一步选择员工或客户 -->
            <div class="step1">
                <div class="per" @click="selType('0')">
                    <FolderFilled class="icon" />
                    员工档案
                </div>
                <div class="cus" @click="selType('1')">
                    <FileFilled class="icon" />
                    客户档案
                </div>
            </div>
        </template>
        <template v-if="currentStep == 2">
            <div class="step2">
                <InputSearch
                    v-model:value="filterName"
                    @search="onSearch"
                    :placeholder="formData.archivesType == '0' ? '员工姓名' : '客户名称'"
                    style="width: 200px; margin-bottom: 10px"
                />
                <div v-if="formData.archivesType == '0'">
                    <!-- 选择员工 -->
                    <BasicTable
                        ref="tableRef"
                        useIndex
                        api="/api/hr-archives-manage/emp"
                        :columns="staffColumns"
                        :params="{ name: filterName }"
                        :rowSelectionShow="false"
                        :pageSize="5"
                    >
                        <template #operation="{ record }">
                            <Button size="small" @click="selectStaff(record)">选择Ta</Button>
                        </template>
                    </BasicTable>
                </div>
                <div v-if="formData.archivesType == '1'">
                    <!-- 选择客户 -->
                    <BasicTable
                        ref="tableRef"
                        useIndex
                        api="/api/hr-archives-manage/client"
                        :columns="customerColumns"
                        :params="{ clientName: filterName }"
                        :rowSelectionShow="false"
                        :pageSize="5"
                    >
                        <template #operation="{ record }">
                            <Button size="small" @click="selectCustomer(record)">选择Ta</Button>
                        </template>
                    </BasicTable>
                </div>
            </div>
        </template>
        <template v-if="currentStep == 3">
            <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" :rules="rules">
                <Row>
                    <Col span="12">
                        <FormItem label="档案名称" name="archivesName">
                            <Input disabled v-model:value="formData.archivesName" placeholder="请输入档案名称" />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="档案类型" name="archivesType">
                            <Select
                                disabled
                                v-model:value="formData.archivesType"
                                :options="archivesTypeList"
                                placeholder="请选择档案类型"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem label="档案编号" name="archivesNum">
                            <Input v-model:value="formData.archivesNum" placeholder="请输入档案编号" />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="档案所处位置" name="archivesLocal">
                            <Input v-model:value="formData.archivesLocal" placeholder="请输入档案所处位置" />
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem label="入库时间" name="warehouseTime">
                            <DatePicker
                                v-model:value="formData.warehouseTime"
                                placeholder="请选择入库时间"
                                format="YYYY-MM-DD"
                                valueFormat="YYYY-MM-DD"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="档案状态" name="archivesStatus">
                            <Select
                                v-model:value="formData.archivesStatus"
                                :options="archivesStatusList"
                                placeholder="请选择档案状态"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <FormItem label="档案明细" name="hrArchivesDetailList" :label-col="{ span: 3 }" :wrapper-col="{ span: 19 }">
                    <Button @click="createDetail" size="small" type="primary">添加明细</Button>
                    <Card size="small" v-for="(i, idx) in formData.hrArchivesDetailList" :key="idx" style="margin-top: 10px">
                        <template #title>
                            <div class="cardTitle">
                                <div>
                                    <Tag :color="i.type == 1 ? 'green' : 'blue'"> {{ i.type == 1 ? '实体件' : '电子件' }} </Tag>
                                    {{ i.name }}
                                </div>
                                <div>
                                    <FormOutlined @click="editOne(i, idx)" />
                                    <CloseSquareOutlined @click="removeOne(idx)" style="margin-left: 10px" />
                                </div>
                            </div>
                        </template>
                        <div class="flexStart">
                            <a v-for="f in i.appendixList" :key="f.id" href="javascript: void(0)" @click="previewFile(f.fileUrl)">
                                {{ f.originName }}
                            </a>
                            <span v-if="!i.appendixList || !i.appendixList.length">无附件</span>
                        </div>
                    </Card>
                </FormItem>
            </Form>
        </template>
        <template #footer>
            <template v-if="currentStep == 1">
                <Button @click="modalClose">取消</Button>
            </template>
            <template v-else-if="currentStep == 2">
                <Button type="primary" @click="prevStep">上一步</Button>
            </template>
            <template v-else-if="currentStep == 3">
                <Button type="primary" :loading="loading" @click="navToStep2">上一步</Button>
                <Button type="primary" :loading="loading" @click="modalConfirm">保存</Button>
            </template>
        </template>
    </BasicEditModalSlot>
    <!-- 编辑明细 -->
    <EditDetailName
        :visible="showDetail"
        :currentValue="detailForm"
        :detailTitle="detailTitle"
        @confirm="detailConfirm"
        @cancel="detailClose"
    />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { FolderFilled, FileFilled, FormOutlined, CloseSquareOutlined } from '@ant-design/icons-vue'
import { InputSearch, Card, Tag } from 'ant-design-vue'
import { archivesStatusList, archivesTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import downFile from '/@/utils/downFile'
import { uploadRecordFile } from '/@/utils/upload'
import EditDetailName from './EditDetailName.vue'
import { previewFile } from '/@/utils/index'

export default defineComponent({
    name: 'CreateModal',
    components: {
        FolderFilled,
        FileFilled,
        InputSearch,
        Card,
        Tag,
        FormOutlined,
        CloseSquareOutlined,
        EditDetailName,
    },
    props: {
        visible: Boolean,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const currentStep = ref(1)
        const formData = ref<Recordable>({
            archivesType: '',
            archivesName: undefined,
            archivesNum: undefined,
            empUnit: undefined,
            archivesStatus: undefined,
            archivesLocal: undefined,
            staffId: undefined,
            clientId: undefined,
            warehouseTime: undefined,
            hrArchivesDetailList: [],
        })

        const rules = {
            archivesName: { required: true, message: '请输入档案名称', trigger: 'blur' },
            archivesType: { required: true, message: '请选择档案类型', trigger: 'blur' },
            archivesNum: { required: true, message: '请输入档案编号', trigger: 'blur' },
            hrArchivesDetailList: { required: true, type: 'array', message: '至少输入一条档案明细', trigger: 'change' },
            archivesStatus: { required: true, message: '请选择档案状态', trigger: 'blur' },
            warehouseTime: { required: true, message: '请选择入库时间', trigger: 'blur' },
        }

        const selType = (type) => {
            formData.value.archivesType = type
            currentStep.value = 2
        }

        const filterName = ref('')
        const tableRef = ref()
        const onSearch = () => {
            tableRef.value.refresh(1)
        }
        const selectStaff = (item) => {
            console.log('选择的员工', item)
            formData.value.archivesName = item.name
            formData.value.staffId = item.id
            formData.value.empUnit = item.clientName
            formData.value.clientId = item.clientId
            nextStep()
        }
        const selectCustomer = (item) => {
            console.log('选择的客户', item)
            formData.value.archivesName = item.clientName
            formData.value.clientId = item.id
            formData.value.empUnit = item.clientName
            formData.value.staffId = undefined
            nextStep()
        }

        const prevStep = () => {
            currentStep.value = 1
            filterName.value = ''
            formData.value = {
                archivesType: '',
                archivesName: undefined,
                archivesNum: undefined,
                empUnit: undefined,
                archivesStatus: undefined,
                archivesLocal: undefined,
                staffId: undefined,
                clientId: undefined,
                warehouseTime: undefined,
                hrArchivesDetailList: [],
            }
        }
        const nextStep = () => {
            currentStep.value = 3
            filterName.value = ''
        }

        const navToStep2 = () => {
            currentStep.value = 2
            formData.value = {
                archivesType: formData.value.archivesType,
                archivesName: undefined,
                archivesNum: undefined,
                empUnit: undefined,
                archivesStatus: undefined,
                archivesLocal: undefined,
                staffId: undefined,
                clientId: undefined,
                warehouseTime: undefined,
                hrArchivesDetailList: [],
            }
        }

        const modalClose = () => {
            emit('cancel')
            resetData()
        }
        const formInline = ref()
        const loading = ref(false)
        const modalConfirm = async () => {
            try {
                await formInline.value.validate()
                loading.value = true

                const form = { ...formData.value }
                await request.post(`/api/hr-archives-manage`, form)
                resetData()
                emit('confirm')
            } catch (error) {
                console.log('confirm', error)
            } finally {
                loading.value = false
            }
        }
        const resetData = () => {
            currentStep.value = 1
            formData.value = {
                archivesType: '',
                archivesName: undefined,
                archivesNum: undefined,
                empUnit: undefined,
                archivesStatus: undefined,
                archivesLocal: undefined,
                staffId: undefined,
                clientId: undefined,
                warehouseTime: undefined,
                hrArchivesDetailList: [],
            }
        }

        const download = (url) => {
            downFile('get', url, url, {})
        }

        const detailForm = ref<inObject>({
            name: undefined,
            type: undefined,
            appendixList: [],
        })
        const beforeUpload = async (file) => {
            const res = await uploadRecordFile(file)
            detailForm.value.appendixList.push(res)
            return false
        }
        const detailTitle = ref('档案明细')
        const detailIndex = ref(0)
        const showDetail = ref(false)
        const createDetail = () => {
            detailTitle.value = '添加档案明细'
            detailForm.value = {
                name: undefined,
                type: undefined,
                appendixList: [],
            }
            showDetail.value = true
        }
        const editOne = (item, idx) => {
            detailTitle.value = '编辑档案明细'
            detailForm.value = { ...item }
            detailIndex.value = idx
            showDetail.value = true
        }
        const removeOne = (idx) => {
            formData.value.hrArchivesDetailList.splice(idx, 1)
        }
        const detailClose = () => {
            showDetail.value = false
        }
        const detailConfirm = async () => {
            if (detailTitle.value.includes('添加')) {
                formData.value.hrArchivesDetailList.push(detailForm.value)
            } else {
                formData.value.hrArchivesDetailList[detailIndex.value] = detailForm.value
            }
            showDetail.value = false
        }

        return {
            previewFile,
            removeOne,
            editOne,
            detailTitle,
            createDetail,
            detailForm,
            showDetail,
            detailClose,
            detailConfirm,
            download,
            formInline,
            loading,
            beforeUpload,
            rules,
            navToStep2,
            selectStaff,
            selectCustomer,
            prevStep,
            nextStep,
            onSearch,
            tableRef,
            filterName,
            formData,
            archivesTypeList,
            selType,
            currentStep,
            modalClose,
            archivesStatusList,
            modalConfirm,
            staffColumns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'center',
                    width: 200,
                    ellipsis: true,
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    align: 'center',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    slots: { customRender: 'operation' },
                    width: 100,
                },
            ],
            customerColumns: [
                {
                    title: '客户编号',
                    dataIndex: 'unitNumber',
                    align: 'center',
                },
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'center',
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    slots: { customRender: 'operation' },
                    width: 100,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.cardTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.tag {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
}
.tags {
    width: 100%;
    margin-top: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}
.step1 {
    display: flex;
    justify-content: space-around;
    align-content: center;
    margin: 30px 0;
    div {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 30px;
        flex-direction: column;
        color: white;
        cursor: pointer;
        &:hover {
            opacity: 0.8;
        }
    }
    .icon {
        color: white;
        font-size: 80px;
        margin-bottom: 10px;
    }
    .per {
        background: #597ef7;
        border-radius: @border-radius-base;
    }
    .cus {
        background: #36cfc9;
        border-radius: @border-radius-base;
    }
}
</style>
