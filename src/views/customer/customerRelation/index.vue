<template>
    <div style="height: 100%">
        <div class="search">
            <input-search v-model:value="search.searchValue" style="width: 200px" placeholder="客户名称" />
            <Select
                v-model:value="search.clientLevel"
                showSearch
                :options="hierarchicalFilteringList"
                optionFilterProp="label"
                allowClear
                placeholder="客户层级"
                style="width: 150px; margin: 0 12px"
            />
            <Input v-model:value="clientNum" disabled style="width: 150px" />
        </div>
        <div class="treeBox">
            <tree
                :expandedKeys="expandedKeys"
                :tree-data="gData"
                @expand="onExpand"
                :replaceFields="{ children: 'children', title: 'clientName', key: 'id' }"
                :autoExpandParent="autoExpandParent"
            >
                <template #switcherIcon>
                    <down-outlined style="font-size: 16px; vertical-align: middle" />
                </template>
                <template #title="{ title, id }">
                    <p style="width: calc(100% - 3px); display: inline-block" @click="editRow(id)">
                        <InsertRowRightOutlined />
                        <span v-if="title.indexOf(search.searchValue) > -1">
                            {{ title.substr(0, title.indexOf(search.searchValue)) }}
                            <span style="color: #f50">{{ search.searchValue }}</span>
                            {{ title.substr(title.indexOf(search.searchValue) + search.searchValue.length) }}
                        </span>
                        <span v-else>{{ title }}</span>
                    </p>
                </template>
            </tree>
            <Empty class="myEmpty" v-show="!gData.length" />
        </div>
        <EditModal
            :visible="showEdit"
            viewType="see"
            :title="modalTitle"
            :item="currentValue"
            @cancel="modalCancel"
            @confirm="modalConfirm"
        />
        <ProtocolList
            ref="refProtocolList"
            :title="otherModalTitle"
            :visible="protocolListVisible"
            :item="currentValue"
            @cancel="otherModalCancel"
        />
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch, onMounted, computed } from 'vue'
import { TreeDataItem } from 'ant-design-vue/es/tree/Tree'
import request from '/@/utils/request'
import { ArrToTree, SectionToChinese } from '/@/utils/index'
import { DownOutlined, InsertRowRightOutlined } from '@ant-design/icons-vue'
import EditModal from '../customerInfo/editModal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import customerUserStore from '/@/store/modules/customer'
import ProtocolList from '../customerInfo/component/protocolList.vue'
import { Empty } from 'ant-design-vue'
const getParentKey = (key: string, tree: TreeDataItem[]): string | number | undefined => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i]

        if (node.children) {
            console.log(node) //海三
            if (node.children.some((item) => item.id === key)) {
                parentKey = node.id
            } else if (getParentKey(key, node.children)) {
                parentKey = getParentKey(key, node.children)
            }
        }
    }
    return parentKey
}
export default defineComponent({
    name: 'CustomerRelationIndex',
    components: {
        DownOutlined,
        InsertRowRightOutlined,
        EditModal,
        ProtocolList,
        Empty,
    },
    setup() {
        const expandedKeys = ref<string[]>([])
        const search = ref({
            searchValue: '',
            clientLevel: undefined,
        })
        const clientNum = ref(0)
        const autoExpandParent = ref<boolean>(true)
        const rawData = ref([])
        const filterData = ref([])
        const originalData = ref<TreeDataItem[]>([])
        const gData = ref<TreeDataItem[]>([])
        const viewType = computed(() => {
            return customerUserStore().getViewType
        })
        const protocolListVisible = ref(false)
        const otherModalTitle = ref<string>('')
        const dataList: TreeDataItem[] = []
        const generateList = (data: TreeDataItem[]) => {
            for (let i = 0; i < data.length; i++) {
                const node = data[i]
                const key = node.id
                const title = node.clientName
                dataList.push({ key, title: title as string })
                if (node.children) {
                    generateList(node.children)
                }
            }
        }
        const hierarchicalFilteringList = computed(() => {
            let newHierarchicalFilteringList: LabelValueOptions = []
            for (let i = 1; i <= dictionaryDataStore().dictionaryTreeDataMaxlevel; i++) {
                newHierarchicalFilteringList.push({ label: SectionToChinese(i) + '级', value: i })
            }
            return newHierarchicalFilteringList
        })

        const formatTitle = (arr) => {
            function loop(data) {
                data.forEach((item) => {
                    if ('children' in item) {
                        if (item.children.length > 0) {
                            item.title = `${item.title}（${item.children.length}）`
                            loop(item.children)
                        }
                    }
                })
            }
            loop(arr)
        }

        onMounted(() => {
            dictionaryDataStore()
                .setSelectclients()
                .then((res: inObject[]) => {
                    filterData.value = dictionaryDataStore().dictionaryData['clients']
                    rawData.value = JSON.parse(JSON.stringify(filterData.value))
                    originalData.value = JSON.parse(JSON.stringify(dictionaryDataStore().dictionaryTreeData))
                    formatTitle(originalData.value)
                    gData.value = originalData.value
                    clientNum.value = filterData.value.length
                })
        })

        const onExpand = (keys: string[]) => {
            expandedKeys.value = keys
            autoExpandParent.value = false
        }
        const getParentKeys = (value: string, treeObg: TreeDataItem, parentKeys): any => {
            if ((treeObg.title as string).indexOf(value) > -1) {
                parentKeys.push(treeObg.parentId)
            }
            if (treeObg.children?.length) {
                treeObg.children.forEach((item) => {
                    getParentKeys(value, item, parentKeys)
                })
            }
            return parentKeys
        }
        let time1: any = null
        let oldValue = ''
        watch(
            () => search.value,
            (newV) => {
                /* if (!newV.clientLevel && !newV.searchValue) {
                    expandedKeys.value = []
                    gData.value = JSON.parse(JSON.stringify(originalData.value))
                    autoExpandParent.value = false
                    return
                }
                //优化连续输入开销增大
                if (time1) {
                    clearTimeout(time1)
                } else {
                    oldValue = newV
                    funSearch()
                }
                time1 = setTimeout(() => {
                    time1 = null
                    if (oldValue == newV) {
                        oldValue = ''
                        return
                    }
                    oldValue = ''
                    funSearch()
                }, 500) */
                funSearch()
            },
            { deep: true },
        )
        const funSearch = () => {
            let filterOdds
            /* let expanded = []
            let newGData: any = []
            for (var i = 0; i < originalData.value.length; i++) {
                let item = originalData.value[i]
                let isShow = []
                isShow = getParentKeys(search.value.searchValue, item, [])
                if (isShow?.length) {
                    newGData.push(item)
                }
                expanded.push(...isShow)
            }
            expandedKeys.value = expanded as string[]
            gData.value = newGData
            autoExpandParent.value = true */
            if (!search.value.searchValue && !search.value.clientLevel) {
                filterData.value = JSON.parse(JSON.stringify(rawData.value))
                clientNum.value = filterData.value.length
                expandedKeys.value = []
                gData.value = JSON.parse(JSON.stringify(originalData.value))
                autoExpandParent.value = false
                return
            } else if (search.value.searchValue && !search.value.clientLevel) {
                filterOdds = (el) => {
                    return el.title.includes(search.value.searchValue)
                }
            } else if (!search.value.searchValue && search.value.clientLevel) {
                filterOdds = (el) => {
                    return el.level == search.value.clientLevel
                }
            } else {
                filterOdds = (el) => {
                    return el.level == search.value.clientLevel && el.title.includes(search.value.searchValue)
                }
            }
            filterData.value = rawData.value.filter((el) => {
                return filterOdds(el)
            })
            generateTreeData()
        }

        watch(viewType, (val) => {
            if (val) {
                protocolListVisible.value = true
                if (val == 'look') otherModalTitle.value = '查看历史协议'
            } else {
                protocolListVisible.value = false
            }
        })

        const generateTreeData = () => {
            gData.value = ArrToTree(filterData.value, { id: 'id', pid: 'parentId' })
            formatTitle(gData.value)
            clientNum.value = filterData.value.length
            let expanded = []
            for (let i = 0; i < gData.value.length; i++) {
                let item = gData.value[i]
                let isShow = []
                isShow = getParentKeys(search.value.searchValue, item, [])
                expanded.push(...isShow)
            }
            expandedKeys.value = expanded as string[]
            autoExpandParent.value = true
        }

        const showEdit = ref(false) //编辑
        const modalTitle = ref('查看客户') //弹窗名称
        // 当前编辑的数据
        const currentValue = ref<any>(null) //编辑的数据
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '查看客户'
            currentValue.value = { id: record }
            customerUserStore().fetchCustomerProtocol(currentValue.value, true)
            // console.log({ ...record })
        }
        const otherModalCancel = () => {
            customerUserStore().setViewType('')
            otherModalTitle.value = ''
            protocolListVisible.value = false
        }
        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '查看客户'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                // tableRef.value.refresh(1)
            } else {
                // tableRef.value.refresh()
            }
        }
        return {
            //数据
            search,
            hierarchicalFilteringList,
            clientNum,
            expandedKeys,
            autoExpandParent,
            gData,
            showEdit,
            currentValue,
            modalTitle,
            //函数
            onExpand,

            editRow,
            modalCancel,
            modalConfirm,
            otherModalTitle,
            protocolListVisible,
            otherModalCancel,
        }
    },
})
</script>
<style scoped lang="less">
.treeBox {
    background: #fff;
    height: calc(100% - 40px);
    padding-right: 24px;
    overflow: auto;
    position: relative;
    .myEmpty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
:deep(.ant-tree-node-content-wrapper) {
    display: inline-block;
    width: calc(100% - 30px);
}
:deep(.ant-tree li span.ant-tree-switcher) {
    top: -2px;
}
.search {
    margin-bottom: 8px;
}
</style>
