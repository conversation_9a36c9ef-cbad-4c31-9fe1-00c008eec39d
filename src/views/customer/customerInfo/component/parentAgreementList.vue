<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" title="历史协议" width="1200px">
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRefProtocol"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="tableColumns"
            :data-source="tableData"
            :row-key="(record) => record.id"
            :pagination="false"
            :loading="loading"
            :row-selection="selectionRowConfig"
        />
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted, h } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { stateOptions } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'ParentAgreementList',
    props: {
        currentId: {
            type: String,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        options: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { visible, currentId, options } = toRefs<any>(props)
        //筛选
        const params = ref<inObject>({})
        //  Data
        const tableRefProtocol = ref()
        let businessTypeOptions = ref<LabelValueOptions>([])
        //表格数据
        const tableColumns = ref<inObject>([
            {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                customRender: (record) => {
                    return h('span', record.index + 1)
                },
                width: 80,
            },
            {
                title: '所属客户',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '协议编码',
                dataIndex: 'agreementNumber',
                align: 'center',
            },
            {
                title: '协议标题',
                dataIndex: 'agreementTitle',
                align: 'center',
            },
            {
                title: '协议类型',
                dataIndex: 'agreementType',
                align: 'center',
                customRender: ({ record }) => {
                    return businessTypeOptions.value.find((el) => {
                        return el.value == record.agreementType
                    })?.label
                },
            },
            {
                title: '结算方式',
                dataIndex: 'settlementMethod',
                align: 'center',
                customRender: ({ record }) => {
                    return record.settlementMethodkey
                },
            },
            {
                title: '协议开始日期',
                dataIndex: 'agreementStartDate',
                align: 'center',
                width: 110,
            },
            {
                title: '协议结束日期',
                dataIndex: 'agreementEndDate',
                align: 'center',
                width: 110,
            },
            {
                title: '协议状态',
                dataIndex: 'states',
                align: 'center',
                width: 110,
                customRender: ({ record, text }) => {
                    let myText = ''
                    if (text == 1 || text == 5) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    } else if (text == 2 || text == 6) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    }
                    const str = stateOptions.find((item) => {
                        return text == item.value
                    })?.label as string
                    return str.includes('(') ? str.split('(')[0] + myText : str
                },
            },
        ])
        onMounted(() => {
            // 协议类型
            dictionaryDataStore()
                .setDictionaryData('businessType', '', 'get', true)
                .then((res: LabelValueOptions) => {
                    businessTypeOptions.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const tableData = ref([])

        watch(
            visible,
            (val) => {
                if (val) {
                    tableColumns.value = [
                        ...tableColumns.value.map((i) => ({
                            ...i,
                            sorter: false,
                            align: i.align || 'center',
                        })),
                    ]
                    tableData.value = options.value
                }
            },
            {
                immediate: true,
            },
        )

        const loading = ref(false)

        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            type: 'radio',
            getCheckboxProps: (record: inObject) => {
                return {
                    defaultChecked: currentId.value ? currentId.value == record.id : false,
                    id: record.id + '',
                }
            },
        }
        // 复选框置空
        const checkboxReset = () => {
            selectedRowsArr.value = []
            selectedRowKeysArr.value = []
        }

        const onCancel = () => {
            emit('cancel')
            checkboxReset()
        }
        const onConfirm = () => {
            if (selectedRowsArr.value.length) {
                emit('confirm', selectedRowsArr.value[0])
                checkboxReset()
            } else message.warning('请选择要使用的协议')
        }

        return {
            loading,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            selectionRowConfig,

            tableRefProtocol,
            tableColumns,
            params,
            onCancel,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
