<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="1200px">
        <SearchBar v-model="params" v-if="viewType == 'edit'" :options="options" @change="searchData" />
        <div class="btns">
            <Button type="primary" v-if="viewType == 'edit'" @click="createProtocol">新增协议</Button>
        </div>
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRefProtocol"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="tableColumns"
            :data-source="tableData"
            :row-key="(record) => record.agreementNumber"
            :pagination="viewType == 'edit' ? false : pagination"
            :loading="loading"
            :row-selection="viewType == 'edit' ? selectionRowConfig : null"
            @change="tableChange"
        />
        <template #footer>
            <div v-if="viewType == 'edit'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
// import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted, computed, h } from 'vue'
import { SearchBarOption } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { stateOptions } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import customerUserStore from '/@/store/modules/customer'
import { deepClone, toUnderline } from '/@/utils'
import { message } from 'ant-design-vue'
// import request from '/@/utils/request'
export default defineComponent({
    name: 'ProtocolList',
    props: {
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { visible, item } = toRefs<any>(props)
        //筛选
        const params = ref<inObject>({})
        const viewType = computed(() => {
            return customerUserStore().getViewType
        })
        const protocolInfo = computed(() => {
            return customerUserStore().getCustomerProtocol
        })
        //  Data
        const tableDataList = ref([])
        const tableRefProtocol = ref()
        let businessTypeOptions = ref<LabelValueOptions>([])
        // 搜索
        const searchData = async () => {
            refresh(1)
        }
        const refresh = (page = pagination.value.current) => {
            pagination.value.current = page
            getTableData()
        }
        //表格数据
        const tableColumns = ref<inObject[]>([])
        const columns = [
            {
                title: '所属客户',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '协议编码',
                dataIndex: 'agreementNumber',
                align: 'center',
            },
            {
                title: '协议标题',
                dataIndex: 'agreementTitle',
                align: 'center',
            },
            {
                title: '协议类型',
                dataIndex: 'agreementType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.agreementTypekey
                },
            },
            {
                title: '结算方式',
                dataIndex: 'settlementMethod',
                align: 'center',
                customRender: ({ record }) => {
                    return record.settlementMethodkey
                },
            },
            {
                title: '协议开始日期',
                dataIndex: 'agreementStartDate',
                align: 'center',
            },
            {
                title: '协议结束日期',
                dataIndex: 'agreementEndDate',
                align: 'center',
            },
            {
                title: '协议状态',
                dataIndex: 'states',
                align: 'center',
                customRender: ({ record, text }) => {
                    let myText = ''
                    if (text == 1 || text == 5) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    } else if (text == 2 || text == 6) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    }
                    const str = stateOptions.find((item) => {
                        return text == item.value
                    })?.label as string
                    return str.includes('(') ? str.split('(')[0] + myText : str
                },
            },
            {
                title: '使用数量',
                dataIndex: 'clientIdSum',
                align: 'center',
            },
        ]
        watch(
            viewType,
            (val, old) => {
                tableColumns.value = deepClone(columns)
                if (val == 'look') {
                    let clientIndex = tableColumns.value.findIndex((el) => {
                        return el.dataIndex == 'clientName'
                    })
                    tableColumns.value.splice(clientIndex, 1)
                    let numIndex = tableColumns.value.findIndex((el) => {
                        return el.dataIndex == 'usageCount'
                    })
                    tableColumns.value.splice(numIndex, 1)
                }
                // 自定义列展示
                tableColumns.value =
                    viewType.value == 'look'
                        ? [
                              {
                                  title: '序号',
                                  dataIndex: 'index',
                                  align: 'center',
                                  customRender: (record) => {
                                      return h(
                                          'span',
                                          (pagination.value.current - 1) * pagination.value.pageSize + record.index + 1,
                                      )
                                  },
                                  width: 80,
                              },
                              ...tableColumns.value.map((i) => ({
                                  ...i,
                                  sorter: i.sorter === false ? false : true,
                                  align: i.align || 'center',
                              })),
                          ]
                        : tableColumns.value.map((i) => ({
                              ...i,
                              sorter: false,
                              align: i.align || 'center',
                          }))
                if (val == 'edit' && old == 'add') {
                    refresh(1)
                }
            },
            { immediate: true },
        )
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '协议类型',
                key: 'agreementTypeList',
                options: businessTypeOptions,
                multiple: true,
            },
        ]
        onMounted(() => {
            // 协议类型
            dictionaryDataStore()
                .setDictionaryData('businessType', '', 'get', true)
                .then((res: LabelValueOptions) => {
                    businessTypeOptions.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        const sortParams = ref({
            field: undefined,
            order: undefined,
        })
        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const tableRef = ref()
        const tableData = ref([])

        watch(visible, (val) => {
            if (val) {
                getTableData()
            }
        })
        watch(protocolInfo, (val) => {
            if (val && viewType.value == 'edit') {
                selectedRowKeysArr.value = [val.agreementNumber]
                selectedRowsArr.value = [val]
            }
        })

        const loading = ref(false)
        const pagination = ref<any>({
            current: 1,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['5', '10', '20', '30', '50'],
            total: 0,
        })
        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field: sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }

            getTableData()
        }

        const getTableData = async () => {
            loading.value = true
            try {
                let fn
                if (viewType.value == 'edit') {
                    fn = () =>
                        request.post('/api/hr-protocols/selectProtocols', {
                            ...params.value,
                            clientId: item.value.id,
                        })
                } else if (viewType.value == 'look') {
                    fn = () =>
                        request.post(
                            `/api/hr-protocols/historypage?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`,
                            {
                                ...params.value,
                                clientId: item.value.id,
                                ...{
                                    field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                    order:
                                        sortParams.value.order === 'ascend'
                                            ? 'DESC'
                                            : sortParams.value.order === 'descend'
                                            ? 'ASC'
                                            : undefined,
                                },
                            },
                        )
                }
                const data = await fn()
                tableData.value = data.records || data || []

                pagination.value.total = data.total || 0
                // 复选框置空
                selectedRowsArr.value = []
                selectedRowKeysArr.value = []
            } finally {
                loading.value = false
            }
        }
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            type: 'radio',
            getCheckboxProps: (record: inObject) => {
                let defaultChecked = false
                if (protocolInfo.value?.agreementNumber == record.agreementNumber) {
                    defaultChecked = true
                    selectedRowKeysArr.value = [protocolInfo.value?.agreementNumber || '']
                    selectedRowsArr.value = [protocolInfo.value]
                }
                return {
                    defaultChecked: defaultChecked,
                    id: record.agreementNumber + '',
                }
            },
        }

        const createProtocol = () => {
            customerUserStore().setViewType('add')
        }
        const onCancel = () => {
            params.value = {}
            emit('cancel', true)
        }
        const onConfirm = () => {
            if (!selectedRowsArr.value.length) {
                message.error('请选择要使用的协议！')
                return
            } else {
                request
                    .post('/api/hr-protocols/updateprotocols', {
                        clientId: item.value.id,
                        protocolId: protocolInfo.value.id,
                        newProtocolId: selectedRowsArr.value?.[0]?.id,
                    })
                    .then((res) => {
                        console.log(res)
                        customerUserStore().fetchCustomerProtocol({ id: item.value.id }, true)
                        emit('confirm', true)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        return {
            loading,
            columns,
            tableRef,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            pagination,
            selectionRowConfig,
            tableChange,

            viewType,
            tableRefProtocol,
            tableColumns,
            options,
            params,
            tableDataList,
            searchData,
            createProtocol,
            onCancel,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
