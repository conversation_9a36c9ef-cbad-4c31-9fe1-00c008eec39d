<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span class="title">当前使用中的协议</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">协议编号：</span>
                <span>{{ protocolInfo?.agreementNumber }}</span>
            </div>
            <div class="item-flex">
                <span class="label">所属客户：</span>
                <span class="value" style="width: 170px" :title="protocolInfo?.clientName">{{ protocolInfo?.clientName }}</span>
            </div>
            <div class="item-flex" style="width: 30%">
                <span class="label">协议标题：</span>
                <span class="value" style="width: 200px" :title="protocolInfo?.agreementTitle">
                    {{ protocolInfo?.agreementTitle }}
                </span>
            </div>
            <div class="item-other">
                <span class="label">协议类型：</span>
                <span>{{ getAgreementType(protocolInfo?.agreementType) }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">结算方式：</span>
                <span>{{ getSettlementMethod(protocolInfo?.settlementMethod) }}</span>
            </div>
            <div class="item-flex">
                <span class="label">服务费类型：</span>
                <span>{{ getServiceChargeType(protocolInfo?.serviceChargeType) }}</span>
            </div>
            <div class="item-flex" style="width: 50%">
                <span class="label">{{ `${protocolInfo?.serviceFeeType == 1 ? '浮动' : '固定'}服务费：` }}</span>
                <span v-if="protocolInfo?.serviceFeeType == 0">{{ protocolInfo?.serviceCharge }}</span>
                <span
                    v-if="protocolInfo?.serviceFeeType == 1"
                    style="display: flex; flex-wrap: wrap"
                    :title="serviceFeeTitle"
                    class="value"
                >
                    {{ protocolInfo?.serviceCharge }}
                    <span class="symbol">
                        <PlusOutlined />
                    </span>
                    {{ calculationOptions.find((el) => el.value == protocolInfo?.serviceFeeData)?.label }}
                    <span class="symbol">
                        <CloseOutlined />
                    </span>
                    {{ protocolInfo?.calculationFormula }}
                </span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">协议开始日期：</span>
                <span>{{ protocolInfo?.agreementStartDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">协议结束日期：</span>
                <span>{{ protocolInfo?.agreementEndDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">协议状态：</span>
                <span>{{ getStateName(protocolInfo, protocolInfo?.states) }}</span>
            </div>
            <p class="linefeed"></p>
            <template v-if="protocolInfo?.hrDocking && protocolInfo?.hrDocking?.length">
                <template v-for="(item, index) in protocolInfo?.hrDocking" :key="item">
                    <div class="item-flex">
                        <span class="label" :class="{ hide: index !== 0 }">对接部门：</span>
                        <span>{{ item?.department }}</span>
                    </div>
                    <div class="item-flex">
                        <span class="label" :class="{ hide: index !== 0 }">对接人：</span>
                        <span>{{ item?.docking }}</span>
                    </div>
                    <div class="item-flex">
                        <span class="label" :class="{ hide: index !== 0 }">对接人联系电话：</span>
                        <span>{{ item?.dockingPhone }}</span>
                    </div>
                    <p class="linefeed"></p>
                </template>
            </template>
            <div class="item-flex">
                <span class="label">备注：</span>
                <span>{{ protocolInfo?.remark }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex-detail">
                <span class="label">附件：</span>
                <div class="details" v-if="protocolInfo?.appendixList && protocolInfo?.appendixList.length">
                    <span v-for="ele in protocolInfo.appendixList" :key="ele.id">
                        <a href="javascript: void(0)" @click="previewFile(ele.fileUrl)" style="margin-right: 10px">
                            {{ `${ele.originName};` }}
                        </a>
                    </span>
                </div>
            </div>
            <p class="linefeed"></p>
            <div class="item-opt-btns">
                <Button type="primary" @click="showHistory">查看历史协议</Button>
                <Button type="primary" @click="changeCurrent" :disabled="viewType == 'see'">修改当前协议</Button>
            </div>
        </div>
        <slot :protocolInfo="protocolInfo"></slot>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { stateOptions, calculationOptions } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils'
import customerUserStore from '/@/store/modules/customer'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'ProtocolInfo',
    components: {
        PlusOutlined,
        CloseOutlined,
    },
    props: {
        viewType: String,
    },
    setup(props) {
        const serviceFeeType = ref<inObject[]>([])
        const settlementMethodOptions = ref<inObject[]>([])
        const businessTypeOptions = ref<inObject[]>([])
        onMounted(() => {
            //服务费类型
            dictionaryDataStore()
                .setDictionaryData('agreementChargeType', '')
                .then((res: LabelValueOptions) => {
                    serviceFeeType.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('settlementMethod', '')
                .then((res: LabelValueOptions) => {
                    settlementMethodOptions.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            dictionaryDataStore()
                .setDictionaryData('businessType', '')
                .then((res: LabelValueOptions) => {
                    businessTypeOptions.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const protocolInfo = computed(() => {
            return customerUserStore().getCustomerProtocol
        })
        const serviceFeeTitle = computed(() => {
            return `${protocolInfo.value.serviceCharge} + ${
                calculationOptions.find((el) => el.value == protocolInfo.value.serviceFeeData)?.label
            } x ${protocolInfo.value.calculationFormula}`
        })
        const getAgreementType = (type) => {
            return businessTypeOptions.value.find((el) => {
                return el.value == type
            })?.label
        }
        const getSettlementMethod = (type) => {
            return settlementMethodOptions.value.find((el) => {
                return el.value == type
            })?.label
        }
        const getServiceChargeType = (type) => {
            return serviceFeeType.value.find((el) => {
                return el.value == type
            })?.label
        }
        const getStateName = (record, state) => {
            console.log(record)
            let myText = ''
            if (state == 1 || state == 5) {
                if (record.types != 1) {
                    myText = '(未续签)'
                } else {
                    myText = '(已续签)'
                }
            } else if (state == 2 || state == 6) {
                if (record.types != 1) {
                    myText = '(未续签)'
                } else {
                    myText = '(已续签)'
                }
            }
            const str = stateOptions.find((item) => {
                return state == item.value
            })?.label as string
            return str?.includes('(') ? str.split('(')[0] + myText : str
        }
        const showHistory = () => {
            customerUserStore().setViewType('look')
        }
        const changeCurrent = () => {
            customerUserStore().setViewType('edit')
        }
        return {
            calculationOptions,
            protocolInfo,
            getAgreementType,
            getSettlementMethod,
            getServiceChargeType,
            previewFile,
            showHistory,
            changeCurrent,
            getStateName,
            serviceFeeTitle,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    padding: 25px 25px 100px;
    box-sizing: border-box;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .title {
        font-weight: bold;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        // padding-left: 15px;
        .item-flex-detail {
            width: 100%;
            margin: 5px 0px;
            display: flex;
            .details {
                display: flex;
                flex-direction: column;
                & span > span {
                    margin-right: 25px;
                }
            }
        }
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            display: flex;
        }
        .value {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .item-other {
            width: 20%;
            display: flex;
            align-items: center;
        }
        .hide {
            visibility: hidden;
        }
        .label {
            text-align: right;
            width: 115px;
            color: rgba(153, 153, 153, 1);
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .item-opt-btns {
            display: flex;
            margin: 30px 0 0 75px;
            .ant-btn {
                margin-right: 15px;
            }
        }
    }
}
.symbol {
    font-size: 16px;
    display: flex;
    align-items: center;
    margin: 0 10px;
}
</style>
