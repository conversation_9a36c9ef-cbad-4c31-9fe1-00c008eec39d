<template>
    <div>
        <Form ref="formSalaryParam" :model="formData" :label-col="{ style: { width: '150px' } }">
            <div class="param-box">
                <div class="title">
                    <span>社保</span>
                    <span class="btns" v-if="customerInfo_edit_pay_edit">
                        <Button type="link" size="small" @click="showAccountModal(3, 'socialInsurance')">修改缴纳账户</Button>
                        <Button type="link" size="small" @click="showAccountModal(2)">修改社保类型</Button>
                    </span>
                </div>
                <div class="box-body">
                    <div class="form-flex">
                        <FormItem label="社保缴纳账户">
                            <Select
                                v-model:value="socialSecurityAccountId"
                                showSearch
                                :showArrow="false"
                                :options="socialSecurityAccountOptions"
                                :placeholder="`社保缴纳账户`"
                                disabled
                            />
                        </FormItem>
                        <FormItem label="密码">
                            <InputPassword :value="socialSecurityAccountIdPassword" placeholder="密码" readonly />
                        </FormItem>
                    </div>
                    <Table
                        :columns="socialSecurityColumns"
                        :data-source="socialSecurityData"
                        :pagination="false"
                        :row-key="(record) => record.id + 'socialSecurityColumns'"
                        class="smallTable"
                        bordered
                    />
                </div>
            </div>
            <div class="param-box">
                <div class="title">
                    <span> 医保 </span>
                    <span class="btns" v-if="customerInfo_edit_pay_edit">
                        <Button type="link" size="small" @click="showAccountModal(3, 'medicalInsurance')">修改缴纳账户</Button>
                    </span>
                </div>
                <div class="box-body">
                    <div class="form-flex">
                        <FormItem label="医保缴纳账户">
                            <Select
                                v-model:value="medicalInsuranceAccountId"
                                showSearch
                                :showArrow="false"
                                :options="medicalInsuranceAccountOptions"
                                :placeholder="`医保缴纳账户`"
                                disabled
                            />
                        </FormItem>
                        <FormItem label="密码">
                            <InputPassword :value="medicalInsuranceAccountIdPassword" placeholder="密码" readonly />
                        </FormItem>
                    </div>
                </div>
            </div>
            <div class="param-box">
                <div class="title">
                    <span> 公积金 </span>
                    <span class="btns" v-if="customerInfo_edit_pay_edit">
                        <Button type="link" size="small" @click="showAccountModal(3, 'providentFund')">修改缴纳账户</Button>
                        <Button type="link" size="small" @click="showAccountModal(1)">修改公积金类型</Button>
                    </span>
                </div>
                <div class="box-body">
                    <div class="form-flex">
                        <FormItem label="公积金缴纳账户">
                            <Select
                                v-model:value="providentFundAccountId"
                                showSearch
                                :showArrow="false"
                                :options="providentFundAccountOptions"
                                :placeholder="`公积金缴纳账户`"
                                disabled
                            />
                        </FormItem>
                        <FormItem label="密码">
                            <InputPassword :value="providentFundAccountIdPassword" placeholder="密码" readonly />
                        </FormItem>
                    </div>
                </div>
                <Table
                    :columns="accumulationColumns"
                    :data-source="accumulationData"
                    :pagination="false"
                    :row-key="(record) => record.id + 'accumulationColumns'"
                    class="smallTable"
                    bordered
                />
            </div>
            <div class="param-box" style="margin-top: 0">
                <div class="title">
                    <span> 应发工资 </span>
                    <span class="btns" v-if="customerInfo_edit_pay_edit">
                        <Button type="link" size="small" @click="showAccountModal(3, 'salary')"> 修改缴纳账户 </Button>
                    </span>
                </div>
                <Table
                    :columns="salaryColumns"
                    :data-source="payrollAccountData"
                    :pagination="false"
                    :row-key="(record) => record.id + 'salaryColumns'"
                    style="width: 60%; margin-top: 15px"
                    class="smallTable"
                    bordered
                />
            </div>
        </Form>
        <AccountList
            :title="modalTitle"
            :visible="accountModalVisible"
            :item="currentValue"
            :accountType="accountType"
            @cancel="modalCancel"
            @confirm="confirm"
        />
    </div>
</template>
<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { InputPassword, message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, watchEffect, computed, toRefs } from 'vue'
import request from '/@/utils/request'
import customerUserStore from '/@/store/modules/customer'
import { isEmpty } from '/@/utils/index'
import { useAuth } from '/@/utils/hooks'
import AccountList from './accountList.vue'
export default defineComponent({
    name: 'SalsryParams',
    components: { InputPassword, AccountList },
    props: {
        viewType: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { viewType } = toRefs<any>(props)

        let customerInfo_edit_pay_edit = ref(false)
        const accountModalVisible = ref(false)
        const modalTitle = ref('')
        const currentValue = ref<any>(null)
        const accountType = ref(0)

        let providentFundTypeOptions = ref<LabelValueOptions>([])
        let socialSecurityTypeOptions = ref<LabelValueOptions>([])

        let socialSecurityAccountOptions = ref<LabelValueOptions>([])
        let medicalInsuranceAccountOptions = ref<LabelValueOptions>([])
        let providentFundAccountOptions = ref<LabelValueOptions>([])
        let payrollAccountOptions = ref<LabelValueOptions>([])
        onMounted(() => {
            if (viewType.value == 'see') {
                customerInfo_edit_pay_edit.value = false
            } else {
                customerInfo_edit_pay_edit.value = !!useAuth.value.buttons.find((i) => i == 'customerInfo_edit_pay_edit')
            }

            request.get('/api/hr-social-securities/selecthrplatform').then((res) => {
                socialSecurityTypeOptions.value = res.hrSocialSecurityDTO
                providentFundTypeOptions.value = res.hrAccumulationFundDTO
                let socialSecurityAccount: LabelValueOptions = [] //社保
                let medicalInsuranceAccount: LabelValueOptions = [] //医保
                let providentFundAccount: LabelValueOptions = [] //公积金
                let payrollAccount: LabelValueOptions = [] //工资
                res.hrPlatformAccountDTO.forEach((item: inObject) => {
                    switch (item.platformType) {
                        case '1':
                            socialSecurityAccount.push({ label: item.accountNumber, value: item.id, ...item })
                            break
                        case '2':
                            medicalInsuranceAccount.push({ label: item.accountNumber, value: item.id, ...item })
                            break
                        case '3':
                            providentFundAccount.push({ label: item.accountNumber, value: item.id, ...item })
                            break
                        case '4':
                            payrollAccount.push({ label: item.accountNumber, value: item.id, ...item })
                            break
                    }
                })
                socialSecurityAccountOptions.value = socialSecurityAccount
                medicalInsuranceAccountOptions.value = medicalInsuranceAccount
                providentFundAccountOptions.value = providentFundAccount
                payrollAccountOptions.value = payrollAccount
            })
        })

        //社保表格数据
        const socialSecurityColumns = [
            {
                title: '社保类型',
                dataIndex: 'socialSecurityName',
                align: 'center',
                rowKey: 'receivingAccount1',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
                rowKey: 'area1',
            },
            {
                title: '收款单位名称',
                dataIndex: 'nameOfBeneficiary',
                align: 'center',
                rowKey: 'nameOfBeneficiary1',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'accountBank',
                align: 'center',
                rowKey: 'accountBank1',
            },
            {
                title: '单位养老',
                dataIndex: 'unitPension',
                align: 'center',
                rowKey: 'unitPension1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位医疗',
                dataIndex: 'unitMedical',
                align: 'center',
                rowKey: 'unitMedical1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位工伤',
                dataIndex: 'workInjury',
                align: 'center',
                rowKey: 'workInjury1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位失业',
                dataIndex: 'unitUnemployment',
                align: 'center',
                rowKey: 'unitUnemployment1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位生育',
                dataIndex: 'unitMaternity',
                align: 'center',
                rowKey: 'unitMaternity1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人养老',
                dataIndex: 'personalPension',
                align: 'center',
                rowKey: 'personalPension1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人医疗',
                dataIndex: 'personalMedical',
                align: 'center',
                rowKey: 'personalMedical1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人失业',
                dataIndex: 'personalUnemployment',
                align: 'center',
                rowKey: 'personalUnemployment1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人生育',
                dataIndex: 'personalMaternity',
                align: 'center',
                rowKey: 'personalMaternity1',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]
        //公积金表格数据
        const accumulationColumns = [
            {
                title: '公积金类型名称',
                dataIndex: 'typeName',
                align: 'center',
                rowKey: 'typeName2',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
                rowKey: 'area2',
            },
            {
                title: '收款单位名称',
                dataIndex: 'payeeName',
                align: 'center',
                rowKey: 'payeeName2',
            },
            {
                title: '收款单位账号',
                dataIndex: 'payeeAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'payeeBank',
                align: 'center',
                rowKey: 'payeeBank2',
            },
            {
                title: '单位比例',
                dataIndex: 'unitScale',
                align: 'center',
                rowKey: 'unitScale2',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人比例',
                dataIndex: 'personageScale',
                align: 'center',
                rowKey: 'personageScale2',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]
        //工资
        const salaryColumns = [
            {
                title: '发放银行',
                dataIndex: 'issuingBank',
                align: 'center',
                rowKey: 'issuingBank3',
            },
            {
                title: '银行卡号',
                dataIndex: 'accountNumber',
                align: 'center',
                rowKey: 'accountNumber3',
            },
        ]
        const socialSecurityAccountIdPassword = computed(() => {
            return socialSecurityAccountOptions.value.find((item) => {
                return item.value == formData?.value?.socialSecurityAccountId
            })?.password
        })
        const medicalInsuranceAccountIdPassword = computed(() => {
            return medicalInsuranceAccountOptions.value.find((item) => {
                return item.value == formData?.value?.medicalInsuranceAccountId
            })?.password
        })
        const providentFundAccountIdPassword = computed(() => {
            return providentFundAccountOptions.value.find((item) => {
                return item.value == formData?.value?.providentFundAccountId
            })?.password
        })

        const basicDetail = ref<object>({})

        // Form Data
        const formData = ref<any>()

        watchEffect(() => {
            formData.value = customerUserStore().getCustomerInfo
        })

        const socialSecurityAccountId = computed(() => {
            let tempObj = socialSecurityAccountOptions.value.find((item) => {
                return item.id == formData.value.socialSecurityAccountId
            })
            if (tempObj) return formData.value.socialSecurityAccountId
            else return undefined
        })
        const medicalInsuranceAccountId = computed(() => {
            let tempObj = medicalInsuranceAccountOptions.value.find((item) => {
                return item.id == formData.value.medicalInsuranceAccountId
            })
            if (tempObj) return formData.value.medicalInsuranceAccountId
            else return undefined
        })
        const providentFundAccountId = computed(() => {
            let tempObj = providentFundAccountOptions.value.find((item) => {
                return item.id == formData.value.providentFundAccountId
            })
            if (tempObj) return formData.value.providentFundAccountId
            else return undefined
        })

        const socialSecurityData = computed(() => {
            let data = socialSecurityTypeOptions.value.filter((item) => {
                return item.id == formData.value.socialSecurityTypeId
            })
            return isEmpty(data) ? [{ receivingAccount: customerInfo_edit_pay_edit.value ? '请选择社保类型' : '' }] : data
        })
        const accumulationData = computed(() => {
            let data = providentFundTypeOptions.value.filter((item) => {
                return item.id == formData.value.providentFundTypeId
            })
            return isEmpty(data) ? [{ typeName: customerInfo_edit_pay_edit.value ? '请选择公积金类型' : '' }] : data
        })
        const payrollAccountData = computed(() => {
            let data = payrollAccountOptions.value.filter((item) => {
                return item.id == formData.value.payrollAccountId
            })
            return isEmpty(data) ? [{ issuingBank: customerInfo_edit_pay_edit.value ? '请选择发放银行' : '' }] : data
        })
        const changeSocialSecurityData = ({ key }) => {
            formData.value.socialSecurityTypeId = key.split(';;')[0]
        }
        const changeAccumulationData = ({ key }) => {
            formData.value.providentFundTypeId = key.split(';;')[0]
        }
        const changePayrollAccountData = ({ key }) => {
            formData.value.payrollAccountId = key.split(';;')[0]
        }
        // 账户0&公积金1&社保弹窗2
        const showAccountModal = (type, str = '') => {
            let [id, typeName] = ['', '']
            switch (type) {
                case 3:
                    if (str == 'socialInsurance') {
                        id = formData.value.socialSecurityAccountId
                        typeName = 'socialSecurityAccountId'
                    }
                    if (str == 'medicalInsurance') {
                        id = formData.value.medicalInsuranceAccountId
                        typeName = 'medicalInsuranceAccountId'
                    }
                    if (str == 'providentFund') {
                        id = formData.value.providentFundAccountId
                        typeName = 'providentFundAccountId'
                    }
                    if (str == 'salary') {
                        id = formData.value.payrollAccountId
                        typeName = 'payrollAccountId'
                    }
                    modalTitle.value = '修改缴纳账户'
                    break
                case 1:
                    id = formData.value.providentFundTypeId
                    typeName = 'providentFundTypeId'
                    modalTitle.value = '修改公积金类型'
                    break
                case 2:
                    id = formData.value.socialSecurityTypeId
                    typeName = 'socialSecurityTypeId'
                    modalTitle.value = '修改社保类型'
                    break
            }
            accountModalVisible.value = true
            currentValue.value = { id: id, typeName: typeName }
            accountType.value = type
        }

        const confirm = (obj) => {
            /* let {
                id,
                socialSecurityAccountId,
                medicalInsuranceAccountId,
                providentFundAccountId,
                socialSecurityTypeId,
                providentFundTypeId,
                payrollAccountId,
            } = formData.value */
            request
                .post('/api/hr-clients/update', {
                    flag: false,
                    id: formData.value.id,
                    ...obj,
                })
                .then((res) => {
                    console.log(res)
                    Object.keys(res).forEach((item) => {
                        res[item] ?? delete res[item]
                    })
                    customerUserStore().updateCustomerInfo(res)
                    message.success('保存成功!')
                    modalCancel()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const modalCancel = () => {
            accountModalVisible.value = false
            modalTitle.value = ''
            currentValue.value = null
            accountType.value = 0
        }
        const resetFormData = () => {
            formData.value = {}
        }
        return {
            socialSecurityAccountId,
            medicalInsuranceAccountId,
            providentFundAccountId,
            //按钮权限
            customerInfo_edit_pay_edit,

            formData,
            socialSecurityColumns,
            socialSecurityData,
            accumulationColumns,
            accumulationData,
            basicDetail,
            payrollAccountData,

            salaryColumns,

            modalCancel,
            confirm,

            //类型
            socialSecurityTypeOptions,
            providentFundTypeOptions,

            socialSecurityAccountOptions,
            medicalInsuranceAccountOptions,
            providentFundAccountOptions,
            payrollAccountOptions,

            //事件
            changeSocialSecurityData,
            changeAccumulationData,
            changePayrollAccountData,

            socialSecurityAccountIdPassword,
            medicalInsuranceAccountIdPassword,
            providentFundAccountIdPassword,
            showAccountModal,
            accountModalVisible,
            currentValue,
            modalTitle,
            accountType,
        }
    },
})
</script>
<style scoped lang="less">
.param-box {
    margin: 20px 0px;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    box-sizing: border-box;
    .title {
        height: 43px;
        background-color: #6894fe;
        color: #fff;
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .btns {
            .ant-btn-link {
                color: #fff;
            }
        }
    }
    .box-body {
        padding-top: 24px;
    }
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.ant-modal-body {
    padding: 24px 0 0 !important;
    & > .ant-form.ant-form-horizontal {
        margin: 0 24px 24px;
    }
}
.smallTable {
    margin: 0 15px 30px;
    // border: 1px solid #e8e8e8;
    :deep(.ant-table-thead > tr > th) {
        background-color: #fafafa;
        color: #000;
    }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
    color: #000;
    background: #fff;
}
.ant-dropdown-link {
    min-height: 18px;
    display: inline-block;
}
</style>
