<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="1200px">
        <SearchBar v-model="params" :options="options" @change="searchData" />
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRef"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="tableColumns"
            :data-source="tableData"
            :row-key="(record) => record.id"
            :pagination="accountType == 3 ? false : pagination"
            :loading="loading"
            :row-selection="selectionRowConfig"
            @change="tableChange"
        />
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
import { ref, defineComponent, toRefs, watch, onMounted, computed } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import request from '/@/utils/request'
import { toUnderline } from '/@/utils'
export default defineComponent({
    name: 'AccountList',
    props: {
        item: {
            type: Object,
            default: () => {},
        },
        accountType: {
            type: Number,
            default: 0,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //筛选
        const params = ref<inObject>({})
        const tableRef = ref()
        //  Data
        const { item, accountType } = toRefs<any>(props)
        // 搜索
        const searchData = async () => {
            refresh(1)
        }
        const refresh = (page = pagination.value.current) => {
            pagination.value.current = page
            getTableData()
        }
        const tableColumns = ref<inObject[]>([])
        const accountTypeList = ref<any>([])
        // 公积金
        const accumulationColumns = [
            {
                title: '公积金类型名称',
                dataIndex: 'typeName',
                align: 'center',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
            },
            {
                title: '收款单位名称',
                dataIndex: 'payeeName',
                align: 'center',
            },
            {
                title: '收款单位账号',
                dataIndex: 'payeeAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'payeeBank',
                align: 'center',
            },
            {
                title: '单位比例',
                dataIndex: 'unitScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人比例',
                dataIndex: 'personageScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
        ]
        // 社保
        const socialSecurityColumns = [
            {
                title: '社保类型名称',
                dataIndex: 'socialSecurityName',
                align: 'center',
                width: 100,
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
                width: 100,
            },
            {
                title: '收款单位名称',
                dataIndex: 'nameOfBeneficiary',
                align: 'center',
                width: 100,
            },
            {
                title: '收款单位账号',
                dataIndex: 'receivingAccount',
                align: 'center',
                width: 100,
            },
            {
                title: '收款单位开户行',
                dataIndex: 'accountBank',
                align: 'center',
                width: 100,
            },
            {
                title: '单位养老',
                dataIndex: 'unitPension',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位医疗',
                dataIndex: 'unitMedical',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位生育',
                dataIndex: 'unitMaternity',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位工伤',
                dataIndex: 'workInjury',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位失业',
                dataIndex: 'unitUnemployment',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人养老',
                dataIndex: 'personalPension',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人医疗',
                dataIndex: 'personalMedical',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人生育',
                dataIndex: 'personalMaternity',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人失业',
                dataIndex: 'personalUnemployment',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
        ]
        // 账户
        const platformColumns = [
            {
                title: '账号类型',
                dataIndex: 'accountType',
                align: 'center',
            },
            {
                title: '发放银行',
                dataIndex: 'issuingBank',
                align: 'center',
            },
            {
                title: '账户号',
                dataIndex: 'accountNumber',
                align: 'center',
            },
            {
                title: '密码',
                dataIndex: 'password',
                align: 'center',
            },
        ]
        const options = computed(() => {
            return [
                {
                    type: 'string',
                    label: '公积金类型名称',
                    key: 'typeName',
                    show: accountType.value == 1,
                },
                {
                    type: 'string',
                    label: '社保类型名称',
                    key: 'socialSecurityName',
                    show: accountType.value == 2,
                },
                {
                    type: 'string',
                    label: '账户号',
                    key: 'accountNumber',
                    show: accountType.value == 3,
                },
            ]
        })
        watch(
            accountType,
            (val, old) => {
                if (val) {
                    getTableData()
                }
            },
            { deep: true },
        )
        // 账户0&公积金1&社保弹窗2
        const getCurrentApi = () => {
            if (accountType.value == 3) {
                tableColumns.value = platformColumns
                return '/api/hr-PlatformAccount/selectaccount'
            } else if (accountType.value == 1) {
                tableColumns.value = accumulationColumns
                return '/api/hr-accumulation-funds/page'
            } else {
                tableColumns.value = socialSecurityColumns
                return '/api/hr-social-securities/page'
            }
        }
        const getPlatformType = () => {
            if (item.value.typeName == 'socialSecurityAccountId') return '1'
            if (item.value.typeName == 'medicalInsuranceAccountId') return '2'
            if (item.value.typeName == 'providentFundAccountId') return '3'
            if (item.value.typeName == 'payrollAccountId') return '4'
        }
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('list', '/api/hr-platform-accounts/list', 'get', true)
                .then((data: inObject[]) => {
                    accountTypeList.value = data.map((item) => {
                        return { label: item.accountType, value: item.accountType }
                    })
                })
        })
        const selectionRowConfig = computed(() => {
            return {
                selectedRowKeys: selectedRowKeysArr,
                onChange: onSelectChange,
                type: 'radio',
                getCheckboxProps: (record: inObject) => {
                    return {
                        defaultChecked: [item.value.id].indexOf(record.id) > -1 ? true : false,
                        id: record.id + '',
                    }
                },
            }
        })
        const sortParams = ref({
            field: undefined,
            order: undefined,
        })
        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const tableData = ref([])

        const loading = ref(false)
        const pagination = ref<any>({
            current: 1,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
            total: 0,
        })
        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field: sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }
            getTableData()
        }
        const getTableData = async () => {
            loading.value = true
            try {
                let fn
                if (accountType.value == 3) {
                    fn = () => request.post(getCurrentApi(), { id: getPlatformType(), ...params.value })
                } else {
                    fn = () =>
                        request.post(
                            `${getCurrentApi()}?pageNumber=${pagination.value.current}&pageSize=${pagination.value.pageSize}`,
                            {
                                ...params.value,
                                ...{
                                    field: sortParams.value.field ? toUnderline(sortParams.value.field) : undefined,
                                    order:
                                        sortParams.value.order === 'ascend'
                                            ? 'DESC'
                                            : sortParams.value.order === 'descend'
                                            ? 'ASC'
                                            : undefined,
                                },
                            },
                        )
                }
                const data = await fn()
                tableData.value = data.records || data || []
                pagination.value.total = data.total || 0
                // 复选框置空
                selectedRowsArr.value = []
                selectedRowKeysArr.value = [item.value.id]
            } finally {
                loading.value = false
            }
        }
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }

        const onCancel = () => {
            params.value = {}
            emit('cancel')
        }
        const onConfirm = () => {
            emit('confirm', { [item.value.typeName]: selectedRowKeysArr.value[0] })
        }

        return {
            loading,
            tableRef,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            pagination,
            selectionRowConfig,
            tableChange,

            tableColumns,
            options,
            params,
            searchData,
            onCancel,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
