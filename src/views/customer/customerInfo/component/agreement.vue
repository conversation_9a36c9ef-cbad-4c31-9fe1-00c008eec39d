<template>
    <div>
        <Form
            ref="formInlineAgreement"
            :model="formData"
            :label-col="{ style: { width: '130px' } }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="(itemForm, index) in myOptions" :key="itemForm.name + 'agreement'">
                <MyFormItem
                    v-if="!itemForm.external"
                    :class="[rules[itemForm.name]?.required ? 'required ' : '', itemForm.slots]"
                    :width="itemForm.width"
                    :item="itemForm"
                    v-model:value="formData[itemForm.name]"
                >
                    <template #clientId>
                        <ClientSelectTree v-model:value="formData[itemForm.name]" v-model:itemForm="myOptions[index]" />
                    </template>
                </MyFormItem>

                <template v-else-if="itemForm.name == 'hrDocking'">
                    <template v-for="(hrDockingItem, index) in formData.hrDocking" :key="index + 'hrDocking'">
                        <FormItem
                            :class="index == 0 ? '' : 'hide'"
                            :label="'对接部门'"
                            :rules="validateDepartment"
                            :name="['hrDocking', index, 'department']"
                        >
                            <Input
                                v-model:value="hrDockingItem.department"
                                :disabled="viewType == 'seeCustomer'"
                                :placeholder="`请输入对接部门`"
                            />
                        </FormItem>
                        <FormItem
                            :class="index == 0 ? '' : 'hide'"
                            :label="'对接人'"
                            :rules="validateDocking"
                            :name="['hrDocking', index, 'docking']"
                        >
                            <Input
                                v-model:value="hrDockingItem.docking"
                                :disabled="viewType == 'seeCustomer'"
                                :placeholder="`请输入对接人`"
                            />
                        </FormItem>
                        <FormItem
                            :class="index == 0 ? '' : 'hide'"
                            :label="'对接人联系电话'"
                            :rules="onePhoneCheck"
                            :name="['hrDocking', index, 'dockingPhone']"
                        >
                            <Input
                                v-model:value="hrDockingItem.dockingPhone"
                                :disabled="viewType == 'seeCustomer'"
                                :placeholder="`请输入对接人联系电话`"
                            />

                            <span style="width: 24px; margin-left: 5px; display: inline-block">
                                <MinusCircleOutlined
                                    v-show="index != 0"
                                    v-if="viewType != 'seeCustomer'"
                                    class="dynamic-delete-button"
                                    @click="deleDomain(index)"
                                />
                            </span>
                        </FormItem>
                    </template>
                    <Button type="dashed" @click="addDomain" class="addhrDocking" v-if="viewType != 'seeCustomer'">
                        <PlusOutlined />
                    </Button>
                </template>

                <template v-else-if="itemForm.name == 'serviceChargeItem'">
                    <FormItem
                        :label="itemForm.label"
                        :name="itemForm.name"
                        :rules="validateServiceCharge"
                        :style="{ width: itemForm.width }"
                    >
                        <div class="charge_item">
                            <Select
                                :options="serviceChargeOptions"
                                placeholder="请选择"
                                v-model:value="formData[itemForm.name].serviceFeeType"
                                :getPopupContainer="getPopupContainer"
                                :disabled="itemForm.disabled"
                                @change="serviceChargeChange"
                                v-if="formData.agreementType != 2"
                                style="margin-right: 15px"
                            />
                            <InputNumber
                                v-model:value="formData[itemForm.name].serviceCharge"
                                :disabled="itemForm.disabled"
                                :placeholder="`请输入服务费`"
                                style="width: 150px"
                                :min="0"
                                @change="formValidateOptional(['serviceChargeItem'])"
                            />
                            <template v-if="formData[itemForm.name].serviceFeeType == 1 && formData.agreementType != 2">
                                <span class="symbol">
                                    <PlusOutlined />
                                </span>
                                <Select
                                    :dropdownMatchSelectWidth="false"
                                    :options="calculationOptions"
                                    placeholder="请选择"
                                    v-model:value="formData[itemForm.name].serviceFeeData"
                                    :getPopupContainer="getPopupContainer"
                                    :disabled="itemForm.disabled"
                                    @change="formValidateOptional(['serviceChargeItem'])"
                                />
                                <span class="symbol">
                                    <CloseOutlined />
                                </span>
                                <Input
                                    v-model:value="formData[itemForm.name].calculationFormula"
                                    :disabled="itemForm.disabled"
                                    :placeholder="`请输入公式`"
                                    style="width: 150px"
                                    @change="(e) => calculationFormat(e, 'serviceChargeItem')"
                                    @blur="formValidateOptional(['serviceChargeItem'])"
                                />
                            </template>
                        </div>
                    </FormItem>
                </template>
                <template v-else-if="itemForm.name == 'taxItem'">
                    <FormItem
                        :label="itemForm.label"
                        :name="itemForm.name"
                        :rules="validateTaxItem"
                        :style="{ width: itemForm.width }"
                        v-if="itemForm?.show"
                    >
                        <div class="charge_item">
                            <Select
                                :dropdownMatchSelectWidth="false"
                                :options="calculationOptions"
                                placeholder="请选择"
                                v-model:value="formData[itemForm.name].serviceFeeData"
                                :getPopupContainer="getPopupContainer"
                                :disabled="itemForm.disabled"
                                @change="(e) => calculationFormat(e, 'taxItem')"
                            />
                            <span class="symbol">
                                <CloseOutlined />
                            </span>
                            <Input
                                v-model:value="formData[itemForm.name].calculationFormula"
                                :disabled="itemForm.disabled"
                                :placeholder="`请输入公式`"
                                style="width: 150px"
                                @change="(e) => calculationFormat(e, 'taxItem')"
                                @blur="formValidateOptional(['taxItem'])"
                            />
                        </div>
                    </FormItem>
                </template>

                <template v-else>
                    <FormItem :label="'附件'" :name="itemForm.name" style="width: 99%">
                        <ImportFile
                            v-model:fileUrls="formData[itemForm.name]"
                            ref="refImportFile"
                            :readOnly="viewType == 'seeCustomer'"
                        />
                    </FormItem>
                </template>
            </template>
        </Form>
        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <template v-if="viewType == 'add'">
                <Button key="submit" type="primary" @click="backConfirm">上一步</Button>
                <Button key="submit" type="primary" @click="customerAddConfirm">完成</Button>
            </template>
            <template v-else>
                <Button key="submit" type="primary" @click="confirm" v-if="viewType != 'seeCustomer'">确定</Button>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { CloseOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
// import { RuleObject } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import { computed, defineComponent, nextTick, onMounted, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import { checkFormula } from '/@/utils/format'
import moment, { Moment } from 'moment'
import customerUserStore from '/@/store/modules/customer'

import {
    agreementArchiveStatusList,
    calculationOptions,
    invoiceTypeOptions,
    serviceChargeOptions,
    stateOptions,
} from '/@/utils/dictionaries'
import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'

export default defineComponent({
    name: 'AgreeMent',
    components: {
        MinusCircleOutlined,
        PlusOutlined,
        CloseOutlined,
    },
    props: {
        viewType: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
        isEditAdd: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'backConfirm', 'cancel', 'customerAddConfirm'],
    setup(props, { emit }) {
        //表单数据
        // const rules: Array<Object> = []
        // let stateOptions = ref<LabelValueOptions>([
        //     {
        //         value: 0,
        //         label: '生效中',
        //     },
        //     { value: 1, label: '即将过期' },
        //     { value: 2, label: '已过期' },
        //     { value: 3, label: '已终止' },
        // ])
        let settlementMethodOptions = ref<LabelValueOptions>([])

        let businessTypeOptions = ref<LabelValueOptions>([])
        let serviceFeeType = ref<LabelValueOptions>([])
        let serviceFeeTypeList = ref<LabelValueOptions>([])
        let countDown = ref(60)
        onMounted(() => {
            // 业务类型
            dictionaryDataStore()
                .setDictionaryData('businessType', '')
                .then((res: LabelValueOptions) => {
                    businessTypeOptions.value = res
                })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/businessType', {}).then((res) => {
            //     businessTypeOptions.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            //结算方式
            dictionaryDataStore()
                .setDictionaryData('settlementMethod', '')
                .then((res: LabelValueOptions) => {
                    settlementMethodOptions.value = res
                })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/settlementMethod', {}).then((res) => {
            //     settlementMethodOptions.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            request.get('/api/hr-protocols/template/time', {}).then((res) => {
                if (typeof res?.ruleDay == undefined) {
                    countDown.value = 60
                } else {
                    countDown.value = isNaN(res?.ruleDay) ? 60 : res?.ruleDay
                }
            })

            //服务费类型
            dictionaryDataStore()
                .setDictionaryData('agreementChargeType', '')
                .then((res: LabelValueOptions) => {
                    serviceFeeTypeList.value = res
                    serviceFeeType.value = res
                })

            // 上级客户
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id }
            //     })
            // })
        })
        //请求
        const { viewType, item, visible, isEditAdd } = toRefs<any>(props)
        // Form 实例
        const formInlineAgreement = ref(null) as any

        const stateIs3 = ref(false)
        const isShowRemark = ref(true)
        const isViewTypeRecovery = ref(false)
        const isViewTypeEditCustomer = ref(false)
        const isViewTypeRenewCustomer = ref(false)
        const isViewTypeAddCustomer = ref(false)
        const isViewTypeAddAndEditCustomer = ref(false)
        const agreementTitleWidth = ref('66%')

        const isViewTypeRenewclientId = ref(false)

        const agreementDisabled = computed(() => {
            return isViewTypeRenewCustomer.value || isViewTypeEditCustomer.value
        })
        const agreementBr = computed(() => {
            return !isViewTypeRenewCustomer.value && !isViewTypeEditCustomer.value
        })
        const firstSignTimeDisabled = computed(() => {
            return viewType.value == 'seeCustomer'
        })

        //修改客户名称
        const changeClientId = (value: string, option: inObject) => {
            // console.log(option)
            formData.value.agreementTitle = option.label + '的协议'
            formValidateOptional(['agreementTitle'])
        }
        const changeData = () => {
            let agreementStartDate: number = new Date(formData.value?.agreementStartDate)?.valueOf()
            let agreementEndDate: number = new Date(formData.value?.agreementEndDate)?.valueOf()
            if (agreementStartDate == 0 || agreementEndDate == 0) {
                return
            }
            let todayStartOf: number = moment().startOf('day')?.valueOf()
            let todayEndOf: number = moment().endOf('day')?.valueOf()

            let countDownTime = countDown.value * 24 * 60 * 60 * 1000
            if (agreementStartDate > todayEndOf) {
                formData.value.states = 4
                if (agreementEndDate >= todayStartOf) {
                    if (agreementEndDate - countDownTime < todayEndOf) {
                        formData.value.states = 5
                    }
                }
            } else if (agreementStartDate <= todayEndOf) {
                formData.value.states = 0
                if (agreementEndDate < todayStartOf) {
                    formData.value.states = 2
                } else if (agreementEndDate >= todayStartOf) {
                    if (agreementEndDate - countDownTime < todayEndOf) {
                        formData.value.states = 5
                    }
                }
            }
            // if (formData.value.agreementEndDate) {
            // }
        }

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '单位编号',
                name: 'unitNumber1',
                disabled: true,
                show: isViewTypeRecovery,
                required: false,
            },
            {
                label: '所属客户',
                name: 'clientName1',
                disabled: true,
                showbr: true,
                show: isViewTypeRecovery,
                required: false,
            },
            {
                label: '协议编号',
                name: 'agreementNumber',
                disabled: isViewTypeEditCustomer,
            },
            {
                label: '所属客户',
                name: 'clientId',
                type: 'slots',
                slots: 'clientId',
                disabled: isViewTypeRenewclientId,
                showbr: isViewTypeAddAndEditCustomer,
                show: isViewTypeAddAndEditCustomer,
                // options: selectclientsOptions,
                required: true,
                onChange: changeClientId,
            },
            {
                label: '协议标题',
                name: 'agreementTitle',
                width: agreementTitleWidth,
                disabled: isViewTypeEditCustomer,
            },

            {
                required: true,
                label: '协议类型',
                name: 'agreementType',
                type: 'change',
                options: businessTypeOptions,

                disabled: isViewTypeEditCustomer,
                ruleType: 'number',
                onChange: (value, option) => {
                    if (value == 2) formData.value.serviceChargeItem.serviceFeeType = 1
                    else formData.value.serviceChargeItem.serviceFeeType = 0
                },
            },
            {
                label: '结算方式',
                name: 'settlementMethod',
                type: 'change',
                options: settlementMethodOptions,

                disabled: isViewTypeEditCustomer,
                ruleType: 'number',
                onChange: (value, option) => radioGroupChange(value, option),
            },
            {
                type: 'select',
                label: '服务费类型',
                name: 'serviceChargeType',
                options: serviceFeeType,
                disabled: computed(() => {
                    return isViewTypeEditCustomer.value
                }),
                ruleType: 'number',
                required: true,
            },
            {
                label: '服务费',
                name: 'serviceChargeItem',
                disabled: isViewTypeEditCustomer,
                type: 'slots',
                ruleType: 'object',
                external: true,
                width: computed(() => {
                    return formData.value.agreementType == 2 ? '33%' : '66%'
                }),
                default: {
                    serviceFeeType: 0,
                    serviceCharge: 0,
                    serviceFeeData: 1,
                    calculationFormula: '',
                },
            },
            {
                label: '税费',
                name: 'taxItem',
                disabled: isViewTypeEditCustomer,
                ruleType: 'object',
                external: true,
                default: {
                    serviceFeeData: 0,
                    calculationFormula: '',
                },
                width: '33%',
                show: computed(() => {
                    return formData.value.agreementType == 2
                }),
            },
            {
                label: '开票内容',
                name: 'invoiceType',
                type: 'select',
                showbr: true,
                required: true,
                options: invoiceTypeOptions,
                disabled: isViewTypeEditCustomer,
                allowClear: false,
                ruleType: 'number',
                default: 1,
            },
            {
                label: '协议开始日期',
                name: 'agreementStartDate',
                type: 'date',

                disabled: agreementDisabled,
                onChange: changeData,
                disabledDate: (startValue: Moment) => {
                    if (!startValue || !formData.value?.agreementEndDate) {
                        return false
                    }
                    return startValue.startOf('day').valueOf() > new Date(formData.value?.agreementEndDate)?.valueOf()
                },
            },

            {
                label: '协议结束日期',
                name: 'agreementEndDate',
                type: 'date',

                showbr: agreementBr,
                disabled: isViewTypeEditCustomer,
                onChange: changeData,
                disabledDate: (endValue: Moment) => {
                    if (!endValue || !formData.value?.agreementStartDate) {
                        return false
                    }
                    if (viewType.value == 'renewCustomer') {
                        return moment().subtract(1, 'day') > endValue.startOf('day')
                    } else {
                        return new Date(formData.value?.agreementStartDate).valueOf() >= endValue.startOf('day').valueOf()
                    }
                },
            },
            {
                label: '初签时间',
                name: 'firstSignTime',
                type: 'date',
                required: false,
                disabled: firstSignTimeDisabled,
            },
            {
                label: '协议状态',
                name: 'states',
                showbr: true,
                disabled: true,
                show: isViewTypeAddAndEditCustomer,
                default: 0,
                ruleType: 'number',
                type: 'change',
                options: stateOptions,

                required: isViewTypeAddAndEditCustomer,
            },
            // 新增表单 归档时间/归档状态/协议存放地
            {
                label: '归档时间',
                name: 'archiveTime',
                type: 'date',
            },
            {
                label: '归档状态',
                name: 'archiveStatus',
                type: 'change',
                options: agreementArchiveStatusList,
                ruleType: 'number',
                onChange: (value, option) => {
                    console.log('归档状态', value, option)
                },
            },
            {
                label: '协议存放地',
                name: 'archiveAddress',
                width: agreementTitleWidth,
            },
            // 新增字段 结束
            {
                label: '协议终止时间',
                name: 'agreementTerminationDate',
                required: false,
                disabled: true,
                show: stateIs3,
            },
            {
                label: '协议终止原因',
                name: 'reasonTerminationAgreement',
                required: false,
                showbr: true,
                disabled: true,
                width: '66%',
                show: stateIs3,
            },

            {
                label: '对接',
                name: 'hrDocking',
                ruleType: 'array',
                required: false,
                external: true,
                default: [
                    {
                        docking: '',
                        dockingPhone: '',
                        department: '',
                    },
                ],
            },
            {
                label: '备注',
                name: 'remark',
                width: '99%',
                required: false,
                type: 'textarea',
                show: isShowRemark,
            },
            {
                label: '附件',
                name: 'appendixList',
                ruleType: 'array',
                required: false,
                external: true,
                default: [],
            },
        ])

        // FormData rules 初始值
        const { values: initFormData, rules }: any = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const radioGroupChange = (value, option) => {
            console.log(value, rules)
            serviceFeeType.value = serviceFeeTypeList.value
            // console.log(event?.target?.value == 2 || event?.target?.value == 3)
            if (value == 3 || value == 4) {
                rules.serviceChargeType.required = false
                formData.value.serviceChargeType = null
                serviceFeeType.value = serviceFeeTypeList.value.filter((el) => {
                    return el.value != 1
                })
            } else {
                rules.serviceChargeType.required = true
            }

            formValidateOptional(['serviceChargeType'])
        }

        watch(
            visible,
            () => {
                if (visible.value) {
                    if (viewType.value == 'recovery') {
                        formData.value = Object.assign({}, initFormData, {
                            clientId: item.value?.clientId,
                            unitNumber1: item.value?.unitNumber,
                            clientName1: item.value?.clientName,
                            agreementNumber: 'XY' + new Date().getTime() || '',
                            agreementStartDate: moment(new Date()).format('YYYY-MM-DD') || '',
                            agreementTitle: item.value?.clientName + '的协议' || '',
                        })
                        formData.value.serviceChargeItem = {
                            serviceFeeType: item.value?.serviceFeeType ?? 0,
                            serviceCharge: item.value?.serviceCharge,
                            serviceFeeData: item.value?.serviceFeeData ?? 1,
                            calculationFormula: item.value?.calculationFormula,
                        }
                        if (item.value?.agreementType == 2) {
                            formData.value.taxItem = {
                                serviceFeeData: item.value?.serviceFeeData ?? 1,
                                calculationFormula: item.value?.calculationFormula,
                            }
                        }
                    } else if (item.value?.id) {
                        request.get(`/api/hr-protocols`, { id: item.value.id }).then((res) => {
                            formData.value = Object.assign({}, initFormData, res)
                            formData.value.serviceChargeItem = {
                                serviceFeeType: res?.serviceFeeType ?? 0,
                                serviceCharge: res?.serviceCharge,
                                serviceFeeData: res?.serviceFeeData ?? 1,
                                calculationFormula: res?.calculationFormula,
                            }

                            if (item.value?.agreementType == 2) {
                                formData.value.taxItem = {
                                    serviceFeeData: res?.serviceFeeData ?? 1,
                                    calculationFormula: res?.calculationFormula,
                                }
                            }
                            if (viewType.value == 'renewCustomer') {
                                formData.value.agreementNumber = 'XY' + new Date().getTime()
                                formData.value.agreementStartDate = moment(new Date(res.agreementEndDate))
                                    .add(1, 'days')
                                    .format('YYYY-MM-DD')
                                formData.value.agreementEndDate = ''
                            }
                        })
                    } else {
                        formData.value = initFormData
                        formData.value.agreementNumber = 'XY' + new Date().getTime()
                        formData.value.agreementStartDate = moment(new Date()).format('YYYY-MM-DD')
                        if (isEditAdd.value) {
                            formData.value.clientId = item.value?.clientId
                        }
                        // console.log(formData.value.agreementStartDate)
                    }
                    //定制化校验
                    switch (viewType.value) {
                        case 'recovery':
                            isShowRemark.value = true
                            isViewTypeRecovery.value = true
                            isViewTypeEditCustomer.value = false
                            isViewTypeRenewclientId.value = false
                            isViewTypeAddCustomer.value = false
                            isViewTypeRenewCustomer.value = false
                            isViewTypeAddAndEditCustomer.value = false
                            agreementTitleWidth.value = '66%'
                            stateIs3.value = false
                            break
                        case 'addCustomer':
                            isShowRemark.value = true
                            isViewTypeRecovery.value = false
                            isViewTypeEditCustomer.value = false
                            if (isEditAdd.value) isViewTypeRenewclientId.value = true
                            else isViewTypeRenewclientId.value = false
                            isViewTypeAddCustomer.value = true
                            isViewTypeRenewCustomer.value = false
                            isViewTypeAddAndEditCustomer.value = true
                            agreementTitleWidth.value = '99%'

                            stateIs3.value = false
                            break
                        case 'editCustomer':
                            isShowRemark.value = true
                            isViewTypeRecovery.value = false
                            isViewTypeEditCustomer.value = true
                            isViewTypeRenewclientId.value = true
                            isViewTypeAddCustomer.value = false
                            isViewTypeAddAndEditCustomer.value = true
                            agreementTitleWidth.value = '99%'

                            stateIs3.value = formData.value.state == 3
                            isViewTypeRenewCustomer.value = false
                            break
                        case 'renewCustomer':
                            isShowRemark.value = true
                            isViewTypeRecovery.value = false
                            isViewTypeEditCustomer.value = false
                            isViewTypeRenewclientId.value = true
                            isViewTypeRenewCustomer.value = true
                            isViewTypeAddCustomer.value = false
                            isViewTypeAddAndEditCustomer.value = true
                            agreementTitleWidth.value = '99%'
                            stateIs3.value = formData.value.state == 3
                            break
                        case 'seeCustomer':
                            isShowRemark.value = false
                            isViewTypeRecovery.value = false
                            isViewTypeEditCustomer.value = true
                            isViewTypeRenewclientId.value = true
                            isViewTypeAddCustomer.value = false
                            isViewTypeRenewCustomer.value = false
                            isViewTypeAddAndEditCustomer.value = true
                            agreementTitleWidth.value = '99%'

                            stateIs3.value = formData.value.state == 3
                            break
                    }
                }
            },
            { immediate: true },
        )
        const refImportFile = ref()
        // confirm handle
        const confirm = (confirmData?: inObject) => {
            let appendixId = refImportFile.value.getFileUrls().map((item) => {
                return item.id
            })
            // return false
            formInlineAgreement.value
                .validate()
                .then(async () => {
                    let hrDocking = []
                    if (formData.value?.hrDocking) {
                        hrDocking = formData.value?.hrDocking?.filter((element: inObject) => {
                            return element.dockingPhone
                        })
                    }
                    //update
                    let params: any = {}
                    if (viewType.value == 'renewCustomer') {
                        if (formData.value.states == 5) {
                            formData.value.states = 1
                        } else if (formData.value.states == 6) {
                            formData.value.states = 2
                        }
                        params = {
                            ...formData.value,
                            ...confirmData,
                            ...formData.value.serviceChargeItem,
                            hrDocking: hrDocking.map((el: inObject) => {
                                delete el.id
                                return el
                            }),
                            renewType: 1,
                            protocolId: formData.value.id,
                            appendixId: appendixId.join(),
                            appendixList: null,
                        }
                        delete params.id
                        await request.post(
                            '/api/hr-protocols/insert',
                            formData.value.agreementType == 2 ? { ...params, ...formData.value.taxItem } : params,
                        )
                        message.success('续签成功!')
                        emit('confirm')
                    } else if (viewType.value != 'editCustomer' && viewType.value != 'seeCustomer') {
                        if (formData.value.states == 5) {
                            formData.value.states = 1
                        } else if (formData.value.states == 6) {
                            formData.value.states = 2
                        }
                        params = {
                            ...formData.value,
                            ...confirmData,
                            ...formData.value.serviceChargeItem,
                            hrDocking,
                            appendixId: appendixId.join(),
                            renewType: 0,
                            appendixList: null,
                        }
                        let record = await request.post(
                            '/api/hr-protocols/insert',
                            formData.value.agreementType == 2 ? { ...params, ...formData.value.taxItem } : params,
                        )
                        message.success('新增成功!')
                        if (isEditAdd.value) {
                            customerUserStore().fetchCustomerProtocol({ id: record?.clientId }, true)
                        }
                        emit('confirm')
                    } else {
                        params = {
                            ...formData.value,
                            ...confirmData,
                            ...formData.value.serviceChargeItem,
                            hrDocking,
                            appendixId: appendixId.join(),
                            appendixList: null,
                        }
                        await request.post(
                            '/api/hr-protocols/updateHrDocking',
                            formData.value.agreementType == 2 ? { ...params, ...formData.value.taxItem } : params,
                        )
                        message.success('修改成功!')
                        emit('confirm')
                    }
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        //完成在添加的时候需要拿到客户id
        const customerAddConfirm = () => {
            formInlineAgreement.value
                .validate()
                .then(async () => {
                    //update
                    emit('customerAddConfirm')
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const backConfirm = () => {
            emit('backConfirm')
        }
        // cancel handle
        const cancel = () => {
            resetData()
            emit('cancel')
        }

        const resetData = () => {
            formData.value = initFormData
            formData.value.serviceChargeItem = {
                serviceFeeType: 0,
                serviceCharge: 0,
                serviceFeeData: 1,
                calculationFormula: '',
            }
            formData.value.hrDocking = [
                {
                    docking: '',
                    dockingPhone: '',
                    department: '',
                },
            ]
            formInlineAgreement.value.resetFields()
        }
        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInlineAgreement.value?.validate(nameList)
            })
        }
        const addDomain = () => {
            if (formData.value?.hrDocking) {
                formData.value?.hrDocking?.push({
                    docking: '',
                    dockingPhone: '',
                    department: '',
                })
            } else {
                formData.value.hrDocking = [
                    {
                        docking: '',
                        dockingPhone: '',
                        department: '',
                    },
                ]
            }
        }
        const deleDomain = (index) => {
            let myArray = JSON.parse(JSON.stringify(formData.value.hrDocking))
            myArray.splice(index, 1)
            formData.value.hrDocking = myArray
        }

        const serviceChargeChange = (value) => {
            if (value == 1) formData.value.serviceChargeItem.serviceFeeData = 1
            if (value == 0) {
                formData.value.serviceChargeItem.serviceFeeData = 1
                formData.value.serviceChargeItem.calculationFormula = ''
            }
            formValidateOptional(['serviceChargeItem'])
        }

        const calculationFormat = (e, key: string) => {
            formData.value[key].calculationFormula = e.target.value
                .replace(
                    /[A-Za-z\&\^\$\#\@\!\（\）\？\?\,\！\%\……\￥\，\。\①\②\③\④\⑤\⑥\⑦\⑧\⑨\⑩\÷\；\：\'\;\:\"\“\”\：\、\】\【\{\}\》\《\=\<\>\\\|\·\~\`\——\_\[\]\’\‘]/g,
                    '',
                )
                .replace(/[\s\u4e00-\u9fa5]/g, '')
                // .replace(/0+\./g, '0.')
                .replace(/\.+/g, '.')
                .replace(/(\d+\.\d*)\./g, '$1')
            // .replace(/(\d+\.[1-8]*?|0+[1-8]*?)0+$/g, '$1')
            formValidateOptional(key)
        }

        const validateTaxItem = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.taxItem
                return checkFormula(rule, formDataItem.calculationFormula)
            },
        }

        const validateServiceCharge = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.serviceChargeItem
                if (formDataItem?.serviceFeeType == 0) {
                    if (isEmpty(formDataItem.serviceCharge)) return Promise.reject('请将服务费信息填写完整')
                    else return Promise.resolve()
                } else {
                    if (formData.value.agreementType != 2) {
                        if (isEmpty(formDataItem.serviceCharge)) return Promise.reject('请将服务费信息填写完整')
                        else return checkFormula(rule, formDataItem.calculationFormula)
                    } else return Promise.resolve()
                }
            },
        }

        const onePhoneCheck = {
            required: true,
            trigger: 'blur',
            type: 'string',
            validator: (rule: inObject, value: any) => {
                if (!value) return Promise.reject('请输入手机号')
                let formDataItem = formData.value?.hrDocking[rule.field.split('.')[1]]
                if (formDataItem?.docking || formDataItem?.department || formDataItem?.dockingPhone) {
                    if (!value) return Promise.reject('请输入手机号')
                    return Promise.resolve(true)
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validateDocking = {
            required: true,
            trigger: 'blur',
            type: 'string',
            validator: (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请输入对接人')
                }
                let formDataItem = formData.value?.hrDocking[rule.field.split('.')[1]]
                if (formDataItem?.department || formDataItem?.dockingPhone) {
                    if (!value) {
                        return Promise.reject('请输入对接人')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validateDepartment = {
            required: true,
            trigger: 'blur',
            type: 'string',
            validator: (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请输入对接部门')
                }
                let formDataItem = formData.value?.hrDocking[rule.field.split('.')[1]]
                if (formDataItem?.docking || formDataItem?.dockingPhone) {
                    if (!value) {
                        return Promise.reject('请输入对接部门')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }

        return {
            formValidateOptional,
            confirm,
            cancel,
            resetData,
            backConfirm,
            customerAddConfirm,
            deleDomain,

            rules,
            formData,
            myOptions,
            serviceChargeOptions,
            calculationOptions,
            agreementArchiveStatusList,
            formInlineAgreement,
            //校验
            onePhoneCheck,
            validateDocking,
            validateDepartment,
            serviceChargeChange,
            calculationFormat,
            validateServiceCharge,
            validateTaxItem,

            // 方法
            addDomain,
            changeClientId,
            //ref
            refImportFile,

            getPopupContainer: (triggerNode) => {
                return triggerNode.parentNode
            },
        }
    },
})
</script>
<style scoped lang="less">
.linefeed {
    width: 100%;
    padding: 0;
    margin: 0;
}

.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;

    :deep(.ant-form-item) {
        width: 33%;

        .ant-form-item-control {
            width: calc(100% - 130px) !important;
        }
    }

    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }

    .hide {
        :deep(.ant-col.ant-form-item-label) {
            label {
                color: rgba(0, 0, 0, 0) !important;
            }
        }
    }

    :deep(.required .ant-form-item-label label::before) {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
    }
}

.dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 4px;
    font-size: 24px;
    color: #999;
    transition: all 0.3s;

    color: @dangerous-color;
}

.dynamic-delete-button:hover {
    color: @dangerous-color;
}

.dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}

.addhrDocking {
    width: calc(99% - 130px);
    margin: 0 0 30px 130px;
    float: right;
}

.charge_item {
    width: 100%;
    display: flex;

    :deep(.ant-select) {
        width: 130px;
    }

    .symbol {
        font-size: 16px;
        display: flex;
        align-items: center;
        margin: 0 10px;
    }
}
</style>
