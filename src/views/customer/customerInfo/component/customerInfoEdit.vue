<template>
    <div>
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules" class="form-flex">
            <template v-for="(item, index) in myOptions" :key="item.name + 'customerInfoEdit'">
                <MyFormItem
                    v-if="!item.external"
                    :width="item.width"
                    :item="item"
                    v-model:value="formData[item.name]"
                    :disabled="viewType == 'see'"
                >
                    <template #parentId>
                        <ClientSelectTree
                            v-model:value="formData[item.name]"
                            :disabled="viewType == 'see'"
                            v-model:itemForm="myOptions[index]"
                            :renderInBody="true"
                        />
                    </template>
                </MyFormItem>
                <div v-else-if="item.name == 'createType'" style="width: 100%">
                    <FormItem :label="item.label" :name="item.name" style="width: 100%" v-if="viewType == 'add'">
                        <RadioGroup name="radioGroup" v-model:value="formData[item.name]">
                            <Radio
                                v-for="radio in parentProtocolList"
                                :key="radio.value"
                                :value="radio.value"
                                v-show="radio.show"
                                @change="item.onChange(radio)"
                                >{{ radio.label }}</Radio
                            >
                        </RadioGroup>
                    </FormItem>
                </div>
            </template>
        </Form>
        <div class="ant-modal-footer" v-show="viewType != 'see'" :key="Math.random()">
            <Button key="back" @click="cancel">取消</Button>
            <!-- formData.value.createType -->
            <template v-if="nextShow">
                <Button key="submit" type="primary" @click="nextConfirm">下一步</Button>
                <!-- <Button key="submit" type="primary" @click="confirm()">跳过,直接创建</Button> -->
            </template>
            <template v-else>
                <Button key="submit" type="primary" @click="confirm()">保存</Button>
            </template>
        </div>
        <ParentAgreementList
            :visible="parentAgreementVisible"
            :options="parentProtocolOptions"
            :currentId="formData.agreementId"
            @confirm="selectParentAgreement"
            @cancel="parentAgreementCancel"
        />
    </div>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import customerUserStore from '/@/store/modules/customer'

import { customerTypeOptions } from '/@/utils/dictionaries'
import { truncateSync } from 'fs'
import { valuesAndRules } from '/#/component'
import moment, { Moment } from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import ParentAgreementList from './parentAgreementList.vue'

export default defineComponent({
    name: 'CustomerInfo',
    props: {
        viewType: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
        myTotal: String,
    },
    components: { ParentAgreementList },
    emits: ['confirm', 'nextConfirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        // const rules: Array<Object> = []
        //后期统一管理

        const options = ref<LabelValueOptions>([])

        const enterpriseOptions = ref<LabelValueOptions>([])
        const industryOptions = ref<LabelValueOptions>([])
        const selectclientsOptions = ref<LabelValueOptions>([])
        const socialSecurityTypeOptions = ref<LabelValueOptions>([])
        const providentFundTypeOptions = ref<LabelValueOptions>([])
        const specializedOptions = ref<LabelValueOptions>([])

        const socialSecurityAccountOptions = ref<LabelValueOptions>([])
        const medicalInsuranceAccountOptions = ref<LabelValueOptions>([])
        const providentFundAccountOptions = ref<LabelValueOptions>([])
        const payrollAccountOptions = ref<LabelValueOptions>([])

        const businessTypeOptions = ref<LabelValueOptions>([])

        const parentProtocolOptions = ref<LabelValueOptions>([])

        const serviceFeeType = ref<LabelValueOptions>([])
        const nextShow = ref(false)
        const parentAgreementVisible = ref(false)
        onMounted(() => {
            // 企业性质
            // request.get('/api/com-code-tables/getCodeTableByInnerName/enterprise', {}).then((res) => {
            //     enterpriseOptions.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            //     console.log(enterpriseOptions.value)
            // })
            // 行业类别
            // request.get('/api/com-code-tables/getCodeTableByInnerName/', {}).then((res) => {
            //     industryOptions.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            // 企业性质
            dictionaryDataStore()
                .setDictionaryData('enterprise', '')
                .then((res: LabelValueOptions) => {
                    enterpriseOptions.value = res
                })
            // 行业类别
            dictionaryDataStore()
                .setDictionaryData('industry', '')
                .then((res: LabelValueOptions) => {
                    industryOptions.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('agreementChargeType', '')
                .then((res: LabelValueOptions) => {
                    serviceFeeType.value = res
                })
            // 上级客户
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id, ...item }
            //     })
            // })
            // 客户层级
            options.value = [
                { label: '一级', value: 0 },
                { label: '多级', value: 1 },
            ]

            //社保类型
            customerUserStore()
                .setSocialSecurityTypeOptions()
                .then((item: LabelValueOptions) => {
                    socialSecurityTypeOptions.value = item
                })

            //公积金类型
            customerUserStore()
                .setProvidentFundTypeOptions()
                .then((item: LabelValueOptions) => {
                    providentFundTypeOptions.value = item
                })

            //缴纳账号
            customerUserStore()
                .setPlatformAccountOptions()
                .then((item: inObject) => {
                    socialSecurityAccountOptions.value = item.socialSecurityAccountOptions
                    medicalInsuranceAccountOptions.value = item.medicalInsuranceAccountOptions
                    providentFundAccountOptions.value = item.providentFundAccountOptions
                    payrollAccountOptions.value = item.payrollAccountOptions
                })
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
            // 业务类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/businessType', {}).then((res) => {
                businessTypeOptions.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        // const rules: Array<Object> = []
        //最新协议
        const hrProtocolDTO = ref<inObject>({})
        const hrDockingDTO = ref<inObject[]>([])

        //专管员
        const specializedIdChange = (value: string | number) => {
            let specializedItem = specializedOptions.value.find((item) => {
                return item.value == value
            })
            formData.value.specializedphone = specializedItem?.phone
        }

        //客户层级
        const parentIdDisabled = ref<boolean>(false)
        const hierarchyChange = (value: string | number) => {
            if (value == 0) {
                formData.value.parentId = null
                parentIdDisabled.value = true
                rules.parentId.required = false
                showParentProtocol2.value = false
                if (formData.value.createType == 2) {
                    formData.value.createType = 1
                }
                parentProtocolOptions.value == []
            } else {
                parentIdDisabled.value = false
                rules.parentId.required = true
                showParentProtocol2.value = true
            }
            createTypeChange()
            formValidateOptional(['parentId'])
        }

        //上级客户修改查上级协议
        const parentIdChange = (value: string | number, option: inObject) => {
            if (item.value.clientId == value) {
                message.error('上级客户不能选择当前客户！')
                formData.value.parentId = undefined
            }
            request.get('/api/hr-clients/selectProtocolIds', { id: value }).then((res) => {
                parentProtocolOptions.value = res.map((item) => {
                    return { label: item.agreementTitle, value: item.id, ...item }
                })
            })
        }

        //协议来源修改
        const createTypeChange = (createType?) => {
            formData.value.agreementId = null
            formData.value.clientId = null
            if (createType?.value == 2) {
                agreementIdShow.value = true
                parentAgreementVisible.value = true
            } else {
                parentAgreementVisible.value = false
                agreementIdShow.value = false
                // formData.value.agreementId = null
                // formData.value.clientId = null
            }
            if (createType?.value == 3) {
                nextShow.value = true
            } else {
                nextShow.value = false
            }
        }

        const showParentProtocol2 = ref(false)
        //协议来源
        const parentProtocolList = ref([
            {
                value: 1,
                label: '无协议',
                show: true,
            },
            {
                value: 2,
                label: '使用上级协议',
                show: showParentProtocol2,
            },
            {
                value: 3,
                label: '添加并使用新的协议',
                show: true,
            },
        ])
        const agreementIdShow = ref(false)
        const userNameDisabled = ref(false)
        const isViewTypeAdd = ref(true)

        //是否发薪
        const validClientPay = ref<Boolean>(true)
        // 修改是否发薪
        const changeClientPay = (item) => {
            console.log(item)
            if (item == true) {
                validClientPay.value = true
            } else {
                validClientPay.value = false
            }
        }
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '单位编号',
                name: 'unitNumber',
            },
            {
                label: '用户名',
                name: 'userName',
                disabled: userNameDisabled,
            },

            {
                label: '客户名称',
                name: 'clientName',
            },
            {
                label: '企业性质',
                name: 'enterpriseNature',
                type: 'change',
                options: enterpriseOptions,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '客户层级',
                name: 'hierarchy',
                type: 'change',
                ruleType: 'number',
                options: options,

                onChange: hierarchyChange,
                default: 0,
            },
            {
                label: '上级客户',
                name: 'parentId',
                type: 'slots',
                slots: 'parentId',
                // options: selectclientsOptions,

                disabled: parentIdDisabled,
                showbr: true,
                onChange: parentIdChange,
            },

            {
                label: '注册资本',
                name: 'registeredCapital',
                required: false,
            },
            {
                label: '成立日期',
                name: 'establishedDate',
                type: 'date',
                required: false,
                disabledDate: (current: Moment) => {
                    return current && current > moment().endOf('day')
                },
            },
            {
                label: '行业类别',
                name: 'industry',
                required: false,
                type: 'change',
                options: industryOptions,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '办公地址',
                name: 'address',
                width: '100%',
                required: false,
            },
            {
                label: '社保缴纳账户',
                name: 'socialSecurityAccountId',
                required: false,
                type: 'change',
                options: socialSecurityAccountOptions,

                show: isViewTypeAdd,
            },
            {
                label: '医保缴纳账户',
                name: 'medicalInsuranceAccountId',
                required: false,
                type: 'change',
                options: medicalInsuranceAccountOptions,

                show: isViewTypeAdd,
            },
            {
                label: '公积金缴纳账户',
                name: 'providentFundAccountId',
                required: false,
                type: 'change',
                options: providentFundAccountOptions,

                show: isViewTypeAdd,
            },
            {
                label: '社保类型',
                name: 'socialSecurityTypeId',
                required: true,
                type: 'change',
                options: socialSecurityTypeOptions,

                show: isViewTypeAdd,
            },
            {
                label: '公积金类型',
                name: 'providentFundTypeId',
                required: false,
                type: 'change',
                options: providentFundTypeOptions,

                show: isViewTypeAdd,
            },
            {
                label: '工资发放账户',
                name: 'payrollAccountId',
                required: false,
                type: 'change',
                options: payrollAccountOptions,

                show: isViewTypeAdd,
            },
            {
                label: '业务类型',
                name: 'businessType',
                required: true,
                type: 'change',
                options: businessTypeOptions,
                trigger: 'change',
                ruleType: 'number',
            },

            {
                label: '是否发薪',
                name: 'clientPay',
                ruleType: 'boolean',
                type: 'isTrue',
                default: true,
                onChange: changeClientPay,
            },
            {
                label: '工资发放日',
                name: 'payDate',
                showbr: true,
                type: 'number',
                ruleType: 'number',
                max: 31,
                min: 1,
                show: validClientPay,
                unit: '日',
            },

            {
                label: '协议来源',
                name: 'createType',
                external: true,
                default: 1,
                onChange: createTypeChange,
                ruleType: 'number',
            },
            {
                label: '使用协议',
                name: 'agreementId',
                required: true,
                type: 'change',
                options: parentProtocolOptions,
                trigger: 'change',
                disabled: true,
                showbr: true,
                show: agreementIdShow,
            },

            {
                label: '专管员',
                name: 'specializedId',
                required: false,
                type: 'change',
                options: specializedOptions,

                onChange: specializedIdChange,
            },
            {
                label: '专管员联系电话',
                name: 'specializedphone',
                required: false,
                disabled: true,
                showbr: true,
            },
            {
                label: '最新协议',
                name: 'hrProtocolDTO1',
                required: false,
                external: true,
            },
            {
                label: '备注',
                name: 'remarks',
                width: '100%',
                required: false,
                type: 'textarea',
            },
        ])
        //请求
        const { viewType, item, visible, myTotal } = toRefs<any>(props)
        // Form 实例
        const formInline = ref(null) as any
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate(nameList)
            })
        }
        // FormData rules 初始值
        const { values: initFormData, rules }: any = getValuesAndRules(myOptions.value)

        // Form Data
        const formData = ref<any>(initFormData)
        const makeid = () => {
            let makeidText = ''
            let possible = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz0123456789'
            for (let i = 0; i < 4; i++) makeidText += possible.charAt(Math.floor(Math.random() * possible.length))
            return makeidText
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    userNameDisabled.value = viewType.value != 'add'
                    isViewTypeAdd.value = viewType.value == 'add'
                    if (item.value?.id) {
                        request.get(`/api/hr-clients/selectClients`, { id: item.value.id }).then((res) => {
                            let newFormData = Object.assign({}, initFormData, res.hrClientDTO[0])
                            //存放状态管理
                            customerUserStore().setCustomerInfo(newFormData)

                            formData.value = newFormData
                            hrDockingDTO.value = res.hrDockingDTO || []
                            hrProtocolDTO.value = res.hrProtocolDTO[0] || {}
                            if (formData.value.parentId == 0) {
                                formData.value.hierarchy = 0
                            } else {
                                formData.value.hierarchy = 1
                            }
                            hierarchyChange(formData.value.hierarchy)
                            console.log(formData.value.clientPay)
                            if (formData.value.clientPay == true) {
                                //是否发薪
                                validClientPay.value = true
                            } else {
                                validClientPay.value = false
                            }
                        })
                    } else {
                        formData.value = initFormData
                        hierarchyChange(formData.value.hierarchy)

                        formData.value.unitNumber = 'KH' + new Date().getTime()

                        console.log(myTotal.value.length)
                        formData.value.userName = 'kh_' + makeid()
                        // if (myTotal.value.length == 1) {
                        //     formData.value.userName = 'kehu' + '00' + myTotal.value
                        // } else if (myTotal.value.length == 2) {
                        //     formData.value.userName = 'kehu' + '0' + myTotal.value
                        // } else {
                        //     formData.value.userName = 'kehu' + myTotal.value
                        // }
                    }
                }
            },
            { immediate: true },
        )

        // confirm handle
        const confirm = (messageShow?: boolean) => {
            return new Promise(async (resolve, reject) => {
                formInline.value
                    .validate()
                    .then(async () => {
                        let requestInfo: any = null
                        //update
                        if (viewType.value == 'add') {
                            requestInfo = request.post('/api/hr-clients/index', { ...formData.value, password: 'c123456' })
                        } else {
                            requestInfo = request.post('/api/hr-clients/update', formData.value)
                        }
                        requestInfo
                            .then((ref: any) => {
                                resolve(ref)
                                dictionaryDataStore().setSelectclients('clients', true)
                                console.log(messageShow)
                                if (!messageShow) {
                                    console.log(11)
                                    emit('confirm')
                                    if (viewType.value == 'add') {
                                        message.success('新增成功!')
                                    } else {
                                        message.success('编辑成功!')
                                    }
                                }
                            })
                            .catch((ref) => {
                                reject(ref)
                            })
                    })
                    .catch((error) => {
                        // message.success('表单验证失败!')
                        reject('表单验证失败')
                        console.log('表单验证失败', error)
                    })
            })
        }
        const nextConfirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    emit('nextConfirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const cancel = () => {
            resetData()
            emit('cancel')
        }

        const parentAgreementCancel = () => {
            parentAgreementVisible.value = false
            formData.value.createType = 1
        }

        const selectParentAgreement = (selectItem) => {
            formData.value.agreementId = selectItem.id
            parentAgreementVisible.value = false
        }

        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        return {
            confirm,
            cancel,
            resetData,
            nextConfirm,

            rules,
            formData,
            myOptions,
            hrProtocolDTO,
            hrDockingDTO,
            // ref
            formInline,
            //customerTypeOptions
            customerTypeOptions,
            changeClientPay,
            serviceFeeType,
            //协议来源
            parentProtocolList,
            //下一步显示
            nextShow,
            parentAgreementVisible,
            parentProtocolOptions,
            parentAgreementCancel,
            selectParentAgreement,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        // .ant-form-item-control {
        //     max-width: calc(100% - 130px) !important;
        // }
    }
    .hrProtocolDTO {
        width: 100%;
        margin-bottom: 20px;
        .title {
            width: 130px;
            text-align: right;
            font-size: 18px;
            font-weight: 600;
        }
        .content {
            display: flex;
            flex-wrap: wrap;
            padding-left: 130px;
            .hide {
                color: rgba(0, 0, 0, 0) !important;
            }
            & > p {
                color: #999;
                width: 20%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                span {
                    padding-left: 5px;
                    color: #333;
                }
            }
        }
    }
}
</style>
