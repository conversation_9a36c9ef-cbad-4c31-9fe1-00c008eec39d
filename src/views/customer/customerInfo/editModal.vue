<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <RadioGroup class="RadioGroup" :default-value="tabs" @change="tabsChange">
            <RadioButton :value="'tab1'">基本信息</RadioButton>
            <RadioButton :value="'tab2'">薪酬配置</RadioButton>
            <RadioButton :value="'tab3'">协议信息</RadioButton>
        </RadioGroup>
        <CustomerInfo
            v-show="tabs == 'tab1'"
            :viewType="viewType"
            :visible="visible"
            :item="item"
            @cancel="cancel"
            @confirm="confirm"
            @nextConfirm="nextConfirm"
            ref="refAddCustomerInfo"
        />
        <SalaryParam
            v-show="tabs == 'tab2'"
            :viewType="viewType"
            :visible="visible"
            :item="item"
            @cancel="cancel"
            @confirm="confirm"
        />
        <ProtocolInfo v-show="tabs == 'tab3'" :viewType="viewType" ref="refProtocoInfo" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import addStaffBasic from './addStaffBasic.vue'
import CustomerInfo from './component/customerInfoEdit.vue'
// import Agreement from './component/agreement.vue'

import ProtocolInfo from './component/protocolInfo.vue'
import SalaryParam from './component/salaryParam.vue'
export default defineComponent({
    name: 'EditModal',
    components: {
        // AddStaffBasic: addStaffBasic,
        CustomerInfo,
        // Agreement,
        ProtocolInfo,
        SalaryParam,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: 'edit',
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs<any>(props)
        // Form 实例
        const refAddCustomerInfo = ref(null) as any
        const refAddCustomerProtoco = ref(null) as any

        const tabs = ref('tab1')
        // tab切换
        const tabsChange = (value) => {
            tabs.value = value.target.value
        }

        const cancel = () => {
            refAddCustomerInfo.value.resetData()
            tabs.value = 'tab1'
            emit('cancel')
        }

        const confirm = () => {
            cancel()
            emit('confirm')
        }
        const nextConfirm = () => {}
        const backConfirm = () => {}

        return {
            // 按钮
            confirm,
            cancel,
            nextConfirm,
            backConfirm,

            //refs实例
            refAddCustomerInfo,
            refAddCustomerProtoco,

            tabs,
            tabsChange,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
<style scoped lang="less">
.RadioGroup {
    margin: 0 24px 24px;
}
</style>
