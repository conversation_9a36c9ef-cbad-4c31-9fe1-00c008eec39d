<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'customerInfo_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'customerInfo_import'" @click="importData">导入</Button>
        <Button type="primary" v-auth="'customerInfo_export'" @click="exportData">
            {{ exportText }}
        </Button>
        <Button type="primary" v-auth="'customerInfo_delete'" danger @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-clients/page"
        deleteApi="/api/hr-clients/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="displayColumns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            <template v-if="record.customerType == 3">
                &nbsp;
                <Button type="primary" class="yellow" size="small" @click="recoveryProtocolRow(record)">恢复</Button>
            </template>
            <template v-if="record.customerType == 2">
                &nbsp;
                <Button type="primary" class="green" size="small" @click="terminationProtocolRow(record)">终止</Button>
            </template>
            &nbsp;
            <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
        <template #peoplesum="{ record }">
            <Button v-if="record.peoplesum" type="link" size="small" @click="goToStaffList(record)">
                {{ record.peoplesum }}
            </Button>
            <span v-else>{{ record.peoplesum }}</span>
        </template>
    </BasicTable>

    <AddModal :visible="showAdd" :title="modalTitle" :myTotal="myTotal" @cancel="modalCancel" @confirm="modalConfirm" />
    <EditModal
        :visible="showEdit"
        :viewType="editViewType"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <!-- <RecoveryProtocol
        :visible="showRecoveryProtocol"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    /> -->
    <!-- <TerminationProtocol
        :visible="showTerminationProtocol"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    /> -->
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="importComplete" />
    <ProtocolList
        ref="refProtocolList"
        :title="otherModalTitle"
        :visible="protocolListVisible"
        :item="currentValue"
        @cancel="otherModalCancel"
        @confirm="otherModalConfirm"
    />
    <CustomerAgreement
        :title="otherModalTitle"
        :visible="agreementVisible"
        :item="agreementValue"
        @cancel="otherModalCancel"
        @confirm="otherModalConfirm"
        isEditAdd
    />
</template>

<script lang="ts">
// import { Modal, Tag } from 'ant-design-vue'
import { defineComponent, ref, onMounted, watch, computed, watchEffect } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import AddModal from './addModal.vue'
import EditModal from './editModal.vue'
// import RecoveryProtocol from '../customerAgreement/recoveryProtocol.vue'
// import TerminationProtocol from '../customerAgreement/terminationProtocol.vue'
import ProtocolList from './component/protocolList.vue'
import CustomerAgreement from '../customerAgreement/editCustomer.vue'

import { customerTypeOptions } from '/@/utils/dictionaries'
import customerUserStore from '/@/store/modules/customer'
import { useRoute, useRouter } from 'vue-router'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, getDynamicText, SectionToChinese } from '/@/utils'
export default defineComponent({
    name: 'CustomerInfo',
    components: { AddModal, EditModal, ProtocolList, CustomerAgreement },
    setup() {
        const router = useRouter()
        const route = useRoute()
        const viewType = computed(() => {
            return customerUserStore().getViewType
        })
        const protocolListVisible = ref(false)
        const agreementVisible = ref(false)
        const otherModalTitle = ref<string>('')
        //筛选
        // let selectclientsOptions = ref<LabelValueOptions>([])
        let businessTypeOptions = ref<LabelValueOptions>([])
        let enterpriseOptions = ref<LabelValueOptions>([])
        let specializedOptions = ref<LabelValueOptions>([])

        const hierarchicalFilteringList = computed(() => {
            let newHierarchicalFilteringList: LabelValueOptions = []
            for (let i = 1; i <= dictionaryDataStore().dictionaryTreeDataMaxlevel; i++) {
                newHierarchicalFilteringList.push({ label: SectionToChinese(i) + '级', value: i })
            }
            return newHierarchicalFilteringList
        })
        onMounted(() => {
            // 业务类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/businessType', {}).then((res) => {
                businessTypeOptions.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 企业性质
            request.get('/api/com-code-tables/getCodeTableByInnerName/enterprise', {}).then((res) => {
                enterpriseOptions.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
        })
        const params = ref({ idList: route.params?.clientId || undefined })
        const tableData = ref([])
        watch(
            tableData,
            (newV) => {
                calcDynamicCols(newV)
            },
            { deep: true },
        )
        const calcDynamicCols = (data) => {
            const maxLevel = Math.max(...data.map((el) => el.hrClientList?.length || 0))
            let dynamicArr: any = []
            for (let i = 0; i < maxLevel; i++) {
                dynamicArr.push({
                    title: `${SectionToChinese(i + 1)}级客户`,
                    dataIndex: 'hrClientList',
                    align: 'center',
                    width: 155,
                    customRender: ({ record }) => {
                        return (
                            record?.hrClientList?.find((item) => {
                                return i + 1 == item.level
                            })?.clientName || ''
                        )
                    },
                    sorter: false,
                })
            }
            displayColumns.value = [...columns.slice(0, 2), ...dynamicArr, ...columns.slice(2)]
        }
        // 多选
        const selectedRowsArr = ref([])

        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '单位编号',
                key: 'unitNumber',
            },
            {
                type: 'clientSelectTree',
                label: '客户名称',
                key: 'idList',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'select',
                label: '客户类型',
                key: 'customerType',
                options: customerTypeOptions,
            },
            {
                type: 'select',
                label: '协议类型',
                key: 'agreementTypeList',
                options: businessTypeOptions,
                multiple: true,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'specializedIdList',
                options: specializedOptions,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '协议开始日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '协议结束日期',
                key: 'contractEndDateQuery',
            },
            {
                type: 'select',
                label: '客户层级',
                key: 'levelList',
                options: hierarchicalFilteringList,
                multiple: true,
            },
        ]

        watch(viewType, (val) => {
            if (val) {
                if (val == 'add') {
                    otherModalTitle.value = '新增协议'
                    agreementVisible.value = true
                    agreementValue.value = JSON.parse(JSON.stringify(currentValue.value))
                    agreementValue.value.clientId = agreementValue.value.id
                    delete agreementValue.value.id
                } else {
                    protocolListVisible.value = true
                    agreementVisible.value = false
                    if (val == 'look') otherModalTitle.value = '查看历史协议'
                    if (val == 'edit') otherModalTitle.value = '修改客户协议'
                }
            } else {
                agreementVisible.value = false
                protocolListVisible.value = false
            }
        })

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '单位编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 155,
            },
            {
                title: '用户名',
                dataIndex: 'userName',
                align: 'center',
                width: 155,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 155,
            },
            {
                title: '客户类型',
                dataIndex: 'customerType',
                align: 'center',
                customRender: ({ text }) => {
                    return customerTypeOptions.find((item) => {
                        return text == item.value
                    })?.label
                },
                sorter: false,
                width: 155,
            },
            {
                title: '当前协议编号',
                dataIndex: 'agreementNumber',
                align: 'center',
                width: 155,
            },
            {
                title: '协议类型',
                dataIndex: 'agreementTypekey',
                align: 'center',
                width: 155,
            },
            /* {
                title: '企业性质',
                dataIndex: 'enterpriseNature',
                align: 'center',
                customRender: ({ record }) => {
                    return record.enterpriseNaturekey
                },
                width: 155,
            }, */
            {
                title: '协议开始日期',
                dataIndex: 'agreementStartDate',
                align: 'center',
                width: 155,
            },
            {
                title: '协议结束日期',
                dataIndex: 'agreementEndDate',
                align: 'center',
                width: 155,
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                sorter: false,
                width: 155,
            },
            {
                title: '员工数量',
                dataIndex: 'peoplesum',
                align: 'center',
                slots: { customRender: 'peoplesum' },
                width: 155,
                sorter: false,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 230,
                align: 'center',
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]

        const displayColumns = ref<any>([...columns])

        const showEdit = ref(false) //编辑
        const showAdd = ref(false) //新增
        const modalTitle = ref('新增客户') //弹窗名称
        // 当前编辑的数据
        const currentValue = ref(undefined) //编辑的数据
        const agreementValue = ref<any>(undefined) //协议的数据
        const myTotal = ref('')
        const createRow = () => {
            let pageTotal = tableRef.value.pagination.total + 1
            myTotal.value = pageTotal.toString()
            showAdd.value = true
            modalTitle.value = '新增客户'
        }
        const editViewType = ref('')
        const editRow = (record, type) => {
            editViewType.value = type
            modalTitle.value = `${type == 'edit' ? '编辑' : '查看'}客户`
            currentValue.value = { ...record }
            customerUserStore().fetchCustomerProtocol(record, true)
            showEdit.value = true
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
                dictionaryDataStore().setSelectclients('clients', true)
            })
        }
        const otherModalCancel = (flag = false) => {
            if (!flag) {
                customerUserStore().setViewType('edit')
                otherModalTitle.value = '修改客户协议'
            } else {
                customerUserStore().setViewType('')
                otherModalTitle.value = ''
            }
        }
        const modalCancel = () => {
            // showTerminationProtocol.value = false
            // showRecoveryProtocol.value = false
            showAdd.value = false
            showEdit.value = false
            currentValue.value = undefined
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增') || otherModalTitle.value.includes('新增')) {
                console.log(11)
                tableRef.value.refresh(1)
            } else {
                console.log(11)
                tableRef.value.refresh()
            }
            dictionaryDataStore().setSelectclients('clients', true)
        }

        const otherModalConfirm = async (flag = false) => {
            otherModalCancel(flag)
            tableRef.value.refresh()
        }

        const goToStaffList = (record) => {
            if (record.peoplesum) {
                router.push({
                    name: 'staffList',
                    params: { clientId: record?.clientIdList ? record.clientIdList : [] },
                })
            }
        }
        const importComplete = () => {
            searchData()
            dictionaryDataStore().setSelectclients('clients', true)
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'customerInfo_edit',
                    show: true,
                    click: (record) => editRow(record, 'edit'),
                },
                {
                    // customerInfo_see
                    neme: '查看',
                    auth: 'customerInfo_see',
                    show: true,
                    click: (record) => editRow(record, 'see'),
                },
                // {
                //     neme: '恢复',
                //     auth: 'customerInfo_recover',
                //     show: (record) => {
                //         return record.customerType == 1
                //     },
                //     click: recoveryProtocolRow,
                // },
                // {
                //     neme: '终止',
                //     auth: 'customerInfo_termination',
                //     show: (record) => {
                //         return record.customerType == 2
                //     },
                //     click: terminationProtocolRow,
                // },
                // {
                //     neme: '删除',
                //     auth: 'customerInfo_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        // const myOperationClick = (item, record) => {
        //     switch (item.auth) {
        //         case 'customerInfo_edit':
        //             editRow(record)
        //             break
        //         case 'customerInfo_recover':
        //             recoveryProtocolRow(record)
        //             break
        //         case 'customerInfo_termination':
        //             terminationProtocolRow(record)
        //             break
        //         case 'customerInfo_delete':
        //             deleteRow(record)
        //             break
        //     }
        // }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-clients/template'
        const importUrl = '/api/hr-clients/import'
        const exportUrl = '/api/hr-clients/export'
        const importData = () => {
            importVisible.value = true
        }
        const exportData = () => {
            tableRef.value.exportRow('clientIds', exportText.value, params.value)
        }
        // <Button type="primary" @click="importData">导入</Button>
        // <Button type="primary" @click="exportData">导出</Button>
        return {
            params,
            options,

            modalCancel,
            modalConfirm,
            otherModalCancel,
            otherModalConfirm,

            showEdit,
            showAdd,
            // showRecoveryProtocol,
            // showTerminationProtocol,
            modalTitle,
            otherModalTitle,
            protocolListVisible,
            agreementVisible,
            agreementValue,
            currentValue,

            columns,

            searchData,
            importComplete,
            tableRef,
            createRow,
            editRow,
            // recoveryProtocolRow,
            // terminationProtocolRow,
            deleteRow,
            goToStaffList,
            myTotal,

            //事件

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            importData,
            exportData,
            exportUrl,

            //操作
            myOperation,
            // myOperationClick,
            editViewType,
            selectedRowsArr,
            exportText,
            tableData,
            displayColumns,
        }
    },
})
</script>

<style scoped lang="less"></style>
