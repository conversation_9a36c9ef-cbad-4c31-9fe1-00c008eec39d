<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <CustomerInfo
            v-show="customerInfoShow == 1"
            viewType="add"
            :visible="visible"
            :item="{}"
            :myTotal="myTotal"
            @cancel="cancel"
            @confirm="confirm"
            @nextConfirm="nextConfirm"
            ref="refAddCustomerInfo"
        />
        <Agreement
            v-show="customerInfoShow == 2"
            viewType="add"
            :visible="visible"
            :item="{}"
            @cancel="cancel"
            @confirm="confirm"
            @customerAddConfirm="customerAddConfirm"
            @backConfirm="backConfirm"
            ref="refAgreement"
        />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent } from 'vue'
import CustomerInfo from './component/customerInfoEdit.vue'
import Agreement from './component/agreement.vue'
export default defineComponent({
    name: 'AddModal',
    components: {
        // AddStaffBasic: addStaffBasic,
        CustomerInfo,
        Agreement,
    },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        myTotal: String,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        // Form 实例
        const refAddCustomerInfo = ref(null) as any
        const refAgreement = ref(null) as any

        const customerInfoShow = ref(1)

        const cancel = () => {
            refAddCustomerInfo.value.resetData()
            refAgreement.value.resetData()
            emit('cancel')
            customerInfoShow.value = 1
        }

        const confirm = () => {
            cancel()
            emit('confirm')
        }
        const customerAddConfirm = () => {
            refAddCustomerInfo.value.confirm(true).then((ref: any) => {
                console.log(ref.id)
                refAgreement.value.confirm({ clientId: ref.id })
            })
        }
        const nextConfirm = (data) => {
            customerInfoShow.value = 2
            refAgreement.value?.changeClientId('', { label: data?.clientName || '' })
        }
        const backConfirm = () => {
            customerInfoShow.value = 1
        }

        return {
            // 按钮
            confirm,
            cancel,
            nextConfirm,
            backConfirm,
            customerAddConfirm,

            customerInfoShow,
            //refs实例
            refAddCustomerInfo,
            refAgreement,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
