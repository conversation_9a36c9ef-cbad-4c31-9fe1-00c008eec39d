<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="870px">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules" class="form-flex">
            <template v-for="(itemForm, index) in myOptions" :key="index">
                <MyFormItem
                    v-if="!itemForm.external"
                    :width="itemForm.width"
                    :item="itemForm"
                    v-model:value="formData[itemForm.name]"
                    :class="itemForm.slots"
                />
                <template v-else-if="itemForm.name == 'appendixId'">
                    <FormItem :label="'附件'" :name="itemForm.name" style="width: 99%">
                        <ImportFile v-model:fileUrls="formData[itemForm.name]" ref="refImportFile" />
                    </FormItem>
                </template>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'AllotMenu',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const { item, visible } = toRefs(props)

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '协议编号',
                name: 'agreementNumber',
                disabled: true,
                required: false,
            },
            {
                label: '客户名称',
                name: 'clientName',
                disabled: true,
                showbr: true,
                required: false,
            },
            {
                label: '协议标题',
                name: 'agreementTitle',
                width: '100%',
                disabled: true,
                showbr: true,
                required: false,
            },
            {
                label: '协议终止日期',
                name: 'agreementTerminationDate',
                type: 'date',
                showbr: true,
            },
            {
                label: '协议终止原因',
                name: 'reasonTerminationAgreement',
                width: '100%',
                type: 'textarea',
            },
            {
                label: '附件',
                name: 'appendixId',
                ruleType: 'array',
                required: false,
                external: true,
                default: [],
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                console.log()
                formData.value = Object.assign({}, initFormData, {
                    id: item.value?.id,
                    clientId: item.value?.clientId,
                    agreementNumber: item.value?.agreementNumber,
                    clientName: item.value?.clientName,
                    agreementTitle: item.value?.agreementTitle,
                })
            }
        })
        const refImportFile = ref()

        // confirm handle
        const confirm = () => {
            let appendixId = refImportFile.value
                .getFileUrls()
                .map((item) => {
                    return item.id
                })
                ?.join()
            formInline.value
                .validate()
                .then(async () => {
                    let mas = await request.put('/api/hr-protocols/update', { ...formData.value, appendixId: appendixId })
                    message.success(mas)
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            console.log(11)
            emit('cancel')
            resetFormData()
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            refImportFile,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 4%;
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
}
</style>
