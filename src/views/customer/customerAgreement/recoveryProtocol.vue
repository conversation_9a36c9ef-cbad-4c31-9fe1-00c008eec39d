<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <Agreement viewType="recovery" :visible="visible" :item="item" @cancel="cancel" @confirm="confirm" ref="refAgreement" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent } from 'vue'
import Agreement from '../customerInfo/component/agreement.vue'
export default defineComponent({
    name: 'RecoveryProtocol',
    components: {
        Agreement,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        // Form 实例
        const refAgreement = ref(null) as any

        const cancel = () => {
            refAgreement.value.resetData()
            emit('cancel')
        }

        const confirm = () => {
            cancel()
            emit('confirm')
        }

        return {
            // 按钮
            confirm,
            cancel,
            //refs实例
            refAgreement,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
