<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" width="1200px">
        <div class="importFileBox">
            <ImportFile v-model:fileUrls="fileUrls" ref="refImportFile" />
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { defineProps, defineEmits, watch, toRefs } from 'vue'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'
const props = defineProps({
    item: {
        type: Object,
    },
    visible: {
        type: Boolean,
        default: false,
    },
})
//请求
const { item, visible } = toRefs<any>(props)
const emit = defineEmits(['cancel', 'confirm'])
const refImportFile = ref()
const fileUrls = ref<inObject[]>([])
const getAppendixs = (id) => {
    request.post('/api/hr-protocols/appendixs', [id]).then((res: any[]) => {
        fileUrls.value = res.filter((el) => {
            return el
        })
    })
}
watch(
    visible,
    () => {
        if (visible.value) {
            getAppendixs(item.value.id)
        }
    },
    { immediate: true },
)
const confirm = async () => {
    let appendixId = refImportFile.value.getFileUrls().map((item) => {
        return item.id
    })

    await request.post('/api/hr-protocols/updateHrDocking', {
        id: item.value.id,
        appendixId: appendixId.join(),
        appendixList: null,
    })
    message.success('修改成功!')
    emit('confirm')
    cancel()
}
const cancel = () => {
    emit('cancel')
}
</script>
<style lang="less">
.importFileBox {
    min-height: 60vh;
}
</style>
