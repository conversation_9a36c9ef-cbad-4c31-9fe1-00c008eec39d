<template>
    <SearchBar v-model="params" :options="options" @change="searchData" :showSelectedLine="isShowSelectedLine" />
    <div class="btns">
        <Button type="primary" v-auth="'customerAgreement_import'" @click="importData">导入</Button>
        <Button type="primary" v-auth="'customerAgreement_download'" @click="download()">{{ exportText }}</Button>
        <Button danger type="primary" v-auth="'customerAgreement_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-protocols/page"
        deleteApi="/api/hr-protocols/deletes"
        :params="{ ...params }"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
        :tableDataFormat="tableDataFormat"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
        <template #clientIdSum="{ record }">
            <Button v-if="+record.clientIdSum" type="link" size="small" @click="goToCustomInfo(record)">
                {{ record.clientIdSum }}
            </Button>
            <span v-else>{{ record.clientIdSum }}</span>
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
    <UploadAttachments :visible="showUpload" :title="modalTitle" :item="currentValue" @cancel="modalCancel" />

    <ImportModal
        v-model:visible="importVisible"
        temUrl="/api/hr-protocols/template"
        importUrl="/api/hr-protocols/import"
        @getResData="searchData"
    />
    <TerminationProtocol
        :visible="showTerminationProtocol"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <RecoveryProtocol
        :visible="showRecoveryProtocol"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import { downMultFile } from '/@/utils/downFile'
import request from '/@/utils/request'
import modal from '../editCustomer.vue'
import UploadAttachments from '../uploadAttachments.vue'
import { stateOptions, oldStateOption, agreementArchiveStatusList } from '/@/utils/dictionaries'
import { useRoute, useRouter } from 'vue-router'
import TerminationProtocol from '../terminationProtocol.vue'
import RecoveryProtocol from '../recoveryProtocol.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'UserAdmin',
    components: { MyModal: modal, UploadAttachments, TerminationProtocol, RecoveryProtocol },
    setup() {
        const router = useRouter()
        // 获取全部角色
        let businessTypeOptions = ref<LabelValueOptions>([])
        let settlementMethodOptions = ref<LabelValueOptions>([])
        onMounted(() => {
            // 业务类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/businessType', {}).then((res) => {
                businessTypeOptions.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //结算方式
            request.get('/api/com-code-tables/getCodeTableByInnerName/settlementMethod', {}).then((res) => {
                settlementMethodOptions.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const route = useRoute()
        //筛选  即将过期的协议
        const params = ref(route.query?.type === 'customer_agreement' ? { statesList: [2] } : { statesList: [6] })
        const stateNum = ref()
        const isShowSelectedLine = ref(false)
        const postStateList = () => {
            stateNum.value = params.value.statesList.length
        }
        watch(params, () => {
            postStateList()
            if (stateNum.value == 0) {
                params.value.statesList = [2, 6]
                isShowSelectedLine.value = false
            } else if (stateNum.value == 1) {
                isShowSelectedLine.value = true
            }
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '协议编码',
                key: 'agreementNumber',
            },
            {
                type: 'clientSelectTree',
                label: '所属客户',
                key: 'clientIdList',
                placeholder: '所属客户',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'select',
                label: '协议类型',
                key: 'agreementTypeList',
                options: businessTypeOptions,
                multiple: true,
            },
            {
                type: 'select',
                label: '结算方式',
                key: 'settlementMethodList',
                options: settlementMethodOptions,
                multiple: true,
            },
            {
                type: 'select',
                label: '协议状态',
                key: 'statesList',
                options: oldStateOption,
                multiple: true,
                allowClear: false,
                disabled: true,
            },
            {
                type: 'daterange',
                label: '协议开始日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '协议结束日期',
                key: 'contractEndDateQuery',
            },
            {
                type: 'select',
                label: '归档状态',
                key: 'archiveStatusList',
                options: agreementArchiveStatusList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '归档日期',
                key: 'archiveTimeQuery',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const selectedRowsArr = ref<any>([])
        const tableData = ref<any>([])

        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selectedRowsArr.value)
        })
        //表格数据
        const columns = [
            {
                title: '协议编码',
                dataIndex: 'agreementNumber',
                align: 'center',
                width: 200,
            },
            {
                title: '所属客户',
                dataIndex: 'clientName',
                align: 'center',
                width: 180,
            },
            {
                title: '协议类型',
                dataIndex: 'agreementType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.agreementTypekey
                },
                width: 150,
            },
            {
                title: '结算方式',
                dataIndex: 'settlementMethod',
                align: 'center',
                customRender: ({ record }) => {
                    return record.settlementMethodkey
                },
                width: 100,
            },
            {
                title: '协议开始日期',
                dataIndex: 'agreementStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '协议结束日期',
                dataIndex: 'agreementEndDate',
                align: 'center',
                width: 150,
            },
            {
                title: '协议状态',
                dataIndex: 'states',
                align: 'center',
                customRender: ({ text, record }) => {
                    let myText = ''
                    if (text == 1 || text == 5) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    } else if (text == 2 || text == 6) {
                        if (record.types != 1) {
                            myText = '(未续签)'
                        } else {
                            myText = '(已续签)'
                        }
                    }
                    const str = stateOptions.find((item) => {
                        return text == item.value
                    })?.label as string
                    return str.includes('(') ? str.split('(')[0] + myText : str
                },
                width: 150,
            },
            {
                title: '归档状态',
                dataIndex: 'archiveStatus',
                align: 'center',
                customRender: ({ text, record }) => {
                    const str = agreementArchiveStatusList.find((item) => {
                        return text == item.value
                    })?.label as string
                    return str
                },
                width: 150,
            },
            {
                title: '归档时间',
                dataIndex: 'archiveTime',
                align: 'center',
                width: 150,
            },
            {
                title: '使用数量',
                dataIndex: 'clientIdSum',
                align: 'center',
                sorter: false,
                slots: { customRender: 'clientIdSum' },
                width: 100,
            },

            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        const goToCustomInfo = (record) => {
            if (record.clientIdSum) {
                router.push({
                    name: 'customerInfo',
                    params: { clientId: record.clientIdSumList },
                })
            }
        }

        const showEdit = ref(false)
        const showUpload = ref(false)
        const modalTitle = ref('新增协议')
        // 当前编辑的数据
        const currentValue = ref(undefined)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增协议'
            currentValue.value = undefined
        }
        const editRow = (record, viewType: string) => {
            // edit编辑
            // see查看
            showEdit.value = true
            if (viewType == 'see') {
                modalTitle.value = '查看协议'
            } else if (viewType == 'renew') {
                modalTitle.value = '续签'
            } else {
                modalTitle.value = '编辑协议'
            }
            currentValue.value = { ...record }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        const modalCancel = () => {
            showRecoveryProtocol.value = false
            showUpload.value = false
            showEdit.value = false
            showTerminationProtocol.value = false
            modalTitle.value = '查看协议'
            currentValue.value = undefined
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        // 上传
        const upload = (record) => {
            console.log(11)
            showUpload.value = true
            modalTitle.value = '上传附件'
            currentValue.value = { ...record }
        }
        // 下载
        const download = (record?) => {
            let ids: any[] = []
            let body = {}
            let multFileName = '客户协议附件批量导出'
            if (record) {
                ids = [record.id]
                multFileName = record.clientName + '_协议附件'
                body = { ids: ids }
            } else {
                if (!tableData.value.length) {
                    message.error('未查询到相关数据!')
                    return
                }
                if (exportText.value.indexOf('选中') != -1) {
                    let sameClientName = true
                    let clientName = selectedRowsArr.value[0]?.clientName
                    selectedRowsArr.value.forEach((el: inObject) => {
                        if (el?.clientName != clientName) {
                            sameClientName = false
                        }
                        ids.push(el.id)
                    })
                    body = { ids: ids }
                    if (sameClientName) {
                        multFileName = clientName + '_协议附件'
                    }
                }
                if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            }
            request.post('/api/hr-protocols/appendixs', body).then(async (res: any[]) => {
                let urls: any[] = []
                let folders: inObject = []
                let sysOperLogsUrls: any[] = []
                let sysOperLogsNames: any[] = []
                res?.forEach((element: any) => {
                    sysOperLogsNames.push(element.agreementTitle)
                    if (element) {
                        urls.push(element)
                        folders.push({
                            id: element.id,
                            folderName: element.agreementTitle,
                            srcs: element.appendixList.map((el) => {
                                sysOperLogsUrls.push(el?.fileUrl)
                                return { url: el?.fileUrl, name: el?.originName }
                            }),
                        })
                    }
                })
                if (!urls.length) {
                    message.error('暂无可供下载附件')
                    return
                }
                request.post(
                    '/api/sys-oper-logs/download',
                    {
                        title: '客户协议',
                        operDetail: '客户协议下载:' + sysOperLogsNames.join(),
                        fileUrl: sysOperLogsUrls.join(),
                    },
                    { loading: false },
                )
                await downMultFile(multFileName, folders, [], true)
            })
        }

        // 导入
        const importVisible = ref(false)
        const importData = () => {
            importVisible.value = true
        }

        //终止协议
        const showTerminationProtocol = ref(false) //终止协议
        const terminationProtocolRow = (record) => {
            showTerminationProtocol.value = true
            modalTitle.value = '终止协议'
            currentValue.value = { ...record }
        }
        const showRecoveryProtocol = ref(false) //恢复协议
        const recoveryProtocolRow = (record) => {
            showRecoveryProtocol.value = true
            modalTitle.value = '恢复协议'
            currentValue.value = { ...record }
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'customerAgreement_edit',
                    show: true,
                    click: (record) => editRow(record, 'edit'),
                },
                {
                    neme: '查看',
                    auth: 'customerAgreement_check',
                    show: true,
                    click: (record) => editRow(record, 'see'),
                },
                {
                    neme: '上传',
                    auth: 'customerAgreement_uploadEnclosure',
                    show: true,
                    click: upload,
                },
                {
                    neme: '续签',
                    auth: 'customerAgreement_renew',
                    show: (record) => {
                        return (record.states == 1 || record.states == 2) && record.types != 1
                    },
                    click: (record) => editRow(record, 'renew'),
                },

                {
                    neme: '下载',
                    auth: 'customerAgreement_download',
                    show: (record) => {
                        return !!record.appendixId
                    },
                    click: download,
                },
                {
                    neme: '终止协议',
                    auth: 'customerInfo_termination',
                    show: (record) => {
                        return record.states == 0 || record.states == 1
                    },
                    click: terminationProtocolRow,
                },
                {
                    neme: '恢复协议',
                    auth: 'customerInfo_recover',
                    show: (record) => {
                        return record.states == 3
                    },
                    click: recoveryProtocolRow,
                },
            ]),
        )
        const tableDataFormat = (data) => {
            console.log(data)
            return data.filter((i) => i.states == 2)
        }
        return {
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            showUpload,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,

            download,
            editRow,
            deleteRow,
            upload,
            //事件
            //导入
            importVisible,
            importData,
            goToCustomInfo,

            // 操作按钮
            myOperation,

            selectedRowsArr,

            showTerminationProtocol,
            terminationProtocolRow,
            //恢复
            showRecoveryProtocol,
            recoveryProtocolRow,
            exportText,
            tableData,
            tableDataFormat,
            isShowSelectedLine,
        }
    },
})
</script>

<style scoped lang="less"></style>
