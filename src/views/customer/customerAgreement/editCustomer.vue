<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
        :zIndex="1009"
    >
        <Agreement
            :viewType="viewType"
            :isEditAdd="isEditAdd"
            :visible="visible"
            :item="item"
            @cancel="cancel"
            @confirm="confirm"
            ref="refAgreement"
        />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import Agreement from '../customerInfo/component/agreement.vue'
export default defineComponent({
    name: 'EditCustomer',
    components: {
        Agreement,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        isEditAdd: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        // Form 实例
        const refAgreement = ref(null) as any
        const viewType = ref('addCustomer')
        const { title } = toRefs(props)
        watch(
            title,
            () => {
                if (title.value?.includes('新增')) {
                    viewType.value = 'addCustomer'
                } else if (title.value?.includes('编辑')) {
                    viewType.value = 'editCustomer'
                } else if (title.value?.includes('续签')) {
                    viewType.value = 'renewCustomer'
                } else {
                    viewType.value = 'seeCustomer'
                }

                console.log(viewType.value)
            },
            { immediate: true },
        )

        const cancel = () => {
            refAgreement.value.resetData()
            emit('cancel')
        }

        const confirm = () => {
            cancel()
            emit('confirm')
        }

        return {
            // 按钮
            confirm,
            cancel,
            //refs实例
            refAgreement,
            //数据
            viewType,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
