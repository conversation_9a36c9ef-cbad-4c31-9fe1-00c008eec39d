<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <div>
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; overflow-x: auto; margin-top: 20px; margin-right: 20px"
                class="form-flex"
            >
                <template v-for="(item, index) in myOptions" :key="item">
                    <MyFormItem
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        :class="item.slots"
                        v-if="item.show != false"
                    >
                        <template #Cascader>
                            <!-- <Cascader
                                v-model:value="clientId"
                                :options="clientList"
                                :load-data="loadData"
                                change-on-select
                                @change="clientChange"
                                :placeholder="item.placeholder || `请选择${item.label}`"
                            /> -->
                            <ClientSelectTree
                                v-model:value="formData[item.name]"
                                :itemForm="item"
                                @change="ClientSelectTreeChange()"
                            />
                        </template>
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[index]" allowClear />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="ant-modal-footer">
                <Button key="back" @click="cancel">取消</Button>
                <Button key="submit" type="primary" @click="confirm">确定</Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'
import { validatePhone, idCardValidity } from '/@/utils/format'
import { SearchBarOption, valuesAndRules } from '/#/component'
import moment, { Moment } from 'moment'

export default defineComponent({
    name: 'AddStaff',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const { title, item, visible } = toRefs<any>(props)
        const sexList = ref<object[]>([]) // 性别
        const station = ref<object[]>([]) //岗位
        const staffTypeList = ref<object[]>([]) //人员类型
        const protocolList = ref<object[]>([]) //客户协议
        // const clientId = ref<string[]>([])
        // const clientList = ref<Option[]>([]) //所属客户
        const isDisabled = ref<Boolean>(true)

        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/hr-stations/list', {}).then((res) => {
                station.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // request.get('/api/hr-clients/owned-customer', { id: '' }).then((res) => {
            //     clientList.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id, isLeaf: !item.isLeaf }
            //     })
            // })
        })

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '所属客户',
                name: 'clientId',
                slots: 'Cascader',
                type: 'slots',
                // default: null,
            },
            {
                label: '客户协议',
                name: 'protocolId',
                type: 'change',
                options: protocolList,

                showbr: true, //换行
                disabled: isDisabled,
            },
            {
                label: '姓名',
                name: 'name',
            },
            {
                label: '身份证号',
                name: 'certificateNum',
                validator: idCardValidity,
            },
            {
                label: '性别',
                name: 'sex',
                type: 'change',
                options: sexList,

                default: '1',
                ruleType: 'number',
            },
            {
                label: '联系方式',
                name: 'phone',
                validator: validatePhone,
            },
            {
                label: '岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '合同开始日期',
                name: 'contractStartDate',
                type: 'date',
                disabledDate: (startValue: Moment) => {
                    if (!startValue || !formData.value?.contractEndDate) {
                        return false
                    }
                    return startValue.startOf('day').valueOf() > new Date(formData.value?.contractEndDate)?.valueOf()
                },
            },
            {
                label: '合同结束日期',
                name: 'contractEndDate',
                type: 'date',
                disabledDate: (endValue: Moment) => {
                    if (!endValue || !formData.value?.contractStartDate) {
                        return endValue && endValue < moment().startOf('day')
                    }
                    return (
                        new Date(formData.value?.contractStartDate).valueOf() >= endValue.endOf('day').valueOf() ||
                        endValue < moment().startOf('day')
                    )
                },
            },
            {
                label: '基础工资/月',
                name: 'basicWage',
                ruleType: 'number',
                type: 'number',
                min: 0,
            },
            {
                label: '社保基数',
                name: 'socialSecurityCardinal',
                ruleType: 'number',
                type: 'number',
                min: 0,
            },
            {
                label: '医保基数',
                name: 'medicalInsuranceCardinal',
                ruleType: 'number',
                type: 'number',
                min: 0,
            },
            {
                label: '公积金基数',
                name: 'accumulationFundCardinal',
                ruleType: 'number',
                type: 'number',
                min: 0,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const getDetail = (value) => {
            // clientId.value = []
            request.get('/api/hr-apply-entry-staffs', { id: value }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
                // getAgreement(formData.value.clientId)
                // clientId.value.push(formData.value.clientId)
                ClientSelectTreeChange(true)
            })
        }
        watch(
            visible,
            () => {
                if (!visible.value) {
                    return
                }
                if (item.value) {
                    getDetail(item.value?.id)
                } else {
                    formData.value.clientId = null
                }
                formData.value.contractStartDate = moment(new Date()).format('YYYY-MM-DD')
                // console.log(formData.value.contractStartDate)
            },
            { immediate: true },
        )

        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        // // 客户协议
        // const clientChange = (e) => {
        //     formData.value.clientId = e[e.length - 1]
        //     protocolList.value = []
        //     formData.value.protocolId = null
        //     if (formData.value.clientId) {
        //         isDisabled.value = false
        //         getAgreement(formData.value.clientId)
        //     } else {
        //         isDisabled.value = true
        //     }
        // }
        // 客户协议接口
        const getAgreement = (value) => {
            request.get('/api/hr-protocols/customer-agreement', { clientId: value }).then((res) => {
                protocolList.value = res.map((item) => {
                    return { label: item.agreementTitle, value: item.id }
                })
            })
        }
        // // 所属客户下级
        // const loadData = (selectedOptions: Option[]) => {
        //     const targetOption = selectedOptions[selectedOptions.length - 1]
        //     targetOption.loading = true
        //     request.get('/api/hr-clients/owned-customer', { id: targetOption.value }).then((res) => {
        //         targetOption.loading = false
        //         targetOption.children = res.map((item) => {
        //             return { label: item.clientName, value: item.id, isLeaf: !item.isLeaf }
        //         })
        //         clientList.value = [...clientList.value]
        //     })
        // }
        const confirm = () => {
            console.log(formData.value)
            let formNewData = {
                clientId: formData.value.clientId,
                protocolId: formData.value.protocolId,
                name: formData.value.name,
                certificateNum: formData.value.certificateNum,
                sex: formData.value.sex,
                phone: formData.value.phone,
                stationId: formData.value.stationId,
                personnelType: formData.value.personnelType,
                contractStartDate: formData.value.contractStartDate,
                contractEndDate: formData.value.contractEndDate,
                basicWage: formData.value.basicWage,
                socialSecurityCardinal: formData.value.socialSecurityCardinal,
                medicalInsuranceCardinal: formData.value.medicalInsuranceCardinal,
                accumulationFundCardinal: formData.value.accumulationFundCardinal,
            }

            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')

                    await request.post('/api/hr-apply-entry-staffs/create', formNewData)
                    message.success('新增成功!')

                    emit('confirm')
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const ClientSelectTreeChange = (type = false) => {
            if (!type) {
                formData.value.protocolId = null
            }

            if (formData.value.clientId) {
                isDisabled.value = false
                getAgreement(formData.value.clientId)
            } else {
                isDisabled.value = true
            }
        }
        return {
            rules,
            formData,
            myOptions,
            formInline,
            sexList,
            station,
            staffTypeList,
            // clientList,
            // clientId,
            // loadData,
            confirm,
            cancel,
            // clientChange,

            ClientSelectTreeChange,
        }
    },
})
</script>
<style scoped lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
.btn {
    text-align: right;
    margin: 10px;
    button {
        margin-left: 10px;
    }
}
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
:deep(.add-table) {
    border: 1px solid #f0f0f0;
    border-top: none;
    padding: 10px;
    text-align: center;
    color: #999;
}
</style>
