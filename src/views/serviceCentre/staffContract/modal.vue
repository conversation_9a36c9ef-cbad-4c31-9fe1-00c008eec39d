<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" :currentStep="getCurrentStep()" labelPlacement="vertical" />
        <ApplicationInfo :detailData="item" />
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" v-if="item?.applyOpLogsList?.length" />
        <div class="examine" v-if="modalType !== 'look'">
            <div class="examine-top">
                <Divider type="vertical" class="divid" />
                <span>拒绝理由</span>
            </div>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Textarea v-model:value="formData.reason" :rows="3" allowClear placeholder="若拒绝，请输入拒绝理由" />
            </div>
            <!-- 是否需要客户审核 -->
            <template v-if="item?.states == 1">
                <div class="examine-top">
                    <Divider type="vertical" class="divid" />
                    <span>是否需要客户审核</span>
                </div>
                <div class="examine-wrap">
                    <p class="linefeed"></p>
                    <RadioGroup name="radioGroup" v-model:value="formData.hasCustomAudit">
                        <Radio :value="true">需要</Radio>
                        <Radio :value="false">不需要</Radio>
                    </RadioGroup>
                </div>
            </template>
        </div>
        <template #footer>
            <div v-if="modalType !== 'look'">
                <Button @click="onConfirm(false)" class="rejectBtn">拒绝</Button>
                <Button @click="onConfirm(true)" type="primary" class="successBtn">
                    {{ formData.hasCustomAudit && item?.states == 1 ? '通知客户' : '通过' }}
                </Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup="StaffContractModal">
import { ref, toRefs } from 'vue'
import { isEmpty } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import ApplicationInfo from './component/ApplicationInfo.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'
import { message } from 'ant-design-vue'
const props = defineProps({
    title: String,
    visible: {
        type: Boolean,
        default: false,
    },
    modalType: {
        type: String,
        default: '',
    },
    item: {
        type: Object,
        default: () => {},
    },
})
const emit = defineEmits(['cancel', 'confirm'])
const { item, modalType } = toRefs(props)
//表单数据
const stepsData = ref([
    {
        title: '员工发起申请',
    },
    {
        title: '平台审核',
    },
    {
        title: '公司确认申请',
    },
])

// Form Data
const formData = ref<any>({ hasCustomAudit: false, reason: '' })

// resetData
const resetData = () => {
    formData.value = { hasCustomAudit: false, reason: '' }
}

const getCurrentStep = () => {
    if (item.value?.states == 4 || item.value?.states == 5) return 3
    else return Number(item.value?.states) || 0
}

// cancel handle
const onCancel = () => {
    modalType.value !== 'look' && resetData()
    emit('cancel')
}
// confirm handle
const onConfirm = (flag) => {
    if (!flag && isEmpty(formData.value.reason)) {
        message.warn('请输入拒绝理由')
        return
    }
    emit('confirm', {
        checkerReason: !flag ? formData.value.reason : undefined,
        isNeed: item.value.states == 1 ? (formData.value.hasCustomAudit ? 2 : 1) : undefined,
        applyId: item.value.id,
        opt: flag,
    })
    resetData()
}
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .examine-top {
        margin-top: 15px;
    }
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 20px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
// step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
.successBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
