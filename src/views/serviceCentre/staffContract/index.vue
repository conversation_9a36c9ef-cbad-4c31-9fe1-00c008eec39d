<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #professionIdList="itemForm">
            <PostTree
                v-model:value="params.professionIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'staffContract_pass'" @click="batchAudit(true)" class="downloadBtn">批量通过</Button>
        <Button type="primary" v-auth="'staffContract_reject'" @click="batchReject" class="delBtn">批量拒绝</Button>
        <Button type="primary" v-auth="'staffContract_del'" @click="batchDelete" class="delBtn">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-contract-views/page"
        deleteApi="/api/hr-contract-views/deletes"
        exportUrl="/api/hr-contract-views/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        :visible="modalVisible"
        :modalType="detailType"
        :item="currentValue"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="batchRejectCancel"
        @confirm="batchAudit(false)"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts" setup="StaffContract">
import { message } from 'ant-design-vue'
import { ref, onMounted, onBeforeMount, computed } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import permissionStore from '/@/store/modules/permission'
import { getHaveAuthorityOperation, openNotification } from '/@/utils/index'
import { SearchBarOption } from '/#/component'
import { sexList, contractLookTypeOptions, staffStatus } from '/@/utils/dictionaries'
import { getDynamicText } from '/@/utils'
import MyModal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { useRoute } from 'vue-router'
const RoleState = permissionStore().getPermission

// 筛选
let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
let statusTypeList = ref<LabelValueOptions>([]) // 状态
let specializedList = ref<LabelValueOptions>([]) // 专管员

//表格数据
let columns = ref([
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 180,
        align: 'center',
    },
    {
        title: '员工姓名',
        dataIndex: 'name',
        width: 100,
        align: 'center',
    },
    {
        title: '员工状态',
        dataIndex: 'staffStatus',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
            return staffStatus.find((el) => {
                return el.value == text
            })?.label
        },
    },
    {
        title: '身份证号',
        dataIndex: 'certificateNum',
        width: 160,
        align: 'center',
    },
    {
        title: '性别',
        dataIndex: 'sex',
        width: 70,
        align: 'center',
        customRender: ({ record }) => {
            return sexList.find((el) => {
                return record.sex == el.value
            })?.label
        },
    },
    {
        title: '联系方式',
        dataIndex: 'phone',
        align: 'center',
        width: 110,
    },
    {
        title: '岗位',
        dataIndex: 'professionName',
        width: 130,
        align: 'center',
    },
    {
        title: '人员类型',
        dataIndex: 'personnelType',
        align: 'center',
        width: 100,
        customRender: ({ record }) => {
            return staffTypeList.value.find((el) => {
                return record.personnelType == el.value
            })?.label
        },
    },
    {
        title: '申请日期',
        dataIndex: 'createdDate',
        align: 'center',
        width: 150,
    },
    {
        title: '申请内容',
        dataIndex: 'applyState',
        align: 'center',
        width: 100,
        customRender: ({ record }) => {
            return record.applyStateLabel
        },
        ellipsis: true,
    },
    {
        title: '申请描述',
        dataIndex: 'applyDescription',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '状态',
        dataIndex: 'states',
        align: 'center',
        width: 135,
        customRender: ({ record }) => {
            return record.statesLabel
        },
    },
    {
        title: '专管员',
        dataIndex: 'specialized',
        align: 'center',
        width: 130,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        slots: { customRender: 'operation' },
        width: 250,
        fixed: 'right',
    },
])

let options: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        placeholder: '客户名称',
        multiple: true,
        type: 'clientSelectTree',
        maxTag: '0',
        checkStrictly: false,
    },
    {
        type: 'string',
        label: '员工姓名',
        key: 'name',
    },
    {
        type: 'select',
        label: '员工状态',
        key: 'staffStatusList',
        multiple: true,
        options: staffStatus,
    },
    {
        type: 'string',
        label: '身份证号',
        key: 'certificateNum',
    },
    {
        type: 'select',
        label: '性别',
        key: 'sex',
        options: sexList,
    },
    {
        type: 'string',
        label: '联系方式',
        key: 'phone',
    },
    {
        type: 'selectSlot',
        label: '岗位',
        key: 'professionIdList',
        placeholder: '岗位',
        maxTag: '0',
    },
    {
        type: 'select',
        label: '人员类型',
        key: 'personnelTypeList',
        options: staffTypeList,
        multiple: true,
    },
    {
        type: 'daterange',
        label: '申请日期',
        key: 'createdDateQuery',
    },
    {
        type: 'select',
        label: '申请内容',
        key: 'applyState',
        options: contractLookTypeOptions,
    },
    {
        type: 'select',
        label: '状态',
        key: 'statesList',
        options: statusTypeList,
        multiple: true,
    },
    {
        type: 'select',
        label: '专管员',
        key: 'userIdList',
        options: specializedList,
        multiple: true,
    },
]

onBeforeMount(() => {
    if (!RoleState.staffState) {
        let searchInd = options.findIndex((el) => {
            return el.key == 'specializedList'
        })
        let tableInd = columns.value.findIndex((el) => {
            return el.dataIndex == 'specialized'
        })
        options.splice(searchInd, 1)
        columns.value.splice(tableInd, 1)
    }
})

onMounted(() => {
    // 人员类型
    dictionaryDataStore()
        .setDictionaryData('staffType', '')
        .then((data: inObject[]) => {
            staffTypeList.value = data.map((item) => {
                return { label: item.itemName, value: item.itemValue }
            })
        })
    // 状态
    dictionaryDataStore()
        .setDictionaryData('staffContractStates', '')
        .then((data: inObject[]) => {
            statusTypeList.value = data.map((item) => {
                return { label: item.itemName, value: item.itemValue }
            })
        })
    // 专管员
    dictionaryDataStore()
        .setDictionaryData('Specialized', '/api/users/getSpecialized')
        .then((data: inObject[]) => {
            specializedList.value = data.map((item) => {
                return { label: item.realName, value: item.id }
            })
        })
})
//表格dom
const tableRef = ref()
//筛选
const route = useRoute()
const params = ref<any>({
    statesList: route.query?.statusList ? JSON.parse(route.query?.statusList as string) : undefined,
})

// 搜索
const searchData = async () => {
    tableRef.value.refresh(1)
}

const modalTitle = ref('')
const modalVisible = ref(false)
const detailType = ref('')

// 当前编辑的数据
const currentValue = ref<any>(null)

// 显示弹窗
const showModal = (record, type) => {
    modalVisible.value = true
    switch (type) {
        case 'look':
            modalTitle.value = '查看'
            break
        case 'audit':
            modalTitle.value = '审核'
            break
    }
    getDetails(record.id)
    detailType.value = type
}

// 获取详细信息
const getDetails = (id) => {
    request
        .get(`/api/hr-contract-views/${id}`)
        .then((res) => {
            currentValue.value = {
                ...res,
                sexLabel: sexList.find((el) => {
                    return res.sex == el.value
                })?.label,
            }
        })
        .catch((err) => {
            console.log(err)
        })
}

const cancelHandle = () => {
    modalVisible.value = false
}

const exportText = computed(() => {
    return getDynamicText('导出', params.value, selectedRowsArr.value)
})
//导出
const exportData = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}
const executeOperation = (obj) => {
    request
        .post(`/api/hr-contract-views/special-supervisor-operation`, obj)
        .then((res) => {
            tableRef.value.refresh()
            cancelHandle()
            message.success(obj.opt ? (obj.isNeed == 2 ? '已通知客户' : '审核已通过') : '审核已拒绝')
        })
        .catch((err) => {
            console.log(err)
        })
}

// 多选
const selectedRowsArr = ref([])

const showReject = ref(false)
const checkerReason = ref('')
const batchReject = () => {
    if (selectedRowsArr.value.length <= 0) {
        message.warning('请选择要拒绝的申请')
        return false
    }
    showReject.value = true
}

const batchRejectCancel = () => {
    showReject.value = false
    checkerReason.value = ''
}

// 批量通过/拒绝
const batchAudit = (opt) => {
    if (selectedRowsArr.value.length <= 0) {
        message.error('请至少选择选择一条数据!')
        return
    }
    if (!opt && !checkerReason.value) {
        message.error('请填写拒绝理由!')
        return
    }
    request
        .post('/api/hr-contract-views/batch-operation', {
            applyIdList: selectedRowsArr.value.map((el) => {
                return el.id
            }),
            checkerReason: checkerReason.value,
            opt: opt,
        })
        .then((res: inObject) => {
            tableRef.value.refresh()
            if (res.checkCode == 500) {
                openNotification(res.checkMsg)
            }
            if (res.checkCode == 200) {
                message.success(res.checkMsg)
            }
            batchRejectCancel()
        })
        .catch((err) => {
            console.log(err)
        })
}

// 批量删除
const batchDelete = () => {
    tableRef.value.deleteRow().then((ref) => {})
}

const auditVisible = (record) => {
    const statesList = [1, 2]
    if (
        ((RoleState.roleKey == 'super_admin' || RoleState.roleKey == 'maintenance') && statesList.includes(record.states)) ||
        (RoleState.roleKey == 'client' && record.states == 2) ||
        // (RoleState.roleKey == 'customer_service_staff' && record.states == 1) ||
        (RoleState.roleKey !== 'client' && record.states == 1)
    )
        return true
    else return false
}

let myOperation = ref<inObject[]>(
    getHaveAuthorityOperation([
        {
            neme: '查看',
            auth: 'staffContract_look',
            show: true,
            click: (record) => showModal(record, 'look'),
        },
        {
            neme: '审核',
            auth: 'staffContract_audit',
            show: (record) => {
                return auditVisible(record)
            },
            click: (record) => showModal(record, 'audit'),
        },
    ]),
)
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
.hidden {
    display: none;
}
</style>
