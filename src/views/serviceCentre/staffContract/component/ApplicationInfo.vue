<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>申请信息</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">员工姓名：</span>
                <span>{{ detailData?.name }}</span>
            </div>
            <div class="item-flex">
                <span class="label">身份证号：</span>
                <span>{{ detailData?.certificateNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">性别：</span>
                <span>{{ detailData?.sexLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">联系方式：</span>
                <span>{{ detailData?.phone }}</span>
            </div>
            <div class="item-flex">
                <span class="label">岗位：</span>
                <span>{{ detailData?.professionName }}</span>
            </div>
            <div class="item-flex">
                <span class="label">人员类型：</span>
                <span>{{ detailData?.personnelTypeLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同开始日期：</span>
                <span>{{ detailData?.contractStartDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同结束日期：</span>
                <span>{{ detailData?.contractEndDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">申请内容：</span>
                <span>{{ detailData?.applyStateLabel }}</span>
            </div>
        </div>
        <div class="item-row">
            <span class="label">申请描述：</span>
            <span class="value">{{ detailData?.applyDescription }}</span>
        </div>
    </div>
</template>

<script lang="ts" setup="ApplicationInfo">
defineProps({
    detailData: {
        type: Object,
        default: () => {},
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .item-flex {
        width: 25%;
        margin: 5px 0px;
        .label {
            width: 75px;
            color: rgba(153, 153, 153, 1);
        }
    }
    .item-row {
        width: 100%;
        padding-left: 15px;
        margin: 5px 0px;
        display: flex;
        .label {
            width: 75px;
            color: rgba(153, 153, 153, 1);
        }
        .value {
            flex: 1;
        }
    }
    .linefeed {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
    }
}
</style>
