<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="确认信息" :width="'600px'" :footer="true">
        <template v-if="!showTip">
            <div class="content">
                <div class="img-wrapper">
                    <img src="~//@/assets/hint.png" alt="" />
                </div>
                <div class="tips">是否确认信息正确且完整</div>
            </div>
            <div class="btns_wrapper">
                <Button @click="confirmationInfo(false)" danger type="primary" class="btn" shape="round" size="large"
                    >否,通知员工修改</Button
                >
                <Button @click="confirmationInfo(true)" type="primary" class="btn" shape="round" size="large">是</Button>
            </div>
        </template>
        <template v-else>
            <div style="font-size: 16px">
                <span>确认信息成功</span>
                <span style="color: #83a7fd; margin: 0 5px">{{ confirmInforValue.success_num || 0 }}</span>
                <span>人,</span>
                <span>确认信息失败</span>
                <span style="color: #fe6b6b; margin: 0 5px">{{ confirmInforValue.error_num || 0 }}</span>
                <span>人</span>
            </div>
            <div style="padding: 20px 0">
                <span style="font-weight: bold">失败原因:</span>
                {{ confirmInforValue.failure_message }}<br />
            </div>
            <div>
                <span style="font-weight: bold">失败员工:</span>
                {{ confirmInforValue.failure_staff }}
            </div>
        </template>

        <!-- <template #footer>
            <Button key="back" @click="cancel" type="primary" class="btn">完成</Button>
        </template> -->
    </BasicEditModalSlot>
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="requestData(false)"
        title="修改意见"
        width="500px"
    >
        <Textarea v-model:value="errorMessage" placeholder="请输入修改意见" :rows="7" />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message, notification } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'OperationModal',
    props: {
        applyStaffIdList: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:visible', 'confirm'],
    setup(props, { emit }) {
        const { applyStaffIdList, visible } = toRefs(props)
        const showTip = ref(false)
        watch(visible, () => {
            if (visible.value) {
                showTip.value = false
                errorMessage.value = ''
                // formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })
        const showRejec = ref(false)
        const errorMessage = ref('')
        const confirmInforValue = ref<inObject>({ error_num: '0', failure_message: '', failure_staff: '', success_num: '1' })
        const confirmationInfo = (opt) => {
            if (opt) {
                requestData(opt)
            } else {
                showRejec.value = true
            }
        }
        const requestData = (opt) => {
            showRejec.value = false
            request
                .post('/api/hr-apply-entry-staffs/notarize_information', {
                    applyStaffIdList: applyStaffIdList.value,
                    opt,
                    errorMessage: errorMessage.value,
                })
                .then((res) => {
                    console.log(res)

                    confirmInforValue.value = res.body
                    showTip.value = true
                    // if (!opt) {
                    confirm()
                    // }
                    // if (res.body.failure_message) {
                    //     openNotification(res.body.failure_message)
                    // }
                })
        }
        // //提示
        // const openNotification = (tip) => {
        //     notification.open({
        //         message: '处理信息提示',
        //         description: tip,
        //         onClick: () => {
        //             console.log('Notification Clicked!')
        //         },
        //     })
        // }
        const confirm = () => {
            emit('confirm')
        }
        const cancel = () => {
            console.log(11)
            emit('update:visible', false)
        }

        return {
            confirmInforValue,
            errorMessage,
            showRejec,
            confirmationInfo,
            requestData,
            cancel,

            showTip,
        }
    },
})
</script>
<style scoped lang="less">
.content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img-wrapper {
        width: 200px;
        height: 200px;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .tips {
        font-weight: 600;
    }
}
.btns_wrapper {
    display: flex;
    justify-content: space-between;
    padding: 30px 40px 0;
    .btn {
        width: 215px;
    }
}
</style>
