<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px" :footer="null">
        <div class="step-detail">
            <Steps :current="applyStep" labelPlacement="vertical" class="my_steps">
                <Step title="发送入职通知" />
                <Step title="员工录入信息" />
                <Step title="发起劳动合同" />
                <Step title="签署劳动合同" />
                <Step title="入职完成" />
            </Steps>
        </div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>入职详情</span>
            <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                <div class="list-item">
                    <span style="width: 40px">{{ index + 1 }}</span>
                    <div class="item-flex">
                        <span class="box1">操作人：</span>
                        <span class="box2">{{ item.realName }}</span>
                    </div>
                    <div class="item-flex2">
                        <span>操作时间：</span>
                        <span>{{ item.createdDate }}</span>
                    </div>
                    <div class="item-flex3">
                        <span class="box1">操作信息：</span>
                        <span class="box2">{{ item.message }}</span>
                    </div>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message, Modal, notification } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'

export default defineComponent({
    name: 'Induction',
    props: {
        title: String,
        type: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, visible } = toRefs<any>(props)
        const applyStep = ref(0)
        const detailData = ref<inObject>({})
        const opLogsList = ref<object[]>([])
        const checkerReason = ref('')

        let applyStepArr = [0, 1, 2, 2, 3, 4, 5, 6]
        const getDetail = (value) => {
            request.get('/api/hr-talent-staff/secondary-info', { id: value }).then((res) => {
                applyStep.value = applyStepArr[Number(res.entryStatus) - 1]
                // applyStep.value = Number(res.applyStep)
                detailData.value = res
                opLogsList.value = res.opLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )
        const cancel = () => {
            emit('cancel')
        }
        const confirm = () => {
            let applyId = detailData.value.id
            request
                .post('/api/hr-apply-entry-staffs/approve-passed', { applyIdList: [applyId], checkerReason: checkerReason.value })
                .then((res) => {
                    if (res.body.message_exist) {
                        openNotification(res.body.message_exist)
                    }
                    emit('cancel')
                    emit('confirm')
                })
        }
        const openNotification = (tip) => {
            notification.open({
                message: '处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }
        return {
            checkerReason,
            applyStep,
            detailData,
            opLogsList,
            cancel,
            confirm,
        }
    },
})
</script>
<style scoped lang="less">
.step-detail {
    margin-bottom: 30px;
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 200px;
                .box1 {
                    float: left;
                    width: 58px;
                }
                .box2 {
                    float: right;
                    width: 142px;
                    padding-right: 4px;
                }
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title) {
    color: #6894fe;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
