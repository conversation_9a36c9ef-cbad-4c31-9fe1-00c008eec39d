<template>
    <div class="en-div">
        <Table :columns="Columns" :data-source="columnsData" :pagination="false" :row-key="(record) => record.id || record.index">
            <template #operation="{ record, index }">
                <template v-if="record.type == '1'">
                    <Button type="primary" size="small" @click="sendClick(record, index)" v-if="record.isDelete == 1"
                        >制作</Button
                    >
                    <Button
                        type="primary"
                        class="warning-btn"
                        size="small"
                        v-if="record.isDelete == 0"
                        @click="sendClick(record, index)"
                        >修改</Button
                    >
                </template>
                <template v-if="record.type != 1">
                    <Button type="primary" size="small" v-if="record.isDelete == 1" @click="stateConfirm(record)">确认</Button>
                    <Button type="primary" class="warning-btn" size="small" v-if="record.isDelete == 0" @click="updataRow(record)"
                        >修改</Button
                    >
                </template>
                &nbsp;
                <Button danger type="primary" size="small" @click="delClick(record)">删除</Button>
            </template>
        </Table>
    </div>
    <div class="btn-flex">
        <Button type="primary" @click="backClick">上一步</Button>
        <Button type="primary" @click="confirmClick">确认发送</Button>
    </div>
    <ProductionContract
        v-if="contractVisible"
        :viewType="viewType"
        :type="type"
        :visible="contractVisible"
        :title="contractTitle"
        :item="currentValue"
        @cancel="sendCancel"
        @confirm="sendConfirm"
    />
    <UpdateEnclosure
        v-if="uploadVisible"
        :visible="uploadVisible"
        :title="'发起电签'"
        :item="updateValue"
        @cancel="updateCancel"
        @confirm="updateConfirm"
    />
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, watchEffect } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import productionContract from './productionContract.vue'
import signStore from '/@/store/modules/electricSign'
import { message } from 'ant-design-vue'
import staffStore from '/@/store/modules/staff'
import updateEnclosure from './updateEnclosure.vue'

export default defineComponent({
    name: 'SelectTemplate',
    components: {
        ProductionContract: productionContract,
        UpdateEnclosure: updateEnclosure,
    },
    props: {
        viewType: {
            type: String,
            default: 'electricSign',
        },
        type: {
            type: String,
            default: 'single',
            validator: (val: string) => {
                return ['single', 'multiple'].includes(val)
            },
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'changeTab'],
    setup(props, { emit }) {
        const { visible, viewType, type } = toRefs<any>(props)
        const activeKey = ref('1')
        const contractVisible = ref<Boolean>(false)
        const contractTitle = ref('制作合同')
        const currentValue = ref(null)
        const columnsData = ref<inObject[]>([])
        // const staffContactList = ref<inObject[]>([])
        // const mustAttachmentList = ref<inObject[]>([])
        // const optionalAttachmentList = ref<inObject[]>([])
        // const contractStartDate = ref('') //合同开始日期
        // const contractEndDate = ref('') //合同结束日期
        const uploadVisible = ref<Boolean>(false)
        const updateValue = ref(null)

        const templateData = ref<inObject>({})

        //表格dom
        const tableRef = ref()
        //表格数据
        const Columns = [
            {
                title: '名称',
                dataIndex: 'name',
                align: 'center',
            },
            {
                title: '状态',
                dataIndex: 'isDelete',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 1) {
                        text = '未确认'
                    } else {
                        text = '已确认'
                    }
                    return text
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
            },
        ]

        // 监听数据变化
        watchEffect(() => {
            templateData.value = signStore().getSign.templateData
        })
        watch(
            visible,
            () => {
                if (visible.value) {
                }
            },
            { immediate: true },
        )
        watch(
            templateData,
            () => {
                const list = ref<object[]>([])
                if (templateData.value?.requiredList?.length) {
                    let name = templateData.value.requiredList
                        .map((item) => {
                            return item.name
                        })
                        .join(',')
                    list.value.push({ name: `必填附件：${name}`, isDelete: '1', id: 'must1', type: '2' })
                }
                if (templateData.value?.optionalList?.length) {
                    let name = templateData.value.optionalList
                        .map((item) => {
                            return item.name
                        })
                        .join(',')
                    list.value.push({ name: `可填附件：${name}`, isDelete: '1', id: 'must2', type: '3' })
                }
                columnsData.value = []
                columnsData.value = columnsData.value.concat(templateData.value?.contractList, list.value).sort((a, b) => {
                    return a.type - b.type
                })
            },
            { deep: true, immediate: true },
        )

        // 关闭弹窗
        const sendCancel = () => {
            contractVisible.value = false
            contractTitle.value = '制作合同'
            currentValue.value = null
        }
        let currentIndex
        // 确认弹窗
        const sendConfirm = async (val) => {
            columnsData.value[currentIndex].firstPartInfo = val
            // tableRef.value.refresh()
        }

        const sendClick = (record, index) => {
            currentIndex = index
            contractVisible.value = true
            contractTitle.value = '制作合同'
            currentValue.value = { ...record }
        }
        const delClick = (record) => {
            const ids: (string | number)[] = []
            if (record.type == '1') {
                // 合同
                ids.push(record.id)
                templateData.value.contractList = templateData.value.contractList.filter((el) => el.id !== record.id)
                signStore().setTemplateDataByKey(templateData.value.contractList, 'contractList')
            } else if (record.type == '2') {
                // 必填
                templateData.value.requiredList.forEach((el) => {
                    ids.push(el.id)
                })
                signStore().setTemplateDataByKey([], 'requiredList')
            } else if (record.type == '3') {
                // 可填
                templateData.value.optionalList.forEach((el) => {
                    ids.push(el.id)
                })
                signStore().setTemplateDataByKey([], 'optionalList')
            }
            columnsData.value = columnsData.value.filter((item) => {
                return record.id != item.id
            })
            request.post('/api/hr-contracts/appendix-delete', ids).then((res) => {
                console.log(res)
            })
        }
        const updataRow = (record) => {
            if (record.type == '3' || record.type == '2') {
                uploadVisible.value = true
                updateValue.value = { ...record }
            } else if (record.type == '1') {
                contractVisible.value = true
                contractTitle.value = '制作合同'
                currentValue.value = { ...record }
            }
        }
        // 关闭弹窗
        const updateCancel = () => {
            uploadVisible.value = false
            updateValue.value = null
        }
        // 确认弹窗
        const updateConfirm = async () => {
            // tableRef.value.refresh()
        }
        const backClick = () => {
            if (viewType.value == 'renewal') {
                emit('changeTab', '1')
            } else {
                if (signStore().getSign.tabs == 3) {
                    emit('changeTab', '2')
                } else {
                    emit('changeTab', '1')
                }
            }
        }
        // 必填可填确认
        const stateConfirm = (record) => {
            let ids: (string | number)[] = []
            if (record.type == '2') {
                // 必填
                templateData.value.requiredList.forEach((el) => {
                    ids.push(el.id)
                })
            } else if (record.type == '3') {
                // 可填
                templateData.value.optionalList.forEach((el) => {
                    ids.push(el.id)
                })
            }
            request.post('/api/hr-contracts/change-contract-appendix-state', ids).then((res) => {
                console.log(res)
                record.isDelete = 0
                if (record.type == '2') {
                    // 必填
                    templateData.value.requiredList.forEach((el) => {
                        if (el.id == record.id) {
                            el.isDelete = 0
                        }
                    })
                } else if (record.type == '3') {
                    // 可填
                    templateData.value.optionalList.forEach((el) => {
                        if (el.id == record.id) {
                            el.isDelete = 0
                        }
                    })
                }
            })
        }
        // 获取本地时间和三年后时间
        // const dateTime = (value) => {
        //     let nowData = new Date()
        //     let year = nowData.getFullYear() + value
        //     let month = nowData.getMonth() + 1 < 10 ? '0' + (nowData.getMonth() + 1) : nowData.getMonth() + 1
        //     let day = nowData.getDate() < 10 ? '0' + nowData.getDate() : nowData.getDate()
        //     return year + '-' + month + '-' + day
        // }
        const cancel = () => {
            emit('cancel')
        }
        const confirm = () => {
            emit('confirm')
        }
        const confirmClick = () => {
            for (let i = 0; i < columnsData.value.length; i++) {
                if (columnsData.value[i].isDelete == '1') {
                    message.warning('未确认完成')
                    return false
                }
            }
            let formData = ref<inObject>({})
            formData.value.staffId = staffStore().getStaff.staffId
            formData.value.clientId = signStore().getSign.clientId
            // if(contractStartDate.value) {
            //     formData.value.contractStartDate = contractStartDate.value
            // }else {
            //     formData.value.contractStartDate = dateTime(null)  // 获取本地时间
            // }
            // if(contractEndDate.value) {
            //     formData.value.contractEndDate = contractEndDate.value
            // }else {
            //     formData.value.contractEndDate = dateTime(3)  // 获取本地时间
            // }
            let api =
                viewType.value == 'electricSign'
                    ? '/api/hr-contracts/send-staff-contract'
                    : type.value == 'multiple'
                    ? '/api/hr-talent-staff/batch-renewal-send-confirm'
                    : '/api/hr-talent-staff/renewal-send-confirm'
            request
                .post(
                    api,
                    type.value == 'multiple'
                        ? {
                              clientId: signStore().getSign.clientId,
                              staffIds: staffStore().getStaff.staffList.map((el) => el.id),
                          }
                        : formData.value,
                    { loading: true },
                )
                .then((res) => {
                    console.log(res)
                    message.success('发送成功')
                    cancel()
                    confirm()
                })
        }

        return {
            activeKey,
            tableRef,
            Columns,
            contractVisible,
            contractTitle,
            currentValue,
            columnsData,
            confirmClick,
            confirm,
            sendCancel,
            sendConfirm,
            sendClick,
            delClick,
            backClick,
            stateConfirm,
            cancel,
            updataRow,
            uploadVisible,
            updateValue,
            updateCancel,
            updateConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.en-div {
    margin-bottom: 20px;
    .basicTable {
        max-height: 300px;
    }
}
.btn-flex {
    display: flex;
    justify-content: space-between;
}
</style>
