<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px" :footer="null">
        <!-- 必填附件 -->
        <div>
            <Enclosure :visible="visible" @cancel="cancel" :update="true" />
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { defineComponent, toRefs, watch } from 'vue'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import enclosure from './enclosure.vue'
// import signStore from '/@/store/modules/electricSign'

export default defineComponent({
    name: 'ElectricSign',
    components: {
        Enclosure: enclosure,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible } = toRefs<any>(props)
        watch(visible, () => {
            if (visible.value) {
            }
        })

        const cancel = () => {
            emit('cancel')
        }
        const confirm = () => {
            emit('confirm')
        }
        return {
            confirm,
            cancel,
        }
    },
})
</script>
<style scoped lang="less">
.btn-flex {
    display: flex;
    justify-content: space-between;
}
</style>
