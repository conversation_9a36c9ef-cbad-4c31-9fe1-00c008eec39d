<template>
    <div>
        <Tabs v-model:activeKey="activeKey" @change="tabsChange">
            <TabPane key="3" tab="选择合同模板">
                <Table
                    :columns="selectColumns"
                    :data-source="selectColumnsData"
                    :row-selection="rowSelection"
                    :pagination="false"
                    :row-key="(record) => record.id"
                />
            </TabPane>
            <TabPane key="2" tab="使用合同组">
                <Table
                    :columns="useColumns"
                    :data-source="useColumnsData"
                    :row-selection="rowSelectionRadio"
                    :pagination="false"
                    :row-key="(record) => record.id"
                />
            </TabPane>
            <TabPane key="1" tab="使用历史合同" v-if="viewType == 'electricSign'">
                <ClientSelectTree
                    class="select-tem"
                    v-model:value="clientId"
                    v-model:itemForm="clientSelectTreeData"
                    @change="getClient"
                />
                <!-- <Select
                    class="select-tem"
                    v-model:value="clientId"
                    :options="options"
                    placeholder="客户名称"
                    @change="getClient"
                /> -->
                <Table
                    :columns="historyColumns"
                    :data-source="historyColumnsData"
                    :row-selection="rowSelectionRadio"
                    :pagination="false"
                    :row-key="(record) => record.id"
                />
            </TabPane>
        </Tabs>
        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="nextClick" :loading="confirmLoading">下一步</Button>
        </div>
    </div>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'

import signStore from '/@/store/modules/electricSign'
import staffStore from '/@/store/modules/staff'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'SelectTemplate',
    components: {},
    props: {
        viewType: {
            type: String,
            default: 'electricSign',
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'changeTab'],
    setup(props, { emit }) {
        const { item, visible, viewType } = toRefs<any>(props)
        const activeKey = ref('1')
        const clientId = ref<any>(null)
        // const options = ref<object[]>([])
        const selectColumnsData = ref<inObject[]>([])
        const useColumnsData = ref<inObject[]>([])
        const historyColumnsData = ref<inObject[]>([])

        const confirmLoading = ref<Boolean>(false)

        onMounted(() => {})

        // 选择客户
        const getClient = (value) => {
            getOneTabTemplates(value)
        }
        const tabsChange = (value) => {
            signStore().setTemplateData({})
            activeKey.value = value
        }
        const clientSelectTreeData = {
            label: '客户',
            name: 'parentId',
            onChange: getClient,
        }
        //表格数据
        const historyColumns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '合同发起时间',
                dataIndex: 'contractInitDate',
                align: 'center',
            },
            {
                title: '员工姓名',
                dataIndex: 'staffName',
                align: 'center',
            },
            {
                title: '岗位',
                dataIndex: 'postName',
                align: 'center',
            },
        ]
        const useColumns = [
            {
                title: '合同组标题',
                dataIndex: 'contractGroupName',
                align: 'center',
            },
            {
                title: '文件数',
                dataIndex: 'contractTemplateSum',
                align: 'center',
            },
        ]
        const selectColumns = [
            {
                title: '模板标题',
                dataIndex: 'title',
                align: 'center',
            },
            {
                title: '模板类型',
                dataIndex: 'typeStr',
                align: 'center',
            },
        ]

        // 获取历史合同数据
        const getOneTabTemplates = (value) => {
            return new Promise((resolve) => {
                request.post('/api/hr-contracts/history', { clientId: value }).then((res) => {
                    resolve(res)
                    historyColumnsData.value = res
                })
            })
        }
        // 获取合同组数据
        const getTwoTabTemplates = () => {
            request.get('/api/hr-contract-groups/getHrCertificate/selectsum', {}).then((res) => {
                useColumnsData.value = res
            })
        }
        // 获取合同模板数据
        const getThreeTemplates = () => {
            request.get('/api/hr-contract-templates/list', {}).then((res) => {
                selectColumnsData.value = res
            })
        }

        //选择行
        let selectedRadioRows = ref('')
        let rowSelectionRadio: any = null
        let rowSelection: any = null

        let selectKeys = ref<(string | number)[]>([])
        // 单选行
        const rowSelectionRadioChange = (selectedRowKeys) => {
            selectKeys.value = selectedRowKeys
            let selectItemId = ref('')
            if (activeKey.value == '1') {
                for (let i = 0; i < historyColumnsData.value.length; i++) {
                    for (let j = 0; j < selectedRowKeys.length; j++) {
                        if (historyColumnsData.value[i].id == selectedRowKeys[j]) {
                            selectItemId.value = historyColumnsData.value[i].id
                        }
                    }
                }
            } else if (activeKey.value == '2') {
                for (let i = 0; i < useColumnsData.value.length; i++) {
                    for (let j = 0; j < selectedRowKeys.length; j++) {
                        if (useColumnsData.value[i].id == selectedRowKeys[j]) {
                            selectItemId.value = useColumnsData.value[i].id
                        }
                    }
                }
            }

            selectedRadioRows.value = selectItemId.value
        }
        rowSelectionRadio = {
            type: 'radio',
            selectedRowKeys: selectKeys,
            onChange: rowSelectionRadioChange,
        }

        let selectcheckKeys = ref<(string | number)[]>([])
        let selectedRowsArr = ref<object[]>([])
        // 多选行
        rowSelection = {
            type: 'checkbox',
            selectedRowKeys: selectcheckKeys,
            onChange: (selectedRowKeys) => {
                selectcheckKeys.value = selectedRowKeys
                let selectItemId = ref<object[]>([])
                for (let i = 0; i < selectColumnsData.value.length; i++) {
                    for (let j = 0; j < selectedRowKeys.length; j++) {
                        if (selectColumnsData.value[i].id == selectedRowKeys[j]) {
                            selectItemId.value.push(selectColumnsData.value[i].id)
                        }
                    }
                }
                selectedRowsArr.value = selectItemId.value
            },
        }

        watch(
            visible,
            () => {
                if (visible.value) {
                    clientId.value = null
                    if (signStore().getSign.tabs) {
                        activeKey.value = signStore().getSign.tabs
                    }
                    getOneTabTemplates('').then((ref: any) => {
                        if (activeKey.value == '1') {
                            // selectKeys.value = [ref[0].id]
                            rowSelectionRadioChange([ref[0].id])
                        }
                    })
                    getTwoTabTemplates()
                    getThreeTemplates()

                    // let selecteList = ref<object[]>([])
                    // let templateData = ref<inObject>({})
                    // if(templateData.value) {
                    //     // selectedRowsArr.value = signStore().getSign.staffContactList
                    //     selecteList.value = templateData.value?.contractList.map((item)=>{
                    //         return item.id
                    //     })
                    // }else {
                    //     selecteList.value = []
                    // }
                    // if(activeKey.value == '1' || activeKey.value == '2') {
                    //     selectKeys.value = selecteList.value
                    // }else if(activeKey.value == '3') {
                    //     selectcheckKeys.value = selecteList.value
                    // }
                }
            },
            { immediate: true },
        )

        const nextClick = () => {
            confirmLoading.value = true

            signStore().setTabs(activeKey.value)
            const formData = ref<inObject>({})
            formData.value.staffId = staffStore().getStaff.staffId
            formData.value.clientId = signStore().getSign.clientId
            formData.value.templateSource = activeKey.value // 合同来源方式

            if (activeKey.value == '3') {
                // return
                let laborContractList = selectColumnsData.value
                    ?.filter((el: inObject) => {
                        return selectedRowsArr.value?.includes(el.id)
                    })
                    ?.filter((el: inObject) => {
                        return el.type == 1
                    })
                confirmLoading.value = false
                // if (laborContractList.length > 1) {
                //     message.warning('请您只选择一项劳动合同!')
                //     return
                // }
                // 合同模板
                signStore().setContractTemplate(selectedRowsArr.value)
                if (viewType.value == 'renewal') {
                    emit('changeTab', '3')
                } else {
                    emit('changeTab', '2')
                }
            } else if (activeKey.value == '2' && viewType.value != 'renewal') {
                //合同组
                formData.value.contractGroupId = selectedRadioRows.value //合同组id
                request
                    .post('/api/hr-contracts/template_pretreatment', formData.value)
                    .then((res) => {
                        signStore().setTemplateData(res)
                        emit('changeTab', '3')
                    })
                    .finally(() => {
                        confirmLoading.value = false
                    })
            } else if (activeKey.value == '2' && viewType.value == 'renewal') {
                formData.value.contractGroupId = selectedRadioRows.value //合同组id
                let item = useColumnsData.value?.find((el) => el.id === selectedRadioRows.value)

                emit('changeTab', '3', item)
                confirmLoading.value = false
            } else if (activeKey.value == '1') {
                //历史合同
                formData.value.contractId = selectedRadioRows.value // 历史合同id
                request
                    .post('/api/hr-contracts/template_pretreatment', formData.value)
                    .then((res) => {
                        signStore().setTemplateData(res)
                        emit('changeTab', '3')
                    })
                    .finally(() => {
                        confirmLoading.value = false
                    })
            }
        }

        const cancel = () => {
            emit('cancel')
        }

        return {
            confirmLoading,

            activeKey,
            selectColumns,
            useColumns,
            historyColumns,
            clientId,
            // options,
            selectColumnsData,
            useColumnsData,
            historyColumnsData,
            rowSelection,
            rowSelectionRadio,
            selectedRowsArr,
            confirm,
            cancel,
            tabsChange,
            getClient,
            nextClick,

            clientSelectTreeData,
        }
    },
})
</script>
<style lang="less">
.ant-table-body .ant-table-thead > tr > th {
    background-color: #6894fe;
    color: #fff;
}
</style>
<style scoped lang="less">
.select-tem {
    width: 200px !important;
    margin-bottom: 20px;
}
</style>
