<template>
    <BasicEditModalSlot
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="80vw"
        :footer="null"
        style="top: 25px"
        modalHeight="inherit"
    >
        <div class="modal-pdf">
            <div class="pdf-box">
                <PdfPreview class="pdf" v-if="visible" :pdfUrl="pdfUrl" :urlParams="pdfUrlParams" />
            </div>
            <div class="form-box">
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ span: 12 }"
                    :wrapper-col="{ span: 24 }"
                    :rules="rules"
                    style="overflow-y: auto; margin-top: 20px"
                    class="form-flex"
                >
                    <template v-for="item in myOptions" :key="item">
                        <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                        <div class="pdf-btn-img" v-if="item.img">
                            <img :src="formData[item.name]" alt="" />
                        </div>
                    </template>
                </Form>
                <!-- <div class="buttonBox" v-if="myOptions?.length">
                    <Button type="primary" class="pdf-btn" @click="fillClick">一键填入</Button>
                </div> -->
            </div>
        </div>
        <div class="btn-bottom">
            <Button type="primary" @click="confirm">确定</Button>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { valuesAndRules } from '/#/component'
// import staffStore from '/@/store/modules/staff'
import PdfPreview from '/@/components/PdfPreview/src/PdfPreview.vue'
import signStore from '/@/store/modules/electricSign'
import staffStore from '/@/store/modules/staff'

export default defineComponent({
    name: 'SelectTemplate',
    components: { PdfPreview },
    props: {
        viewType: {
            type: String,
            default: 'electricSign',
        },
        type: {
            type: String,
            default: 'single',
            validator: (val: string) => {
                return ['single', 'multiple'].includes(val)
            },
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, visible, viewType, type } = toRefs<any>(props)
        const pdfUrl = ref('')
        const reason = ref('')
        const sealUrlList = ref<object[]>([])
        const sealImageUrl = ref('') // 选择的印章
        const pdfUrlParams = ref<any>(null)
        const selectObj = ref<any>({})

        const sealUrlChange = (value) => {
            sealImageUrl.value = value
        }

        const myOptions = ref<valuesAndRules[]>([])

        // 获取印章
        const getSchedules = () => {
            request.get('/api/hr-sealses/selectsealses', {}).then((res) => {
                res.forEach((item) => {
                    if (item.sealUrl) {
                        sealUrlList.value.push({ label: item.sealName, value: item.sealUrl })
                    }
                })
            })
        }
        const fillInfo = ref<inObject>([])
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值

        // Form Data
        const formData = ref<inObject>({})
        const rules = ref<inObject>({})
        const templateData = ref<inObject>({})

        const iframeAutoSetValue = (item) => {
            const iframeDocument = document.getElementsByName('iframe')
            let docItems = iframeDocument[0].contentDocument.getElementsByName(item.name)

            if (item?.type == 'date') {
                const dateKeys = [
                    'contractStartDate',
                    'contractEndDate',
                    'sendStartDate',
                    'probationStartDate',
                    'probationEndDate',
                    // 'firstPartSignatureDate',
                    // 'closingDate',
                    'promiseDate',
                ]
                if (dateKeys.includes(item.name)) {
                    const arr = ['Year', 'Month', 'Day']
                    const dateArr = formData.value[item.name].split('-')
                    arr.forEach((str, index) => {
                        const dateDoms = iframeDocument[0].contentDocument.getElementsByName(`${item.name}${str}`)
                        if (dateDoms.length) {
                            dateDoms.forEach((el) => {
                                el.value = dateArr[index]
                            })
                        }
                    })
                } else {
                    if (docItems.length) {
                        docItems.forEach((el) => {
                            el.value = formData.value[item.name]
                        })
                    }
                }
            } else if (item?.type == 'change') {
                if (docItems.length) {
                    if (item.name != 'seal') {
                        docItems.forEach((el) => {
                            el.value = item?.options?.find((el) => (el.value = formData.value[item.name]))?.label || ''
                        })
                    }
                }
            } else {
                if (docItems.length) {
                    docItems.forEach((el) => {
                        el.value = formData.value[item.name]
                    })
                }
            }
        }
        /**
         * 计算N年后,YYYYMMDD
         * startdate：为开始时间，格式YYYYMMDD
         * nextYear：为间隔年月，如1表示一年后，2表示两年后
         */
        const getAfterNYear = (startDate, nextYear) => {
            var expriedYear = parseInt(startDate.substring(0, 4)) + Number(nextYear)
            var expriedMonth = startDate.substring(4, 6)
            var expriedDay = startDate.substring(6)
            //考虑二月份场景，若N年后的二月份日期大于该年的二月份的最后一天，则取该年二月份最后一天
            if (expriedMonth == '02' || expriedMonth == 2) {
                var monthEndDate = new Date(expriedYear, expriedMonth, 0).getDate()
                if (parseInt(expriedDay) > monthEndDate) {
                    //为月底时间
                    //取两年后的二月份最后一天
                    expriedDay = monthEndDate
                }
            }
            return expriedYear + expriedMonth + expriedDay
        }

        watch(
            visible,
            () => {
                if (visible.value) {
                    fillInfo.value = JSON.parse(item.value?.firstPartInfo)
                    myOptions.value = fillInfo.value?.map((item) => {
                        return {
                            ...item,
                            default: item?.value,
                            type: item?.type ? item.type : item?.value ? typeof item.value : 'string',
                            ruleType: item?.value ? typeof item.value : 'string',
                            onChange: (e, option) => {
                                nextTick(() => {
                                    iframeAutoSetValue(item)
                                    if (item.type == 'change' && item.name != 'seal') {
                                        selectObj.value[item.name] = option.label
                                    }
                                    if (item.name == 'contractDeadlineYears') {
                                        const month = fillInfo.value.find((i) => i.name == 'contractDeadlineMonth')
                                        const endDate = fillInfo.value.find((i) => i.name == 'contractEndDate')
                                        if (month) {
                                            formData.value.contractDeadlineMonth = formData.value.contractDeadlineYears * 12
                                            iframeAutoSetValue(month)
                                        }
                                        if (endDate) {
                                            formData.value.contractEndDate = getAfterNYear(
                                                formData.value.contractStartDate,
                                                formData.value.contractDeadlineYears,
                                            )
                                            iframeAutoSetValue(endDate)
                                        }
                                    }
                                })
                            },
                        }
                    })
                    pdfUrl.value = item.value.appendixPath
                    pdfUrlParams.value = { fillInfo: item.value.firstPartInfo }

                    const { values: initFormData, rules: initRules } = getValuesAndRules(myOptions.value)

                    formData.value = initFormData
                    rules.value = initRules

                    templateData.value = signStore().getSign.templateData
                }
            },
            { immediate: true },
        )

        const resetFormData = () => {
            formData.value = {}
            formInline.value.resetFields()
        }
        const fillClickRes = ref<inObject>({})
        // 一键填入
        const fillClick = () => {
            request
                .post('/api/hr-contract-templates/make-template', {
                    inputLabel: formData.value,
                    templateId: item.value.templateId,
                })
                .then((res) => {
                    fillClickRes.value = res
                    pdfUrl.value = res.fileUrl
                })
        }

        // 确认
        const confirm = () => {
            const firstPart = fillInfo.value.map((el) => {
                return { ...el, value: { ...formData.value, ...selectObj.value }?.[el.name] || '' }
            })
            let generateData = {}
            let api = '/api/hr-contracts/generate-contract-confirmation'
            if (viewType.value == 'electricSign') {
                api = '/api/hr-contracts/generate-contract-confirmation'
            } else {
                if (type.value == 'multiple') {
                    api = '/api/hr-talent-staff/batch-renewal-confirmation'
                    generateData = {
                        staffIds: staffStore().getStaff.staffList.map((el) => el.id) || [],
                        clientId: staffStore().getStaff.staffList[0].clientId || '',
                        templateId: item.value?.templateId || '',
                        inputLabel: JSON.stringify(formData.value),
                    }
                } else {
                    api = '/api/hr-talent-staff/renewal-contract-confirmation'
                    generateData = {
                        staffId: staffStore().getStaff.staffId,
                        clientId: signStore().getSign.clientId,
                    }
                }
            }
            request
                .post(
                    api,
                    {
                        id: item.value.id,
                        appendixId: fillClickRes.value.id,
                        appendixPath: fillClickRes.value.fileUrl,
                        firstPartInfo: firstPart.length ? JSON.stringify(firstPart) : undefined,
                        ...generateData,
                    },
                    { loading: true },
                )
                .then((res) => {
                    templateData.value.contractList.forEach((el) => {
                        if (el.id == item.value.id) {
                            el.isDelete = 0
                        }
                    })
                    signStore().setTemplateData(templateData.value)
                    fillInfo.value.forEach((el) => {
                        Object.keys(formData.value).forEach((it) => {
                            if (el.name == it) {
                                el.value = formData.value[it]
                            }
                        })
                    })
                    let par = JSON.stringify(fillInfo.value)
                    emit('cancel')
                    emit('confirm',par)
                    resetFormData()
                })
        }

        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            pdfUrlParams,
            reason,
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            pdfUrl,
            sealImageUrl,
            confirm,
            cancel,
            fillClick,
        }
    },
})
</script>
<style scoped lang="less">
.modal-pdf {
    display: flex;
    .pdf-box {
        height: 75vh;
        width: 50%;
        .pdf {
            height: 75vh;
        }
    }
    .form-box {
        max-height: 75vh;
        width: 47%;
        border: 1px solid #d9d9d9;
        overflow-y: auto;
        position: relative;
        padding: 20px;
        margin-left: 20px;
        box-sizing: border-box;
        .reason {
            margin: 15px 0;
            & > span {
                font-weight: 600;
                display: inline-block;
                margin-bottom: 15px;
            }
        }
        .buttonBox {
            position: sticky;
            bottom: 0;
            background-color: #fff;
            z-index: 999;
            display: flex;
            justify-content: center;
        }
    }
    .form-flex {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        align-items: flex-start;
        position: relative;
        p {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        :deep(.ant-form-item) {
            width: 62%;
        }
        :deep(.img) {
            position: absolute;
            right: 1%;
        }
    }
    .pdf-btn {
        // margin-left: 130px;
        margin-bottom: 30px;
    }
    .pdf-btn-img {
        margin-left: 130px;
        margin-bottom: 30px;
        img {
            height: 150px;
            width: 150px;
        }
    }
}
.btn-bottom {
    text-align: right;
    margin-top: 20px;
    button + button {
        margin-left: 15px;
    }
}
</style>
