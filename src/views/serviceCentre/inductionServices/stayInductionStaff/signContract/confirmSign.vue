<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px">
        <Table :columns="columns" :data-source="columnsData" boder bordered :pagination="false" :row-key="(record) => record.id">
            <template #operation="{ record }">
                <Button type="primary" size="small" @click="editRow(record)">查看</Button>
            </template>
        </Table>
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '130px' } }"
            :rules="rules"
            style="overflow-y: auto; margin-top: 20px"
            class="form-flex"
        >
            <template v-for="itemForm in myOptions" :key="itemForm">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]" />
            </template>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="confirmationTelegram">确定</Button>
        </template>
    </BasicEditModalSlot>
    <ContractPdf
        :visible="showPdf"
        v-if="showPdf"
        :title="'劳动合同'"
        :item="currentPdfValue"
        @cancel="pdfCancel"
        @confirm="pdfConfirm"
    />
    <MustFile
        :visible="showMust"
        v-if="showMust"
        :title="'附件'"
        :item="currentMustValue"
        @cancel="mustCancel"
        @confirm="mustConfirm"
    />
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, h, computed } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import staffStore from '/@/store/modules/staff'
import contractPdf from './contractPdf.vue'
import mustFile from './mustFile.vue'
import { message } from 'ant-design-vue'
import { filter } from 'jszip'
export default defineComponent({
    name: 'ElectricSign',
    components: {
        ContractPdf: contractPdf,
        MustFile: mustFile,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: 'electricSign',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, visible, viewType } = toRefs<any>(props)
        const columnsData = ref<inObject[]>([])
        const educationList = computed(() => {
            let education: inObject[] = []
            let contractFail = false
            let otherFail = false
            let allPass = true
            columnsData.value?.forEach((item) => {
                if (item.state == 3) {
                    otherFail = true
                }

                if (item.state != 2) {
                    allPass = false
                }
            })

            //当有一项审核不通过时不能选择通过
            if (otherFail) {
                education = [
                    { value: 1, label: '通知员工修改' },
                    { value: 2, label: '作废' },
                ]
            } else if (allPass) {
                education = [{ value: 3, label: '通过' }]
            } else {
                education = [
                    { value: 1, label: '通知员工修改' },
                    { value: 2, label: '作废' },
                    { value: 3, label: '通过' },
                ]
            }
            return education
        })
        //表格dom
        const tableRef = ref()
        //表格数据
        const columns = [
            {
                title: '名称',
                dataIndex: 'name',
                align: 'center',
            },
            {
                title: '审核结果',
                dataIndex: 'state',
                align: 'center',
                customRender: ({ text }) => {
                    let name = ''
                    switch (text) {
                        case 2:
                            name = '通过'
                            break
                        case 3:
                            name = '未通过'
                            break
                        case 4:
                            name = '重签'
                            break
                        case 5:
                            name = '作废'
                            break
                        default:
                            name = ''
                    }
                    return h(
                        'span',
                        {
                            style: 'color:' + (text == 2 ? 'green;' : 'red;'),
                        },
                        name,
                    )
                },
            },
            {
                title: '原因',
                dataIndex: 'msg',
                align: 'center',
                width: 450,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
            },
        ]

        // 劳动合同弹窗
        const showPdf = ref<Boolean>(false)
        const currentPdfValue = ref<any>(null)
        const showMust = ref<Boolean>(false)
        const currentMustValue = ref(null)
        const editRow = (record) => {
            if (record.type == 1) {
                currentPdfValue.value = record
                showPdf.value = true
            } else {
                currentMustValue.value = record
                showMust.value = true
            }
        }
        const pdfCancel = () => {
            showPdf.value = false
            currentPdfValue.value = null
        }
        const pdfConfirm = (itemInfo, msg, state) => {
            pdfCancel()
            getData(item.value?.clientId, item.value?.id)
            if (state == 3) {
                let checkMsg = formData.value?.checkMsg || ''
                checkMsg += itemInfo.name + '：' + msg + '；'
                formData.value.checkMsg = checkMsg

                if (formData.value?.checkResult) {
                    formData.value.checkResult = ''
                }
            }
            // checkMsg
        }
        // 附件弹窗
        const mustCancel = () => {
            showMust.value = false
            currentMustValue.value = null
        }
        const mustConfirm = (itemInfo, msg, state) => {
            mustCancel()
            getData(item.value?.clientId, item.value?.id)
            if (state == 3) {
                let checkMsg = formData.value?.checkMsg || ''
                checkMsg += itemInfo.name + '：' + msg + '；'
                formData.value.checkMsg = checkMsg

                if (formData.value?.checkResult) {
                    formData.value.checkResult = ''
                }
            }
            // checkMsg
        }
        const myOptions = ref([
            {
                label: '审核信息',
                name: 'checkMsg',
                required: false,
                width: '100%',
                type: 'textarea',
            },
            {
                label: '审核结果',
                name: 'checkResult',
                type: 'change',
                options: educationList,

                ruleType: 'number',
                required: false,
            },
        ])
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(JSON.parse(JSON.stringify(initFormData)))

        const resetFormData = () => {
            formData.value = JSON.parse(JSON.stringify(initFormData))
            formInline.value.resetFields()
        }
        // 获取基本信息详情
        const getData = (clientId, staffId) => {
            request
                .get(`/api/hr-contracts/confirmation-telegram/${clientId}/${staffId}`, {})
                .then((res) => {
                    // formData.value = JSON.parse(JSON.stringify(initFormData))
                    columnsData.value = res.filter((item: any) => {
                        return item.appendixId
                    })
                    // appendixId
                })
                .catch(() => {
                    formData.value = JSON.parse(JSON.stringify(initFormData))
                    columnsData.value = []
                })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    getData(item.value?.clientId, item.value?.id)
                }
            },
            { immediate: true },
        )

        const cancel = () => {
            formData.value = JSON.parse(JSON.stringify(initFormData))
            formInline.value.resetFields()
            emit('cancel')
        }
        const confirmationTelegram = () => {
            let NotApproved = false
            columnsData.value?.forEach((item) => {
                if (item.state == 1) {
                    NotApproved = true
                }
            })
            if (NotApproved) {
                message.error('请对每一项进行审核!')
                return
            }
            if (!formData.value?.checkResult) {
                message.error('请选择审核结果!')
                return
            }
            if (formData.value?.checkResult != 3) {
                if (!formData.value?.checkMsg) {
                    message.error('请输入审核信息!')
                    return
                }
            }
            let api = '/api/hr-contracts/confirmation-telegram'
            if (viewType.value == 'electricSign') {
                api = '/api/hr-contracts/confirmation-telegram'
            } else {
                api = '/api/hr-talent-staff/renewal-confirmation-telegram'
            }
            request
                .post(
                    api,
                    {
                        id: columnsData.value[0].contractId,
                        ...formData.value,
                        // signId: formData.value.checkResult === 3 ? localStorage.getItem('signId') : undefined,
                    },
                    { loading: true },
                )
                .then((res) => {
                    console.log(res)
                    message.success('审核完成!')
                    localStorage.signId = ''
                    cancel()
                    emit('confirm')
                })
        }
        return {
            tableRef,
            columns,
            columnsData,
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            confirm,
            cancel,
            editRow,
            showPdf,
            currentPdfValue,
            pdfCancel,
            pdfConfirm,
            contractPdf,
            showMust,
            currentMustValue,
            mustCancel,
            mustConfirm,

            confirmationTelegram,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
</style>
