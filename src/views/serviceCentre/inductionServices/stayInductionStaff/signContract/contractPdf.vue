<template>
    <BasicEditModalSlot
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="80vw"
        :footer="null"
        style="top: 25px"
        modalHeight="inherit"
    >
        <div class="modal-pdf">
            <div class="pdf-box">
                <PdfPreview class="pdf" :pdfUrl="item?.appendixPath" />
            </div>
            <div class="examine" v-if="item?.state == 1 || item?.state == 2 || item?.state == 3">
                <div v-if="item?.templateType == 1">
                    <FormItem label="选择印章1">
                        <Select
                            v-model:value="formData.seal"
                            allowClear
                            showSearch
                            style="width: 200px"
                            optionFilterProp="label"
                            placeholder="请选择印章"
                            :options="sealsList"
                            @change="sealUrlChange"
                        />
                    </FormItem>
                    <div class="pdf-btn-img">
                        <img class="img" v-if="formData.sealImg && showImg" :src="formData.sealImg" alt="" />
                        <div class="img" v-else>印章图片</div>
                    </div>
                </div>
                <div class="reason">
                    <Divider type="vertical" class="divid" />
                    <span>审核失败原因</span>
                    <div class="examine-area">
                        <Textarea v-model:value="msg" :rows="3" placeholder="请输入审核失败原因" />
                    </div>
                </div>
            </div>
        </div>
        <div class="ant-modal-footer" v-if="item?.state == 1 || item?.state == 2 || item?.state == 3">
            <Button danger type="primary" @click="rejectRow">审核失败</Button>
            <Button type="primary" @click="confirm">审核通过</Button>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, onUnmounted, watch, nextTick } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import staffStore from '/@/store/modules/staff'
import PdfPreview from '/@/components/PdfPreview/src/PdfPreview.vue'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'ElectricSign',
    components: { PdfPreview },
    props: {
        title: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { title, item, visible } = toRefs<any>(props)
        const showImg = ref(true)
        const formData = ref<any>({})
        const sealsList = ref([])

        const pdfUrl = ref('')
        const msg = ref('')

        const sealUrlChange = (val, option) => {
            showImg.value = false
            formData.value.sealImg = option.sealUrl
            showImg.value = true
        }
        const cancel = () => {
            emit('cancel')
        }
        const rejectRow = () => {
            if (!msg.value) {
                message.error('请输入审核失败原因！')
                return
            }
            funRequest(3)
        }
        const confirm = () => {
            if (!formData.value.seal && item.value?.templateType == 1) {
                message.error('请选择印章！')
                return
            }
            funRequest(2)
        }
        const funRequest = (state) => {
            request
                .post(`/api/hr-contracts/single-check`, {
                    id: item.value.id,
                    msg: msg.value,
                    state,
                    sealSignId: formData.value.seal,
                })
                .then((res) => {
                    message.success('审核完成!')
                    if (item.value?.templateType == 1 && state === 2) localStorage.signId = formData.value.seal
                    emit('confirm', item.value, msg.value, state)
                })
        }
        onMounted(async () => {
            await getSealsList()
            if (item.value.type == 1) {
                nextTick(() => {
                    formData.value.seal = item.value.sealSignId ?? '16754324'
                    let it: any = sealsList.value.find((el: any) => item.value.sealSignId == el.signId)
                    formData.value.sealImg =
                        it.sealUrl ?? 'https://hr-server.hdqhr.com:10443/minio/hrresources/253bbd7f844b4f02b9186e696db629df.png'
                })
            }
        })
        onUnmounted(() => {
            formData.value = {}
        })
        const getSealsList = async () => {
            const list = await request.get('/api/hr-sealses/list')
            sealsList.value = list.map((el) => {
                return { ...el, value: el.signId, label: el.sealName }
            })
        }

        return {
            pdfUrl,
            msg,
            formData,
            sealsList,
            sealUrlChange,
            cancel,
            confirm,
            rejectRow,
            showImg,
        }
    },
})
</script>
<style scoped lang="less">
.modal-pdf {
    display: flex;
    .pdf-box {
        height: 75vh;
        width: 50%;
        .pdf {
            height: 100%;
        }
    }
    .examine {
        max-height: 75vh;
        width: 47%;
        border: 1px solid #d9d9d9;
        overflow-y: auto;
        position: relative;
        padding: 20px;
        box-sizing: border-box;
        margin-left: 20px;
        .pdf-btn-img {
            margin-left: 130px;
            margin-bottom: 30px;
            .img {
                height: 150px;
                width: 150px;
                background-color: rgb(245, 245, 245);
                text-align: center;
                line-height: 150px;
            }
        }
        .divid {
            border-left: 3px solid #1890ff;
            height: 26px;
        }
        .reason {
            position: absolute;
            bottom: 50px;
            left: 20px;
            right: 20px;
        }
        .examine-area {
            margin: 20px 0px 20px 20px;
        }
    }
}
.ant-modal-footer {
    text-align: right;
    margin-top: 20px;
}
</style>
