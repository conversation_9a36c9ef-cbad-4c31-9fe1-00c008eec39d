<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px" :footer="null">
        <!-- 选择合同 -->
        <div v-show="tab == 1">
            <SelectTemplate :visible="visible" @changeTab="changeTab" @cancel="cancel" />
        </div>
        <!-- 必填附件 -->
        <div v-if="tab == 2">
            <Enclosure :visible="visible" :update="false" @changeTab="changeTab" @cancel="cancel" />
        </div>
        <!-- 发送电签 -->
        <div v-if="tab == 3">
            <SendConfirm :visible="visible" @changeTab="changeTab" @cancel="cancel" @confirm="confirm" />
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import selectTemplate from './selectTemplate.vue'
import enclosure from './enclosure.vue'
import sendConfirm from './sendConfirm.vue'
// import signStore from '/@/store/modules/electricSign'

export default defineComponent({
    name: 'ElectricSign',
    components: {
        SelectTemplate: selectTemplate,
        Enclosure: enclosure,
        SendConfirm: sendConfirm,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible } = toRefs<any>(props)
        const tab = ref(1)
        watch(visible, () => {
            if (visible.value) {
                tab.value = 1
            }
        })
        const changeTab = (value) => {
            tab.value = value
        }

        const cancel = () => {
            emit('cancel')
        }
        const confirm = () => {
            emit('confirm')
        }
        return {
            tab,
            confirm,
            cancel,
            changeTab,
        }
    },
})
</script>
<style scoped lang="less">
.btn-flex {
    display: flex;
    justify-content: space-between;
}
</style>
