<template>
    <div class="en-div">
        <p>必传附件</p>
        <Table
            :columns="accessoriesColumns"
            :data-source="contractRequiredAccessoriesData"
            :pagination="false"
            :row-key="(record) => record.id"
            bordered
            :row-selection="contractRequiredAccessoriesRow"
        />
    </div>
    <div>
        <p>可传附件</p>
        <Table
            :columns="accessoriesColumns"
            :data-source="contractOptionalAccessoriesData"
            :pagination="false"
            :row-key="(record) => record.id"
            bordered
            :row-selection="contractOptionalAccessoriesRow"
        />
    </div>
    <div class="ant-modal-footer" v-if="!update">
        <Button type="primary" @click="backClick">上一步</Button>
        <Button type="primary" @click="nextClick" :loading="confirmLoading">下一步</Button>
    </div>
    <div class="ant-modal-footer" v-if="update">
        <Button type="primary" @click="cancel">取消</Button>
        <Button type="primary" @click="confirmClick" :loading="confirmLoading">确定</Button>
    </div>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import staffStore from '/@/store/modules/staff'
import signStore from '/@/store/modules/electricSign'
import { fileTypeList } from '/@/utils/dictionaries'
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}

export default defineComponent({
    name: 'SelectTemplate',
    components: {},
    props: {
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        update: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'changeTab'],
    setup(props, { emit }) {
        const { item, visible, update } = toRefs<any>(props)
        let ifDataistrue = false

        const confirmLoading = ref<Boolean>(false)
        onMounted(() => {
            // 获取必填和可填列表
            request.get('/api/hr-contract-groups/getHrCertificate', {}).then((res) => {
                contractRequiredAccessoriesData.value = res.map((item) => {
                    return { ...item, disabled: false }
                })
                contractOptionalAccessoriesData.value = res.map((item) => {
                    return { ...item, disabled: false }
                })
                if (ifDataistrue) {
                    contractRequiredAccessoriesRowChange(contractRequiredAccessoriesKeys.value)
                    contractOptionalAccessoriesRowChange(contractOptionalAccessoriesKeys.value)
                } else {
                    ifDataistrue = true
                }
            })
        })

        //表格dom
        const tableRef = ref()
        const accessoriesColumns = [
            {
                title: '名称',
                dataIndex: 'certificateName',
                align: 'center',
            },
            {
                title: '类型',
                dataIndex: 'certificateType',
                align: 'center',
                customRender: ({ text }) => {
                    return fileTypeList.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
        ]
        const contractRequiredAccessoriesData = ref<inObject[]>([])
        const contractOptionalAccessoriesData = ref<inObject[]>([])
        let mustRowsArr = ref<inObject[]>([])
        let moreRowsArr = ref<inObject[]>([])

        let contractRequiredAccessoriesKeys = ref<(string | number)[]>([])
        const contractRequiredAccessoriesRowChange = (selectedRowKeys: (string | number)[], selectedRows?: DataItem[]) => {
            let selectItem = ref<inObject[]>([])
            contractRequiredAccessoriesKeys.value = selectedRowKeys
            contractOptionalAccessoriesData.value.map((item: inObject) => {
                item.disabled = selectedRowKeys.includes(item.id)
                return item
            })
            for (let i = 0; i < contractOptionalAccessoriesData.value.length; i++) {
                for (let j = 0; j < selectedRowKeys.length; j++) {
                    if (contractOptionalAccessoriesData.value[i].id == selectedRowKeys[j]) {
                        selectItem.value.push(contractOptionalAccessoriesData.value[i].id)
                    }
                }
            }
            mustRowsArr.value = selectItem.value
            contractOptionalAccessoriesData.value = [...contractOptionalAccessoriesData.value]
        }
        const contractRequiredAccessoriesRow = {
            // 必传
            selectedRowKeys: contractRequiredAccessoriesKeys,
            onChange: contractRequiredAccessoriesRowChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled, // Column configuration not to be checked
                }
            },
        }
        let contractOptionalAccessoriesKeys = ref<(string | number)[]>([])
        const contractOptionalAccessoriesRowChange = (selectedRowKeys: (string | number)[], selectedRows?: DataItem[]) => {
            let selectItem = ref<inObject[]>([])
            contractOptionalAccessoriesKeys.value = selectedRowKeys

            contractRequiredAccessoriesData.value.map((item: inObject) => {
                item.disabled = selectedRowKeys.includes(item.id)
                return item
            })
            for (let i = 0; i < contractRequiredAccessoriesData.value.length; i++) {
                for (let j = 0; j < selectedRowKeys.length; j++) {
                    if (contractRequiredAccessoriesData.value[i].id == selectedRowKeys[j]) {
                        selectItem.value.push(contractRequiredAccessoriesData.value[i].id)
                    }
                }
            }
            moreRowsArr.value = selectItem.value
            contractRequiredAccessoriesData.value = [...contractRequiredAccessoriesData.value]
        }
        const contractOptionalAccessoriesRow = {
            // 可传
            selectedRowKeys: contractOptionalAccessoriesKeys,
            onChange: contractOptionalAccessoriesRowChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled, // Column configuration not to be checked
                }
            },
        }

        watch(
            visible,
            () => {
                if (visible.value) {
                    let must = ref<string | number[]>([])
                    let more = ref<string | number[]>([])
                    if (signStore().getSign.templateData?.requiredList) {
                        mustRowsArr.value = signStore().getSign.templateData?.requiredList
                        must.value = mustRowsArr.value.map((item) => {
                            return item.templateId
                        })
                    } else {
                        must.value = []
                    }
                    if (signStore().getSign.templateData?.optionalList) {
                        moreRowsArr.value = signStore().getSign.templateData?.optionalList
                        more.value = moreRowsArr.value.map((item) => {
                            return item.templateId
                        })
                    } else {
                        more.value = []
                    }

                    contractRequiredAccessoriesKeys.value = must.value
                    contractOptionalAccessoriesKeys.value = more.value
                    if (ifDataistrue) {
                        contractRequiredAccessoriesRowChange(must.value)
                        contractOptionalAccessoriesRowChange(more.value)
                    } else {
                        ifDataistrue = true
                    }
                }
            },
            { immediate: true },
        )
        const getTemplate = (row) => {
            confirmLoading.value = true
            const formData = ref<inObject>({})
            formData.value.staffId = staffStore().getStaff.staffId
            formData.value.clientId = signStore().getSign.clientId
            formData.value.templateSource = '3' // 合同来源方式
            formData.value.templateIds = signStore().getSign.staffContactList //合同模板Ids
            formData.value.requiredAccessories = mustRowsArr.value //必传附件IDs
            formData.value.optionalAccessories = moreRowsArr.value //可传附件IDs
            if (update.value) {
                formData.value.contractId = signStore().getSign.templateData?.contractList[0]?.contractId || ''
            }
            // return
            let api = update.value ? '/api/hr-contracts/update-contract-appendix' : '/api/hr-contracts/template_pretreatment'
            request
                .post(api, formData.value)
                .then((res) => {
                    signStore().setTemplateData(res)
                    if (row == 'next') {
                        emit('changeTab', '3')
                    } else {
                        emit('cancel')
                    }
                })
                .finally(() => {
                    confirmLoading.value = false
                })
        }
        const nextClick = () => {
            getTemplate('next')
        }
        const backClick = () => {
            emit('changeTab', '1')
        }

        const cancel = () => {
            emit('cancel')
        }
        const confirmClick = () => {
            // signStore().setMustData(mustRowsArr.value)
            // signStore().setMoreData(moreRowsArr.value)
            getTemplate('cancel')
        }

        return {
            confirmLoading,

            tableRef,
            accessoriesColumns,
            contractRequiredAccessoriesData,
            contractOptionalAccessoriesData,

            contractRequiredAccessoriesRow,
            contractOptionalAccessoriesRow,
            confirm,
            nextClick,
            cancel,
            backClick,
            confirmClick,
        }
    },
})
</script>
<style scoped lang="less">
.en-div {
    margin-bottom: 20px;
    .basicTable {
        max-height: 300px;
    }
}
</style>
