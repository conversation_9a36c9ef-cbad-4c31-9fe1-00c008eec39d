<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="800px" :footer="null">
        <div class="uploadBox">
            <div class="item" :class="certificateAttribute == 1 ? 'IdCardPicture' : ''" v-for="(el, i) in appendixPaths" :key="i">
                <a :href="i.fileUrl" target="_blank" @click="previewFile(el.fileUrl)">
                    <img
                        v-if="['.jpg', '.png', '.jpeg'].includes(el.fileUrl?.substring(el.fileUrl?.lastIndexOf('.')))"
                        class="img"
                        :src="el.fileUrl"
                        alt=""
                    />
                    <div v-else class="txt">{{ el.originName }}</div>
                </a>
                <div class="after">
                    <div class="txt">{{ el.originName }}</div>
                    <Button size="small" type="primary" @click="download(el.fileUrl, el.originName)"> 下载 </Button>
                </div>
            </div>
        </div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>审核失败原因</span>
            <div class="examine-area">
                <Textarea v-model:value="msg" placeholder="请输入审核失败原因" />
            </div>
            <div class="ant-modal-footer" style="margin-top: 60px">
                <Button danger type="primary" @click="rejectRow">审核失败</Button>
                <Button type="primary" @click="confirm">审核通过</Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, onMounted } from 'vue'
import request from '/@/utils/request'

import { message } from 'ant-design-vue'
import downFile from '/@/utils/downFile'
import { previewFile } from '/@/utils/index'
export default defineComponent({
    name: 'ElectricSign',
    components: {},
    props: {
        title: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item } = toRefs<any>(props)
        let appendixPaths = ref([])
        const certificateAttribute = ref()
        onMounted(() => {
            let appendixPath = item.value?.appendixPath?.split(',') || []
            appendixPaths.value = appendixPath.map((el, index) => {
                // "身份证"
                let originName = ''
                certificateAttribute.value = item.value?.hrCertificate?.certificateAttribute
                if (item.value?.hrCertificate?.certificateAttribute == 1) {
                    originName = index == 0 ? '身份证正面照片' : '身份证反面照片'
                } else {
                    originName = item.value?.name + '(' + (index + 1) + ')'
                }
                return {
                    fileUrl: el,
                    originName,
                }
            })
        })
        const msg = ref('')
        const cancel = () => {
            emit('cancel')
        }
        const rejectRow = () => {
            funRequest(3)
        }
        const confirm = () => {
            funRequest(2)
        }
        const funRequest = (state) => {
            request.post(`/api/hr-contracts/single-check`, { id: item.value.id, msg: msg.value, state }).then((res) => {
                message.success('审核完成!')
                emit('confirm', item.value, msg.value, state)
            })
        }
        const download = (url, name) => {
            downFile('get', url, name, {})
        }
        return {
            //文件类型
            certificateAttribute,

            appendixPaths,
            cancel,
            confirm,
            rejectRow,
            msg,

            download,
            previewFile,
        }
    },
})
</script>
<style scoped lang="less">
.uploadBox {
    align-items: flex-start;
    min-height: 40vh;
    .after {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        .txt {
            width: 100%;
            color: #000;
        }
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
}
.IdCardPicture {
    width: 135.5px;
}
</style>
