<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1300px">
        <div class="step-detail">
            <Steps :current="applyStep" labelPlacement="vertical" class="my_steps">
                <Step title="发送入职通知" />
                <Step title="员工录入信息" />
                <Step title="发起劳动合同" />
                <Step title="签署劳动合同" />
                <Step title="入职完成" />
            </Steps>
        </div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>申请信息</span>
            <div class="examine-flex">
                <div class="item-flex">
                    <span>客户编号：</span>
                    <span>{{ detailData.unitNumber }}</span>
                </div>
                <div class="item-flex">
                    <span>客户名称：</span>
                    <span>{{ detailData.clientName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span>协议编号：</span>
                    <span>{{ detailData.agreementNumber }}</span>
                </div>
                <div class="item-flex">
                    <span>协议标题：</span>
                    <span>{{ detailData.agreementTitle }}</span>
                </div>
                <div class="item-flex">
                    <span>协议类型：</span>
                    <span>{{ detailData.businessTypeLabel }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span>员工姓名：</span>
                    <span>{{ detailData.name }}</span>
                </div>
                <div class="item-flex">
                    <span>身份证号：</span>
                    <span>{{ detailData.certificateNum }}</span>
                </div>
                <div class="item-flex">
                    <span>性别：</span>
                    <span>{{ detailData.sexLabel }}</span>
                </div>
                <div class="item-flex">
                    <span>联系方式：</span>
                    <span>{{ detailData.phone }}</span>
                </div>
                <div class="item-flex">
                    <span>岗位：</span>
                    <span>{{ detailData.professionName }}</span>
                </div>
                <div class="item-flex">
                    <span>人员类型：</span>
                    <span>{{ detailData.personnelTypeLabel }}</span>
                </div>
                <div class="item-flex">
                    <span>合同开始日期：</span>
                    <span>{{ detailData.contractStartDate }}</span>
                </div>
                <div class="item-flex">
                    <span>合同结束日期：</span>
                    <span>{{ detailData.contractEndDate }}</span>
                </div>
                <div class="item-flex">
                    <span>基本工资：</span>
                    <span>{{ detailData.basicWage }}</span>
                </div>
                <!-- <div class="item-flex">
                    <span>医保基数：</span>
                    <span>{{ detailData.medicalInsuranceCardinal }}</span>
                </div> -->

                <div class="item-flex">
                    <span>公积金基数：</span>
                    <span>{{ detailData.accumulationFundCardinal }}</span>
                </div>
                <template v-if="cardinalArr.length > 0">
                    <div class="item-flex" v-for="(item, index) in cardinalArr" :key="index">
                        <span class="span1">{{ item.title }}</span>
                        <span>：{{ detailData[item.dataIndexS[0]] }}</span>
                    </div>
                </template>
                <template v-if="cardinalArr.length == 0">
                    <div class="item-flex">
                        <span>单位大额医疗费用：</span>
                        <span>{{ detailData.unitLargeMedicalExpense }}</span>
                    </div>
                    <div class="item-flex">
                        <span>补充工伤费用：</span>
                        <span>{{ detailData.replenishWorkInjuryExpense }}</span>
                    </div>
                    <div class="item-flex">
                        <span>个人大额医疗费用：</span>
                        <span>{{ detailData.personalLargeMedicalExpense }}</span>
                    </div>
                    <div class="item-flex">
                        <span>单位养老基数：</span>
                        <span>{{ detailData.unitPensionCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>单位失业基数：</span>
                        <span>{{ detailData.unitUnemploymentCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>单位医疗基数：</span>
                        <span>{{ detailData.medicalInsuranceCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>单位生育基数：</span>
                        <span>{{ detailData.unitMaternityCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>单位工伤基数：</span>
                        <span>{{ detailData.workInjuryCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>个人生育基数：</span>
                        <span>{{ detailData.personalMaternityCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>个人医疗基数：</span>
                        <span>{{ detailData.medicalInsuranceCardinalPersonal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>个人养老基数：</span>
                        <span>{{ detailData.personalPensionCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>个人失业基数：</span>
                        <span>{{ detailData.personalUnemploymentCardinal }}</span>
                    </div>
                </template>
            </div>
        </div>
        <div class="examine" v-if="type == 'look'">
            <Divider type="vertical" class="divid" />
            <span>审核信息</span>
            <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                <div class="list-item">
                    <span style="width: 40px">{{ index + 1 }}</span>
                    <div class="item-flex">
                        <span class="box1">操作人：</span>
                        <span class="box2">{{ item.realName }}</span>
                    </div>
                    <div class="item-flex2">
                        <span>操作时间：</span>
                        <span>{{ item.createdDate }}</span>
                    </div>
                    <div class="item-flex3">
                        <div class="box1">操作信息：</div>
                        <div class="box2">{{ item.message }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="examine" v-if="type == 'examine'">
            <Divider type="vertical" class="divid" />
            <span>拒绝理由</span>
            <div class="examine-area">
                <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" />
            </div>
            <div class="ant-modal-footer" style="margin-top: 60px">
                <Button danger type="primary" @click="rejectRow">拒绝</Button>
                <Button type="primary" @click="confirm">通过</Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'InductionDetail',
    props: {
        title: String,
        type: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { title, item, visible } = toRefs<any>(props)
        const applyStep = ref(0)
        const detailData = ref<inObject>({})
        const opLogsList = ref<object[]>([])
        const checkerReason = ref('')
        // [ {
        //   "itemName" : "待入职",
        //   "itemValue" : 1,
        // }, {
        //   "itemName" : "待提交信息",
        //   "itemValue" : 2,
        // }, {
        //   "itemName" : "待确认信息",
        //   "itemValue" : 3,
        // }, {
        //   "itemName" : "待发合同",
        //   "itemValue" : 4,
        // }, {
        //   "itemName" : "待签合同",
        //   "itemValue" : 5,
        // }, {
        //   "itemName" : "待合同确认",
        //   "itemValue" : 6,
        // }, {
        //   "itemName" : "入职完成",
        //   "itemValue" : 7,
        // }, {
        //   "itemName" : "已删除",
        //   "itemValue" : 8,
        // } ]
        // <Step title="发送入职通知" />
        // <Step title="员工录入信息" />
        // <Step title="发起劳动合同" />
        // <Step title="签署劳动合同" />
        // <Step title="入职完成" />
        let applyStepArr = [0, 1, 2, 2, 3, 4, 5, 6]
        const getDetail = (value) => {
            request.get('/api/hr-apply-entry-staffs', { id: value }).then((res) => {
                applyStep.value = applyStepArr[Number(res.entryStatus) - 1]
                // applyStep.value = Number(res.applyStep)
                detailData.value = res
                opLogsList.value = res.opLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        watch(
            item,
            () => {
                if (item.value) {
                    getDetail(item.value?.id)
                    getList()
                }
            },
            { immediate: true },
        )
        const cancel = () => {
            emit('cancel')
        }
        const rejectRow = () => {
            let applyId = detailData.value.id
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            request
                .post('/api/hr-apply-entry-staffs/approve-reject', { applyIdList: [applyId], checkerReason: checkerReason.value })
                .then((res) => {
                    emit('cancel')
                    emit('confirm')
                })
        }
        const confirm = () => {
            let applyId = detailData.value.id
            request
                .post('/api/hr-apply-entry-staffs/approve-passed', { applyIdList: [applyId], checkerReason: checkerReason.value })
                .then((res) => {
                    if (res.body.message_exist) {
                        message.error(res.body.message_exist)
                    }
                    emit('cancel')
                    emit('confirm')
                })
        }
        const cardinalArr = ref<any>([])
        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, { clientId: item.value?.clientId })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = [...arr]
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })

                return item
            })
            return resArr
        }
        return {
            checkerReason,
            applyStep,
            detailData,
            opLogsList,
            cancel,
            rejectRow,
            confirm,

            cardinalArr,
        }
    },
})
</script>
<style scoped lang="less">
.step-detail {
    margin-bottom: 30px;
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 200px;
                .box1 {
                    float: left;
                    width: 58px;
                }
                .box2 {
                    float: right;
                    width: 142px;
                    padding-right: 4px;
                }
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            span {
                display: inline-block;
                max-width: 75%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
