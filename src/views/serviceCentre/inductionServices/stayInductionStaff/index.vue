<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'stayInductionStaff-export'" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'stayInductionStaff-induction'" @click="InductionMoreRow()">批量入职</Button>
        <!-- <Button type="primary" v-auth="'staff_entry'" @click="InductionMoreRow()">批量入职</Button> -->
        <Button type="primary" v-auth="'stayInductionStaff-confirm'" @click="confirmInfoRow()">批量确认信息</Button>
        <Button type="primary" v-auth="'inductionServices_updataStatus'" @click="updataStatus()">修改员工状态</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-apply-entry-staffs/page"
        deleteApi="/api/hr-apply-entry-staffs/deletes"
        exportUrl="/api/hr-apply-entry-staffs/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #staffStatus="{ record }">
            <Button v-if="record.entryStatus !== 1" type="link" size="small" @click="detailRow(record, 'look')">{{
                record.entryStatusLabel
            }}</Button>
            <span v-else>{{ record.entryStatusLabel }}</span>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <InductionDetail
        v-model:visible="showDetail"
        :title="modalTitle"
        :item="currentDetail"
        :type="detailType"
        @cancel="detailCancel"
        @confirm="detailConfirm"
    />
    <!-- 电签 -->
    <ElectricSign :visible="showSign" :title="signTitle" :item="currentValue" @cancel="signCancel" @confirm="signConfirm" />
    <!-- 确认电签 -->
    <ConfirmSign
        :visible="showConfirmSign"
        title="确认电签"
        :item="currentValue"
        @cancel="confirmSignCancel"
        @confirm="confirmSignConfirm"
    />
    <!-- 批量确认信息 -->
    <ConfirmInfoModal v-model:visible="showConfirmInfor" :applyStaffIdList="applyStaffIdList" @confirm="confirmInforCancel" />
    <!-- 入职中-->
    <!-- <Induction :visible="showInduction" title="详情" :item="inductionValue" @cancel="modalCancelInduction" /> -->

    <!-- 添加编辑员工 -->
    <AddStaff
        :visible="showStaff"
        :title="modalTitle"
        :item="currentValue"
        typePage="staff"
        :viewType="viewType"
        @cancel="staffModalCancel"
        @confirm="staffModalConfirm"
    />
    <!-- 调入调出 -->
    <TransferModal
        viewType="laborContract"
        title="录入合同"
        :visible="showTransfer"
        :currentData="currentData"
        @cancel="transferClose"
        @confirm="transferConfirm"
    />
    <!-- 修改员工状态 -->
    <BasicEditModalSlot
        :visible="showUpdataModal"
        title="修改员工合同状态"
        @cancel="updataStatusCancel"
        @ok="updataStatusConfirm"
        width="400px"
    >
        <div class="statusTip">修改员工合同状态会导致已经完成签订的劳动合同作废，此操作不可回撤，慎用！</div>
        <Form ref="statusForm" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem
                label="员工状态"
                name="employeesStatus"
                :rules="{ required: true, message: '请选择员工状态', trigger: ['change', 'blur'] }"
            >
                <Select
                    v-model:value="formData.employeesStatus"
                    placeholder="员工入职状态"
                    :disabled="true"
                    :options="employeesOptions"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>
<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message, Modal, notification } from 'ant-design-vue'
import { ref, defineComponent, onMounted, onBeforeMount, computed } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import addInduction from './addInduction.vue'
import InductionDetail from './InductionDetail.vue'
import permissionStore from '/@/store/modules/permission'
import { SearchBarOption } from '/#/component'

import electricSign from './signContract/electricSign.vue'
import confirmSign from './signContract/confirmSign.vue'
import staffStore from '/@/store/modules/staff'
import signStore from '/@/store/modules/electricSign'
import confirmInfoModal from './confirmInfoModal.vue'
// import induction from './employeeInduction.vue'
import AddStaff from '/@/views/staff/staff/addStaff.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import TransferModal from '/@/views/recordAdmin/recordList/TransferModal.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
import { trigger } from '@vue/reactivity'
export default defineComponent({
    name: 'InductionList',
    components: {
        AddStaff,
        InductionDetail,
        // Induction: induction,
        ElectricSign: electricSign,
        ConfirmSign: confirmSign,
        ConfirmInfoModal: confirmInfoModal,
        TransferModal,
        PostTree,
    },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false
        const body = document.body
        //筛选
        // let selectclientsOptions = ref<LabelValueOptions>([]) //客户名称
        let sexTypeList = ref<LabelValueOptions>([]) // 性别
        let station = ref<LabelValueOptions>([]) // 岗位
        let staffTypeList = ref<LabelValueOptions>([]) //人员类型
        let entryStatusLIst = ref<LabelValueOptions>([]) //申请状态
        let specializedOptions = ref<LabelValueOptions>([]) //专管员

        //表格数据
        const columns = ref([
            {
                title: '申请日期',
                dataIndex: 'createdDate',
                align: 'center',
                width: 100,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 180,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ record }) => {
                    return record.sexLabel
                },
                width: 100,
            },

            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
                width: 100,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 150,
            },
            {
                title: '合同开始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
            },
            {
                title: '专管员',
                dataIndex: 'real_name',
                align: 'center',
                customRender: ({ record }) => {
                    return record.specialized
                },
                width: 120,
            },
            // {
            //     title: '基本工资',
            //     dataIndex: 'basicWage',
            //     align: 'center',
            // },

            // {
            //     title: '审批状态',
            //     dataIndex: 'applyStatus',
            //     align: 'center',
            //     customRender: ({ record }) => {
            //         return record.applyStatusLabel
            //     },
            // },
            {
                title: '入职流程',
                dataIndex: 'entryStatus',
                align: 'center',
                slots: { customRender: 'staffStatus' },
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ])

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = options.findIndex((el) => {
                    return el.key == 'userIdList'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'real_name'
                })
                options.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        onMounted(() => {
            // 客户
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id }
            //     })
            // })
            // 性别
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
            // 岗位
            request.get('/api/hr-stations/list', {}).then((res) => {
                station.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
            //人员类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //入职流程
            request.get('/api/com-code-tables/getCodeTableByInnerName/ToBeHiredStates', {}).then((res) => {
                entryStatusLIst.value = res.map((item) => {
                    console.log(item)
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            columns.value.forEach((item) => {
                if (RoleState) {
                    if (item.dataIndex == 'createdDate') {
                        let index = columns.value.indexOf(item)
                        columns.value.splice(index, 1)
                    }
                } else {
                    // if (item.dataIndex == 'entryStatusLabel') {
                    //     let index = columns.value.indexOf(item)
                    //     columns.value.splice(index, 1)
                    // }
                }
            })
            columns.value = [...columns.value]
        })
        //表格dom
        const tableRef = ref()
        //筛选
        const params = ref<Recordable>({ entryStatusList: [2, 1, 3, 4, 5, 6] })
        const options: SearchBarOption[] = [
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },

            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIds',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                // type: 'string',
                // label: '岗位',
                // key: 'professionName',
                // options: station,
                type: 'selectSlot',
                label: '岗位',
                key: 'stationIdList',
                placeholder: '岗位',
                maxTag: '0',
            },

            {
                // type: 'select',
                // label: '专管员',
                // key: 'specializedId',
                // options: specializedOptions,
                type: 'select',
                label: '专管员',
                key: 'userIdList',
                options: specializedOptions,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '合同开始日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '合同结束日期',
                key: 'contractEndDateQuery',
            },
            {
                type: 'select',
                label: '入职流程',
                key: 'entryStatusList',
                options: entryStatusLIst,
                multiple: true,
            },
        ]

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        // 新增编辑
        // const showAdd = ref(false)
        const modalTitle = ref('新增')
        // const title = ref('批量拒绝')
        const checkerReason = ref('')
        const detailType = ref('')
        const showDetail = ref(false)
        // const showRejec = ref(false)

        // 当前编辑的数据
        const currentValue = ref<any>(null)
        const currentDetail = ref<any>(null)

        // 详情
        const detailRow = (record, type) => {
            showDetail.value = true
            modalTitle.value = '详情'
            currentDetail.value = { ...record }
            detailType.value = type
        }

        // 关闭弹窗
        const detailCancel = () => {
            showDetail.value = false
            modalTitle.value = '新增'
            currentDetail.value = null
        }
        // 确认弹窗
        const detailConfirm = async () => {
            tableRef.value.refresh(1)
        }
        //批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {})
        }

        // 发起电签
        const showSign = ref(false)
        const signTitle = ref('发起电签')
        // 打开发起电签弹窗
        const signRow = (record) => {
            showSign.value = true
            signTitle.value = '发起电签'
            currentValue.value = { ...record, id: record.staffId, employedId: record.id }
            staffStore().setStaff(record.staffId)
            signStore().setClientId(record.clientId)
        }
        // 关闭发起电签弹窗
        const signCancel = () => {
            showSign.value = false
            signTitle.value = '发起电签'
            currentValue.value = null
            signStore().setContractTemplate([])
            signStore().setMustData([])
            signStore().setMoreData([])
            signStore().setTabs('1')
        }
        // 发起电签确认弹窗
        const signConfirm = async () => {
            showConfirmSign.value = false
            tableRef.value.refresh()
        }

        // 确认电签
        const showConfirmSign = ref(false)
        // 打开确认电签弹窗
        const signConfirmRow = (record) => {
            showConfirmSign.value = true
            modalTitle.value = '确认电签'
            currentValue.value = { ...record, id: record.staffId, employedId: record.id }
        }
        // 关闭确认电签弹窗
        const confirmSignCancel = () => {
            showConfirmSign.value = false
            modalTitle.value = '确认电签'
            currentValue.value = null
        }
        // 确认确认电签弹窗
        const confirmSignConfirm = async () => {
            confirmSignCancel()
            tableRef.value.refresh()
        }
        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 入职
        const InductionRow = (record) => {
            let ids: any[] = []
            ids.push(record.id)
            request.post('/api/hr-apply-entry-staffs/entry-level', ids).then((res) => {
                if (res.body.errorMessage) {
                    openNotification(res.body.errorMessage)
                }
                tableRef.value.refresh()
            })
        }
        // 批量入职
        const InductionMoreRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先选择入职人员!')
                return
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            request.post('/api/hr-apply-entry-staffs/entry-level', applyIdList).then((res) => {
                if (res.body.errorMessage) {
                    openNotification(res.body.errorMessage)
                }
                tableRef.value.refresh()
            })
        }
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }

        // 确认信息
        const applyStaffIdList = ref<any[]>([])
        const showConfirmInfor = ref(false)
        const confirmInfoRow = (record?) => {
            let ids: any[] = []
            if (record) {
                ids.push(record.id)
            } else {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请先选择人员!')
                    return
                }
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
            }
            applyStaffIdList.value = ids
            showConfirmInfor.value = true
            // tableRef.value.refresh()

            // let ids: any[] = []
            // ids.push(record.id)
            // request.post('/api/hr-apply-entry-staffs/confirm-entry', ids).then((res) => {
            //     confirmInforValue.value = res.body
            //     showConfirmInfor.value = true
            //     tableRef.value.refresh()
            // })
        }
        // // 批量确认信息

        // const confirmInforValue = ref(null)
        // const confirmInfoMoreRow = () => {
        //     if (selectedRowsArr.value.length <= 0) {
        //         message.error('请先选择人员!')
        //         return
        //     }
        //     let applyIdList = selectedRowsArr.value.map((el: inObject) => {
        //         return el.staffId
        //     })
        //     request.post('/api/hr-apply-entry-staffs/confirm-entry', applyIdList).then((res) => {
        //         confirmInforValue.value = res.body
        //         showConfirmInfor.value = true
        //         if (res.body.length > 0) {
        //             openNotification(`选中的数据中 ${res.body?.join()}，不是待入职状态`)
        //         }
        //         tableRef.value.refresh()
        //     })
        // }
        const confirmInforCancel = () => {
            // showConfirmInfor.value = false
            tableRef.value.refresh()
        }

        //入职中
        const showInduction = ref(false)
        const inductionValue = ref(null)

        const clickStaffStatus = (record) => {
            showInduction.value = true
            inductionValue.value = { ...record, id: record.staffId, employedId: record.id }
        }
        const modalCancelInduction = () => {
            showInduction.value = false
            inductionValue.value = null
        }

        //编辑员工,完善信息
        const showStaff = ref(false)
        // 编辑
        const viewType = ref('')
        const staffEditRow = (record, type) => {
            showStaff.value = true
            viewType.value = type
            modalTitle.value = type == 'see' ? '员工信息' : '完善信息'
            currentValue.value = { ...record, id: record.staffId, employedId: record.id }
        }

        // 关闭弹窗
        const staffModalCancel = () => {
            showStaff.value = false
            modalTitle.value = '完善信息'
            currentValue.value = null
        }
        // 确认弹窗
        const staffModalConfirm = async () => {
            tableRef.value.refresh()
        }
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        //档案调入
        const showTransfer = ref(false)
        const currentData = ref({})
        const transferIn = (record) => {
            request.get(`/api/hr-archives-manage/${record.clientId}/${record.staffId}`).then((res) => {
                currentData.value = res
                showTransfer.value = true
            })
        }
        const transferClose = () => {
            showTransfer.value = false
            currentData.value = {}
        }
        const transferConfirm = () => {
            showTransfer.value = false
            tableRef.value.refresh()
            currentData.value = {}
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'stayInductionStaff-see',
                    show: true,
                    click: (record) => detailRow(record, 'look'),
                },
                {
                    neme: '员工信息',
                    auth: 'stayInductionStaff-confirm',
                    show: (record) => {
                        return !(record.entryStatus == '1' || record.entryStatus == '2')
                    },
                    click: (record) => staffEditRow(record, 'see'),
                },
                {
                    neme: '通知入职',
                    auth: 'stayInductionStaff-warn',
                    show: (record) => {
                        return record.entryStatus == '1'
                    },
                    click: InductionRow,
                },
                {
                    neme: '确认信息',
                    auth: 'stayInductionStaff-confirm',
                    show: (record) => {
                        return record.entryStatus == '3'
                    },
                    click: confirmInfoRow,
                },
                {
                    neme: '发起电签',
                    auth: 'stayInductionStaff-electric',
                    show: (record) => {
                        return record.entryStatus == '4'
                    },
                    click: signRow,
                },
                {
                    neme: '录入合同',
                    auth: 'stayInductionStaff_contract',
                    show: (record) => {
                        return record.entryStatus == '4'
                    },
                    click: transferIn,
                },
                {
                    neme: '确认电签',
                    auth: 'stayInductionStaff_confirm_electric',
                    show: (record) => {
                        return record.entryStatus == '6'
                    },
                    click: signConfirmRow,
                },
                {
                    neme: '完善信息',
                    auth: 'stayInductionStaff_perfect',
                    show: (record) => {
                        return record.entryStatus == '1' || record.entryStatus == '2'
                    },
                    click: (record) => staffEditRow(record, 'perfect'),
                },
                // {
                //     neme: '删除',
                //     auth: 'stayInductionStaff_delete',
                //     show: (record) => {
                //         return record.applyStatus == '1'
                //     },
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        // // 修改员工状态
        const statusForm = ref()
        const formData = ref({
            employeesStatus: '1',
        })
        const showUpdataModal = ref(false)
        const updataStatus = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先选择员工!')
                return
            }
            showUpdataModal.value = true
        }
        const updataStatusCancel = () => {
            showUpdataModal.value = false
            restUpdataModal()
        }
        const restUpdataModal = () => {
            statusForm.value?.resetFields()
            formData.value.employeesStatus = '1'
        }
        const updataStatusConfirm = () => {
            statusForm.value
                .validate()
                .then(() => {
                    let staffIds = selectedRowsArr.value?.map((el: any) => el.staffId)
                    request
                        .post(
                            `/api/hr-talent-staffs/manual_update_staff_induction_process/${formData.value.employeesStatus}`,
                            staffIds,
                        )
                        .then((res) => {
                            if (res) {
                                showUpdataModal.value = false
                                restUpdataModal()
                            }
                        })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const employeesOptions = ref<inObject[]>([
            { label: '入职待发合同', value: '1' },
            { label: '续签待发合同', value: '2' },
        ])

        return {
            // data
            exportText,
            tableRef,
            columns,
            params,
            options,
            // showAdd,
            viewType,
            modalTitle,
            currentValue,
            currentDetail,
            showDetail,
            // showRejec,
            // title,
            checkerReason,
            detailType,
            // 事件
            searchData,
            // addRow,
            // editRow,
            detailRow,
            // modalCancel,
            // modalConfirm,
            detailCancel,
            detailConfirm,
            deleteRow,
            // selectedRowsArr,
            // rejectRow,
            // cancel,
            confirm,
            // passRow,
            // InductionRow,
            // InductionMoreRow,

            //操作
            myOperation,

            //入职中
            showInduction,
            inductionValue,
            clickStaffStatus,
            modalCancelInduction,

            showSign,
            signTitle,
            showConfirmSign,

            signCancel,
            signConfirm,
            signRow,
            signConfirmRow,
            confirmSignCancel,
            confirmSignConfirm,
            InductionRow,
            selectedRowsArr,
            InductionMoreRow,
            confirmInfoRow,

            // confirmInfoMoreRow,
            // showConfirmInfor,
            confirmInforCancel,
            // confirmInforValue,
            applyStaffIdList,
            showConfirmInfor,

            showStaff,
            // 编辑
            staffEditRow,
            // 关闭弹窗
            staffModalCancel,
            // 确认弹窗
            staffModalConfirm,
            //导出
            exportData,

            //档案调入
            // start
            showTransfer,
            currentData,
            transferIn,
            transferClose,
            transferConfirm,

            // // 修改员状态
            showUpdataModal,
            updataStatus,
            body,
            updataStatusConfirm,
            formData,
            employeesOptions,
            statusForm,
            updataStatusCancel,
            //end
        }
    },
})
</script>
<style scoped lang="less">
.statusTip {
    color: red;
    text-align: center;
    font-size: 16px;
    width: 100%;
    margin-bottom: 20px;
}
</style>
