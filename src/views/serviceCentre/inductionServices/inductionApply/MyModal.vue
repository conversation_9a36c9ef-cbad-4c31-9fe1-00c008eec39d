<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1500px">
        <div class="seeInfoBox" v-if="viewType == 'see' || viewType == 'examine'">
            <div class="wtwenty">
                <p class="label">客户编号：</p>
                <Tooltip placement="top" :getPopupContainer="getPopupContainer">
                    <template #title>
                        <p>{{ formData.unitNumber }}</p>
                    </template>
                    <p class="tooltipTitle">{{ formData.unitNumber }}</p>
                </Tooltip>
            </div>
            <div class="wtwenty">
                <p class="label">客户名称：</p>
                <Tooltip placement="top" :getPopupContainer="getPopupContainer">
                    <template #title>
                        <p>{{ formData.clientName }}</p>
                    </template>
                    <p class="tooltipTitle">{{ formData.clientName }}</p>
                </Tooltip>
            </div>
            <br v-if="viewType == 'see'" />
            <div class="wtwenty">
                <p class="label">协议编号：</p>
                <Tooltip placement="top" :getPopupContainer="getPopupContainer">
                    <template #title>
                        <p>{{ formData.agreementNumber }}</p>
                    </template>
                    <p class="tooltipTitle">{{ formData.agreementNumber }}</p>
                </Tooltip>
            </div>
            <div class="wtwenty">
                <p class="label">协议标题：</p>
                <Tooltip placement="top" :getPopupContainer="getPopupContainer">
                    <template #title>
                        <p>{{ formData.agreementTitle }}</p>
                    </template>
                    <p class="tooltipTitle">{{ formData.agreementTitle }}</p>
                </Tooltip>
            </div>
            <div class="wtwenty">
                <p class="label">协议类型：</p>
                <Tooltip placement="top" :getPopupContainer="getPopupContainer">
                    <template #title>
                        <p>{{ formData.agreementTypeLabel }}</p>
                    </template>
                    <p class="tooltipTitle">{{ formData.agreementTypeLabel }}</p>
                </Tooltip>
            </div>
        </div>
        <div class="seeInfoBox" v-if="viewType == 'examine'">
            <div>
                <p class="label">附件：</p>
                <div v-for="item in hrAppendixList" :key="item.id" class="hrAppendixListBox">
                    <Tooltip placement="top">
                        <template #title>
                            <span>{{ item?.originName }}</span>
                        </template>
                        <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                    </Tooltip>
                </div>
            </div>
            <br />
            <div>
                <p class="label">备注：</p>
                <p>{{ formData.applyRemark }}</p>
            </div>
        </div>
        <template v-if="viewType == 'add' || viewType == 'edit'">
            <Form
                ref="formInline"
                :model="formData"
                :rules="rules"
                :label-col="{ style: { width: '120px' } }"
                class="form-flex"
                style="max-height: 60vh"
            >
                <template v-for="(itemForm, i) in myOptions" :key="i">
                    <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]">
                        <template #Cascader>
                            <ClientSelectTree
                                v-model:value="formData[itemForm.name]"
                                :itemForm="itemForm"
                                @change="ClientSelectTreeChange()"
                            />
                        </template>
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[i]" allowClear />
                        </template>
                        <template #staffType>
                            <RadioGroup v-model:value="formData[itemForm.name]" :options="staffTypeOptions" />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="btns" style="margin: 0">
                <p class="label">待入职员工</p>
                <Button type="primary" @click="addRow()">添加</Button>
                <Button type="primary" @click="importIn">导入</Button>
            </div>
        </template>
        <EmployedStaff
            :visible="visible"
            @operationEdit="editRow"
            @operationDelete="deleteRow"
            :viewType="viewType"
            :currentValue="currentValue"
            @getDataInfo="getDataInfo"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
            @auditRequest="auditRequest"
            :cardinalArr="cardinalArr"
        />

        <Form
            ref="formInlineAdd"
            :model="formData"
            :rules="rules"
            :label-col="{ style: { width: '120px' } }"
            v-if="viewType == 'add' || viewType == 'examine' || viewType == 'edit'"
        >
            <FormItem
                label="附件"
                name="newAppendixIdList"
                :rules="{
                    required: viewType == 'add' || viewType == 'edit',
                    type: 'array',
                    message: '请添加附件',
                    trigger: ['change', 'blur'],
                }"
            >
                <ImportFile v-model:fileUrls="formData.newAppendixIdList" ref="refImportFile" listType="fileList" />
            </FormItem>
            <FormItem label="备注" name="userName">
                <Textarea v-model:value="formData.newApplyRemark" :rows="3" allowClear placeholder="请输入备注" />
            </FormItem>
        </Form>
        <div v-if="viewType == 'see'">
            <div class="seeInfoBox">
                <div>
                    <p class="label">附件：</p>
                    <div v-for="item in hrAppendixList" :key="item.id" class="hrAppendixListBox">
                        <Tooltip placement="top">
                            <template #title>
                                <span>{{ item?.originName }}</span>
                            </template>
                            <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                        </Tooltip>
                    </div>
                </div>
                <br />
                <div>
                    <p class="label">备注：</p>
                    <p>{{ formData.applyRemark }}</p>
                </div>
            </div>
            <p class="examineTitle label"><span></span>审核信息&nbsp;&nbsp;&nbsp;</p>
            <div class="seeInfoBox" style="margin-top: 10px" v-for="opLogsItem in opLogsList" :key="opLogsItem.id">
                <div>
                    <p class="label">审核人：</p>
                    <p>{{ opLogsItem.realName }}</p>
                </div>
                <div>
                    <p class="label">审核时间：</p>
                    <p>{{ opLogsItem.createdDate }}</p>
                </div>
                <br />
                <div>
                    <p class="label">附件：</p>
                    <div v-for="item in opLogsItem.hrAppendixList" :key="item.id" class="hrAppendixListBox">
                        <Tooltip placement="top">
                            <template #title>
                                <span>{{ item?.originName }}</span>
                            </template>
                            <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                        </Tooltip>
                    </div>
                </div>
                <br />
                <div>
                    <p class="label">备注：</p>
                    <p>{{ opLogsItem.message }}</p>
                </div>
            </div>
        </div>

        <div class="tip">
            <p>温馨提示：根据相关规定社保和医保基数高于上限或低于下限的按照最高上限和最低下限缴费标准进行缴纳</p>
        </div>

        <template #footer>
            <template v-if="viewType == 'examine'">
                <Button type="primary" class="btn" key="back" @click="refuseOrAdopt(1)">批量通过</Button>
                <Button danger type="primary" key="back" @click="rejectRow()">批量拒绝</Button>
            </template>
            <template v-if="viewType == 'add' || viewType == 'edit'">
                <Button key="back" @click="cancel">取消</Button>
                <Button type="primary" @click="confirm(2)">暂存</Button>
                <Button type="primary" @click="confirm(1)">提交</Button>
            </template>
            <template v-if="viewType == 'see'">
                <Button key="submit" v-auth="'inductionServices_export'" type="primary" @click="exportData">
                    导出失败人员信息
                </Button>
            </template>
        </template>
    </BasicEditModalSlot>
    <AddInduction
        v-model:visible="showAdd"
        :title="modalTitle"
        :itemInfo="itemInfo"
        :inductionApplyData="formData"
        :stationList="station"
        :editDataIndex="editDataIndex"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :cardinalArr="cardinalArr"
    />
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="refuseOrAdopt(2)"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <!-- <ImportModal
        v-model:visible="importVisible"
        @getResData="getResData"
        temUrl="/api/hr-apply-entries/staff/template"
        importUrl="/api/hr-apply-entries/staff/import"
    /> -->
    <ImportModal
        v-model:visible="importVisible"
        @getResData="getResData"
        temUrl="/api/hr-apply-entries/staff/template"
        :importUrl="`/api/hr-apply-entries/staff/import/${formData.clientId}`"
    />
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted, computed } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules, openNotification, previewFile } from '/@/utils/index'
import EmployedStaff from './component/employedStaff.vue'
import AddInduction from './component/addInduction.vue'
import inductionApplyStore from '/@/store/modules/inductionApply'
import { staffTypeOptions } from '/@/utils/dictionaries'
import downFile from '/@/utils/downFile'
interface Option {
    value: string
    label: string
    loading?: boolean
    isLeaf?: boolean
    children?: Option[]
}
export default defineComponent({
    name: 'InductionApplyAddModal',
    components: { EmployedStaff, AddInduction, PaperClipOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'add', 'edit', 'examine'].indexOf(value) !== -1
            },
        },
    },
    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { currentValue, visible, viewType } = toRefs<any>(props)
        const station = ref<object[]>([]) //岗位
        const protocolList = ref<object[]>([]) //客户协议
        const isDisabled = ref<Boolean>(true)
        const clientId = ref<string[]>([])

        const hrAppendixList = ref<inObject[]>([])
        const opLogsList = ref<inObject[]>([])
        onMounted(() => {
            request.get('/api/hr-stations/list', {}).then((res) => {
                station.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
        })
        // const cardinalList = computed(() => inductionApplyStore().cardinalList)
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '所属客户',
                name: 'clientId',
                slots: 'Cascader',
                type: 'slots',
            },
            {
                label: '客户协议',
                name: 'protocolId',
                type: 'change',
                options: protocolList,
                trigger: 'change',
                disabled: true,
            },
            {
                label: '缴费年月',
                name: 'paymentDate',
                required: false,
                type: 'month',
            },
            {
                label: '岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
                onChange: (value, option) => selectChange(value, option, 'stationId'),
            },
            {
                label: '基础工资/月',
                name: 'basicWage',
                type: 'number',
                ruleType: 'number',
                required: false,
                min: 0,
            },
            {
                label: '工资工龄基数',
                name: 'seniorityWageBase',
                type: 'number',
                ruleType: 'number',
                required: false,
                min: 0,
            },
            {
                label: '员工类型',
                name: 'staffType',
                slots: 'staffType',
                type: 'slots',
                trigger: 'change',
                ruleType: 'number',
                required: false,
            },
            {
                label: '待转时长',
                name: 'internshipDuration',
                unit: '月',
                required: false,
                placeholder: '请输入试用期时长',
                type: 'number',
                ruleType: 'number',
                min: 0,
            },
        ])
        const selectChange = (value, option, name) => {
            if (name == 'stationId') {
                formData.value['professionName'] = option?.label
            }
            formData.value[name + 'Label'] = option?.label
        }
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                getDataInfo(currentValue.value?.id)
            }
        })
        const getDataInfo = (infoId) => {
            if (infoId) {
                request.get('/api/hr-apply-entries', { id: infoId }).then((res) => {
                    console.log('res', res)
                    if (viewType.value != 'edit') {
                        formData.value = Object.assign({}, initFormData, res, { newAppendixIdList: [], newApplyRemark: '' })
                        hrAppendixList.value = res?.hrAppendixList
                        opLogsList.value = res?.opLogsList
                        formData.value.newAppendixIdList = res?.opLogsList?.[0]?.hrAppendixList || []
                        formData.value.newApplyRemark = res?.opLogsList?.[0]?.message || ''
                    } else {
                        formData.value = Object.assign({}, initFormData, res)
                        formData.value.newAppendixIdList = res?.hrAppendixDTOS || []
                        formData.value.newApplyRemark = res?.applyRemark || ''
                    }
                    inductionApplyStore().setEmployedStaffList(res?.applyEntryStaffDTOList || [])

                    ClientSelectTreeChange(true)
                })
            } else {
                formData.value = Object.assign({}, initFormData, currentValue.value, {
                    newAppendixIdList: [],
                    newApplyRemark: '',
                })
                ClientSelectTreeChange(true)
            }
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value?.resetFields()
        }
        const refImportFile = ref()
        // cancel handle
        const cancel = () => {
            inductionApplyStore().setEmployedStaffList([])

            emit('cancel')
            emit('update:visible', false)
            resetFormData()
        }
        const importIn = () => {
            if (!formData.value.clientId) {
                message.warn({ content: '请先选择所属客户' })
                return
            }
            importVisible.value = true
        }

        // confirm handle
        const formInlineAdd = ref()
        const confirm = (flag) => {
            let appendixIdList = refImportFile.value.getFileUrls().map((item) => {
                return item.id
            })
            let applyEntryStaffDTOList = inductionApplyStore().getEmployedStaffList

            const saveHandle = async () => {
                await request.post('/api/hr-apply-entries/create', {
                    ...formData.value,
                    appendixIdList,
                    applyEntryStaffDTOList,
                    applyRemark: formData.value.newApplyRemark,
                    isDefault: flag,
                })
                message.success(`${flag == 1 ? '提交' : '暂存'}成功!`)
                cancel()
                // 表单关闭后的其它操作 如刷新表
                emit('confirm', formData.value)
            }
            if (flag == 2) {
                if (!formData.value?.clientId) {
                    message.warning('请选择所属客户')
                    return
                }
                saveHandle()
            } else {
                if (applyEntryStaffDTOList.length === 0) {
                    return message.warning('请添加最少一条入职员工！')
                } else {
                    let tip = ''
                    applyEntryStaffDTOList.forEach((item) => {
                        //拿停止缴费年月做必填判断
                        if (!item?.contractEndDate) {
                            tip += item.name + ','
                        }
                        return item?.contractEndDate
                    })
                    if (tip) {
                        openNotification('员工：' + tip + '信息不完整，请编辑后提交！')
                        return
                    }
                }
                if (appendixIdList.length === 0) {
                    formInlineAdd.value.validate()
                    return message.warning('请上传附件信息！')
                }
                formInline.value
                    .validate()
                    .then(() => {
                        saveHandle()
                    })
                    .catch((error) => {
                        console.log('表单验证失败', error)
                    })
            }
        }

        // 客户协议
        const clientChange = (e) => {
            formData.value.clientId = e[e.length - 1]
            protocolList.value = []
            formData.value.protocolId = null
            if (formData.value.clientId) {
                isDisabled.value = false
                getAgreement(formData.value.clientId)
            } else {
                isDisabled.value = false
            }
        }
        // 客户协议接口
        const getAgreement = (value) => {
            request.get('/api/hr-protocols/customer-agreement', { clientId: value }).then((res) => {
                if (res?.id) {
                    protocolList.value = [{ ...res, label: res.agreementTitle, value: res.id }]
                    formData.value.protocolId = res?.id || null
                } else {
                    message.warning('请选择的客户暂无协议，暂无法进行入职操作！')
                }
            })
        }
        //新增编辑
        const showAdd = ref(false)
        const modalTitle = ref('新增')
        // 当前编辑的数据
        const itemInfo = ref<any>({})
        let editDataIndex = ref<any>(null)
        const importVisible = ref(false)
        //导入员工
        const getResData = (data: inObject) => {
            let employedStaffList = [...inductionApplyStore().getEmployedStaffList, ...(data?.successList || [])]
            inductionApplyStore().setEmployedStaffList(employedStaffList)
        }
        //添加员工
        const addRow = () => {
            if (!formData.value?.clientId) {
                message.warning({
                    content: '请先选择所属客户',
                })
                return
            }
            editDataIndex.value = null
            itemInfo.value = null
            showAdd.value = true
            modalTitle.value = '添加员工'
        }
        //修改员工
        const editRow = (data) => {
            editDataIndex.value = data.index
            itemInfo.value = data.record
            showAdd.value = true
            modalTitle.value = '编辑员工'
        }
        const modalCancel = () => {}
        const modalConfirm = (data) => {
            if (typeof editDataIndex.value == 'number') {
                inductionApplyStore().setEmployedStaffListReplace(data, editDataIndex.value)
            } else {
                inductionApplyStore().setEmployedStaffListPush(data)
            }
        }
        // 删除员工
        const deleteRow = (data) => {
            inductionApplyStore().setEmployedStaffListDele(data.index, viewType.value)
        }
        const downloadFil = (item) => {
            previewFile(item.fileUrl)
        }

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            checkerReason.value = ''
            showRejec.value = true
        }
        const checkerReason = ref('')

        const refuseOrAdopt = (type) => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择审核人员')
                return false
            }
            if (type == 2) {
                if (!checkerReason.value) {
                    message.warning('请填写拒绝理由')
                    return false
                }
                showRejec.value = false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            auditRequest(type, applyIdList, checkerReason.value)
        }
        //审核请求
        const auditRequest = (type, applyIdList, staffRemark) => {
            let appendixIdList = refImportFile.value.getFileUrls().map((item: inObject) => {
                return item.id
            })
            let api = '/api/hr-apply-entry-staffs/approve-passed'
            if (type == 2) {
                api = '/api/hr-apply-entry-staffs/approve-reject'
            }
            request
                .post(api, {
                    applyId: currentValue.value?.id || '',
                    appendixIdList,
                    applyRemark: formData.value.newApplyRemark,
                    staffRemark: staffRemark,
                    applyStaffIdList: applyIdList,
                })
                .then((res) => {
                    getDataInfo(currentValue.value?.id || '')
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    emit('confirm')
                })
        }
        const ClientSelectTreeChange = (type = false) => {
            if (!type) {
                formData.value.protocolId = null
            }

            if (formData.value.clientId) {
                isDisabled.value = false
                getAgreement(formData.value.clientId)
                getList()
            } else {
                isDisabled.value = true
            }
            viewType === 'add' && inductionApplyStore().setEmployedStaffList([])
            // formData.value.clientId && inductionApplyStore().setCardinalTypeList({ clientId: formData.value.clientId })
        }
        const cardinalArr = ref<any[]>([])
        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, { clientId: formData.value.clientId })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = [...arr]
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })
                return item
            })
            return resArr
        }
        //导出失败人员信息
        const exportData = () => {
            downFile('post', `/api/hr-apply-entries/staff/export?applyId=${formData.value.id}`, '')
        }
        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,

            clientId,
            clientChange,

            showAdd,
            modalTitle,
            itemInfo,
            editDataIndex,

            getResData,
            addRow,
            editRow,

            modalConfirm,
            modalCancel,

            station,
            staffTypeOptions,

            refImportFile,

            hrAppendixList,
            opLogsList,

            downloadFil,

            refuseOrAdopt,

            selectedRowsArr,
            showRejec,
            // 批量拒绝
            rejectRow,
            checkerReason,

            importVisible,
            getDataInfo,

            ClientSelectTreeChange,
            //导出失败人员信息
            exportData,
            //审核请求
            auditRequest,
            formInlineAdd,
            importIn,

            deleteRow,
            getPopupContainer: () => {
                return document.body
            },
            cardinalArr,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        .ant-form-item-control {
            width: calc(100% - 120px) !important;
        }
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
}
.label {
    width: 120px;
    display: inline-block;
    text-align: right;
    padding-right: 8px;
}
.examineTitle {
    margin-top: 10px;
    color: rgba(51, 51, 51, 100);
    font-size: 14px;
    font-weight: 600;
    vertical-align: middle;
    span {
        margin-right: 10px;
        vertical-align: middle;
        display: inline-block;
        width: 6px;
        height: 24px;
        line-height: 20px;
        background-color: rgba(104, 148, 254, 100);
    }
}
.seeInfoBox {
    width: 100%;
    & > div {
        line-height: 38px;
        display: inline-block;
        &.wtwenty {
            width: 20%;
        }

        p {
            display: inline-block;
            color: #333;
        }
        p:first-child {
            color: #999999;
        }
        .hrAppendixListBox {
            display: inline-block;
            padding-right: 10px;
            .enclosure {
                line-height: 26px;
                color: @primary-color;
                display: inline-block;
                cursor: pointer;
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: middle;

                &:hover {
                    background: #ddd;
                }
            }
        }
    }
    .tooltipTitle {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
}
.tip {
    color: red;
    font-size: 14px;
    width: 100%;
    p {
        width: 100%;
        text-align: center;
    }
}
</style>
