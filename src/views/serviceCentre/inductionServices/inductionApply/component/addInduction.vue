<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px">
        <div v-if="cardinalArr.length > 0" style="width: 100%" class="formBox">
            <Form ref="formAddInduction" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules">
                <Row wrap>
                    <Col span="12" v-for="(item, index) in myOptions.slice(0, 16)" :key="item">
                        <MyFormItem
                            :class="rules[item.name]?.required ? 'required ' + (item.slots || '') : item.slots || ''"
                            :width="item.width"
                            :item="item"
                            v-model:value="formData[item.name]"
                            v-if="item.show != false"
                        >
                            <template #stationCascader>
                                <StationCascader
                                    v-model:value="formData.stationId"
                                    v-model:itemForm="myOptions[index]"
                                    allowClear
                                />
                            </template>
                            <template #staffType>
                                <RadioGroup
                                    v-model:value="formData[item.name]"
                                    :options="staffTypeOptions"
                                    @change="radioGroupChange"
                                />
                            </template>
                        </MyFormItem>
                    </Col>
                    <Col span="12" v-for="(item, index) in cardinalArr" :key="index">
                        <FormItem
                            :label="item.title"
                            :name="item.dataIndexS[0]"
                            :rules="{
                                required: true,
                                type: 'number',
                                message: `请输入${item.title}`,
                                trigger: ['blur', 'change'],
                            }"
                            :labelCol="{ span: 8 }"
                            :wrapperCol="{ span: 16 }"
                        >
                            <InputNumber
                                v-model:value.number="formData[item.dataIndexS[0]]"
                                :placeholder="`请输入${item.title}`"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        </div>
        <div v-if="cardinalArr.length == 0">
            <Form
                ref="formAddInduction"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                class="form-flex"
            >
                <template v-for="(item, index) in myOptions" :key="item">
                    <MyFormItem
                        :class="rules[item.name]?.required ? 'required ' + (item.slots || '') : item.slots || ''"
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        v-if="item.show != false"
                    >
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[index]" allowClear />
                        </template>
                        <template #staffType>
                            <RadioGroup
                                v-model:value="formData[item.name]"
                                :options="staffTypeOptions"
                                @change="radioGroupChange"
                            />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted, nextTick, computed } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'
import { validatePhone, idCardValidity } from '/@/utils/format'
import moment, { Moment } from 'moment'
import { staffTypeOptions } from '/@/utils/dictionaries'
import inductionApplyStore from '/@/store/modules/inductionApply'
export default defineComponent({
    name: 'InductionApplyAddInduction',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        itemInfo: {
            type: Object,
            default: () => {},
        },
        inductionApplyData: {
            type: Object,
            default: () => {},
        },
        stationList: {
            type: Array,
            default: () => [],
        },
        editDataIndex: Number,
        cardinalArr: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['cancel', 'confirm', 'update:visible'],
    setup(props, { emit }) {
        const { visible, itemInfo, inductionApplyData, stationList: station, editDataIndex, cardinalArr } = toRefs<any>(props)
        const sexList = ref<object[]>([]) // 性别
        const staffTypeList = ref<object[]>([]) //人员类型
        const residenceTypeList = ref<inObject[]>([]) //人员类型
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/residenceType', {}).then((res) => {
                residenceTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const specialArr = ref([
            'unitLargeMedicalExpense',
            'personalLargeMedicalExpense',
            'replenishWorkInjuryExpense',
            'commercialInsurance',
            'unitEnterpriseAnnuity',
            'personalEnterpriseAnnuity',
        ])
        const certificateNumBlur = (rule: inObject, value) => {
            // let code = ref(value)
            return idCardValidity(rule, value, () => onCompute(value))
        }
        // 根据身份证计算生日，性别，年龄
        const onCompute = (code) => {
            // 计算性别
            if (code.substr(16, 1) % 2 === 1) {
                formData.value.sex = 1
                formData.value.sexLabel = '男'
            } else {
                formData.value.sex = 2
                formData.value.sexLabel = '女'
            }
            formValidateOptional(['sex'])
            return Promise.resolve()
        }
        const myOptions = ref([
            {
                label: '姓名',
                name: 'name',
            },
            {
                label: '身份证号',
                name: 'certificateNum',
                // validator: idCardValidity,
                validator: certificateNumBlur,
                trigger: 'blur',
            },
            {
                label: '性别',
                name: 'sex',
                type: 'change',
                options: sexList,
                trigger: 'change',
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'sex'),
            },
            {
                label: '联系方式',
                name: 'phone',
                validator: validatePhone,
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'personnelType'),
            },
            {
                label: '岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
                onChange: (value, option) => selectChange(value, option, 'stationId'),
            },
            {
                label: '合同开始日期',
                name: 'contractStartDate',
                type: 'date',
                disabledDate: (startValue: Moment) => {
                    if (!startValue || !formData.value?.contractEndDate) {
                        return false
                    }
                    return startValue.startOf('day').valueOf() > new Date(formData.value?.contractEndDate)?.valueOf()
                },
            },
            {
                label: '合同结束日期',
                name: 'contractEndDate',
                type: 'date',
                disabledDate: (endValue: Moment) => {
                    if (!endValue || !formData.value?.contractStartDate) {
                        return endValue && endValue < moment().startOf('day')
                    }
                    return (
                        new Date(formData.value?.contractStartDate).valueOf() >= endValue.endOf('day').valueOf() ||
                        endValue < moment().startOf('day')
                    )
                },
            },
            {
                label: '缴费年月',
                name: 'paymentDate',
                type: 'month',
            },
            {
                label: '基础工资/月',
                name: 'basicWage',
                type: 'number',
                ruleType: 'number',
                min: 0,
                required: false,
            },
            {
                label: '户口性质',
                name: 'householdRegistration',
                type: 'change',
                options: residenceTypeList,
                trigger: 'change',
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'householdRegistration'),
                required: false,
            },
            {
                label: '工资工龄基数',
                name: 'seniorityWageBase',
                required: false,
                type: 'number',
                ruleType: 'number',
                min: 0,
            },
            {
                label: '员工类型',
                name: 'staffType',
                slots: 'staffType',
                type: 'slots',
                // type: 'change',
                // // options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '待转时长',
                name: 'internshipDuration',
                unit: '月',
                required: false,
                placeholder: '请输入试用期时长',
                type: 'number',
                ruleType: 'number',
                min: 0,
            },
            {
                label: '是否计算补缴',
                name: 'supplementaryPayment',
                required: true,
                placeholder: '请选择是否计算补缴',
                type: 'select',
                ruleType: 'number',
                options: [
                    {
                        label: '不计算',
                        value: 0,
                    },
                    { label: '计算', value: 1 },
                ],
            },
            {
                label: '公积金基数',
                name: 'accumulationFundCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位养老基数',
                name: 'unitPensionCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位失业基数',
                name: 'unitUnemploymentCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位医疗基数',
                name: 'medicalInsuranceCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位生育基数',
                name: 'unitMaternityCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位工伤基数',
                name: 'workInjuryCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '单位大额医疗费用',
                name: 'unitLargeMedicalExpense',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '补充工伤费用',
                name: 'replenishWorkInjuryExpense',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '个人医疗基数',
                name: 'medicalInsuranceCardinalPersonal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '个人养老基数',
                name: 'personalPensionCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '个人失业基数',
                name: 'personalUnemploymentCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '个人大额医疗费用',
                name: 'personalLargeMedicalExpense',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
            {
                label: '个人生育基数',
                name: 'personalMaternityCardinal',
                type: 'number',
                ruleType: 'number',
                min: 0,
                // ruleType: 'any',
            },
        ])
        const selectChange = (value, option, name) => {
            if (name == 'stationId') {
                formData.value['professionName'] = option?.label
            }
            formData.value[name + 'Label'] = option?.label
        }
        // 基数key  每次读取第一次输入的基数
        let baseNumList = [
            'unitPensionCardinal',
            'unitUnemploymentCardinal',
            'medicalInsuranceCardinal',
            'unitMaternityCardinal',
            'workInjuryCardinal',
            'unitLargeMedicalExpense',
            'replenishWorkInjuryExpense',
            'medicalInsuranceCardinalPersonal',
            'personalPensionCardinal',
            'personalUnemploymentCardinal',
            'personalLargeMedicalExpense',
            'personalMaternityCardinal',
            'accumulationFundCardinal',
        ]
        // Form 实例
        const formAddInduction = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules }: any = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formAddInduction.value.resetFields()
        }
        watch(
            visible,
            async () => {
                if (visible.value) {
                    if (itemInfo.value) {
                        formData.value = Object.assign({}, initFormData, itemInfo.value)
                    } else {
                        let list = inductionApplyStore().getEmployedStaffList
                        if (list.length > 0) {
                            let firstObj = {}
                            baseNumList.forEach((el) => {
                                firstObj[el] = list[0][el]
                            })
                            formData.value = Object.assign({}, initFormData, inductionApplyData.value, firstObj)
                        } else {
                            formData.value = Object.assign({}, initFormData, inductionApplyData.value)
                        }
                        formData.value.contractStartDate = moment(new Date()).format('YYYY-MM-DD')
                    }
                }
            },
            { immediate: true },
        )
        const resObj = computed(() => {
            let obj = {}
            cardinalArr.value?.map((el) => {
                for (const k in el) {
                    el.dataIndexS.forEach((row) => {
                        if (row === k) {
                            obj[row] = formData.value[el.dataIndexS[0]]
                        }
                    })
                }
                return el
            })
            return obj
        })
        const cancel = () => {
            resetFormData()
            emit('cancel')
            emit('update:visible', false)
        }

        let secondaryVerification = false
        const confirm = () => {
            formData.value = { ...formData.value, ...resObj.value }
            let validate = inductionApplyStore().getEmployedStaffList.some((item, i) => {
                if (editDataIndex.value == i) {
                    return false
                }
                return item.certificateNum == formData.value?.certificateNum
            })
            if (validate) {
                message.warning('您输入的身份证号有重复，请重新输入')
                return
            }
            if (formData.value?.staffType == 2 || formData.value?.staffType == 3) {
                let staffTypeName = staffTypeOptions.find((item) => {
                    return formData.value?.staffType == item.value
                })?.label
                if (!formData.value?.internshipDuration) {
                    message.warning(
                        `您添加的员工为${staffTypeName}，请输入${
                            formData.value?.internshipDuration == 0 ? '的待转时长应大于0' : '有效的待转时长'
                        }！`,
                    )
                    return
                }
            }
            // if (formData.value?.staffType == 3) {
            //     if (formData.value?.internshipDuration <= 0) {
            //         message.warning('您添加的员工为实习工，请输入代转时长')
            //     }
            //     return
            // }
            secondaryVerification = true
            formAddInduction.value
                .validate()
                .then(async () => {
                    try {
                        await request.post(`/api/real-name-auth`, {
                            name: formData.value.name,
                            certificateNum: formData.value.certificateNum,
                        })
                    } catch (error) {
                        return
                    }
                    emit('confirm', { ...formData.value })
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
                .finally(() => {
                    secondaryVerification = false
                })
        }
        //单个校验
        const formValidateOptional = (nameList: string[]) => {
            if (!secondaryVerification) {
                nextTick(() => {
                    formAddInduction.value?.validateFields(nameList)
                })
            }
            secondaryVerification = false
        }

        const radioGroupChange = (event) => {
            rules.internshipDuration.required = event?.target?.value == 2 || event?.target?.value == 3
            formValidateOptional(['internshipDuration'])
        }
        return {
            rules,
            formData,
            myOptions,
            formAddInduction,
            staffTypeList,
            confirm,
            cancel,
            staffTypeOptions,
            radioGroupChange,

            cardinalArr,
            specialArr,
        }
    },
})
</script>
<style scoped lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
.btn {
    text-align: right;
    margin: 10px;
    button {
        margin-left: 10px;
    }
}
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
    :deep(.required .ant-form-item-label label::before) {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
    }
}
.add-table {
    border: 1px solid #f0f0f0;
    border-top: none;
    padding: 10px;
    text-align: center;
    color: #999;
}
.formBox :deep(.ant-form-item .ant-form-item-label) {
    label {
        width: 100%;
        display: inline-block;
        overflow: hidden;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
</style>
