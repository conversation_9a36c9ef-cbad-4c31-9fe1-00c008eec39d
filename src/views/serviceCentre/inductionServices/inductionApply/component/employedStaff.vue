<template>
    <Table
        :columns="columns"
        :data-source="columnsData"
        :pagination="false"
        bordered
        :row-selection="viewType == 'examine' ? rowSelection : null"
        :row-key="(record) => record.id + 'employedStaff'"
        class="smallTable"
        :scroll="{ x: '100%', y: 200 }"
    >
        <template #reasonRejection="{ record }">
            <span v-if="viewType == 'see' || record?.applyStatus">{{ record.staffRemark }}</span>
            <Input v-model:value="record.staffRemark" v-if="viewType == 'examine' && !record?.applyStatus" />
        </template>
        <template #operation="operationData" v-if="viewType == 'add' || viewType == 'examine' || viewType == 'edit'">
            <Button
                size="small"
                type="primary"
                class="btn"
                @click="operationEdit(operationData)"
                v-if="viewType == 'add' || viewType == 'edit'"
            >
                编辑
            </Button>
            &nbsp;
            <Button
                size="small"
                type="primary"
                danger
                @click="operationDelete(operationData)"
                v-if="viewType == 'add' || viewType == 'launch' || viewType == 'edit'"
            >
                删除
            </Button>
            <template v-if="viewType == 'examine' && !operationData.record?.applyStatus">
                <Button
                    size="small"
                    type="primary"
                    v-auth="'inductionServices_pass'"
                    @click="refuseOrAdopt(operationData.record, 1)"
                >
                    通过
                </Button>
                &nbsp;
                <Button
                    size="small"
                    danger
                    type="primary"
                    v-auth="'inductionServices_reject'"
                    class="btn"
                    @click="refuseOrAdopt(operationData.record, 2)"
                    >拒绝</Button
                >
            </template>
        </template>
    </Table>
</template>

<script lang="ts">
// import { RuleObject } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import { ref, defineComponent, watchEffect, h, toRefs, watch } from 'vue'
import inductionApplyStore from '/@/store/modules/inductionApply'
import { staffTypeOptions } from '/@/utils/dictionaries'
import { monthToYears } from '/@/utils/index'
import lodash from 'lodash'
export default defineComponent({
    name: 'InductionApplyEmployedStaff',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'add', 'edit', 'examine'].indexOf(value) !== -1
            },
        },
        currentValue: {
            type: Object,
        },
        cardinalArr: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['operationEdit', 'selectedRowsArr', 'getDataInfo', 'auditRequest', 'operationDelete'],
    setup(props, { emit }) {
        const { viewType, visible, cardinalArr } = toRefs<any>(props)
        const columns = ref<inObject[]>([])

        const columnsData = ref<inObject[]>([])
        watchEffect(() => {
            columnsData.value = inductionApplyStore()
                .getEmployedStaffList.filter((el) => !el.isDelete)
                .map((item) => {
                    return { ...item, disabled: false }
                })
        })
        const cardinalList = ref<any[]>([])
        const defColumn = ref([
            {
                title: '个人医疗基数',
                dataIndex: 'medicalInsuranceCardinalPersonal',
                align: 'center',
                width: 150,
            },
            {
                title: '个人养老基数',
                dataIndex: 'personalPensionCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '个人失业基数',
                dataIndex: 'personalUnemploymentCardinal',
                align: 'center',
                width: 150,
            },

            {
                title: '个人生育基数',
                dataIndex: 'personalMaternityCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '单位医疗基数',
                dataIndex: 'medicalInsuranceCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '单位养老基数',
                dataIndex: 'unitPensionCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '单位失业基数',
                dataIndex: 'unitUnemploymentCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '单位工伤基数',
                dataIndex: 'workInjuryCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '单位生育基数',
                dataIndex: 'unitMaternityCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '个人大额医疗费用',
                dataIndex: 'personalLargeMedicalExpense',
                align: 'center',
                width: 150,
            },
            {
                title: '单位大额医疗费用',
                dataIndex: 'unitLargeMedicalExpense',
                align: 'center',
                width: 150,
            },
            {
                title: '补充工伤费用',
                dataIndex: 'replenishWorkInjuryExpense',
                align: 'center',
                width: 150,
            },
        ])
        const defArr = () => [
            {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                customRender: (record) => {
                    return h('span', record.index + 1)
                },
                width: 50,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 150,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ record }) => {
                    return record.sexLabel
                },
                width: 100,
            },

            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 200,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
                width: 150,
            },
            {
                title: '岗位',
                dataIndex: 'stationId',
                align: 'center',
                customRender: ({ record }) => {
                    return record.professionName
                },
                width: 150,
            },
            {
                title: '是否计算补缴',
                dataIndex: 'supplementaryPayment',
                align: 'center',
                customRender: ({ record, text }) => {
                    if (record.supplementaryPayment == 0) {
                        record.supplementaryPayment = '不计算'
                    } else if (record.supplementaryPayment == 1) {
                        record.supplementaryPayment = '计算'
                    }
                    return text
                },
                width: 150,
            },
            {
                title: '合同开始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
            },
            {
                title: '基本工资',
                dataIndex: 'basicWage',
                align: 'center',
                width: 150,
            },
            {
                title: '工资工龄基数',
                dataIndex: 'seniorityWageBase',
                align: 'center',
                width: 150,
            },

            {
                title: '公积金基数',
                dataIndex: 'accumulationFundCardinal',
                align: 'center',
                width: 150,
            },
            {
                title: '户口性质',
                dataIndex: 'householdRegistrationLabel',
                align: 'center',
                width: 150,
            },
            {
                title: '缴费年月',
                dataIndex: 'paymentDate',
                align: 'center',
                width: 150,
            },
            {
                title: '员工类型',
                dataIndex: 'staffType',
                align: 'center',
                customRender: ({ text }) => {
                    return staffTypeOptions.find((item) => {
                        return text == item.value
                    })?.label
                },
                width: 150,
            },
            {
                title: '待转时长',
                dataIndex: 'internshipDuration',
                align: 'center',
                customRender: ({ text }) => {
                    return monthToYears(text)
                },
                width: 150,
            },
            {
                title: '审核结果',
                dataIndex: 'applyStatusLabel',
                align: 'center',
                width: 100,
                fixed: 'right',
            },
            {
                title: '拒绝原因',
                dataIndex: 'reasonRejection',
                align: 'center',
                slots: { customRender: 'reasonRejection' },
                width: 200,
                fixed: 'right',
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                fixed: 'right',
                slots: { customRender: 'operation' },
                width: 150,
            },
        ]
        const columnsList = ref()
        const setColumns = () => {
            columnsList.value = defArr()
            if (cardinalArr.value.length > 0) {
                columnsList.value.splice(11, 0, ...cardinalList.value)
            } else {
                columnsList.value.splice(11, 0, ...defColumn.value)
            }

            columns.value = columnsList.value.filter((item) => {
                if (viewType.value == 'see') {
                    return item.dataIndex !== 'operate'
                } else if (viewType.value == 'add' || viewType.value == 'edit') {
                    return item.dataIndex !== 'applyStatusLabel' && item.dataIndex !== 'reasonRejection'
                }
                return true
            })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    // setColumns()
                } else {
                    rowSelectionKeys.value = []
                }
            },
            { immediate: true },
        )
        watchEffect(() => {
            if (cardinalArr.value.length > 0) {
                cardinalList.value = []
                cardinalArr.value?.forEach((el) => {
                    cardinalList.value.push({
                        title: el.title,
                        dataIndex: el.dataIndexS[0],
                        align: 'center',
                        width: 200,
                    })
                })
            }
            if (visible.value) {
                setColumns()
            }
        })
        let rowSelectionKeys = ref<any[]>([])
        const rowSelection = {
            selectedRowKeys: rowSelectionKeys,
            onChange: (selectedRowKeys: (string | number)[], selectedRows: inObject[]) => {
                rowSelectionKeys.value = selectedRowKeys
                emit('selectedRowsArr', selectedRows)
            },
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: !!record.applyStatus, // Column configuration not to be checked
                }
            },
        }
        const operationDelete = (data) => {
            emit('operationDelete', data)
        }
        const operationEdit = (data) => {
            emit('operationEdit', data)
        }
        const refuseOrAdopt = (record, type) => {
            // let api = '/api/hr-apply-entry-staffs/approve-passed'
            if (type == 2) {
                // api = '/api/hr-apply-entry-staffs/approve-reject'
                if (!record.staffRemark) {
                    message.error('请输入拒绝理由！')
                    return
                }
            }
            emit('auditRequest', type, [record.id], record.staffRemark)
        }

        return {
            columns,
            columnsData,
            operationEdit,
            rowSelection,

            refuseOrAdopt,
            operationDelete,
        }
    },
})
</script>
<style scoped lang="less">
.smallTable {
    margin: 15px 0;
    :deep(.ant-table-thead > tr > th) {
        background-color: @primary-color;
        color: #fff;
    }
}
.editable-cell {
    position: relative;
    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
        padding-right: 24px;
    }

    .editable-cell-text-wrapper {
        padding: 5px 24px 5px 5px;
    }

    .editable-cell-icon,
    .editable-cell-icon-check {
        position: absolute;
        right: 0;
        width: 20px;
        cursor: pointer;
    }

    .editable-cell-icon {
        margin-top: 4px;
        display: none;
    }

    .editable-cell-icon-check {
        line-height: 28px;
    }

    .editable-cell-icon:hover,
    .editable-cell-icon-check:hover {
        color: #108ee9;
    }

    .editable-add-btn {
        margin-bottom: 8px;
    }
    &:hover .editable-cell-icon {
        display: inline-block;
    }
}
</style>
