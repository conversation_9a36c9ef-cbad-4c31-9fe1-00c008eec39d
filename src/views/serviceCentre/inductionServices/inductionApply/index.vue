<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'inductionServices_create'" @click="editRow('add')">新增</Button>
        <Button danger type="primary" v-auth="'inductionServices_reject'" @click="rejectRow">批量拒绝</Button>
        <Button class="btn" type="primary" v-auth="'inductionServices_pass'" @click="passRow">批量通过</Button>
        <!-- <Button type="primary" v-auth="'inductionServices_updataStatus'" @click="updataStatus()">修改入职代发合同状态</Button> -->
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-apply-entries/page"
        deleteApi="/api/hr-remind-confs/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <MyModal
        v-model:visible="showEdit"
        :title="modalTitle"
        :currentValue="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <!-- 修改员工状态 -->
    <!-- <BasicEditModalSlot
        :visible="showUpdataModal"
        title="修改员工入职状态"
        @cancel="updataStatusCancel"
        @ok="updataStatusConfirm"
        width="400px"
    >
        <div class="statusTip">此操作不可逆！！！</div>
        <Form ref="statusForm" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem
                label="入职状态"
                name="employeesStatus"
                :rules="{ required: true, message: '请选择员工入职状态', trigger: ['change', 'blur'] }"
            >
                <Select
                    v-model:value="formData.employeesStatus"
                    placeholder="员工入职状态"
                    :disabled="true"
                    :options="employeesOptions"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot> -->
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import MyModal from './MyModal.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, openNotification } from '/@/utils'
import { useRoute } from 'vue-router'
import router from '/@/router'
export default defineComponent({
    name: 'InductionApply',
    components: { MyModal, PostTree },
    setup() {
        const body = document.body
        let applicationList = ref<LabelValueOptions>([]) //申请状态
        onMounted(() => {
            if (route.query?.openAdd) {
                editRow('add', route.query)
                router.replace({})
            }
            //申请状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/leaveStates', {}).then((res) => {
                applicationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        //筛选
        const route = useRoute()
        const params = ref<any>({
            typeName: undefined,
            applyStatusList: route.query?.applyStatusList ? JSON.parse(route.query?.applyStatusList as string) : undefined,
        })
        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '客户编号',
                key: 'unitNumber',
            },
            {
                label: '客户名称',
                key: 'clientIds',
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'selectSlot',
                label: '岗位',
                key: 'stationIdList',
                placeholder: '岗位',
                maxTag: '0',
            },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'applyStatusList',
                options: applicationList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 200,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 200,
            },

            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 200,
            },
            {
                title: '人员数量',
                dataIndex: 'staffNum',
                align: 'center',
                width: 100,
            },
            {
                title: '申请日期',
                dataIndex: 'createdDate',
                align: 'center',
                width: 200,
            },
            {
                title: '状态',
                dataIndex: 'applyStatus',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return record.applyStatusLabel
                },
            },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]
        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增')
        const viewType = ref('add')
        // 当前编辑的数据
        const currentValue = ref<any>(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            modalTitle.value =
                viewTypeName == 'add'
                    ? '新增入职员工'
                    : viewTypeName == 'edit'
                    ? '编辑入职员工'
                    : viewTypeName == 'examine'
                    ? '审核'
                    : '查看'
            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }
        const modalCancel = () => {}
        const modalConfirm = () => {
            if (viewType.value == 'add') {
                searchData()
            } else {
                tableRef.value.refresh()
            }
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'inductionServices_see',
                    show: (record) => {
                        return record.applyStatus != '0'
                    },
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '编辑',
                    auth: 'inductionServices_edit',
                    show: (record) => {
                        return record.applyStatus == '0'
                    },
                    click: (record) => editRow('edit', record),
                },
                {
                    neme: '审核',
                    auth: 'inductionServices_examine',
                    show: (record) => {
                        return record.applyStatus == '1' || record.applyStatus == '5'
                    },
                    click: (record) => editRow('examine', record),
                },
            ]),
        )

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            showRejec.value = true
        }
        const checkerReason = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            request
                .post('/api/hr-apply-entries/approval-reject', {
                    applyIdList: applyIdList,
                    checkerReason: checkerReason.value,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    tableRef.value.refresh()
                })
            showRejec.value = false
        }
        // 批量通过
        const passRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    request
                        .post('/api/hr-apply-entries/approval-pass', {
                            applyIdList: applyIdList,
                            checkerReason: null,
                        })
                        .then((res) => {
                            let tip = ''
                            let success = '您选择的数据已修改成功'
                            if (res.error_status) {
                                tip = res.error_status
                                success = ',选择的其它数据已修改成功'
                            }
                            if (res.success?.length) {
                                tip += success
                            }
                            openNotification(tip)

                            tableRef.value.refresh()
                        })
                },
                onCancel() {},
            })
        }

        // 修改员工状态
        const statusForm = ref()
        const formData = ref({
            employeesStatus: '1',
        })
        const showUpdataModal = ref(false)
        const updataStatus = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先选择员工!')
                return
            }
            showUpdataModal.value = true
        }
        const updataStatusCancel = () => {
            showUpdataModal.value = false
            restUpdataModal()
        }
        const restUpdataModal = () => {
            statusForm.value?.resetFields()
            formData.value.employeesStatus = '1'
        }
        const updataStatusConfirm = () => {
            statusForm.value
                .validate()
                .then(() => {
                    let staffIds = selectedRowsArr.value?.map((el: any) => el.staffId)
                    request
                        .post(
                            `/api/hr-talent-staffs/manual_update_staff_induction_process/${formData.value.employeesStatus}`,
                            staffIds,
                        )
                        .then((res) => {
                            if (res) {
                                showUpdataModal.value = false
                                restUpdataModal()
                            }
                        })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const employeesOptions = ref<inObject[]>([
            { label: '入职待发合同', value: '1' },
            { label: '续签待发合同', value: '2' },
        ])

        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量拒绝显示
            showRejec,
            // 点击批量拒绝
            rejectRow,
            //批量拒绝描述
            checkerReason,
            //批量拒绝确认
            rejectConfirm,
            // 批量通过
            passRow,
            //新增
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,

            // 修改员状态
            // showUpdataModal,
            // updataStatus,
            // body,
            // updataStatusConfirm,
            // formData,
            // employeesOptions,
            // statusForm,
            // updataStatusCancel,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
// .statusTip {
//     color: red;
//     text-align: center;
//     font-size: 16px;
//     width: 100%;
//     margin-bottom: 20px;
// }
</style>
