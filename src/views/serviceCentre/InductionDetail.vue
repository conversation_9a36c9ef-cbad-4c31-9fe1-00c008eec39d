<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px" :footer="null">
        <div class="step-detail">
            <Steps :current="applyStep" labelPlacement="vertical" class="my_steps">
                <Step title="发送入职通知" />
                <Step title="员工录入信息" />
                <Step title="发起劳动合同" />
                <Step title="签署劳动合同" />
                <Step title="入职完成" />
            </Steps>
        </div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>申请信息</span>
            <div class="examine-flex">
                <div class="item-flex">
                    <span>客户编号：</span>
                    <span>{{ detailData.unitNumber }}</span>
                </div>
                <div class="item-flex">
                    <span>客户名称：</span>
                    <span>{{ detailData.clientName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span>协议编号：</span>
                    <span>{{ detailData.agreementNumber }}</span>
                </div>
                <div class="item-flex">
                    <span>协议标题：</span>
                    <span>{{ detailData.agreementTitle }}</span>
                </div>
                <div class="item-flex">
                    <span>协议类型：</span>
                    <span>{{ detailData.agreementTypeLabel }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span>员工姓名：</span>
                    <span>{{ detailData.name }}</span>
                </div>
                <div class="item-flex">
                    <span>身份证号：</span>
                    <span>{{ detailData.certificateNum }}</span>
                </div>
                <div class="item-flex">
                    <span>性别：</span>
                    <span>{{ detailData.sexLabel }}</span>
                </div>
                <div class="item-flex">
                    <span>联系方式：</span>
                    <span>{{ detailData.phone }}</span>
                </div>
                <div class="item-flex">
                    <span>岗位：</span>
                    <span>{{ detailData.professionName }}</span>
                </div>
                <div class="item-flex">
                    <span>人员类型：</span>
                    <span>{{ detailData.personnelTypeLabel }}</span>
                </div>
                <div class="item-flex">
                    <span>合同开始日期：</span>
                    <span>{{ detailData.contractStartDate }}</span>
                </div>
                <div class="item-flex">
                    <span>合同结束日期：</span>
                    <span>{{ detailData.contractEndDate }}</span>
                </div>
                <div class="item-flex">
                    <span>基本工资：</span>
                    <span>{{ detailData.basicWage }}</span>
                </div>
                <div class="item-flex">
                    <span>社保基数：</span>
                    <span>{{ detailData.socialSecurityCardinal }}</span>
                </div>
                <div class="item-flex">
                    <span>医保基数：</span>
                    <span>{{ detailData.medicalInsuranceCardinal }}</span>
                </div>
                <div class="item-flex">
                    <span>公积金基数：</span>
                    <span>{{ detailData.accumulationFundCardinal }}</span>
                </div>
            </div>
        </div>
        <div class="examine" v-if="type == 'look'">
            <Divider type="vertical" class="divid" />
            <span>审核信息</span>
            <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                <div class="list-item">
                    <span style="width: 40px">{{ index + 1 }}</span>
                    <div class="item-flex">
                        <span class="box1">操作人：</span>
                        <span class="box2">{{ item.realName }}</span>
                    </div>
                    <div class="item-flex2">
                        <span>操作时间：</span>
                        <span>{{ item.createdDate }}</span>
                    </div>
                    <div class="item-flex3">
                        <div class="box1">操作信息：</div>
                        <div class="box2">{{ item.message }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="examine" v-if="type == 'examine'">
            <Divider type="vertical" class="divid" />
            <span>拒绝理由</span>
            <div class="examine-area">
                <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" />
            </div>
            <div class="ant-modal-footer" style="margin-top: 60px">
                <Button danger type="primary" @click="rejectRow">拒绝</Button>
                <Button type="primary" @click="confirm">通过</Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message, Modal, notification } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'Addstaff',
    props: {
        title: String,
        type: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { title, item, visible } = toRefs<any>(props)
        const applyStep = ref(0)
        const detailData = ref<inObject>({})
        const opLogsList = ref<object[]>([])
        const checkerReason = ref('')

        const getDetail = (value) => {
            request.get('/api/hr-apply-entry-staffs', { id: value }).then((res) => {
                applyStep.value = Number(res.applyStep)
                detailData.value = res
                opLogsList.value = res.opLogsList
            })
        }
        watch(
            item,
            () => {
                if (item.value) {
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )
        const cancel = () => {
            emit('cancel')
        }
        const rejectRow = () => {
            let applyId = detailData.value.id
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            request
                .post('/api/hr-apply-entry-staffs/approve-reject', { applyIdList: [applyId], checkerReason: checkerReason.value })
                .then((res) => {
                    if (res.errorMessage) {
                        openNotification(res.errorMessage)
                    }
                    emit('cancel')
                    emit('confirm')
                })
        }
        const confirm = () => {
            let applyId = detailData.value.id
            request
                .post('/api/hr-apply-entry-staffs/approve-passed', { applyIdList: [applyId], checkerReason: checkerReason.value })
                .then((res) => {
                    if (res.body.message_exist) {
                        openNotification(res.body.message_exist)
                    }
                    emit('cancel')
                    emit('confirm')
                })
        }
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }
        return {
            checkerReason,
            applyStep,
            detailData,
            opLogsList,
            cancel,
            rejectRow,
            confirm,
        }
    },
})
</script>
<style scoped lang="less">
.step-detail {
    margin-bottom: 30px;
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 150px;
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title) {
    color: #6894fe;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
