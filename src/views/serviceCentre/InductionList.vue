<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'induction_add'" @click="addRow">新增</Button>
        &nbsp;
        <Button type="primary" v-auth="'induction_pass'" @click="passRow">批量通过</Button>
        &nbsp;
        <Button type="primary" v-auth="'induction_refuse'" danger @click="rejectRow">批量拒绝</Button>
        &nbsp;
        <Button type="primary" v-auth="'induction_entry'" @click="InductionMoreRow()">批量入职</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-apply-entry-staffs/page"
        deleteApi="/api/hr-apply-entry-staffs/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="myOperationClick" />
            <!-- <Button type="primary" size="small" @click="detailRow(record, 'look')">查看</Button>
            &nbsp;
            <Button type="primary" size="small" @click="editRow(record)">复制</Button>
            &nbsp;
            <Button type="primary" size="small" v-if="record.applyStatus == '1'" @click="detailRow(record, 'examine')"
                >审核</Button
            >
            &nbsp;
            <Button type="primary" size="small" v-if="record.applyStatus == '3'" @click="InductionRow(record)">入职</Button>
            &nbsp;
            <Button type="primary" danger size="small" v-if="record.applyStatus == '1'" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>
    <AddInduction
        v-if="showAdd"
        :visible="showAdd"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <InductionDetail
        v-if="showDetail"
        :visible="showDetail"
        :title="modalTitle"
        :item="currentDetail"
        :type="detailType"
        @cancel="detailCancel"
        @confirm="detailConfirm"
    />
    <BasicEditModalSlot :visible="showRejec" @cancel="cancel" @confirm="confirm" :title="title" width="500px" :footer="null">
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="confirm">确定</Button>
        </div>
    </BasicEditModalSlot>
    <!-- 电签 -->
    <ElectricSign :visible="showSign" :title="signTitle" :item="currentValue" @cancel="signCancel" @confirm="signConfirm" />
</template>
<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message, Modal, notification } from 'ant-design-vue'
import { ref, defineComponent, onMounted } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import addInduction from './addInduction.vue'
import InductionDetail from './InductionDetail.vue'
import permissionStore from '/@/store/modules/permission'
import { SearchBarOption } from '/#/component'
import signStore from '/@/store/modules/electricSign'
import staffStore from '/@/store/modules/staff'
// import electricSign from '/@/views/staff/staff/signContract/electricSign.vue'
import electricSign from '/@/views/serviceCentre/inductionServices/stayInductionStaff/signContract/electricSign.vue'
import { getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'InductionList',
    components: {
        ElectricSign: electricSign,
        AddInduction: addInduction,
        InductionDetail,
    },
    setup() {
        const RoleState = permissionStore().getPermission.customerService // 专管员true

        //筛选
        // let selectclientsOptions = ref<LabelValueOptions>([]) //客户名称
        let sexTypeList = ref<LabelValueOptions>([]) // 性别
        let station = ref<LabelValueOptions>([]) // 岗位
        let staffTypeList = ref<LabelValueOptions>([]) //人员类型
        let applicationList = ref<LabelValueOptions>([]) //申请状态

        //表格数据
        const columns = ref([
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ record }) => {
                    return record.sexLabel
                },
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
            },
            {
                title: '基本工资',
                dataIndex: 'basicWage',
                align: 'center',
            },
            {
                title: '专管员',
                dataIndex: 'real_name',
                align: 'center',
                customRender: ({ record }) => {
                    return record.specialized
                },
            },
            {
                title: '申请日期',
                dataIndex: 'createdDate',
                align: 'center',
            },
            {
                title: '审批状态',
                dataIndex: 'applyStatus',
                align: 'center',
                customRender: ({ record }) => {
                    return record.applyStatusLabel
                },
            },
            {
                title: '入职流程',
                dataIndex: 'entryStatus',
                align: 'center',
                customRender: ({ record }) => {
                    return record.entryStatusLabel
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ])

        onMounted(() => {
            // // 客户
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id }
            //     })
            // })
            // 性别
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 岗位
            request.get('/api/hr-stations/list', {}).then((res) => {
                station.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
            //人员类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //申请状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/applicationStatus', {}).then((res) => {
                applicationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            columns.value.forEach((item) => {
                if (RoleState) {
                    if (item.dataIndex == 'createdDate') {
                        let index = columns.value.indexOf(item)
                        columns.value.splice(index, 1)
                    }
                } else {
                    // if (item.dataIndex == 'entryStatusLabel') {
                    //     let index = columns.value.indexOf(item)
                    //     columns.value.splice(index, 1)
                    // }
                }
            })
            columns.value = [...columns.value]
        })
        //表格dom
        const tableRef = ref()
        //筛选
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '客户编号',
                key: 'unitNumber',
            },
            {
                label: '客户名称',
                key: 'clientId',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sex',
                options: sexTypeList,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'string',
                label: '岗位',
                key: 'professionName',
                // options: station,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelType',
                options: staffTypeList,
            },
            // {
            //     type: 'select',
            //     label: '专管员',
            //     key: 'specializedId',
            //     options: specializedOptions,
            // },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'applyStatus',
                options: applicationList,
            },
        ]

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        // 新增编辑
        const showAdd = ref(false)
        const modalTitle = ref('新增')
        const title = ref('批量拒绝')
        const checkerReason = ref('')
        const detailType = ref('')
        const showDetail = ref(false)
        const showRejec = ref(false)

        // 当前编辑的数据
        const currentValue = ref(null)
        const currentDetail = ref(null)
        const addRow = () => {
            showAdd.value = true
            modalTitle.value = '新增入职信息'
            currentValue.value = null
        }
        // 编辑
        const editRow = (record) => {
            showAdd.value = true
            modalTitle.value = '复制入职信息'
            currentValue.value = { ...record }
        }
        // 详情
        const detailRow = (record, type) => {
            showDetail.value = true
            modalTitle.value = '查看入职流程'
            currentDetail.value = { ...record }
            detailType.value = type
        }
        // 关闭弹窗
        const modalCancel = () => {
            showAdd.value = false
            modalTitle.value = '新增入职信息'
            currentValue.value = null
        }
        // 确认弹窗
        const modalConfirm = async () => {
            tableRef.value.refresh(1)
        }
        // 关闭弹窗
        const detailCancel = () => {
            showDetail.value = false
            modalTitle.value = '新增'
            currentDetail.value = null
        }
        // 确认弹窗
        const detailConfirm = async () => {
            tableRef.value.refresh(1)
        }
        //批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {})
        }
        // 选中的ID
        const applyIdList = ref<any>([])
        const staffList = ref<any>([])
        const noStaffIdName = ref<any>([])
        // 多选
        const selectedRowsArr = (item) => {
            let ids: (string | number)[] = []
            let staffIds: (string | number)[] = []
            let myStaffIds: any = []
            ids = item.map((val) => {
                return val.id
            })
            noStaffIdName.value = []
            myStaffIds = item.filter((val) => {
                if (val.staffId == null) {
                    noStaffIdName.value.push(val.name)
                }
                return val.staffId != null
            })
            staffIds = myStaffIds.map((val) => {
                return val.staffId
            })
            // console.log(staffIds)
            // console.log(noStaffIdName.value)
            applyIdList.value = ids
            staffList.value = staffIds
        }
        // 批量拒绝
        const rejectRow = () => {
            title.value = '批量拒绝'
            if (applyIdList.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            showRejec.value = true
        }
        // 关闭弹窗
        const cancel = () => {
            showRejec.value = false
            modalTitle.value = '新增'
            currentDetail.value = null
        }
        // 确认弹窗
        const confirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            request
                .post('/api/hr-apply-entry-staffs/approve-reject', {
                    applyIdList: applyIdList.value,
                    checkerReason: checkerReason.value,
                })
                .then((res) => {
                    tableRef.value.refresh()
                    if (res.errorMessage) {
                        openNotification(res.errorMessage)
                    }
                })
            showRejec.value = false

            applyIdList.value = []
        }
        // 批量通过
        const passRow = () => {
            if (applyIdList.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    request
                        .post('/api/hr-apply-entry-staffs/approve-passed', {
                            applyIdList: applyIdList.value,
                            checkerReason: null,
                        })
                        .then((res) => {
                            console.log(res.body)
                            if (res.body.message_exist) {
                                openNotification(res.body.message_exist)
                            }
                            if (res.body.message_error) {
                                openNotification(res.body.message_error)
                            }
                            tableRef.value.refresh()
                        })
                    // tableRef.value.refresh()
                },
                onCancel() {},
            })
        }
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }
        // 入职
        const InductionRow = (record: inObject) => {
            let ids: any[] = []
            ids.push(record.staffId)
            request.post('/api/hr-apply-entry-staffs/entry-level', ids).then((res) => {
                if (res.body.length > 0) {
                    openNotification(`选中的数据中 ${res.body?.join()}，不是待入职状态`)
                } else {
                    message.success('入职短信发送成功')
                }
                tableRef.value.refresh()
            })
        }
        // 批量入职
        const InductionMoreRow = () => {
            if (staffList.value.length <= 0) {
                message.error('请先选择入职人员!')
                return
            }
            request.post('/api/hr-apply-entry-staffs/entry-level', staffList.value).then((res) => {
                tableRef.value.refresh()
                console.log(noStaffIdName.value)
                console.log(res.body)
                let a: any = []
                if (noStaffIdName.value.length > 0 && res.body.length > 0) {
                    a = [...noStaffIdName.value, ...res.body]
                } else if (noStaffIdName.value.length == 0 && res.body.length > 0) {
                    a = [...res.body]
                } else if (noStaffIdName.value.length > 0 && res.body.length == 0) {
                    a = [...noStaffIdName.value]
                }

                if (a.length > 0) {
                    openNotification(`选中的数据中 ${a?.join()}，不是待入职状态`)
                }
            })
        }

        // 发起电签
        const showSign = ref(false)
        const signTitle = ref('发起电签')
        // 打开发起电签弹窗
        const signRow = (record) => {
            console.log(signStore().getSign.tabs)
            console.log(signStore().getSign.staffContactList)
            showSign.value = true
            signTitle.value = '发起电签'
            currentValue.value = { ...record, id: record.staffId }
            staffStore().setStaff(record.staffId)
            signStore().setClientId(record.clientId)
        }
        // 关闭发起电签弹窗
        const signCancel = () => {
            showSign.value = false
            signTitle.value = '发起电签'
            currentValue.value = null
            signStore().setContractTemplate([])
            signStore().setMustData([])
            signStore().setMoreData([])
            signStore().setTabs('1')
        }
        // 发起电签确认弹窗
        const signConfirm = async () => {
            showSign.value = false
            signTitle.value = '发起电签'
            currentValue.value = null
            tableRef.value.refresh()
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'induction_detail',
                    show: true,
                },
                {
                    neme: '复制',
                    auth: 'induction_copy',
                    show: true,
                },
                {
                    neme: '审核',
                    auth: 'induction_audit',
                    show: (record) => {
                        return record.applyStatus == '1'
                    },
                },
                {
                    neme: '入职',
                    auth: 'induction_entry',
                    show: (record) => {
                        return record.applyStatus == '3'
                    },
                },
                {
                    neme: '电签',
                    auth: 'induction_cades',
                    show: (record) => {
                        return record.izStartEnd == 1 && record.applyStatus == '4' && record.entryStatus == '2'
                    },
                },

                {
                    neme: '删除',
                    auth: 'induction_delete',
                    show: (record) => {
                        return record.applyStatus == '1'
                    },
                    type: 'delete',
                },
            ]),
        )
        const myOperationClick = (item, record) => {
            switch (item.auth) {
                case 'induction_detail':
                    detailRow(record, 'look')
                    break
                case 'induction_copy':
                    editRow(record)
                    break
                case 'induction_audit':
                    detailRow(record, 'examine')
                    break
                case 'induction_entry':
                    InductionRow(record)
                    break
                case 'induction_delete':
                    deleteRow(record)
                    break
                case 'induction_cades':
                    signRow(record)
                    break
            }
        }
        return {
            // data
            tableRef,
            columns,
            params,
            options,
            showAdd,
            modalTitle,
            currentValue,
            currentDetail,
            showDetail,
            applyIdList,
            showRejec,
            title,
            checkerReason,
            detailType,
            // 事件
            searchData,
            addRow,
            editRow,
            detailRow,
            modalCancel,
            modalConfirm,
            detailCancel,
            detailConfirm,
            deleteRow,
            selectedRowsArr,
            rejectRow,
            cancel,
            confirm,
            passRow,
            InductionRow,
            InductionMoreRow,

            //操作
            myOperation,
            myOperationClick,
            //发起电签
            showSign,
            signTitle,
            signCancel,
            signConfirm,
            signRow,
        }
    },
})
</script>
<style scoped lang="less"></style>
