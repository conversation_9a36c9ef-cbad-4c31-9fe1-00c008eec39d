<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '130px' } }"
            :rules="rules"
            style="overflow-y: auto; overflow-x: auto; margin-top: 20px; margin-right: 20px"
            class="form-flex"
        >
            <EmployeeMatching
                :formItemNames="['name', 'certificateNum']"
                v-model:name="formData.name"
                v-model:card="formData.certificateNum"
                @changeCertificateNum="changeCertificateNum"
            />
            <template v-for="ele in myOptions" :key="ele">
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]" />
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn">提交</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, nextTick } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import { valuesAndRules } from '/#/component'
import { message } from 'ant-design-vue'
import inductionApplyStore from '/@/store/modules/inductionApply'
export default defineComponent({
    name: 'IndustrialInjuryAddModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        staffTypeList: {
            type: Array,
            default: () => [],
        },
        professionNameList: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { staffTypeList, professionNameList } = toRefs(props)
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '姓名',
                name: 'name',
                show: false,
            },
            {
                label: '身份证号',
                name: 'certificateNum',
                show: false,
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
                disabled: true,
                required: false,
            },
            {
                label: '联系方式',
                name: 'phone',
                disabled: true,
            },
            {
                label: '岗位',
                name: 'professionName',
                type: 'change',
                options: professionNameList,
                onChange: (value, option) => selectChange(value, option, 'professionName'),
                disabled: true,
                required: false,
            },
            {
                label: '备注',
                name: 'juryMessage',
                type: 'textarea',
                width: '100%',
                required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        // cancel handle
        const onCancel = () => {
            emit('cancel')
            resetFormData()
        }
        const selectChange = (value, option, name) => {
            formData.value[name + 'Label'] = option.label
        }
        const validateDuration = {
            required: true,
            trigger: 'change',
            validator: () => {
                let formDataItem = formData.value?.testDuration
                if (formDataItem == '1') {
                    if (!formData.value?.timeLimit) {
                        return Promise.reject('请输入考试时长')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }
        //自动填入
        const changeCertificateNum = (userInfo) => {
            formData.value.sex = userInfo.sex
            formData.value.phone = userInfo.phone
            formData.value.personnelType = userInfo.personnelType
            formData.value.professionName = userInfo.stationId
            formData.value.staffId = userInfo.id
            formValidateOptional(['name', 'sex', 'phone', 'personnelType'])
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        // 单个校验
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate(nameList)
            })
        }
        // confirm handle
        const onConfirm = () => {
            let validate = inductionApplyStore().getEmployedStaffList.some((item, i) => {
                return item.certificateNum == formData.value?.certificateNum
            })
            if (validate) {
                message.warning('您输入的身份证号有重复，请重新输入')
                return
            }
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    emit('confirm', formData.value)
                    resetFormData()
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,

            validateDuration,
            changeCertificateNum,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
</style>
