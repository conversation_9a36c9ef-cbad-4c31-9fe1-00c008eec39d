<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar
            :stepsData="item.application == '用工单位' ? stepsData : stepsData2"
            labelPlacement="vertical"
            :currentStep="getCurrentStep()"
        />
        <ApplicationInfo :detailData="item">
            <template #inner>
                <div class="item-flex">
                    <span class="label">申请描述：</span>
                    <span>{{ item?.injuryDescription }}</span>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" />
        <div class="examine" v-if="modalType !== 'look'">
            <Divider type="vertical" class="divid" />
            <span>操作</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: modalType == 'update_results' ? '130px' : '80px' } }"
                    :rules="rules"
                    class="form-flex"
                >
                    <template v-if="modalType == 'update_results'">
                        <div style="display: flex; width: 100%" v-if="item.appraisalStatus">
                            <FormItem label="停工留薪开始日期" name="workStoppageStartDate" style="width: 100%">
                                <DatePicker
                                    v-model:value="formData.workStoppageStartDate"
                                    format="YYYY-MM-DD"
                                    placeholder="请选择开始日期"
                                    valueFormat="YYYY-MM-DD"
                                    :disabled="true"
                                    :getPopupContainer="
                                        () => {
                                            return document.body
                                        }
                                    "
                                />
                            </FormItem>
                            <FormItem label="停工留薪结束日期" name="workStoppageEndDate" style="width: 100%">
                                <DatePicker
                                    v-model:value="formData.workStoppageEndDate"
                                    format="YYYY-MM-DD"
                                    placeholder="请选择结束日期"
                                    valueFormat="YYYY-MM-DD"
                                    :getPopupContainer="
                                        () => {
                                            return document.body
                                        }
                                    "
                                />
                            </FormItem>
                        </div>
                    </template>
                    <FormItem label="附件" name="formData.appendixIds" :rules="validateAppend" style="width: 100%">
                        <ImportFile v-model:fileUrls="formData.appendixIds" ref="refImportFile" />
                    </FormItem>
                    <FormItem label="说明" name="juryMessage" style="width: 100%">
                        <Textarea v-model:value="formData.juryMessage" :rows="3" allowClear placeholder="请输入说明" />
                    </FormItem>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="modalType !== 'look'">
                <Button @click="onConfirm" type="primary" class="btn">提交</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import ApplicationInfo from '../retirement/component/ApplicationInfo.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'IndustrialInjuryModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const refImportFile = ref()
        const { visible, modalType, item } = toRefs(props)
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '附件',
                name: 'appendixIds',
                default: [],
                ruleType: 'array',
                required: true,
            },
            {
                label: '停工留薪开始日期',
                name: 'workStoppageStartDate',
            },
            {
                label: '停工留薪结束日期',
                name: 'workStoppageEndDate',
            },
            {
                label: '说明',
                name: 'juryMessage',
                required: false,
            },
        ])
        const stepsData = ref([
            {
                title: '通知员工进行劳动能力鉴定',
            },
            {
                title: '员工反馈',
            },
            {
                title: '员工进行鉴定中',
            },
            {
                title: '更新鉴定结果',
            },
        ])
        const stepsData2 = ref([
            {
                title: '员工发起劳动能力鉴定申请',
            },
            {
                title: '员工进行鉴定中',
            },
            {
                title: '更新鉴定结果',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(
            item,
            (val) => {
                if (val) {
                    if (val.injuryDate) {
                        formData.value.workStoppageStartDate = val.injuryDate
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            modalType.value !== 'look' && formInline.value.resetFields()
        }

        const getCurrentStep = () => {
            if (item.value.application == '用工单位') {
                if (item.value.status == 3 || item.value.status == 4) return 1
                else return item.value.status
            } else {
                if (item.value.status == 2) return 1
                else return item.value.status
            }
        }

        // cancel handle
        const onCancel = () => {
            resetFormData()
            emit('cancel')
        }
        const validateAppend = {
            required: true,
            trigger: 'change',
            validator: () => {
                let formDataItem = formData.value?.appendixIds
                if (!formDataItem.length) {
                    return Promise.reject('附件不能为空')
                } else {
                    return Promise.resolve()
                }
            },
        }
        // confirm handle
        const onConfirm = () => {
            let myUrl = []
            if (formData.value.appendixIds.length) {
                myUrl = refImportFile.value.getFileUrls().map((item) => {
                    return item.id
                })
            }
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (modalType.value == 'send') {
                        emit(
                            'confirm',
                            {
                                ...formData.value,
                                appendixIds: myUrl,
                                appraisalStatus: item.value.appraisalStatus,
                                clientId: item.value.clientId,
                            },
                            4,
                        )
                        resetFormData()
                    } else {
                        if (item.value.appraisalStatus) {
                            emit('confirm', { ...formData.value, appendixIds: myUrl, clientId: item.value.clientId }, 100)
                            resetFormData()
                        } else {
                            emit(
                                'confirm',
                                { ...formData.value, appendixIds: myUrl, appraisalStatus: false, clientId: item.value.clientId },
                                5,
                            )
                            resetFormData()
                        }
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            refImportFile,
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,
            stepsData,
            stepsData2,

            validateAppend,
            getCurrentStep,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex-inner {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.item-flex-outer {
    margin: 5px 0px;
    padding-left: 15px;
    display: flex;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
.item-flex {
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
</style>
