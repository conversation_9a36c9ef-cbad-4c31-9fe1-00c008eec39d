<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #professionIdList="itemForm">
            <PostTree
                v-model:value="params.professionIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'labourIdentify_batch_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'labourIdentify_add'" @click="showAddModal()">新建</Button>
        <Button type="primary" @click="batchNotic(record, 'multiple')" v-auth="'labourIdentify_batch_notice'" class="downloadBtn">
            批量通知
        </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-labor-appraisals/page"
        deleteApi="/api/hr-labor-appraisals/deletes"
        exportUrl="/api/hr-labor-appraisals/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <MyModal
        :visible="modalVisible"
        :item="currentValue"
        :modalType="detailType"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <AddModal
        :visible="addModalVisible"
        :staffTypeList="staffTypeList"
        :professionNameList="professionNameList"
        @confirm="addLabourIdentify"
        :title="modalTitle"
        @cancel="cancelHandle"
    />
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, onBeforeMount, computed } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import permissionStore from '/@/store/modules/permission'
import { SearchBarOption } from '/#/component'
import { sexList, staffStatus } from '/@/utils/dictionaries'

import modal from './modal.vue'
import AddModal from './addModal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'LabourIdentify',
    components: {
        MyModal: modal,
        AddModal,
        PostTree,
    },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false

        // 筛选
        let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        let statusTypeList = ref<LabelValueOptions>([]) // 状态
        let specializedList = ref<LabelValueOptions>([]) // 专管员
        let professionNameList = ref<LabelValueOptions>([]) // 岗位

        let options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位名称',
                key: 'professionIdList',
                placeholder: '岗位名称',
                maxTag: 0,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '受伤日期',
                key: 'injuryDateList',
            },
            {
                type: 'daterange',
                label: '停工留薪开始时间',
                key: 'workStoppageStartDateList',
            },
            {
                type: 'daterange',
                label: '停工留薪结束时间',
                key: 'workStoppageEndDateList',
            },
            {
                type: 'string',
                label: '申请方',
                key: 'application',
            },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'applicationDateList',
            },
            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                options: statusTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'specializedList',
                options: specializedList,
                multiple: true,
            },
        ]

        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 180,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },

            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 130,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return staffTypeList.value.find((el) => {
                        return record.personnelType == el.value
                    })?.label
                },
            },
            {
                title: '受伤日期',
                dataIndex: 'injuryDate',
                align: 'center',
                width: 100,
            },
            {
                title: '申请描述',
                dataIndex: 'injuryDescription',
                align: 'center',
                width: 160,
                ellipsis: true,
            },
            {
                title: '停工留薪期',
                dataIndex: 'workStoppageStartDate',
                align: 'center',
                width: 180,
                customRender: ({ record }) => {
                    if (record.workStoppageStartDate)
                        return `${record.workStoppageStartDate}至${record?.workStoppageEndDate || '~'}`
                    else return ''
                },
            },
            {
                title: '申请方',
                dataIndex: 'application',
                align: 'center',
                width: 100,
            },
            {
                title: '申请日期',
                dataIndex: 'applicationDate',
                align: 'center',
                width: 110,
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return statusTypeList.value.find((el) => {
                        return record.status == el.value
                    })?.label
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 130,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 170,
                fixed: 'right',
            },
        ])

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = options.findIndex((el) => {
                    return el.key == 'specializedList'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'specialized'
                })
                options.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        onMounted(() => {
            // 人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 状态
            dictionaryDataStore()
                .setDictionaryData('labourIdentifyState', '')
                .then((data: inObject[]) => {
                    statusTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 专管员
            dictionaryDataStore()
                .setDictionaryData('Specialized', '/api/users/getSpecialized')
                .then((data: inObject[]) => {
                    specializedList.value = data.map((item) => {
                        return { label: item.realName, value: item.id }
                    })
                })
            dictionaryDataStore()
                .setDictionaryData('hr-stations', '/api/hr-stations/list')
                .then((res: any) => {
                    professionNameList.value = res.map((item) => {
                        return { label: item.professionName, value: item.id }
                    })
                })
        })

        //表格dom
        const tableRef = ref()
        //筛选
        const params = ref({})

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const modalTitle = ref('')
        const modalVisible = ref(false)
        const addModalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<inObject>({})
        const showAddModal = () => {
            modalTitle.value = '添加员工'
            addModalVisible.value = true
        }
        // 显示弹窗
        const showModal = (record, type) => {
            switch (type) {
                case 'look':
                    modalVisible.value = true
                    modalTitle.value = '查看'
                    break
                case 'update_results':
                    modalVisible.value = true
                    modalTitle.value = '更新鉴定结果'
                    break
                case 'send':
                    modalVisible.value = true
                    modalTitle.value = '处理通知'
                    break
            }
            getDetails(record.id)
            detailType.value = type
        }

        // 获取详细信息
        const getDetails = (id) => {
            request
                .post('/api/hr-labor-appraisals/detail', { id: id })
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        sexLabel: sexList.find((el) => {
                            return res.sex == el.value
                        })?.label,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = () => {
            modalVisible.value = false
            addModalVisible.value = false
        }
        // 新建
        const addLabourIdentify = (obj) => {
            request
                .post('/api/hr-labor-appraisals', obj)
                .then((res: inObject) => {
                    console.log(res)
                    tableRef.value.refresh(1)
                    message.success('新建成功')
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const executeOperation = (obj, status) => {
            let msg = ''
            switch (status) {
                case 4:
                    msg = '已处理通知'
                    dealNotice(obj, status, msg)
                    break
                case 5:
                    msg = '已更新鉴定结果'
                    dealNotice(obj, status, msg)
                    break
                case 100:
                    msg = '已更新鉴定结果'
                    updateIdentifyResults(obj, msg)
                    break
            }
        }
        // 处理通知
        const dealNotice = (obj, status, msg) => {
            request
                .put('/api/hr-labor-appraisals', {
                    id: currentValue.value.id,
                    ...obj,
                    status: status,
                    staffId: currentValue.value.staffId,
                    clientId: currentValue.value.clientId,
                })
                .then((res: inObject) => {
                    console.log(res)
                    tableRef.value.refresh()
                    message.success(msg)
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 更新鉴定结果
        const updateIdentifyResults = (obj, msg) => {
            request
                .post('/api/hr-work-injuries/identification-result', {
                    ...obj,
                    id: currentValue.value.workInjuryId,
                    clientId: currentValue.value.clientId,
                })
                .then((res) => {
                    tableRef.value.refresh()
                    cancelHandle()
                    message.success(msg)
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 批量通知
        const batchNotic = (obj, type) => {
            if (type !== 'single') {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请选择员工!')
                    return
                }
            }
            let selectRows: inObject[] = []
            if (type == 'single') {
                selectRows = [{ ...obj }]
            } else {
                selectRows = selectedRowsArr.value
            }
            request
                .post('/api/hr-labor-appraisals/update', selectRows)
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '已通知员工进行劳动能力鉴定'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他员工已成功通知'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // 多选
        const selectedRowsArr = ref([])

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'labourIdentify_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '通知',
                    auth: 'labourIdentify_notice',
                    show: (record) => {
                        return record.status == 0
                    },
                    click: (record) => batchNotic(record, 'single'),
                },
                {
                    neme: '鉴定结果',
                    auth: 'labourIdentify_update_results',
                    show: (record) => {
                        return record.status == 2
                    },
                    click: (record) => showModal(record, 'update_results'),
                },
                {
                    neme: '处理通知',
                    auth: 'labourIdentify_send',
                    show: (record) => {
                        return record.status == 3
                    },
                    click: (record) => showModal(record, 'send'),
                },
            ]),
        )

        return {
            exportText,
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            addModalVisible,
            currentValue,
            detailType,
            staffTypeList,
            professionNameList,
            // 事件
            searchData,
            confirm,
            showAddModal,
            cancelHandle,
            batchExport,
            batchNotic,
            addLabourIdentify,
            executeOperation,

            //操作
            myOperation,
            selectedRowsArr,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
