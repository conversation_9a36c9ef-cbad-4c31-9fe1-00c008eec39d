<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #clientList="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                :checkStrictly="false"
                v-model:value="params.clientList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="addRegularWorker">新增</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button class="btn" v-auth="'regularWorker_pass'" type="primary" @click="passRow">批量通过</Button>
        <Button danger type="primary" v-auth="'regularWorker_refuse'" @click="rejectRow">批量拒绝</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-staff-turn-positives/page"
        exportUrl="/api/hr-staff-turn-positives/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="refuse" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <AddModal :visible="showRegularWorker" :title="modalTitle" @cancel="regularWorkerCancel" @confirm="regularWorkerConfirm" />
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onBeforeMount, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import modal from './modal.vue'
import AddModal from './addModal.vue'
import request from '/@/utils/request'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import permissionStore from '/@/store/modules/permission'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'InductionApply',
    components: { MyModal: modal, ClientSelectTree, AddModal },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false
        let staffTypeList = ref<LabelValueOptions>([
            {
                label: '实习中',
                value: 3,
            },
            {
                label: '试用中',
                value: 9,
            },
        ]) //员工状态
        //状态 企业pc
        let statesListEnterprise = ref<any>([
            {
                label: '待确认',
                value: 0,
            },
            {
                label: '待客户审核',
                value: null,
            },
            // {
            //     label: '待客户审核',
            //     value: 1,
            // },
            {
                label: '已拒绝',
                value: 2,
            },
        ])
        //状态 客户pc
        let statesListCustomer = ref<any>([
            {
                label: '待专管员确认',
                value: 1,
            },
            {
                label: '待审核',
                value: null,
            },
            {
                label: '已拒绝',
                value: 0,
            },
        ])
        //状态
        let statesList = ref<any>([])
        onBeforeMount(() => {
            console.log(RoleState)
            if (!RoleState) {
                //客户
                statesList.value = [...statesListCustomer.value]
            } else {
                //企业
                statesList.value = [...statesListEnterprise.value]
            }
        })

        // onMounted(() => {
        //     //员工状态
        //     request.get('/api/com-code-tables/getCodeTableByInnerName/staffStates', {}).then((res) => {
        //         staffTypeList.value = res.map((item) => {
        //             return { label: item.itemName, value: item.itemValue }
        //         })
        //     })
        // })
        //筛选
        const params = ref<Recordable>({})

        const searchOptions: SearchBarOption[] = [
            {
                type: 'selectSlot',
                label: '客户名称',
                key: 'clientList',
                // options: selectclientsOptions,
                placeholder: '客户名称',
                maxTag: 1,
            },
            {
                type: 'string',
                label: '员工编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '员工入职日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '试用/实习结束日期',
                key: 'internshipDateStartDateQuery',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 200,
            },
            {
                title: '员工编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 200,
            },

            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '员工状态',
                dataIndex: 'staffType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
                width: 100,
            },
            {
                title: '员工入职日期',
                dataIndex: 'boarDate',
                align: 'center',
                width: 150,
            },
            {
                title: '试用/实习结束日期',
                dataIndex: 'internshipDate',
                align: 'center',
                width: 180,
            },
            {
                title: '申请描述',
                dataIndex: 'applicationDescription',
                align: 'center',
                width: 180,
                ellipsis: true,
            },
            {
                title: '专管员',
                dataIndex: 'specializedName',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return record.specializedName
                },
            },
            {
                title: '状态',
                dataIndex: 'states',
                align: 'center',
                width: 150,
                customRender: ({ text, record }) => {
                    if (!RoleState) {
                        //客户
                        return statesList.value.find((item) => {
                            return text == item.value
                        })?.label
                    } else {
                        //企业、专管员
                        let myText = ''
                        if (record.states == 1 && record.enterpriseStates == 0) {
                            myText = '待确认'
                        } else if (record.states == null && record.enterpriseStates == 0) {
                            myText = '待客户审核'
                        } else if (record.states == 1 && record.enterpriseStates == 2) {
                            myText = '已拒绝'
                        }
                        return myText
                    }
                },
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]
        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增')
        const viewType = ref('add')
        // 当前编辑的数据
        const currentValue = ref<any>(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            if (viewTypeName == 'confirm') {
                modalTitle.value = '确认员工转正'
            } else if (viewTypeName == 'see') {
                modalTitle.value = '查看'
            } else {
                modalTitle.value = '审核'
            }
            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }

        const showRegularWorker = ref<any>(false)
        const addRegularWorker = () => {
            showRegularWorker.value = true
            modalTitle.value = '新建转正'
        }
        const regularWorkerCancel = () => {
            showRegularWorker.value = false
        }
        const regularWorkerConfirm = () => {
            showRegularWorker.value = false
            tableRef.value.refresh(1)
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        const modalCancel = () => {
            showEdit.value = false
        }
        const modalConfirm = () => {
            searchData()
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'regularWorker_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '确认转正',
                    auth: 'regularWorker_confirm',
                    show: (record) => {
                        if (RoleState) {
                            return record.states == 1 && record.enterpriseStates == 0
                        }
                    },
                    click: (record) => editRow('confirm', record),
                },
                {
                    neme: '审核',
                    auth: 'regularWorker_examine',
                    show: (record) => {
                        if (!RoleState) {
                            return record.states == null
                        }
                    },
                    click: (record) => editRow('examine', record),
                },
            ]),
        )

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            showRejec.value = true
        }
        const refuse = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!refuse.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            let hrFertilityDTOList = selectedRowsArr.value.map((item: any) => {
                return {
                    ...item,
                    refuse: refuse.value,
                }
            })
            let myStatusRef: any = null
            if (!RoleState) {
                //客户
                myStatusRef = 0
            } else {
                //企业
                myStatusRef = 2
            }
            request.post(`/api/hr-staff-turn-positives/batch_consent?status=${myStatusRef}`, hrFertilityDTOList).then((res) => {
                let tip = ''
                let success = '您选择的数据已修改成功'
                if (res.error_status) {
                    tip = res.error_status
                    success = ',选择的其它数据已修改成功'
                }
                if (res.success?.length) {
                    tip += success
                }
                openNotification(tip)
                tableRef.value.refresh()
            })
            showRejec.value = false

            // applyIdList.value = []
        }
        // 批量通过
        const passRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            let hrFertilityDTOList = [...selectedRowsArr.value]
            let myStatus: any = null
            myStatus = 1
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    request
                        .post(`/api/hr-staff-turn-positives/batch_consent?status=${myStatus}`, hrFertilityDTOList)
                        .then((res: any) => {
                            let tip = ''
                            let success = '您选择的数据已修改成功'
                            if (res.error_status) {
                                tip = res.error_status
                                success = ',选择的其它数据已修改成功'
                            }
                            if (res.success?.length) {
                                tip += success
                            }
                            openNotification(tip)

                            tableRef.value.refresh()
                        })
                    // tableRef.value.refresh()
                },
                onCancel() {},
            })
        }
        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量拒绝显示
            showRejec,
            // 点击批量拒绝
            rejectRow,
            //批量拒绝描述
            refuse,
            //批量拒绝确认
            rejectConfirm,
            // 批量通过
            passRow,
            //新增
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            exportData,
            exportText,
            showRegularWorker,
            addRegularWorker,
            regularWorkerCancel,
            regularWorkerConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
