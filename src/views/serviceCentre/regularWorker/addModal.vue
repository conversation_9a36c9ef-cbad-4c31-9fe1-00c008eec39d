<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="600px"
        :footer="null"
        @selectedRow="templateSelect"
    >
        <div>
            <Form
                ref="formInline"
                :model="formData"
                :rules="rules"
                style="
                    overflow-y: auto;
                    overflow-x: auto;
                    margin-top: 20px;
                    margin-right: 20px;
                    padding-left: 40px;
                    padding-right: 40px;
                "
            >
                <div class="btns">
                    <Button type="primary" @click="selectedStaff">选择员工</Button>
                </div>
                <template v-for="(item, index) in myOptions" :key="index">
                    <MyFormItem
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        :class="item.slots"
                        v-if="item.show != false"
                    >
                        <template #staffInfo>
                            <Input :disabled="true" placeholder="请选择员工" v-model:value="formData.staffId" />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="ant-modal-footer">
                <Button key="back" @click="cancel">取消</Button>
                <Button key="submit" type="primary" @click="confirm">确定</Button>
            </div>
        </div>
    </BasicEditModalSlot>
    <staffChoiceList :visible="showSelectedStaff" :viewType="viewType" @cancel="selectedCancel" @confirm="selectedConfirm" />
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'
import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import staffChoiceList from '/@/components/EmployeeMatching/src/staffChoiceList.vue'
export default defineComponent({
    name: 'AddStaff',
    components: { staffChoiceList },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'selectedRow'],

    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const { visible } = toRefs<any>(props)
        watch(visible, () => {
            if (visible) {
                formData.value = { ...Object.assign({}, initFormData) }
            }
        })
        const sexList = ref<object[]>([]) // 性别
        const station = ref<object[]>([]) //岗位
        const staffTypeList = ref<object[]>([]) //人员类型
        const staffsInfo = ref<any>()
        const showTemplateProot = ref<any>(false)
        const showSelectedStaff = ref<any>(false)

        const selectedTemplate = () => {
            showTemplateProot.value = true
        }
        const selectIds: any = ref([])
        const templateSelect = (res) => {
            console.log(res)
        }
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '员工信息',
                name: 'staffId',
                type: 'slots',
                slots: 'staffInfo',
            },
            {
                label: '试用/实习结束日期',
                name: 'internshipDate',
                type: 'date',
            },
            {
                label: '申请描述',
                name: 'applicationDescription',
                type: 'textarea',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const viewType = ref<any>()
        const selectedStaff = () => {
            showSelectedStaff.value = true
            viewType.value = 'regularWorker'
        }
        const selectedCancel = () => {
            showSelectedStaff.value = false
        }
        let templateTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('certificateTemplateType', '')
                .then((data: inObject[]) => {
                    templateTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const staffChoiceInfo = ref<any>({})
        const templateList = ref<any>()
        const appendList = ref<any>()
        const selectedConfirm = (data) => {
            formData.value.staffId = data.name
            staffChoiceInfo.value = data
        }
        watch(
            visible,
            () => {
                if (visible) {
                }
            },
            { immediate: true },
        )

        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const confirm = () => {
            let formNewData = {
                staffId: staffChoiceInfo.value?.id,
                internshipDate: formData.value.internshipDate,
                applicationDescription: formData.value.applicationDescription,
            }

            formInline.value
                .validate()
                .then(async () => {
                    await request.post('/api/hr-staff-turn-positives/apply/create', formNewData)
                    message.success('新增成功!')

                    emit('confirm')
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        return {
            rules,
            formData,
            myOptions,
            formInline,
            sexList,
            station,
            staffTypeList,
            selectedTemplate,
            showTemplateProot,
            // clientList,
            // clientId,
            // loadData,
            confirm,
            cancel,
            selectIds,
            templateSelect,
            selectedStaff,
            showSelectedStaff,
            selectedCancel,
            staffsInfo,
            selectedConfirm,
            viewType,
        }
    },
})
</script>
<style scoped lang="less"></style>
