<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" :currentStep="currentStep" labelPlacement="vertical" />
        <BasicInfo :viewType="viewType" :basicInfoList="basicInfoList" />
        <div class="examine" v-if="viewType === 'examine'">
            <Divider type="vertical" class="divid" />
            <span>客户审核信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: '135px' } }"
                    :rules="rules"
                    class="form-flex"
                    style="margin-top: 10px"
                >
                    <FormItem label="是否通过转正申请" name="states" style="width: 50%">
                        <RadioGroup v-model:value="formData.states" :options="optionsList" @change="tabsChange" />
                    </FormItem>
                    <FormItem label="转正后的基础薪资" name="salary" style="width: 50%" v-if="formData.states == 1">
                        <Input v-model:value="formData.salary" placeholder="请输入转正后的基础薪资" style="width: 190px" />
                    </FormItem>
                    <FormItem label="附件" name="formData.clientAppendixId" style="width: 100%">
                        <ImportFile v-model:fileUrls="formData.clientAppendixId" ref="refImportFile" />
                    </FormItem>
                    <FormItem label="备注" name="clientRemark" style="width: 50%">
                        <Textarea v-model:value="formData.clientRemark" :rows="3" allowClear placeholder="请输入备注" />
                    </FormItem>
                    <FormItem label="拒绝理由" name="refuse" style="width: 50%" v-if="formData.states == 0">
                        <Textarea v-model:value="formData.refuse" :rows="3" allowClear placeholder="请输入拒绝理由" />
                    </FormItem>
                </Form>
            </div>
        </div>

        <div class="examine" v-if="viewType !== 'see' && viewType !== 'examine'">
            <Divider type="vertical" class="divid" />
            <span>专管员审核信息</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: viewType == 'retire' ? '80px' : '110px' } }"
                    :rules="rules"
                    class="form-flex"
                >
                    <FormItem label="附件" name="formData.clientAppendixId" style="width: 100%">
                        <ImportFile v-model:fileUrls="formData.clientAppendixId" ref="refImportFile" />
                    </FormItem>
                    <FormItem label="备注" name="clientRemark" style="width: 100%">
                        <Textarea v-model:value="formData.clientRemark" :rows="3" allowClear placeholder="请输入备注" />
                    </FormItem>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="viewType !== 'see'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import BasicInfo from './component/BasicInfo.vue'
import request from '/@/utils/request'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import permissionStore from '/@/store/modules/permission'
export default defineComponent({
    name: 'RetireModal',
    components: { StepBar, BasicInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false
        // 薪资校验
        const validateSalaryCode = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.reject('请输入转正后的基础薪资')
            }
            let parnt = /^[0-9]\d*(\.\d+)?$/
            if (!parnt.test(value)) {
                return Promise.reject('请输入正确的薪资')
            } else {
                return Promise.resolve()
            }
        }

        const { viewType, item } = toRefs(props)
        //表单数据
        const myOptions = ref([
            {
                label: '是否通过转正申请',
                name: 'states',
                // required: true,
                ruleType: 'number',
                default: 1,
            },
            {
                label: '转正后的基础薪资',
                name: 'salary',
                validator: validateSalaryCode,
            },
            {
                label: '附件',
                name: 'clientAppendixId',
                required: false,
            },
            {
                label: '备注',
                name: 'clientRemark',
                required: false,
            },
            {
                label: '拒绝理由',
                name: 'refuse',
            },
        ])
        const stepsData = ref([
            {
                title: '发起申请',
            },
            {
                title: '待客户审核',
            },
            {
                title: '待专管员确认',
            },
            {
                title: '已结束',
            },
        ])
        const currentStep = ref<number>(0)
        const optionsList = ref<any>([
            { label: '通过', value: 1 },
            { label: '拒绝', value: 0 },
        ])
        //请求
        const { visible } = toRefs(props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(
            visible,
            () => {
                if (visible.value) {
                    if (item.value?.staffId) {
                        nextTick(() => {
                            getData()
                        })
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )
        //基本信息数据
        const basicInfoList = ref<any>({})
        //获取详情数据
        const getData = () => {
            request.get('/api/hr-staff-turn-positives-select', { staffId: item.value?.staffId }).then((res) => {
                basicInfoList.value = res
                if (res.states != null && viewType.value == 'examine') {
                    if (formData.value.states == 1) {
                        formData.value.refuse = ''
                    } else {
                        formData.value.salary = ''
                    }
                }
                if (!RoleState) {
                    //客户
                    if (res?.states == 1) {
                        currentStep.value = 2 //待专管员确认
                    } else if (res?.states == null) {
                        currentStep.value = 1 //待客户审核
                    } else if (res?.states == 0) {
                        currentStep.value = 3 //已结束
                    }
                } else {
                    //企业
                    if (res?.states == 1 && res?.enterpriseStates == 0) {
                        currentStep.value = 2 //待专管员确认
                    } else if (res?.states == null && res?.enterpriseStates == 0) {
                        currentStep.value = 1 //待客户审核
                    } else if (res?.states == 1 && res?.enterpriseStates == 2) {
                        currentStep.value = 3 //已结束
                    }
                }
                // if (res.states == null) {
                //     currentStep.value = 1
                // } else if (res.states == 1) {
                //     currentStep.value = 2
                // } else if (res.states == 0) {
                //     currentStep.value = 3
                // }
                // if (res.enterpriseStates) {
                //     currentStep.value = 3
                // }
            })
        }
        // 是否通过转正申请切换
        const tabsChange = (value) => {
            // 0是拒绝  1是通过
            formData.value.states = value.target.value
            if (formData.value.states == 1) {
                formData.value.refuse = ''
            } else {
                formData.value.salary = ''
            }
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        // cancel handle
        const onCancel = () => {
            emit('cancel')
            if (viewType.value !== 'see') {
                resetFormData()
                formData.value.clientAppendixId = null
            } else {
                basicInfoList.value = {}
            }
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        //附件
        const refImportFile = ref()
        //请求
        const api = '/api/hr-staff-turn-positives'
        // confirm handle
        const onConfirm = () => {
            let appendixIdList = refImportFile.value.getFileUrls().map((item) => {
                return item.id
            })
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (viewType.value == 'examine') {
                        //审核
                        formData.value.clientId = item.value?.clientId
                    }
                    formData.value.staffId = item.value?.staffId
                    formData.value.id = item.value?.id
                    await request.post(api || '', { ...formData.value, clientAppendixId: appendixIdList.join() })
                    message.success('确认成功!')
                    onCancel()
                    emit('confirm')
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,
            stepsData,
            optionsList,
            basicInfoList,
            tabsChange,
            refImportFile,
            currentStep,

            limitNumber,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
