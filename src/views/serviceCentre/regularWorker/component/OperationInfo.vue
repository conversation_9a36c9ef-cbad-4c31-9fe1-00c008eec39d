<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>操作信息</span>
        <div class="examine-wrap">
            <p class="linefeed"></p>
            <template v-for="(item, index) in operationLog" :key="'operation' + index">
                <div class="operation_item">
                    <span class="value">{{ index + 1 }}</span>
                    <span class="label">操作人：</span>
                    <span class="value">{{ item.name }}</span>
                    <span class="label">操作时间：</span>
                    <span class="value">{{ item.time }}</span>
                    <span class="label">操作信息：</span>
                    <span class="value">{{ item.detail }}</span>
                    <Divider class="divid_item" v-if="index !== operationLog.length - 1" />
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
export default defineComponent({
    name: 'OperationInfo',
    setup() {
        const operationLog = ref<inObject[]>([
            {
                name: '张三',
                time: '2021-10-21',
                detail: '×××通知张三准备退休审档资料',
            },
            {
                name: '张三',
                time: '2021-10-21',
                detail: '×××确认收到了张三的退休审档资料',
            },
            {
                name: '张三',
                time: '2021-10-21',
                detail: '×××更新了张三的实际退休时间。附：×××.pdf',
            },
            {
                name: '张三',
                time: '2021-10-21',
                detail: '×××办理了张三的退休手续。附：退休审批表.pdf',
            },
        ])

        return {
            operationLog,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    .operation_item {
        font-size: 14px;
        .label {
            color: rgba(153, 153, 153, 1);
        }
        .value {
            font-weight: 600;
            color: rgba(51, 51, 51, 1);
            margin-right: 20px;
        }
        .divid_item {
            margin: 10px 0;
        }
    }
}
</style>
