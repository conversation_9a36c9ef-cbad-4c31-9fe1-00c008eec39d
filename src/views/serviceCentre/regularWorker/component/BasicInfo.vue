<template>
    <div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>基本信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">客户编号：</span>
                    <span>{{ basicInfoList.unitNumber }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">客户名称：</span>
                    <span>{{ basicInfoList.clientName }}</span>
                </div>
                <div class="item-flex"></div>
                <div class="item-flex"></div>
                <div class="item-flex">
                    <span class="label">员工编号：</span>
                    <span>{{ basicInfoList.systemNum }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">姓名：</span>
                    <span>{{ basicInfoList.name }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">身份证号：</span>
                    <span>{{ basicInfoList.certificateNum }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">性别：</span>
                    <span>{{ basicInfoList.sex == 1 ? '男' : '女' }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">联系方式：</span>
                    <span>{{ basicInfoList.phone }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">岗位：</span>
                    <span>{{ basicInfoList.professionName }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">人员类型：</span>
                    <span>{{ basicInfoList.personnelTypeLabel }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">员工状态：</span>
                    <span>{{ basicInfoList.staffStatusLabel }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">员工入职日期：</span>
                    <span>{{ basicInfoList.boarDate }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">试用/实习结束日期：</span>
                    <span>{{ basicInfoList.internshipDate }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">合同开始日期：</span>
                    <span>{{ basicInfoList.contractStartDate }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">合同结束日期：</span>
                    <span>{{ basicInfoList.contractEndDate }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">专管员：</span>
                    <span>{{ basicInfoList.specializedName }}</span>
                </div>
                <div class="item-flex" v-if="viewType === 'examine'">
                    <span class="label">申请时间：</span>
                    <span>{{ basicInfoList.applyCreatedDate }}</span>
                </div>
            </div>
        </div>
        <div class="examine" v-if="viewType !== 'see' && viewType !== 'examine'">
            <Divider type="vertical" class="divid" />
            <span>客户审核信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex1">
                    <span class="label">是否通过转正申请：</span>
                    <span>{{ basicInfoList.states ? (basicInfoList.states == 2 ? '拒绝' : '通过') : '' }}</span>
                </div>

                <div class="item-flex1" v-if="basicInfoList.states == 2">
                    <span class="label">拒绝理由：</span>
                    <span>{{ basicInfoList.refuse }}</span>
                </div>
                <div class="item-flex1" v-else>
                    <span class="label">转正后薪资：</span>
                    <span>{{ basicInfoList.salary ? basicInfoList.salary + '/月' : '' }}</span>
                </div>
                <div class="item-flex1">
                    <span class="label">附件：</span>
                    <div v-for="item in basicInfoList.appendixIdList" :key="item.id" class="hrAppendixListBox">
                        <Tooltip placement="top">
                            <template #title>
                                <span>{{ item?.originName }}</span>
                            </template>
                            <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                        </Tooltip>
                    </div>
                </div>
                <div class="item-flex-remarks">
                    <span class="label">备注：</span>
                    <span>{{ basicInfoList.clientRemark }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import downFile from '/@/utils/downFile'
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { previewFile } from '/@/utils'
export default defineComponent({
    name: 'ApplicationInfo',
    components: { PaperClipOutlined },
    props: {
        viewType: {
            type: String,
            default: '',
        },
        basicInfoList: {
            type: Object,
        },
    },
    setup() {
        // const basicInfoList = ref<inObject>({})
        const downloadFil = (item) => {
            // downFile('get', item.fileUrl, item.originName, {})
            previewFile(item.fileUrl)
        }

        return {
            // basicInfoList,
            downloadFil,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                color: rgba(153, 153, 153, 1);
            }
        }
        .item-flex1 {
            width: 33%;
            margin: 5px 0px;
            .label {
                color: rgba(153, 153, 153, 1);
            }
        }
        .item-flex2 {
            width: 50%;
            margin: 5px 0px;
            .label {
                color: rgba(153, 153, 153, 1);
            }
        }
        .item-flex-remarks {
            width: 100%;
            margin: 5px 0px;
            .label {
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    .hrAppendixListBox {
        display: inline-block;
        padding-right: 10px;
        .enclosure {
            line-height: 26px;
            color: @primary-color;
            display: inline-block;
            cursor: pointer;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: middle;

            &:hover {
                background: #ddd;
            }
        }
    }
}
</style>
