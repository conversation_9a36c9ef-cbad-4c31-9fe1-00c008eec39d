<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'leaveStaff_export'" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'leaveStaff_warn'" @click="leaveWarn()">批量通知</Button>
        <Button class="btn" v-auth="'leaveStaff_dimission'" type="primary" @click="sponsorLeave()">批量发起离职</Button>
        <Button class="btn" v-auth="'leaveStaff_pass'" type="primary" @click="passRow">批量通过</Button>
        <Button danger v-auth="'leaveStaff_refuse'" type="primary" @click="rejectRow">批量拒绝</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-apply-departure-staffs/page"
        deleteApi="/api/hr-apply-departure-staffs/deletes"
        :exportUrl="exportUrl"
        :params="{
            ...params,
            izOnlineContract: params.izOnlineContract === 0 ? false : params.izOnlineContract === 1 ? true : undefined,
        }"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #staffStatus="{ record }">
            <!-- {{ record }} -->
            {{ record.departureStaffStatusLabel }}
            <!-- <span>{{ record.departureStaffStatusLabel }}</span> -->
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <VModal
        v-model:visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm(false)"
        @auditRequest="auditRequest"
    />
    <CreateModal
        v-model:visible="showModal"
        :title="modalTitle"
        :currentValue="currentValue"
        viewType="launch"
        @cancel="modalCancel"
        @confirm="modalConfirm(true)"
    />
    <!-- 离职状态 -->
    <!-- <LeaveStatus :visible="showInduction" title="离职进度查看" :item="inductionValue" @cancel="modalCancelInduction" /> -->
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import vModal from './vModal.vue'
import CreateModal from '../leaveServe/CreateModal.vue'
import inductionApplyStore from '/@/store/modules/inductionApply'

import request from '/@/utils/request'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils'
import { useRoute } from 'vue-router'

export default defineComponent({
    name: 'LeaveStaffIndex',
    components: { VModal: vModal, CreateModal },
    setup() {
        let applicationList = ref<LabelValueOptions>([]) //状态
        let staffTypeList = ref<LabelValueOptions>([]) //人员类型
        let specializedOptions = ref<LabelValueOptions>([])
        onMounted(() => {
            //人员类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/waitLeaveStates', {}).then((res) => {
                applicationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
        })
        //筛选
        const route = useRoute()
        const params = ref<any>({
            departureStaffStatusList: route.query?.departureStaffStatusList
                ? JSON.parse(route.query?.departureStaffStatusList as string)
                : undefined,
        })
        const searchOptions: SearchBarOption[] = [
            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIds',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },

            {
                type: 'daterange',
                label: '实际工作开始日期',
                key: 'workDurationStartDateQuery',
            },
            {
                type: 'daterange',
                label: '实际工作结束日期',
                key: 'workDurationEndDateQuery',
            },
            {
                type: 'string',
                label: '经济补偿金',
                key: 'compensation',
            },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
            {
                type: 'string',
                label: '证明人',
                key: 'certifier',
            },
            {
                type: 'string',
                label: '证明电话',
                key: 'certifierPhone',
            },
            {
                type: 'daterange',
                label: '离职日期',
                key: 'departureDateQuery',
            },
            {
                type: 'month',
                label: '停止缴费年月',
                key: 'stopPaymentDate',
            },
            {
                type: 'select',

                label: '状态',
                key: 'departureStaffStatusList',
                options: applicationList,
                multiple: true,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'userIdList',
                options: specializedOptions,
                multiple: true,
            },
            {
                type: 'select',
                label: '是否签订电子签',
                key: 'izOnlineContract',
                options: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 },
                ],
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },

            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',

                width: 150,
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
            },
            {
                title: '实际工作日期',
                dataIndex: 'workDurationStartDate',
                width: 200,
                align: 'center',
                customRender: ({ record }) => {
                    if (record?.workDurationStartDate) {
                        return record?.workDurationStartDate + ' 至 ' + record?.workDurationEndDate
                    }
                },
            },

            {
                title: '经济补偿金',
                dataIndex: 'compensation',
                width: 150,
                align: 'center',
            },
            {
                title: '申请日期',
                dataIndex: 'createdDate',
                type: 'date',
                width: 150,
                align: 'center',
            },
            {
                title: '证明人',
                dataIndex: 'certifier',
                width: 150,
                align: 'center',
            },
            {
                title: '证明人电话',
                dataIndex: 'certifierPhone',
                width: 150,
                align: 'center',
            },

            {
                title: '离职日期',
                dataIndex: 'departureDate',
                width: 150,
                align: 'center',
            },
            {
                title: '停止缴费年月',
                dataIndex: 'stopPaymentDate',
                type: 'date',
                width: 150,
                align: 'center',
                sorter: false,
            },
            {
                title: '状态',
                dataIndex: 'departureStaffStatus',
                align: 'center',
                width: 200,
                slots: { customRender: 'staffStatus' },
                customRender: ({ record }) => {
                    return record.departureStaffStatusLabel
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ]
        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('查看')
        const viewType = ref('')
        // 当前编辑的数据
        const currentValue = ref(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            modalTitle.value =
                viewTypeName == 'examine'
                    ? '审核'
                    : viewTypeName == 'electrical'
                    ? '审核电签信息'
                    : viewTypeName == 'offline'
                    ? '审核线下签信息'
                    : '查看'

            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }

        // 批量发起离职
        const showModal = ref(false)
        const sponsorLeave = (record?) => {
            let clientNameErr = false
            let [departureApplicantErr, hasStagedArr, newSelectedRowsArr]: [any, any, any] = [[], [], []]
            if (!record) {
                if (selectedRowsArr.value.length <= 0) {
                    message.warning('请选择一条数据')
                    return false
                }
                const name: any = selectedRowsArr.value[0]
                selectedRowsArr.value?.forEach((item) => {
                    if (item?.clientName != name?.clientName) {
                        clientNameErr = true
                    }
                    if (item?.isDefault == 2 && item.isLaunch == 2) {
                        hasStagedArr.push(item.name)
                    }
                    if (item?.departureStaffStatus != 2) {
                        departureApplicantErr.push(item.name)
                    } else {
                        newSelectedRowsArr.push(item)
                    }
                })
            } else {
                newSelectedRowsArr = [record]
            }

            if (!clientNameErr) {
                if (hasStagedArr.length > 1) {
                    if (!hasStagedArr.every((el) => el == hasStagedArr[0])) {
                        openNotification(`您选择的员工中 ${departureApplicantErr.join()} 已经暂存离职服务。`)
                        return
                    }
                }
                if (departureApplicantErr.length) {
                    if (newSelectedRowsArr.length) {
                        openNotification(`您选择的员工中 ${departureApplicantErr.join()} 已经发起了离职，已为您剔除。`)
                    } else {
                        openNotification(`您选择的员工中 ${departureApplicantErr.join()} 已经发起了离职。`)
                        return
                    }
                }
                inductionApplyStore().setEmployedStaffList(newSelectedRowsArr)
                showModal.value = true
                modalTitle.value = '发起离职'
                currentValue.value = record
            } else {
                message.warning('只能选择相同客户发起离职')
                return false
            }
        }

        const inductionValue = ref(null)

        const clickStaffStatus = (record) => {
            showEdit.value = true
            inductionValue.value = { ...record, id: record.id, employedId: record.id }
        }

        const modalCancelInduction = () => {
            showEdit.value = false
            inductionValue.value = null
        }

        const modalCancel = () => {
            currentValue.value = null
        }
        const modalConfirm = (toFirst) => {
            tableRef.value.refresh(toFirst ? 1 : undefined)
        }
        // 导出接口
        const exportUrl = '/api/hr-apply-departure-staffs/export'
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出按钮
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const leaveWarn = (record?) => {
            let applyStaffIdList: any[] = []
            if (!record) {
                if (selectedRowsArr.value.length <= 0) {
                    message.warning('请选择一条数据')
                    return false
                }
                applyStaffIdList = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
            } else {
                applyStaffIdList = [record.id]
            }

            request.post('/api/hr-apply-departure-staffs/notice', { applyStaffIdList: applyStaffIdList }).then((res) => {
                let tip = ''
                let success = '您选择的数据已发送成功'
                if (res.error_status) {
                    tip = res.error_status
                    success = ',选择的其它数据已发送成功'
                }
                if (res.success?.length) {
                    tip += success
                }
                openNotification(tip)
                tableRef.value.refresh()
            })
        }
        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'leaveStaff_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '离职通知',
                    auth: 'leaveStaff_warn',
                    show: (record) => {
                        return record.departureStaffStatus == '4'
                    },
                    click: leaveWarn,
                },
                {
                    neme: '审核',
                    auth: 'leaveStaff_audit',
                    show: (record) => {
                        return record.departureStaffStatus == '1'
                    },
                    click: (record) => editRow('examine', record),
                },
                {
                    neme: '发起离职',
                    auth: 'leaveStaff_dimission',
                    show: (record) => {
                        return record.departureStaffStatus == '2'
                    },
                    click: (record) => sponsorLeave(record),
                },
                {
                    neme: '确认离职',
                    auth: 'leaveStaff_electrical',
                    show: (record) => {
                        return record.departureStaffStatus == '4' || record.departureStaffStatus == '5'
                    },
                    click: (record) => {
                        if (record?.izOnlineContract) {
                            editRow('electrical', record)
                        } else {
                            editRow('offline', record)
                        }
                        // editRow('electrical', record)
                    },
                },
                // {
                //     neme: '审核线下签信息',
                //     auth: 'leaveStaff_offline',
                //     show: (record) => {
                //         return record.departureStaffStatus == '5'
                //     },
                //     click: (record) => editRow('offline', record),
                // },
            ]),
        )

        // 多选
        const selectedRowsArr = ref<inObject[]>([])
        const showRejec = ref(false)
        // 批量拒绝
        const checkerReason = ref('')
        const remark = ref<string>('')
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            checkerReason.value = ''
            showRejec.value = true
        }
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            showRejec.value = false
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            auditRequest(false, applyIdList, checkerReason.value, remark.value)
        }
        // 批量通过
        const passRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                        return el.id
                    })
                    auditRequest(true, applyIdList, '', '')
                },
                onCancel() {},
            })
        }
        const auditRequest = (opt, applyIdList, checkerReason, remark) => {
            request
                .post('/api/hr-apply-departure-staffs/audit-data', {
                    applyStaffIdList: applyIdList,
                    checkerReason: checkerReason,
                    remark: remark,
                    opt,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    tableRef.value.refresh()
                })
        }

        return {
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量拒绝显示
            showRejec,
            // 点击批量拒绝
            rejectRow,
            //批量拒绝描述
            checkerReason,
            //批量拒绝确认
            rejectConfirm,
            // 批量通过
            passRow,
            //新增
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            // 导出
            exportUrl,
            // 导出按钮
            exportData,
            //批量发起离职
            sponsorLeave,
            showModal,

            clickStaffStatus,
            modalCancelInduction,
            // showInduction,
            inductionValue,
            //审核请求
            auditRequest,
            leaveWarn,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
