<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="800px"
        :footer="null"
        @selectedRow="templateSelect"
    >
        <div>
            <Form
                ref="formInline"
                :model="formData"
                :rules="rules"
                class="form-flex"
                style="margin-top: 20px; margin-right: 20px; padding-left: 40px; padding-right: 40px"
            >
                <!-- <div class="btns">
                    <Button type="primary" @click="selectedStaff">选择员工</Button>
                </div> -->
                <template v-for="(item, index) in myOptions" :key="index">
                    <MyFormItem
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        :class="item.slots"
                        v-if="item.show != false"
                    >
                        <template #materialsLoan>
                            <CheckboxGroup
                                v-model:value="formData.typeIds"
                                :options="checkList"
                                @change="(e) => selectedTypeIds(e)"
                                placeholder="请选择申请借阅材料"
                            />
                        </template>
                        <template #staffInfo>
                            <Input :disabled="true" placeholder="请选择员工" v-model:value="formData.name" />
                        </template>
                    </MyFormItem>
                </template>
                <div class="tips">
                    温馨提示：<br />
                    申请成功后，电子材料可自行下载打印；纸质材料请于3个工作日内，由本人携带身份证前往我公司办公地点提取。
                    咨询电话：0532-80981910，办公地址：青岛市黄岛区漓江西路877号海投大厦1楼东侧
                </div>
            </Form>
            <div class="ant-modal-footer" style="margin-top: 20px">
                <Button key="back" @click="cancel">取消</Button>
                <Button key="submit" type="primary" @click="confirm">确定</Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message, CheckboxGroup } from 'ant-design-vue'
import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'AddStaff',
    components: { CheckboxGroup },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'selectedRow'],

    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const { visible, item } = toRefs<any>(props)
        watch(visible, () => {
            if (visible) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                getTalentStaffsDetails()
            }
        })
        const sexList = ref<object[]>([]) // 性别
        const station = ref<object[]>([]) //岗位
        const staffTypeList = ref<object[]>([]) //人员类型
        const staffsInfo = ref<any>()
        const showTemplateProot = ref<any>(false)
        const showSelectedStaff = ref<any>(false)

        const selectedTemplate = () => {
            showTemplateProot.value = true
        }
        const checkList = ref<any>([])
        const selectIds: any = ref([])
        const templateSelect = (res) => {
            console.log(res)
        }
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '员工信息',
                name: 'staffId',
                type: 'slots',
                slots: 'staffInfo',
            },
            {
                label: '档案类型',
                name: 'type',
                type: 'change',
                options: [
                    {
                        label: '在职借阅',
                        value: 0,
                    },
                    {
                        label: '离职提档',
                        value: 1,
                    },
                ],
                ruleType: 'number',
                default: 1,
                disabled: true,
            },
            {
                label: '离职提档材料',
                name: 'typeIds',
                type: 'slots',
                slots: 'materialsLoan',
                ruleType: 'array',
            },
            {
                label: '申请理由明细',
                name: 'detail',
                type: 'textarea',
                required: false,
            },
            {
                label: '预计线下提档时间',
                name: 'endDate',
                type: 'date',
                required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            checkList.value = []
            formInline.value.resetFields()
        }
        const selectedStaff = () => {
            showSelectedStaff.value = true
        }
        const selectedCancel = () => {
            showSelectedStaff.value = false
        }

        const checkSelected = ref<any>()
        const checkStatus = ref<Number>()
        const checkListStr = ref<String>('')
        const selectedTypeIds = (e) => {
            if (e[0] == '线下提档') {
                checkSelected.value = e
                checkStatus.value = 1
            } else {
                checkStatus.value = 2
                let arr: any = []
                let str = ''
                arr = checkList.value.filter((el) => e.includes(el.value))
                arr.forEach((list) => {
                    str += list.label.concat(';')
                })
                checkListStr.value = str
            }
        }
        let templateTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('certificateTemplateType', '')
                .then((data: inObject[]) => {
                    templateTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const columns = [
            {
                title: '模板标题',
                dataIndex: 'title',
                align: 'center',
                width: 250,
            },
            {
                title: '模板类型',
                dataIndex: 'templateType',
                align: 'center',
                width: 120,
                customRender: ({ record }) => {
                    return templateTypeList.value.find((el) => {
                        return el.value == record.templateType
                    })?.label
                },
            },
            {
                title: '更新时间',
                dataIndex: 'lastModifiedDate',
                align: 'center',
                width: 150,
            },
            {
                title: '应用次数',
                dataIndex: 'useNum',
                align: 'center',
                width: 120,
            },
        ]
        const templateCancel = () => {
            showTemplateProot.value = false
        }
        const staffChoiceInfo = ref<any>({})
        const templateList = ref<any>()
        const templateConfirm = (data) => {
            templateList.value = data
            formData.value.proofTemplateId = data.templateTypeLabel
            showTemplateProot.value = false
        }
        const staffsIdList = ref<any>()
        const selectedConfirm = (data) => {
            formData.value.staffId = data.name
            staffChoiceInfo.value = data
            staffsIdList.value = data.id
        }
        const getTalentStaffsDetails = async () => {
            const res = await request.get(`/api/hr-talent-staffs/getDetail?staffId=${formData.value.staffId}`)
            console.log(res)
            if (!res.length) {
                res.push({
                    name: '线下提档',
                })
                checkList.value = res.map((item) => {
                    return {
                        label: item.name,
                        value: item.name,
                    }
                })
            } else {
                checkList.value = res.map((item) => {
                    let isDisabled = false
                    if (item.state != 1) {
                        isDisabled = true
                    }
                    return {
                        label: item.name,
                        value: item.id,
                        disabled: isDisabled,
                    }
                })
                checkList.value = [...checkList.value, { label: '线下提档', value: '' }]
            }
        }
        watch(
            visible,
            () => {
                if (visible) {
                }
            },
            { immediate: true },
        )

        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const confirm = () => {
            let formNewData = {
                staffId: formData.value.staffId,
                typeIds: checkStatus.value == 2 ? formData.value.typeIds : [],
                title: formData.value.title,
                detail: formData.value.detail,
                endDate: formData.value.endDate,
                typeListStr: checkStatus.value == 1 ? checkSelected.value[0] : checkListStr.value,
                type: formData.value.type,
            }

            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')

                    await request.post('/api/hr-lending-applies', formNewData)
                    message.success('新增成功!')

                    emit('confirm')
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        return {
            rules,
            formData,
            myOptions,
            formInline,
            sexList,
            station,
            staffTypeList,
            selectedTemplate,
            showTemplateProot,
            confirm,
            cancel,
            columns,
            templateCancel,
            templateConfirm,
            selectIds,
            templateSelect,
            selectedStaff,
            showSelectedStaff,
            selectedCancel,
            staffsInfo,
            selectedConfirm,
            checkList,
            selectedTypeIds,
        }
    },
})
</script>
<style scoped lang="less">
.tips {
    font-size: 26rpx;
    color: #888686;
    margin: 38rpx 0 18rpx 0;
    line-height: 40rpx;
}

// .form-flex {
//     :deep(.ant-form-item) {
//         width: 63%;
//     }
//     :deep(.img) {
//         position: absolute;
//         right: 1%;
//     }
// }
</style>
