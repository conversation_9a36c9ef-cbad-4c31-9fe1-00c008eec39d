<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1500px">
        <div class="seeInfoBox">
            <div class="step-detail">
                <Steps :current="applyStep" v-if="detailData.departureApplicant === 1" labelPlacement="vertical" class="my_steps">
                    <Step title="员工发起申请" />
                    <Step title="公司确认离职申请" />
                    <Step title="公司发起离职" />
                    <Step title="入离职专员审批" />
                    <Step title="员工办理离职手续" />
                    <Step title="离职手续办理完成" />
                    <Step title="离职结束" v-if="applyStep === 7" />
                </Steps>
                <Steps :current="applyStep" v-else labelPlacement="vertical" class="my_steps">
                    <Step title="公司发起离职服务" />
                    <Step title="入离职专员审批" />
                    <Step title="员工办理离职手续" />
                    <Step title="离职手续办理完成" />
                    <Step title="离职结束" v-if="applyStep === 7" />
                </Steps>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>申请信息</span>
                <div class="examine-flex">
                    <div class="item-flex">
                        <span>员工姓名：</span>
                        <span>{{ detailData.name }}</span>
                    </div>
                    <div class="item-flex">
                        <span>身份证号：</span>
                        <span>{{ detailData.certificateNum }}</span>
                    </div>

                    <div class="item-flex">
                        <span>性别：</span>
                        <span>{{ detailData.sexLabel }}</span>
                    </div>
                    <div class="item-flex">
                        <span>联系方式：</span>
                        <span>{{ detailData.phone }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>岗位：</span>
                        <span>{{ detailData.professionName }}</span>
                    </div>

                    <div class="item-flex">
                        <span>基本工资：</span>
                        <span>{{ detailData.basicWage }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同开始日期：</span>
                        <span>{{ detailData.contractStartDate }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同结束日期：</span>
                        <span>{{ detailData.contractEndDate }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>离职日期：</span>
                        <span>{{ detailData.departureDate }}</span>
                    </div>
                    <div class="item-flex">
                        <span>停止缴费年月：</span>
                        <span>{{ detailData.stopPaymentDate }}</span>
                    </div>
                    <div class="item-flex">
                        <span>经济补偿金：</span>
                        <span>{{ detailData.compensation }}</span>
                    </div>
                    <div class="item-flex">
                        <span>实际工作日期：</span>
                        <span>{{
                            detailData?.workDurationStartDate
                                ? detailData?.workDurationStartDate + ' 至 ' + detailData?.workDurationEndDate
                                : ''
                        }}</span>
                    </div>
                    <div class="item-flex">
                        <span>是否退缴该员工本月社保：</span>
                        <span>{{
                            detailData?.izRefundSocialSecurity == 1 ? '是' : detailData?.izRefundSocialSecurity == 2 ? '否' : ''
                        }}</span>
                    </div>
                </div>
                <div class="leave" v-if="detailData?.departureApplicant === 1">
                    <div>离职原因：</div>
                    <div>{{ detailData.departureReason }}</div>
                </div>
                <div class="leave" v-else>
                    <div>退回原因：</div>
                    <div>{{ detailData.returnReason }}</div>
                </div>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>操作信息</span>
                <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                    <div class="list-item">
                        <span style="width: 40px">{{ index + 1 }}</span>
                        <div class="item-flex">
                            <span>操作人：</span>
                            <span>{{ item.realName }}</span>
                        </div>
                        <div class="item-flex2">
                            <span>操作时间：</span>
                            <span>{{ item.createdDate }}</span>
                        </div>
                        <div class="item-flex3">
                            <div class="box1">操作信息：</div>
                            <div class="box2">
                                {{ item.message }}
                                <div v-if="item?.hrAppendixList?.length" style="display: inline-block">
                                    附：
                                    <div
                                        v-for="AppendixItem in item.hrAppendixList"
                                        :key="AppendixItem.id"
                                        class="hrAppendixListBox"
                                    >
                                        <Tooltip placement="top">
                                            <template #title>
                                                <span>{{ AppendixItem?.originName }}</span>
                                            </template>
                                            <a class="enclosure" @click="downloadFil(AppendixItem)"
                                                ><PaperClipOutlined />{{ AppendixItem?.originName }}</a
                                            >
                                        </Tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 当viewType为examine,electrical时显示审核底部 -->
        <div class="examine" v-if="viewType == 'examine' || viewType == 'electrical'">
            <Row :gutter="20">
                <Col span="12">
                    <Divider type="vertical" class="divid" />
                    <span>审批</span>
                    <div class="examine-area">
                        <Textarea :rows="3" v-model:value="checkerReason" allowClear placeholder="若拒绝，请输入拒绝理由" /></div
                ></Col>
                <Col span="12">
                    <Divider type="vertical" class="divid" />
                    <span>备注：</span>
                    <p class="linefeed"></p>
                    <div class="examine-area">
                        <Textarea v-model:value="remark" :rows="3" allowClear placeholder="请输入备注" />
                    </div>
                </Col>
            </Row>

            <div v-if="viewType == 'electrical'" style="margin-top: 10px">
                <span class="span"><span style="color: red">*&nbsp;</span>是否退缴该员工本月社保：</span>
                <RadioGroup name="radioGroup" v-model:value="izRefundSocialSecurity">
                    <Radio :value="1">是</Radio>
                    <Radio :value="2">否</Radio>
                </RadioGroup>
            </div>
        </div>

        <div class="examine" v-if="viewType == 'offline'">
            <Divider type="vertical" class="divid" />
            <span>审批</span>
            <div class="file">
                <span class="span" v-if="viewType == 'offline'"><span style="color: red">*&nbsp;</span>附件：</span>
                <ImportFile v-model:fileUrls="fileUrls" ref="refImportFile" />
            </div>
            <div>
                <span class="span"><span style="color: red">*&nbsp;</span>是否退缴该员工本月社保：</span>
                <RadioGroup name="radioGroup" v-model:value="izRefundSocialSecurity">
                    <Radio :value="1">是</Radio>
                    <Radio :value="2">否</Radio>
                </RadioGroup>
            </div>
            <div class="box">
                <span class="span">备注</span>
                <div class="examine-area">
                    <Textarea v-model:value="checkerReason" placeholder="若拒绝，请输入拒绝理由" />
                </div>
            </div>
        </div>
        <template #footer>
            <div></div>
            <div v-if="viewType == 'examine' || viewType == 'electrical' || viewType == 'offline'" :key="Math.random()">
                <Button danger type="primary" @click="rejectRow" v-if="viewType != 'offline'">拒绝</Button>
                <Button type="primary" @click="pass">通过</Button>
            </div>
        </template>
    </BasicEditModalSlot>
    <AddModal
        :visible="showAddArchives"
        :title="'新建档案'"
        :item="formData"
        @cancel="showAddArchives = false"
        @confirm="showAddArchives = false"
    />
</template>

<script lang="ts">
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { openNotification, previewFile } from '/@/utils'
import request from '/@/utils/request'
import AddModal from './addModal.vue'
export default defineComponent({
    name: 'CreateA',
    components: { PaperClipOutlined, AddModal },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'examine', 'electrical', 'offline'].indexOf(value) !== -1
            },
        },
    },

    emits: ['confirm', 'cancel', 'update:visible', 'auditRequest'],
    setup(props, { emit }) {
        let websiteDepartureSchedule = {
            departureApplicant1: [0, 1, 2, 3, 4, 4, 6, 7, 7, 4],
            departureApplicant2: [0, 0, 0, 1, 2, 2, 6, 7, 7, 2],
        }
        const { item, viewType } = toRefs<any>(props)
        const refImportFile = ref()
        const fileUrls = ref<inObject[]>([])

        const applyStep = ref(0)
        const detailData = ref<inObject>({})
        const opLogsList = ref<object[]>([])
        const checkerReason = ref('')
        const remark = ref<string>('')
        const izRefundSocialSecurity = ref<number>(1)
        const getDetail = (value) => {
            request.get('/api/hr-apply-departure-staffs', { id: value }).then((res) => {
                let list =
                    websiteDepartureSchedule['departureApplicant' + (res.departureApplicant == 1 ? res.departureApplicant : 2)]
                applyStep.value = list[Number(res.websiteDepartureSchedule)]
                if (res.websiteDepartureSchedule == 5) {
                    if (res?.izOnlineContract) {
                        applyStep.value += 1
                    }
                }

                detailData.value = res
                opLogsList.value = res.opLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        watch(
            item,
            () => {
                if (item.value) {
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )

        // cancel handle
        const cancel = () => {
            emit('cancel')
            emit('update:visible', false)
            fileUrls.value = []
            izRefundSocialSecurity.value = 1
            checkerReason.value = ''
            // resetFormData()
        }
        const rejectRow = () => {
            let applyId = detailData.value.id
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            if (viewType.value == 'examine') {
                emit('auditRequest', false, [applyId], checkerReason.value, remark.value)
            } else if (viewType.value == 'offline') {
                auditOfflineSign()
            } else if (viewType.value == 'electrical') {
                auditOnlineSign(false)
            }
            cancel()
            emit('confirm')
        }
        const showAddArchives = ref(false)
        const formData = ref<Recordable>({})
        // confirm handle
        const pass = () => {
            let applyId = detailData.value.id
            if (viewType.value == 'examine') {
                emit('auditRequest', true, [applyId], '', '')
            } else if (viewType.value == 'offline') {
                if (!fileUrls.value.length) {
                    message.error('该员工还未上传辞职申请书，请上传辞职申请书后再进行确认离职。')
                    return
                } else {
                    auditOfflineSign()
                    showAddArchives.value = true
                    formData.value = detailData.value
                }
            } else if (viewType.value == 'electrical') {
                auditOnlineSign(true)
                showAddArchives.value = true
                formData.value = detailData.value
            }
            cancel()
            emit('confirm')
        }
        //线下签
        const auditOfflineSign = () => {
            let appendixIdList = []
            if (viewType.value == 'offline') {
                appendixIdList = refImportFile.value?.getFileUrls().map((item) => {
                    return item.id
                })
            }
            request
                .post('/api/hr-apply-departure-staffs/audit-offline-sign', {
                    applyStaffIdList: [detailData.value.id],
                    izRefundSocialSecurity: izRefundSocialSecurity.value,
                    appendixIdList: appendixIdList,
                    applyRemark: checkerReason.value,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    emit('confirm')
                })
        }
        //线上签
        const auditOnlineSign = (opt) => {
            let appendixIdList = []
            if (viewType.value == 'offline') {
                appendixIdList = refImportFile.value?.getFileUrls().map((item) => {
                    return item.id
                })
            }

            request
                .post('/api/hr-apply-departure-staffs/audit-online-sign', {
                    applyStaffIdList: [detailData.value.id],
                    izRefundSocialSecurity: izRefundSocialSecurity.value,
                    appendixIdList: appendixIdList,
                    checkerReason: checkerReason.value,
                    remark: remark.value,
                    opt: opt,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    emit('confirm')
                })
        }
        const downloadFil = (item) => {
            previewFile(item.fileUrl)
        }
        return {
            refImportFile,
            opLogsList,
            confirm,
            cancel,
            detailData,
            rejectRow,
            checkerReason,
            applyStep,
            izRefundSocialSecurity,
            fileUrls,
            pass,
            //下载
            downloadFil,
            remark,
            showAddArchives,
            formData,
        }
    },
})
</script>
<style scoped lang="less">
//
.step-detail {
    margin-bottom: 30px;
}
.examine {
    margin: 50px 0px 0px;
    .span {
        padding-left: 10px;
    }
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 150px;
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;

        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.leave {
    padding-left: 15px;
    margin: 5px 0px;
    display: flex;
}
.file {
    // padding-left: 15px;
    padding: 20px 0;
    display: flex;
    align-items: center;
}
.hrAppendixListBox {
    display: inline-block;
    padding-right: 10px;
    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
.box {
    margin: 0px 0px 0px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    span {
        width: 40px;
    }
    .examine-area {
        width: 97%;
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
