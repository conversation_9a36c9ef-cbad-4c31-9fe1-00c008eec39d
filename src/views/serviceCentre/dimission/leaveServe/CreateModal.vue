<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1500px">
        <div class="seeInfoBox" v-if="viewType == 'see' || viewType == 'examine'">
            <div style="display: flex">
                <div class="client">
                    <p class="label">客户编号：</p>
                    <p>{{ formData.unitNumber }}</p>
                </div>
                <div class="client" style="width: 80%; display: flex">
                    <p class="label">客户名称：</p>
                    <p class="clientName" :title="formData.clientName">{{ formData.clientName }}</p>
                </div>
            </div>
            <br />
            <div>
                <p class="label">附件：</p>
                <div v-for="item in hrAppendixList" :key="item.id" class="hrAppendixListBox">
                    <Tooltip placement="top">
                        <template #title>
                            <span>{{ item?.originName }}</span>
                        </template>
                        <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                    </Tooltip>
                </div>
            </div>
            <br />
            <div>
                <p class="label">备注：</p>
                <p>{{ formData.applyRemark }}</p>
            </div>
        </div>

        <template v-if="viewType == 'add' || viewType == 'launch' || viewType == 'edit'">
            <Form
                ref="formInline"
                :model="formData"
                :rules="rules"
                :label-col="{ style: { width: '120px' } }"
                class="form-flex"
                style="max-height: 60vh"
            >
                <template v-for="(itemForm, i) in myOptions" :key="i">
                    <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]">
                        <template #Cascader>
                            <ClientSelectTree
                                :disabled="viewType == 'launch'"
                                v-model:value="formData[itemForm.name]"
                                :itemForm="itemForm"
                                @change="ClientSelectTreeChange()"
                            />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="btns" style="margin: 0" v-if="viewType == 'add' || viewType == 'edit'" :key="Math.random()">
                <p class="label"><span style="color: red">*&nbsp;</span>待离职员工</p>
                <Button type="primary" @click="addRow()">添加</Button>
                <Button type="primary" @click="ImportData">导入</Button>
            </div>
        </template>
        <EmployedStaff
            :visible="visible"
            @operationEdit="editRow"
            @operationDelete="deleteRow"
            :viewType="viewType"
            :currentValue="currentValue"
            @getDataInfo="getDataInfo"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
            @auditRequest="auditRequest"
        />

        <Form
            ref="formInlineAdd"
            :model="formData"
            :label-col="{ style: { width: '120px' } }"
            v-if="viewType == 'add' || viewType == 'launch' || viewType == 'examine' || viewType == 'edit'"
        >
            <FormItem
                label="附件"
                name="newAppendixIdList"
                :rules="{
                    required: viewType == 'add' || viewType == 'launch' || viewType == 'edit',
                    type: 'array',
                    message: '请添加附件',
                    trigger: ['change', 'blur'],
                }"
            >
                <ImportFile v-model:fileUrls="formData.newAppendixIdList" ref="refImportFile" listType="fileList" />
            </FormItem>
            <FormItem label="备注" name="userName">
                <Textarea v-model:value="formData.newApplyRemark" :rows="3" allowClear placeholder="请输入备注" />
            </FormItem>
        </Form>
        <div v-if="viewType == 'see'">
            <p class="examineTitle label"><span></span>审核信息&nbsp;&nbsp;&nbsp;</p>
            <div class="seeInfoBox" style="margin-top: 10px" v-for="opLogsItem in opLogsList" :key="opLogsItem.id">
                <div>
                    <p class="label">审核人：</p>
                    <p>{{ opLogsItem.realName }}</p>
                </div>
                <div>
                    <p class="label">审核时间：</p>
                    <p>{{ opLogsItem.createdDate }}</p>
                </div>
                <br />
                <div>
                    <p class="label">附件：</p>
                    <div v-for="item in opLogsItem.hrAppendixList" :key="item.id" class="hrAppendixListBox">
                        <Tooltip placement="top">
                            <template #title>
                                <span>{{ item?.originName }}</span>
                            </template>
                            <a class="enclosure" @click="downloadFil(item)"><PaperClipOutlined />{{ item?.originName }}</a>
                        </Tooltip>
                    </div>
                </div>
                <br />
                <div>
                    <p class="label">备注：</p>
                    <p>{{ opLogsItem.message }}</p>
                </div>
            </div>
        </div>
        <template #footer>
            <template v-if="viewType == 'examine'">
                <Button type="primary" class="btn" key="back" @click="refuseOrAdopt(1)">批量通过</Button>
                <Button danger type="primary" key="back" @click="rejectRow()">批量拒绝</Button>
            </template>
            <template v-if="viewType == 'add' || viewType == 'launch' || viewType == 'edit'">
                <Button key="back" @click="cancel">取消</Button>
                <Button type="primary" @click="confirm(2)">暂存</Button>
                <Button type="primary" @click="confirm(1)">提交</Button>
            </template>
            <template v-if="viewType == 'see'">
                <Button key="submit" type="primary" @click="exportData">导出失败人员信息</Button>
            </template>
        </template>
    </BasicEditModalSlot>
    <AddInduction
        v-model:visible="showAdd"
        :title="modalTitle"
        :itemInfo="itemInfo"
        :inductionApplyData="formData"
        :editDataIndex="editDataIndex"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :viewType="viewType"
    />
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="refuseOrAdopt(2)"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <ImportModal
        v-model:visible="importVisible"
        @getResData="getResData"
        temUrl="/api/hr-apply-departures/template"
        importUrl="/api/hr-apply-departures/import"
        :importUrlParam="importUrlParam"
    />
</template>

<script lang="ts">
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules, openNotification, previewFile } from '/@/utils/index'
import EmployedStaff from './employedStaff.vue'
import AddInduction from './addInduction.vue'
import inductionApplyStore from '/@/store/modules/inductionApply'
import downFile from '/@/utils/downFile'
export default defineComponent({
    name: 'CreateModal',
    components: { EmployedStaff, AddInduction, PaperClipOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'add', 'edit', 'launch', 'examine'].indexOf(value) !== -1
            },
        },
    },
    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { currentValue, visible, viewType } = toRefs<any>(props)

        const hrAppendixList = ref<inObject[]>([])
        const opLogsList = ref<inObject[]>([])
        onMounted(() => {})

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '所属客户',
                name: 'clientId',
                slots: 'Cascader',
                type: 'slots',
            },

            {
                label: '退回日期',
                name: 'departureDate',
                type: 'date',
            },
            {
                label: '停止缴费年月',
                name: 'stopPaymentDate',
                type: 'month',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        const importUrlParam = ref<any>({})
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                getDataInfo(currentValue.value?.id)
            }
        })
        watch(
            () => inductionApplyStore().getEmployedStaffList,
            (newV) => {
                const arr = newV.filter((el) => !el.isDelete)
                if (arr.length) {
                    console.log(arr[0])
                    if (!formData.value.departureDate) formData.value.departureDate = arr[0].departureDate
                    if (!formData.value.stopPaymentDate) formData.value.stopPaymentDate = arr[0].stopPaymentDate
                }
            },
            { deep: true },
        )
        const ClientSelectTreeChange = () => {
            importUrlParam.value.clientId = formData.value.clientId
            inductionApplyStore().setEmployedStaffList([])
        }

        const getDataInfo = (infoId) => {
            if (infoId) {
                if (viewType.value == 'launch' && currentValue.value?.isDefault != 2 && currentValue.value?.isLaunch != 2) {
                    formData.value.clientId = inductionApplyStore().getEmployedStaffList[0]?.clientId || null
                    return
                }
                request
                    .get('/api/hr-apply-departures', {
                        id: viewType.value == 'launch' ? currentValue.value?.applyDepartureId : infoId,
                    })
                    .then((res) => {
                        if (viewType.value != 'edit') {
                            formData.value = Object.assign({}, initFormData, res, { newAppendixIdList: [], newApplyRemark: '' })
                            hrAppendixList.value = res?.hrAppendixDTOS
                            opLogsList.value = res?.hrApplyOpLogsDTOS || []

                            formData.value.newAppendixIdList = opLogsList.value?.[0]?.hrAppendixList || []
                            formData.value.newApplyRemark = opLogsList.value?.[0]?.message || ''
                        } else {
                            formData.value = Object.assign({}, initFormData, res)
                            formData.value.newAppendixIdList = res?.hrAppendixDTOS || []
                            formData.value.newApplyRemark = res?.applyRemark || ''
                        }

                        inductionApplyStore().setEmployedStaffList(res?.hrApplyDepartureStaffDTOS || [])
                    })
            } else {
                formData.value = Object.assign({}, initFormData, currentValue.value, {
                    newAppendixIdList: [],
                    newApplyRemark: '',
                })
            }
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value?.resetFields()
        }
        const refImportFile = ref()
        // cancel handle
        const cancel = () => {
            inductionApplyStore().setEmployedStaffList([])
            emit('cancel')
            emit('update:visible', false)
            resetFormData()
        }
        // confirm handle
        const formInlineAdd = ref()
        const confirm = (flag) => {
            let appendixIdList = refImportFile.value.getFileUrls().map((item) => {
                return item.id
            })
            let hrApplyDepartureStaffDTOS = inductionApplyStore().getEmployedStaffList
            const saveHandle = async () => {
                await request.post('/api/hr-apply-departures/create', {
                    ...formData.value,
                    appendixIdList,
                    hrApplyDepartureStaffDTOS,
                    applyRemark: formData.value.newApplyRemark,
                    isDefault: flag,
                    isLaunch: viewType.value == 'launch' ? 2 : 1,
                })
                message.success(`${flag == 1 ? '提交' : '暂存'}成功!`)
                cancel()
                // 表单关闭后的其它操作 如刷新表
                emit('confirm', formData.value)
            }
            if (flag == 2) {
                if (!formData.value?.clientId) {
                    message.warning('请选择所属客户')
                    return
                }
                saveHandle()
            } else {
                if (hrApplyDepartureStaffDTOS?.length === 0) {
                    return message.warning('请添加最少一条离职员工')
                } else {
                    let tip = ''
                    hrApplyDepartureStaffDTOS.forEach((item) => {
                        //拿停止缴费年月做必填判断
                        if (!item?.stopPaymentDate) {
                            tip += item.name + ','
                        }
                        return item?.stopPaymentDate
                    })
                    if (tip) {
                        openNotification('员工：' + tip + '信息不完整，请编辑后提交！')
                        return
                    }
                }
                if (appendixIdList.length === 0) {
                    formInlineAdd.value.validate()
                    return message.warning('请上传附件信息！')
                }
                formInline.value
                    .validate()
                    .then(async () => {
                        saveHandle()
                    })
                    .catch((error) => {
                        console.log('表单验证失败', error)
                    })
            }
        }

        //新增编辑
        const showAdd = ref(false)
        const modalTitle = ref('新增')
        // 当前编辑的数据
        const itemInfo = ref<any>({})
        let editDataIndex = ref<any>(null)
        const importVisible = ref(false)
        const ImportData = () => {
            if (formData.value.clientId === null) {
                return message.warn('请先选择所属客户')
            }
            importVisible.value = true
        }

        //导入员工
        const getResData = (data: inObject) => {
            let employedStaffList = [...inductionApplyStore().getEmployedStaffList, ...(data?.departureStaffSuccessList || [])]
            inductionApplyStore().setEmployedStaffList(employedStaffList)
        }
        //添加员工
        const addRow = () => {
            if (formData.value.clientId === null) {
                return message.warn('请先选择所属客户')
            }
            editDataIndex.value = null
            itemInfo.value = null
            showAdd.value = true
            modalTitle.value = '新增员工'
        }
        //修改员工
        const editRow = (data) => {
            editDataIndex.value = data.index
            itemInfo.value = data.record
            showAdd.value = true
            modalTitle.value = '编辑员工'
        }
        // 删除员工

        const deleteRow = (data) => {
            inductionApplyStore().setEmployedStaffListDele(data.index, viewType.value)
        }

        const modalCancel = () => {}
        const modalConfirm = (data) => {
            if (typeof editDataIndex.value == 'number') {
                inductionApplyStore().setEmployedStaffListReplace(data, editDataIndex.value)
            } else {
                inductionApplyStore().setEmployedStaffListPush(data)
            }
        }
        const downloadFil = (item) => {
            previewFile(item.fileUrl)
        }

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)

        //提示
        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            checkerReason.value = ''
            showRejec.value = true
        }
        const checkerReason = ref('')

        const refuseOrAdopt = (type) => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择审核人员')
                return false
            }
            if (type == 2) {
                if (!checkerReason.value) {
                    message.warning('请填写拒绝理由')
                    return false
                }
                showRejec.value = false
            }

            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            auditRequest(type, applyIdList, checkerReason.value)
            cancel()
            emit('confirm')
        }
        //审核请求
        const auditRequest = (type, applyIdList, staffRemark) => {
            let appendixIdList = refImportFile.value.getFileUrls().map((item: inObject) => {
                return item.id
            })
            let api = '/api/hr-apply-departure-staffs/manager-review'

            request
                .post(api, {
                    opt: type != 2,
                    applyId: currentValue.value?.id || '',
                    appendixIdList,
                    applyRemark: formData.value.newApplyRemark,
                    checkerReason: staffRemark,
                    applyStaffIdList: applyIdList,
                })
                .then((res) => {
                    getDataInfo(currentValue.value?.id || '')
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    emit('confirm')
                })
        }
        //导出失败人员信息
        const exportData = () => {
            downFile('post', `/api/hr-apply-departures/export?departureId=${formData.value.id}`, '')
        }
        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            formInlineAdd,

            showAdd,
            modalTitle,
            itemInfo,
            editDataIndex,

            getResData,
            addRow,
            editRow,
            deleteRow,

            modalConfirm,
            modalCancel,

            refImportFile,

            hrAppendixList,
            opLogsList,

            downloadFil,

            selectedRowsArr,
            showRejec,
            // 批量拒绝
            // rejectRow,
            //拒绝原因
            checkerReason,
            refuseOrAdopt,

            rejectRow,

            importVisible,
            getDataInfo,
            //导出
            exportData,
            //导入额外参数
            importUrlParam,
            //导入
            ImportData,
            //客户修改
            ClientSelectTreeChange,
            //审核请求
            auditRequest,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        .ant-form-item-control {
            width: calc(100% - 120px) !important;
        }
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
}
.label {
    width: 120px;
    display: inline-block;
    text-align: right;
    padding-right: 8px;
}
.examineTitle {
    margin-top: 10px;
    color: rgba(51, 51, 51, 100);
    font-size: 14px;
    font-weight: 600;
    vertical-align: middle;
    span {
        margin-right: 10px;
        vertical-align: middle;
        display: inline-block;
        width: 6px;
        height: 24px;
        line-height: 20px;
        background-color: rgba(104, 148, 254, 100);
    }
}
.seeInfoBox {
    width: 100%;
    & > div {
        line-height: 38px;
        display: inline-block;
        &.client {
            width: 20%;
        }
        .clientName {
            max-width: 1000px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        p {
            display: inline-block;
            color: #333;
        }
        p:first-child {
            color: #999999;
        }
        .hrAppendixListBox {
            display: inline-block;
            padding-right: 10px;
            .enclosure {
                line-height: 26px;
                color: @primary-color;
                display: inline-block;
                cursor: pointer;
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: middle;

                &:hover {
                    background: #ddd;
                }
            }
        }
    }
}
.btn {
    background: @upload-color;
    border: none;
}
</style>
