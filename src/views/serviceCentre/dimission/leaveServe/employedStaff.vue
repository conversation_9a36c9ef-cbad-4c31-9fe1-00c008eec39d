<template>
    <Table
        :columns="columns"
        :data-source="columnsData"
        :pagination="false"
        bordered
        :row-selection="viewType == 'examine' ? rowSelection : null"
        :row-key="(record) => record.id + 'employedStaff'"
        class="smallTable"
        :scroll="{ x: '100%', y: 200 }"
    >
        <template #reasonRejection="{ record }">
            <span v-if="viewType == 'see' || record?.departureApplyStatus">{{ record.rejectionReason }}</span>
            <Input v-model:value="record.rejectionReason" v-if="viewType == 'examine' && !record?.departureApplyStatus" />
        </template>
        <template
            #operation="operationData"
            v-if="viewType == 'add' || viewType == 'launch' || viewType == 'examine' || viewType == 'edit'"
        >
            <Button
                size="small"
                type="primary"
                class="btn"
                @click="operationEdit(operationData)"
                v-if="viewType == 'add' || viewType == 'launch' || viewType == 'edit'"
            >
                编辑
            </Button>
            &nbsp;
            <Button
                size="small"
                type="primary"
                danger
                @click="operationDelete(operationData)"
                v-if="viewType == 'add' || viewType == 'launch' || viewType == 'edit'"
            >
                删除
            </Button>
            <template v-if="viewType == 'examine' && !operationData.record?.departureApplyStatus">
                <Button size="small" type="primary" @click="refuseOrAdopt(operationData.record, 1)">通过</Button>
                &nbsp;
                <Button size="small" danger type="primary" class="btn" @click="refuseOrAdopt(operationData.record, 2)">
                    拒绝
                </Button>
            </template>
        </template>
    </Table>
</template>

<script lang="ts">
// import { RuleObject } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import { ref, defineComponent, watchEffect, h, toRefs, watch } from 'vue'
import inductionApplyStore from '/@/store/modules/inductionApply'

export default defineComponent({
    name: 'EmployedStaff',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'edit', 'add', 'launch', 'examine'].indexOf(value) !== -1
            },
        },
        currentValue: {
            type: Object,
        },
    },
    emits: ['operationEdit', 'selectedRowsArr', 'getDataInfo', 'operationDelete', 'auditRequest'],
    setup(props, { emit }) {
        const { viewType, visible } = toRefs<any>(props)
        // const isadd = computed(() => viewType.value == 'add')
        // const issee = computed(() => viewType.value == 'see')
        // const isexamine = computed(() => viewType.value == 'examine')
        const columns = ref<inObject[]>([])
        const columnsData = ref<inObject[]>([])
        watchEffect(() => {
            columnsData.value = inductionApplyStore()
                .getEmployedStaffList.filter((el) => !el.isDelete)
                .map((item) => {
                    return { ...item, disabled: false }
                })
        })

        const setColumns = () => {
            let columnsList = [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    customRender: (record) => {
                        return h('span', record.index + 1)
                    },
                    width: 80,
                    fixed: 'left',
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    align: 'center',
                    width: 100,
                    fixed: 'left',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    align: 'center',
                    width: 150,
                    fixed: 'left',
                },

                {
                    title: '联系方式',
                    dataIndex: 'phone',
                    align: 'center',
                    width: 150,
                },
                {
                    title: '人员类型',
                    dataIndex: 'personnelTypeLabel',
                    align: 'center',
                    customRender: ({ record }) => {
                        return record.personnelTypeLabel
                    },
                    width: 150,
                },
                {
                    title: '实际工作日期',
                    dataIndex: 'workDurationArray',
                    width: 200,
                    align: 'center',
                    customRender: ({ record }) => {
                        if (record?.workDurationStartDate) {
                            return record?.workDurationStartDate + ' 至 ' + record?.workDurationEndDate
                        }
                    },
                },

                {
                    title: '经济补偿金',
                    dataIndex: 'compensation',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '申请日期',
                    dataIndex: 'createdDate',
                    type: 'date',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '证明人',
                    dataIndex: 'certifier',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '证明人电话',
                    dataIndex: 'certifierPhone',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '退回原因',
                    dataIndex: 'returnReason',
                    width: 150,
                    align: 'center',
                    ellipsis: true,
                },
                {
                    title: '离职日期',
                    dataIndex: 'departureDate',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '停止缴费年月',
                    dataIndex: 'stopPaymentDate',
                    type: 'date',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '审核结果',
                    dataIndex: 'departureApplyStatusLabel',
                    align: 'center',
                    width: 100,
                    fixed: 'right',
                },
                {
                    title: '拒绝原因',
                    dataIndex: 'reasonRejection',
                    align: 'center',
                    fixed: 'right',
                    slots: { customRender: 'reasonRejection' },
                    width: 200,
                },
                {
                    title: '操作',
                    dataIndex: 'operate',
                    align: 'center',
                    width: 150,
                    slots: { customRender: 'operation' },
                    fixed: 'right',
                },
            ]
            columns.value = columnsList.filter((item) => {
                if (viewType.value == 'see') {
                    return item.dataIndex !== 'operate'
                } else if (viewType.value == 'add' || viewType.value == 'launch' || viewType.value == 'edit') {
                    return item.dataIndex !== 'departureApplyStatusLabel' && item.dataIndex !== 'reasonRejection'
                }
                return true
            })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    setColumns()
                } else {
                    // rowSelectionKeys.value = []
                }
            },
            { immediate: true },
        )
        let rowSelectionKeys = ref<any[]>([])
        const rowSelection = {
            selectedRowKeys: rowSelectionKeys,
            onChange: (selectedRowKeys: (string | number)[], selectedRows: inObject[]) => {
                rowSelectionKeys.value = selectedRowKeys
                emit('selectedRowsArr', selectedRows)
            },
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: !!record.departureApplyStatus, // Column configuration not to be checked
                }
            },
        }
        const operationEdit = (data) => {
            emit('operationEdit', data)
        }
        const operationDelete = (data) => {
            emit('operationDelete', data)
        }
        const refuseOrAdopt = (record, type) => {
            if (type == 2) {
                if (!record.rejectionReason) {
                    message.error('请输入拒绝理由！')
                    return
                }
            }
            emit('auditRequest', type, [record.id], record.rejectionReason)
        }
        return {
            columns,
            columnsData,
            operationEdit,
            operationDelete,
            rowSelection,

            refuseOrAdopt,
        }
    },
})
</script>
<style scoped lang="less">
.smallTable {
    margin: 15px 0;
    // width: 100%;
    // :deep(.ant-table-body) {
    //     overflow-x: auto;
    // }
    :deep(.ant-table-thead > tr > th) {
        background-color: @primary-color;
        color: #fff;
    }
}
.editable-cell {
    position: relative;
    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
        padding-right: 24px;
    }

    .editable-cell-text-wrapper {
        padding: 5px 24px 5px 5px;
    }

    .editable-cell-icon,
    .editable-cell-icon-check {
        position: absolute;
        right: 0;
        width: 20px;
        cursor: pointer;
    }

    .editable-cell-icon {
        margin-top: 4px;
        display: none;
    }

    .editable-cell-icon-check {
        line-height: 28px;
    }

    .editable-cell-icon:hover,
    .editable-cell-icon-check:hover {
        color: #108ee9;
    }

    .editable-add-btn {
        margin-bottom: 8px;
    }
    &:hover .editable-cell-icon {
        display: inline-block;
    }
}
</style>
