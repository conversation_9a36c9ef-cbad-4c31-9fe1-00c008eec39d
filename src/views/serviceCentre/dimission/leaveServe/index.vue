<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'leaveServe_add'" @click="editRow('add')">新增</Button>
        <Button danger type="primary" v-auth="'leaveServe_refuse'" @click="rejectRow">批量拒绝</Button>
        <Button class="btn" v-auth="'leaveServe_pass'" type="primary" @click="passRow">批量通过</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="api/hr-apply-departures/page"
        deleteApi="/api/hr-apply-departures/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <CreateModal
        v-model:visible="showEdit"
        :title="modalTitle"
        :currentValue="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import CreateModal from './CreateModal.vue'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, openNotification } from '/@/utils'
import { useRoute } from 'vue-router'
export default defineComponent({
    name: 'LeaveServeIndex',
    components: { CreateModal },
    setup() {
        let applicationList = ref<LabelValueOptions>([]) //状态

        onMounted(() => {
            //状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/leaveStates', {}).then((res) => {
                applicationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        //筛选
        const route = useRoute()
        const params = ref<{}>({
            typeName: undefined,
            applyStatusList: route.query?.applyStatusList ? JSON.parse(route.query?.applyStatusList as string) : undefined,
        })

        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '客户编号',
                key: 'unitNumber',
            },
            {
                label: '客户名称',
                key: 'clientIds',
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'daterange',
                label: '创建日期',
                key: 'createdDateQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'applyStatusList',
                options: applicationList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 150,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },

            {
                title: '人员数量',
                dataIndex: 'staffNum',
                align: 'center',
                width: 100,
            },
            {
                title: '创建日期',
                dataIndex: 'createdDate',
                align: 'center',
                width: 200,
            },
            {
                title: '状态',
                dataIndex: 'applyStatus',
                align: 'center',
                width: 100,
                slots: { customRender: 'staffStatus' },
                customRender: ({ record }) => {
                    return record.applyStatusLabel
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return record.specialized
                },
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
            },
        ]
        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增')
        const viewType = ref('add')
        // 当前编辑的数据
        const currentValue = ref(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            modalTitle.value =
                viewTypeName == 'add'
                    ? '新增离职员工'
                    : viewTypeName == 'edit'
                    ? '编辑离职员工'
                    : viewTypeName == 'examine'
                    ? '审核'
                    : '查看'
            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }
        const showInduction = ref(false)
        const inductionValue = ref(null)

        const clickStaffStatus = (record) => {
            showInduction.value = true
            inductionValue.value = { ...record, id: record.staffId, employedId: record.id }
        }

        const modalCancelInduction = () => {
            showInduction.value = false
            inductionValue.value = null
        }

        const modalCancel = () => {}
        const modalConfirm = () => {
            if (viewType.value == 'add') {
                searchData()
            } else {
                tableRef.value.refresh()
            }
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'leaveServe_watch',
                    show: (record) => {
                        return record.applyStatus != '0'
                    },
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '编辑',
                    auth: 'leaveServe_edit',
                    show: (record) => {
                        return record.applyStatus == '0'
                    },
                    click: (record) => editRow('edit', record),
                },
                {
                    neme: '审核',
                    auth: 'leaveServe_audit',
                    show: (record) => {
                        return record.applyStatus == '1' || record.applyStatus == '5'
                    },
                    click: (record) => editRow('examine', record),
                },
            ]),
        )

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            showRejec.value = true
        }
        const checkerReason = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            request
                .post('/api/hr-apply-departures/manager-review', {
                    applyIdList: applyIdList,
                    checkerReason: checkerReason.value,
                    opt: false,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    tableRef.value.refresh()
                })
            showRejec.value = false
        }
        // 批量通过
        const passRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    request
                        .post('/api/hr-apply-departures/manager-review', {
                            applyIdList: applyIdList,
                            checkerReason: null,
                            opt: true,
                        })
                        .then((res) => {
                            let tip = ''
                            let success = '您选择的数据已修改成功'
                            if (res.error_status) {
                                tip = res.error_status
                                success = ',选择的其它数据已修改成功'
                            }
                            if (res.success?.length) {
                                tip += success
                            }
                            openNotification(tip)

                            tableRef.value.refresh()
                        })
                },
                onCancel() {},
            })
        }

        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量拒绝显示
            showRejec,
            // 点击批量拒绝
            rejectRow,
            //批量拒绝描述
            checkerReason,
            //批量拒绝确认
            rejectConfirm,
            // 批量通过
            passRow,
            //新增
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,

            clickStaffStatus,
            modalCancelInduction,
            showInduction,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
