<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" okText="提交" width="1200px">
        <div>
            <Form
                ref="formAddInduction"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                class="form-flex"
            >
                <EmployeeMatching
                    :clientId="clientId"
                    :formItemNames="['name', 'certificateNum']"
                    v-model:name="formData.name"
                    v-model:card="formData.certificateNum"
                    @changeCertificateNum="changeCertificateNum"
                    :disabled="viewType == 'launch'"
                />
                <template v-for="item in myOptions" :key="item">
                    <MyFormItem
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        :class="item.slots"
                        v-if="item.show != false"
                    />
                </template>
            </Form>
            <!-- <div class="text">
                <div>返回原因:</div>
                <Textarea :rows="4" v-model:value="formData.departureReason" placeholder="请输入返回原因" />
            </div> -->
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'
import { validatePhone } from '/@/utils/format'

import { staffTypeOptions } from '/@/utils/dictionaries'
import inductionApplyStore from '/@/store/modules/inductionApply'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { Moment } from 'moment'
import moment from 'moment'
export default defineComponent({
    name: 'AddInduction',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        itemInfo: {
            type: Object,
            default: () => {},
        },
        inductionApplyData: {
            type: Object,
            default: () => {},
        },

        editDataIndex: Number,
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'add', 'launch', 'edit', 'examine'].indexOf(value) !== -1
            },
        },
    },
    emits: ['cancel', 'confirm', 'update:visible'],
    setup(props, { emit }) {
        const { visible, itemInfo, inductionApplyData, editDataIndex, viewType } = toRefs<any>(props)

        const sexList = ref<inObject[]>([]) // 性别
        const staffTypeList = ref<inObject[]>([]) //人员类型
        const clientId = ref('')
        const staffId = ref('')
        // const date = ref<[]>([])
        const inductionData = ref('')
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('sexType')
                .then((res: inObject[]) => {
                    sexList.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('staffType')
                .then((res: inObject[]) => {
                    staffTypeList.value = res
                })
        })

        const myOptions = ref([
            {
                label: '姓名',
                name: 'name',
                show: false,
            },
            {
                label: '身份证号',
                name: 'certificateNum',
                // validator: idCardValidity,
                show: false,
            },
            {
                label: '性别',
                name: 'sex',
                type: 'change',
                options: sexList,
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'sex'),
                disabled: true,
                required: false,
            },
            {
                label: '联系方式',
                name: 'phone',
                disabled: true,
                required: false,
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'personnelType'),
                disabled: true,
                required: false,
            },
            {
                label: '实际工作日期',
                name: 'workDurationArray',
                type: 'rangePicker',
                ruleType: 'array',
                default: [],
                required: false,
                onChange: (data) => workDurationArrayChange(data),
                disabledDate: (current: Moment) => {
                    if (!current || !inductionData.value) {
                        return false
                    }
                    return new Date(inductionData.value).valueOf() >= current.endOf('day').valueOf()
                },
            },

            {
                label: '经济补偿金',
                name: 'compensation',
                type: 'number',
                ruleType: 'number',
                min: 0,
            },
            {
                label: '退回日期',
                name: 'departureDate',
                type: 'date',
                disabled: computed(() => {
                    return viewType.value == 'launch'
                }),
                disabledDate: (current: Moment) => {
                    if (!current || !inductionData.value) {
                        return false
                    }
                    return new Date(inductionData.value).valueOf() >= current.endOf('day').valueOf()
                },
            },
            {
                label: '停止缴费年月',
                name: 'stopPaymentDate',
                type: 'month',
            },
            {
                label: '证明人',
                name: 'certifier',
                required: false,
            },
            {
                label: '证明人电话',
                name: 'certifierPhone',
                required: false,
                validator: validatePhone,
            },
            {
                label: '退回原因',
                name: 'returnReason',
                required: true,
                type: 'textarea',
                width: '99%',
            },
        ])
        const selectChange = (value, option, name) => {
            formData.value[name + 'Label'] = option.label
        }
        const workDurationArrayChange = (data) => {
            if (data?.length) {
                formData.value.workDurationStartDate = data[0]
                formData.value.workDurationEndDate = data[1]
            }
        }
        // Form 实例
        const formAddInduction = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formAddInduction.value.resetFields()
        }

        watch(
            visible,
            () => {
                if (visible.value) {
                    if (itemInfo.value) {
                        staffId.value = itemInfo.value?.staffId || ''
                        clientId.value = itemInfo.value?.clientId || ''
                        inductionData.value = itemInfo.value?.boardDate || ''
                        formData.value = Object.assign({}, initFormData, itemInfo.value)
                    } else {
                        clientId.value = inductionApplyData.value?.clientId || ''
                        formData.value = Object.assign({}, initFormData, inductionApplyData.value)
                        // formData.value.contractStartDate = []
                    }
                }
            },
            { immediate: true },
        )

        //自动填入
        const changeCertificateNum = (userInfo) => {
            formData.value.sex = userInfo.sex
            formData.value.phone = userInfo.phone
            formData.value.personnelType = userInfo.personnelType
            formData.value.personnelTypeLabel = userInfo.personnelTypeLabel
            formData.value.workDurationArray = [userInfo.firstContractStartDate, moment().format('YYYY-MM-DD')]
            formData.value.workDurationStartDate = formData.value.workDurationArray[0]
            formData.value.workDurationEndDate = formData.value.workDurationArray[1]
            staffId.value = userInfo.id
            inductionData.value = userInfo.boardDate
            formValidateOptional(['name', 'sex', 'phone', 'personnelType'])
        }

        // 单个校验
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formAddInduction.value?.validate(nameList)
            })
        }

        const cancel = () => {
            resetFormData()
            emit('cancel')
            emit('update:visible', false)
        }

        const confirm = () => {
            let validate = inductionApplyStore().getEmployedStaffList.some((item, i) => {
                if (editDataIndex.value == i) {
                    return false
                }
                return item.certificateNum == formData.value?.certificateNum
            })
            if (validate) {
                message.warning('您选中的员工有重复，请重新选择')
                return
            }

            formAddInduction.value
                .validate()
                .then(() => {
                    request
                        .post('/api/hr-apply-departure-staffs/save', {
                            id: formData.value.id,
                            staffId: staffId.value,
                            clientId: clientId.value,
                        })
                        .then((res) => {
                            emit('confirm', { ...formData.value, staffId: staffId.value, clientId: clientId.value })
                            cancel()
                        })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            //客户id
            clientId,
            // date,
            rules,
            formData,
            myOptions,
            formAddInduction,
            staffTypeList,
            confirm,
            cancel,
            staffTypeOptions,
            //身份证号选择
            changeCertificateNum,
        }
    },
})
</script>
<style scoped lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
.btn {
    text-align: right;
    margin: 10px;
    button {
        margin-left: 10px;
    }
}
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.add-table {
    border: 1px solid #f0f0f0;
    border-top: none;
    padding: 10px;
    text-align: center;
    color: #999;
}
.text {
    display: flex;
    margin-top: 20px;
    padding: 0 40px;

    div {
        width: 70px;
    }
}
</style>
