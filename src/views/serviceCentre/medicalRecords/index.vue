<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="addMedicalRecord">新增</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-remote-medical-records/page"
        :params="params"
        :columns="columns"
        exportUrl="/api/hr-remote-medical-records/export"
        :sorter="false"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #action="{ record }">
            <Button type="primary" size="small" @click="remarkClick(record)">备注</Button>
        </template>
    </BasicTable>
    <MyModal
        v-model:visible="showModel"
        :title="'备注'"
        :item="modelData"
        @onCancel="
            () => {
                showModel = false
            }
        "
        @confirm="modalConfirm"
        :roleId="roleId"
    />
    <AddModal :visible="showMedicalRecord" :title="modalTitle" @cancel="medicalRecordCancel" @confirm="medicalRecordConfirm" />
</template>
<!-- reimbursementApplication_create -->
<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import AddModal from './component/AddModal.vue'
import MyModal from './MyModal.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import { getDynamicText } from '/@/utils'

import { useRoute } from 'vue-router'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { staffStatus } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'medicalRecords',
    components: { MyModal, PostTree, AddModal },
    setup() {
        // 获取全部角色
        //筛选
        let applicationList = ref<LabelValueOptions>([]) //状态
        let staffTypeList = ref<LabelValueOptions>([]) //人员类型
        let sexTypeList = ref<LabelValueOptions>([]) // 性别
        const roleId = ref(0) // 角色标识--1客户 2经理 3超管
        onMounted(() => {
            //人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((res: LabelValueOptions) => {
                    staffTypeList.value = res
                })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
            //     staffTypeList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            //状态
            dictionaryDataStore()
                .setDictionaryData('IssueStates', '')
                .then((res: LabelValueOptions) => {
                    applicationList.value = res
                })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/IssueStates', {}).then((res) => {
            //     applicationList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })

            // 性别
            dictionaryDataStore()
                .setDictionaryData('sexType', '')
                .then((res: LabelValueOptions) => {
                    sexTypeList.value = res
                })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
            //     sexTypeList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
        })

        const showModel = ref(false)
        //筛选
        const route = useRoute()
        const params = ref<{}>({
            certificateStatusList: route.query?.certificateStatusList
                ? JSON.parse(route.query?.certificateStatusList as string)
                : undefined,
        })

        const options: SearchBarOption[] = [
            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIds',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexTypeList,
                multiple: true,
            },

            {
                type: 'string',
                // label: '岗位',
                key: 'professionName',
                // type: 'selectSlot',
                label: '岗位',
                // key: 'stationIdList',
                placeholder: '岗位',
                maxTag: '0',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },

            {
                type: 'daterange',
                label: '登记生效开始日期',
                key: 'registerStartDateQuery',
            },
            {
                type: 'daterange',
                label: '登记生效结束日期',
                key: 'registerEndDateQuery',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return record.sexLabel
                },
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 200,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
            },

            {
                title: '登记生效开始日期',
                dataIndex: 'registerStartDate',
                width: 150,
                align: 'center',
            },
            {
                title: '登记生效结束日期',
                dataIndex: 'registerEndDate',
                width: 150,
                align: 'center',
            },
            {
                title: '就医地',
                dataIndex: 'medicinePlace',
                width: 150,
                align: 'center',
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'remark',
                align: 'center',
                slots: { customRender: 'action' },
                width: 150,
                fixed: 'right',
            },
        ]

        const modelData = ref<any>({})
        // 点击备注
        const remarkClick = (record) => {
            showModel.value = true
            modelData.value = record
        }

        const showMedicalRecord = ref<any>(false)
        const modalTitle = ref<any>('')
        const addMedicalRecord = () => {
            showMedicalRecord.value = true
            modalTitle.value = '新增异地医疗备案'
        }

        const medicalRecordCancel = () => {
            showMedicalRecord.value = false
        }

        const medicalRecordConfirm = () => {
            showMedicalRecord.value = false
            tableRef.value.refresh(1)
        }
        const modalConfirm = () => {
            showModel.value = false
            tableRef.value.refresh()
        }

        const CertificatmodalClose = () => {
            // showCertificatModal.value = false
        }
        const onlineAccessories = ref({})
        const confirmOnline = (file) => {
            onlineAccessories.value = file
        }

        const confirmOffline = () => {
            tableRef.value.refresh()
        }

        // 导出
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // const examineName = ref('')
        watch(
            selectedRowsArr,
            (newValue) => {
                // inductionApplyStore().setEmployedStaffList(newValue)
            },
            { deep: true },
        )
        watch(
            params,
            (newValue) => {
                console.log('params', newValue)
            },
            { deep: true },
        )
        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            options,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            showModel, //model显隐
            modelData, //model数据
            //多选数组
            selectedRowsArr,

            //弹窗确认
            modalConfirm,

            remarkClick, //备注点击

            CertificatmodalClose,
            //线上上传证明
            confirmOnline,
            //线下上传证明
            confirmOffline,
            onlineAccessories,

            roleId,
            exportText,
            exportData,
            addMedicalRecord,
            showMedicalRecord,
            modalTitle,
            medicalRecordCancel,
            medicalRecordConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
