<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="600px"
        :footer="null"
        @selectedRow="templateSelect"
    >
        <div>
            <Form
                ref="formInline"
                :model="formData"
                :rules="rules"
                style="
                    overflow-y: auto;
                    overflow-x: auto;
                    margin-top: 20px;
                    margin-right: 20px;
                    padding-left: 40px;
                    padding-right: 40px;
                "
            >
                <div class="btns">
                    <Button type="primary" @click="selectedStaff">选择员工</Button>
                </div>
                <template v-for="(item, index) in myOptions" :key="index">
                    <MyFormItem
                        :width="item.width"
                        :item="item"
                        v-model:value="formData[item.name]"
                        :class="item.slots"
                        v-if="item.show != false"
                    >
                        <template #staffInfo>
                            <Input :disabled="true" placeholder="请选择员工" v-model:value="formData.staffId" />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="ant-modal-footer">
                <Button key="back" @click="cancel">取消</Button>
                <Button key="submit" type="primary" @click="confirm">确定</Button>
            </div>
        </div>
    </BasicEditModalSlot>
    <staffChoiceList :visible="showSelectedStaff" @cancel="selectedCancel" @confirm="selectedConfirm" />
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'
import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import staffChoiceList from '/@/components/EmployeeMatching/src/staffChoiceList.vue'
export default defineComponent({
    name: 'AddStaff',
    components: { staffChoiceList },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm', 'selectedRow'],

    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const { visible } = toRefs<any>(props)
        watch(visible, () => {
            if (visible) {
                formData.value = { ...Object.assign({}, initFormData) }
            }
        })
        const sexList = ref<object[]>([]) // 性别
        const station = ref<object[]>([]) //岗位
        const staffTypeList = ref<object[]>([]) //人员类型
        const staffsInfo = ref<any>()
        const showTemplateProot = ref<any>(false)
        const showSelectedStaff = ref<any>(false)

        const selectedTemplate = () => {
            showTemplateProot.value = true
        }
        const selectIds: any = ref([])
        const templateSelect = (res) => {
            console.log(res)
        }
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '员工信息',
                name: 'staffId',
                type: 'slots',
                slots: 'staffInfo',
            },
            {
                label: '就医地',
                name: 'medicinePlace',
            },
            {
                label: '登记生效开始日期',
                name: 'registerStartDate',
                type: 'date',
            },
            {
                label: '登记生效结束日期',
                name: 'registerEndDate',
                type: 'date',
            },
            {
                label: '联系电话',
                name: 'phone',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const selectedStaff = () => {
            showSelectedStaff.value = true
        }
        const selectedCancel = () => {
            showSelectedStaff.value = false
        }
        let templateTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('certificateTemplateType', '')
                .then((data: inObject[]) => {
                    templateTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const columns = [
            {
                title: '模板标题',
                dataIndex: 'title',
                align: 'center',
                width: 250,
            },
            {
                title: '模板类型',
                dataIndex: 'templateType',
                align: 'center',
                width: 120,
                customRender: ({ record }) => {
                    return templateTypeList.value.find((el) => {
                        return el.value == record.templateType
                    })?.label
                },
            },
            {
                title: '更新时间',
                dataIndex: 'lastModifiedDate',
                align: 'center',
                width: 150,
            },
            {
                title: '应用次数',
                dataIndex: 'useNum',
                align: 'center',
                width: 120,
            },
        ]
        const templateCancel = () => {
            showTemplateProot.value = false
        }
        const staffChoiceInfo = ref<any>({})
        const templateList = ref<any>()
        const appendList = ref<any>()
        const templateConfirm = (data) => {
            templateList.value = data
            formData.value.proofTemplateId = data.templateTypeLabel

            if (!formData.value.proofTemplateId) {
                formData.value.proofTemplateId = '暂无所需模板'
                appendList.value = data
                templateList.value.id = new Date().getTime().toString()
            }
            showTemplateProot.value = false
        }
        const selectedConfirm = (data) => {
            formData.value.staffId = data.name
            staffChoiceInfo.value = data
        }
        watch(
            visible,
            () => {
                if (visible) {
                }
            },
            { immediate: true },
        )

        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const confirm = () => {
            console.log(appendList.value)
            console.log(templateList.value)
            let formNewData = {
                staffId: staffChoiceInfo.value?.id,
                medicinePlace: formData.value.medicinePlace,
                registerStartDate: formData.value.registerStartDate,
                registerEndDate: formData.value.registerEndDate,
                phone: formData.value.phone,
            }

            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')

                    await request.post('/api/hr-remote-medical-records', formNewData)
                    message.success('新增成功!')

                    emit('confirm')
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        return {
            rules,
            formData,
            myOptions,
            formInline,
            sexList,
            station,
            staffTypeList,
            selectedTemplate,
            showTemplateProot,
            // clientList,
            // clientId,
            // loadData,
            confirm,
            cancel,
            columns,
            templateCancel,
            templateConfirm,
            selectIds,
            templateSelect,
            selectedStaff,
            showSelectedStaff,
            selectedCancel,
            staffsInfo,
            selectedConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
.btn {
    text-align: right;
    margin: 10px;
    button {
        margin-left: 10px;
    }
}
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
:deep(.add-table) {
    border: 1px solid #f0f0f0;
    border-top: none;
    padding: 10px;
    text-align: center;
    color: #999;
}
</style>
