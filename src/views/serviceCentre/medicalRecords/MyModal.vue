<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <ApplicationInfo :detailData="detailData" :title="'员工信息'">
            <template #inner="{ detailData }">
                <div class="item-flex-inner">
                    <span class="label">登记生效开始日期：</span>
                    <span>{{ detailData?.registerStartDate }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">登记生效结束日期：</span>
                    <span>{{ detailData?.registerEndDate }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">就医地：</span>
                    <span>{{ detailData?.medicinePlace }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">联系电话：</span>
                    <span>{{ detailData?.phone }}</span>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :applyOpLogs="applyOpLogsList" />
        <!-- 备注 -->
        <div class="examine" style="margin-top: 50px">
            <Divider type="vertical" class="divid" />
            <span>备注</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <Textarea v-model:value="textValue" :rows="4" allowClear placeholder="请输入备注" />
                </div>
            </div>
        </div>
        <div class="examine upView">
            <div class="upLeft">附件：</div>
            <div class="upLeft">
                <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                    <Button type="primary" size="small">上传文件</Button>
                </Upload>
                <div class="files">
                    <span class="item" v-for="(i, idx) in enclosures" :key="i.id">
                        <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                        <span @click="removeFile(idx)">x</span>
                    </span>
                </div>
            </div>
        </div>
        <template #footer>
            <div>
                <Button @click="onCancel()" class="">取消</Button>
                <Button @click="onConfirm" type="primary" :loading="loading" class="btn">完成</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import ApplicationInfo from '../retirement/component/ApplicationInfo.vue'
import OperationInfo from './component/OperationInfo.vue'
import moment, { Moment } from 'moment'
import request from '/@/utils/request'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
export default defineComponent({
    name: 'IndustrialInjuryModal',
    components: { ApplicationInfo, OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, item, modalType } = toRefs(props)
        const detailData = ref({})
        let applyOpLogsList = ref([])
        watch(
            visible,
            (newValue) => {
                if (visible) {
                    request.get(`/api/hr-remote-medical-records/${item.value.id}`, {}).then((res) => {
                        detailData.value = res
                        applyOpLogsList.value = res?.applyOpLogsList
                    })
                }
            },
            { deep: true },
        )
        const refImportFile = ref()

        let textValue = ref('')
        let loading = ref(false)

        const dateVisible = ref(false)
        const approachChange = ({ target: { value } }) => {
            if (value == '2') dateVisible.value = true
            else dateVisible.value = false
        }

        let enclosures = ref<any>([])

        const beforeUpload = async (file) => {
            const res = await uploadFile(file)
            enclosures.value.push(res)
            return false
        }
        const removeFile = (idx) => {
            enclosures.value.splice(idx, 1)
        }

        const onCancel = () => {
            restModel()
            emit('onCancel')
        }

        watch(modalType, (val) => {}, {
            immediate: true,
            deep: true,
        })
        const restModel = () => {
            enclosures.value = []
            textValue.value = ''
        }
        // confirm handle
        const onConfirm = () => {
            let params = {
                id: item.value.id,
                remark: textValue.value,
                appendixIds: enclosures.value.map((el) => el.id),
            }
            request
                .put(`/api/hr-remote-medical-records`, params)
                .then((res) => {
                    detailData.value = res
                    applyOpLogsList.value = res?.applyOpLogsList
                    loading.value = true
                    emit('confirm', false)
                    restModel()
                })
                .catch((err) => {
                    loading.value = false
                })
        }

        return {
            detailData, //
            applyOpLogsList,
            onConfirm,
            refImportFile,
            approachChange,
            dateVisible,
            textValue,
            previewFile,
            removeFile,
            beforeUpload,
            enclosures,
            loading,
            onCancel,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.item-flex-inner {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}

.upView {
    display: flex;
    align-items: flex-start;
    padding-left: 15px;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
