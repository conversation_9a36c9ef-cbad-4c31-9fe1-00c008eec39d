<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" width="1200px" :footer="null">
        <StepBar :stepsData="stepsData" :currentStep="applyStep" labelPlacement="vertical" />
        <OperationInfo :applyOpLogs="opLogsList" v-if="opLogsList?.length" />
    </BasicEditModalSlot>
</template>
<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import StepBar from '../retirement/component/StepBar.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'

const props = defineProps({
    title: String,
    item: {
        type: Object,
    },
    visible: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['cancel'])
const { item, visible } = toRefs<any>(props)
const opLogsList = ref<object[]>([])
const steps1 = [
    {
        title: '原单位发起',
    },
    {
        title: '新单位确认',
    },
    {
        title: '业务员审批',
    },
    {
        title: '借调完成',
    },
]
const steps2 = [
    {
        title: '新单位发起',
    },
    {
        title: '原单位确认',
    },
    {
        title: '业务员审批',
    },
    {
        title: '借调结束',
    },
]
const applyStep = ref(0)
const stepsData = ref([])

const getDetail = (id) => {
    const applyStepArr = [0, 1, 2, 3, 4, 1, 2, 3, 4, 4]
    request
        .get(`/api/hr-staff-secondments/${id}`)
        .then((res) => {
            applyStep.value = applyStepArr[Number(res.step)] || 0
            opLogsList.value = res.applyOpLogsList
            if (res.isDefault) stepsData.value = steps2
            else stepsData.value = steps1
        })
        .catch((err) => {
            console.log(err)
        })
}
watch(
    visible,
    () => {
        if (visible.value) {
            getDetail(item.value?.secondmentId)
        }
    },
    { immediate: true },
)
const cancel = () => {
    emit('cancel')
}
</script>
<style scoped lang="less">
// step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
