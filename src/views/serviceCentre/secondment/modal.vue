<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" :currentStep="applyStep" labelPlacement="vertical" />
        <ApplicationInfo :detailData="item" :showBasicWage="false">
            <template #inner="{ detailData }">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">借调至单位：</span>
                    <span>{{ detailData?.newClientName }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">开始日期：</span> <span>{{ detailData?.startDate }}</span>
                </div>
            </template>
            <template #outer="{ detailData }">
                <div class="item-outer">
                    <span class="label">借调说明：</span> <span>{{ detailData?.reason }}</span>
                </div>
                <div class="item-outer" v-if="item?.isDefault">
                    <span class="label">借调结束日期：</span> <span>{{ detailData?.endDate }}</span>
                </div>
                <div class="item-outer" v-if="item?.isDefault">
                    <span class="label">借调结束说明：</span> <span>{{ detailData?.remark }}</span>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" v-if="item?.applyOpLogsList?.length" />
        <Row :gutter="20">
            <Col span="10">
                <div class="examine" v-if="modalType !== 'look'">
                    <Divider type="vertical" class="divid" />
                    <span>备注</span>
                    <div class="examine-wrap">
                        <p class="linefeed"></p>
                        <Textarea v-model:value="remark" :rows="3" allowClear placeholder="请输入备注" />
                    </div></div
            ></Col>
            <Col span="10">
                <div class="examine" v-if="modalType !== 'look'">
                    <Divider type="vertical" class="divid" />
                    <span>拒绝理由</span>
                    <div class="examine-wrap">
                        <p class="linefeed"></p>
                        <Textarea v-model:value="reason" :rows="3" allowClear placeholder="若拒绝，请输入拒绝理由" />
                    </div></div
            ></Col>
        </Row>

        <template #footer>
            <div v-if="modalType !== 'look'">
                <Button @click="onConfirm(false, item?.step == 1 || item?.step == 5)" class="rejectBtn">拒绝</Button>
                <!-- 通过 -->
                <Button @click="onConfirm(true, item?.step == 1 || item?.step == 5)" type="primary" class="successBtn">
                    {{ item?.step == 1 ? '接收' : item?.step == 5 ? '确认' : '通过' }}
                </Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import { isEmpty } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import ApplicationInfo from '../retirement/component/ApplicationInfo.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'
import { message } from 'ant-design-vue'
const props = defineProps({
    title: String,
    visible: {
        type: Boolean,
        default: false,
    },
    modalType: {
        type: String,
        default: '',
    },
    item: {
        type: Object,
        default: () => {},
    },
})
const emit = defineEmits(['cancel', 'confirm'])
const { item, modalType, visible } = toRefs(props)

const steps1 = [
    {
        title: '原单位发起',
    },
    {
        title: '新单位确认',
    },
    {
        title: '业务员审批',
    },
    {
        title: '借调完成',
    },
]
const steps2 = [
    {
        title: '新单位发起',
    },
    {
        title: '原单位确认',
    },
    {
        title: '业务员审批',
    },
    {
        title: '借调结束',
    },
]
const applyStep = ref(0)
const stepsData = ref([])

watch(visible, (val, oldVal) => {
    if (val) {
        if (item.value?.isDefault) stepsData.value = steps2
        else stepsData.value = steps1
    }
})

watch(
    item,
    () => {
        if (item.value) {
            const applyStepArr = [0, 1, 2, 3, 4, 1, 2, 3, 4, 4]
            applyStep.value = applyStepArr[Number(item.value?.step)] || 0
        }
    },
    { immediate: true },
)

const reason = ref<string>('')
const remark = ref<string>('')
const resetReason = () => {
    reason.value = ''
    remark.value = ''
}

// cancel handle
const onCancel = () => {
    modalType.value !== 'look' && resetReason()
    emit('cancel')
}
// confirm handle
const onConfirm = (flag, isConfirm) => {
    if (isEmpty(reason.value) && !flag) {
        message.warn('请输入拒绝理由')
        return
    }
    emit('confirm', {
        remark: remark.value,
        reason: reason.value,
        id: item.value.id,
        opt: flag,
        isConfirm: isConfirm,
    })
    resetReason()
}
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.linefeed {
    width: 100%;
    padding: 0;
    margin: 0;
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
    }
}
.item-flex {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.item-outer {
    padding-left: 15px;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
.successBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
