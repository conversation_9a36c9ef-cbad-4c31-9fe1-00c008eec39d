<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #professionIdList="itemForm">
            <PostTree
                v-model:value="params.professionIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'secondment_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'secondment_confirm'" @click="batchConfirm(true, true)" class="downloadBtn">
            批量确认
        </Button>
        <Button type="primary" v-auth="'secondment_batchAudit'" @click="batchAudit(true, true)" class="downloadBtn">
            批量通过
        </Button>
        <Button type="primary" v-auth="'secondment_batchAudit'" @click="batchReject" class="rejectBtn"> 批量拒绝 </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-staff-secondments/page"
        :exportUrl="'/api/hr-staff-secondments/export'"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        :visible="modalVisible"
        :modalType="detailType"
        :item="currentValue"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="cancelHandle"
        @confirm="batchAudit(true, false)"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
    <!-- 借调 -->
    <SecondedModal
        v-model:visible="secondedModalVisible"
        title="结束借调"
        viewType="end"
        :item="currentValue"
        @confirm="modalConfirm"
    />
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { ref, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import permissionStore from '/@/store/modules/permission'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils/index'
import { SearchBarOption } from '/#/component'
import { sexList, staffStatus } from '/@/utils/dictionaries'

import MyModal from './modal.vue'
import SecondedModal from '/@/views/staff/staff/seconded/index.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { useRoute } from 'vue-router'
const RoleState = permissionStore().getPermission

// 筛选
let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
let statusTypeList = ref<LabelValueOptions>([]) // 状态

//表格数据
let columns = ref([
    {
        title: '原公司',
        dataIndex: 'oldClientName',
        width: 180,
        align: 'center',
    },
    {
        title: '借调至公司',
        dataIndex: 'newClientName',
        width: 180,
        align: 'center',
    },
    {
        title: '员工姓名',
        dataIndex: 'name',
        width: 100,
        align: 'center',
    },
    {
        title: '员工状态',
        dataIndex: 'staffStatus',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
            return staffStatus.find((el) => {
                return el.value == text
            })?.label
        },
    },
    {
        title: '身份证号',
        dataIndex: 'certificateNum',
        width: 180,
        align: 'center',
    },
    {
        title: '性别',
        dataIndex: 'sex',
        width: 70,
        align: 'center',
        customRender: ({ record }) => {
            return sexList.find((el) => {
                return record.sex == el.value
            })?.label
        },
    },
    {
        title: '联系方式',
        dataIndex: 'phone',
        align: 'center',
        width: 110,
    },
    {
        title: '岗位',
        dataIndex: 'professionName',
        width: 130,
        align: 'center',
    },
    {
        title: '人员类型',
        dataIndex: 'personnelType',
        align: 'center',
        width: 100,
        customRender: ({ record }) => {
            return staffTypeList.value.find((el) => {
                return record.personnelType == el.value
            })?.label
        },
    },
    {
        title: '借调开始日期',
        dataIndex: 'startDate',
        align: 'center',
        width: 130,
    },
    {
        title: '借调结束日期',
        dataIndex: 'endDate',
        align: 'center',
        width: 130,
    },
    {
        title: '状态',
        dataIndex: 'states',
        align: 'center',
        width: 135,
        customRender: ({ record }) => {
            return record.statesLabel
        },
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        slots: { customRender: 'operation' },
        width: 250,
        fixed: 'right',
    },
])

let options: SearchBarOption[] = [
    {
        label: '原单位',
        key: 'oldClientIds',
        placeholder: '原单位',
        multiple: true,
        type: 'clientSelectTree',
        maxTag: '0',
        checkStrictly: false,
    },
    {
        label: '借调至单位',
        key: 'newClientIds',
        placeholder: '借调至单位',
        multiple: true,
        type: 'clientSelectTree',
        maxTag: '0',
        checkStrictly: false,
    },
    {
        type: 'string',
        label: '员工姓名',
        key: 'name',
    },
    {
        type: 'select',
        label: '员工状态',
        key: 'staffStatusList',
        multiple: true,
        options: staffStatus,
    },
    {
        type: 'string',
        label: '身份证号',
        key: 'certificateNum',
    },
    {
        type: 'select',
        label: '性别',
        key: 'sexList',
        multiple: true,
        options: sexList,
    },
    {
        type: 'string',
        label: '联系方式',
        key: 'phone',
    },
    {
        type: 'selectSlot',
        label: '岗位',
        key: 'professionIdList',
        placeholder: '岗位',
        maxTag: '0',
    },
    {
        type: 'select',
        label: '人员类型',
        key: 'personnelTypeList',
        options: staffTypeList,
        multiple: true,
    },
    {
        type: 'daterange',
        label: '借调开始日期',
        key: 'startDateQuery',
    },
    {
        type: 'daterange',
        label: '借调结束日期',
        key: 'endDateQuery',
    },
    {
        type: 'select',
        label: '状态',
        key: 'statesList',
        options: statusTypeList,
        multiple: true,
    },
    {
        type: 'select',
        label: '',
        key: 'stepList',
        show: false,
        showSelect: false,
    },
]

onMounted(() => {
    // 人员类型
    dictionaryDataStore()
        .setDictionaryData('staffType', '')
        .then((data: inObject[]) => {
            staffTypeList.value = data.map((item) => {
                return { label: item.itemName, value: item.itemValue }
            })
        })
    // 状态
    dictionaryDataStore()
        .setDictionaryData('secondmentStatus', '')
        .then((data: inObject[]) => {
            statusTypeList.value = data.map((item) => {
                return { label: item.itemName, value: item.itemValue }
            })
        })
})
//表格dom
const tableRef = ref()
//筛选
const route = useRoute()
const params = ref<any>({
    statesList: route.query?.statusList ? JSON.parse(route.query?.statusList as string) : undefined,
    stepList: route.query?.stepList ? JSON.parse(route.query?.stepList as string) : undefined,
})

// 搜索
const searchData = async () => {
    tableRef.value.refresh(1)
}

const modalTitle = ref('')
const modalVisible = ref(false)
const detailType = ref('')

// 当前编辑的数据
const currentValue = ref<any>(null)

// 显示弹窗
const showModal = (record, type) => {
    modalVisible.value = true
    switch (type) {
        case 'look':
            modalTitle.value = '查看'
            break
        case 'audit':
            modalTitle.value = '审核'
            break
    }
    getDetails(record.id)
    detailType.value = type
}

// 获取详细信息
const getDetails = (id) => {
    request
        .get(`/api/hr-staff-secondments/${id}`)
        .then((res) => {
            currentValue.value = {
                ...res,
                sexLabel: sexList.find((el) => {
                    return res.sex == el.value
                })?.label,
            }
        })
        .catch((err) => {
            console.log(err)
        })
}

const cancelHandle = () => {
    modalVisible.value = false
    showReject.value = false
    checkerReason.value = ''
}

const showReject = ref(false)
const checkerReason = ref('')

const batchReject = () => {
    if (selectedRowsArr.value.length <= 0) {
        message.warning('请选择要拒绝的申请')
        return false
    }
    showReject.value = true
}

// 多选
const selectedRowsArr = ref([])
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selectedRowsArr.value)
})
// 批量导出
const batchExport = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

// 批量确认
const batchConfirm = (isBatch, flag, postObj?) => {
    if (selectedRowsArr.value.length <= 0 && isBatch) {
        message.error('请选择员工!')
        return
    }
    request
        .post('/api/hr-staff-secondments/confirmation-batch', {
            applyIdList: isBatch ? selectedRowsArr.value.map((el: any) => el.id) : [postObj.id],
            opt: flag,
            checkerReason: isBatch ? checkerReason.value : postObj.reason,
            remark: postObj?.remark,
        })
        .then((res: inObject) => {
            tableRef.value.refresh()
            if (res.checkCode == 500) {
                openNotification(res.checkMsg)
            }
            if (res.checkCode == 200) {
                message.success(res.checkMsg)
            }
            cancelHandle()
        })
        .catch((err) => {
            console.log(err)
        })
}

// 批量审核
const batchAudit = (isBatch, flag, postObj?) => {
    if (selectedRowsArr.value.length <= 0 && isBatch) {
        message.error('请选择员工!')
        return
    }
    request
        .post('/api/hr-staff-secondments/salesman-approval-batch', {
            applyIdList: isBatch ? selectedRowsArr.value.map((el: any) => el.id) : [postObj.id],
            opt: flag,
            checkerReason: isBatch ? checkerReason.value : postObj.reason,
            remark: postObj?.remark,
        })
        .then((res: inObject) => {
            tableRef.value.refresh()
            if (res.checkCode == 500) {
                openNotification(res.checkMsg)
            }
            if (res.checkCode == 200) {
                message.success(res.checkMsg)
            }
            cancelHandle()
        })
        .catch((err) => {
            console.log(err)
        })
}

const executeOperation = (obj) => {
    if (obj.isConfirm) batchConfirm(false, obj.opt, obj)
    else batchAudit(false, obj.opt, obj)
}

const canAudit = (record) => {
    const stepsList = [1, 2, 5, 6]
    if (
        ((record.step == 1 || record.step == 5) && !RoleState.staffState) ||
        (RoleState.staffState && (record.step == 2 || record.step == 6)) ||
        // (RoleState.roleKey == 'customer_service_manager' && (record.step == 2 || record.step == 6)) ||
        ((RoleState.roleKey == 'super_admin' || RoleState.roleKey == 'maintenance') && stepsList.includes(record.step))
    )
        return true
    else return false
}

// 结束借调
const secondedModalVisible = ref(false)
const actionSeconded = (record) => {
    secondedModalVisible.value = true
    currentValue.value = record
}

// 确认弹窗
const modalConfirm = () => {
    tableRef.value.refresh()
}

let myOperation = ref<inObject[]>(
    getHaveAuthorityOperation([
        {
            neme: '查看',
            auth: 'secondment_look',
            show: true,
            click: (record) => showModal(record, 'look'),
        },
        {
            neme: '审批',
            auth: 'secondment_audit',
            show: (record) => {
                return (record.states == 1 || record.states == 2) && canAudit(record)
            },
            click: (record) => showModal(record, 'audit'),
        },
        {
            neme: '借调结束',
            auth: 'secondment_finish',
            show: (record) => {
                return (
                    (record.states == 2 && record.step == 4 && record.isDefault == 0) ||
                    (record.states == 4 && record.step == 9 && record.isDefault == 1)
                )
            },
            click: (record) => actionSeconded(record),
        },
    ]),
)
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
.hidden {
    display: none;
}
</style>
