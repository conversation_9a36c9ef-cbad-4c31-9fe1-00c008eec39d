<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" title="选择员工" width="1000px">
        <SearchBar v-model="params" :options="options" @change="searchData" />
        <BasicTable
            ref="tableRef"
            api="/api/hr-talent-staffs/page"
            :params="{ ...params, izDefault: false, clientId: clientId || null, staffStatusList: [3, 4, 7, 8, 9, 10, 11, 12] }"
            :columns="columns"
            rowSelectionType="radio"
            @selectedRowsArr="funSelectedRowsArr"
        />
        <template #footer>
            <div>
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { SearchBarOption } from '/#/component'
export default defineComponent({
    name: 'StaffChoiceList',
    props: {
        clientId: {
            type: String,
            default: '',
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        defaultParams: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { visible, defaultParams } = toRefs<any>(props)
        //筛选
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
        ]
        const params = ref<inObject>({})
        watch(visible, (val) => {
            if (val) {
                // params.value = Object.assign(params.value, defaultParams.value)
            }
        })

        // 搜索
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
            },
            {
                title: '合同起始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
            },
        ]

        //选择行
        const selectedRowsArr = ref<any[]>([])

        const funSelectedRowsArr = (selected) => {
            if (selected?.length) {
                // hasItBeenSelected.value = true
                selectedRowsArr.value = selected
            }
        }
        const checkboxProps = (record: inObject) => {
            let defaultChecked = false
            defaultChecked = record.certificateNum == selectedRowsArr.value[0]?.certificateNum
            return {
                defaultChecked: defaultChecked,
                id: record.id + '',
            }
        }
        const onCancel = () => {
            selectedRowsArr.value = []
            emit('update:visible', false)
            emit('cancel', true)
        }
        const onConfirm = () => {
            if (selectedRowsArr.value?.length) {
                emit('confirm', selectedRowsArr.value[0])
                onCancel()
            } else {
                message.error('请选择一位员工！')
            }
        }

        return {
            checkboxProps,
            columns,
            tableRef,
            funSelectedRowsArr,
            selectedRowsArr,
            options,
            params,
            searchData,
            onCancel,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
