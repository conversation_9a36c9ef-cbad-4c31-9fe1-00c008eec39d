<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #stationIdList="itemForm">
            <PostTree
                v-model:value="params.stationIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="addIssueCertificate">新增</Button>
        <Button type="primary" v-auth="'IssueCertificate_export'" @click="exportData">{{ exportText }}</Button>
        <!-- 需调整 -->
        <Button class="btn" type="primary" v-auth="'Certificate_content'" @click="refuseOrAdopt(1)">批量通过</Button>
        <Button danger type="primary" v-auth="'Certificate_reject'" @click="rejectRow()">批量拒绝</Button>

        <Button danger type="primary" v-auth="'IssueCertificate_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="api/hr-certificate-issuances/page"
        deleteApi="api/hr-certificate-issuances/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #staffStatus="{ record }">
            <Button type="link" size="small" @click="clickStaffStatus(record)">{{ record.departureStaffStatusLabel }}</Button>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="refuseOrAdopt(2)"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>
    <MyModal
        v-model:visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :roleId="roleId"
    />
    <!-- 开具证明弹框 -->
    <CertificatModal
        v-model:visible="showCertificatModal"
        @cancel="CertificatmodalClose"
        @confirmOffline="confirmOffline"
        :title="Title"
        :currentValue="currentValue"
    />
    <ElectronicSeal
        v-model:visible="showElectronicSeal"
        :currentValue="currentValue"
        @confirm="modalConfirm"
        :onlineAccessories="onlineAccessories"
    />
    <AddServiceCenter
        :servieType="servieType"
        :visible="showIssueCer"
        :title="modalTitle"
        @cancel="issueModalCancel"
        @confirm="issueModalConfirm"
    />

    <!-- <ElectronicSeal v-model:visible="showElectronicSeal" :currentValue="currentValue" /> -->
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted, watch, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import MyModal from './MyModal.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import AddServiceCenter from './AddServiceCenter.vue'
import CertificatModal from './IssueCertificatModal.vue'
import inductionApplyStore from '/@/store/modules/inductionApply'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils'
import ElectronicSeal from './ElectronicSeal.vue'
import { useAuth } from '/@/utils/hooks'

import { useRoute } from 'vue-router'
import downFile, { downMultFile } from '/@/utils/downFile'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import useUserStore from '/@/store/modules/user'
import { staffStatus } from '/@/utils/dictionaries'

export default defineComponent({
    name: 'IssueCertificateIndex',
    components: { MyModal, CertificatModal, ElectronicSeal, PostTree, AddServiceCenter },
    setup() {
        // 获取全部角色
        //筛选
        let applicationList = ref<LabelValueOptions>([]) //状态
        let staffTypeList = ref<LabelValueOptions>([]) //人员类型
        let specializedOptions = ref<LabelValueOptions>([]) //专管员
        let sexTypeList = ref<LabelValueOptions>([]) // 性别
        const roleId = ref(0) // 角色标识--1客户 2经理 3超管

        onMounted(() => {
            let IssueCertificate_examine = false // 客户审批
            let IssueCertificate_managerExamine = false // 经理审批
            let IssueCertificate_specialManagerExamine = false // 专管员审批
            useAuth.value.buttons.forEach((i) => {
                if (i == 'IssueCertificate_examine') {
                    IssueCertificate_examine = true
                }
                if (i == 'IssueCertificate_managerExamine') {
                    IssueCertificate_managerExamine = true
                }
                if (i == 'IssueCertificate_specialManagerExamine') {
                    IssueCertificate_specialManagerExamine = true
                }
            })

            if (IssueCertificate_examine && IssueCertificate_managerExamine && IssueCertificate_specialManagerExamine) {
                roleId.value = 3
            } else if (IssueCertificate_examine) {
                roleId.value = 1
            } else if (IssueCertificate_managerExamine) {
                roleId.value = 2
            }
            const userInfo = useUserStore().getUserInfo
            userInfo.roles.forEach((el) => {
                if (
                    el.roleKey == 'financial_director' ||
                    el.roleKey == 'customer_service_staff' ||
                    el.roleKey == 'xinchouzhuanyuanB' ||
                    el.roleKey == 'daiyuheguizhuanyuan' ||
                    el.roleKey == 'general_manager'
                ) {
                    roleId.value = 4
                }
            })
            //人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((res: LabelValueOptions) => {
                    staffTypeList.value = res
                })
            //状态
            dictionaryDataStore()
                .setDictionaryData('IssueStates', '')
                .then((res: LabelValueOptions) => {
                    applicationList.value = res
                })
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
            // 性别
            dictionaryDataStore()
                .setDictionaryData('sexType', '')
                .then((res: LabelValueOptions) => {
                    sexTypeList.value = res
                })
        })
        //筛选
        const route = useRoute()
        const params = ref<{}>({
            certificateStatusList: route.query?.certificateStatusList
                ? JSON.parse(route.query?.certificateStatusList as string)
                : undefined,
        })

        const options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIds',
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位',
                key: 'stationIdList',
                placeholder: '岗位',
                maxTag: '0',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },

            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
            {
                type: 'string',
                label: '证明名称',
                key: 'certificateName',
            },

            {
                type: 'select',
                label: '状态',
                key: 'certificateStatusList',
                options: applicationList,
                multiple: true,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'userIdList',
                options: specializedOptions,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        /**
         * 新增证明开局服务
         */
        const servieType = ref<any>('')
        const showIssueCer = ref(false)
        const addIssueCertificate = () => {
            servieType.value = 'issueCer'
            showIssueCer.value = true
            modalTitle.value = '证明开具新增'
        }
        const issueModalCancel = () => {
            showIssueCer.value = false
        }
        const issueModalConfirm = () => {
            showIssueCer.value = false
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                width: 200,
                customRender: ({ record }) => {
                    return record.sexLabel
                },
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 200,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',

                width: 150,
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
            },

            {
                title: '申请日期',
                dataIndex: 'createdDate',
                type: 'date',
                width: 150,
                align: 'center',
            },
            {
                title: '证明模板',
                dataIndex: 'title',
                width: 150,
                align: 'center',
            },
            {
                title: '证明名称',
                dataIndex: 'certificateName',
                width: 150,
                align: 'center',
            },

            {
                title: '状态',
                dataIndex: 'certificateStatus',
                align: 'center',
                width: 200,
                customRender: ({ record }) => {
                    return record.certificateStatusLabel
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ]
        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('查看')
        const Title = ref('开具证明')

        const viewType = ref('')
        // 当前编辑的数据
        const currentValue = ref(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            // modalTitle.value = viewTypeName == 'add' ? '新增' : viewTypeName == 'examine' ? '审核' : '查看'
            modalTitle.value = viewTypeName == 'examine' ? '审核' : '查看'

            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }

        const showInduction = ref(false)
        const inductionValue = ref(null)

        const clickStaffStatus = (record) => {
            showInduction.value = true
            inductionValue.value = { ...record, id: record.id, employedId: record.id }
        }

        const modalCancelInduction = () => {
            showInduction.value = false
            inductionValue.value = null
        }

        const modalCancel = () => {
            currentValue.value = null
        }
        const modalConfirm = () => {
            tableRef.value.refresh()
        }

        // 导出接口
        const exportUrl = '/api/hr-certificate-issuances/export'
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出按钮
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // 批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {})
        }
        const showCertificatModal = ref(false)

        // 开具证明按钮
        const issueCertificat = async (data) => {
            currentValue.value = data
            if (data.isDefault) {
                try {
                    const file = await request.post('/api/hr-certificate-issuances/proof-template-pretreatment', { id: data.id })
                    file?.result && confirmOnline(file.result)
                } catch (error) {
                    console.log(error)
                }
            } else {
                showCertificatModal.value = true
            }
        }
        const CertificatmodalClose = () => {
            // showCertificatModal.value = false
        }
        const onlineAccessories = ref({})
        const confirmOnline = (file) => {
            onlineAccessories.value = file
            showElectronicSeal.value = true
        }

        const confirmOffline = () => {
            tableRef.value.refresh()
        }

        //印章开关
        const showElectronicSeal = ref(false)

        const downloadFil = (item) => {
            if (!item.hrAppendixList?.length) {
                message.warning('员工没有上传附件信息')
                return
            }

            if (item?.hrAppendixList?.length > 1) {
                let urls: any[] = []
                let names: any[] = []
                item?.hrAppendixList?.forEach((element: any) => {
                    if (element?.fileUrl) {
                        urls.push(element?.fileUrl)
                        names.push(element?.originName)
                    }
                })
                downMultFile('客户协议附件批量导出', urls, names)
            } else {
                downFile('get', item?.hrAppendixList[0].fileUrl, item?.hrAppendixList[0].originName, {})
            }
        }
        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'IssueCertificate_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },

                {
                    neme: '审核',
                    auth: 'IssueCertificate_audit',
                    show: (record) => {
                        if (
                            roleId.value == 3 &&
                            (record.certificateStatus == 6 || record.certificateStatus == 2 || record.certificateStatus == 0)
                        ) {
                            return true
                        }
                        if (roleId.value == 1 && record.certificateStatus == 0) {
                            return true
                        }
                        if (roleId.value == 2 && record.certificateStatus == 2) {
                            return true
                        }
                        if (roleId.value == 4 && record.certificateStatus == 6) {
                            return true
                        }
                        return false
                    },
                    click: (record) => editRow('examine', record),
                },
                {
                    neme: '下载附件',
                    auth: 'IssueCertificate_download',
                    show: (record) => {
                        return !record.isDefault
                        // return !!record.hrAppendixList?.length
                    },
                    click: downloadFil,
                },
                {
                    neme: '开具证明',
                    auth: 'IssueCertificate_prove',
                    show: (record) => {
                        // return true
                        return record.certificateStatus == 4
                    },
                    click: issueCertificat,
                },
            ]),
        )

        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // const examineName = ref('')
        // 批量拒绝
        const rejectRow = () => {
            // examineName.value = name
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            checkerReason.value = ''

            showRejec.value = true
        }
        const checkerReason = ref('')
        // 确认弹窗
        const refuseOrAdopt = (type) => {
            // examineName.value = name
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择审核人员')
                return false
            }
            if (type == 2) {
                if (!checkerReason.value) {
                    message.warning('请填写拒绝理由')
                    return false
                }
                showRejec.value = false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            let api = '/api/hr-certificate-issuances/batch-pass'
            // if (examineName.value == 'manager') {
            //     api = '/api/hr-certificate-issuances/manager-review'
            // } else {
            //     api = '/hr-certificate-issuances/manager-review'
            // }

            // 客户PC端审核通过或者拒绝   /hr-certificate-issuances/customer-review
            // 经理审核通过或者拒绝  /hr-certificate-issuances/manager-review
            //  roleId 角色标识--1客户 2经理 3超管

            request
                .post(api, {
                    roleId: roleId.value,
                    opt: type != 2,
                    applyIdList: applyIdList,
                    checkerReason: checkerReason.value,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    tableRef.value.refresh()
                })
            tableRef.value.refresh()

            showRejec.value = false

            // applyIdList.value = []
        }

        watch(
            selectedRowsArr,
            (newValue) => {
                inductionApplyStore().setEmployedStaffList(newValue)
            },

            { deep: true },
        )
        return {
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            options,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量拒绝显示
            showRejec,
            // 点击批量拒绝
            rejectRow,
            //批量拒绝描述
            checkerReason,
            //批量拒绝确认
            refuseOrAdopt,
            // 批量通过
            // passRow,
            //新增
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            // 导出
            exportUrl,
            // 导出按钮
            exportData,
            //批量发起离职
            // 批量删除
            deleteRow,
            // 开具证明
            issueCertificat,

            clickStaffStatus,
            modalCancelInduction,
            showInduction,
            inductionValue,

            CertificatmodalClose,
            //线上上传证明
            confirmOnline,
            //线下上传证明
            confirmOffline,

            showCertificatModal,
            Title,

            showElectronicSeal,

            onlineAccessories,
            addIssueCertificate,
            servieType,
            issueModalCancel,
            showIssueCer,
            issueModalConfirm,
            roleId,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
