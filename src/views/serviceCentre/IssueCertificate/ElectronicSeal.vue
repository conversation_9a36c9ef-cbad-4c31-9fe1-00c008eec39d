<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" title="开具证明" width="1500px">
        <div class="personalInfo">
            <p>
                <span class="label">员工姓名:</span><span>{{ currentValue?.name }}</span>
            </p>
            <p>
                <span class="label">身份证号:</span><span>{{ currentValue?.certificateNum }}</span>
            </p>
            <p>
                <span class="label">联系方式:</span><span>{{ currentValue?.phone }}</span>
            </p>
        </div>
        <div class="modal-pdf">
            <div class="pdf-box">
                <PdfPreview v-if="pdfUrl" class="pdf" :pdfUrl="pdfUrl" :windowParams="formData" />
            </div>
            <div class="form-box">
                <p style="text-align: center; color: red">选择印章后请在左侧双击确认您要加盖的位置！</p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: '130px' } }"
                    :rules="rules"
                    style="margin-top: 20px"
                >
                    <template v-for="item in myOptions" :key="item">
                        <template v-if="item?.image">
                            <FormItem label="选择印章" name="seal" :rules="rules.seal">
                                <Select
                                    v-model:value="formData.seal"
                                    allowClear
                                    showSearch
                                    style="width: 200px"
                                    optionFilterProp="label"
                                    placeholder="请选择印章"
                                    :options="item.options"
                                    @change="sealUrlChange"
                                />
                            </FormItem>
                            <div class="pdf-btn-img">
                                <img class="img" v-if="formData.sealImg" :src="formData.sealImg" alt="" />
                                <div class="img" v-else>印章图片</div>
                            </div>
                        </template>
                        <MyFormItem
                            v-else
                            :width="item?.width ?? '50%'"
                            :item="item"
                            v-model:value="formData[item.name]"
                            :class="item.slots"
                        />
                        <!-- <div class="pdf-btn-img" v-if="item.img">
                            <img :src="formData[item.name]" alt="" />
                        </div> -->
                    </template>
                    <!-- <FormItem label="选择印章">
                        <Select
                            v-model:value="formData.sealUrl"
                            allowClear
                            showSearch
                            style="width: 200px"
                            optionFilterProp="label"
                            placeholder="请选择印章"
                            :options="sealUrlList"
                            @change="sealUrlChange"
                        />
                    </FormItem>
                    <div class="pdf-btn-img">
                        <img class="img" v-if="formData.sealUrl" :src="formData.sealUrl" alt="" />
                        <div class="img" v-else>印章图片</div>
                    </div> -->
                </Form>

                <div class="buttonBox">
                    <Button type="primary" class="pdf-btn" @click="fillClick">一键填入</Button>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { valuesAndRules } from '/#/component'
import { getValuesAndRules } from '/@/utils'
import request from '/@/utils/request'

export default defineComponent({
    name: 'InductionApplyElectronicSeal',
    components: {},
    props: {
        currentValue: {
            type: Object,
            default: () => {
                return {}
            },
        },
        visible: {
            type: Boolean,
            default: false,
        },
        onlineAccessories: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    emits: ['confirm', 'update:visible'],
    setup(props, { emit }) {
        // 获取印章
        const { onlineAccessories, currentValue, visible } = toRefs<any>(props)
        let sealUrlList = ref<LabelValueOptions>([])
        const signId = ref('')
        const sealUrlx = ref<number>(0)
        const sealUrly = ref<number>(0)
        const pageNo = ref<any>(NaN)
        const fillInfo = ref<any>([])
        const pdfUrl = ref<String>('')
        const fillClickRes = ref<any>({})
        const myOptions = ref<valuesAndRules[]>([])
        // Form 实例
        const formInline = ref(null) as any
        // Form Data
        const formData = ref<inObject>({})
        // FormData rules 初始值
        const rules = ref<inObject>({})

        watch(
            visible,
            () => {
                if (visible.value) {
                    let tempObj: any = {}
                    fillInfo.value = onlineAccessories.value?.fillInfo ? JSON.parse(onlineAccessories.value?.fillInfo) : []
                    myOptions.value = fillInfo.value?.map((item) => {
                        if (item.name == 'seal') {
                            const temp = sealUrlList.value?.find((el: any) => {
                                return el.signId == item.value
                            })
                            if (temp) {
                                tempObj = temp
                                sealUrlChange(temp.sealUrl, temp)
                                signId.value = temp.signId
                            }
                            return {
                                ...item,
                                options: item.options?.map((el) => {
                                    return {
                                        ...el,
                                        ...(sealUrlList.value?.find((ele: any) => {
                                            return el.value == ele.signId
                                        }) || {}),
                                    }
                                }),
                                default: item?.value || '',
                            }
                        } else return { ...item, default: item.value }
                    })
                    pdfUrl.value = onlineAccessories.value.appendixPath
                    const { values: initFormData, rules: initRules } = getValuesAndRules(myOptions.value)
                    formData.value = initFormData
                    formData.value.sealImg = tempObj?.sealUrl
                    rules.value = initRules
                }
            },
            { immediate: true },
        )

        onMounted(() => {
            function sealUrlPosition(e, page) {
                if (isNaN(page)) {
                    pageNo.value = NaN
                }
                sealUrlx.value = e.x / e.width
                sealUrly.value = 1 - e.y / e.height
                pageNo.value = parseInt(page)
            }
            ;(window as any).sealUrlPosition = sealUrlPosition
            request.get('/api/hr-sealses/selectsealses', {}).then((res) => {
                res.forEach((item) => {
                    if (item.sealUrl) {
                        sealUrlList.value.push({ label: item.sealName, value: item.signId, ...item })
                    }
                })
            })
        })
        onBeforeUnmount(() => {
            ;(window as any).sealUrlPosition = undefined
            ;(window as any).sealUrl = undefined
        })

        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInline.value
                    ?.validate([nameList])
                    .then((res) => {
                        return true
                    })
                    .catch((err) => {
                        return false
                    })
            })
        }

        const sealUrlChange = (item, object) => {
            if ((window as any).sealUrlDele) {
                ;(window as any).sealUrlDele()
            }

            if (item) {
                ;(window as any).sealUrl = object?.sealUrl
            } else {
                ;(window as any).sealUrl = undefined
            }
            signId.value = object?.signId
            formData.value.sealImg = object?.sealUrl || ''
            formValidateOptional('seal')
        }
        // 一键填入
        const fillClick = () => {
            request
                .post('/api/hr-certificate-issuances/make-template', {
                    templateId: onlineAccessories.value.appendixIds,
                    ...formData.value,
                    inputLabel: formData.value,
                })
                .then((res) => {
                    fillClickRes.value = res
                    pdfUrl.value = res.fileUrl
                    pageNo.value = NaN
                })
        }
        const cancel = () => {
            emit('update:visible', false)
        }
        const confirm = () => {
            if (!formData.value.seal) return
            if (isNaN(pageNo.value)) {
                message.error('请先确认印章的位置！')
                return
            }
            request
                .post(
                    '/api/hr-certificate-issuances/upload-document',
                    {
                        appendId: fillClickRes.value?.id || currentValue.value?.appendixId,
                        id: currentValue.value.id,
                        x: sealUrlx.value,
                        y: sealUrly.value,
                        signId: signId.value,
                        pageNo: pageNo.value,
                        fillInfo: JSON.stringify(formData.value),
                        fileUrl: fillClickRes.value?.fileUrl || currentValue.value?.appendixPath,
                    },
                    { loading: true },
                )
                .then((res) => {
                    console.log(res)
                    message.success('开具完成')
                    cancel()
                    emit('confirm')
                })
        }
        return {
            myOptions,
            formInline,
            fillInfo,
            pdfUrl,
            fillClick,
            rules,
            formData,
            sealUrlList,
            sealUrlChange,
            cancel,
            confirm,
        }
    },
})
</script>
<style scoped lang="less">
.personalInfo {
    display: flex;
    height: 30px;
    p {
        width: 33.33%;
        .label {
            width: 30%;
            display: inline-block;
            text-align: right;
        }
    }
}
.modal-pdf {
    display: flex;
    height: calc(75vh - 48px - 30px);
    .pdf-box {
        height: 100%;
        width: 50%;
        .pdf {
            height: 100%;
        }
    }
    .form-box {
        max-height: 75vh;
        width: 47%;
        border: 1px solid #d9d9d9;
        margin-left: 20px;
        overflow-y: auto;
        position: relative;
        .buttonBox {
            position: sticky;
            bottom: 0;
            background-color: #fff;
            z-index: 999;
        }
    }
    .pdf-btn {
        margin-left: 130px;
        margin-bottom: 30px;
    }
    .pdf-btn-img {
        margin-left: 130px;
        margin-bottom: 30px;
        .img {
            height: 150px;
            width: 150px;
            background-color: rgb(245, 245, 245);
            text-align: center;
            line-height: 150px;
        }
    }
}
.btn-bottom {
    text-align: right;
    margin-top: 20px;
}
</style>
