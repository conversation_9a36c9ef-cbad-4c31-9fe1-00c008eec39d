<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="cancel" :title="title" width="500px" :footer="null">
        <div class="content">
            <div class="down">
                <div class="title">线下签章</div>
                <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" class="enclosureBox">
                    <FormItem :rules="validateImportFile" label="附件" name="appendixIdList">
                        <ImportFile
                            v-model:fileUrls="formData.appendixIdList"
                            :count="1"
                            :multiple="false"
                            ref="refImportFile"
                            class="refImportFile"
                            listType="fileList"
                            @changeResponse="fileComplete"
                        />
                    </FormItem>
                    <Button type="primary" class="btn" @click="confirmOffline">开具完成线下拿取</Button>
                </Form>
            </div>
            <div class="text">可上传证明附件留存/供申请人下载备用</div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import request from '/@/utils/request'

import { defineComponent, ref, toRefs, nextTick } from 'vue'

export default defineComponent({
    name: 'CertificatModal11',
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['confirmOffline', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { currentValue } = toRefs<any>(props)

        const formData = ref({ appendixIdList: [] })
        const formInline = ref()

        const cancel = () => {
            formData.value.appendixIdList = []
            emit('cancel')
            emit('update:visible', false)
        }
        const refImportFile = ref()

        const validateImportFile = {
            required: true,
            trigger: ['change'],
            type: 'array',
            validator: async (rule: inObject, value: any) => {
                if (!value.length) {
                    return Promise.reject('请先上传附件')
                } else {
                    return Promise.resolve()
                }
            },
        }

        const fileComplete = (res) => {
            nextTick(() => {
                formInline.value?.validate(['appendixIdList'])
            })
        }
        const confirmOffline = () => {
            formInline.value
                .validate()
                .then(async () => {
                    let appendixIdList = refImportFile.value.getFileUrls().map((item) => {
                        return item.id
                    })
                    request
                        .post('/api/hr-certificate-issuances/issue-complete', {
                            applyIdList: [currentValue.value?.id],
                            appendixIdList: appendixIdList,
                        })
                        .then((res) => {
                            cancel()
                            emit('confirmOffline')
                        })
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        return {
            validateImportFile,
            fileComplete,
            formInline,
            formData,
            cancel,
            confirmOffline,
            refImportFile,
        }
    },
})
</script>
<style scoped lang="less">
.content {
    margin: 30px 10px;
    .down {
        width: 300px;
        margin: 0 auto;
        border-radius: 5px;
        height: 200px;
        border: 1px solid #ccc;
        padding-top: 0;
        border: 1px solid rgb(231, 229, 229);
        .btn {
            align-self: center;
        }
    }
}
.title {
    color: white;
    background: #618aed;
    width: 100%;
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
}
.text {
    font-size: 16px;
    text-align: center;
    line-height: 16px;
    margin-top: 15px;
}
.enclosureBox {
    margin-top: 20px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
</style>
