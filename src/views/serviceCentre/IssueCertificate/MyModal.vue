<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1500px" :footer="null">
        <div class="seeInfoBox">
            <div class="step-detail">
                <Steps :current="applyStep" labelPlacement="vertical" class="my_steps">
                    <Step title="员工发起申请" />
                    <Step title="专管员审核" />
                    <Step title="公司确认申请" v-if="item?.isNeed == 2" />
                    <Step title="经理审批" />
                    <Step title="证明开具" />
                    <Step title="开具完成" />
                    <Step title="证明开具结束" v-if="applyStep === 7" />
                </Steps>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>申请信息</span>
                <div class="examine-flex">
                    <div class="item-flex">
                        <span>员工姓名：</span>
                        <span>{{ detailData.name }}</span>
                    </div>
                    <div class="item-flex">
                        <span>身份证号：</span>
                        <span>{{ detailData.certificateNum }}</span>
                    </div>

                    <div class="item-flex">
                        <span>性别：</span>
                        <span>{{ detailData.sexLabel }}</span>
                    </div>
                    <div class="item-flex">
                        <span>联系方式：</span>
                        <span>{{ detailData.phone }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>岗位：</span>
                        <span>{{ detailData.professionName }}</span>
                    </div>

                    <div class="item-flex">
                        <span>基本工资：</span>
                        <span>{{ detailData.basicWage }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同开始日期：</span>
                        <span>{{ detailData.contractStartDate }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同结束日期：</span>
                        <span>{{ detailData.contractEndDate }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>社保基数：</span>
                        <span>{{ detailData.socialSecurityCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>医保基数：</span>
                        <span>{{ detailData.medicalInsuranceCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>公积金基数：</span>
                        <span>{{ detailData.accumulationFundCardinal }}</span>
                    </div>
                </div>
                <div class="leave">
                    <div>证明名称：</div>
                    <div>{{ detailData.certificateName }}</div>
                </div>
                <div class="leave">
                    <div>证明用途：</div>
                    <div>{{ detailData.certificatePurpose }}</div>
                </div>
                <div class="leave">
                    <div>证明文件/证明模板：</div>
                    <div class="hrAppendixListBox">
                        <Tooltip placement="top">
                            <template #title>
                                <span>{{ detailData?.originName }}</span>
                            </template>
                            <a class="enclosure" @click="downloadFil(detailData)">
                                <PaperClipOutlined />{{ detailData?.originName }}
                            </a>
                        </Tooltip>
                    </div>
                </div>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>操作信息</span>
                <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                    <div class="list-item">
                        <span style="width: 40px">{{ index + 1 }}</span>
                        <div class="item-flex">
                            <span>操作人：</span>
                            <span>{{ item.realName }}</span>
                        </div>
                        <div class="item-flex2">
                            <span>操作时间：</span>
                            <span>{{ item.createdDate }}</span>
                        </div>
                        <div class="item-flex3">
                            <div class="box1">操作信息：</div>
                            <div class="box2">
                                {{ item.message }}
                                <span v-if="item.remark != null">备注：</span>
                                <span>{{ item.remark }}</span>
                                <div v-if="item?.hrAppendixList?.length" style="display: inline-block">
                                    附：
                                    <div
                                        v-for="AppendixItem in item.hrAppendixList"
                                        :key="AppendixItem.id"
                                        class="hrAppendixListBox"
                                    >
                                        <Tooltip placement="top">
                                            <template #title>
                                                <span>{{ AppendixItem?.originName }}</span>
                                            </template>
                                            <a class="enclosure" @click="downloadFil(AppendixItem)"
                                                ><PaperClipOutlined />{{ AppendixItem?.originName }}</a
                                            >
                                        </Tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <Row :gutter="20">
            <Col span="10">
                <div class="examine" v-if="viewType == 'examine'">
                    <Divider type="vertical" class="divid" />
                    <span>备注</span>
                    <div class="examine-area">
                        <Textarea :rows="3" v-model:value="remark" placeholder="请输入" />
                    </div>
                </div>
            </Col>
            <Col span="10">
                <div v-if="viewType == 'examine'" class="examine">
                    <Divider type="vertical" class="divid" />
                    <span>拒绝理由</span>
                    <div class="examine-area">
                        <Textarea :rows="3" v-model:value="checkerReason" placeholder="若拒绝，请输入拒绝理由" />
                    </div>
                </div>
            </Col>
        </Row>

        <!-- 是否需要客户审核 v-if="item?.states == 1" -->
        <template v-if="viewType == 'examine' && detailData?.certificateStatus == 6">
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>是否需要客户审核</span>
            </div>
            <div class="examine-area" style="margin-left: 20px">
                <RadioGroup name="radioGroup" v-model:value="hasCustomAudit">
                    <Radio :value="true">需要</Radio>
                    <Radio :value="false">不需要</Radio>
                </RadioGroup>
            </div>
        </template>
        <!-- 需调整 -->
        <div class="ant-modal-footer" style="margin-top: 60px" v-if="viewType == 'examine'">
            <!-- <Button class="examine" danger type="primary" @click="refuseOrAdopt(2, 'customer')">客户拒绝</Button>
            <Button type="primary" @click="refuseOrAdopt(1, 'customer')">客户通过</Button> -->

            <Button class="examine" danger type="primary" @click="refuseOrAdopt(2)">拒绝</Button>
            <Button type="primary" class="successBtn" @click="refuseOrAdopt(1)">
                {{ hasCustomAudit && detailData?.certificateStatus == 6 ? '通知客户' : '通过' }}
            </Button>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
// import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { PaperClipOutlined } from '@ant-design/icons-vue'

import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { openNotification, previewFile } from '/@/utils'

export default defineComponent({
    name: 'IssueCertificateMyModal',
    components: { PaperClipOutlined },
    props: {
        roleId: Number,
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'examine', 'electrical', 'offline'].indexOf(value) !== -1
            },
        },
    },

    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { currentValue, visible, title, item, roleId } = toRefs<any>(props)

        const applyStep = ref(0)
        const detailData = ref<inObject>({})
        const opLogsList = ref<object[]>([])

        const izRefundSocialSecurity = ref<string>('1')
        const getDetail = (value) => {
            // certificateStatus: 0--员工发起申请
            // certificateStatus: 1--专管员审核
            // certificateStatus: 2--公司确认申请
            // certificateStatus: 3--经理审批
            // certificateStatus: 4--证明开具
            // certificateStatus: 5--开具完成
            // certificateStatus: 6--结束
            // <Step title="员工发起申请" />
            // <Step title="公司确认申请" />
            // <Step title="经理审批" />
            // <Step title="证明开具" />
            // <Step title="开具完成" />
            // <Step title="结束" />
            // 状态(0:待客户审核 1:客户审核拒绝 2:待经理审核 3:经理审核拒绝 4:待开具证明 5:证明开具完成 6:待专管员审核 7:专管员审核拒绝)
            const needClientStepArr = [2, 7, 3, 7, 4, 6, 1, 7]
            const withoutClientStepArr = [7, 7, 2, 7, 3, 5, 1, 7]
            request.get('api/hr-certificate-issuances', { id: value }).then((res) => {
                if (item.value?.isNeed == 2) applyStep.value = needClientStepArr[Number(res.certificateStatus)]
                else applyStep.value = withoutClientStepArr[Number(res.certificateStatus)]
                // applyStep.value = Number(res.certificateStatus)
                // if (res.certificateStatus == 2) {
                //     applyStep.value = Number(res.certificateStatus) - 1
                // }
                // if (res.certificateStatus == 3) {
                //     applyStep.value = Number(res.certificateStatus) - 1
                // }
                // if (res.certificateStatus == 4) {
                //     applyStep.value = Number(res.certificateStatus) - 2
                // }
                // if (res.certificateStatus == 5) {
                //     applyStep.value = Number(res.certificateStatus) - 1
                // }

                detailData.value = res
                opLogsList.value = res.applyOpLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        onMounted(() => {})
        watch(
            item,
            () => {
                if (item.value) {
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )

        // cancel handle
        const cancel = () => {
            hasCustomAudit.value = true
            checkerReason.value = ''
            remark.value = ''
            emit('cancel')
            emit('update:visible', false)
        }

        const downloadFil = (item) => {
            previewFile(item.fileUrl)
        }
        const checkerReason = ref('')
        const remark = ref('')
        const hasCustomAudit = ref(true)
        // 确认弹窗
        const refuseOrAdopt = (type) => {
            if (type == 2) {
                if (!checkerReason.value) {
                    message.warning('请填写拒绝理由')
                    return false
                }
            }
            // let applyIdList = selectedRowsArr.value.map((el: inObject) => {
            //     return el.id
            // })
            let api = '/api/hr-certificate-issuances/batch-pass'
            // if (examineName == 'manager') {
            //     api = '/api/hr-certificate-issuances/manager-review'
            // } else {
            //     api = '/hr-certificate-issuances/manager-review'
            // }

            // 客户PC端审核通过或者拒绝   /hr-certificate-issuances/customer-review
            // 专管员通过或者拒绝  /hr-certificate-issuances/manager-review
            if (detailData.value.certificateStatus == 6) {
                request
                    .post('/api/hr-certificate-issuances/reviewed-special-supervisor', {
                        applyId: detailData.value.id,
                        opt: type != 2,
                        isNeed: hasCustomAudit.value ? 2 : 1,
                        checkerReason: checkerReason.value,
                        remark: remark.value,
                    })
                    .then((res) => {
                        message.success(type != 2 ? (hasCustomAudit.value ? '已通知客户' : '审核已通过') : '审核已拒绝')
                        cancel()
                        emit('confirm')
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                request
                    .post(api, {
                        roleId: roleId.value,
                        opt: type != 2,
                        applyIdList: [detailData.value.id],
                        checkerReason: checkerReason.value,
                        remark: remark.value,
                    })
                    .then((res) => {
                        let tip = ''
                        let success = '您选择的数据已修改成功'
                        if (res.error_status) {
                            tip = res.error_status
                            success = ',选择的其它数据已修改成功'
                        }
                        if (res.success?.length) {
                            tip += success
                        }
                        openNotification(tip)
                        cancel()
                        emit('confirm')
                    })
            }
        }

        return {
            opLogsList,
            confirm,
            cancel,
            detailData,
            checkerReason,
            applyStep,
            izRefundSocialSecurity,
            refuseOrAdopt,
            downloadFil,
            hasCustomAudit,
            remark,
        }
    },
})
</script>
<style scoped lang="less">
.successBtn {
    background-color: @upload-color;
    border: none;
}
.step-detail {
    margin-bottom: 30px;
}
.examine {
    margin: 50px 0px 0px;
    .span {
        padding-left: 10px;
    }
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 150px;
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;

        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.leave {
    padding-left: 20px;
    margin: 5px 0px;
    display: flex;
}
.file {
    // padding-left: 15px;
    padding: 20px;
    display: flex;
    span {
        padding-right: 15px;
    }
}
.hrAppendixListBox {
    display: inline-block;
    padding-right: 10px;
    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
