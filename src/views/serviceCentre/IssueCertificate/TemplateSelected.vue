<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        :title="'选择模板'"
        @cancel="templateCancel"
        @confirm="templateConfirm"
        width="1200px"
    >
        <SearchBar v-model="params" :options="searchOption" @change="searchData" />
        <div class="btns">
            <Upload :showUploadList="false" :beforeUpload="beforeUpload" >
                <Button @click="changUpload">暂无所需模板，上传附件</Button>
            </Upload>
            <div class="files">
                <span class="item" v-for="(i, idx) in applicationList.enclosures" :key="i.id">
                    <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                    <span @click="removeFile(idx)">x</span>
                </span>
            </div>
        </div>
        <BasicTable
            ref="tableRef"
            :params="{ ...params }"
            :checkboxProps="checkboxProps"
            @selectedRowsArr="funSelectedRowsArr"
            rowSelectionType="radio"
            api="api/hr-proof-templates/page"
            :columns="columns"
        />
        <template #footer>
            <div>
                <Button @click="templateCancel" class="btn">取消</Button>
                <Button @click="templateConfirm" type="primary" class="btn">确定</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, onMounted } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { SearchBarOption } from '/#/component'
import { message } from 'ant-design-vue'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils'
export default defineComponent({
    name: 'AddStaff',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        servieType: {
            type: String,
            default: '',
        },
        selectedRow: {
            type: Object,
        },
    },
    emits: ['cancel', 'confirm', 'selectedRow', 'update:visible'],
    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }

        let templateTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('certificateTemplateType', '')
                .then((data: inObject[]) => {
                    templateTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const applicationList = ref<any>({
            enclosures: [],
        })
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const params = ref<inObject>({})
        const selectedRowsArr = ref<any[]>([])
        const funSelectedRowsArr = (selected) => {
            if (selected?.length) {
                // hasItBeenSelected.value = true
                selectedRowsArr.value = selected
            }
        }
        const beforeUpload = async (file) => {
            const res = await uploadFile(file)
            applicationList.value.enclosures.push(res)
            return false
        }
        const checkDefault = ref(false)
        const changUpload = () => {
            checkDefault.value = true
        }
        const checkboxProps = (record: inObject) => {
            let defaultChecked = false
            defaultChecked = record.appendixId == selectedRowsArr.value[0]?.appendixId
            return {
                defaultChecked: defaultChecked,
                id: record.id + '',
            }
        }
        const columns = [
            {
                title: '模板标题',
                dataIndex: 'title',
                align: 'center',
                width: 250,
            },
            {
                title: '模板类型',
                dataIndex: 'templateType',
                align: 'center',
                width: 120,
                customRender: ({ record }) => {
                    return templateTypeList.value.find((el) => {
                        return el.value == record.templateType
                    })?.label
                },
            },
        ]
        const searchOption: SearchBarOption[] = [
            {
                type: 'string',
                label: '模板标题',
                key: 'title',
            },
        ]
        const templateCancel = () => {
            selectedRowsArr.value = []
            applicationList.value.enclosures = []
            emit('update:visible', false)
            emit('cancel', true)
        }

        const templateConfirm = () => {
            console.log(applicationList.value?.enclosures)
            if (applicationList.value?.enclosures.length) {
                console.log('applicationList', applicationList.value)
                emit('confirm', applicationList.value?.enclosures)
                templateCancel()
            } else if (selectedRowsArr.value?.length) {
                emit('confirm', selectedRowsArr.value[0])
                templateCancel()
            } else {
                message.error('请选择模板')
            }
        }
        const removeFile = (idx) => {
            applicationList.value.enclosures.splice(idx, 1)
        }
        return {
            confirm,
            columns,
            templateCancel,
            templateConfirm,
            funSelectedRowsArr,
            checkboxProps,
            params,
            searchOption,
            searchData,
            tableRef,
            beforeUpload,
            previewFile,
            applicationList,
            removeFile,
            changUpload,
        }
    },
})
</script>
<style scoped lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
.btn {
    text-align: right;
    margin: 10px;
    button {
        margin-left: 10px;
    }
}
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
:deep(.add-table) {
    border: 1px solid #f0f0f0;
    border-top: none;
    padding: 10px;
    text-align: center;
    color: #999;
}
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
