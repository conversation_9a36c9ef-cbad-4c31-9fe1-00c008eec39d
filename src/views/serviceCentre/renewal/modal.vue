<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'" :footer="null">
        <StepBar :stepsData="stepsData" :currentStep="currentStep" labelPlacement="vertical" />
        <ApplicationInfo :detailData="item" />
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" v-if="item?.applyOpLogsList?.length" />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
import StepBar from '../retirement/component/StepBar.vue'
import ApplicationInfo from './component/ApplicationInfo.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'
export default defineComponent({
    name: 'RenewalModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item } = toRefs(props)
        const currentStep = ref(0)
        const stepsData = ref<any>([])
        const steps = [
            {
                title: '续签通知',
            },
            {
                title: '等待员工续签',
            },
            {
                title: '续签待审核',
            },
            {
                title: '续签成功',
            },
            {
                title: '已结束',
            },
        ]
        watch(
            item,
            (newV, oldV) => {
                if (newV) {
                    // 1 等待员工续签 2 续签待审核 3 续签失败 4 续签成功
                    console.log('newV.renewalProcess', newV.renewalProcess)
                    if (newV.renewalProcess == 4) stepsData.value = steps.slice(0, steps.length - 1)
                    else stepsData.value = steps
                    const stateArr = [1, 2, 5, 4]
                    currentStep.value = stateArr[newV.renewalProcess - 1] || 1
                }
            },
            { deep: true },
        )

        // cancel handle
        const onCancel = () => {
            emit('cancel')
        }

        return {
            currentStep,
            onCancel,
            stepsData,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
.rejectBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
.successBtn {
    background-color: @upload-color;
    border: none;
}
</style>
