<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportDocument }}</Button>
        <Button type="primary" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'renewal_batchConfirm'" @click="showSealModal()">{{ getConfirmDynamicText() }}</Button>
        <Button type="primary" @click="batchRenew" v-auth="'staff_renewal_list'">{{ getRenewDynamicText() }}</Button>
        <Button type="primary" v-auth="'renewal_updataStatus'" @click="updataStatus()">修改续签状态</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-talent-staffs/renewal-page"
        exportUrl="/api/hr-talent-staffs/renewal/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal :visible="modalVisible" :item="currentValue" :title="modalTitle" @cancel="cancelHandle" />
    <!-- 续签 -->
    <RenewalComponent
        v-model:visible="renewalVisible"
        :currentValue="currentValue"
        @confirmOffline="renewalOfflineConfirm"
        @confirmOnline="renewalOnlineConfirm"
    />
    <!-- 审核 -->
    <ConfirmSign
        :visible="showConfirmSign"
        :title="modalTitle"
        viewType="renewal"
        :item="currentValue"
        @cancel="confirmSignCancel"
        @confirm="confirmSignConfirm"
    />
    <ElectricSign :visible="showSign" title="批量续签" type="multiple" @cancel="signCancel" @confirm="signConfirm" />
    <!-- 选择印章 -->
    <BasicEditModalSlot :visible="sealModalVisible" @cancel="onSealCancel" @confirm="onSealConfirm" title="选择印章">
        <div class="examine">
            <FormItem label="选择印章">
                <Select
                    v-model:value="formData.seal"
                    allowClear
                    showSearch
                    style="width: 200px"
                    optionFilterProp="label"
                    placeholder="请选择印章"
                    :options="sealsList"
                    @change="sealUrlChange"
                />
            </FormItem>
            <div class="pdf-btn-img">
                <img class="img" v-if="formData.sealImg" :src="formData.sealImg" alt="" />
                <div class="img" v-else>印章图片</div>
            </div>
        </div>
    </BasicEditModalSlot>

    <!-- 修改员工状态 -->
    <BasicEditModalSlot
        :visible="showUpdataModal"
        title="修改员工续签状态"
        @cancel="updataStatusCancel"
        @ok="updataStatusConfirm"
        width="400px"
    >
        <div class="statusTip">修改员工合同状态会导致已经完成签订的劳动合同作废，此操作不可回撤，慎用！</div>
        <Form ref="statusForm" :model="formData2" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem
                label="员工状态"
                name="employeesStatus"
                :rules="{ required: true, message: '请选择员工状态', trigger: ['change', 'blur'] }"
            >
                <Select
                    v-model:value="formData2.employeesStatus"
                    placeholder="员工续签状态"
                    :disabled="true"
                    :options="employeesOptions"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { ref, defineComponent, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, getDynamicText, isEmptyNull } from '/@/utils/index'
import { SearchBarOption } from '/#/component'
import { sexList, renewalStateOptions } from '/@/utils/dictionaries'

import modal from './modal.vue'
import renewal from '../../staff/staff/renewal/index.vue'
import confirmSign from '/@/views/serviceCentre/inductionServices/stayInductionStaff/signContract/confirmSign.vue'
import ElectricSign from '../../staff/staff/renewal/electricSign.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { useRoute } from 'vue-router'
import downFile, { downMultFile } from '/@/utils/downFile'
import staffStore from '/@/store/modules/staff'
import signStore from '/@/store/modules/electricSign'
export default defineComponent({
    name: 'renewal',
    components: {
        MyModal: modal,
        RenewalComponent: renewal,
        ConfirmSign: confirmSign,
        ElectricSign,
    },
    setup() {
        const body = document.body
        // 筛选
        let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        let staffStatesList = ref<LabelValueOptions>([]) // 员工状态
        //表格数据
        let columns = ref([
            {
                title: '系统编号',
                dataIndex: 'systemNum',
                width: 150,
                align: 'center',
            },
            {
                title: '档案编号',
                dataIndex: 'archivesNum',
                width: 150,
                align: 'center',
            },
            {
                title: '姓名',
                dataIndex: 'name',
                width: 100,
                align: 'center',
            },
            {
                title: '性别',
                dataIndex: 'sex',
                width: 70,
                align: 'center',
                customRender: ({ record }) => {
                    return sexList.find((el) => {
                        return record.sex == el.value
                    })?.label
                },
            },
            {
                title: '出生日期',
                dataIndex: 'birthday',
                width: 180,
                align: 'center',
            },
            {
                title: '年龄',
                dataIndex: 'age',
                width: 100,
                align: 'center',
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                width: 180,
                align: 'center',
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '续签状态',
                dataIndex: 'renewalProcess',
                align: 'center',
                width: 135,
                customRender: ({ record }) => {
                    return renewalStateOptions.find((el) => {
                        return record.renewalProcess == el.value
                    })?.label
                },
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                width: 180,
                align: 'center',
            },
            {
                title: '一级客户',
                dataIndex: 'oneClientName',
                align: 'center',
                width: 150,
                sorter: false,
            },
            {
                title: '二级客户',
                dataIndex: 'twoClientName',
                align: 'center',
                width: 150,
                sorter: false,
            },
            {
                title: '状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 150,
                sorter: false,
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return staffTypeList.value.find((el) => {
                        return record.personnelType == el.value
                    })?.label
                },
            },
            {
                title: '合同开始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 130,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 130,
                customRender: ({ record }) => {
                    return record.contractEndDate || '/'
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ])

        let options: SearchBarOption[] = [
            {
                type: 'string',
                label: '系统编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '档案编号',
                key: 'archivesNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                multiple: true,
                options: sexList,
            },
            {
                type: 'date',
                label: '出生日期',
                key: 'birthday',
                allowClear: true,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '手机号码',
                key: 'phone',
            },
            {
                type: 'select',
                label: '续签状态',
                key: 'renewalProcess',
                options: renewalStateOptions,
            },
            {
                label: '客户名称',
                key: 'clientIds',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '状态',
                key: 'staffStatusList',
                options: staffStatesList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '合同开始日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '合同结束日期',
                key: 'contractEndDateQuery',
            },
        ]

        onMounted(() => {
            // 人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 员工状态
            dictionaryDataStore()
                .setDictionaryData('staffStates', '')
                .then((data: inObject[]) => {
                    let arr: any = []
                    arr = data.filter((item) => {
                        return item.itemName != '离职' && item.itemName != '退休'
                    })
                    console.log('arrr', arr)
                    staffStatesList.value = arr
                })
        })
        //表格dom
        const tableRef = ref()
        //筛选
        const route = useRoute()
        const params = ref({
            renewalProcessList: route.query?.statusList ? JSON.parse(route.query?.statusList as string) : undefined,
        })

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const modalTitle = ref('')
        const renewalVisible = ref(false)
        const modalVisible = ref(false)
        const showConfirmSign = ref(false)
        const detailType = ref('')

        // 发起电签
        const showSign = ref(false)
        // 发起电签确认弹窗
        const signConfirm = async () => {
            showSign.value = false
            tableRef.value.refresh()
        }
        // 关闭发起电签弹窗
        const signCancel = () => {
            showSign.value = false
        }

        // 当前编辑的数据
        const currentValue = ref<any>({})

        // 显示弹窗
        const showModal = async (record, type) => {
            await getDetails(record.id)
            switch (type) {
                case 'look':
                    modalTitle.value = '查看'
                    modalVisible.value = true
                    break
                case 'approve':
                    modalTitle.value = '审核'
                    showConfirmSign.value = true
                    break
                case 'again':
                    modalTitle.value = '重新续签'
                    renewalVisible.value = true
                    break
            }
            detailType.value = type
        }

        const confirmSignCancel = () => {
            showConfirmSign.value = false
        }
        const confirmSignConfirm = () => {
            confirmSignCancel()
            tableRef.value.refresh()
        }

        // 获取详细信息
        const getDetails = async (id) => {
            await request
                .get(`/api/hr-talent-staffs/renewal-info?id=${id}`)
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        sexLabel: sexList.find((el) => {
                            return res.sex == el.value
                        })?.label,
                        status: res.status,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = () => {
            modalVisible.value = false
        }

        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selectedRowsArr.value)
        })

        const exportDocument = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        const exportData = () => {
            tableRef.value.exportRow('ids', exportDocument.value, params.value)
        }
        // 批量导出
        const batchExport = async () => {
            let resList: any[] = []
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            let body = {}
            let ids: any[] = []
            if (exportText.value.indexOf('选中') != -1) {
                selectedRowsArr.value.forEach((item: inObject) => {
                    ids.push(item.id)
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            resList = await request.post('/api/hr-talent-staffs/export-renewal', body)
            let urls: string[] = []
            let urlsName: string[] = []
            resList.forEach((item) => {
                if (item.hrContractAppendixList.length) {
                    item.hrContractAppendixList.forEach((element) => {
                        urls.push(element.appendixPath)
                        urlsName.push(item.staffName + '_' + element.name)
                    })
                }
            })
            if (urls.length > 0) {
                request.post(
                    '/api/sys-oper-logs/download',
                    {
                        title: '合同',
                        operDetail: '续签合同下载:' + urlsName.join(),
                        fileUrl: urls.join(),
                    },
                    { loading: false },
                )
                if (urls.length == 1) {
                    downFile('get', urls[0], urlsName[0], {})
                } else if (urls.length > 1) {
                    downMultFile(resList.length > 1 ? '电子合同批量导出' : `${resList[0].staffName}电子合同`, urls, urlsName)
                }
            } else {
                message.error('您选择的人员暂无电子合同可下载!')
            }
        }
        const tableData = ref([])
        const getConfirmDynamicText = () => {
            let exportStr = ''
            if (
                Object.values(params.value).filter((el) => {
                    if (Array.isArray(el)) return el.length > 0
                    else return !isEmptyNull(el) && el !== ''
                }).length > 0
            ) {
                exportStr = '确认筛选内容'
                if (selectedRowsArr.value.length > 0) exportStr = '确认选中内容'
            } else if (selectedRowsArr.value.length > 0) exportStr = '确认选中内容'
            else exportStr = '批量确认'
            return exportStr
        }
        const getRenewDynamicText = () => {
            let exportStr = ''
            if (
                Object.values(params.value).filter((el) => {
                    if (Array.isArray(el)) return el.length > 0
                    else return !isEmptyNull(el) && el !== ''
                }).length > 0
            ) {
                exportStr = '续签筛选内容'
                if (selectedRowsArr.value.length > 0) exportStr = '续签选中内容'
            } else if (selectedRowsArr.value.length > 0) exportStr = '续签选中内容'
            else exportStr = '批量续签'
            return exportStr
        }
        // 批量续签
        const batchRenew = async () => {
            if (selectedRowsArr.value.length == 0) {
                Modal.warning({
                    title: '提示',
                    content: '至少选择一条数据',
                })
                return
            }

            try {
                let res: any = null
                if (!tableData.value.length) {
                    message.error('未查询到相关数据!')
                    return
                }
                if (getRenewDynamicText().indexOf('选中') != -1) {
                    res = await request.post('/api/hr-talent-staff/not-page', {
                        ids: selectedRowsArr.value.map((item: any) => {
                            return item.id
                        }),
                        distinguish: 1,
                    })
                } else if (getRenewDynamicText().indexOf('筛选') != -1) {
                    res = await request.post('/api/hr-talent-staff/not-page', { ...params.value, distinguish: 1 })
                } else {
                    res = await request.post('/api/hr-talent-staff/not-page', { distinguish: 1 })
                }
                console.log(res)

                if (res) {
                    staffStore().setStaffList(res)
                    signStore().setClientId(res?.[0]?.clientId || '')
                    signStore().setTabs('3')
                    showSign.value = true
                }
            } catch (error) {
                console.log(error)
            }
        }
        // 印章弹窗
        const sealModalVisible = ref(false)
        const sealsList = ref([])
        const formData = ref({
            seal: '16754324',
            sealImg: 'https://hr-server.hdqhr.com:10443/minio/hrresources/253bbd7f844b4f02b9186e696db629df.png',
        })
        const showSealModal = () => {
            if (selectedRowsArr.value.length == 0) {
                Modal.warning({
                    title: '提示',
                    content: '至少选择一条数据',
                })
                return
            }
            sealModalVisible.value = true
        }
        const onSealCancel = () => {
            sealModalVisible.value = false
            formData.value = { seal: '', sealImg: '' }
        }
        const onSealConfirm = () => {
            if (!formData.value.seal) {
                message.warning('请先选择印章！')
                return
            }
            batchConfirm()
        }
        const getSealsList = async () => {
            const list = await request.get('/api/hr-sealses/list')
            sealsList.value = list.map((el) => {
                return { ...el, value: el.signId, label: el.sealName }
            })
        }

        getSealsList()
        const sealUrlChange = (val, option) => {
            formData.value.sealImg = option.sealUrl
        }
        // 批量确认
        const batchConfirm = async () => {
            try {
                let res: any = null
                if (!tableData.value.length) {
                    message.error('未查询到相关数据!')
                    return
                }
                if (getConfirmDynamicText().indexOf('选中') != -1) {
                    res = await request.post('/api/hr-talent-staff/not-page', {
                        ids: selectedRowsArr.value.map((item: any) => {
                            return item.id
                        }),
                        distinguish: 2,
                    })
                } else if (getConfirmDynamicText().indexOf('筛选') != -1) {
                    res = await request.post('/api/hr-talent-staff/not-page', { ...params.value, distinguish: 2 })
                } else {
                    res = await request.post('/api/hr-talent-staff/not-page', { distinguish: 2 })
                }
                if (res) {
                    const confirmRes = await request.post('/api/hr-talent-staff/batch-renewal-confirm', {
                        clientId: res?.[0].clientId || '',
                        staffIds: res?.map((el) => el.id) || [],
                        signId: formData.value.seal,
                    })
                    message.success(confirmRes)
                    searchData()
                }
            } catch (error) {
                console.log(error)
            }
        }
        // 线下
        const renewalOfflineConfirm = () => {
            tableRef.value.refresh()
        }
        // 线上
        const renewalOnlineConfirm = () => {
            tableRef.value.refresh()
        }

        // 打开续签弹窗
        const actionRenewal = (record) => {
            renewalVisible.value = true
            currentValue.value = record
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'renewal_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '续签',
                    auth: 'staff_renewal',
                    show: (record) => {
                        return (
                            (record.renewalProcess === 0 || (record.renewalProcess === 4 && record.isRenewalContract === 1)) &&
                            record.staffStatus != 5 &&
                            record.staffStatus != 6
                        )
                    },
                    click: actionRenewal,
                },
                {
                    neme: '审核',
                    auth: 'renewal_approve',
                    show: (record) => {
                        return record.renewalProcess == 2
                    },
                    click: (record) => showModal(record, 'approve'),
                },
                {
                    neme: '重新续签',
                    auth: 'renewal_again',
                    show: (record) => {
                        return record.renewalProcess == 3
                    },
                    click: (record) => showModal(record, 'again'),
                },
            ]),
        )

        // 修改员工状态
        const statusForm = ref()
        const formData2 = ref({
            employeesStatus: '2',
        })
        const showUpdataModal = ref(false)
        const updataStatus = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先选择员工!')
                return
            }
            showUpdataModal.value = true
        }
        const updataStatusCancel = () => {
            showUpdataModal.value = false
            restUpdataModal()
        }
        const restUpdataModal = () => {
            statusForm.value?.resetFields()
            formData2.value.employeesStatus = '2'
        }
        const updataStatusConfirm = () => {
            statusForm.value
                .validate()
                .then(() => {
                    let staffIds = selectedRowsArr.value?.map((el: any) => el.id)
                    request
                        .post(
                            `/api/hr-talent-staffs/manual_update_staff_induction_process/${formData2.value.employeesStatus}`,
                            staffIds,
                        )
                        .then((res) => {
                            if (res) {
                                showUpdataModal.value = false
                                restUpdataModal()
                            }
                        })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const employeesOptions = ref<inObject[]>([
            { label: '入职待发合同', value: '1' },
            { label: '续签待发合同', value: '2' },
        ])

        return {
            sealUrlChange,
            formData,
            sealsList,
            sealModalVisible,
            showSealModal,
            onSealConfirm,
            onSealCancel,
            showSign,
            signCancel,
            signConfirm,
            getConfirmDynamicText,
            showConfirmSign,
            tableData,
            renewalVisible,
            exportText,
            exportDocument,
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            currentValue,
            detailType,
            //操作
            myOperation,
            selectedRowsArr,
            // 事件
            searchData,
            cancelHandle,

            batchExport,
            batchConfirm,
            renewalOfflineConfirm,
            renewalOnlineConfirm,
            confirmSignCancel,
            confirmSignConfirm,
            batchRenew,
            getRenewDynamicText,

            // 修改员状态
            showUpdataModal,
            updataStatus,
            body,
            updataStatusConfirm,
            formData2,
            employeesOptions,
            statusForm,
            updataStatusCancel,
            exportData,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.hidden {
    display: none;
}
.examine {
    position: relative;
    padding: 20px;
    box-sizing: border-box;
    .pdf-btn-img {
        margin-left: 130px;
        margin-bottom: 30px;
        .img {
            height: 150px;
            width: 150px;
            background-color: rgb(245, 245, 245);
            text-align: center;
            line-height: 150px;
        }
    }
}
.statusTip {
    color: red;
    text-align: center;
    font-size: 16px;
    width: 100%;
    margin-bottom: 20px;
}
</style>
