<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>员工信息</span>
        <div class="examine-flex">
            <div class="item-flex">
                <span class="label">客户编号：</span>
                <span>{{ detailData?.unitNumber }}</span>
            </div>
            <div class="item-flex">
                <span class="label">客户名称：</span>
                <span>{{ detailData?.clientName }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">协议编号：</span>
                <span>{{ detailData?.agreementNumber }}</span>
            </div>
            <div class="item-flex">
                <span class="label">协议标题：</span>
                <span>{{ detailData?.agreementTitle }}</span>
            </div>
            <div class="item-flex">
                <span class="label">协议类型：</span>
                <span>{{ detailData?.agreementTypeLabel }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">员工姓名：</span>
                <span>{{ detailData?.name }}</span>
            </div>
            <div class="item-flex">
                <span class="label">身份证号：</span>
                <span>{{ detailData?.certificateNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">性别：</span>
                <span>{{ detailData?.sexLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">联系方式：</span>
                <span>{{ detailData?.phone }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">岗位：</span>
                <span>{{ detailData?.professionName }}</span>
            </div>
            <div class="item-flex">
                <span class="label">人员类型：</span>
                <span>{{ detailData?.personnelTypeLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">续签合同开始日期：</span>
                <span>{{ detailData?.contractStartDateRenewal }}</span>
            </div>
            <div class="item-flex">
                <span class="label">续签合同结束日期：</span>
                <span>{{ detailData?.contractEndDateRenewal }}</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'ApplicationInfo',
    props: {
        detailData: {
            type: Object,
            default: () => {},
        },
    },
    setup() {
        return {}
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
