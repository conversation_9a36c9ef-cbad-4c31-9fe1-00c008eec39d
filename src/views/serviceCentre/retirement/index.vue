<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #professionIdList="itemForm">
            <PostTree
                v-model:value="params.professionIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="addRetirement">新增</Button>
        <Button type="primary" v-auth="'retirement_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'retirement_receive'" @click="batchReceived()" class="downloadBtn">批量收到资料</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-retires/page"
        :exportUrl="'/api/hr-retires/export'"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        :visible="modalVisible"
        :modalType="detailType"
        :item="currentValue"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <!-- 退休 -->
    <NoticModal
        :visible="noticModalVisible"
        :item="currentValue"
        :serviceType="'退休'"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <AddModal :visible="showAddModal" :title="modalTitle" :servieType="serviceType" @confirm="addConfirm" @cancel="addCancel" />
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, onBeforeMount, computed } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import NoticModal from '/@/views/serviceCentre/industrialInjury/noticModal.vue'
import permissionStore from '/@/store/modules/permission'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils/index'
import { SearchBarOption } from '/#/component'
import { sexList, staffStatus } from '/@/utils/dictionaries'
import AddModal from './component/AddModal.vue'

import modal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { useRoute } from 'vue-router'
export default defineComponent({
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'Retirement',
    components: {
        MyModal: modal,
        PostTree,
        NoticModal,
        AddModal,
    },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false

        // 筛选
        let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        let statusTypeList = ref<LabelValueOptions>([]) // 状态
        let specializedList = ref<LabelValueOptions>([]) // 专管员

        //表格数据
        let columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                width: 180,
                align: 'center',
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                width: 100,
                align: 'center',
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                width: 180,
                align: 'center',
            },
            {
                title: '性别',
                dataIndex: 'sex',
                width: 70,
                align: 'center',
                customRender: ({ record }) => {
                    return sexList.find((el) => {
                        return record.sex == el.value
                    })?.label
                },
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                width: 130,
                align: 'center',
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return staffTypeList.value.find((el) => {
                        return record.personnelType == el.value
                    })?.label
                },
            },
            {
                title: '申请退休日期',
                dataIndex: 'applyRetireDate',
                align: 'center',
                width: 130,
            },
            {
                title: '申请描述',
                dataIndex: 'applyDescription',
                align: 'center',
                width: 200,
                ellipsis: true,
            },
            {
                title: '原退休日期',
                dataIndex: 'originalRetireDate',
                align: 'center',
                width: 130,
            },
            {
                title: '实际退休日期',
                dataIndex: 'actualRetireDate',
                align: 'center',
                width: 130,
                customRender: ({ record }) => {
                    return record.actualRetireDate || '/'
                },
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                width: 135,
                customRender: ({ record }) => {
                    return statusTypeList.value.find((el) => {
                        return record.status == el.value
                    })?.label
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 130,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ])

        let options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                multiple: true,
                options: sexList,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位名称',
                key: 'professionIdList',
                placeholder: '岗位名称',
                maxTag: '0',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '原退休日期',
                key: 'originalRetireDateList',
            },
            {
                type: 'daterange',
                label: '实际退休日期',
                key: 'actualRetireDateList',
            },
            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                options: statusTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'specializedList',
                options: specializedList,
                multiple: true,
            },
        ]

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = options.findIndex((el) => {
                    return el.key == 'specializedList'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'specialized'
                })
                options.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        onMounted(() => {
            // 人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        console.log('item', item)
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 状态
            dictionaryDataStore()
                .setDictionaryData('retireStatus', '')
                .then((data: inObject[]) => {
                    statusTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 专管员
            dictionaryDataStore()
                .setDictionaryData('Specialized', '/api/users/getSpecialized')
                .then((data: inObject[]) => {
                    specializedList.value = data.map((item) => {
                        return { label: item.realName, value: item.id }
                    })
                })
        })
        //表格dom
        const tableRef = ref()
        //筛选
        const route = useRoute()
        const params = ref<Recordable>({
            statusList: route.query?.statusList || route.query?.type ? [5]: undefined,
            staffStatusList: route.query?.type ? [4, 12] : undefined,
            personnelTypeList: route.query?.type ? [1, 2] : undefined,
        })

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const showAddModal = ref(false)
        const serviceType = ref<any>('')
        const addRetirement = () => {
            showAddModal.value = true
            modalTitle.value = '新增'
        }
        const addConfirm = () => {
            showAddModal.value = false
            tableRef.value.refresh(1)
        }
        const addCancel = () => {
            showAddModal.value = false
        }
        const modalTitle = ref('')
        const modalVisible = ref(false)
        const noticModalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<any>(null)

        // 显示弹窗
        const showModal = (record, type) => {
            if (type == 'notice' || type == 'launch') {
                noticModalVisible.value = true
                modalTitle.value = type == 'notice' ? '退休通知' : '发起退休'
            } else {
                modalVisible.value = true
                switch (type) {
                    case 'look':
                        modalTitle.value = '查看'
                        break
                    case 'supplement':
                        modalTitle.value = '补充资料'
                        break
                    case 'time':
                        modalTitle.value = '更新退休时间'
                        break
                    case 'retire':
                        modalTitle.value = '确认退休完成'
                        break
                }
            }
            getDetails(record.id, type == 'notice' ? true : false)
            detailType.value = type
        }

        // 获取详细信息
        const getDetails = (id, isNotice) => {
            request
                .post('/api/hr-retires/detail', { id: id })
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        sexLabel: sexList.find((el) => {
                            return res.sex == el.value
                        })?.label,
                        isNotice: isNotice,
                        status: isNotice ? 0 : res.status,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = () => {
            modalVisible.value = false
            noticModalVisible.value = false
        }

        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // 批量收到资料
        const batchReceived = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请选择员工!')
                return
            }
            request
                .post('/api/hr-retires/update', selectedRowsArr.value)
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const executeOperation = (obj) => {
            let msg = ''
            let params: inObject = { ...obj }
            if (detailType.value == 'supplement') {
                msg = '已通知员工补充资料'
                params = { ...obj, status: 9 }
            } else if (detailType.value == 'launch') {
                request
                    .post('/api/hr-retires', {
                        id: obj.id,
                        staffId: obj.staffId,
                        materialList: obj.materialList,
                    })
                    .then(() => {
                        tableRef.value.refresh()
                        cancelHandle()
                        message.success('发起退休成功')
                    })
                    .catch((err) => {
                        console.log(err)
                    })
                return
            } else {
                switch (obj.status) {
                    case 1:
                        msg = '已收到资料'
                        break
                    case 2:
                        msg = '已完善退休时间'
                        break
                    case 3:
                        msg = '退休办理完成'
                        break
                    case 0:
                        msg = '已通知员工准备退休相关资料'
                        break
                }
            }
            request
                .put('/api/hr-retires', params)
                .then((res) => {
                    tableRef.value.refresh()
                    cancelHandle()
                    message.success(msg)
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'retirement_look',
                    show: (record) => {
                        return record.status != 5
                    },
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '发起退休',
                    auth: 'retirement_launch',
                    show: (record) => {
                        return record.status == 5
                    },
                    click: (record) => showModal(record, 'launch'),
                },
                {
                    neme: '退休通知',
                    auth: 'retirement_notice',
                    show: (record) => {
                        return record.status == 4
                    },
                    click: (record) => showModal(record, 'notice'),
                },
                {
                    neme: '收到资料',
                    auth: 'staff_affirm_info',
                    show: (record) => {
                        return record.status == 0
                    },
                    click: (record) =>
                        executeOperation({
                            id: record.id,
                            name: record.name,
                            status: Number(record.status + 1),
                            staffId: record.staffId,
                            clientId: record.clientId,
                        }),
                },
                {
                    neme: '补充资料',
                    auth: 'retirement_append',
                    show: (record) => {
                        return record.status == 1
                    },
                    click: (record) => showModal(record, 'supplement'),
                },
                {
                    neme: '完善时间',
                    auth: 'retirement_time',
                    show: (record) => {
                        return record.status == 1
                    },
                    click: (record) => showModal(record, 'time'),
                },
                {
                    neme: '确认退休',
                    auth: 'retirement_confirm',
                    show: (record) => {
                        return record.status == 2
                    },
                    click: (record) => showModal(record, 'retire'),
                },
            ]),
        )
        return {
            exportText,
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            noticModalVisible,
            currentValue,
            detailType,
            //操作
            myOperation,
            selectedRowsArr,
            // 事件
            searchData,
            executeOperation,
            cancelHandle,

            batchExport,
            batchReceived,
            addRetirement,
            serviceType,
            showAddModal,
            addConfirm,
            addCancel,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.hidden {
    display: none;
}
</style>
