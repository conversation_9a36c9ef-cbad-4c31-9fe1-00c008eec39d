<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>操作信息</span>
        <div class="examine-wrap">
            <p class="linefeed"></p>
            <template v-for="(item, index) in applyOpLogs" :key="'operation' + index">
                <div class="operation_item">
                    <span class="operation_index">{{ index + 1 }}</span>
                    <div class="item-flex">
                        <span class="box1">操作人：</span>
                        <span class="box2">{{ item.realName || '系统' }}</span>
                    </div>
                    <div class="item-flex2">
                        <span class="box1">操作时间：</span>
                        <span>{{ item.createdDate }}</span>
                    </div>
                    <div class="item-flex3">
                        <div class="box1">操作信息：</div>
                        <div class="box2">
                            {{
                                `${getMessage(item.message)}${item?.hrAppendixList && item.hrAppendixList.length ? ' 附：' : ''}`
                            }}
                            <span v-show="item.remark != null">备注</span>
                            <span>{{ item.remark }}</span>
                            <template v-if="item?.hrAppendixList && item?.hrAppendixList.length">
                                <span v-for="ele in item.hrAppendixList" :key="ele.id">
                                    <a href="javascript: void(0)" @click="previewFile(ele.fileUrl)" style="margin-right: 10px">
                                        {{ ele.originName }}
                                    </a>
                                </span>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue'
import { previewFile } from '/@/utils'
export default defineComponent({
    name: 'OperationInfo',
    props: {
        applyOpLogs: {
            type: Array,
            default: () => [],
        },
    },
    setup(props) {
        const getMessage = (msg) => {
            return msg.split('####')[0]
        }
        return {
            previewFile,
            getMessage,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    .operation_item {
        padding: 10px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        font-size: 14px;
        .operation_index {
            font-weight: 600;
            width: 30px;
        }
        .item-flex {
            display: flex;
            width: 220px;
            .box1 {
                float: left;
                width: 58px;
                font-weight: 600;
            }
            .box2 {
                float: right;
                width: 142px;
                padding-right: 4px;
            }
        }
        .item-flex2 {
            display: flex;
            width: 250px;
            .box1 {
                float: left;
                width: 70px;
                font-weight: 600;
            }
        }
        .item-flex3 {
            display: flex;
            width: 700px;
            .box1 {
                float: left;
                width: 70px;
                font-weight: 600;
            }
            .box2 {
                float: right;
                width: 530px;
            }
        }
    }
}
</style>
