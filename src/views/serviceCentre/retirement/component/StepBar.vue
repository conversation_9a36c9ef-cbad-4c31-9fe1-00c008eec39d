<template>
    <div class="my_steps">
        <Steps :current="currentStep" v-bind="$attrs">
            <template v-for="(item, index) in stepsData" :key="'step' + index">
                <Step
                    :title="item.title"
                    :sub-title="item.subTitle"
                    :disabled="item.disabled"
                    :status="item.status"
                    :description="item.description"
                />
            </template>
        </Steps>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'StepBar',
    props: {
        stepsData: {
            type: Array,
            default: () => [],
        },
        currentStep: Number,
    },
    setup() {
        return {}
    },
})
</script>

<style scoped lang="less">
.my_steps {
    margin-bottom: 30px;
}
//step标题行高
:deep(.ant-steps-item-title) {
    line-height: 20px;
}
</style>
