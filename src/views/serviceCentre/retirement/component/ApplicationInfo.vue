<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>{{title}}</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">员工姓名：</span>
                <span>{{ detailData?.name }}</span>
            </div>
            <div class="item-flex">
                <span class="label">身份证号：</span>
                <span>{{ detailData?.certificateNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">性别：</span>
                <span>{{ detailData?.sexLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">联系方式：</span>
                <span>{{ detailData?.phone }}</span>
            </div>
            <div class="item-flex">
                <span class="label">岗位：</span>
                <span>{{ detailData?.professionName }}</span>
            </div>
            <div class="item-flex" v-if="showBasicWage">
                <span class="label">基本工资：</span>
                <span>{{ detailData?.basicWage }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同开始日期：</span>
                <span>{{ detailData?.contractStartDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同结束日期：</span>
                <span>{{ detailData?.contractEndDate }}</span>
            </div>
            <slot :detailData="detailData" name="inner"></slot>
        </div>
        <slot :detailData="detailData" name="outer"></slot>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'ApplicationInfo',
    props: {
        detailData: {
            type: Object,
            default: () => {},
        },
        showBasicWage: {
            type: Boolean,
            default: true,
        },
        title: {
            type:String,
            default: '申请信息',
            required: false,
        }
    },
    setup() {
        return {}
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
