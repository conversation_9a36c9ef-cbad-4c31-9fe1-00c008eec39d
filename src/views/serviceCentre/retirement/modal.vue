<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" :currentStep="item?.status == 3 ? 4 : item?.status" labelPlacement="vertical" />
        <ApplicationInfo :detailData="item">
            <template #inner="{ detailData }">
                <div class="item-flex">
                    <span class="label">社保基数：</span>
                    <span>{{ detailData?.socialSecurityCardinal }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">医保基数：</span>
                    <span>{{ detailData?.medicalInsuranceCardinal }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">公积金基数：</span>
                    <span>{{ detailData?.accumulationFundCardinal }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">原退休日期：</span> <span>{{ detailData?.originalRetireDate }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">实际退休日期：</span> <span>{{ detailData?.actualRetireDate }}</span>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" v-if="item?.applyOpLogsList.length" />
        <div class="examine" v-if="modalType !== 'look'">
            <Divider type="vertical" class="divid" />
            <span>操作</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: modalType == 'retire' ? '80px' : '110px' } }"
                    :rules="rules"
                    class="form-flex"
                >
                    <!-- 补充资料 -->
                    <template v-if="modalType == 'supplement'">
                        <FormItem name="operateCont" style="width: 100%">
                            <Textarea
                                v-model:value="formData.operateCont"
                                :rows="3"
                                allowClear
                                placeholder="请输入员工补充的资料"
                            />
                        </FormItem>
                    </template>
                    <!-- 完善时间 -->
                    <template v-if="modalType == 'time'">
                        <FormItem label="实际退休日期" name="actualRetireDate" style="width: 100%">
                            <DatePicker
                                v-model:value="formData.actualRetireDate"
                                format="YYYY-MM-DD"
                                placeholder="请选择实际退休日期"
                                valueFormat="YYYY-MM-DD"
                                :getPopupContainer="
                                    () => {
                                        return document.body
                                    }
                                "
                            />
                        </FormItem>
                    </template>
                    <!-- 确定退休 || 完善时间 -->
                    <template v-if="modalType == 'time' || modalType == 'retire'">
                        <FormItem label="附件" name="appendixIds" style="width: 100%">
                            <ImportFile v-model:fileUrls="formData.appendixIds" ref="refImportFile" />
                        </FormItem>
                        <FormItem label="备注" name="remark" style="width: 100%">
                            <Textarea v-model:value="formData.remark" :rows="3" allowClear placeholder="请输入备注" />
                        </FormItem>
                    </template>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="modalType !== 'look'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import StepBar from './component/StepBar.vue'
import ApplicationInfo from './component/ApplicationInfo.vue'
import OperationInfo from './component/OperationInfo.vue'
import { valuesAndRules } from '/#/component'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'RetireModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, modalType } = toRefs(props)
        const refImportFile = ref()
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '实际退休日期',
                name: 'actualRetireDate',
                required: true,
            },
            {
                label: '补充资料',
                name: 'operateCont',
                required: false,
                default: '',
            },
            {
                label: '附件',
                name: 'appendixIds',
                required: false,
                default: [],
                ruleType: 'array',
            },
            {
                label: '备注',
                name: 'remark',
                required: false,
                default: '',
            },
        ])
        const stepsData = ref([
            {
                title: '通知员工准备资料',
            },
            {
                title: '线下审档',
            },
            {
                title: '办理退休手续',
            },
            {
                title: '退休办理完成',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // cancel handle
        const onCancel = () => {
            modalType.value !== 'look' && resetFormData()
            emit('cancel')
        }
        // confirm handle
        const onConfirm = () => {
            let myUrl = []
            if (formData.value.appendixIds.length) {
                myUrl = refImportFile.value.getFileUrls().map((item) => {
                    return item.id
                })
            }

            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (modalType.value == 'supplement') {
                        if (isEmpty(formData.value.operateCont)) {
                            message.warn('请输入补充资料')
                        } else {
                            emit('confirm', {
                                ...formData.value,
                                id: item.value.id,
                                status: Number(item.value.status + 1),
                                name: item.value.name,
                                appendixIds: myUrl,
                                staffId: item.value.staffId,
                                clientId: item.value.clientId,
                            })
                            resetFormData()
                        }
                    } else {
                        emit('confirm', {
                            ...formData.value,
                            id: item.value.id,
                            status: Number(item.value.status + 1),
                            name: item.value.name,
                            appendixIds: myUrl,
                            staffId: item.value.staffId,
                            clientId: item.value.clientId,
                        })
                        resetFormData()
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            refImportFile,
            formInline,
            stepsData,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
