<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" labelPlacement="vertical" :currentStep="getCurrentStep()" />
        <ApplicationInfo :detailData="item">
            <template #inner="{ detailData }">
                <div class="item-flex-inner">
                    <span class="label">社保基数：</span>
                    <span>{{ detailData?.socialSecurityCardinal }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">医保基数：</span>
                    <span>{{ detailData?.medicalInsuranceCardinal }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">公积金基数：</span>
                    <span>{{ detailData?.accumulationFundCardinal }}</span>
                </div>
                <div class="item-flex-inner">
                    <span class="label">停工留薪期：</span>
                    <span>{{
                        `${
                            detailData?.workStoppageStartDate
                                ? detailData?.workStoppageStartDate + ' 至 ' + (detailData?.workStoppageEndDate || '~')
                                : ''
                        }`
                    }}</span>
                </div>
            </template>
            <template #outer="{ detailData }">
                <div class="item-flex-outer">
                    <span class="label">受伤日期：</span> <span>{{ detailData?.injuryDate }}</span>
                </div>
                <div class="item-flex-outer">
                    <span class="label">伤情描述：</span> <span>{{ detailData?.injuryDescription }}</span>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :applyOpLogs="item?.applyOpLogsList" />
        <div class="examine" v-if="modalType !== 'look'">
            <Divider type="vertical" class="divid" />
            <span>{{ footTitle }}</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{
                        style: {
                            width:
                                modalType == 'identify_results' || modalType == 'downtime' || modalType == 'work_injury'
                                    ? '130px'
                                    : '80px',
                        },
                    }"
                    :rules="rules"
                    class="form-flex"
                >
                    <!-- 补充资料 -->
                    <template v-if="modalType == 'supplement'">
                        <FormItem name="juryMessage" style="width: 100%">
                            <Textarea
                                v-model:value="formData.juryMessage"
                                :rows="3"
                                allowClear
                                placeholder="请输入员工补充的资料"
                            />
                        </FormItem>
                    </template>
                    <!-- 鉴定结果 | 停工留薪期 | 认定结果 -->
                    <template
                        v-else-if="
                            modalType == 'confirm' ||
                            modalType == 'downtime' ||
                            modalType == 'identify_results' ||
                            modalType == 'affirm_results'
                        "
                    >
                        <template v-if="modalType == 'affirm_results'">
                            <FormItem label="是否工伤" name="date" style="width: 100%">
                                <RadioGroup v-model:value="formData.isIndustrialInjury" :options="injuryOptions" />
                            </FormItem>
                        </template>
                        <template v-else-if="modalType == 'confirm'">
                            <FormItem label="申报日期" name="declareDate" style="width: 100%">
                                <DatePicker
                                    v-model:value="formData.declareDate"
                                    format="YYYY-MM-DD"
                                    placeholder="请选择您为员工进行申报的日期"
                                    valueFormat="YYYY-MM-DD"
                                    style="width: 250px"
                                    :getPopupContainer="
                                        () => {
                                            return document.body
                                        }
                                    "
                                />
                            </FormItem>
                        </template>
                        <template v-else>
                            <div style="display: flex; width: 100%">
                                <FormItem label="停工留薪开始日期" name="workStoppageStartDate" style="width: 100%">
                                    <DatePicker
                                        v-model:value="formData.workStoppageStartDate"
                                        format="YYYY-MM-DD"
                                        placeholder="请选择开始日期"
                                        valueFormat="YYYY-MM-DD"
                                        :disabled="modalType == 'identify_results'"
                                        :disabled-date="startDisabledDate"
                                        @change="changeDate"
                                        :getPopupContainer="
                                            () => {
                                                return document.body
                                            }
                                        "
                                    />
                                </FormItem>
                                <FormItem label="停工留薪结束日期" name="workStoppageEndDate" style="width: 100%">
                                    <DatePicker
                                        v-model:value="formData.workStoppageEndDate"
                                        format="YYYY-MM-DD"
                                        placeholder="请选择结束日期"
                                        valueFormat="YYYY-MM-DD"
                                        :disabled-date="identifyDisabledDate"
                                        :getPopupContainer="
                                            () => {
                                                return document.body
                                            }
                                        "
                                    />
                                </FormItem>
                            </div>
                        </template>
                        <FormItem label="附件" name="appendixIds" style="width: 100%">
                            <ImportFile v-model:fileUrls="formData.appendixIds" ref="refImportFile" />
                        </FormItem>
                        <FormItem label="说明" name="juryMessage" style="width: 100%">
                            <Textarea v-model:value="formData.juryMessage" :rows="3" allowClear placeholder="请输入说明" />
                        </FormItem>
                    </template>
                    <!-- 审核 -->
                    <template v-else-if="modalType == 'audit'">
                        <FormItem name="juryMessage" style="width: 100%">
                            <Textarea
                                v-model:value="formData.juryMessage"
                                :rows="3"
                                allowClear
                                placeholder="若拒绝，请输入拒绝理由"
                            />
                        </FormItem>
                    </template>
                    <template v-if="modalType == 'audit'">
                        <Divider type="vertical" class="divid" />
                        <span>备注</span>
                        <div class="examine-wrap" style="width: 100%;">
                            <FormItem name="juryMessage" style="width: 100%">
                                <Textarea v-model:value="formData.remark" :rows="3" allowClear placeholder="请输入备注" />
                            </FormItem>
                        </div>
                    </template>
                    <!-- 工伤返岗 -->
                    <template v-else-if="modalType == 'work_injury'">
                        <FormItem :rules="validateDate" label="员工到岗情况" name="work_injury_approach" style="width: 100%">
                            <RadioGroup v-model:value="formData.work_injury_approach" @change="approachChange">
                                <Radio value="0">已按时到岗</Radio>
                                <Radio value="1">旷工</Radio>
                                <Radio value="2" v-if="item.appraisalStatus == 0">更新停工留薪期</Radio>
                            </RadioGroup>
                            <DatePicker
                                style="width: 250px"
                                v-model:value="formData.workStoppageEndDate"
                                @change="formValidateOptional('work_injury_approach')"
                                format="YYYY-MM-DD"
                                placeholder="请选择停工留薪结束日期"
                                valueFormat="YYYY-MM-DD"
                                v-if="dateVisible"
                                :disabled-date="disabledDate"
                                :getPopupContainer="
                                    () => {
                                        return document.body
                                    }
                                "
                            />
                        </FormItem>
                    </template>
                    <!-- 旷工返岗 -->
                    <template v-else-if="modalType == 'absenteeism'">
                        <FormItem label="返岗日期" name="returnDate" style="width: 100%">
                            <DatePicker
                                style="width: 250px"
                                v-model:value="formData.returnDate"
                                format="YYYY-MM-DD"
                                placeholder="请选择员工返岗日期"
                                valueFormat="YYYY-MM-DD"
                                :getPopupContainer="
                                    () => {
                                        return document.body
                                    }
                                "
                            />
                        </FormItem>
                    </template>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="modalType == 'audit'">
                <Button @click="onCancel(true)" class="delBtn">拒绝</Button>
                <Button @click="onConfirm" type="primary" class="btn">通过</Button>
            </div>
            <div v-else-if="modalType !== 'look'">
                <Button @click="onCancel(false)" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import ApplicationInfo from '../retirement/component/ApplicationInfo.vue'
import OperationInfo from '../retirement/component/OperationInfo.vue'
import { valuesAndRules } from '/#/component'
import { message } from 'ant-design-vue'
import moment, { Moment } from 'moment'
export default defineComponent({
    name: 'IndustrialInjuryModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, item, modalType } = toRefs(props)
        const injuryOptions = ref([
            {
                label: '是',
                value: '0',
            },
            {
                label: '否',
                value: '1',
            },
        ])
        const refImportFile = ref()
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '是否工伤',
                name: 'isIndustrialInjury',
                required: false,
                default: '0',
            },
            {
                label: '附件',
                name: 'appendixIds',
                default: [],
                ruleType: 'array',
                required: false,
            },
            {
                label: '停工留薪开始日期',
                name: 'workStoppageStartDate',
                required: true,
            },
            {
                label: '停工留薪结束日期',
                name: 'workStoppageEndDate',
                required: true,
            },
            {
                label: '申报日期',
                name: 'declareDate',
                required: true,
            },
            {
                label: '说明',
                name: 'juryMessage',
                required: false,
            },
            {
                label: '员工到岗情况',
                name: 'work_injury_approach',
                default: '0',
                required: false,
            },
            {
                label: '返岗日期',
                name: 'returnDate',
                required: true,
            },
        ])
        const stepsData = ref([
            {
                title: '员工发起申请',
            },
            {
                title: '公司确认工伤认定申请',
            },
            {
                title: '准备材料',
            },
            {
                title: '开始办理工伤认定',
            },
            {
                title: '工伤认定完成',
            },
            {
                title: '确认停工留薪期',
            },
            {
                title: '员工返岗情况',
            },
        ])

        let footTitle = ref('')

        const dateVisible = ref(false)
        const approachChange = ({ target: { value } }) => {
            if (value == '2') dateVisible.value = true
            else dateVisible.value = false
        }

        const disabledDate = (current: Moment) => {
            // Can not select days before today and today
            return current && current < moment().endOf('day')
        }

        const startDisabledDate = (endValue: Moment) => {
            return new Date(item.value?.injuryDate).valueOf() >= endValue.endOf('day').valueOf()
        }

        const changeDate = () => {
            formData.value.workStoppageEndDate = undefined
        }

        const identifyDisabledDate = (endValue: Moment) => {
            return new Date(formData.value?.workStoppageStartDate).valueOf() >= endValue.endOf('day').valueOf()
        }

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(
            modalType,
            (val) => {
                if (val) {
                    if (val == 'identify_results') {
                        if (item.value.workStoppageStartDate) {
                            formData.value.workStoppageStartDate = item.value.workStoppageStartDate
                        }
                    }
                    if (val == 'supplement' || val == 'identify_results' || val == 'downtime') footTitle.value = '操作'
                    else if (val == 'audit') footTitle.value = '审核信息'
                    else if (val == 'work_injury' || val == 'absenteeism') footTitle.value = '审批'
                    else footTitle.value = '操作'
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )
        watch(
            item,
            (val) => {
                if (val) {
                    if (modalType.value == 'identify_results') {
                        if (val.workStoppageStartDate) {
                            formData.value.workStoppageStartDate = val.workStoppageStartDate
                        }
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        // cancel handle
        const onCancel = (flag = false) => {
            if (flag) {
                if (isEmpty(formData.value.juryMessage)) {
                    message.warn('请先输入拒绝理由')
                } else {
                    emit('cancel', true, { juryMessage: formData.value.juryMessage })
                    resetFormData()
                }
            } else {
                emit('cancel')
                resetFormData()
            }
        }
        const formValidateOptional = (name: string) => {
            nextTick(() => {
                formInline.value?.validate(name)
            })
        }
        const validateDate = {
            required: true,
            trigger: 'change',
            validator: () => {
                let formDataItem = formData.value?.work_injury_approach
                if (formDataItem == '2') {
                    if (!formData.value?.workStoppageEndDate) {
                        return Promise.reject('请选择停工留薪结束时间')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.resolve()
                }
            },
        }

        const getCurrentStep = () => {
            if (item.value.status == 16 || item.value.status == 7 || item.value.status == 14 || item.value.status == 17) return 6
            else if (item.value.status == 4 || item.value.status == 5) return 3
            else if (item.value.status == 12) return 4
            else if (item.value.status == 6) return 5
            else if (item.value.status == 13) return 1
            else return item.value.status
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            modalType.value !== 'look' && formInline.value.resetFields()
        }

        const getStatus = () => {
            if (formData.value.isIndustrialInjury == '0') {
                if (modalType.value == 'affirm_results') return 6
                else if (modalType.value == 'work_injury') {
                    if (formData.value.work_injury_approach == '0') return 9
                    else if (formData.value.work_injury_approach == '1') return 10
                    else return 11
                } else if (modalType.value == 'absenteeism') return 9
                else return Number(item.value.status + 1)
            } else {
                return 0
            }
        }

        // confirm handle
        const onConfirm = () => {
            let myUrl = []
            if (formData.value.appendixIds.length) {
                myUrl = refImportFile.value.getFileUrls().map((item) => {
                    return item.id
                })
            }
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (modalType.value == 'supplement') {
                        if (isEmpty(formData.value.juryMessage)) {
                            message.warn('请输入补充资料')
                        } else {
                            emit(
                                'confirm',
                                {
                                    ...formData.value,
                                    appendixIds: myUrl,
                                },
                                getStatus(),
                            )
                            resetFormData()
                        }
                    } else if (modalType.value == 'identify_results') {
                        emit(
                            'confirm',
                            {
                                ...formData.value,
                                appendixIds: myUrl,
                            },
                            100,
                        )
                        resetFormData()
                    } else if (modalType.value == 'confirm') {
                        emit(
                            'confirm',
                            {
                                ...formData.value,
                                appendixIds: myUrl,
                            },
                            99,
                        )
                        resetFormData()
                    } else {
                        emit(
                            'confirm',
                            {
                                ...formData.value,
                                appendixIds: myUrl,
                            },
                            getStatus(),
                        )
                        resetFormData()
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            injuryOptions,
            refImportFile,
            approachChange,
            dateVisible,
            myOptions,
            formInline,
            stepsData,
            footTitle,

            validateDate,
            formValidateOptional,
            getCurrentStep,
            disabledDate,
            identifyDisabledDate,
            startDisabledDate,
            changeDate,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
        padding-bottom: 20px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 5px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex-inner {
    width: 25%;
    margin: 5px 0px;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.item-flex-outer {
    margin: 5px 0px;
    padding-left: 15px;
    display: flex;
    .label {
        width: 75px;
        color: rgba(153, 153, 153, 1);
    }
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
