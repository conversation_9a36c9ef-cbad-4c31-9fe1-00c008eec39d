<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1000px'">
        <BasicTable
            ref="tableRef"
            :api="'/api/hr-materials/page'"
            :params="params"
            :columns="columns"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        />
        <template #footer>
            <Button @click="onConfirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, onMounted, watch } from 'vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'NoticeModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        serviceType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, serviceType } = toRefs(props)

        const tableRef = ref()
        const params = ref<inObject>({})
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('serviceType', '')
                .then((data: inObject[]) => {
                    params.value = {
                        typeList: [
                            data.find((item) => {
                                return item.itemName == serviceType.value
                            })?.itemValue,
                        ],
                    }
                })
        })
        // 多选
        const selectedRowsArr = ref([])
        //表格数据
        const columns = ref([
            {
                title: '名称',
                dataIndex: 'name',
                align: 'center',
            },
        ])
        // cancel handle
        const onCancel = () => {
            tableRef.value.checkboxReset()
            emit('cancel')
        }
        // confirm handle
        const onConfirm = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请选择所需材料!')
                return
            }
            emit('confirm', {
                ...item.value,
                materialList: selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                }),
            })
            tableRef.value.checkboxReset()
        }

        return {
            tableRef,
            selectedRowsArr,
            params,
            columns,
            onCancel,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
//
</style>
