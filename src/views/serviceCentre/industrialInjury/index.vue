<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #professionIdList="itemForm">
            <PostTree
                v-model:value="params.professionIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="addIndustrialInjury">新增</Button>
        <Button type="primary" v-auth="'industrialInjury_batch_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'industrialInjury_batch_pass'" @click="batchPassRow()">批量通过</Button>
        <Button type="primary" class="rejectBtn" v-auth="'industrialInjury_batch_reject'" @click="batchReject()">
            批量拒绝
        </Button>
        <Button
            type="primary"
            @click="batchInjuryNotice({}, 'multiple_notice')"
            v-auth="'industrialInjury_batch_notice'"
            class="downloadBtn"
        >
            批量工伤通知
        </Button>
        <Button
            type="primary"
            @click="batchReceived(null, 'multiple')"
            v-auth="'industrialInjury_batch_receive'"
            class="downloadBtn"
        >
            批量收到资料
        </Button>
        <Button
            type="primary"
            @click="batchInjuryReturn(null, 'multiple')"
            v-auth="'industrialInjury_batch_return'"
            class="downloadBtn"
        >
            批量返岗通知
        </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-work-injuries/page"
        deleteApi="/api/hr-work-injuries/deletes"
        :exportUrl="'/api/hr-work-injuries/export'"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="rejectCancelHandle"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
    <NoticModal
        :visible="noticModalVisible"
        :serviceType="'工伤'"
        :title="modalTitle"
        @confirm="injuryNoticRow"
        @cancel="cancelHandle"
    />
    <MyModal
        :visible="modalVisible"
        :item="currentValue"
        :modalType="detailType"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
    <AddModal :visible="showIndustrial" :title="modalTitle" @cancel="addIndustrialCancel" @confirm="addIndustrialConfirm" />
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, onBeforeMount, computed } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import permissionStore from '/@/store/modules/permission'
import { SearchBarOption } from '/#/component'
import { sexList, staffStatus } from '/@/utils/dictionaries'
import AddModal from './addModal.vue'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils/index'

import modal from './modal.vue'
import NoticModal from './noticModal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { useRoute } from 'vue-router'
export default defineComponent({
    name: 'IndustrialInjury',
    components: {
        MyModal: modal,
        NoticModal,
        PostTree,
        AddModal,
    },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false

        // 筛选
        let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        let statusTypeList = ref<LabelValueOptions>([]) // 状态
        let specializedList = ref<LabelValueOptions>([]) // 专管员

        let options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位名称',
                key: 'professionIdList',
                placeholder: '岗位名称',
                maxTag: '0',
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '受伤日期',
                key: 'injuryDateList',
            },
            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                options: statusTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '停工留薪开始时间',
                key: 'workStoppageStartDateList',
            },
            {
                type: 'daterange',
                label: '停工留薪结束时间',
                key: 'workStoppageEndDateList',
            },
            {
                type: 'select',
                label: '专管员',
                key: 'specializedList',
                options: specializedList,
                multiple: true,
            },
        ]

        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                width: 180,
                align: 'center',
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 110,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 130,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return staffTypeList.value.find((el) => {
                        return record.personnelType == el.value
                    })?.label
                },
            },
            {
                title: '受伤日期',
                dataIndex: 'injuryDate',
                align: 'center',
                width: 100,
            },
            {
                title: '申报日期',
                dataIndex: 'declareDate',
                align: 'center',
                width: 100,
            },
            {
                title: '伤情描述',
                dataIndex: 'injuryDescription',
                align: 'center',
                width: 160,
                ellipsis: true,
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    if (record.status == 5) return '待完成工伤认定'
                    else if (record.status == 16 || record.status == 17) return '休假中'
                    else if (record.status == 14) return '待客户确定返岗情况'
                    else
                        return statusTypeList.value.find((el) => {
                            return record.status == el.value
                        })?.label
                },
            },
            {
                title: '停工留薪期',
                dataIndex: 'workStoppageStartDate',
                align: 'center',
                width: 180,
                customRender: ({ record }) => {
                    if (record.workStoppageStartDate)
                        return `${record.workStoppageStartDate}至${record?.workStoppageEndDate || '~'}`
                    else return ''
                },
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 130,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ])

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = options.findIndex((el) => {
                    return el.key == 'specializedList'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'specialized'
                })
                options.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        onMounted(() => {
            // 人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 状态
            dictionaryDataStore()
                .setDictionaryData('workInjuryType', '')
                .then((data: inObject[]) => {
                    statusTypeList.value = data
                        .filter((el) => {
                            return el.itemValue !== 5 && el.itemValue !== 0 && el.itemValue !== 14
                        })
                        .map((item) => {
                            if (item.itemValue == 4) return { label: '待完成工伤认定', value: item.itemValue }
                            else return { label: item.itemName, value: item.itemValue }
                        })
                })
            // 专管员
            dictionaryDataStore()
                .setDictionaryData('Specialized', '/api/users/getSpecialized')
                .then((data: inObject[]) => {
                    specializedList.value = data.map((item) => {
                        return { label: item.realName, value: item.id }
                    })
                })
        })
        /**
         * 新增工伤认定服务
         */
        const showIndustrial = ref<any>(false)

        const addIndustrialInjury = () => {
            showIndustrial.value = true
            modalTitle.value = '新增工伤认定服务'
        }

        const addIndustrialCancel = () => {
            showIndustrial.value = false
        }
        const addIndustrialConfirm = () => {
            showIndustrial.value = false
            tableRef.value.refresh(1)
        }
        //表格dom
        const tableRef = ref()

        const route = useRoute()
        //筛选
        const params = ref({
            injuryDateList: route.query?.injuryDateList || undefined,
            statusList: route.query?.statusList ? JSON.parse(route.query?.statusList as string) : undefined,
        })

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const rejectCancelHandle = () => {
            showReject.value = false
            modalVisible.value = false
            checkerReason.value = ''
        }

        const modalTitle = ref('')
        const modalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<inObject>({})

        // 显示弹窗
        const showModal = (record, type) => {
            switch (type) {
                case 'look':
                    modalVisible.value = true
                    modalTitle.value = '查看'
                    break
                case 'supplement':
                    modalVisible.value = true
                    modalTitle.value = '补充资料'
                    break
                case 'confirm':
                    modalVisible.value = true
                    modalTitle.value = '申报确认'
                    break
                case 'affirm_results':
                    modalVisible.value = true
                    modalTitle.value = '工伤认定结果'
                    break
                case 'downtime':
                    modalVisible.value = true
                    modalTitle.value = '确认停工留薪期'
                    break
                case 'identify_results':
                    modalVisible.value = true
                    modalTitle.value = '更新鉴定结果'
                    break
                case 'audit':
                    modalVisible.value = true
                    modalTitle.value = '审核'
                    break
                case 'work_injury':
                    modalVisible.value = true
                    modalTitle.value = '确认到岗情况'
                    break
                case 'absenteeism':
                    modalVisible.value = true
                    modalTitle.value = '确认到岗情况'
                    break
            }
            getDetails(record.id)
            detailType.value = type
        }

        // 获取详细信息
        const getDetails = (id) => {
            request
                .post('/api/hr-work-injuries/detail', { id: id })
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        sexLabel: sexList.find((el) => {
                            return res.sex == el.value
                        })?.label,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = (flag = false, obj?) => {
            if (flag) {
                rejectRow('single', obj)
            }
            modalVisible.value = false
            noticModalVisible.value = false
        }

        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // 批量通过
        const batchPassRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请选择员工!')
                return
            }
            request
                .post(`/api/hr-work-injuries/update?status=2`, selectedRowsArr.value)
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '所选数据审核已通过'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已审核通过成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const noticModalVisible = ref(false)
        // 批量工伤通知
        const batchInjuryNotice = (obj, type) => {
            if (type == 'multiple_notice') {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请选择员工!')
                    return
                }
            }
            currentValue.value = obj
            detailType.value = type
            modalVisible.value = false
            modalTitle.value = '工伤通知'
            noticModalVisible.value = true
        }
        const injuryNoticRow = (obj) => {
            let selectRows: inObject[] = []
            if (detailType.value == 'single_notice') {
                selectRows = [{ ...currentValue.value, ...obj }]
            } else {
                selectRows = selectedRowsArr.value.map((item: inObject) => {
                    return { ...item, ...obj }
                })
            }
            request
                .post(`/api/hr-work-injuries/update?status=3`, selectRows)
                .then((res) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '已通知员工准备工伤相关资料'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已拒绝成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 批量返岗通知
        const batchInjuryReturn = (obj, type) => {
            if (type !== 'single') {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请选择员工!')
                    return
                }
            }
            let selectRows: inObject[] = []
            if (type == 'single') {
                selectRows = [{ ...obj }]
            } else {
                selectRows = selectedRowsArr.value
            }
            request
                .post(`/api/hr-work-injuries/update?status=8`, selectRows)
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '已通知所选员工返岗'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他员工已成功通知'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 批量收到资料
        const batchReceived = (obj, type) => {
            if (type !== 'single') {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请选择员工!')
                    return
                }
            }
            let selectRows: inObject[] = []
            if (type == 'single') {
                selectRows = [{ ...obj }]
            } else {
                selectRows = selectedRowsArr.value
            }
            if (
                !selectRows.every((el: inObject) => {
                    return el.status == 3
                })
            ) {
                openNotification(
                    `选择的数据中${selectRows
                        .filter((el: inObject) => {
                            return el.status != 3
                        })
                        .map((el: inObject) => el.name)
                        .join()}的状态不支持该操作`,
                )
                tableRef.value.checkboxReset()
            } else {
                request
                    .post(`/api/hr-work-injuries/update?status=4`, selectRows)
                    .then((res: inObject) => {
                        tableRef.value.refresh()
                        let tip = ''
                        let success = '已收到员工提交的相关资料'
                        if (res.error_status) {
                            tip = res.error_status
                            success = ',选择的其他员工的相关资料接收成功'
                        }
                        if (res.success?.length) {
                            tip += success
                        }
                        openNotification(tip)
                        cancelHandle()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const executeOperation = (obj, status) => {
            let myStatus = status
            let msg = ''
            let params: inObject = { ...obj }
            if (detailType.value == 'supplement') {
                msg = '已通知员工补充资料'
                myStatus = 4
            } else {
                switch (status) {
                    case 2:
                        msg = '审核已通过'
                        break
                    case 6:
                        msg = '工伤认定完成'
                        break
                    case 7:
                        msg = '已确认停工留薪期'
                        break
                    case 9:
                        params = { ...obj, workStoppageEndDate: currentValue.value.workStoppageEndDate }
                        msg = '员工已到岗'
                        break
                    case 10:
                        params = { ...obj, workStoppageEndDate: currentValue.value.workStoppageEndDate }
                        msg = '员工未按时到岗'
                        break
                    case 11:
                        params = { ...obj, workStoppageStartDate: currentValue.value.workStoppageStartDate }
                        msg = '已更新停工留薪期'
                        break
                    case 99:
                        msg = '已完成申报确认'
                        break
                    case 100:
                        msg = '已更新鉴定结果'
                        break
                    case 0:
                        msg = '工伤认定完成' // 非工伤 => 结束
                        break
                }
            }
            if (status == 100) {
                request
                    .post('/api/hr-work-injuries/identification-result', { ...currentValue.value, ...params })
                    .then((res) => {
                        tableRef.value.refresh()
                        cancelHandle()
                        message.success(msg)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else if (status == 99) {
                request
                    .put('/api/hr-work-injuries', { ...currentValue.value, ...params })
                    .then((res) => {
                        tableRef.value.refresh()
                        cancelHandle()
                        message.success(msg)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                request
                    .post(`/api/hr-work-injuries/update?status=${myStatus}`, [{ ...currentValue.value, ...params }])
                    .then((res) => {
                        tableRef.value.refresh()
                        cancelHandle()
                        message.success(msg)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'industrialInjury_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '审核',
                    auth: 'industrialInjury_audit',
                    show: (record) => {
                        return record.status == 1
                    },
                    click: (record) => showModal(record, 'audit'),
                },
                {
                    neme: '申报确认',
                    auth: 'industrialInjury_confirm',
                    show: (record) => {
                        return (record.status == 2 || record.status == 3) && !record.declareDate
                    },
                    click: (record) => showModal(record, 'confirm'),
                },
                {
                    neme: '工伤通知',
                    auth: 'industrialInjury_notice',
                    show: (record) => {
                        return record.status == 2
                    },
                    click: (record) => batchInjuryNotice(record, 'single_notice'),
                },
                {
                    neme: '收到资料',
                    auth: 'industrialInjury_receive',
                    show: (record) => {
                        return record.status == 3
                    },
                    click: (record) => batchReceived(record, 'single'),
                },
                {
                    neme: '补充资料',
                    auth: 'industrialInjury_supplement',
                    show: (record) => {
                        return record.status == 4 || record.status == 5
                    },
                    click: (record) => showModal(record, 'supplement'),
                },
                {
                    neme: '认定结果',
                    auth: 'industrialInjury_affirm_results',
                    show: (record) => {
                        return record.status == 4 || record.status == 5
                    },
                    click: (record) => showModal(record, 'affirm_results'),
                },
                {
                    neme: '停工留薪期',
                    auth: 'industrialInjury_downtime',
                    show: (record) => {
                        return record.status == 6
                    },
                    click: (record) => showModal(record, 'downtime'),
                },
                {
                    neme: '返岗通知',
                    auth: 'industrialInjury_return',
                    show: (record) => {
                        return record.status == 7 && record.workStoppageEndDate
                    },
                    click: (record) => batchInjuryReturn(record, 'single'),
                },
                {
                    neme: '鉴定结果',
                    auth: 'industrialInjury_identify_results',
                    show: (record) => {
                        return record.appraisalStatus == 1
                    },
                    click: (record) => showModal(record, 'identify_results'),
                },
                {
                    neme: '工伤返岗',
                    auth: 'industrialInjury_work_injury',
                    show: (record) => {
                        return record.status == 14
                    },
                    click: (record) => showModal(record, 'work_injury'),
                },
                {
                    neme: '旷工返岗',
                    auth: 'industrialInjury_absenteeism',
                    show: (record) => {
                        return record.status == 10
                    },
                    click: (record) => showModal(record, 'absenteeism'),
                },
            ]),
        )

        // 批量拒绝
        const showReject = ref(false)
        const batchReject = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择要拒绝人员')
                return false
            }
            modalTitle.value = '批量拒绝'
            showReject.value = true
        }
        const rejectRow = (type, obj) => {
            let selectRows: inObject[] = []
            if (type == 'single') {
                selectRows = [{ ...currentValue.value, ...obj }]
            } else {
                selectRows = selectedRowsArr.value.map((item: inObject) => {
                    return { ...item, juryMessage: checkerReason.value }
                })
            }
            request
                .post(`/api/hr-work-injuries/update?status=0`, selectRows)
                .then((res) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '您选择的数据已拒绝'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已拒绝成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    rejectCancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const checkerReason = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            rejectRow('multiple', null)
            showReject.value = false
        }

        return {
            exportText,
            checkerReason,
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            noticModalVisible,
            currentValue,
            detailType,
            showReject,
            // 事件
            searchData,
            confirm,
            cancelHandle,
            batchReject,
            batchInjuryNotice,
            injuryNoticRow,
            batchInjuryReturn,
            rejectCancelHandle,
            addIndustrialInjury,

            //操作
            myOperation,
            selectedRowsArr,

            executeOperation,
            batchExport,
            batchReceived,
            rejectRow,
            batchPassRow,
            rejectConfirm,
            showIndustrial,
            addIndustrialCancel,
            addIndustrialConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
