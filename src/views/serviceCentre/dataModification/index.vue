<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #clientIds="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                v-model:value="params.clientIds"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                :checkStrictly="false"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'dataModification_pass'" @click="passRow()" class="downloadBtn">批量通过</Button>
        <Button type="primary" v-auth="'dataModification_refuse'" @click="rejectRow()" class="delBtn">批量拒绝</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-data-modifications/page"
        exportUrl="/api/hr-data-modifications/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showRejec"
        @cancel="() => (showRejec = false)"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
    </BasicEditModalSlot>

    <MyModal
        :visible="modalVisible"
        :viewType="detailType"
        :item="currentValue"
        :title="modalTitle"
        @cancel="cancelHandle"
        @confirm="confirmHandle"
    />
</template>
<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { ref, defineComponent, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import { SearchBarOption } from '/#/component'
import modal from './modal.vue'
import { getHaveAuthorityOperation, openNotification } from '/@/utils'
import { useRoute } from 'vue-router'
import { dataModificationApplyTypeList, staffStatus } from '/@/utils/dictionaries'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'DataModification',
    components: {
        MyModal: modal,
        ClientSelectTree,
    },
    setup() {
        //状态
        const applyStatusList = ref<any>([
            {
                label: '待审核',
                value: 0,
            },
            {
                label: '已通过',
                value: 1,
            },
            {
                label: '已拒绝',
                value: 2,
            },
        ])
        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 200,
            },
            {
                title: '员工编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 200,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '申请内容',
                dataIndex: 'applyType',
                ellipsis: true,
                // sorter: false,
                customRender: ({ text }) => {
                    return dataModificationApplyTypeList.find((item) => {
                        return text == item.value
                    })?.label
                },
                width: 200,
                // customRender: ({ text }) => {
                //     // let titleList: inObject[] = []
                //     // let options = myOptions['myOptions' + record?.applyType] || {}
                //     // let modificationTitles = record?.modificationTitles?.split(',') || []
                //     // modificationTitles?.forEach((title, index) => {
                //     //     //获取字段对应的name
                //     //     //标题处理
                //     //     let modificationTitle = title
                //     //     try {
                //     //         let modificationTitleList = title?.split('--')
                //     //         let name = options[modificationTitleList[1]] || ''
                //     //         if (name) {
                //     //             modificationTitle = modificationTitleList[0] + '--' + name
                //     //         } else {
                //     //             // if (item.modificationType == 2) {
                //     //             //因下拉数据的显示问题所以当标题的后半段为字母时不显示
                //     //             let isletter2 = /^[a-zA-Z]+$/.test(modificationTitleList[1])
                //     //             if (isletter2) {
                //     //                 return
                //     //             }
                //     //             // }
                //     //             modificationTitle = title
                //     //         }
                //     //     } catch (err) {
                //     //         modificationTitle = title
                //     //     }
                //     //     titleList.push(modificationTitle)
                //     // })
                //     // return titleList.join()
                // },
            },

            {
                title: '申请时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'applyStatus',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 0) {
                        text = '待审核'
                    } else if (text == 1) {
                        text = '已通过'
                    } else {
                        text = '已拒绝'
                    }
                    return text
                },
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ])

        let options: SearchBarOption[] = [
            {
                type: 'selectSlot',
                label: '客户名称',
                key: 'clientIds',
                placeholder: '客户名称',
                maxTag: 1,
            },
            {
                type: 'string',
                label: '员工编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },

            {
                type: 'select',
                label: '状态',
                key: 'applyStatusList',
                options: applyStatusList,
                multiple: true,
            },
            {
                type: 'select',
                label: '申请内容',
                key: 'applyTypeList',
                options: dataModificationApplyTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
        ]

        onMounted(() => {})
        //表格dom
        const tableRef = ref()
        //筛选
        const route = useRoute()
        const params = ref<any>({
            applyStatusList: route.query?.applyStatusList ? JSON.parse(route.query?.applyStatusList as string) : undefined,
        })

        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const modalTitle = ref('')
        const modalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<any>(null)

        // 显示弹窗
        const showModal = (record, type) => {
            modalVisible.value = true
            currentValue.value = record
            switch (type) {
                case 'look':
                    modalTitle.value = '查看资料'
                    break
                case 'examine':
                    modalTitle.value = '审核资料'
                    break
            }
            detailType.value = type
        }

        const cancelHandle = () => {
            modalVisible.value = false
            // tableRef.value.refresh()
        }
        const confirmHandle = () => {
            modalVisible.value = false
            tableRef.value.refresh()
        }
        // 多选
        const selectedRowsArr = ref([])
        const showRejec = ref(false)
        // 批量通过
        const passRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择通过人员')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            Modal.confirm({
                title: '确认',
                content: '您确定要批量通过该条数据吗？',
                onOk() {
                    request
                        .post('/api/hr-data-modifications/batch-pass', {
                            applyIdList: applyIdList,
                            checkerReason: null,
                        })
                        .then((res) => {
                            let tip = ''
                            let success = '您选择的数据已修改成功'
                            if (res.error_status) {
                                tip = res.error_status
                                success = ',选择的其它数据已修改成功'
                            }
                            if (res.success?.length) {
                                tip += success
                            }
                            openNotification(tip)

                            tableRef.value.refresh()
                        })
                    // tableRef.value.refresh()
                },
                onCancel() {},
            })
        }

        // 批量拒绝
        const rejectRow = () => {
            modalTitle.value = '批量拒绝'
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择拒绝人员')
                return false
            }
            showRejec.value = true
        }
        const checkerReason = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            request
                .post('/api/hr-data-modifications/batch-reject', {
                    applyIdList: applyIdList,
                    checkerReason: checkerReason.value,
                })
                .then((res) => {
                    let tip = ''
                    let success = '您选择的数据已修改成功'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其它数据已修改成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)

                    tableRef.value.refresh()
                })
            showRejec.value = false

            // applyIdList.value = []
        }
        // 批量入职
        const InductionMoreRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先选择入职人员!')
                return
            }
            let applyIdList = selectedRowsArr.value.map((el: inObject) => {
                return el.staffId
            })
            request.post('/api/hr-apply-entry-staffs/entry-level', applyIdList).then((res) => {
                console.log(res)
                tableRef.value.refresh()
            })
        }

        // 确认信息
        const confirmInfoRow = (record?) => {
            let ids: any[] = []
            if (record) {
                ids.push(record.id)
            } else {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请先选择人员!')
                    return
                }
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.staffId
                })
            }
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'dataModification_see',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '审核',
                    auth: 'dataModification_examine',
                    show: (record) => {
                        return record.applyStatus == 0
                    },
                    click: (record) => showModal(record, 'examine'),
                },
            ]),
        )
        return {
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            currentValue,
            detailType,
            // 操作
            myOperation,
            selectedRowsArr,
            // 事件
            searchData,
            cancelHandle,
            confirmHandle,

            InductionMoreRow,
            confirmInfoRow,
            rejectRow,
            // 批量拒绝显示
            showRejec,
            //批量拒绝确认
            rejectConfirm,
            //批量拒绝描述
            checkerReason,
            // 批量通过
            passRow,
            exportData,
            exportText,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
