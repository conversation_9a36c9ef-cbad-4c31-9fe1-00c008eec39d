<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>申请修改内容</span>
        <div class="examine-flex">
            <BasicTable
                ref="tableRef"
                api=""
                :columns="columns"
                :data-source="columnsData"
                :rowSelectionShow="false"
                :isPage="false"
            >
                <template #modificationBefore="{ text }">
                    <div v-html="text"></div>
                </template>
                <template #modificationAfter="{ text }">
                    <div v-html="text"></div>
                </template>
            </BasicTable>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { myOptions } from './data'

export default defineComponent({
    name: 'ApplicationInfo',
    props: {
        currentValue: {
            type: Object,
        },
    },
    setup(props) {
        const { currentValue } = toRefs(props)
        const columnsData = ref({})
        // const detailData = ref<inObject>({
        //     date: '2021-10-01',
        //     description:
        //         'aljksdaipshjlhioho开始努力扩大鸡排的按时间都库按时吃枯燥库活动库阿萨德加厚意外哦 到is回家啊搜到回家哦库暗示大家哦库爱仕达卡拉是从哪  ',
        // })
        //表格数据
        const columns = ref([
            {
                title: '类型',
                dataIndex: 'modificationTypeLabel',
                align: 'center',
                sorter: false,
            },
            {
                title: '标题',
                dataIndex: 'modificationTitle',
                align: 'center',
                sorter: false,
            },
            {
                title: '修改前',
                dataIndex: 'modificationBefore',
                align: 'left',
                slots: { customRender: 'modificationBefore' },
                sorter: false,
            },
            {
                title: '修改后',
                dataIndex: 'modificationAfter',
                align: 'left',
                slots: { customRender: 'modificationBefore' },
                sorter: false,
            },
        ])
        const contentProcess = (data, options, modificationType) => {
            let Text = ''
            for (let info in data) {
                let name = options[info]
                if (modificationType == 2) {
                    if (info == 'avatar' || info == 'bankCardUrl') {
                        //当为图片时
                        Text = `<img width="100px" src="${data[info]}" />`
                    } else {
                        Text = data[info]
                    }
                    // modificationAfterText = modificationAfter[info]
                } else {
                    if (info == 'avatar' || info == 'bankCardUrl') {
                        //当为图片时
                        Text += `${name}: <img width="100px" src="${data[info]}" /> </br>`
                    } else if (name) {
                        Text += `${name}: ${data[info]}</br>`
                    }
                }
            }
            return Text
        }
        watch(
            currentValue,
            () => {
                // columnsData.value = currentValue.value?.modificationContentList
                let columnsList: inObject[] = []
                //获取字段对应的name
                let options = myOptions['myOptions' + currentValue.value?.applyType] || {}
                currentValue.value?.modificationContentList?.forEach((item, index) => {
                    let manyInfo = item.izSingle
                    //标题处理
                    let modificationTitle = item.modificationTitle
                    try {
                        let modificationTitleList = item.modificationTitle.split('--')
                        let modificationTitleLength = modificationTitleList.length
                        let name = options[modificationTitleList[modificationTitleLength - 1]] || ''
                        if (name) {
                            modificationTitleList.splice(modificationTitleLength - 1, 1, name)
                            modificationTitle = modificationTitleList.join('--')
                        } else {
                            // if (item.modificationType == 2) {
                            //因下拉数据的显示问题所以当标题的后半段为字母时不显示
                            if (manyInfo == 2) {
                                let isletter2 = /^[a-zA-Z]+$/.test(modificationTitleList[1])
                                if (isletter2) {
                                    return
                                }
                            }
                            // }
                            modificationTitle = item.modificationTitle
                        }
                    } catch (err) {
                        modificationTitle = item.modificationTitle
                    }

                    //内容处理
                    let modificationAfter = JSON.parse(item.modificationAfter)
                    let modificationBefore = JSON.parse(item.modificationBefore)
                    //开始内容
                    let modificationAfterText = contentProcess(modificationAfter, options, manyInfo)
                    //结束内容
                    let modificationBeforeText = contentProcess(modificationBefore, options, manyInfo)

                    // for (let info in modificationAfter) {
                    //     let name = options[info]
                    //     if (item.modificationType == 2) {
                    //         if (info == 'avatar') {
                    //             //当为图片时
                    //             modificationAfterText = `<img width="100px" src="${modificationAfter[info]}" />`
                    //         } else {
                    //             modificationAfterText = modificationAfter[info]
                    //         }
                    //         // modificationAfterText = modificationAfter[info]
                    //     } else {
                    //         if (info == 'avatar') {
                    //             //当为图片时
                    //             modificationAfterText += `${name}: <img width="100px" src="${modificationAfter[info]}" /> </br>`
                    //         } else if (name) {
                    //             modificationAfterText += `${name}: ${modificationAfter[info]}</br>`
                    //         }
                    //     }
                    // }
                    // //结束内容
                    // for (let info in modificationBefore) {
                    //     let name = options[info]
                    //     if (item.modificationType == 2) {
                    //         if (info == 'avatar') {
                    //             //当为图片时
                    //             modificationBeforeText = `<img width="100px" src="${modificationBefore[info]}" />`
                    //         } else {
                    //             modificationBeforeText = modificationBefore[info]
                    //         }
                    //     } else {
                    //         if (info == 'avatar') {
                    //             //当为图片时
                    //             modificationBeforeText += `${name}: <img width="100px" src="${modificationBefore[info]}" /> </br>`
                    //         } else if (name) {
                    //             modificationBeforeText += `${name}: ${modificationBefore[info]}</br>`
                    //         }
                    //     }
                    // }

                    columnsList.push({
                        modificationTypeLabel: item.modificationTypeLabel,
                        modificationTitle,
                        modificationAfter: modificationAfterText,
                        modificationBefore: modificationBeforeText,
                        id: index,
                    })
                })
              
                columnsData.value = columnsList
            },
            {
                immediate: true,
                deep: true,
            },
        )

        return {
            columnsData,
            columns,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
