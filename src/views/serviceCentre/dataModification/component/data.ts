//基本信息
const myOptions1 = {
    avatar: '照片',
    name: '姓名',
    nationalityLabel: '国籍(地区)',
    nation: '民族',
    certificateTypeLabel: '证件类型',
    certificateNum: '证件号码',
    birthday: '出生日期',
    age: '年龄',
    sexLabel: '性别',
    politicsStatusLabel: '政治面貌',
    partyDate: '入党时间',
    partyBranch: '所在党支部',
    phone: '手机号码',
    maritalStatusLabel: '婚姻状态',
    nativePlace: '籍贯',
    highestEducationLabel: '最高学历',
    height: '身高(cm)',
    email: 'E-maill',
    censusRegisterAddress: '户籍地址(身份证)',
    censusRegisterPostcode: '邮编',
    contactAddress: '通讯地址(现居住)',
    contactPostcode: '邮编',
    householdRegistrationLabel: '户口性质',
    domicilePlace: '户口所在地',
    nickName: '职称',
    izMilitary: '是否服兵役',
    militaryDate: '服兵役期限',
    rewardsPunishment: '奖惩情况',
    clientName: '待入职单位名称',
    professionName: '岗位',
    deptName: '部门名称',
    salarySectionLabel: '月薪区间',
    workNatureLabel: '工作地性质',
    workLocation: '工作地址',
}
//工作经历
const myOptions2 = {
    employerUnit: '用工单位',
    deptName: '部门名称',
    // stationId: '岗位',
    professionName: '岗位',
    personnelTypeLabel: '人员类型',
    workLocation: '工作地址',
    salarySectionLabel: '月薪区间',
    boardDate: '入职日期',
    departureDate: '离职日期',
    dimissionReason: '离职原因',
    certifyPeople: '证明人',
    certifyPhone: '证明人',
    workAchievement: '工作成果',
    selfEvaluation: '自我评价',
}

//教育经历
const myOptions3 = {
    education: '教育机构',
    educationStartDate: '开始时间',
    educationEndDate: '结束时间',
    specialty: '所学专业',
    highestDegreeLabel: '最高学历',
    degreeLabel: '学位',
    studyModalityLabel: '学习形式',
    yearNum: '年制',
    certificate: '所获证书',
}
//应试经历

const myOptions4 = {
    interviewUnit: '应试单位',
    // interviewStationId: '应试岗位',
    professionName: '应试岗位',
    interviewLinkLabel: '应试环节',
    interviewTime: '应试时间',
    interviewPlace: '应试地点',
    interviewExaminer: '考官',
    interviewGrade: '应试成绩',
    interviewResultLabel: '应试结果',
    interviewEvaluate: '应试评价',
    interviewRemark: '备注',
}
//家庭成员
const myOptions5 = {
    familyName: '姓名',
    relation: '关系',
    contactWay: '联系方式',
    workUnit: '工作单位',
    presentAddress: '现居地',
}
//紧急联系人
const myOptions6 = { familyName: '姓名', relation: '关系', contactWay: '联系方式', contactAddress: '联系地址' }
//语言能力
const myOptions7 = { languageLabel: '语种', proficiencyLabel: '熟练程度', ratingCertificate: '等级证书' }
//专业技能
const myOptions8 = { skillName: '技能名称', skillProficiencyLabel: '熟练程度', utilityDuration: '使用时长(月)' }
//职业（工种）资格
const myOptions9 = { qualificationName: '职业（工种）资格名称', qualificationGradeLabel: '等级', acquisitionDate: '取得时间' }
//职业技术能力
const myOptions10 = { techniqueName: '职业技术资格', techniqueGradeLabel: '等级', acquisitionDate: '取得时间' }
//证书
const myOptions11 = { certificateName: '证书名称', acquisitionDate: '获取时间', issuingAgency: '颁发机构' }
//工资卡
const myOptions12 = { ownedLabel: '工资卡', salaryCardNum: '工资卡卡号', bankCardUrl: '工资卡图片' }

export const myOptions = {
    myOptions1,
    myOptions2,
    myOptions3,
    myOptions4,
    myOptions5,
    myOptions6,
    myOptions7,
    myOptions8,
    myOptions9,
    myOptions10,
    myOptions11,
    myOptions12,
}
