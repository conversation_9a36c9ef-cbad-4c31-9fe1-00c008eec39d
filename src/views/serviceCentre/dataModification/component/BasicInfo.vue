<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>基本信息</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">客户编号：</span>
                <span>{{ basicInfoList.unitNumber }}</span>
            </div>
            <div class="item-flex">
                <span class="label">客户名称：</span>
                <span>{{ basicInfoList.clientName }}</span>
            </div>
            <div class="item-flex"></div>
            <div class="item-flex"></div>
            <div class="item-flex">
                <span class="label">员工编号：</span>
                <span>{{ basicInfoList.systemNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">姓名：</span>
                <span>{{ basicInfoList.name }}</span>
            </div>
            <div class="item-flex">
                <span class="label">身份证号：</span>
                <span>{{ basicInfoList.certificateNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">手机号：</span>
                <span>{{ basicInfoList.phone }}</span>
            </div>
            <div class="item-flex">
                <span class="label">申请时间：</span>
                <span>{{ basicInfoList.createdDate }}</span>
            </div>
        </div>
        <slot :detailData="detailData"></slot>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue'
export default defineComponent({
    name: 'ApplicationInfo',
    props: {
        basicInfoList: {
            type: Object,
        },
    },
    setup() {
        const detailData = ref<inObject>({
            date: '2021-10-01',
            description:
                'aljksdaipshjlhioho开始努力扩大鸡排的按时间都库按时吃枯燥库活动库阿萨德加厚意外哦 到is回家啊搜到回家哦库暗示大家哦库爱仕达卡拉是从哪  ',
        })

        return {
            detailData,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    // margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
