<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <!-- 基本信息 -->
        <BasicInfo :viewType="viewType" :basicInfoList="formData" />
        <!-- 申请修改内容 -->
        <ApplicationInfo :currentValue="formData" />
        <!-- 审核信息 -->
        <template v-if="viewType == 'look'">
            <div class="examine" style="margin-top: 50px">
                <Divider type="vertical" class="divid" />
                <span>审核信息</span>
                <div class="examine-flex">
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span class="label">审核人：</span>
                        <span>{{ examineList.realName }}</span>
                    </div>
                    <div class="item-flex">
                        <span class="label">审核时间：</span>
                        <span>{{ examineList.createdDate }}</span>
                    </div>
                    <div class="item-flex"></div>
                    <div class="item-flex"></div>
                    <div class="item-flex">
                        <span class="label">审核结果：</span>
                        <span>{{ examineList.checkerResultLabel }}</span>
                    </div>
                    <div class="item-flex" style="width: 75%">
                        <span class="label">拒绝理由：</span>
                        <span>{{ examineList.checkerReason }}</span>
                    </div>
                    <div class="item-flex" style="width: 100%">
                        <span class="label">备注：</span>
                        <span>{{ examineList.checkerRemark }}</span>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="viewType == 'examine'">
            <div class="examine-bottm">
                <!-- 备注 -->
                <div class="examine1" style="margin-top: 50px">
                    <Divider type="vertical" class="divid" />
                    <span>备注</span>
                    <div class="examine-flex">
                        <p class="linefeed"></p>
                        <div class="item-flex">
                            <Textarea v-model:value="examineList.checkerRemark" :rows="4" allowClear placeholder="请输入备注" />
                        </div>
                    </div>
                </div>
                <!-- 拒绝理由 -->
                <div class="examine2" style="margin-top: 50px">
                    <Divider type="vertical" class="divid" />
                    <span>拒绝理由</span>
                    <div class="examine-flex">
                        <p class="linefeed"></p>
                        <div class="item-flex">
                            <Textarea
                                v-model:value="examineList.checkerReason"
                                :rows="4"
                                allowClear
                                placeholder="请输入拒绝理由"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <template #footer>
            <div v-if="viewType === 'examine'">
                <Button @click="onRefuse" class="delBtn">拒绝</Button>
                <Button @click="onPass" type="primary" class="btn">通过</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import BasicInfo from './component/BasicInfo.vue'
import ApplicationInfo from './component/ApplicationInfo.vue'
import request from '/@/utils/request'
import inductionApplyStore from '/@/store/modules/inductionApply'
export default defineComponent({
    name: 'DataModificationModal',
    components: { BasicInfo, ApplicationInfo },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { viewType, title, item, visible } = toRefs(props)
        onMounted(() => {})

        // Form 实例
        const formInline = ref(null) as any

        // Form Data
        const formData = ref<any>({})
        //申请信息数据
        // const applicationList = ref<any>({})
        // //基本信息数据
        // const basicInfoList = ref<any>({})
        //审核信息数据
        const examineList = ref<any>({})
        //操作信息数据
        const operationLog = ref<any>([])
        //获取详情数据
        const getData = () => {
            request.get(`/api/hr-data-modifications/?id=` + item.value?.id).then((res) => {
                formData.value = res
                if (res?.applyCheckerList != null) {
                    examineList.value = res.applyCheckerList[0]
                }
            })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    // console.log(item.value)
                    if (item.value?.id) {
                        getData()
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        // 取消
        const onCancel = () => {
            emit('cancel')
        }
        // 拒绝
        const onRefuse = async () => {
            let applyIdList: any = []
            applyIdList = []
            applyIdList.push(item.value?.id)
            let params = {
                checkerReason: examineList.value?.checkerReason,
                applyRemark: examineList.value?.checkerRemark,
                applyIdList: applyIdList,
            }
            if (!params.checkerReason) {
                message.warning('请填写拒绝理由')
                return false
            }
            await request.post('/api/hr-data-modifications/batch-reject' || '', params)
            message.success('操作成功!')
            examineList.value.checkerReason = ''
            examineList.value.checkerRemark = ''
            onCancel()
            emit('confirm')
        }
        //通过
        const onPass = async () => {
            let applyIdList: any = []
            applyIdList = []
            applyIdList.push(item.value?.id)
            let params = {
                // checkerReason: examineList.value?.checkerReason,
                applyRemark: examineList.value?.checkerRemark,
                applyIdList: applyIdList,
            }
            await request.post('/api/hr-data-modifications/batch-pass' || '', params)
            message.success('操作成功!')
            examineList.value.checkerReason = ''
            examineList.value.checkerRemark = ''
            onCancel()
            // 表单关闭后的其它操作 如刷新表
            emit('confirm')
        }

        return {
            onRefuse,
            onPass,
            onCancel,
            formData,
            // applicationList,
            // basicInfoList,
            examineList,
            operationLog,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
}

.examine {
    // margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.examine-bottm {
    display: flex;
    justify-content: space-between;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine1 {
        width: 40%;
    }
    .examine2 {
        width: 50%;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 100%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
