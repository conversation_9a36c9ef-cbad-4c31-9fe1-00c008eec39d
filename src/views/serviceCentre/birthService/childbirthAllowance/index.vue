<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #professionNameList="itemForm">
            <PostTree
                v-model:value="params.professionNameList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                style="width: 190px"
            />
        </template>
        <template #clientIdList="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                v-model:value="params.clientIdList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                :checkStrictly="false"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'childbirthAllowance_batchPass'" @click="batchPass(true, true)"> 批量通过 </Button>
        <Button type="primary" v-auth="'childbirthAllowance_batchReject'" danger @click="batchReject()"> 批量拒绝 </Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-maternity-allowances/page"
        :params="params"
        :columns="columns"
        exportUrl="/api/hr-maternity-allowances/export"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="modalCancel"
        @confirm="batchOperate(2)"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message, Modal, notification } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import modal from './modal.vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
import { childbirthAllowanceStateOptions, sexList, staffStatus } from '/@/utils/dictionaries'
import permissionStore from '/@/store/modules/permission'
export default defineComponent({
    name: 'ChildbirthAllowance',
    components: { MyModal: modal, ClientSelectTree, PostTree },
    setup() {
        const RoleKey = permissionStore().getPermission.roleKey
        let personnelTypeList = ref<LabelValueOptions>([]) //人员类型
        // 筛选
        const params = ref<any>({})
        let searchOptions: SearchBarOption[] = [
            {
                type: 'selectSlot',
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                maxTag: 1,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexList,
                multiple: true,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位',
                key: 'professionNameList',
                placeholder: '岗位',
                maxTag: 1,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: personnelTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '生育日期',
                key: 'operationDateQuery',
            },
            {
                type: 'daterange',
                label: '申请日期',
                key: 'createdDateQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'stateList',
                options: childbirthAllowanceStateOptions,
                show: RoleKey != 'client',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const applyRemark = ref<any>()
        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 220,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 180,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text == 1 ? '男' : '女'
                    }
                    return text
                },
                width: 100,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 150,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeName
                },
                width: 150,
            },
            {
                title: '生育日期',
                dataIndex: 'operationDate',
                align: 'center',
                width: 150,
            },
            {
                title: '申请日期',
                dataIndex: 'createdDate',
                align: 'center',
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'state',
                align: 'center',
                customRender: ({ record }) => {
                    return (
                        childbirthAllowanceStateOptions.find((el) => {
                            el.value == record.state
                        })?.label || ''
                    )
                },
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ])

        onMounted(() => {
            //人员类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                personnelTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('')
        const viewType = ref('')
        // 当前编辑的数据
        const currentValue = ref(null)
        const editRow = (viewTypeName, record) => {
            showEdit.value = true
            viewType.value = viewTypeName
            switch (viewTypeName) {
                case 'see':
                    modalTitle.value = '查看'
                    break
                case 'audit':
                    modalTitle.value = '审核'
                    break
                case 'finish':
                    modalTitle.value = '完成'
                    break
                default:
                    break
            }
            currentValue.value = { ...record }
        }
        const modalCancel = () => {
            showEdit.value = false
            showReject.value = false
        }
        const modalConfirm = () => {
            tableRef.value.refresh()
        }

        // 多选
        const selectedRowsArr = ref([])
        // 批量操作
        const batchOperate = (type) => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            let ids = selectedRowsArr.value.map((el: inObject) => {
                return el.id
            })
            request
                .post(`/api/hr-maternity-allowances/batch-approval`, {
                    ids: ids,
                    opt: type == 1 ? true : false,
                    checkerReason: checkerReason.value,
                    remark: applyRemark.value,
                })
                .then((res: any) => {})
        }
        // 批量通过
        const batchPass = (isBatch, flag, postObj?) => {
            if (selectedRowsArr.value.length <= 0 && isBatch) {
                message.warning('请至少选择一条数据')
                return
            }

            request
                .post('/api/hr-maternity-allowances/batch-approval', {
                    opt: flag,
                    checkerReason: isBatch ? checkerReason.value : postObj.reason,
                    remark: isBatch ? applyRemark.value : postObj.remark,
                })
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    if (res.checkCode == 500) {
                        openNotification(res.checkMsg)
                    }
                    if (res.checkCode == 200) {
                        message.success(res.checkMsg)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // 批量拒绝
        const showReject = ref(false)
        const checkerReason = ref('')
        const batchReject = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请至少选择一条数据')
                return false
            }
            checkerReason.value = ''
            showReject.value = true
        }
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '批量处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'childbirthAllowance_see',
                    show: (record) => {
                        return record.state != 4 || record.state != 5
                    },
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '审核',
                    auth: 'childbirthAllowance_audit',
                    show: (record) => {
                        return record.state == 1
                    },
                    backColor: '#3eb889',
                    click: (record) => editRow('audit', record),
                },
                {
                    neme: '完成',
                    auth: 'childbirthAllowance_finish',
                    show: (record) => {
                        return record.state == 4
                    },
                    backColor: '#3eb889',
                    click: (record) => editRow('finish', record),
                },
            ]),
        )
        return {
            showReject,
            checkerReason,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量通过
            batchPass,
            //编辑
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            batchReject,
            batchOperate,
            applyRemark,
            exportData,
            exportText,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
