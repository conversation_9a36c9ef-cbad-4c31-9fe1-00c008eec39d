<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" :currentStep="currentStep" labelPlacement="vertical" />
        <ApplicationInfo :viewType="viewType" :detailData="applicationList">
            <template #outer="{ detailData }">
                <div class="item-outer">
                    <span class="label">产假开始日期：</span> <span>{{ detailData?.maternityLeaveStartDate }}</span>
                    <span class="label">产假结束日期：</span> <span>{{ detailData?.maternityLeaveEndDate }}</span>
                </div>
                <div class="item-outer">
                    <span class="label">生育病种：{{ detailData?.diseaseType }}</span>
                    <span class="label">生育/手术时间：{{ detailData?.operationDate }}</span>
                    <span class="label">胎次：{{ detailData?.parity }}</span>

                    <span class="label">胎儿数：{{ detailData?.fetusNum }}</span>
                </div>
                <div class="item-outer">
                    <span class="label">生育服务手册编号：{{ detailData?.manualNum }}</span>
                    <span class="label">出生医学证明编号：{{ detailData?.proveNum }}</span>
                    <span class="label">孕周数：{{ detailData?.gestationalWeeks }}</span>
                    <span class="label">联系电话：{{ detailData?.phone }}</span>
                </div>
                <div class="item-outer-pic">
                    <span class="label">出生医学证明：</span>
                    <div class="append_list">
                        <span v-for="el in detailData.birthAppendixList" :key="el.id" class="imgWrapper">
                            <a href="javascript: void(0)" @click="previewFile(el.fileUrl)" style="margin-right: 25px">
                                <img :src="el.fileUrl" alt="" />
                            </a>
                        </span>
                    </div>
                </div>
                <div class="item-outer-pic">
                    <span class="label">出院记录或出院小结：</span>
                    <div class="append_list">
                        <span v-for="el in detailData.leaveHospitalAppendixList" :key="el.id" class="imgWrapper">
                            <a href="javascript: void(0)" @click="previewFile(el.fileUrl)" style="margin-right: 25px">
                                <img :src="el.fileUrl" alt="" />
                            </a>
                        </span>
                    </div>
                </div>
                <div class="item-outer-pic">
                    <span class="label">生育服务手册：</span>
                    <div class="append_list">
                        <span v-for="el in detailData.fertilityAppendixList" :key="el.id" class="imgWrapper">
                            <a href="javascript: void(0)" @click="previewFile(el.fileUrl)" style="margin-right: 25px">
                                <img :src="el.fileUrl" alt="" />
                            </a>
                        </span>
                    </div>
                </div>
            </template>
        </ApplicationInfo>
        <OperationInfo :operationLog="operationLog" />
        <template v-if="viewType == 'audit'">
            <div class="audit-wrapper">
                <Row :gutter="20">
                    <Col span="12">
                        <div class="examine">
                            <Divider type="vertical" class="divid" />
                            <span>备注</span>
                            <p class="linefeed"></p>
                            <div class="examine-wrap">
                                <Textarea v-model:value="remark" :rows="3" allowClear placeholder="请输入备注" />
                            </div></div
                    ></Col>
                    <Col span="12">
                        <div class="examine">
                            <Divider type="vertical" class="divid" />
                            <span>拒绝理由</span>
                            <p class="linefeed"></p>
                            <div class="examine-wrap">
                                <Textarea
                                    v-model:value="denialReason"
                                    :rows="3"
                                    allowClear
                                    placeholder="若拒绝，请输入拒绝理由"
                                />
                            </div></div
                    ></Col>
                </Row>
            </div>
        </template>
        <template v-if="viewType == 'finish'">
            <div class="audit-wrapper">
                <Divider type="vertical" class="divid" />
                <span>办理结果</span>
                <p class="linefeed"></p>
                <div class="wrapper">
                    <Row>
                        <Col span="12">
                            <span>补贴金额：</span>
                            <InputNumber
                                style="width: 200px"
                                v-model:value="fertilityAmount"
                                placeholder="请输入补贴金额"
                                suffix="元/天"
                            />
                        </Col>
                        <Col span="12">
                            <span>日期：</span>
                            <RangePicker
                                v-model:value="dateTime"
                                placeholder="申请日期"
                                format="YYYY-MM-DD"
                                valueFormat="YYYY-MM-DD"
                                :allowClear="true"
                            />
                        </Col>
                    </Row>
                </div>
                <div class="wrapper">
                    <Row>
                        <Col span="12">
                            <span>上传附件：</span>
                            <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                                <Button type="primary" size="small">上传文件</Button>
                            </Upload>
                            <div class="files">
                                <span class="item" v-for="(i, idx) in applicationList.enclosures" :key="i.id">
                                    <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                                    <span @click="removeFile(idx)">x</span>
                                </span>
                            </div>
                        </Col>

                        <Col span="10" style="display: flex">
                            <span>备注：</span>
                            <div>
                                <Textarea v-model:value="remark" placeholder="请输入" />
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>
        </template>

        <template #footer>
            <div v-if="viewType === 'finish' || viewType === 'audit'">
                <Button @click="onCancel" class="btn" v-if="viewType == 'finish'">取消</Button>
                <Button @click="onConfirm(false)" danger  v-if="viewType == 'audit'" :style="{color:'#FFF'}">拒绝</Button>
                <Button @click="onConfirm(true)" type="primary" class="success_btn" v-if="viewType == 'audit'">通过</Button>
                <Button @click="onFinishConfirm" type="primary" class="btn" v-if="viewType == 'finish'">完成</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import StepBar from '../../retirement/component/StepBar.vue'
import ApplicationInfo from '../../retirement/component/ApplicationInfo.vue'
import OperationInfo from '../component/OperationInfo.vue'
import request from '/@/utils/request'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { previewFile } from '/@/utils'
import { uploadFile } from '/@/utils/upload'
export default defineComponent({
    name: 'ChildbirthAllowanceModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { viewType, title, item } = toRefs(props)

        const staffTypeList = ref<object[]>([]) //人员类型
        const professionNameList = ref<object[]>([]) //岗位
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('staffType')
                .then((res: any) => {
                    staffTypeList.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('hr-stations', '/api/hr-stations/list')
                .then((res: any) => {
                    professionNameList.value = res.map((item) => {
                        return { label: item.professionName, value: item.id }
                    })
                })
        })
        //表单数据
        const myOptions = ref([
            {
                label: '补贴金额',
                name: 'name',
                type: 'slots',
                slot: 'fertilityAmount',
            },
            {
                label: '申请日期',
                name: 'name',
                type: 'slots',
                ruleType: 'array',
                slot: 'grantDateList',
            },
        ])
        const stepsData = ref([
            {
                title: '员工发起生育津贴申请',
            },
            {
                title: '待遇合规专员审核',
            },
            {
                title: '办理中',
            },
            {
                title: '办理完成',
            },
        ])
        //请求
        const { visible } = toRefs(props)
        const currentStep = ref<number>()

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        //申请信息数据
        const applicationList = ref<any>({
            enclosures: [],
        })
        //操作信息数据
        const operationLog = ref<any>([])
        // 备注信息
        const remark = ref<string>('')
        // 日期
        const dateTime = ref<any>([])
        // 补贴金额
        const fertilityAmount = ref<number>()
        // 拒绝信息
        const denialReason = ref<string>('')
        //获取详情数据
        const getData = () => {
            request.get(`/api/hr-maternity-allowances/${item.value?.id}`).then((res) => {
                console.log('res', res)
                // status状态 0 通知 1确认时间 2 返岗通知
                let date = new Date()
                let month: any = date.getMonth() + 1
                let strDate: any = date.getDate()
                if (month >= 1 && month <= 9) {
                    month = '0' + month
                }
                if (strDate >= 0 && strDate <= 9) {
                    strDate = '0' + strDate
                }
                let currentdate = date.getFullYear() + '-' + month + '-' + strDate
                if (res.state == 0) {
                    //公司通知
                    currentStep.value = 1
                } else if (res.state == 1) {
                    if (currentdate >= res.maternityLeaveStartDate) {
                        //员工开始休假
                        currentStep.value = 1
                    } else {
                        //专管员确认产假开始时间
                        currentStep.value = 1
                    }
                } else if (res.state == 2) {
                    if (currentdate >= res.maternityLeaveEndDate) {
                        //员工休假结束
                        currentStep.value = 5
                    } else {
                        //发送返岗通知
                        currentStep.value = 4
                    }
                } else if (res.state == 4) {
                    if (currentdate >= res.maternityLeaveEndDate) {
                        currentStep.value = 2
                    } else {
                        //发送返岗通知
                        currentStep.value = 2
                    }
                } else if (res.state == 5) {
                    currentStep.value = 5
                }
                console.log(currentStep.value)
                applicationList.value = { ...res, enclosures: [] }
                operationLog.value = res.applyOpLogsList
            })
        }
        watch(
            visible,
            () => {
                console.log(viewType.value)
                if (visible.value) {
                    // console.log(item.value)
                    if (item.value?.id) {
                        getData()
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )
        const beforeUpload = async (file) => {
            const res = await uploadFile(file)
            applicationList.value.enclosures.push(res)
            return false
        }
        const removeFile = (idx) => {
            applicationList.value.enclosures.splice(idx, 1)
        }

        // 单个校验
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate(nameList)
            })
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const resetData = () => {
            remark.value = ''
            dateTime.value = ''
            fertilityAmount.value = 0
        }
        // cancel handle
        const onCancel = () => {
            emit('cancel')
            if (viewType.value != 'see') {
                resetFormData()
            }
        }
        const resetReason = () => {
            denialReason.value = ''
            remark.value = ''
            emit('cancel')
        }
        //请求
        // /api/hr-maternity-allowances/batch-approval
        const api = viewType.value == 'finish' ? '/api/hr-maternity-allowances/confirm' : '/api/hr-fertility'
        // confirm handle
        const onConfirm = (type) => {
            if (type == 2) {
                if (!denialReason.value) {
                    message.warning('请输入拒绝理由')
                    return false
                }
            }
            request
                .post('/api/hr-maternity-allowances/batch-approval', {
                    id: item.value?.id,
                    checkerReason: denialReason.value,
                    remark: remark.value,
                    opt: type,
                })
                .then((res) => {
                    message.success(res)
                    resetReason()
                    emit('confirm')
                    remark.value = ''
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const onFinishConfirm = () => {
            let appendixIdList = []
            appendixIdList = applicationList.value.enclosures.map((el) => {
                return el.id
            })
            if (!fertilityAmount.value) {
                message.warning('请填写补贴金额')
                return
            }
            if (!remark.value) {
                message.warning('请填写备注')
                return
            }

            if (!dateTime.value) {
                message.warning('请输入日期')
                return
            }
            if (!applicationList.value.enclosures) {
                message.warning('请添加附件')
                return
            }
            request
                .post(`/api/hr-maternity-allowances/confirm`, {
                    id: item.value?.id,
                    fertilityAmount: fertilityAmount.value,
                    grantDateList: dateTime.value,
                    remark: remark.value,
                    appendixIdList: appendixIdList,
                })
                .then((res) => {
                    message.success(res)
                    resetData()
                    emit('confirm')
                    onCancel()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        return {
            previewFile,
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,
            stepsData,
            currentStep,
            applicationList,
            operationLog,
            remark,
            denialReason,
            beforeUpload,
            removeFile,
            onFinishConfirm,
            dateTime,
            fertilityAmount,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
.success_btn {
    background: @upload-color;
    border: none;
}
.item-outer {
    padding-left: 15px;
    margin: 5px 0px;
    padding-top: 10px;
    .label {
        display: inline-block;
        width: 280px;
        color: rgba(153, 153, 153, 1);
    }
}
.item-outer-pic {
    display: flex;
    padding-left: 15px;
    padding-top: 10px;
    margin: 5px 0px;
    .label {
        display: inline-block;
        width: 180px;
        color: rgba(153, 153, 153, 1);
    }
}
.append_list {
    display: flex;
    flex-wrap: wrap;
    .imgWrapper {
        margin-right: 10px;
        margin-bottom: 10px;
        width: 80px;
        height: 80px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
.audit-wrapper {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .wrapper {
        padding-top: 20px;
        padding-left: 20px;
    }
    .files {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 10px;
        .item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            margin-right: 20px;
            cursor: pointer;
            border: 1px solid #eee;
            padding: 0 10px;
            border-radius: 10px;
            margin-bottom: 5px;
            span {
                margin-left: 10px;
                color: @warning-color;
                font-size: 18px;
            }
        }
    }
    .examine-wrap {
        margin-top: 20px;
        padding-left: 25px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
