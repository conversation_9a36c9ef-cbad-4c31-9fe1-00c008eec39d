<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>申请信息</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">员工姓名：</span>
                <span>{{ applicationList.name }}</span>
            </div>
            <div class="item-flex">
                <span class="label">身份证号：</span>
                <span>{{ applicationList.certificateNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">性别：</span>
                <span>{{ applicationList.sex == 1 ? '男' : '女' }}</span>
            </div>
            <div class="item-flex">
                <span class="label">联系方式：</span>
                <span>{{ applicationList.phone }}</span>
            </div>
            <div class="item-flex">
                <span class="label">岗位：</span>
                <span>{{ applicationList.professionName }}</span>
            </div>
            <div class="item-flex">
                <span class="label">基本工资：</span>
                <span>{{ applicationList.basicWage }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同开始日期：</span>
                <span>{{ applicationList.contractStartDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">合同结束日期：</span>
                <span>{{ applicationList.contractEndDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">社保基数：</span>
                <span>{{ applicationList.socialSecurityCardinal }}</span>
            </div>
            <div class="item-flex">
                <span class="label">医保基数：</span>
                <span>{{ applicationList.medicalInsuranceCardinal }}</span>
            </div>
            <div class="item-flex">
                <span class="label">公积金基数：</span>
                <span>{{ applicationList.accumulationFundCardinal }}</span>
            </div>
            <div class="item-flex"></div>
            <div class="item-flex">
                <span class="label">产假开始日期：</span>
                <span>{{ applicationList.maternityLeaveStartDate }}</span>
            </div>
            <div class="item-flex">
                <span class="label">产假结束日期：</span>
                <span>{{ applicationList.maternityLeaveEndDate }}</span>
            </div>
        </div>
        <slot :detailData="detailData"></slot>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue'
export default defineComponent({
    name: 'ApplicationInfo',
    props: {
        applicationList: {
            type: Object,
        },
    },
    setup() {
        const detailData = ref<inObject>({
            date: '2021-10-01',
            description:
                'aljksdaipshjlhioho开始努力扩大鸡排的按时间都库按时吃枯燥库活动库阿萨德加厚意外哦 到is回家啊搜到回家哦库暗示大家哦库爱仕达卡拉是从哪  ',
        })

        return {
            detailData,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
