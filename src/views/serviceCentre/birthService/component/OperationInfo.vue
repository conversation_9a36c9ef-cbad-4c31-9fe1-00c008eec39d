<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span>操作信息</span>
        <div class="examine-wrap">
            <p class="linefeed"></p>
            <template v-for="(item, index) in operationLog" :key="'operation' + index">
                <div class="operation_item">
                    <span class="value">{{ index + 1 }}</span>
                    <span class="label">操作人：</span>
                    <span class="value">{{ item.realName || '系统' }}</span>
                    <span class="label">操作时间：</span>
                    <span class="value">{{ item.createdDate }}</span>
                    <span class="label">操作信息：</span>
                    <span class="value">{{ item?.message?.split('####')[0] || '' }}</span>
                    <span class="label" v-if="item?.remark != null">备注：</span>
                    <span class="value">{{ item?.remark }}</span>
                    <Divider class="divid_item" v-if="index !== operationLog.length - 1" />
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
export default defineComponent({
    name: 'OperationInfo',
    props: {
        operationLog: {
            type: Array,
        },
    },
    setup() {
        // const operationLog = ref<inObject[]>([])

        return {
            // operationLog,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    .operation_item {
        font-size: 14px;
        .label {
            color: rgba(153, 153, 153, 1);
        }
        .value {
            font-weight: 600;
            color: rgba(51, 51, 51, 1);
            margin-right: 20px;
        }
        .divid_item {
            margin: 10px 0;
        }
    }
}
</style>
