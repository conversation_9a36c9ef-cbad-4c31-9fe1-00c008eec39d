<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <template v-if="viewType === 'add'">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; overflow-x: auto; margin-top: 20px; margin-right: 20px"
                class="form-flex"
            >
                <EmployeeMatching
                    :formItemNames="['name', 'certificateNum']"
                    v-model:name="formData.name"
                    v-model:card="formData.certificateNum"
                    @changeCertificateNum="changeCertificateNum"
                />
                <template v-for="i in myOptions" :key="i">
                    <MyFormItem
                        :width="i.width"
                        :item="i"
                        v-model:value="formData[i.name]"
                        :class="i.slots"
                        v-if="i.show != false"
                    />
                </template>
            </Form>
        </template>
        <template v-else-if="viewType === 'see'">
            <StepBar :stepsData="stepsData" :currentStep="currentStep" labelPlacement="vertical" />
            <ApplicationInfo :viewType="viewType" :applicationList="applicationList" />
            <OperationInfo :operationLog="operationLog" />
        </template>

        <template #footer>
            <div v-if="viewType === 'add'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import StepBar from '../../retirement/component/StepBar.vue'
import ApplicationInfo from '../component/ApplicationInfo.vue'
import OperationInfo from '../component/OperationInfo.vue'
import request from '/@/utils/request'
import inductionApplyStore from '/@/store/modules/inductionApply'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'MaternityLeaveModal',
    components: { StepBar, ApplicationInfo, OperationInfo },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { viewType, title, item } = toRefs(props)

        const sexList = ref<object[]>([]) //性别
        const staffTypeList = ref<object[]>([]) //人员类型
        const professionNameList = ref<object[]>([]) //岗位
        onMounted(() => {
            // request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
            //     sexList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            dictionaryDataStore()
                .setDictionaryData('sexType')
                .then((res: any) => {
                    sexList.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('staffType')
                .then((res: any) => {
                    staffTypeList.value = res
                })
            dictionaryDataStore()
                .setDictionaryData('hr-stations', '/api/hr-stations/list')
                .then((res: any) => {
                    professionNameList.value = res.map((item) => {
                        return { label: item.professionName, value: item.id }
                    })
                })
        })
        //表单数据
        const myOptions = ref([
            {
                label: '姓名',
                name: 'name',
                show: false,
            },
            {
                label: '身份证号',
                name: 'certificateNum',
                show: false,
            },
            {
                label: '性别',
                name: 'sex',
                type: 'change',
                options: sexList,
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'sex'),
                disabled: true,
            },
            {
                label: '联系方式',
                name: 'phone',
                disabled: true,
            },
            {
                label: '岗位',
                name: 'professionName',
                type: 'change',
                options: professionNameList,
                // ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'professionName'),
                disabled: true,
                required: false,
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: staffTypeList,
                trigger: 'change',
                ruleType: 'number',
                onChange: (value, option) => selectChange(value, option, 'personnelType'),
                disabled: true,
                required: false,
            },
            {
                label: '产假开始日期',
                name: 'maternityLeaveStartDate',
                type: 'date',
            },
        ])
        const selectChange = (value, option, name) => {
            console.log(value, option, name)
            formData.value[name + 'Label'] = option.label
        }
        const stepsData = ref([
            {
                title: '公司通知',
            },
            {
                title: '专管员确认产假开始时间',
            },
            {
                title: '员工开始休假',
            },
            {
                title: '发送返岗通知',
            },
            {
                title: '休假结束',
            },
        ])
        //请求
        const { visible } = toRefs(props)
        const currentStep = ref<number>()

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        //申请信息数据
        const applicationList = ref<any>({})
        //操作信息数据
        const operationLog = ref<any>([])
        //获取详情数据
        const getData = () => {
            request.post('/api/hr-fertility/detail', { id: item.value?.id }).then((res) => {
                // status状态 0 通知 1确认时间 2 返岗通知
                let date = new Date()
                let month: any = date.getMonth() + 1
                let strDate: any = date.getDate()
                if (month >= 1 && month <= 9) {
                    month = '0' + month
                }
                if (strDate >= 0 && strDate <= 9) {
                    strDate = '0' + strDate
                }
                let currentdate = date.getFullYear() + '-' + month + '-' + strDate
                if (res.status == 0) {
                    //公司通知
                    currentStep.value = 1
                } else if (res.status == 1) {
                    if (currentdate >= res.maternityLeaveStartDate) {
                        //员工开始休假
                        currentStep.value = 3
                    } else {
                        //专管员确认产假开始时间
                        currentStep.value = 2
                    }
                } else if (res.status == 2) {
                    if (currentdate >= res.maternityLeaveEndDate) {
                        //员工休假结束
                        currentStep.value = 5
                    } else {
                        //发送返岗通知
                        currentStep.value = 4
                    }
                }
                applicationList.value = res
                operationLog.value = res.applyOpLogsList
            })
        }
        watch(
            visible,
            () => {
                if (visible.value) {
                    // console.log(item.value)
                    if (item.value?.id) {
                        getData()
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        //自动填入
        const changeCertificateNum = (userInfo) => {
            console.log(userInfo)
            formData.value.sex = userInfo.sex
            formData.value.phone = userInfo.phone
            formData.value.personnelType = userInfo.personnelType
            formData.value.professionName = userInfo.stationId
            formData.value.staffId = userInfo.id
            formValidateOptional(['name', 'sex', 'phone', 'personnelType', 'professionName'])
        }

        // 单个校验
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate(nameList)
            })
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        // cancel handle
        const onCancel = () => {
            // console.log(123)
            emit('cancel')
            if (title.value?.includes('新增')) {
                resetFormData()
            }
        }
        //请求
        const api = '/api/hr-fertility'
        // confirm handle
        const onConfirm = () => {
            let validate = inductionApplyStore().getEmployedStaffList.some((item, i) => {
                // if (editDataIndex.value == i) {
                //     return false
                // }
                return item.certificateNum == formData.value?.certificateNum
            })
            if (validate) {
                message.warning('您输入的身份证号有重复，请重新输入')
                return
            }
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    console.log(formData.value)
                    // formData.value.staffId = '1bfd5ae27db594713bdd12ef1d61ff2f'
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    }
                    onCancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,
            stepsData,
            currentStep,
            applicationList,
            operationLog,
            //身份证号选择
            changeCertificateNum,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
}
// .form-flex {
//     display: flex;
//     align-items: center;
//     flex-wrap: wrap;
//     align-items: flex-start;
//     position: relative;
//     padding-right: 2%;
//     :deep(.ant-form-item) {
//         width: 33.33%;
//     }
// }
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
