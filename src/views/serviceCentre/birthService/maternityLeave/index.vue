<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #professionNameList="itemForm">
            <PostTree
                v-model:value="params.professionNameList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                style="width: 190px"
            />
        </template>
        <template #clientIdList="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                v-model:value="params.clientIdList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                :checkStrictly="false"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'birthService_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'birthService_export'" @click="exportData">{{ exportText }}</Button>
        <Button class="btn" type="primary" v-auth="'birthService_confirm'" @click="batchConfirmNotice('confirm')"
            >批量确认</Button
        >
        <Button class="btn" type="primary" v-auth="'birthService_notice'" @click="batchConfirmNotice('notice')">批量通知</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-fertility/page"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { message, Modal, notification } from 'ant-design-vue'
import { defineComponent, ref, onMounted, onBeforeMount, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import modal from './modal.vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import permissionStore from '/@/store/modules/permission'
import { useRoute } from 'vue-router'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
import { staffStatus } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'MaternityLeave',
    components: { MyModal: modal, ClientSelectTree, PostTree },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false
        let personnelTypeList = ref<LabelValueOptions>([]) //人员类型
        //性别
        let sexList = ref<LabelValueOptions>([
            {
                label: '男',
                value: 1,
            },
            {
                label: '女',
                value: 2,
            },
        ])
        //状态
        let statusList = ref<LabelValueOptions>([])
        //专管员
        let specializedList = ref<LabelValueOptions>([])
        //筛选
        const route = useRoute()
        const params = ref<any>({
            fertilityStatus: route.query?.fertilityStatus ? JSON.parse(route.query?.fertilityStatus as string)[0] : undefined,
        })

        let searchOptions: SearchBarOption[] = [
            {
                type: 'selectSlot',
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                maxTag: 1,
            },
            {
                type: 'string',
                label: '员工姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexList,
                multiple: true,
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'selectSlot',
                label: '岗位',
                key: 'professionNameList',
                placeholder: '岗位',
                maxTag: 1,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: personnelTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '产假开始日期',
                key: 'maternityLeaveStartDateList',
            },
            {
                type: 'daterange',
                label: '产假结束日期',
                key: 'maternityLeaveEndDateList',
            },
            {
                type: 'select',
                label: '状态',
                key: 'fertilityStatus',
                options: statusList,
            },
            {
                type: 'select',
                label: '专管员',
                key: 'specializedList',
                options: specializedList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '员工姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text == 1 ? '男' : '女'
                    }
                    return text
                },
                width: 100,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 100,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 150,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeName
                },
                width: 150,
            },
            {
                title: '产假开始日期',
                dataIndex: 'maternityLeaveStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '产假结束日期',
                dataIndex: 'maternityLeaveEndDate',
                align: 'center',
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                customRender: ({ record }) => {
                    return record.statusName
                },
                width: 150,
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ])

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = searchOptions.findIndex((el) => {
                    return el.key == 'specialized'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'specialized'
                })
                searchOptions.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        onMounted(() => {
            //人员类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                personnelTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            //状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/fertilityStatus', {}).then((res) => {
                statusList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            //专管员
            request.get('/api/users/getSpecialized', {}).then((res) => {
                specializedList.value = res.map((item) => {
                    return { label: item.realName, value: item.id }
                })
            })
        })

        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增生育服务')
        const viewType = ref('add')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增生育服务'
            currentValue.value = null
            viewType.value = 'add'
        }
        const editRow = (viewTypeName, record) => {
            showEdit.value = true
            modalTitle.value = '查看生育服务'
            currentValue.value = { ...record }
            viewType.value = viewTypeName
        }
        const modalCancel = () => {
            showEdit.value = false
        }
        const modalConfirm = () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 单个确认通知
        const confirmNotice = (type, record) => {
            let myStatus: any = null
            let hrFertilityDTOList: any = []

            hrFertilityDTOList = []
            hrFertilityDTOList.push(record)
            function confirmRequest() {
                request.post(`/api/hr-fertility/update?status=${myStatus}`, hrFertilityDTOList).then((res) => {
                    if (res.error_status) {
                        openNotification(res.error_status)
                    }

                    tableRef.value.refresh()
                })
            }
            if (type == 'confirm') {
                // 确认
                myStatus = 1
                Modal.confirm({
                    title: '提示',
                    content: '您是否要确认该员工产假开始时间？',
                    onOk() {
                        confirmRequest()
                    },
                    onCancel() {},
                })
            } else {
                // 通知
                myStatus = 2
                Modal.confirm({
                    title: '提示',
                    content: '您确定要通知该员工返岗吗？',
                    onOk() {
                        confirmRequest()
                    },
                    onCancel() {},
                })
            }
        }

        // 多选
        const selectedRowsArr = ref([])
        // 批量通知确认
        const batchConfirmNotice = (type) => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择员工')
                return false
            }
            let hrFertilityDTOList = [...selectedRowsArr.value]
            let myStatus: any = null
            function confirmRequest() {
                request.post(`/api/hr-fertility/update?status=${myStatus}`, hrFertilityDTOList).then((res: any) => {
                    if (res.error_status) {
                        openNotification(res.error_status)
                    }

                    tableRef.value.refresh()
                })
            }
            if (type == 'confirm') {
                // 确认
                myStatus = 1
                Modal.confirm({
                    title: '提示',
                    content: '您是否要批量确认员工产假开始时间？',
                    onOk() {
                        confirmRequest()
                    },
                    onCancel() {},
                })
            } else {
                // 通知
                myStatus = 2
                Modal.confirm({
                    title: '提示',
                    content: '您确定要批量通知返岗吗？',
                    onOk() {
                        confirmRequest()
                    },
                    onCancel() {},
                })
            }
        }
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '批量处理信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }

        //导入导出
        const exportUrl = '/api/hr-fertility/export'
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 获取当前年月日
        let date = new Date()
        let month: any = date.getMonth() + 1
        let strDate: any = date.getDate()
        if (month >= 1 && month <= 9) {
            month = '0' + month
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = '0' + strDate
        }
        let currentdate = date.getFullYear() + '-' + month + '-' + strDate

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'birthService_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '确认',
                    auth: 'birthService_confirm',
                    show: (record) => {
                        return record.status == 0
                    },
                    class: 'green',
                    click: (record) => confirmNotice('confirm', record),
                },
                {
                    neme: '返岗通知',
                    auth: 'birthService_notice',
                    show: (record) => {
                        return record.status == 1 && currentdate >= record.maternityLeaveStartDate
                    },
                    class: 'green',
                    click: (record) => confirmNotice('notice', record),
                },
            ]),
        )
        return {
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 批量通知确认
            batchConfirmNotice,
            // 单个通知确认
            confirmNotice,
            //新增
            createRow,
            //编辑
            editRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            //导出
            exportData,
            exportUrl,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
</style>
