<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <StepBar :stepsData="stepsData" labelPlacement="vertical" :currentStep="getCurrentStep()" />
        <BasicInfo :detailData="item">
            <template #default="{ detailData }" v-if="modalType == 'look' || modalType == 'audit'">
                <div class="item-flex">
                    <span class="label">调出目的地：</span> <span>{{ detailData?.calloutPlace }}</span>
                </div>
            </template>
        </BasicInfo>
        <AuditProcess v-if="item?.opLogsList && item?.opLogsList?.length" :currentItem="item" :applyOpLogs="item?.opLogsList" />
        <div class="examine" v-if="modalType !== 'look'">
            <Divider type="vertical" class="divid" />
            <span class="title">{{ modalType == 'audit' ? `${role}审核` : '提取人信息' }}</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form
                    ref="formInline"
                    :model="formData"
                    :label-col="{ style: { width: modalType == 'audit' ? '120px' : '80px' } }"
                    :rules="rules"
                    class="form-flex"
                >
                    <!-- 专管员 -->
                    <FormItem
                        v-if="statusStr == 'auditor-review'"
                        label="是否需要客户审核"
                        name="needCustomerCheck"
                        style="width: 100%"
                    >
                        <Switch
                            v-model:checked="formData.needCustomerCheck"
                            checked-children="是"
                            un-checked-children="否"
                            :checkedValue="true"
                            :unCheckedValue="false"
                            :getPopupContainer="getPopupContainer"
                        />
                    </FormItem>
                    <FormItem label="附件" name="appendixIdList" style="width: 100%">
                        <ImportFile v-model:fileUrls="formData.appendixIdList" ref="refImportFile" />
                    </FormItem>
                    <!-- 档案管理员 -->
                    <div class="inline-div" v-if="statusStr == 'archives-manager-review'">
                        <FormItem label="是否需要归还" name="needReturn" style="width: 100%">
                            <Switch
                                v-model:checked="formData.needReturn"
                                checked-children="是"
                                un-checked-children="否"
                                :checkedValue="true"
                                :unCheckedValue="false"
                                :getPopupContainer="getPopupContainer"
                                @change="onSwitchChange"
                                style="margin-right: 15px"
                            />
                        </FormItem>
                        <FormItem label="预计归还时间" v-if="dateVisible" name="returnTime" style="width: 100%">
                            <DatePicker
                                v-model:value="formData.returnTime"
                                format="YYYY-MM-DD"
                                placeholder="请选择预计归还时间"
                                valueFormat="YYYY-MM-DD"
                                :getPopupContainer="getPopupContainer"
                                :disabled-date="disableDate"
                            />
                        </FormItem>
                    </div>
                    <template v-if="modalType == 'extract'">
                        <FormItem label="调出目的地" name="calloutPlace" style="width: 100%">
                            <Input v-model:value="formData.calloutPlace" allowClear placeholder="请输入调出目的地" />
                        </FormItem>
                        <FormItem label="备注" name="remark" style="width: 100%">
                            <Textarea v-model:value="formData.remark" :rows="3" allowClear placeholder="请输入备注" />
                        </FormItem>
                    </template>
                    <div class="inline-div" v-if="modalType == 'audit'">
                        <FormItem label="备注" name="remark" style="width: 100%">
                            <Textarea v-model:value="formData.remark" :rows="3" allowClear placeholder="请输入备注" />
                        </FormItem>
                        <FormItem label="拒绝理由" name="checkerReason" style="width: 100%">
                            <Textarea v-model:value="formData.checkerReason" :rows="3" allowClear placeholder="请输入拒绝理由" />
                        </FormItem>
                    </div>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="modalType == 'extract'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm(true)" type="primary" class="btn">确定</Button>
            </div>
            <div v-else-if="modalType == 'audit'">
                <Button @click="onConfirm(false)" class="delBtn">拒绝</Button>
                <Button @click="onConfirm(true)" type="primary" class="btn">通过</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import StepBar from '../retirement/component/StepBar.vue'
import BasicInfo from './component/BasicInfo.vue'
import AuditProcess from './component/AuditProcess.vue'
import { valuesAndRules } from '/#/component'
import { message } from 'ant-design-vue'
import { Moment } from 'moment'
import moment from 'moment'
export default defineComponent({
    name: 'ArchivesModal',
    components: { StepBar, BasicInfo, AuditProcess },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { modalType, item } = toRefs(props)
        const refImportFile = ref()
        const dateVisible = ref(false)
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '调出目的地',
                name: 'calloutPlace',
                required: false,
            },
            {
                label: '是否需要客户审核',
                name: 'needCustomerCheck',
                type: 'switch',
                required: false,
                ruleType: 'boolean',
                default: false,
            },
            {
                label: '是否需要归还',
                name: 'needReturn',
                type: 'switch',
                required: false,
                ruleType: 'boolean',
                default: false,
            },
            {
                label: '附件',
                name: 'appendixIdList',
                default: [],
                ruleType: 'array',
                required: false,
            },
            {
                label: '预计归还时间',
                name: 'returnTime',
            },
            {
                label: '备注',
                name: 'remark',
                required: false,
            },
            {
                label: '拒绝理由',
                name: 'checkerReason',
                required: false,
            },
        ])
        // opt 是否通过
        const stepsData = ref([
            {
                title: '发起申请',
            },
            {
                title: '待专管员审核',
            },
            {
                title: '待客户审核',
            },
            // {
            //     title: '待客服经理审核',
            // },
            {
                title: '待档案管理员审核',
            },
            {
                title: '待提取',
            },
            {
                title: '已结束',
            },
        ])

        const role = ref('')
        const statusStr = ref('')
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        // cancel handle
        const onCancel = () => {
            dateVisible.value = false
            emit('cancel')
            resetFormData()
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            modalType.value !== 'look' && formInline.value.resetFields()
        }

        const getRoleName = (val) => {
            if (val.status == 1) {
                role.value = '专管员'
                statusStr.value = 'auditor-review'
            } else if (val.status == 3) {
                role.value = ''
                statusStr.value = 'customer-review'
            } else if (val.status == 5) {
                role.value = '客服经理'
                statusStr.value = 'manager-review'
            } else if (val.status == 7) {
                role.value = '档案管理员'
                statusStr.value = 'archives-manager-review'
            } else if (val.status == 9) {
                statusStr.value = 'pick-up'
            }
        }

        watch(
            item,
            (val) => {
                if (val) {
                    getRoleName(val)
                    stepsData.value = [
                        {
                            title: '发起申请',
                        },
                        {
                            title: '待专管员审核',
                        },
                        {
                            title: '待客户审核',
                        },
                        // {
                        //     title: '待客服经理审核',
                        // },
                        {
                            title: '待档案管理员审核',
                        },
                        {
                            title: '待提取',
                        },
                        {
                            title: '已结束',
                        },
                    ]
                    if (!val.isCustomerReviewed) {
                        let ind = stepsData.value.findIndex((el) => {
                            return el.title == '待客户审核'
                        })
                        ind != -1 && stepsData.value.splice(ind, 1)
                    }
                    if (!val.isPickUp) {
                        let ind = stepsData.value.findIndex((el) => {
                            return el.title == '待提取'
                        })
                        ind != -1 && stepsData.value.splice(ind, 1)
                    }
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        const getCurrentStep = () => {
            // 1 3 5 7 9
            if (!item.value.isCustomerReviewed) {
                if (item.value.status == 3) return 1
                else if (item.value.status == 7) return 2
                else if (item.value.status == 9) return 4
                else if (item.value.status == 2 || item.value.status == 4 || item.value.status == 6 || item.value.status == 8)
                    return 10
                else return item.value.status
            } else if (item.value.isCustomerReviewed && !item.value.isPickUp) {
                if (item.value.status == 3) return 2
                else if (item.value.status == 5) return 3
                else if (item.value.status == 7) return 4
                else if (item.value.status == 9) return 5
                else if (item.value.status == 2 || item.value.status == 4 || item.value.status == 6 || item.value.status == 8)
                    return 10
                else return item.value.status
            } else {
                if (item.value.status == 3) return 2
                else if (item.value.status == 5) return 3
                else if (item.value.status == 7) return 4
                else if (item.value.status == 9) return 5
                else if (item.value.status == 2 || item.value.status == 4 || item.value.status == 6 || item.value.status == 8)
                    return 10
                else return item.value.status
            }
        }

        const onSwitchChange = (checked) => {
            dateVisible.value = checked
            formData.value.returnTime = ''
        }

        const disableDate = (endValue: Moment) => {
            return endValue && endValue < moment().startOf('day')
        }

        // confirm handle
        const onConfirm = (flag) => {
            let myUrl = []
            if (formData.value.appendixIdList.length) {
                myUrl = refImportFile.value.getFileUrls().map((item) => {
                    return item.id
                })
            }
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (!flag) {
                        if (isEmpty(formData.value.checkerReason)) {
                            message.warn('请输入拒绝理由')
                        } else {
                            emit(
                                'confirm',
                                { ...formData.value, id: item.value.id, appendixIdList: myUrl, opt: flag },
                                statusStr.value,
                            )
                            resetFormData()
                        }
                    } else {
                        emit(
                            'confirm',
                            { ...formData.value, id: item.value.id, appendixIdList: myUrl, opt: flag },
                            statusStr.value,
                        )
                        resetFormData()
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            dateVisible,
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            formInline,
            stepsData,
            role,
            statusStr,
            refImportFile,

            getCurrentStep,
            onSwitchChange,
            disableDate,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .title {
        font-weight: bold;
    }
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.item-flex {
    width: 25%;
    margin: 5px 0px;
    display: flex;
    .label {
        text-align: right;
        width: 120px;
        color: rgba(153, 153, 153, 1);
    }
}
.inline-div {
    display: flex;
    width: 100%;
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
//step标题行高
:deep(.my_steps .ant-steps-item-title) {
    line-height: 20px;
}
</style>
