<template>
    <div class="examine">
        <Divider type="vertical" class="divid" />
        <span class="title">基本信息</span>
        <div class="examine-flex">
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">档案申请：</span>
                <span>{{ detailData?.archivesName }}</span>
            </div>
            <div class="item-flex">
                <span class="label">档案编号：</span>
                <span>{{ detailData?.systemNum }}</span>
            </div>
            <div class="item-flex">
                <span class="label">档案类型：</span>
                <span>{{ detailData?.archivesTypeLabel }}</span>
            </div>
            <div class="item-flex">
                <span class="label">借阅类型：</span>
                <span>{{ detailData?.type == 1 ? '离职提档' : '在职借阅' }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">操作事由：</span>
                <span>档案借阅</span>
            </div>
            <div class="item-flex">
                <span class="label">申请理由简述：</span>
                <span>{{ detailData?.title }}</span>
            </div>
            <div class="item-flex">
                <span class="label">申请人：</span>
                <span>{{ detailData?.staffName }}</span>
            </div>
            <div class="item-flex">
                <span class="label">申请人手机号：</span>
                <span>{{ detailData?.phone }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex">
                <span class="label">申请人身份证号：</span>
                <span>{{ detailData?.certificateNum }}</span>
            </div>
            <div class="item-flex-reason">
                <span class="label">申请理由明细：</span>
                <span style="flex: 1">{{ detailData?.detail }}</span>
            </div>
            <p class="linefeed"></p>
            <div class="item-flex-detail">
                <span class="label">档案明细：</span>
                <!-- <div v-if="!detailData?.archivesDetailList">
                    {{ detailData.typeListStr }}
                </div> -->
                <div class="details" v-if="detailData?.archivesDetailList && detailData?.archivesDetailList.length">
                    <div v-for="ele in detailData.archivesDetailList" :key="ele.id" class="wrapper">
                        <span class="name">{{ ele.name }}</span>
                        <span class="type">{{ getMaterialType(ele.type) }}</span>
                        <div class="append_list">
                            <span v-for="el in ele.appendixList" :key="el.id">
                                <a href="javascript: void(0)" @click="previewFile(el.fileUrl)" style="margin-right: 25px">
                                    {{ `${el.originName};` }}
                                </a>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <slot :detailData="detailData"></slot>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue'
import { materialTypeList } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils'
export default defineComponent({
    name: 'BasicInfo',
    props: {
        detailData: {
            type: Object,
            default: () => {},
        },
    },
    setup(props) {
        const { detailData } = toRefs(props)
        const archivesDetail = ref('')

        const getMaterialType = (type) => {
            return materialTypeList.find((el) => {
                return el.value == type
            })?.label
        }
        return {
            archivesDetail,
            getMaterialType,
            previewFile,
        }
    },
})
</script>

<style scoped lang="less">
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .title {
        font-weight: bold;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        // padding-left: 15px;
        .item-flex-detail {
            margin: 5px 0px;
            display: flex;
            .details {
                display: flex;
                flex-direction: column;
                .name {
                    width: 180px;
                }
                .type {
                    width: 50px;
                    margin-right: 20px;
                }
                .append_list {
                    flex: 1;
                    display: flex;
                    flex-wrap: wrap;
                }
                .wrapper {
                    flex: 1;
                    display: flex;
                }
            }
        }
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            display: flex;
        }
        .item-flex-reason {
            width: 75%;
            margin: 5px 0px;
            display: flex;
        }
        .label {
            text-align: right;
            width: 120px;
            color: rgba(153, 153, 153, 1);
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
