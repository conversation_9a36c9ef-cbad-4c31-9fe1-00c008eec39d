<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="addArchives">新增</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>

        <Button type="primary" v-auth="'archives_batch_pass'" @click="batchAuditOperation(1)" class="downloadBtn">
            批量通过
        </Button>
        <Button type="primary" v-auth="'archives_batch_reject'" @click="batchReject()" class="delBtn"> 批量拒绝 </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-lending-applies/page"
        deleteApi="/api/hr-lending-applies/deletes"
        :params="params"
        :columns="columns"
        exportUrl="/api/hr-lending-applies/export"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="() => (showReject = false)"
        @confirm="batchAuditOperation(0)"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>

    <MyModal
        :visible="modalVisible"
        :modalType="detailType"
        :item="currentValue"
        :title="modalTitle"
        @cancel="cancelHandle"
        @confirm="executeOperation"
    />
    <AddModal
        :visible="showAddArchives"
        :item="currentValue"
        :title="modalTitle"
        @cancel="archivesModalCancel"
        @confirm="archivesModalConfirm"
    />
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import permissionStore from '/@/store/modules/permission'
import { SearchBarOption } from '/#/component'
import { archivesTypeList, staffStatus } from '/@/utils/dictionaries'
import AddModal from './addModal.vue'
import modal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, openNotification, getDynamicText } from '/@/utils'
import { useRoute } from 'vue-router'
export default defineComponent({
    name: 'ArchivesIndex',
    components: {
        MyModal: modal,
        AddModal,
    },
    setup() {
        const RoleState = permissionStore().getPermission // 客户=>false

        // 筛选
        let statusTypeList = ref<LabelValueOptions>([]) // 档案借阅状态
        let operationalList = ref<LabelValueOptions>([]) // 操作事由

        //表格数据
        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 130,
            },
            {
                title: '员工编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 160,
            },
            {
                title: '姓名',
                dataIndex: 'staffName',
                align: 'center',
                width: 160,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return staffStatus.find((el) => {
                        return el.value == text
                    })?.label
                },
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '档案编号',
                dataIndex: 'archivesNum',
                align: 'center',
                width: 160,
            },
            {
                title: '申请理由',
                dataIndex: 'title',
                align: 'center',
                width: 160,
            },
            {
                title: '材料明细',
                dataIndex: 'typeListStr',
                align: 'center',
                width: 160,
                ellipsis: true,
            },
            {
                title: '申请时间',
                dataIndex: 'startDate',
                align: 'center',
                width: 160,
            },
            {
                title: '专管员',
                dataIndex: 'specialized',
                align: 'center',
                width: 130,
            },
            {
                title: '状态',
                dataIndex: 'applyStatus',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return statusTypeList.value.find((el) => {
                        return record.applyStatus == el.value
                    })?.label
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
                fixed: 'right',
            },
        ])

        let options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'staffName',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStatus,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '申请理由',
                key: 'title',
            },
            {
                type: 'string',
                label: '材料明细',
                key: 'typeListStr',
            },
            {
                type: 'daterange',
                label: '申请时间',
                key: 'applyDateList',
            },
            {
                type: 'select',
                label: '状态',
                key: 'stateList',
                multiple: true,
                options: statusTypeList,
            },
        ]

        /* onBeforeMount(() => {
            if (!RoleState.staffState) {
                let index = options.findIndex((el) => {
                    return el.key == 'status'
                })
                options.splice(index, 1)
            }
        }) */

        onMounted(() => {
            // 档案借阅状态
            dictionaryDataStore()
                .setDictionaryData('lendingStatus', '')
                .then((data: inObject[]) => {
                    statusTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 操作事由
            dictionaryDataStore()
                .setDictionaryData('operationReason', '')
                .then((data: inObject[]) => {
                    operationalList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        //表格dom
        const tableRef = ref()
        //筛选
        const route = useRoute()
        const params = ref({
            stateList: route.query?.stateList ? JSON.parse(route.query?.stateList as string) : undefined,
        })

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const modalTitle = ref('')
        const modalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<inObject>({})

        const showArchivesBatch = ref<any>(false)
        const addArchivesBatch = () => {
            showArchivesBatch.value = true
            modalTitle.value = '新增档案借阅'
        }

        const archivesBatchCancel = () => {
            showArchivesBatch.value = false
        }
        const archivesBatchConfirm = () => {
            showArchivesBatch.value = false
            tableRef.value.refresh(1)
        }
        // 显示弹窗
        const showModal = (record, type) => {
            modalVisible.value = true
            currentValue.value = record
            switch (type) {
                case 'look':
                    modalTitle.value = '查看'
                    break
                case 'audit':
                    modalTitle.value = '档案借阅审核'
                    break
                case 'extract':
                    modalTitle.value = '档案提取'
                    break
            }
            getDetails(record.id)
            detailType.value = type
        }

        // 获取详细信息
        const getDetails = (id) => {
            request
                .get(`/api/hr-lending-applies/${id}`)
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        archivesTypeLabel: archivesTypeList.find((el) => {
                            return el.value == res.archivesType
                        })?.label,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = () => {
            modalVisible.value = false
            checkerReason.value = ''
        }

        // 新增
        const showAddArchives = ref<any>(false)
        const addArchives = () => {
            showAddArchives.value = true
            modalTitle.value = '新增'
        }

        const archivesModalCancel = () => {
            showAddArchives.value = false
        }

        const archivesModalConfirm = () => {
            showAddArchives.value = false
            tableRef.value.refresh(1)
        }
        // 多选
        const selectedRowsArr = ref([])

        // 确认信息
        const confirmInfoRow = (record?) => {
            let ids: any[] = []
            if (record) {
                ids.push(record.id)
            } else {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请先选择人员!')
                    return
                }
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.staffId
                })
            }
        }
        const getStatusName = () => {
            if (currentValue.value.status == 1) {
                return 'auditor-review'
            } else if (currentValue.value.status == 3) {
                return 'customer-review'
            } else if (currentValue.value.status == 5) {
                return 'manager-review'
            } else if (currentValue.value.status == 7) {
                return 'archives-manager-review'
            } else if (currentValue.value.status == 9) {
                return 'pick-up'
            }
        }
        const getRoleId = () => {
            if (RoleState.roleKey == 'super_admin' || RoleState.roleKey == 'maintenance') return 10
            if (RoleState.roleKey == 'customer_service_staff') return 0
            if (RoleState.roleKey == 'client') return 1
            if (RoleState.roleKey == 'customer_service_manager') return 2
            if (
                RoleState.roleKey == 'general_manager' ||
                RoleState.roleKey == 'general_staff' ||
                RoleState.roleKey == 'danganguanliyuan'
            )
                return 3
            if (RoleState.roleKey == 'zhuwaizhuanyuanA') return 4
        }
        const auditVisible = (record) => {
            if (
                ((getRoleId() == 0 || getRoleId() == 2) && record.status == 1) ||
                (getRoleId() == 1 && record.status == 3) ||
                (getRoleId() == 2 && record.status == 5) ||
                (getRoleId() == 3 && record.status == 7) ||
                (getRoleId() == 4 && record.status == 1) ||
                (getRoleId() == 10 && (record.status == 1 || record.status == 3 || record.status == 5 || record.status == 7))
            )
                return true
            else return false
        }
        const executeOperation = (obj, status) => {
            let msg = ''
            if (status == 'pick-up') {
                msg = '档案提取成功'
            } else {
                if (obj.opt) msg = '审核已通过'
                else msg = '审核已拒绝'
            }
            request
                .post(`/api/hr-lending-applies/${status}`, { applyIdList: [obj.id], ...obj })
                .then((res) => {
                    tableRef.value.refresh()
                    cancelHandle()
                    message.success(msg)
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const showReject = ref(false)
        const checkerReason = ref('')
        const batchReject = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请选择要拒绝的申请')
                return false
            }
            modalTitle.value = '批量拒绝'
            showReject.value = true
        }

        // 批量审核
        const batchAuditOperation = (opt) => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请选择员工!')
                return
            }
            let success = ''
            if (!opt) {
                if (!checkerReason.value) {
                    message.warning('请填写拒绝理由')
                    return false
                }
                success = '所选数据审核已拒绝'
                showReject.value = false
            } else {
                success = '所选数据审核已通过'
            }
            request
                .post('/api/hr-lending-applies/batch-pass', {
                    applyIdList: selectedRowsArr.value.map((el: inObject) => {
                        return el.id
                    }),
                    opt: Number(opt),
                    needCustomerCheck: false,
                    needReturn: false,
                    roleId: getRoleId(),
                    checkerReason: checkerReason.value,
                })
                .then((res) => {
                    tableRef.value.refresh()
                    let tip = ''
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他员工已成功通知'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    cancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'archives_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '审核',
                    auth: 'archives_audit',
                    show: (record) => {
                        return auditVisible(record)
                    },
                    click: (record) => showModal(record, 'audit'),
                },
                {
                    neme: '提取',
                    auth: 'archives_extract',
                    show: (record) => {
                        return record.status == 9
                    },
                    click: (record) => showModal(record, 'extract'),
                },
            ]),
        )
        return {
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            currentValue,
            detailType,
            // 操作
            myOperation,
            selectedRowsArr,
            checkerReason,
            // 事件
            searchData,
            cancelHandle,
            showReject,
            batchReject,

            confirmInfoRow,
            executeOperation,
            batchAuditOperation,
            getStatusName,
            exportText,
            exportData,
            addArchives,
            showAddArchives,
            archivesModalCancel,
            archivesModalConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
