<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'talent_add'" @click="addtalent">新增</Button>
        <Button type="primary" v-auth="'talent_import'" @click="importData">导入</Button>
        <Button type="primary" v-auth="'talent_export'" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" @click="deleteRow" v-auth="'talent_delete'">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-talent-staffs/page"
        deleteApi="/api/hr-talented-person/deletes"
        :exportUrl="exportUrl"
        :params="{ ...params, izDefault: true }"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" v-auth="'staff_detail_list'" @click="editRow(record)">查看</Button>
            &nbsp;
            <Button type="primary" size="small" v-auth="'staff_edit_list'" @click="editRow(record)">编辑</Button>
            &nbsp;
            <Button danger type="primary" size="small" v-auth="'staff_delete_list'" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>
    <AddStaff
        :visible="showAdd"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        typePage="talent"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>
<script lang="ts">
// import { Modal, Tag } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import addStaff from '../staff/staff/addStaff.vue'
// import moment from 'moment'
import { SearchBarOption } from '/#/component'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'TalentList',
    components: { AddStaff: addStaff },
    setup() {
        let sexTypeList = ref<LabelValueOptions>([]) //性别
        let station = ref<LabelValueOptions>([]) // 岗位
        let educationList = ref<LabelValueOptions>([]) // 最高学历
        let workList = ref<LabelValueOptions>([]) // 工作状态

        onMounted(() => {
            //性别
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 岗位
            request.get('/api/hr-stations/list', {}).then((res) => {
                station.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
            // 最高学历
            request.get('/api/com-code-tables/getCodeTableByInnerName/educationStates', {}).then((res) => {
                educationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 工作状态
            request.get('/api/com-code-tables/getCodeTableByInnerName/workStatus', {}).then((res) => {
                workList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '系统编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 200,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ record }) => {
                    return record.sexLabel
                },
                width: 80,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 200,
            },
            {
                title: '出生日期',
                dataIndex: 'birthday',
                align: 'center',
                width: 150,
            },
            {
                title: '年龄',
                dataIndex: 'age',
                align: 'center',
                sorter: false,
                width: 80,
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '通讯地址',
                dataIndex: 'contactAddress',
                align: 'center',
                width: 200,
            },
            {
                title: '最高学历',
                dataIndex: 'highestEducation',
                align: 'center',
                customRender: ({ record }) => {
                    return record.highestEducationLabel
                },
                width: 100,
            },
            {
                title: '适配岗位',
                dataIndex: 'professionName',
                align: 'center',
                customRender: ({ record }) => {
                    return record.adaptationPosition
                },
                width: 100,
            },
            {
                title: '工作状态',
                dataIndex: 'workStatus',
                align: 'center',
                customRender: ({ record }) => {
                    return record.workStatusLabel
                },
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        const showAdd = ref(false)
        const modalTitle = ref('编辑人才')
        // 当前编辑的数据
        const currentValue = ref(null)
        const viewType = ref('')
        const changeSex = (value: any) => {
            ;(params.value as any).sex = value.option.value
        }
        const changeStaffStatus = (value: any) => {
            ;(params.value as any).staffStatus = value.option.value
        }
        const changeStaffType = (value: any) => {
            ;(params.value as any).personnelType = value.option.value
        }

        const birthdayChange = (value: any) => {
            params.value.birthday = value
        }
        // 编辑
        const editRow = (record, type) => {
            showAdd.value = true
            modalTitle.value = type == 'edit' ? '编辑人才' : '查看人才'
            currentValue.value = { ...record }
            viewType.value = type
        }
        const addtalent = () => {
            viewType.value = 'edit'
            showAdd.value = true
            modalTitle.value = '新增人才'
            currentValue.value = null
        }
        //批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {})
        }

        // 关闭弹窗
        const modalCancel = () => {
            showAdd.value = false
            modalTitle.value = '编辑人才'
            currentValue.value = null
        }
        // 确认弹窗
        const modalConfirm = async () => {
            tableRef.value.refresh()
        }
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-talent/template'
        const importUrl = '/api/hr-talent/import'
        const exportUrl = '/api/hr-talent/export'
        const importData = () => {
            importVisible.value = true
        }
        // 下载
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const params = ref({
            systemNum: undefined,
            name: undefined,
            sex: undefined,
            birthday: undefined,
            certificateNum: undefined,
            phone: undefined,
            highestEducation: undefined,
            // stationList: undefined,
            workStatus: undefined,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '系统编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'date',
                label: '出生日期',
                key: 'birthday',
                allowClear: true,
            },
            {
                type: 'string',
                label: '手机号码',
                key: 'phone',
            },
            {
                type: 'select',
                label: '最高学历',
                key: 'highestEducationList',
                options: educationList,
                multiple: true,
            },
            {
                type: 'select',
                label: '工作状态',
                key: 'workStatusList',
                options: workList,
                multiple: true,
            },
            {
                type: 'string',
                label: '适配岗位',
                key: 'professionName',
                // options: station,
            },
            {
                type: 'string',
                label: '通讯地址',
                key: 'contactAddress',
            },
        ]

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'talent_edit',
                    show: true,
                    click: (record) => editRow(record, 'edit'),
                },
                {
                    neme: '查看',
                    auth: 'talent_show',
                    show: true,
                    click: (record) => editRow(record, 'see'),
                },
                // {
                //     neme: '删除',
                //     auth: 'talent_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        return {
            // data
            exportText,
            selectedRowsArr,
            tableRef,
            columns,
            params,
            options,
            showAdd,
            viewType,
            modalTitle,
            currentValue,
            // 事件
            searchData,
            editRow,
            deleteRow,
            modalCancel,
            modalConfirm,
            changeSex,
            changeStaffStatus,
            changeStaffType,
            birthdayChange,
            addtalent,
            //导入
            importVisible,
            importTemUrl,
            importUrl,
            importData,
            exportData,
            exportUrl,
            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less">
.dateItem {
    margin-right: 10px;
    margin-bottom: 10px;
    & > .name {
        margin-right: 10px;
    }
    & > .input {
        width: 200px;
    }
}
</style>
