<template>
    <div class="main">
        <div class="title">常用功能</div>
        <div class="content">
            <div class="item noselect" v-for="i in funList" :key="i.name" @click="navTo(i)">
                <img :src="useIcons(i.meta.title)" alt="" />
                <div class="name" :title="i.meta.title">{{ i.meta.title }}</div>
            </div>
            <div class="noData" v-if="!funList.length">还没有设置常用功能哦！</div>
        </div>
        <div class="set-btn" @click="setHandle">
            <SettingOutlined />
            <span class="txt">常用功能设置</span>
        </div>
    </div>
    <!-- Drawer -->
    <Drawer title="常用功能" v-model:visible="showDialog" width="1000">
        <div class="dialog-main">
            <div class="header">
                <div class="tit">
                    已选 <span class="num">{{ selFun.length }}</span> / 6
                </div>
                <div class="selList">
                    <div
                        class="item noselect"
                        v-for="(s, index) in selFun || []"
                        :key="s.name"
                        draggable="true"
                        @drag="dragHandle"
                        @dragstart="dragstartHandle($event, index)"
                        @dragend="dragendHandle"
                        @dragover="dragoverHandle"
                        @dragenter="dragenterHandle($event, index)"
                        @dragleave="dragleaveHandle($event, index)"
                        @drop="dropHandle"
                    >
                        <img draggable="false" :src="useIcons(s.meta.title)" alt="" />
                        <div class="name">{{ s.meta.title }}</div>
                        <MinusCircleOutlined class="icon del" @click="delItem(s)" />
                    </div>
                </div>
            </div>
            <div class="list">
                <div class="cell" v-for="i in accessRoutes" :key="i.name">
                    <div class="cell-title">{{ i.meta.title }}</div>
                    <div class="menus">
                        <div class="item" v-for="m in i.children || []" :key="m.name">
                            <img :src="useIcons(m.meta.title)" alt="" />
                            <div class="name" :title="m.meta.title">{{ m.meta.title }}</div>
                            <PlusCircleOutlined
                                v-if="selFunNames.length < 6 && !selFunNames.includes(m.name)"
                                class="icon"
                                @click="selItem(m)"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer">
                <Button @click="modalClose">取消</Button>
                <Button type="primary" @click="modalConfirm">保存</Button>
            </div>
        </div>
    </Drawer>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { SettingOutlined, PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import { Drawer, message } from 'ant-design-vue'
import useCacheStore from '/@/store/modules/cache'
import { useRouter } from 'vue-router'
import request from '/@/utils/request'
import { useIcons } from '/@/utils/index'

export default defineComponent({
    name: 'CommonFun',
    components: {
        SettingOutlined,
        PlusCircleOutlined,
        MinusCircleOutlined,
        Drawer,
    },
    setup() {
        const funList = ref<any[]>([])
        const selFun = ref<any[]>([])

        const selFunNames = computed(() => selFun.value.map((i) => i.name))

        const accessRoutes = useCacheStore().getAccessRoutes

        const showDialog = ref(false)
        const setHandle = () => {
            showDialog.value = true
            selFun.value = [...funList.value]
        }

        const selItem = (route) => {
            selFun.value.push(route)
        }
        const delItem = (route) => {
            const idx = selFun.value.findIndex((i) => i.name == route.name)
            idx != -1 && selFun.value.splice(idx, 1)
        }

        const modalClose = () => {
            showDialog.value = false
            selFun.value = []
        }
        const modalConfirm = async () => {
            await request.post(`/api/hr-common-functionses`, {
                commonlyKeyid: JSON.stringify([...selFun.value]),
            })
            modalClose()
            getData()
        }

        const getData = async () => {
            const res = await request.get(`/api/hr-common-functionses`, {}, { loading: false })
            const commonlyKeyid = res.commonlyKeyid || '[]'

            try {
                funList.value = JSON.parse(commonlyKeyid)
            } catch (error) {
                funList.value = []
                message.warning('解析错误，请重新配置常用功能！')
            }
        }

        const router = useRouter()
        const navTo = (route) => {
            router.push(route)
        }

        //setup
        getData()

        // 拖动排序
        const dragIndex = ref() // 拖动的index
        const enterIndex = ref() // 拖至的index

        const dragHandle = () => {}
        const dragstartHandle = (e, index) => {
            dragIndex.value = index
            e.target.style.opacity = 0.5
        }
        const dragendHandle = (e) => {
            e.target.style.opacity = ''
        }
        const dragoverHandle = (e) => {
            e.preventDefault()
        }
        document.addEventListener(
            'dragexit',
            function (e) {
                e.preventDefault()
            },
            false,
        )
        const dragenterHandle = (e, index) => {
            enterIndex.value = index
        }
        const dragleaveHandle = (e, index) => {}
        const dropHandle = (e) => {
            e.preventDefault()
            // 调换顺序
            ;[selFun.value[dragIndex.value], selFun.value[enterIndex.value]] = [
                selFun.value[enterIndex.value],
                selFun.value[dragIndex.value],
            ]
        }

        return {
            dragHandle,
            dragstartHandle,
            dragendHandle,
            dragoverHandle,
            dragenterHandle,
            dragleaveHandle,
            dropHandle,

            useIcons,
            accessRoutes,
            funList,
            selFun,
            selFunNames,
            showDialog,
            navTo,
            selItem,
            delItem,
            setHandle,
            modalConfirm,
            modalClose,
        }
    },
})
</script>

<style scoped lang="less">
.item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    padding: 8px 10px;
    border-radius: @border-radius-base;
    margin-right: 10px;
    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
        background: @right-slider-color;
    }
    img {
        width: 50px;
        height: 50px;
        display: inline-block;
        margin-bottom: 5px;
    }
    .name {
        width: 64px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .icon {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px;
        color: @primary-color;
        cursor: pointer;
        background: white;
    }
    .del {
        color: @dangerous-color;
    }
}
.dialog-main {
    width: 100%;
    height: ~'calc(100vh - 100px)';
    .header {
        width: 100%;
        .tit {
            font-weight: bold;
            padding-top: 10px;
            .num {
                color: @primary-color;
            }
        }
        .selList {
            width: 100%;
            height: 120px;
            border-bottom: 1px solid #ccc;
            overflow-x: auto;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            & .item {
                border: 1px dashed #ccc;
            }
        }
    }
    .list {
        height: ~'calc(100% - 170px)';
        overflow-y: auto;
        .cell {
            padding: 10px 0;
            .cell-title {
                font-weight: bold;
                padding-bottom: 10px;
            }
            .menus {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex-wrap: wrap;
            }
        }
    }
    .footer {
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        border-top: 1px solid #ccc;
        button {
            margin-left: 10px;
        }
    }
}
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        font-weight: bold;
        color: #000;
    }
    .content {
        width: ~'calc(100% - 100px)';
        padding-top: 10px;
        min-height: 150px;
        overflow-x: auto;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .noData {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    .set-btn {
        position: absolute;
        top: 0;
        right: 0;
        width: 60px;
        height: 100%;
        color: @primary-color;
        cursor: pointer;
        writing-mode: vertical-lr;
        letter-spacing: 3px;
        border-left: 10px solid @right-slider-color;
        display: flex;
        justify-content: center;
        align-items: center;
        .txt {
            padding-top: 5px;
        }
    }
}
</style>
