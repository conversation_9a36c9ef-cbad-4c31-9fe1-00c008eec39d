<template>
    <div class="main">
        <div class="title">
            <span class="txt">提醒列表</span>
            <span class="more"></span>
        </div>
        <Spin :spinning="loading">
            <div class="table">
                <template v-for="i in reminderList" :key="i.id">
                    <div class="row">
                        <div class="left">
                            <p class="tit ellipsis">{{ i.title }}</p>
                            <p class="msg ellipsis">{{ i.content }}</p>
                        </div>
                        <div class="right" @click="navToNotice(i)">
                            <span>查看详情</span>
                            <DoubleRightOutlined />
                        </div>
                    </div>
                </template>
            </div>
        </Spin>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import request from '/@/utils/request'
import { useRouter } from 'vue-router'
import { Spin } from 'ant-design-vue'

export default defineComponent({
    name: 'ReminderList',
    components: { DoubleRightOutlined, Spin },
    setup() {
        const reminderList = ref<Recordable[]>([])

        const router = useRouter()
        const navToNotice = (item) => {
            switch (item.type) {
                case 'customer_agreement':
                    router.replace({
                        name: 'expiredAgreements',
                        query: {
                            type: 'customer_agreement',
                        },
                    })
                    break
                case 'staff_contract':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_contract',
                        },
                    })
                    break
                case 'archives_borrowing':
                    router.push({
                        name: 'changeRecord',
                        query: {
                            type: 'archives_borrowing',
                        },
                    })
                    break
                case 'staff_retire':
                    router.push({
                        name: 'retirement',
                        query: {
                            type: 'staff_retire',
                        },
                    })
                    break
                case 'staff_injure':
                    router.push({
                        name: 'industrialInjury',
                        query: {
                            injuryDateList: item.startDate,
                        },
                    })
                    break
                case 'staff_medical':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_medical',
                        },
                    })
                    break
                case 'staff_birth':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_birth',
                        },
                    })
                    break
                case 'staff_turn':
                    router.push({
                        name: 'regularWorker',
                        query: {
                            type: 'staff_turn',
                        },
                    })
                    break
                case 'staff_contract_expire':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_contract_expire',
                        },
                    })
                case 'customer_agreement_expire':
                    router.push({
                        name: 'effectiveAgreement',
                        query: {
                            type: 'customer_agreement',
                        },
                    })
                default:
                    break
            }
        }

        const loading = ref(false)
        const getData = async () => {
            try {
                loading.value = true
                const res = await request.get(`/api/hr-remind-page`, {}, { loading: false })
                reminderList.value = res
            } finally {
                loading.value = false
            }
        }

        getData()

        return {
            loading,
            navToNotice,
            reminderList,
        }
    },
})
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .table {
        margin-top: 10px;
        height: 200px;
        overflow-y: auto;
        .row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #bbbbbb;
            padding: 10px 10px 10px 0;
            font-size: 13px;
            .left {
                width: 70%;
                .msg {
                    margin-top: 5px;
                    color: #929292;
                }
            }
            .right {
                text-align: right;
                color: @primary-color;
                cursor: pointer;
            }
        }
    }
}
</style>
