<template>
    <div class="main">
        <div class="title">
            <div class="txt">业务指标</div>
            <div class="more"></div>
        </div>
        <div class="content">
            <div id="home-chart-0"></div>
            <div id="home-chart-1"></div>
            <div id="home-chart-2"></div>
            <div id="home-chart-3"></div>
            <div id="home-chart-4"></div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue'
import * as echarts from 'echarts'
import request from '/@/utils/request'
import moment from 'moment'

export default defineComponent({
    name: 'BusinessCharts',
    setup() {
        let chartRefs: echarts.ECharts[] = []
        const drawChart = (item, index) => {
            chartRefs[index] && chartRefs[index]?.dispose()
            const chartMain = echarts.init(document.getElementById(`home-chart-${index}`) as HTMLDivElement)
            const option: echarts.EChartsOption = {
                title: {
                    text: '上月/本月' + item.title,
                    top: '3%',
                    left: '5%',
                    textStyle: {
                        fontSize: 13,
                        color: '#666',
                    },
                },
                xAxis: {
                    type: 'category',
                    data: item.xAxis || [],
                    boundaryGap: true,
                    axisTick: {
                        alignWithLabel: true,
                    },
                    // axisLabel: {
                    //     interval: item.title == '服务' ? 0 : 'auto',
                    // },
                },
                yAxis: {
                    offset: 50, // 隐藏y轴数字
                    minInterval: 1,
                    type: 'value',
                    axisLine: {
                        show: false,
                    },
                    splitLine: {
                        lineStyle: {
                            type: 'dashed',
                            color: '#dddddd',
                        },
                    },
                },
                grid: {
                    top: '15%',
                    left: '10%',
                    right: '10%',
                    bottom: '10%',
                },
                tooltip: {
                    trigger: 'axis',
                },
                series: item.data
                    ? [
                          {
                              name: '上月/本月' + item.title,
                              data: item.data || [],
                              type: 'line',
                              smooth: true,
                          },
                      ]
                    : [
                          {
                              name: '上月' + item.title,
                              data: item.lastMonthData || [],
                              type: 'line',
                              smooth: true,
                          },
                          {
                              name: '本月' + item.title,
                              data: item.currentMonthData || [],
                              type: 'line',
                              smooth: true,
                          },
                      ],
            }
            chartMain.setOption(option)
            chartRefs.push(chartMain)
        }

        const inital = (data) => {
            const lastMonth = moment().subtract(1, 'month').format('YYYY-MM')
            const currentMonth = moment().format('YYYY-MM')
            const charts: Recordable[] = [
                {
                    title: '入职',
                    xAxis: [
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
                        30, 31,
                    ],
                    lastMonthData: new Array(31).fill(0),
                    currentMonthData: new Array(31).fill(0),
                },
                {
                    title: '离职',
                    xAxis: [
                        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
                        30, 31,
                    ],
                    lastMonthData: new Array(31).fill(0),
                    currentMonthData: new Array(31).fill(0),
                },
                {
                    title: '服务',
                    xAxis: ['入职', '离职', '转正', '退休', '生育', '医疗', '工伤', '证明开具'],
                    lastMonthData: new Array(8).fill(0),
                    currentMonthData: new Array(8).fill(0),
                },
                {
                    title: '薪资',
                    xAxis: [lastMonth, currentMonth],
                    data: new Array(2).fill(0),
                },
                {
                    title: '福利',
                    xAxis: [lastMonth, currentMonth],
                    data: new Array(2).fill(0),
                },
            ]
            // 入职
            data.hrOnboardingDTO.forEach((i) => {
                const date = moment(i.createdDate)
                if (date.format('YYYY-MM') == lastMonth) {
                    charts[0].lastMonthData[Number(i.day) - 1] = i.peopleSum
                } else {
                    charts[0].currentMonthData[Number(i.day) - 1] = i.peopleSum
                }
            })
            // 离职
            data.hrResignDTO.forEach((i) => {
                const date = moment(i.createdDate)
                if (date.format('YYYY-MM') == lastMonth) {
                    charts[1].lastMonthData[Number(i.day) - 1] = i.peopleSum
                } else {
                    charts[1].currentMonthData[Number(i.day) - 1] = i.peopleSum
                }
            })
            // 服务
            data.hrServiceDTO.forEach((i) => {
                const date = moment(i.createdDate)
                const idx = charts[2].xAxis.findIndex((j) => j == i.type)
                if (date.format('YYYY-MM') == lastMonth) {
                    charts[2].lastMonthData[idx] = i.peopleSum
                } else {
                    charts[2].currentMonthData[idx] = i.peopleSum
                }
            })
            // 薪酬
            data.hrSalaryDTO.forEach((i) => {
                if (i.createdDate == lastMonth) {
                    charts[3].data[0] = i.realSalaryTotal
                } else {
                    charts[3].data[1] = i.realSalaryTotal
                }
            })
            // 福利
            data.hrWelfareDTO.forEach((i) => {
                if (i.createdDate == lastMonth) {
                    charts[4].data[0] = i.welfare
                } else {
                    charts[4].data[1] = i.welfare
                }
            })
            charts.forEach(async (i, idx) => {
                drawChart(i, idx)
            })
        }

        const data = ref()
        const getData = async () => {
            const res = await request.get(`/api/hr-remind-business-indicators`, {}, { loading: false })
            data.value = res
            inital(res)
        }

        // setup
        onMounted(() => {
            getData()
        })

        onUnmounted(() => {
            chartRefs.forEach((i) => {
                i.dispose()
            })
        })

        return {}
    },
})
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    .title {
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .content {
        height: 260px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        div {
            width: 19.2%;
            height: 100%;
            background: white;
            border-radius: @border-radius-base;
        }
    }
}
</style>
