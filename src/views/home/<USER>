<template>
    <div class="main" v-bind="$attrs">
        <div class="title">
            <span class="txt">资讯列表</span>
            <span class="more"></span>
        </div>
        <div class="table">
            <template v-if="cols == 1">
                <div v-for="i in msgList" :key="i.id" class="row" @click="showRow(i)">
                    <div class="left ellipsis">
                        {{ i.informationTitle }}
                    </div>
                    <div class="right ellipsis">
                        {{ i.releaseDate }}
                    </div>
                </div>
            </template>
            <div v-else class="twoCol">
                <div v-for="i in msgList" :key="i.id" class="row" style="width: 48%" @click="showRow(i)">
                    <div class="left ellipsis">
                        {{ i.informationTitle }}
                    </div>
                    <div class="right">
                        {{ i.releaseDate }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 查看弹框 -->
    <BasicEditModalSlot
        class="detail"
        title="查看"
        v-model:visible="showDetail"
        @cancel="showDetail = false"
        width="800px"
        :footer="null"
        centered
        :zIndex="1031"
    >
        <div class="tit">
            <p>{{ currentValue.informationTitle }}</p>
            <p class="name">{{ currentValue.createdBy }}:&nbsp;&nbsp; {{ currentValue.createdDate }}</p>
        </div>

        <div class="content" v-html="currentValue.informationContent"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import request from '/@/utils/request'
defineProps({
    cols: {
        type: Number,
        default: 1,
    },
})

const showDetail = ref(false)
const currentValue = ref<Recordable>({})

const showRow = (record) => {
    currentValue.value = { ...record }
    showDetail.value = true
}

const msgList = ref<Recordable>([])
const getData = async () => {
    const res = await request.get(`/api/hr-informations/release`, {}, { loading: false })
    msgList.value = res
}

getData()
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .table {
        height: ~'calc(100% - 22px)';
        margin-top: 10px;
        overflow-y: auto;
        .twoCol {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .row {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #bbbbbb;
            padding: 10px 0;
            font-size: 13px;
            &:hover {
                color: @primary-color;
            }
            .left {
                width: 65%;
            }
            .right {
                text-align: right;
                color: @primary-color;
                cursor: pointer;
            }
        }
    }
}

.detail {
    .tit {
        width: 100%;
        height: 64px;
        text-align: center;
        // line-height: 64px;
        border-bottom: 1px solid rgba(224, 219, 219, 0.85);
        font-size: 20px;
        font-weight: 500;
        padding-bottom: 64px;
        .name {
            text-align: end;
            font-size: 14px;
            padding-right: 10px;
        }
    }
    .content {
        min-height: 400px;
        padding-top: 20px;
    }
}
</style>
