<template>
    <div id="home-chart"></div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import request from '/@/utils/request'
import * as echarts from 'echarts'
import moment from 'moment'

export default defineComponent({
    name: 'StatisticChart',
    setup() {
        const getData = async () => {
            const res = await request.get(
                `/api/hr-apply-entry-staffs/entry-count?particularYear=${moment().format('YYYY-MM-DD')}`,
            )
            drawChart(res)
        }
        const drawChart = (data) => {
            const chartMain = echarts.init(document.getElementById('home-chart') as HTMLDivElement)
            const option = {
                xAxis: {
                    type: 'category',
                    data: data.map((i) => i.months + '月'),
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                },
                legend: {
                    top: '30',
                    data: ['本月入职'],
                },
                grid: {
                    top: '20%',
                    left: '3%',
                    right: '4%',
                    bottom: '5%',
                    containLabel: true,
                },
                tooltip: {
                    trigger: 'axis',
                },
                series: [
                    {
                        name: '本月入职',
                        data: data.map((i) => i.countNum),
                        type: 'line',
                        smooth: true,
                    },
                ],
            }
            chartMain.setOption(option)
        }
        // setup
        onMounted(() => {
            getData()
        })

        return {}
    },
})
</script>

<style scoped lang="less">
#home-chart {
    margin-top: 20px;
    height: 500px;
    background: white;
}
</style>
