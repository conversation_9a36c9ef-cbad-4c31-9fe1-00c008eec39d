<template>
    <div class="main">
        <div class="title">
            <span class="txt">申请列表</span>
            <span class="more"></span>
        </div>
        <Table
            class="table"
            :columns="columns"
            :data-source="tableData"
            :bordered="true"
            :row-key="(record) => record.id"
            :pagination="false"
            :loading="loading"
            :indentSize="30"
            :scroll="{ y: 270 }"
            :customRow="customRow"
        >
            <template #state="{ record, text }">
                <Button v-if="record.show" type="primary" ghost size="small" @click="showRow(record)">查看</Button>
                <span v-else>
                    {{ text }}
                </span>
            </template>
        </Table>
    </div>
</template>

<script lang="ts">
import { defineComponent, h, ref } from 'vue'
import { useRouter } from 'vue-router'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'

export default defineComponent({
    name: 'ApplyList',
    setup() {
        const loading = ref(false)
        const tableData = ref([])

        const getData = async () => {
            const res = await request.get(`/api/hr-remind-application-list`, {}, { loading: false })
            console.log(res)
            tableData.value =
                res.map((i, idx) => ({
                    ...i,
                    id: idx,
                    show: false,
                })) || []
        }

        const customRow = (record) => {
            return {
                onMouseenter: (e) => {
                    record.show = true
                },
                onMouseleave: (e) => {
                    record.show = false
                },
            }
        }

        const router = useRouter()
        const showRow = (record) => {
            console.log(record)
            switch (record.jumpPage) {
                case 0:
                    router.push({
                        name: 'inductionApply',
                        query: {
                            applyStatusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 1:
                    const roles = useUserStore().getUserInfo.roles.map((i) => i.roleKey)
                    if (roles.includes('client')) {
                        router.push({
                            name: 'leaveStaff',
                            query: {
                                departureStaffStatusList: JSON.stringify([1]),
                            },
                        })
                    } else if (roles.includes('customer_service_manager')) {
                        router.push({
                            name: 'leaveServe',
                            query: {
                                applyStatusList: JSON.stringify([1]),
                            },
                        })
                    } else {
                        router.push({
                            name: 'leaveStaff',
                            query: {
                                departureStaffStatusList: JSON.stringify([1, 3]),
                            },
                        })
                    }

                    break
                case 2:
                    router.push({
                        name: 'birthService',
                        query: {
                            fertilityStatus: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 3:
                    router.push({
                        name: 'retirement',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 4:
                    router.push({
                        name: 'issueCertificate',
                        query: {
                            certificateStatusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 5:
                    router.push({
                        name: 'regularWorker',
                    })
                    break
                case 6:
                    router.push({
                        name: 'industrialInjury',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 7:
                    router.push({
                        name: 'archives',
                        query: {
                            stateList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 9:
                    router.push({
                        name: 'dataModification',
                        query: {
                            applyStatusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 12:
                    //开票申请
                    router.push({
                        name: 'invoicingRecords',
                        query: {
                            approveStatusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 13:
                    //报销申请
                    router.push({
                        name: 'reimbursementRecords',
                        query: {
                            approveStatusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 18:
                    //作废申请
                    router.push({
                        name: 'expenseApproval',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 19:
                    // 费用审核
                    router.push({
                        name: 'expenseApproval',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 20:
                    // 员工合同服务
                    router.push({
                        name: 'staffContract',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                case 21:
                    // 借调申请服务
                    router.push({
                        name: 'secondment',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                            stepList: JSON.stringify(record.stepList),
                        },
                    })
                    break
                case 22:
                    // 续签服务
                    router.push({
                        name: 'renewal',
                        query: {
                            statusList: JSON.stringify(record.stateList),
                        },
                    })
                    break
                default:
                    break
            }
        }

        getData()

        return {
            showRow,
            customRow,
            loading,
            tableData,
            columns: [
                {
                    title: '#',
                    dataIndex: 'index',
                    align: 'center',
                    width: 50,
                    customRender: ({ index }) => {
                        return h('span', index + 1)
                    },
                },
                {
                    title: '类型',
                    dataIndex: 'type',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '时间',
                    dataIndex: 'date',
                    align: 'center',
                    width: 168,
                },
                {
                    title: '标题',
                    dataIndex: 'title',
                    align: 'center',
                    width: 160,
                    ellipsis: true,
                },
                {
                    title: '状态',
                    dataIndex: 'state',
                    width: 80,
                    align: 'center',
                    slots: { customRender: 'state' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .table {
        margin-top: 10px;
        height: 320px;
        // overflow-y: auto;
    }
}
</style>
