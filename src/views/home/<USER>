<template>
    <div class="main">
        <div class="title">
            <span class="txt">业务联系人</span>
            <!-- <span class="more" @click="navToAll">查看全部 <DoubleRightOutlined /></span> -->
        </div>
        <Table
            class="table"
            :columns="columns"
            :data-source="tableData"
            :bordered="true"
            :row-key="(record) => record.id"
            :pagination="false"
            :loading="loading"
            :indentSize="30"
            :scroll="{ y: higher ? 495 : 215 }"
            :customRow="customRow"
            :style="{ height: '260px' }"
        />
    </div>
</template>

<script lang="ts">
import { defineComponent, h, ref } from 'vue'
// import { DoubleRightOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import request from '/@/utils/request'
import moment from 'moment'
import { message } from 'ant-design-vue'
import { isAuth } from '/@/utils/directive'

export default defineComponent({
    name: 'TodoList',
    // components: { DoubleRightOutlined },
    setup() {
        const loading = ref(false)
        const tableData = ref([])

        const router = useRouter()
        const navToAll = () => {
            router.push({ name: 'toDoList' })
        }

        const getData = async () => {
            loading.value = true
            const res = await request.get(`/api/hr-business-contacts/home-page-list`)
            tableData.value =
                res.map((i) => ({
                    ...i,
                    show: false,
                })) || []
            loading.value = false
        }

        const customRow = (record) => {
            return {
                onMouseenter: (e) => {
                    record.show = true
                },
                onMouseleave: (e) => {
                    record.show = false
                },
            }
        }

        // const finishRow = async (item) => {
        //     await request.put(`/api/hr-upcomings`, { ...item, upcomingStatus: 3 })
        //     message.success('设置成功！')
        //     getData()
        // }

        const higher = !isAuth('home_applyList')

        //setup
        getData()

        return {
            higher,
            // finishRow,
            customRow,
            loading,
            tableData,
            navToAll,
            columns: [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    customRender: (record) => record.index + 1,
                    width: 30,
                },
                {
                    title: '业务类型',
                    dataIndex: 'businessType',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '联系人姓名',
                    dataIndex: 'contacts',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '联系方式',
                    dataIndex: 'contactInformation',
                    width: 100,
                    align: 'center',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .table {
        margin-top: 10px;
    }
}
</style>
