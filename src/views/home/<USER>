<template>
    <div class="home-main">
        <div class="left-side">
            <!-- 常用功能 -->
            <CommonFun />
            <!-- 申请列表 -->
            <ApplyList v-if="isAuth('home_applyList')" style="margin-top: 10px" />
            <!-- 资讯列表 -->
            <MsgList v-if="!isAuth('home_applyList')" style="height: 390px" />
        </div>
        <div class="right-side">
            <!-- 待办事项 -->
            <ToDoList v-if="!isClient" />
            <!-- 提醒列表 -->
            <ReminderList v-if="isAuth('home_reminderList')" style="margin-top: 10px" />

            <BusinessContact v-if="isClient" />
            <!-- 资讯列表 -->
            <MsgList
                v-if="!isAuth('home_reminderList') && isAuth('home_applyList')"
                :style="{ height: '270px', marginTop: '10px' }"
            />
            <!-- :style="{ height: isClient ? '614px' : '270px', marginTop: isClient ? 0 : '10px' }" -->
        </div>
    </div>
    <BusinessCharts />
    <!-- 资讯列表 -->
    <MsgList v-if="isAuth('home_reminderList') && isAuth('home_applyList')" :cols="2" style="height: 300px" />
</template>

<script lang="ts" setup>
import MsgList from './MsgList.vue'
import CommonFun from './CommonFun.vue'
import ToDoList from './ToDoList.vue'
import ApplyList from './ApplyList.vue'
import ReminderList from './ReminderList.vue'
import BusinessCharts from './BusinessCharts.vue'
import BusinessContact from './BusinessContact.vue'

import { isAuth } from '/@/utils/directive'
import useUserStore from '/@/store/modules/user'

const isClient = useUserStore().getUserInfo?.roles[0]?.roleKey == 'client'
</script>

<style scoped lang="less">
.home-main {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left-side {
        width: 55%;
    }
    .right-side {
        width: 44%;
    }
}
</style>
