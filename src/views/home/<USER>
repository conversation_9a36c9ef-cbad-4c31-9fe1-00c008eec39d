<template>
    <div class="main">
        <div class="title">
            <span class="txt">待办事项</span>
            <span class="more" @click="navToAll">查看全部 <DoubleRightOutlined /></span>
        </div>
        <Table
            class="table"
            :columns="columns"
            :data-source="tableData"
            :bordered="true"
            :row-key="(record) => record.id"
            :pagination="false"
            :loading="loading"
            :indentSize="30"
            :scroll="{ y: higher ? 495 : 215 }"
            :customRow="customRow"
            :style="{ height: higher ? '540px' : '260px' }"
        >
            <template #upcomingStatusName="{ record, text }">
                <Button v-if="record.show" type="primary" ghost size="small" @click="finishRow(record)">已办</Button>
                <span v-else>
                    {{ text }}
                </span>
            </template>
        </Table>
    </div>
</template>

<script lang="ts">
import { defineComponent, h, ref } from 'vue'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import request from '/@/utils/request'
import moment from 'moment'
import { message } from 'ant-design-vue'
import { isAuth } from '/@/utils/directive'

export default defineComponent({
    name: 'TodoList',
    components: { DoubleRightOutlined },
    setup() {
        const loading = ref(false)
        const tableData = ref([])

        const router = useRouter()
        const navToAll = () => {
            router.push({ name: 'toDoList' })
        }

        const getData = async () => {
            loading.value = true
            const res = await request.get(`/api/hr-upcomings/index`, {}, { loading: false })
            tableData.value =
                res.map((i) => ({
                    ...i,
                    show: false,
                })) || []
            loading.value = false
        }

        const customRow = (record) => {
            return {
                onMouseenter: (e) => {
                    record.show = true
                },
                onMouseleave: (e) => {
                    record.show = false
                },
            }
        }

        const finishRow = async (item) => {
            await request.put(`/api/hr-upcomings`, { ...item, upcomingStatus: 3 })
            message.success('设置成功！')
            getData()
        }

        const higher = !isAuth('home_applyList')

        //setup
        getData()

        return {
            higher,
            finishRow,
            customRow,
            loading,
            tableData,
            navToAll,
            columns: [
                {
                    title: '日期',
                    dataIndex: 'upcomingDate',
                    align: 'center',
                    width: 90,
                    customRender: ({ text }) => {
                        return h('span', moment(text).format('MM-DD'))
                    },
                },
                {
                    title: '时间',
                    dataIndex: 'warnTime',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '标题',
                    dataIndex: 'title',
                    align: 'center',
                },
                {
                    title: '状态',
                    dataIndex: 'upcomingStatusName',
                    width: 90,
                    align: 'center',
                    slots: { customRender: 'upcomingStatusName' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.main {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    border-radius: @border-radius-base;
    background: white;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .txt {
            font-weight: bold;
            color: #000;
        }
        .more {
            color: #333;
            cursor: pointer;
        }
    }
    .table {
        margin-top: 10px;
    }
}
</style>
