<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" @click="showHandle">数据分析</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-details/page"
        exportUrl="/api/hr-bill-details/exportData"
        :params="params"
        :columns="getColumns('details')"
        @selectedRowsArr="selHandle"
    />
    <BasicTable
        ref="totalTableRef"
        :columns="getColumns('total')"
        :rowSelectionShow="false"
        :sorter="false"
        :tableDataList="tableDataList"
    />
    <!-- chart -->
    <DataAnalysis v-model:visible="showChart" :clientId="clientId" />
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import DataAnalysis from './DataAnalysis.vue'
import request from '/@/utils/request'
import { getDynamicText } from '/@/utils'

const params = ref<{ unitNumber?: string; clientId?: string; paymentDate?: string; name?: string; certificateNum?: string }>({})

const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}

const showChart = ref(false)
const clientId = ref('')
const showHandle = () => {
    const clients = Array.from(new Set(selArr.value?.map((i) => i.clientId)))
    clientId.value = clients[0] // 空数据传 undefined 展示全部客户的统计
    showChart.value = true
}

const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})

const exportData = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

watch(
    [() => params.value, () => selArr.value],
    ([newParams, newSelArr], [oldParams, oldSelArr]) => {
        dynamicQuery()
    },
    { deep: true },
)

const dynamicQuery = async () => {
    if (exportText.value.indexOf('选中') != -1) {
        let body = {}
        body['ids'] = selArr.value.map((item: any) => {
            return item.id
        })
        await fetchTotalTableData(body)
    } else if (exportText.value.indexOf('筛选') != -1) await fetchTotalTableData({ ...params.value })
    else await fetchTotalTableData()
}

const totalTableRef = ref()

const tableDataList = ref([])

const fetchTotalTableData = async (body = {}) => {
    const res = await request.post('/api/hr-bill-details/get-total', body)
    tableDataList.value = [{ ...res, unitNumber: '合计' }] as any
}

fetchTotalTableData()

const getColumns = (type) => {
    return [
        {
            title: '客户编号',
            dataIndex: 'unitNumber',
            width: 160,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 5 : 1,
                    },
                }
            },
        },
        {
            title: '客户名称',
            dataIndex: 'clientName',
            width: 150,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '缴费年月',
            dataIndex: 'paymentDate',
            width: 120,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '姓名',
            dataIndex: 'name',
            width: 120,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '证件号',
            dataIndex: 'certificateNum',
            width: 180,
            sorter: false,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '应发工资',
            dataIndex: 'salary',
            width: 120,
            sorter: false,
        },
        {
            title: '社保',
            sorter: false,
            children: [
                {
                    title: '单位',
                    dataIndex: 'unitSubtotal',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '个人',
                    dataIndex: 'personalSubtotal',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '合计',
                    dataIndex: 'socialSecurityTotal',
                    width: 100,
                    align: 'center',
                },
            ],
        },
        {
            title: '公积金',
            sorter: false,
            children: [
                {
                    title: '单位',
                    dataIndex: 'unitAccumulationFundSubtotal',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '个人',
                    dataIndex: 'personalAccumulationFundSubtotal',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '合计',
                    dataIndex: 'accumulationFundTotal',
                    width: 100,
                    align: 'center',
                },
            ],
        },
        {
            title: '其它费用',
            dataIndex: 'otherSalary',
            width: 120,
            sorter: false,
        },
        {
            title: '服务费',
            dataIndex: 'serviceFee',
            width: 120,
            sorter: false,
        },
        {
            title: '费用合计',
            dataIndex: 'total',
            width: 130,
            sorter: false,
        },
    ]
}

const searchOptions: SearchBarOption[] = [
    {
        label: '客户编号',
        key: 'unitNumber',
    },
    {
        label: '客户名称',
        key: 'clientId',
        type: 'clientSelectTree',
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '姓名',
        key: 'name',
    },
    {
        label: '证件号',
        key: 'certificateNum',
    },
]
</script>

<style scoped lang="less"></style>
