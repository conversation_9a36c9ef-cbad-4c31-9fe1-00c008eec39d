<template>
    <BasicEditModalSlot title="数据统计" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <SearchBar v-model="params" :options="searchOptions" :showSelectedLine="false" @change="initalChart" />
        <div id="dataAnalysisDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'

const props = defineProps({
    visible: Boolean,
    clientId: String,
})
const emit = defineEmits(['update:visible'])

const params = ref({
    clientId: undefined,
    startDate: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        params.value.clientId = clientId?.value as any
        initalChart()
    }
})
const modalClose = () => {
    emit('update:visible', false)
}

let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    chartRef && chartRef?.dispose && chartRef.dispose()
    const data = await request.post(`/api/hr-bill-details/queryAvgSalaryMonthly`, {
        startDate: params.value.startDate[0],
        endDate: params.value.startDate[1],
        clientId: params.value.clientId,
    })
    chartRef = echarts.init(document.getElementById('dataAnalysisDom') as HTMLElement)
    const option: echarts.EChartsOption = {
        title: {
            text: '月平均收入',
            subtext: data.payYear,
            left: 'center',
        },
        tooltip: {
            trigger: 'item',
        },
        legend: {
            top: 'center',
            right: '100',
            orient: 'vertical',
        },
        series: [
            {
                type: 'pie',
                radius: [50, 220],
                center: ['50%', '60%'],
                roseType: 'area',
                itemStyle: {
                    borderRadius: 8,
                },
                data: [
                    { value: data['5k以下'], name: '5k以下' },
                    { value: data['5k-7k'], name: '5k-7k' },
                    { value: data['7k-1w'], name: '7k-1w' },
                    { value: data['1w-3w'], name: '1w-3w' },
                    { value: data['3w-5w'], name: '3w-5w' },
                    { value: data['5w-10w'], name: '5w-10w' },
                    { value: data['10w以上'], name: '10w以上' },
                ],
            },
        ],
    }
    chartRef.setOption(option)
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientId',
        type: 'clientSelectTree',
        allowClear: false,
    },
    {
        label: '月份',
        key: 'startDate',
        type: 'monthrange',
        allowClear: false,
    },
]
</script>

<style scoped lang="less">
#dataAnalysisDom {
    width: 100%;
    height: 50vh;
    margin-top: 20px;
}
</style>
