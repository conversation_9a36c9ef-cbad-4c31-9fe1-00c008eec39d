<template>
    <BasicEditModalSlot title="应收账款统计" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <div class="top_bar">
            <SearchBar v-model="params" :options="searchOptions" @change="initalChart" :showSelectedLine="false" />
            <Button type="primary" @click="downloadReport">报表下载</Button>
        </div>
        <div id="receivablesStatisticDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'
import downFile from '/@/utils/downFile'
import { message } from 'ant-design-vue'

const props = defineProps({
    visible: Boolean,
    clientId: Array,
})
const emit = defineEmits(['update:visible'])

const params = ref({
    clientIds: [],
    dimensions: 1,
    paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        if (clientId?.value.length) params.value.clientIds = clientId?.value
        initalChart()
    }
})
const modalClose = () => {
    params.value = {
        clientIds: [],
        dimensions: 1,
        paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
        paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    }
    emit('update:visible', false)
}

let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    chartRef && chartRef?.dispose && chartRef.dispose()
    if (params.value.clientIds.length) {
        const data = await request.post(`/api/hr-bill-totals/accounts-receivable-statistics`, getParams())

        chartRef = echarts.init(document.getElementById('receivablesStatisticDom') as HTMLElement)
        const option: echarts.EChartsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            legend: {
                left: 'center',
            },
            grid: {
                left: '3%',
                right: '5%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                data:
                    params.value.dimensions == 1
                        ? data.map((i) => i.paymentDateSplicing.split('-')[0] + '-' + i.paymentDateSplicing.split('-')[1] || '-')
                        : data.map((i) => i.paymentDateSplicing.split('-')[0] || '-'),
            },
            yAxis: {
                type: 'value',
            },
            series: [
                {
                    name: '已到账金额',
                    type: 'bar',
                    stack: 'totalArrivalAmount',
                    label: {
                        show: true,
                    },
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.map((i) => i.totalArrivalAmount ?? 0),
                },
                {
                    name: '应收金额',
                    type: 'line',
                    stack: 'receivableAmount',
                    label: {
                        show: true,
                    },
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.map((i) => i.receivableAmount ?? 0),
                },
            ],
        }
        chartRef.setOption(option)
    }
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

const getParams = () => {
    return {
        clientIds: params.value.clientIds,
        filterMonth: params.value.dimensions,
        paymentDateStart: params.value.dimensions == 1 ? params.value.paymentMonth[0] + '-01' : undefined,
        paymentDateEnd:
            params.value.dimensions == 1
                ? params.value.paymentMonth[1] + '-' + moment(params.value.paymentMonth[1], 'YYYY-MM').daysInMonth()
                : undefined,
        filterStartDate: params.value.dimensions == 4 ? params.value.paymentYear[0].split('-')[0] + '-01' + '-01' : undefined,
        filterEndDate: params.value.dimensions == 4 ? params.value.paymentYear[1].split('-')[0] + '-12' + '-31' : undefined,
    }
}

const downloadReport = () => {
    if (!params.value.clientIds.length) {
        message.warning('请至少选择一个客户')
        return
    }
    downFile('post', '/api/hr-bill-totals/accounts-receivable-export', '应收账款统计', getParams())
}

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
        isAll: true,
        defaultAll: true,
        width: '300px',
    },
    {
        label: '统计维度',
        key: 'dimensions',
        type: 'select',
        options: [
            { label: '月度', value: 1 },
            { label: '年度', value: 4 },
        ],
        allowClear: false,
    },
    {
        label: '缴费年月',
        key: 'paymentMonth',
        type: 'monthrange',
        show: computed(() => {
            return params.value.dimensions == 1
        }),
        allowClear: false,
    },
    {
        label: '缴费年月',
        key: 'paymentYear',
        type: 'yearrange',
        show: computed(() => {
            return params.value.dimensions == 4
        }),
        allowClear: false,
    },
]
</script>

<style scoped lang="less">
.top_bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#receivablesStatisticDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
</style>
