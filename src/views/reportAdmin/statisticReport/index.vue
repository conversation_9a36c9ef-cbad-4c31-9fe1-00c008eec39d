<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #seasonLook="itemForm">
            <!-- 按季度 -->
            <!-- <QuarterPicker  v-model:value="params.seasonLook"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData" ></QuarterPicker> -->
        </template>
        <!-- 筛选后面的文字 -->
        <template #textCont>
            <span class="textCont">{{ content }}</span>
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" @click="showPeopLeAdd" v-if="params.reportSelect == 2"> 查看图表 </Button>
        <Button type="primary" @click="showIncomeOverChart" v-if="params.reportSelect == 3"> 查看图表 </Button>
    </div>
    <Table
        class="basicTable"
        style="width: 100%"
        ref="tableRef"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        size="small"
        bordered
        :scroll="{ x: '100' }"
        :columns="columns"
        :data-source="tableListOne"
        :row-key="(record, index) => record.id ?? index"
        :pagination="pagination"
        :loading="loading"
        @change="tableChange"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showDetails(record)">查看详情</Button>
        </template>
    </Table>
    <!--     rowKey="id" -->
    <BasicTable ref="totalTableRef" :columns="columns2" :rowSelectionShow="false" :sorter="false" :tableDataList="tableListTwo" />
    <PeopleIncreaseOrDecrease v-model:visible="showPeopleIncreaseOrDecrease" :clientId="currentClientIds" />
    <!-- 收入同期对比 -->
    <IncomeOverPeriod v-model:visible="showIncomeOver" :clientId="currentClientIds" />

    <!-- 查看详情 -->
    <ShowDetail
        v-model:visible="showDetailsModal"
        :item="currentValue"
        :type="exhibitionType"
        :title="modalTitle"
        @cancel="showDetailCancel"
        @confirm="showDetailConfirm"
    />
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import IncomeOverPeriod from './IncomeOverPeriod.vue'
import ShowDetail from './ShowDetails.vue'
import { getDynamicText, SectionToChinese } from '/@/utils'
import request from '/@/utils/request'
import downFile from '/@/utils/downFile'
import PeopleIncreaseOrDecrease from './PeopleIncreaseOrDecrease.vue'
import {
    colmnsData, //营业数据
    dataTotal, //营业数据total
    personTax, //个税（个人）
} from './data'
// import { validateUserName } from '/@/utils/format'

// 全部居中
let colmns1 = colmnsData()
let colmns2 = colmnsData('total')
colmns1.forEach((el) => {
    el.forEach((it: any) => {
        it.align = 'center'
    })
})
colmns2.forEach((el) => {
    el.forEach((it: any) => {
        it.align = 'center'
    })
})

// 默认日期计算
const dealDate = (y, m) => {
    // 2022  11
    let tM = m - 1 == 0 ? 12 : m - 1
    let mTop = y + '-' + (tM < 10 ? '0' + tM : tM)
    let thisM = y + '-' + (m < 10 ? '0' + m : m)
    let tongMonStart = y - 1 + '-' + (tM < 10 ? '0' + tM : tM)
    let tongMonEed = y - 1 + '-' + (m < 10 ? '0' + m : m)

    let quter
    let tongQuter
    let thisQuter
    if (1 <= m && m <= 3) {
        quter = y - 1 + `年第四季度`
        tongQuter = y - 1 + `年第一季度`
        thisQuter = y + `年第一季度`
    } else if (4 <= m && m <= 6) {
        quter = y + `年第一季度`
        tongQuter = y - 1 + `年第二季度`
        thisQuter = y + `年第二季度`
    } else if (7 <= m && m <= 9) {
        quter = y + `年第二季度`
        tongQuter = y - 1 + `年第三季度`
        thisQuter = y + `年第三季度`
    } else if (10 <= m && m <= 12) {
        quter = y + `年第三季度`
        tongQuter = y - 1 + `年第四季度`
        thisQuter = y + `年第四季度`
    }
    let harf
    let tongHarf
    let thisHarf
    if (1 <= m && m <= 6) {
        harf = y - 1 + '年下半年度'
        tongHarf = y - 1 + '年上半年度'
        thisHarf = y + '年上半年度'
    } else if (7 <= m && m <= 12) {
        harf = y + '年上半年度'
        tongHarf = y - 1 + '年下半年度'
        thisHarf = y + '年下半年度'
    }
    let year = [y - 1 + '', y - 1 + '']
    let thisYear = [y + '', y + '']
    return {
        start: mTop,
        end: thisM,
        quter: quter,
        harf: harf,
        year: year,
        tongMonStart: tongMonStart,
        tongMonEed: tongMonEed,
        tongQuter: tongQuter,
        thisQuter: thisQuter,
        tongHarf: tongHarf,
        thisHarf: thisHarf,
        thisYear: thisYear,
    }
}
// 给默认值
const dd = new Date()

let y = dd.getFullYear()
let mo = dd.getMonth() + 1
let dRes = dealDate(y, mo)
/**
 * @params声明初始化区
 */

const params = ref<{ reportSelect?: number; [x: string]: any }>({
    reportSelect: 0,
    exhibitionType: 1,
    startMonthList: [dRes.end, dRes.end],
})

//有些赛选框后面有文字
const content = ref('')
// 分页
const pagination = ref<any>({
    size: 'default',
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
    pageSizeOptions: ['10', '20', '30', '50', '100', '200', '300'],
    total: 0,
})
const loading = ref(false)
// 初始api
const api = ref('/api/statistics-report/business-data')
// 初始筛选
const lookTypeArr = ref<any[]>(['startMonthList'])
const tableRef = ref()
const columns = ref<any>(colmns1[0])
const columns2 = ref<any>(dataTotal[0])
const tableListOne = ref<any[]>([])
const tableListTwo = ref<any[]>([])

watch(
    tableListOne,
    (newValue) => {
        calcDynamicCols(newValue)
        console.log(columns.value)
    },
    {
        deep: true,
    },
)
const calcDynamicCols = (data) => {
    const maxLevel = Math.max(...data.map((item) => item.hrClientList?.length || 0))
    let dynamicArr: any = []
    for (let i = 0; i < maxLevel; i++) {
        let flag = columns.value.some((i) => {
            return i.dataIndex == 'hrClientList'
        })
        if (!flag) {
            dynamicArr.push({
                title: `${SectionToChinese(i + 1)}级客户`,
                dataIndex: 'hrClientList',
                align: 'center',
                width: 155,
                customRender: ({ record }) => {
                    return (
                        record?.hrClientList?.find((item) => {
                            return i + 1 == item.level
                        })?.clientName || ''
                    )
                },
                sorter: false,
            })
        }
    }
    columns.value = [...columns.value.slice(0, 1), ...dynamicArr, ...columns.value.slice(1)]
}
/**
 * @params筛选数据初始化区
 */
//报表数据
const paramsStatement = ref<any[]>([
    {
        label: '营业数据一览表',
        value: 0,
    },
    {
        label: '工作数据汇总表',
        value: 1,
    },
    {
        label: '人员增减变化表',
        value: 2,
    },
    {
        label: '收入同期对比表',
        value: 3,
    },
    {
        label: '预算执行控制数据统计表',
        value: 4,
    },
    {
        label: '客户变动信息表',
        value: 5,
    },
    {
        label: '个税分类汇总表',
        value: 6,
    },
    {
        label: '招聘数据统计表',
        value: 7,
    },
    {
        label: '招聘信息汇总表',
        value: 8,
    },
    {
        label: 'RPO业务样表',
        value: 9,
    },
])
// 按月份查    查看类型 1按月份查看 2按季度查看 3按半年度查看 4按年度查看  0 汇总分组统计
const mothSeeOptions = ref<any[]>([
    {
        label: '按月份查看',
        value: 1,
    },
    {
        label: '按季度查看',
        value: 2,
    },
    {
        label: '按半年度查看',
        value: 3,
    },
    {
        label: '按年度查看',
        value: 4,
    },
])
// 季度 quarter
const quarterOptions = ref<any>(['第一季度', '第二季度', '第三季度', '第四季度'])
// 半年度
const harfYearOption = ref<any>(['上半年度', '下半年度'])
// options
const searchOptions = ref<SearchBarOption[]>([
    {
        label: '报表查询',
        key: 'reportSelect',
        type: 'select',
        options: paramsStatement,
        show: true,
        allowClear: false,
    },
    {
        label: '类型',
        key: 'taxType',
        type: 'select',
        options: [
            {
                label: '企业',
                value: 0,
            },
            {
                label: '个人',
                value: 1,
            },
        ],
        show: computed(() => {
            return params.value.reportSelect == 6
        }),
        allowClear: false,
    },
    {
        label: '所属公司',
        key: 'clientIds',
        multiple: true,
        checkStrictly: false,
        type: 'clientSelectTree',
        show: computed(() => {
            let flag: Boolean
            if (
                params.value.reportSelect == 5 ||
                params.value.reportSelect == 7 ||
                params.value.reportSelect == 8 ||
                params.value.reportSelect == 9
            ) {
                flag = false
            } else {
                flag = true
            }
            return flag
        }),
    },
    {
        label: '查看方式',
        key: 'exhibitionType',
        type: 'select',
        options: mothSeeOptions,
        show: true,
        allowClear: false,
    },
    {
        label: '选择月份',
        key: 'startMonthList',
        type: 'monthrange',
        placeholder: ['开始月份', '结束月份'],
        show: computed(() => {
            return params.value.exhibitionType == 1 && params.value.reportSelect !== 2 && params.value.reportSelect !== 3
        }),
        allowClear: false,
    },
    {
        label: '选择季度',
        key: 'quarterLook',
        type: 'quterSelect',
        options: quarterOptions,
        show: computed(() => {
            return params.value.exhibitionType == 2 && params.value.reportSelect !== 2 && params.value.reportSelect !== 3
        }),
        allowClear: false,
    },
    {
        label: '选择半年度',
        key: 'halfYear',
        type: 'quterSelect',
        options: harfYearOption,
        placeholder: '请选择半年度',
        allowClear: false,
        show: computed(() => {
            return params.value.exhibitionType == 3 && params.value.reportSelect !== 2 && params.value.reportSelect !== 3
        }),
    },
    {
        label: '选择年度',
        key: 'someYear',
        type: 'yearrange',
        allowClear: false,
        show: computed(() => {
            return params.value.exhibitionType == 4 && params.value.reportSelect !== 2 && params.value.reportSelect !== 3
        }),
    },
    {
        label: '时间一',
        key: 'timeOne',
        type: 'monthrange',
        allowClear: false,
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 1
        }),
    },
    {
        label: '时间一',
        key: 'timeOneQuarter',
        type: 'quterSelect',
        placeholder: '时间一季度',
        allowClear: false,
        options: quarterOptions,
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 2
        }),
    },
    {
        label: '时间一',
        key: 'timeOneHarfYear',
        type: 'quterSelect',
        placeholder: '时间一半年度',
        allowClear: false,
        options: harfYearOption,
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 3
        }),
    },
    {
        label: '时间一年度',
        key: 'timeOneYear',
        allowClear: false,
        type: 'yearrange',
        // type: 'year',
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 4
        }),
    },
    {
        label: '时间二',
        key: 'timeTwo',
        allowClear: false,
        type: 'monthrange',
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 1
        }),
    },
    {
        label: '时间二',
        key: 'timeTwoQuarter',
        type: 'quterSelect',
        allowClear: false,
        placeholder: '时间二季度',
        options: quarterOptions,
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 2
        }),
    },
    {
        label: '时间二',
        key: 'timeTwoHarfYear',
        type: 'quterSelect',
        options: harfYearOption,
        allowClear: false,
        placeholder: '时间二半年度',
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 3
        }),
    },
    {
        label: '时间二年度',
        key: 'timeTwoYear',
        type: 'yearrange',
        // type: 'year',
        allowClear: false,
        show: computed(() => {
            return params.value.reportSelect == 2 && params.value.exhibitionType == 4
        }),
    },
    {
        label: '同期时间',
        key: 'periodTime',
        type: 'monthrange',
        allowClear: false,
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 1
        }),
    },
    {
        label: '同期时间',
        key: 'periodTimeQuarter',
        type: 'quterSelect',
        placeholder: '同期时间季度',
        allowClear: false,
        options: quarterOptions,
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 2
        }),
    },
    {
        label: '同期时间',
        key: 'periodTimeHarfYear',
        type: 'quterSelect',
        options: harfYearOption,
        allowClear: false,
        placeholder: '同期时间半年度',
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 3
        }),
    },
    {
        label: '同期时间年度',
        key: 'periodTimeYear',
        type: 'yearrange',
        allowClear: false,
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 4
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTime',
        allowClear: false,
        type: 'monthrange',
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 1
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTimeQuarter',
        type: 'quterSelect',
        placeholder: '本期时间季度',
        allowClear: false,
        options: quarterOptions,
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 2
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTimeHarfYear',
        type: 'quterSelect',
        allowClear: false,
        options: harfYearOption,
        placeholder: '本期时间半年度',
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 3
        }),
    },
    {
        label: '本期时间年度',
        key: 'thisPeriodTimeYear',
        type: 'yearrange',
        allowClear: false,
        show: computed(() => {
            return params.value.reportSelect == 3 && params.value.exhibitionType == 4
        }),
    },
    {
        label: '净服务费收入类型',
        key: 'serviceFeeType',
        type: 'select',
        show: computed(() => {
            return params.value.reportSelect == 0
        }),
        options: [
            {
                label: '服务费',
                value: 0,
            },
            {
                label: '代理费',
                value: 1,
            },
        ],
    },
    // {
    //     label: '个税合计',
    //     key: 'taxCombined',
    //     type: 'select',
    //     show: computed(() => {
    //         return params.value.reportSelect == 6
    //     }),
    //     options: [
    //         {
    //             label: '企业个税合计',
    //             value: 0,
    //         },
    //         {
    //             label: '员工个税明细',
    //             value: 1,
    //         },
    //     ],
    // },
    {
        label: '招聘需求单位',
        key: 'clientIds',
        multiple: true,
        checkStrictly: false,
        type: 'clientSelectTree',
        show: computed(() => {
            return params.value.reportSelect == 8
        }),
    },
    {
        label: '姓名',
        key: 'staffName',
        type: 'string',
        show: computed(() => {
            return params.value.taxType == 1 && params.value.reportSelect == 6
        }),
    },
    {
        label: '身份证号',
        key: 'certificateNum',
        type: 'string',
        show: computed(() => {
            return params.value.taxType == 1 && params.value.reportSelect == 6
        }),
    },
    {
        label: '补差使用情况',
        key: 'isUsed',
        type: 'select',
        options: [
            {
                label: '未使用',
                value: 0,
            },
            {
                label: '已使用',
                value: 1,
            },
        ],
        show: computed(() => {
            return params.value.taxType == 1 && params.value.reportSelect == 6
        }),
    },
])

const modalTitle = ref('')
const currentValue = ref()
const showDetailsModal = ref(false)
const exhibitionType = ref()
const showDetails = (record) => {
    console.log(record)
    exhibitionType.value = params.value.exhibitionType
    modalTitle.value = '查看详情'
    currentValue.value = { ...record }
    showDetailsModal.value = true
}
const showDetailCancel = () => {
    showDetailsModal.value = false
}
const showDetailConfirm = () => {
    showDetailsModal.value = false
    tableRef.value.refresh(1)
}
const exportApi = ref<string>('/api/statistics-report/business-data/export')
const title = ref<string>('营业收入汇总表')
/**
 * @params筛选数据函数 切换api
 */
const chart = ref(false)
//切换
const selectChange = (val) => {
    content.value = ''
    // let oldTalArr = talArr.value
    switch (val.reportSelect) {
        case 0:
            api.value = '/api/statistics-report/business-data'
            exportApi.value = '/api/statistics-report/business-data/export'
            title.value = '营业收入汇总表'
            break
        case 1:
            api.value = '/api/statistics-report/work-data'
            exportApi.value = '/api/statistics-report/work-data/export'
            title.value = '工作数据汇总表'
            break
        case 2:
            api.value = '/api/statistics-report/employee-change'
            chart.value = true
            exportApi.value = '/api/statistics-report/employee-change/export'
            content.value = '员工增长/流失率计算公式为: (时间二的员工数量-时间一的员工数量)/时间一的员工数量X100%'
            title.value = '人员增减变化表'
            break
        case 3:
            api.value = '/api/statistics-report/income-comparison'
            exportApi.value = '/api/statistics-report/income-comparison/export'
            title.value = '收入同期对比表'
            content.value = '同比率计算公式为: (本期时间的数据-同期时间的数据)/同期时间的数据X100%'
            break
        case 4:
            api.value = '/api/statistics-report/budget-control'
            exportApi.value = '/api/statistics-report/budget-control/export'
            title.value = '预算执行控制数据'
            break
        case 5:
            api.value = '/api/statistics-report/client-info-change'
            exportApi.value = '/api/statistics-report/client-info-change/export'
            title.value = '客户信息变动表'
            break
        case 6:
            api.value = '/api/statistics-report/tax-summary-client'
            exportApi.value = '/api/statistics-report/tax-summary-client/export'
            title.value = '个税分类汇总'
            break
        case 7:
            api.value = '/api/statistics-report/recruitment-data'
            exportApi.value = `/api/statistics-report/recruitment-data/export`
            title.value = '招聘数据统计表'
            break
        case 8:
            api.value = '/api/statistics-report/recruitment-info-data'
            exportApi.value = `/api/statistics-report/recruitment-info-data/export`
            title.value = '招聘信息汇总'
            break
        case 9:
            api.value = '/api/statistics-report/recruitment-pro-data'
            title.value = 'RPO业务样式'
            exportApi.value = '/api/statistics-report/recruitment-pro-data/export'
            break
    }
    clearQuery()
    otherClear()
    defaultValue()
    if (val.reportSelect == 6) {
        params.value.taxType = 0
    } else {
        params.value.taxType = null
    }
}

onMounted(() => {
    postTableList()
})

// 清除的筛选函数
const resetSelect = () => {
    params.value.reportSelect = 0
    params.value.exhibitionType = 1
    params.value.startMonthList = [dRes.end, dRes.end]
}
let tempVal = 0 //模拟监听  记录oldVal
let tepVal = 1 //模拟监听  记录oldVal
let tempTax = 0 //记录   0：企业  1：个税
// 筛选触发此函数
const searchData = async (val) => {
    if (!val.reportSelect && !val.exhibitionType) {
        //清除筛选用
        resetSelect()
        postTableList()
        return
    }
    if (val.reportSelect !== tempVal) {
        // 报表查询下拉（第一个筛选）
        selectChange(val)
        tempVal = val.reportSelect
    }
    // 企业  个人  切换路径
    if (val.reportSelect == 6 && tempTax !== val.taxType) {
        if (val.taxType === 0) {
            api.value = '/api/statistics-report/tax-summary-client'
            exportApi.value = '/api/statistics-report/tax-summary-client/export'
            title.value = '个税分类汇总(企业)'
        } else if (val.taxType == 1) {
            api.value = '/api/statistics-report/tax-summary-staff'
            exportApi.value = '/api/statistics-report/tax-summary-staff/export'
            title.value = '个税分类汇总(个人)'
        }
        taxClear()
        tempTax = val.taxType
    }
    // 招聘数据统计表没有所属公司
    if (val.reportSelect == 7 || val.reportSelect == 5 || val.reportSelect == 9) {
        params.value.clientIds = []
    }
    if (tepVal !== val.exhibitionType) {
        // lookTypeChange(params.value)
        clearQuery()
        defaultValue()
        tepVal = val.exhibitionType
    }

    // 表头切换
    console.log(columns.value)
    if (val.reportSelect !== 6) {
        columns.value = colmns1[val.reportSelect]
        if (val.reportSelect == 0) {
            columns2.value = dataTotal[val.reportSelect]
        } else {
            columns2.value = colmns2[val.reportSelect]
        }
    } else {
        if (val.taxType == 0) {
            //企业
            columns.value = colmns1[6]
            columns2.value = colmns1[6]
        } else if (val.taxType == 1) {
            //个人
            columns.value = personTax
            columns2.value = personTax
        }
    }

    pagination.value.current = 1
    postTableList()
}
//   清除受按月份查看影响的数据
const clearQuery = () => {
    params.value.startMonthList = null
    params.value.quarterLook = null
    params.value.halfYear = null
    params.value.someYear = null
    params.value.timeOne = null
    params.value.timeOneQuarter = null
    params.value.timeOneHarfYear = null
    params.value.timeOneYear = null
    params.value.timeTwo = null
    params.value.timeTwoQuarter = null
    params.value.timeTwoHarfYear = null
    params.value.timeTwoYear = null
    params.value.periodTime = null
    params.value.periodTimeQuarter = null
    params.value.periodTimeHarfYear = null
    params.value.periodTimeYear = null
    params.value.thisPeriodTime = null
    params.value.thisPeriodTimeQuarter = null
    params.value.thisPeriodTimeHarfYear = null
    params.value.thisPeriodTimeYear = null
}
// 换页时清除
const otherClear = () => {
    params.value.taxType = null
    params.value.serviceFeeType = null
    // params.value.taxCombined = null
    params.value.staffName = null
    params.value.certificateNum = null
    params.value.isUsed = null
}

// 个税页的企业/个人筛选清除
const taxClear = () => {
    params.value.staffName = null
    params.value.certificateNum = null
    params.value.isUsed = null
}

const defaultValue = () => {
    if (
        params.value.reportSelect == 6 ||
        params.value.reportSelect == 4 ||
        params.value.reportSelect == 5 ||
        params.value.reportSelect == 7 ||
        params.value.reportSelect == 8 ||
        params.value.reportSelect == 9 ||
        params.value.reportSelect == 0 ||
        params.value.reportSelect == 1
    ) {
        if (params.value.exhibitionType == 1) {
            params.value.startMonthList = [dRes.end, dRes.end]
        } else if (params.value.exhibitionType == 2) {
            params.value.quarterLook = dRes.quter
        } else if (params.value.exhibitionType == 3) {
            params.value.halfYear = dRes.harf
        } else if (params.value.exhibitionType == 4) {
            params.value.someYear = [...dRes.year]
        }
    } else if (params.value.reportSelect == 2) {
        if (params.value.exhibitionType == 1) {
            params.value.timeOne = [dRes.start, dRes.start]
            params.value.timeTwo = [dRes.end, dRes.end]
        } else if (params.value.exhibitionType == 2) {
            params.value.timeOneQuarter = dRes.quter
            params.value.timeTwoQuarter = dRes.quter
        } else if (params.value.exhibitionType == 3) {
            params.value.timeOneHarfYear = dRes.harf
            params.value.timeTwoHarfYear = dRes.harf
        } else if (params.value.exhibitionType == 4) {
            params.value.timeOneYear = [...dRes.year]
            params.value.timeTwoYear = [...dRes.year]
        }
    } else if (params.value.reportSelect == 3) {
        if (params.value.exhibitionType == 1) {
            params.value.periodTime = [dRes.tongMonEed, dRes.tongMonEed]
            params.value.thisPeriodTime = [dRes.end, dRes.end]
        } else if (params.value.exhibitionType == 2) {
            params.value.periodTimeQuarter = dRes.tongQuter
            params.value.thisPeriodTimeQuarter = dRes.thisQuter
        } else if (params.value.exhibitionType == 3) {
            params.value.periodTimeHarfYear = dRes.tongHarf
            params.value.thisPeriodTimeHarfYear = dRes.thisHarf
        } else if (params.value.exhibitionType == 4) {
            params.value.periodTimeYear = [...dRes.year]
            params.value.thisPeriodTimeYear = [...dRes.thisYear]
        }
    }
}

/**
 *  @params请求表格数据
 * */

/**
     参数处理  不管筛选出的值是什么统一在此处理  接口字段不统一  分4类处理
**/
const paramsfilter = (query) => {
    const temp = { ...query }

    let canShu
    // 11个页面分四类   2：人员增减变化表   3：收入同期对比表 4:预算执行控制数据统计表
    if (temp.reportSelect == 2) {
        let res = dealParams(
            temp,
            temp.timeOne,
            temp.timeTwo,
            temp.timeOneQuarter,
            temp.timeTwoQuarter,
            temp.timeOneHarfYear,
            temp.timeTwoHarfYear,
            temp.timeOneYear,
            temp.timeTwoYear,
        )
        canShu = { ...res }
    } else if (temp.reportSelect == 3) {
        let res = dealParams(
            temp,
            temp.periodTime,
            temp.thisPeriodTime,
            temp.periodTimeQuarter,
            temp.thisPeriodTimeQuarter,
            temp.periodTimeHarfYear,
            temp.thisPeriodTimeHarfYear,
            temp.periodTimeYear,
            temp.thisPeriodTimeYear,
        )
        canShu = { ...res }
    } else if (temp.reportSelect == 4) {
        if (temp.exhibitionType == 1) {
            canShu = {
                time1Start: temp.startMonthList ? temp.startMonthList[0] : undefined,
                time1End: temp.startMonthList ? temp.startMonthList[1] : undefined,
            }
        } else if (temp.exhibitionType == 2) {
            //按季度
            if (temp.quarterLook) {
                let quarter = dealQuarter(temp.quarterLook)
                canShu = {
                    time1Start: quarter[0] ?? undefined,
                    time1End: quarter[1] ?? undefined,
                }
            }
        } else if (temp.exhibitionType == 3) {
            //按半年度
            if (temp.halfYear) {
                let quarter = dealQuarter(temp.halfYear)
                canShu = {
                    time1Start: quarter[0] ?? undefined,
                    time1End: quarter[1] ?? undefined,
                }
            }
        } else {
            // 按年度
            canShu = {
                time1Start: temp.someYear ? temp.someYear[0] + '-01' : undefined,
                time1End: temp.someYear ? temp.someYear[1] + '-12' : undefined,
            }
        }
    } else {
        if (temp.exhibitionType == 1) {
            canShu = {
                exhibitionDateStart: temp.startMonthList ? temp.startMonthList[0] : undefined,
                exhibitionDateEnd: temp.startMonthList ? temp.startMonthList[1] : undefined,
            }
        } else if (temp.exhibitionType == 2) {
            //按季度
            if (temp.quarterLook) {
                let quarter = dealQuarter(temp.quarterLook)
                canShu = {
                    exhibitionDateStart: quarter[0] ?? undefined,
                    exhibitionDateEnd: quarter[1] ?? undefined,
                }
            }
        } else if (temp.exhibitionType == 3) {
            //按半年度
            if (temp.halfYear) {
                let quarter = dealQuarter(temp.halfYear)
                canShu = {
                    exhibitionDateStart: quarter[0] ?? undefined,
                    exhibitionDateEnd: quarter[1] ?? undefined,
                }
            }
        } else {
            // 按年度
            canShu = {
                exhibitionDateStart: temp.someYear ? temp.someYear[0] + '-01' : undefined,
                exhibitionDateEnd: temp.someYear ? temp.someYear[1] + '-12' : undefined,
            }
        }
    }

    return {
        reportSelect: undefined,
        ...canShu,
        clientIds: temp.clientIds && temp.clientIds.length == 0 ? undefined : temp.clientIds,
        exhibitionType: temp.exhibitionType ?? undefined,
        serviceFeeType: temp.serviceFeeType ?? undefined,
        // taxCombined: temp.taxCombined ?? undefined,
        staffName: temp.staffName ?? undefined,
        certificateNum: temp.certificateNum ?? undefined,
        isUsed: temp.isUsed ?? undefined,
    }
}

//   2和3页面的参数处理函数   (人员增减变化   同期时间变化)
const dealParams = (
    temp,
    timeOne,
    timeTwo,
    timeOneQuarter,
    timeTwoQuarter,
    timeOneHarfYear,
    timeTwoHarfYear,
    timeOneYear,
    timeTwoYear,
) => {
    let canShu
    if (temp.exhibitionType == 1) {
        canShu = {
            time1Start: timeOne ? timeOne[0] : undefined,
            time1End: timeOne ? timeOne[1] : undefined,
            time2Start: timeTwo ? timeTwo[0] : undefined,
            time2End: timeTwo ? timeTwo[1] : undefined,
            time1: timeOne ? timeOne[0] + '-' + timeOne[1] : undefined,
            time2: timeTwo ? timeTwo[0] + '-' + timeTwo[1] : undefined,
        }
    } else if (temp.exhibitionType == 2) {
        //按季度
        if (timeOneQuarter || timeTwoQuarter) {
            let quarter1 = timeOneQuarter && dealQuarter(timeOneQuarter)
            let quarter2 = timeTwoQuarter && dealQuarter(timeTwoQuarter)
            canShu = {
                time1Start: timeOneQuarter ? quarter1[0] : undefined,
                time1End: timeOneQuarter ? quarter1[1] : undefined,
                time2Start: timeTwoQuarter ? quarter2[0] : undefined,
                time2End: timeTwoQuarter ? quarter2[1] : undefined,
                // time1: timeOneQuarter ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoQuarter ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneQuarter,
                time2: timeTwoQuarter,
            }
        }
    } else if (temp.exhibitionType == 3) {
        //按半年度
        if (timeOneHarfYear || timeTwoHarfYear) {
            let quarter1 = timeOneHarfYear && dealQuarter(timeOneHarfYear)
            let quarter2 = timeTwoHarfYear && dealQuarter(timeTwoHarfYear)
            canShu = {
                time1Start: timeOneHarfYear ? quarter1[0] : undefined,
                time1End: timeOneHarfYear ? quarter1[1] : undefined,
                time2Start: timeTwoHarfYear ? quarter2[0] : undefined,
                time2End: timeTwoHarfYear ? quarter2[1] : undefined,
                // time1: timeOneHarfYear ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoHarfYear ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneHarfYear,
                time2: timeTwoHarfYear,
            }
        }
    } else {
        // 按年度
        canShu = {
            time1Start: timeOneYear ? timeOneYear[0] + '-01' : undefined,
            time1End: timeOneYear ? timeOneYear[1] + '-12' : undefined,
            time2Start: timeTwoYear ? timeTwoYear[0] + '-01' : undefined,
            time2End: timeTwoYear ? timeTwoYear[1] + '-12' : undefined,
            // time1: timeOneYear ? timeOneYear[0] + '-01' + '-' + timeOneYear[1] + '-12' : undefined,
            // time2: timeTwoYear ? timeTwoYear[0] + '-01' + '-' + timeTwoYear[1] + '-12' : undefined,
            time1: timeOneYear ? timeOneYear[0] + '-' + timeOneYear[1] : undefined,
            time2: timeTwoYear ? timeTwoYear[0] + '-' + timeTwoYear[1] : undefined,
        }
    }
    return canShu
}

// 处理季度/年度选择
const dealQuarter = (val) => {
    let mon
    let str1
    let str2
    if (val.includes('季度')) {
        switch (val.slice(6, 7)) {
            case '一':
                mon = ['01', '03']
                break
            case '二':
                mon = ['04', '06']
                break
            case '三':
                mon = ['07', '09']
                break
            case '四':
                mon = ['10', '12']
                break
        }
        str1 = val.slice(0, 4) + '-' + mon[0]
        str2 = val.slice(0, 4) + '-' + mon[1]
    } else if (val.includes('半年度')) {
        if (val.slice(5, 6) == '上') {
            str1 = val.slice(0, 4) + '-' + '01'
            str2 = val.slice(0, 4) + '-' + '06'
        } else {
            str1 = val.slice(0, 4) + '-' + '07'
            str2 = val.slice(0, 4) + '-' + '12'
        }
    }
    return [str1, str2]
}

const postTableList = async () => {
    let obj = paramsfilter(params.value)
    tableListOne.value = []
    tableListTwo.value = []
    pagination.value.total = 0
    const res = await request.post(api.value + `?pageSize=${pagination.value.pageSize}&pageNumber=${pagination.value.current}`, {
        ...obj,
    })
    if (res.list) {
        tableListOne.value = res.list?.records
        tableListTwo.value = [res.sum]
        pagination.value.total = res.list.total
    }
    // loading.value = false
}

//分页change
const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
    pagination.value.current = current
    pagination.value.pageSize = pageSize
    postTableList()
}

/**
 * @params导出
 */
const selArr = ref<any[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, [])
})
const exportData = async () => {
    let obj = paramsfilter(params.value)
    await downFile('post', exportApi.value, `${title.value}.xlsx`, { ...obj })
}

const showIncomeOver = ref(false)
const showIncomeOverChart = () => {
    currentClientIds.value = params.value.clientIds
    // currentClientIds.value = Array.from(new Set(selArr.value.map((i) => i.clientId)))
    showIncomeOver.value = true
}

const showPeopleIncreaseOrDecrease = ref(false)
const showPeopLeAdd = () => {
    currentClientIds.value = params.value.clientIds
    // currentClientIds.value = Array.from(new Set(selArr.value.map((i) => i.clientId)))
    showPeopleIncreaseOrDecrease.value = true
}

/********       旧逻辑         *****/

const currentParams = ref()
const currentClientIds = ref<string[]>([])

const showDataStatistic = ref(false)
const showDataChart = () => {
    currentClientIds.value = Array.from(new Set(selArr.value.map((i) => i.clientId)))
    showDataStatistic.value = true
}
const showIncomeDetail = ref(false)
const showIncomeChart = () => {
    currentClientIds.value = Array.from(new Set(selArr.value.map((i) => i.clientId)))
    showIncomeDetail.value = true
}
const showReceiveDetail = ref(false)
const showReceiveChart = () => {
    currentClientIds.value = Array.from(new Set(selArr.value.map((i) => i.clientId)))
    showReceiveDetail.value = true
}

const showYearLine = ref(false)
const showRow = (record) => {
    currentParams.value = record.clientId
    showYearLine.value = true
}

const totalTableRef = ref()

const tableDataList = ref([])

const getColumns = (type) => {
    return [
        {
            title: '客户编号',
            dataIndex: 'unitNumber',
            width: 160,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 4 : 1,
                    },
                }
            },
        },
        {
            title: '客户名称',
            dataIndex: 'clientName',
            width: 140,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '缴费年月',
            dataIndex: 'paymentDate',
            width: 120,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '账单人数',
            dataIndex: 'staffNum',
            width: 120,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '社保合计',
            dataIndex: 'socialSecurityTotal',
            width: 120,
        },
        {
            title: '公积金统计',
            dataIndex: 'accumulationFundTotal',
            width: 120,
        },
        {
            title: '平均工资',
            dataIndex: 'averageWage',
            width: 120,
        },
        {
            title: '工资合计',
            dataIndex: 'salaryTotal',
            width: 120,
        },
        {
            title: '服务费合计',
            dataIndex: 'serviceFeeTotal',
            width: 120,
        },
        {
            title: '结算费用合计',
            dataIndex: 'total',
            width: 130,
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 120,
            fixed: 'right',
            slots: { customRender: 'operation' },
        },
    ]
}
</script>

<style scoped lang="less">
.textCont {
    font-size: 13px;
}

/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}

:deep(.ant-modal-body) {
    padding-top: 0 !important;
}

.sendBtn {
    background-color: @success-color;
    border: none;
}

.delBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
