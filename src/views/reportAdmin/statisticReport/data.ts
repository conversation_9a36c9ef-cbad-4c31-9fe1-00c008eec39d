import { divide, plus, round, times } from 'number-precision'
// 一览表
export const colmnsData = (type?) => {
    return [
        [
            {
                title: '时间',
                dataIndex: 'dateStr',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '缴费人数',
                dataIndex: 'onJobNum',
                width: 120,
            },
            {
                title: '服务费单价',
                dataIndex: 'serviceAmountPrice',
                width: 120,
            },
            {
                title: '净服务费收入类型',
                dataIndex: 'serviceFeeType',
                width: 120,
            },
            {
                title: '不含税收入',
                dataIndex: 'noTaxIncomeServiceAmount',
                width: 120,
            },
            {
                title: '税额',
                dataIndex: 'taxAmount',
                width: 120,
            },
            {
                title: '净服务费收入',
                dataIndex: 'incomeServiceAmount',
                width: 120,
            },
            {
                title: '营业收入',
                dataIndex: 'incomeAmount',
                width: 120,
            },
            {
                title: '协议到期日期',
                dataIndex: 'agreementEndDate',
                width: 120,
            },
        ],
        // 1 工作数据
        [
            {
                title: '时间',
                dataIndex: 'dateStr',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '入职人数',
                dataIndex: 'boardNum',
                width: 120,
            },
            {
                title: '入职平均用时',
                dataIndex: 'avgBoardHour',
                width: 120,
            },
            {
                title: '离职人数',
                dataIndex: 'departureNum',
                width: 120,
            },
            {
                title: '离职平均用时',
                dataIndex: 'avgDepartureHour',
                width: 120,
            },
            {
                title: '工伤人数',
                dataIndex: 'gsNum',
                width: 120,
            },
            {
                title: '工伤平均用时',
                dataIndex: 'avgGsHour',
                width: 120,
            },
            {
                title: '生育人数',
                dataIndex: 'syNum',
                width: 120,
            },
            {
                title: '退休人数',
                dataIndex: 'txNum',
                width: 120,
            },
            {
                title: '退休平均用时',
                dataIndex: 'avgTxHour',
                width: 120,
            },
            {
                title: '合同续签数量',
                dataIndex: 'contractRenewalNum',
                width: 120,
            },
            {
                title: '合同续签平均用时',
                dataIndex: 'avgContractRenewalHour',
                width: 120,
            },
            {
                title: '证明开具数量',
                dataIndex: 'certificateNum',
                width: 120,
            },
            {
                title: '证明开具平均用时',
                dataIndex: 'avgCertificateHour',
                width: 120,
            },
            // 接口文档无此子段
            {
                title: '协议续签数量',
                dataIndex: 'protocolNum',
                width: 120,
            },
        ],
        // 2 人员增减变化
        [
            {
                title: '时间一',
                dataIndex: 'time1',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '员工数量',
                dataIndex: 'num1',
                width: 120,
            },
            {
                title: '时间二',
                dataIndex: 'time2',
                width: 120,
            },
            {
                title: '员工数量',
                dataIndex: 'num2',
                width: 120,
                // customRender: ({ text }) => {
                //     return text ?? '/'
                // },
            },
            {
                title: '员工增长率',
                dataIndex: 'growthRate',
                width: 120,
            },
            {
                title: '员工流失率',
                dataIndex: 'lossRate',
                width: 120,
            },
        ],
        // 3 收入同期对比
        [
            {
                title: '同期时间',
                dataIndex: 'tqTime',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '服务费收入',
                dataIndex: 'tqServiceFee',
                width: 120,
            },
            {
                title: '营业收入',
                dataIndex: 'tqTotalAmount',
                width: 120,
            },
            {
                title: '本期时间',
                dataIndex: 'bqTime',
                width: 120,
            },
            {
                title: '服务费收入',
                dataIndex: 'bqServiceFee',
                width: 120,
            },
            {
                title: '营业收入',
                dataIndex: 'bqTotalAmount',
                width: 120,
            },
            {
                title: '服务费同比增长/下降',
                dataIndex: 'serviceFeeRateStr',
                width: 120,
            },
            {
                title: '营业收入同比增长/下降',
                dataIndex: 'incomeRateStr',
                width: 120,
            },
        ],
        // 4  预算执行控制数据
        [
            {
                title: '时间',
                dataIndex: 'paymentDate',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '代发工资额',
                dataIndex: 'realSalaryTotal',
                width: 120,
            },
            {
                title: '代缴个税',
                dataIndex: 'personalTaxTotal',
                width: 120,
            },
            {
                title: '代缴社保费(四险合计)',
                dataIndex: 'socialSecurityTotal',
                width: 120,
            },
            {
                title: '代缴公积金',
                dataIndex: 'accumulationFundTotal',
                width: 120,
            },
            {
                title: '服务费收入',
                dataIndex: 'serviceFeeTotal',
                width: 120,
            },
        ],
        // 5   客户信息变动
        [
            {
                title: '时间',
                dataIndex: 'dateStr',
                width: 120,
            },
            {
                title: '公司数量',
                dataIndex: 'clientCount',
                width: 120,
            },
            {
                title: '减少公司',
                dataIndex: 'reducedCount',
                width: 120,
            },
            {
                title: '新增公司',
                dataIndex: 'increasedCount',
                width: 120,
            },
            {
                title: '净增加/减少公司',
                dataIndex: 'ratio',
                width: 120,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 120,
                fixed: 'right',
                slots: { customRender: 'operation' },
            },
        ],
        // 6  个税分类汇总表（企业）
        [
            {
                title: '时间',
                dataIndex: 'dateStr',
                width: 120,
            },
            {
                title: '所属公司',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '员工个税合计',
                dataIndex: 'staffTaxTotal',
                width: 120,
            },
        ],

        //  7  招聘数据统计表
        [
            {
                title: '时间',
                dataIndex: 'dateStr',
                width: 120,
            },
            {
                title: 'RPO场次',
                dataIndex: 'proSessions',
                width: 120,
            },
            {
                title: '报名人数',
                dataIndex: 'signUpNum',
                width: 120,
            },
            {
                title: '审核通过人数',
                dataIndex: 'approvedNum',
                width: 120,
            },
            {
                title: '缴费人数',
                dataIndex: 'payNum',
                width: 120,
            },
            {
                title: '缴费金额',
                dataIndex: 'payAmount',
                width: 120,
            },
            {
                title: '聘用上岗人数',
                dataIndex: 'onDutyNum',
                width: 120,
            },
            {
                title: '招聘服务费',
                dataIndex: 'serviceFee',
                width: 120,
            },
            {
                title: '回款金额',
                dataIndex: 'backArrivalAmount',
                width: 120,
                // customRender: ({ text,record})=>{}
            },
        ],
        // 8 招聘信息汇总表
        [
            {
                title: '招聘需求单位',
                dataIndex: 'clientName',
                width: 120,
            },
            {
                title: '提出需求时间',
                dataIndex: 'needCreateDate',
                width: 120,
            },
            {
                title: '简章发布时间',
                dataIndex: 'brochureCreateDate',
                width: 120,
            },
            {
                title: '报名人数',
                dataIndex: 'signUpNum',
                width: 120,
            },
            {
                title: '审核通过人数',
                dataIndex: 'approvedNum',
                width: 120,
            },
            {
                title: '缴费人数',
                dataIndex: 'payNum',
                width: 120,
            },
            {
                title: '缴费金额',
                dataIndex: 'payAmount',
                width: 120,
            },
            {
                title: '专科学历人数',
                dataIndex: 'specialtyNum',
                width: 120,
                // customRender: ({ text,record})=>{}
            },
            {
                title: '本科学历人数',
                dataIndex: 'undergraduateNum',
                width: 120,
            },
            {
                title: '研究生学历人数',
                dataIndex: 'postgraduateNum',
                width: 120,
            },
            {
                title: '聘用上岗人数',
                dataIndex: 'onDutyNum',
                width: 120,
            },
            {
                title: '招聘服务费',
                dataIndex: 'serviceFee',
                width: 120,
            },
            {
                title: '是否开票',
                dataIndex: 'invoiceStatus',
                width: 120,
            },
            {
                title: '已回款金额',
                dataIndex: 'backArrivalAmount',
                width: 120,
            },
        ],
        // 9  RPO业务样表
        [
            {
                title: '提出需求时间',
                dataIndex: 'needCreateDate',
                width: 120,
            },
            {
                title: '简章发布时间',
                dataIndex: 'brochureCreateDate',
                width: 120,
            },
            {
                title: '报名人数',
                dataIndex: 'signUpNum',
                width: 120,
            },
            {
                title: '审核通过人数',
                dataIndex: 'approvedNum',
                width: 120,
            },
            {
                title: '缴费人数',
                dataIndex: 'payNum',
                width: 120,
            },
            {
                title: '缴费金额',
                dataIndex: 'payAmount',
                width: 120,
            },
            {
                title: '专科学历人数',
                dataIndex: 'specialtyNum',
                width: 120,
                // customRender: ({ text,record})=>{}
            },
            {
                title: '本科学历人数',
                dataIndex: 'undergraduateNum',
                width: 120,
            },
            {
                title: '研究生学历人数',
                dataIndex: 'postgraduateNum',
                width: 120,
            },
            {
                title: '聘用上岗人数',
                dataIndex: 'onDutyNum',
                width: 120,
            },
            {
                title: '招聘服务费',
                dataIndex: 'serviceFee',
                width: 120,
            },
            {
                title: '是否开票',
                dataIndex: 'invoiceStatus',
                width: 120,
            },
            {
                title: '已回款金额',
                dataIndex: 'backArrivalAmount',
                width: 120,
            },
        ],
    ]
}

//   个税分类汇总表（个人）
export const personTax = [
    {
        title: '时间',
        dataIndex: 'dateStr',
        width: 120,
        align: 'center',
    },
    {
        title: '所属公司',
        dataIndex: 'clientName',
        width: 120,
        align: 'center',
    },
    {
        title: '姓名',
        dataIndex: 'staffName',
        width: 120,
        align: 'center',
    },
    {
        title: '身份证号',
        dataIndex: 'certificateNum',
        width: 120,
        align: 'center',
    },
    {
        title: '当月实际缴费数据',
        dataIndex: 'personalTaxSystem',
        width: 120,
        align: 'center',
    },
    {
        title: '个税系统缴费数据',
        dataIndex: 'personalTaxSource',
        width: 120,
        align: 'center',
    },
    {
        title: '个税差异',
        dataIndex: 'personalTax',
        width: 120,
        align: 'center',
    },
    {
        title: '补差使用情况',
        dataIndex: 'usageStatus',
        width: 120,
        align: 'center',
        customRender: ({ text, record }) => {
            return text
        },
    },
]
// 报表总数表头
export const dataTotal = [
    [
        {
            title: '时间',
            dataIndex: 'dateStr',
            width: 120,
        },
        {
            title: '所属公司',
            dataIndex: 'clientName',
            width: 120,
        },
        {
            title: '协议到期日期',
            dataIndex: 'agreementEndDate',
            width: 120,
        },
        {
            title: '缴费人数',
            dataIndex: 'onJobNum',
            width: 120,
        },
        {
            title: '服务费单价',
            dataIndex: 'serviceAmountPrice',
            width: 120,
        },
        {
            title: '净服务费收入类型',
            dataIndex: 'serviceFeeType',
            width: 120,
        },
        {
            title: '不含税收入',
            dataIndex: 'noTaxIncomeServiceAmount',
            width: 120,
        },
        {
            title: '税额',
            dataIndex: 'taxAmount',
            width: 120,
        },
        {
            title: '净服务费收入',
            dataIndex: 'incomeServiceAmount',
            width: 120,
            customRender: ({ record, text }) => {
                record.incomeServiceAmount = Number(record.incomeServiceAmount).toFixed(2)
                return text
            },
        },
        {
            title: '营业收入',
            dataIndex: 'incomeAmount',
            width: 120,
        },
    ],
]
