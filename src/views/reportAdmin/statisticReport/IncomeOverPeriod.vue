<template>
    <BasicEditModalSlot title="收入同期对比图表" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <div class="top">
            <div class="topTab" :class="{ select: idx === 0 }" @click="titTabClick(0)">服务费</div>
            <div class="topTab" :class="{ select: idx === 1 }" @click="titTabClick(1)">营业收入</div>
        </div>
        <div class="top_bar">
            <SearchBar v-model="params" :options="searchOptions" @change="initalChart" :showSelectedLine="false" />
            <!-- <Button type="primary" @click="downloadReport">报表下载</Button> -->
        </div>
        <div id="receivablesStatisticDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'
import downFile from '/@/utils/downFile'
import { message } from 'ant-design-vue'

const props = defineProps({
    visible: Boolean,
    clientId: Array,
})
const emit = defineEmits(['update:visible'])
const { visible, clientId } = toRefs(props)
// 给默认值
const dd = new Date()
// 默认日期计算
const dealDate = (y, m) => {
    // 2022  11
    let tM = m - 1 == 0 ? 12 : m - 1
    let mTop = y + '-' + (tM < 10 ? '0' + tM : tM)
    let thisM = y + '-' + (m < 10 ? '0' + m : m)
    let tongMonStart = y - 1 + '-' + (tM < 10 ? '0' + tM : tM)
    let tongMonEed = y - 1 + '-' + (m < 10 ? '0' + m : m)

    let quter
    let tongQuter
    let thisQuter
    if (1 <= m && m <= 3) {
        quter = y - 1 + `年第四季度`
        tongQuter = y - 1 + `年第一季度`
        thisQuter = y + `年第一季度`
    } else if (4 <= m && m <= 6) {
        quter = y + `年第一季度`
        tongQuter = y - 1 + `年第二季度`
        thisQuter = y + `年第二季度`
    } else if (7 <= m && m <= 9) {
        quter = y + `年第二季度`
        tongQuter = y - 1 + `年第三季度`
        thisQuter = y + `年第三季度`
    } else if (10 <= m && m <= 12) {
        quter = y + `年第三季度`
        tongQuter = y - 1 + `年第四季度`
        thisQuter = y + `年第四季度`
    }
    let harf
    let tongHarf
    let thisHarf
    if (1 <= m && m <= 6) {
        harf = y - 1 + '年下半年度'
        tongHarf = y - 1 + '年上半年度'
        thisHarf = y + '年上半年度'
    } else if (7 <= m && m <= 12) {
        harf = y + '年上半年度'
        tongHarf = y - 1 + '年下半年度'
        thisHarf = y + '年下半年度'
    }
    let year = [y - 1 + '', y - 1 + '']
    let thisYear = [y + '', y + '']
    return {
        start: mTop,
        end: thisM,
        quter: quter,
        harf: harf,
        year: year,
        tongMonStart: tongMonStart,
        tongMonEed: tongMonEed,
        tongQuter: tongQuter,
        thisQuter: thisQuter,
        tongHarf: tongHarf,
        thisHarf: thisHarf,
        thisYear: thisYear,
    }
}
let y = dd.getFullYear()
let mo = dd.getMonth() + 1
let dRes = dealDate(y, mo)

const params = ref<{ [x: string]: any }>({
    clientIds: [],
    exhibitionType: 1,
    periodTime: [dRes.tongMonStart, dRes.tongMonEed],
    thisPeriodTime: [dRes.start, dRes.end],
    periodTimeQuarter: dRes.tongQuter,
    thisPeriodTimeQuarter: dRes.thisQuter,
    periodTimeHarfYear: dRes.tongHarf,
    thisPeriodTimeHarfYear: dRes.thisHarf,
    periodTimeYear: [...dRes.year],
    thisPeriodTimeYear: [...dRes.thisYear],
    // paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    // paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

watch(visible, () => {
    if (visible.value) {
        if (clientId?.value?.length) {
            isAll.value = false
            params.value.clientIds = clientId?.value
        } else {
            isAll.value = true
        }
        initalChart()
    }
})
const modalClose = () => {
    params.value = {
        clientIds: [],
        exhibitionType: 1,
        // paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
        // paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    }
    emit('update:visible', false)
}

const idx = ref(0)
const titTabClick = (index) => {
    idx.value = index
    initalChart()
}

/**
 * @params数据
 *
 */
const isAll = ref(true)
// 季度 quarter
const quarterOptions = ref<any>(['第一季度', '第二季度', '第三季度', '第四季度'])
// 半年度
const harfYearOption = ref<any>(['上半年度', '下半年度'])

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
        isAll: true,
        defaultAll: computed(() => {
            return isAll.value ? true : false
        }),
        width: '300px',
    },
    {
        label: '统计维度',
        key: 'exhibitionType',
        type: 'select',
        options: [
            { label: '月度', value: 1 },
            { label: '季度', value: 2 },
            { label: '半年度', value: 3 },
            { label: '年度', value: 4 },
        ],
        allowClear: false,
    },

    {
        label: '同期时间',
        key: 'periodTime',
        type: 'monthrange',
        show: computed(() => {
            return params.value.exhibitionType == 1
        }),
    },
    {
        label: '同期时间',
        key: 'periodTimeQuarter',
        type: 'quterSelect',
        placeholder: '同期时间季度',
        allowClear: false,
        options: quarterOptions,
        show: computed(() => {
            return params.value.exhibitionType == 2
        }),
    },
    {
        label: '同期时间',
        key: 'periodTimeHarfYear',
        type: 'quterSelect',
        options: harfYearOption,
        placeholder: '同期时间半年度',
        show: computed(() => {
            return params.value.exhibitionType == 3
        }),
    },
    {
        label: '同期时间年度',
        key: 'periodTimeYear',
        type: 'yearrange',
        show: computed(() => {
            return params.value.exhibitionType == 4
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTime',
        type: 'monthrange',
        show: computed(() => {
            return params.value.exhibitionType == 1
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTimeQuarter',
        type: 'quterSelect',
        placeholder: '本期时间季度',
        options: quarterOptions,
        show: computed(() => {
            return params.value.exhibitionType == 2
        }),
    },
    {
        label: '本期时间',
        key: 'thisPeriodTimeHarfYear',
        type: 'quterSelect',
        options: harfYearOption,
        placeholder: '本期时间半年度',
        show: computed(() => {
            return params.value.exhibitionType == 3
        }),
    },
    {
        label: '本期时间年度',
        key: 'thisPeriodTimeYear',
        type: 'yearrange',
        show: computed(() => {
            return params.value.exhibitionType == 4
        }),
    },
]

const data = ref<any>([])
const getDataList = async () => {
    let resParams = paramsfilter()
    const list = await request.post(`/api/statistics-report/income-comparison/chart`, { ...resParams })
    list.forEach((el) => {
        let ti1 = el.tqTime.split('-')
        let ti2 = el.bqTime.split('-')
        el.tqTime = ti1[0] + '.' + ti1[1] + '-' + ti1[2] + '.' + ti1[3]
        el.bqTime = ti2[0] + '.' + ti2[1] + '-' + ti2[2] + '.' + ti2[3]
    })

    if (idx.value == 0) {
        data.value = list.map((el) => {
            return {
                ...el,
                num1: el.tqServiceFee,
                num2: el.bqServiceFee,
                rate: el.serviceFeeRate,
            }
        })
    } else if (idx.value == 1) {
        data.value = list.map((el) => ({
            ...el,
            num1: el.tqTotalAmount,
            num2: el.bqTotalAmount,
            rate: el.incomeRate,
        }))
    }
    data.value = data.value
}

let monType = ref(1)
let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    if (monType.value !== params.value.exhibitionType) {
        monType.value = params.value.exhibitionType
    }

    chartRef && chartRef?.dispose && chartRef.dispose()
    if (params.value.clientIds.length) {
        await getDataList()

        chartRef = echarts.init(document.getElementById('receivablesStatisticDom') as HTMLElement)
        const option: echarts.EChartsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            legend: {
                left: 'center',
                selected: {
                    增长率: false,
                },
            },
            grid: {
                left: '3%',
                right: '5%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                axisTick: {
                    show: false,
                },
                data: data.value.map((i) => i.clientName),
            },
            yAxis: {
                type: 'value',
            },
            series: [
                {
                    name: '同期时间',
                    type: 'bar',
                    stack: 'num1',
                    label: {
                        show: true,
                    },
                    // yAxisIndex: 1,
                    large: true,
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.value.map((i) => i.num1 ?? 0),
                },
                {
                    name: '本期时间',
                    type: 'bar',
                    stack: 'num2',
                    label: {
                        show: true,
                    },
                    large: true,
                    // yAxisIndex: 1,
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.value.map((i) => i.num2 ?? 0),
                },
                {
                    name: '增长率',
                    type: 'line',
                    stack: 'rate',
                    label: {
                        show: true,
                    },
                    // yAxisIndex: 2,
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.value.map((i) => i.rate ?? 0),
                },
            ],
        }
        chartRef.setOption(option)
    }
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

// 处理总参数
const paramsfilter = () => {
    const temp = { ...params.value }
    let canShu
    let res = dealParams(
        temp,
        temp.periodTime,
        temp.thisPeriodTime,
        temp.periodTimeQuarter,
        temp.thisPeriodTimeQuarter,
        temp.periodTimeHarfYear,
        temp.thisPeriodTimeHarfYear,
        temp.periodTimeYear,
        temp.thisPeriodTimeYear,
    )
    canShu = { ...res }
    return {
        ...canShu,
        clientIds: temp.clientIds ?? undefined,
        exhibitionType: temp.exhibitionType ?? undefined,
    }
}

//   2和3页面的参数处理函数   (人员增减变化   同期时间变化)
const dealParams = (
    temp,
    timeOne,
    timeTwo,
    timeOneQuarter,
    timeTwoQuarter,
    timeOneHarfYear,
    timeTwoHarfYear,
    timeOneYear,
    timeTwoYear,
) => {
    let canShu
    if (temp.exhibitionType == 1) {
        canShu = {
            time1Start: timeOne ? timeOne[0] : undefined,
            time1End: timeOne ? timeOne[1] : undefined,
            time2Start: timeTwo ? timeTwo[0] : undefined,
            time2End: timeTwo ? timeTwo[1] : undefined,
            time1: timeOne ? timeOne[0] + '-' + timeOne[1] : undefined,
            time2: timeTwo ? timeTwo[0] + '-' + timeTwo[1] : undefined,
        }
    } else if (temp.exhibitionType == 2) {
        //按季度
        if (timeOneQuarter || timeTwoQuarter) {
            let quarter1 = timeOneQuarter && dealQuarter(timeOneQuarter)
            let quarter2 = timeTwoQuarter && dealQuarter(timeTwoQuarter)
            canShu = {
                time1Start: timeOneQuarter ? quarter1[0] : undefined,
                time1End: timeOneQuarter ? quarter1[1] : undefined,
                time2Start: timeTwoQuarter ? quarter2[0] : undefined,
                time2End: timeTwoQuarter ? quarter2[1] : undefined,
                // time1: timeOneQuarter ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoQuarter ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneQuarter,
                time2: timeTwoQuarter,
            }
        }
    } else if (temp.exhibitionType == 3) {
        //按半年度
        if (timeOneHarfYear || timeTwoHarfYear) {
            let quarter1 = timeOneHarfYear && dealQuarter(timeOneHarfYear)
            let quarter2 = timeTwoHarfYear && dealQuarter(timeTwoHarfYear)
            canShu = {
                time1Start: timeOneHarfYear ? quarter1[0] : undefined,
                time1End: timeOneHarfYear ? quarter1[1] : undefined,
                time2Start: timeTwoHarfYear ? quarter2[0] : undefined,
                time2End: timeTwoHarfYear ? quarter2[1] : undefined,
                // time1: timeOneHarfYear ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoHarfYear ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneHarfYear,
                time2: timeTwoHarfYear,
            }
        }
    } else {
        // 按年度
        canShu = {
            time1Start: timeOneYear ? timeOneYear[0] + '-01' : undefined,
            time1End: timeOneYear ? timeOneYear[1] + '-12' : undefined,
            time2Start: timeTwoYear ? timeTwoYear[0] + '-01' : undefined,
            time2End: timeTwoYear ? timeTwoYear[1] + '-12' : undefined,
            // time1: timeOneYear ? timeOneYear[0] + '-01' + '-' + timeOneYear[1] + '-12' : undefined,
            // time2: timeTwoYear ? timeTwoYear[0] + '-01' + '-' + timeTwoYear[1] + '-12' : undefined,
            time1: timeOneYear ? timeOneYear[0] + '-' + timeOneYear[1] : undefined,
            time2: timeTwoYear ? timeTwoYear[0] + '-' + timeTwoYear[1] : undefined,
        }
    }
    return canShu
}

// 处理季度/年度选择
const dealQuarter = (val) => {
    let mon
    let str1
    let str2
    if (val.includes('季度')) {
        switch (val.slice(6, 7)) {
            case '一':
                mon = ['01', '03']
                break
            case '二':
                mon = ['04', '06']
                break
            case '三':
                mon = ['07', '09']
                break
            case '四':
                mon = ['10', '12']
                break
        }
        str1 = val.slice(0, 4) + '-' + mon[0]
        str2 = val.slice(0, 4) + '-' + mon[1]
    } else if (val.includes('半年度')) {
        if (val.slice(5, 6) == '上') {
            str1 = val.slice(0, 4) + '-' + '01'
            str2 = val.slice(0, 4) + '-' + '06'
        } else {
            str1 = val.slice(0, 4) + '-' + '07'
            str2 = val.slice(0, 4) + '-' + '12'
        }
    }
    return [str1, str2]
}
</script>

<style scoped lang="less">
.top_bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#receivablesStatisticDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
.top {
    margin-bottom: 20px;
    display: flex;
    .topTab {
        width: 66px;
        font-size: 16px;
        text-align: center;
    }

    .select {
        color: #6894fe;
    }
}
</style>
