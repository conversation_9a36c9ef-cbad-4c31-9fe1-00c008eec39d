<template>
    <BasicEditModalSlot title="应收账款统计" :visible="visible" @cancel="modalClose" :footer="null" width="1400px" centered>
        <div class="btns">
            <Button type="primary" @click="exportData">导出</Button>
        </div>
        <BasicTable
            ref="tableRef"
            :api="`/api/statistics-report/client-info-change/detail`"
            :columns="columns"
            :rowSelectionShow="false"
            :sorter="false"
            :params="{ ...params }"
            :pageSize="1000"
            :tableDataFormat="tableDataFormat"
        />
        <div id="receivablesStatisticDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { toRefs, watch, computed, ref } from 'vue'
import { getDynamicText } from '/@/utils'
import downFile from '/@/utils/downFile'
const props = defineProps({
    visible: Boolean,
    type: Number,
    date: String,
    item: Object,
})
const emit = defineEmits(['update:visible'])
const formData = ref<any>()
const tableRef = ref<any>()
const { visible, type, item, date } = toRefs(props)
const params = ref<{ [x: string]: any }>({ exhibitionDate: item?.value?.dateStr, exhibitionType: type?.value })
const modalClose = () => {
    emit('update:visible', false)
}

watch(visible, () => {
    if (visible.value) {
        let quary = dealQuary()

        params.value = { ...quary, exhibitionType: type?.value }
    }
})

// 处理季度/年度选择  return [YYYY-MM,YYYY-MM]
const dealQuarter = (val) => {
    let mon
    let str1
    let str2
    if (val.includes('季度')) {
        switch (val.slice(6, 7)) {
            case '1':
                mon = ['01', '03']
                break
            case '2':
                mon = ['04', '06']
                break
            case '3':
                mon = ['07', '09']
                break
            case '4':
                mon = ['10', '12']
                break
        }
        str1 = val.slice(0, 4) + '-' + mon[0]
        str2 = val.slice(0, 4) + '-' + mon[1]
    } else if (val.includes('半年度')) {
        if (val.slice(5, 6) == '上') {
            str1 = val.slice(0, 4) + '-' + '01'
            str2 = val.slice(0, 4) + '-' + '06'
        } else {
            str1 = val.slice(0, 4) + '-' + '07'
            str2 = val.slice(0, 4) + '-' + '12'
        }
    } else {
        return val
    }
    return [str1, str2]
}
// 参数处理
const dealQuary = () => {
    let timeStr = item?.value?.dateStr
    let canShu
    if (type?.value == 1) {
        // 按月度
        canShu = {
            exhibitionDateStart: timeStr ? timeStr : undefined,
            exhibitionDateEnd: timeStr ? timeStr : undefined,
        }
    } else if (type?.value == 2 || type?.value == 3) {
        //按季度  按半年度
        if (timeStr) {
            let quarter = dealQuarter(timeStr)
            canShu = {
                exhibitionDateStart: quarter[0] ?? undefined,
                exhibitionDateEnd: quarter[1] ?? undefined,
            }
        }
    } else if (type?.value == 4) {
        // 按年度
        canShu = {
            exhibitionDateStart: timeStr ? timeStr.slice(0, 4) + '-01' : undefined,
            exhibitionDateEnd: timeStr ? timeStr.slice(0, 4) + '-12' : undefined,
        }
    }

    return { ...canShu }
}

const exportApi = ref('/api/statistics-report/client-info-change/detail-export')
const exportData = async () => {
    await downFile('post', exportApi.value, `客户详情.xlsx`, { ...params.value })
}
const columns = [
    {
        title: '减少公司名称',
        dataIndex: 'reClientName',
        width: 300,
    },
    {
        title: '解除合作时间',
        dataIndex: 'reDateTimeStr',
        width: 210,
    },

    {
        title: '解除原因',
        dataIndex: 'reSeason',
        width: 110,
    },
    {
        title: '新增公司名称',
        dataIndex: 'clientName',
        width: 350,
    },
    {
        title: '合作开始时间',
        dataIndex: 'dateTimeStr',
        width: 200,
    },
]

const tableDataFormat = (item) => {
    if (item.reduced != undefined && item.increased != undefined) {
        let table1 = [...item.increased.records]
        let table2 = [...item.reduced.records]
        let temp = table2.map((el) => ({
            reClientName: el.clientName,
            reSeason: el.reason,
            reDateTimeStr: el.dateTimeStr,
        }))
        let table: any = []
        let tableLength = table1?.length > table2?.length ? table1.length : table2.length
        for (let i = 0; i < tableLength; i++) {
            table.push({
                ...temp[i],
                ...table1[i],
            })
        }
        return table
    } else if (item.reduced == undefined) {
        return item.increased.records.map((list) => ({
            clientName: list.clientName,
            dateTimeStr: list.dateTimeStr,
        }))
    } else if (item.increased == undefined) {
        return item.reduced.records.map((list) => ({
            reClientName: list.clientName,
            reSeason: list.reason,
            reDateTimeStr: list.dateTimeStr,
        }))
    } else {
        return {}
    }
}
</script>

<style scoped lang="less">
.top_bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#receivablesStatisticDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
</style>
