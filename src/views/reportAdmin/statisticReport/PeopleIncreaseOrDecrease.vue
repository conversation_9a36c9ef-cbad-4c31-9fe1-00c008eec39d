<template>
    <BasicEditModalSlot title="人员增减变化图表" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <div class="top_bar">
            <SearchBar v-model="params" :options="searchOptions" @change="initalChart" :showSelectedLine="false" />
            <!-- <Button type="primary" @click="downloadReport">报表下载</Button> -->
        </div>
        <div id="receivablesStatisticDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'
import downFile from '/@/utils/downFile'
import { message } from 'ant-design-vue'

const props = defineProps({
    visible: Boolean,
    clientId: Array,
})
const emit = defineEmits(['update:visible'])

const params = ref<{ [x: string]: any }>({
    clientIds: [],
    exhibitionType: 1,
    // paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    // paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        params.value.timeOne = [dRes.start, dRes.end]
        params.value.timeTwo = [dRes.start, dRes.end]
        if (clientId?.value?.length) {
            isAll.value = false
            params.value.clientIds = clientId?.value
        } else {
            isAll.value = true
        }

        initalChart()
    }
})
const modalClose = () => {
    params.value = {
        clientIds: [],
        exhibitionType: 1,
        // paymentMonth: [moment().format('YYYY-01'), moment().format('YYYY-12')],
        // paymentYear: [moment().format('YYYY-01'), moment().format('YYYY-12')],
    }
    emit('update:visible', false)
}

/**
 * @params数据
 *
 */
const isAll = ref(true)
// 季度 quarter
const quarterOptions = ref<any>(['第一季度', '第二季度', '第三季度', '第四季度'])
// 半年度
const harfYearOption = ref<any>(['上半年度', '下半年度'])

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
        isAll: true,
        defaultAll: computed(() => {
            return isAll.value ? true : false
        }),
        width: '300px',
    },
    {
        label: '统计维度',
        key: 'exhibitionType',
        type: 'select',
        options: [
            { label: '月度', value: 1 },
            { label: '季度', value: 2 },
            { label: '半年度', value: 3 },
            { label: '年度', value: 4 },
        ],
        allowClear: false,
    },

    {
        label: '时间一',
        key: 'timeOne',
        type: 'monthrange',
        allowClear: false,
        show: computed(() => {
            return params.value.exhibitionType == 1
        }),
    },
    {
        label: '时间一',
        key: 'timeOneQuarter',
        type: 'quterSelect',
        placeholder: '时间一季度',
        allowClear: false,
        options: quarterOptions,
        show: computed(() => {
            return params.value.exhibitionType == 2
        }),
    },
    {
        label: '时间一',
        key: 'timeOneHarfYear',
        type: 'quterSelect',
        placeholder: '时间一半年度',
        allowClear: false,
        options: harfYearOption,
        show: computed(() => {
            return params.value.exhibitionType == 3
        }),
    },
    {
        label: '时间一年度',
        key: 'timeOneYear',
        allowClear: false,
        type: 'yearrange',
        show: computed(() => {
            return params.value.exhibitionType == 4
        }),
    },
    {
        label: '时间二',
        key: 'timeTwo',
        allowClear: false,
        type: 'monthrange',
        show: computed(() => {
            return params.value.exhibitionType == 1
        }),
    },
    {
        label: '时间二',
        key: 'timeTwoQuarter',
        type: 'quterSelect',
        allowClear: false,
        placeholder: '时间二季度',
        options: quarterOptions,
        show: computed(() => {
            return params.value.exhibitionType == 2
        }),
    },
    {
        label: '时间二',
        key: 'timeTwoHarfYear',
        type: 'quterSelect',
        options: harfYearOption,
        allowClear: false,
        placeholder: '时间二半年度',
        show: computed(() => {
            return params.value.exhibitionType == 3
        }),
    },
    {
        label: '时间二年度',
        key: 'timeTwoYear',
        type: 'yearrange',
        allowClear: false,
        show: computed(() => {
            return params.value.exhibitionType == 4
        }),
    },

    // {
    //     label: '缴费年月',
    //     key: 'paymentMonth',
    //     type: 'monthrange',
    //     show: computed(() => {
    //         return params.value.exhibitionType == 1
    //     }),
    //     allowClear: false,
    // },
    // {
    //     label: '缴费年月',
    //     key: 'paymentYear',
    //     type: 'yearrange',
    //     show: computed(() => {
    //         return params.value.exhibitionType == 4
    //     }),
    //     allowClear: false,
    // },
]

let monType = ref(1)
let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    if (monType.value !== params.value.exhibitionType) {
        // clearQuery()
        defaultValue()
        monType.value = params.value.exhibitionType
    }

    let resParams = paramsfilter()

    chartRef && chartRef?.dispose && chartRef.dispose()
    if (params.value.clientIds.length) {
        const data = await request.post(`/api/statistics-report/employee-change/chart`, { ...resParams })
        data.forEach((el) => {
            let ti1 = el.time1.split('-')
            let ti2 = el.time2.split('-')
            el.time1 = ti1[0] + '.' + ti1[1] + '-' + ti1[2] + '.' + ti1[3]
            el.time2 = ti2[0] + '.' + ti2[1] + '-' + ti2[2] + '.' + ti2[3]
        })
        chartRef = echarts.init(document.getElementById('receivablesStatisticDom') as HTMLElement)
        const option: echarts.EChartsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            legend: {
                left: 'center',
                // data:[''],
                selected: {
                    增长率: false,
                },
            },
            grid: {
                left: '3%',
                right: '5%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                axisTick: {
                    show: false,
                },
                data: data.map((i) => i.clientName),
            },
            yAxis: {
                type: 'value',
            },
            series: [
                {
                    name: '时间一',
                    type: 'bar',
                    stack: 'num1',
                    label: {
                        show: true,
                    },
                    // yAxisIndex: 1,
                    // barGap: '5%',
                    large: true,
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.map((i) => i.num1 ?? 0),
                },
                {
                    name: '时间二',
                    type: 'bar',
                    stack: 'num2',
                    label: {
                        show: true,
                    },
                    large: true,
                    // yAxisIndex: 1,
                    // barGap: '5%',
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.map((i) => i.num2 ?? 0),
                },
                {
                    name: '增长率',
                    type: 'line',
                    stack: 'rate',
                    label: {
                        show: true,
                    },
                    // yAxisIndex: 2,
                    emphasis: {
                        focus: 'series',
                    },
                    data: data.map((i) => i.rate ?? 0),
                },
            ],
        }
        chartRef.setOption(option)
    }
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

// const getParams = () => {
//     return {
//         clientIds: params.value.clientIds,
//         filterMonth: params.value.exhibitionType,
//         paymentDateStart: params.value.exhibitionType == 1 ? params.value.paymentMonth[0] + '-01' : undefined,
//         paymentDateEnd:
//             params.value.exhibitionType == 1
//                 ? params.value.paymentMonth[1] + '-' + moment(params.value.paymentMonth[1], 'YYYY-MM').daysInMonth()
//                 : undefined,
//         filterStartDate: params.value.exhibitionType == 4 ? params.value.paymentYear[0].split('-')[0] + '-01' + '-01' : undefined,
//         filterEndDate: params.value.exhibitionType == 4 ? params.value.paymentYear[1].split('-')[0] + '-12' + '-31' : undefined,
//     }
// }

// const downloadReport = () => {
//     if (!params.value.clientIds.length) {
//         message.warning('请至少选择一个客户')
//         return
//     }
//     downFile('post', '/api/hr-bill-totals/accounts-receivable-export', '应收账款统计', getParams())
// }

//   清除受按月份查看影响的数据

// const clearQuery = () => {
//     params.value.timeOne = null
//     params.value.timeOneQuarter = null
//     params.value.timeOneHarfYear = null
//     params.value.timeOneYear = null
//     params.value.timeTwo = null
//     params.value.timeTwoQuarter = null
//     params.value.timeTwoHarfYear = null
//     params.value.timeTwoYear = null
// }

const dealDate = (y, m) => {
    // 2022  11
    let tM = m - 1 == 0 ? 12 : m - 1
    let mTop = y + '-' + (tM < 10 ? '0' + tM : tM)
    let thisM = y + '-' + (m < 10 ? '0' + m : m)
    let tongMonStart = y - 1 + '-' + (tM < 10 ? '0' + tM : tM)
    let tongMonEed = y - 1 + '-' + (m < 10 ? '0' + m : m)

    let quter
    let tongQuter
    let thisQuter
    if (1 <= m && m <= 3) {
        quter = y - 1 + `年第四季度`
        tongQuter = y - 1 + `年第一季度`
        thisQuter = y + `年第一季度`
    } else if (4 <= m && m <= 6) {
        quter = y + `年第一季度`
        tongQuter = y - 1 + `年第二季度`
        thisQuter = y + `年第二季度`
    } else if (7 <= m && m <= 9) {
        quter = y + `年第二季度`
        tongQuter = y - 1 + `年第三季度`
        thisQuter = y + `年第三季度`
    } else if (10 <= m && m <= 12) {
        quter = y + `年第三季度`
        tongQuter = y - 1 + `年第四季度`
        thisQuter = y + `年第四季度`
    }
    let harf
    let tongHarf
    let thisHarf
    if (1 <= m && m <= 6) {
        harf = y - 1 + '年下半年度'
        tongHarf = y - 1 + '年上半年度'
        thisHarf = y + '年上半年度'
    } else if (7 <= m && m <= 12) {
        harf = y + '年上半年度'
        tongHarf = y - 1 + '年下半年度'
        thisHarf = y + '年下半年度'
    }
    let year = [y - 1 + '', y - 1 + '']
    let thisYear = [y + '', y + '']
    return {
        start: mTop,
        end: thisM,
        quter: quter,
        harf: harf,
        year: year,
        tongMonStart: tongMonStart,
        tongMonEed: tongMonEed,
        tongQuter: tongQuter,
        thisQuter: thisQuter,
        tongHarf: tongHarf,
        thisHarf: thisHarf,
        thisYear: thisYear,
    }
}
// 给默认值
const dd = new Date()
let y = dd.getFullYear()
let mo = dd.getMonth() + 1
let dRes = dealDate(y, mo)
const defaultValue = () => {
    if (params.value.exhibitionType == 1) {
        params.value.timeOne = [dRes.start, dRes.end]
        params.value.timeTwo = [dRes.start, dRes.end]
    } else if (params.value.exhibitionType == 2) {
        params.value.timeOneQuarter = dRes.quter
        params.value.timeTwoQuarter = dRes.quter
    } else if (params.value.exhibitionType == 3) {
        params.value.timeOneHarfYear = dRes.harf
        params.value.timeTwoHarfYear = dRes.harf
    } else if (params.value.exhibitionType == 4) {
        params.value.timeOneYear = [...dRes.year]
        params.value.timeTwoYear = [...dRes.year]
    }
}

// 处理总参数
const paramsfilter = () => {
    const temp = { ...params.value }
    let canShu
    let res = dealParams(
        temp,
        temp.timeOne,
        temp.timeTwo,
        temp.timeOneQuarter,
        temp.timeTwoQuarter,
        temp.timeOneHarfYear,
        temp.timeTwoHarfYear,
        temp.timeOneYear,
        temp.timeTwoYear,
    )
    canShu = { ...res }
    return {
        ...canShu,
        clientIds: temp.clientIds ?? undefined,
        exhibitionType: temp.exhibitionType ?? undefined,
    }
}
//   2和3页面的参数处理函数   (人员增减变化   同期时间变化)
const dealParams = (
    temp,
    timeOne,
    timeTwo,
    timeOneQuarter,
    timeTwoQuarter,
    timeOneHarfYear,
    timeTwoHarfYear,
    timeOneYear,
    timeTwoYear,
) => {
    let canShu
    if (temp.exhibitionType == 1) {
        canShu = {
            time1Start: timeOne ? timeOne[0] : undefined,
            time1End: timeOne ? timeOne[1] : undefined,
            time2Start: timeTwo ? timeTwo[0] : undefined,
            time2End: timeTwo ? timeTwo[1] : undefined,
            time1: timeOne ? timeOne[0] + '-' + timeOne[1] : undefined,
            time2: timeTwo ? timeTwo[0] + '-' + timeTwo[1] : undefined,
        }
    } else if (temp.exhibitionType == 2) {
        //按季度
        if (timeOneQuarter || timeTwoQuarter) {
            let quarter1 = timeOneQuarter && dealQuarter(timeOneQuarter)
            let quarter2 = timeTwoQuarter && dealQuarter(timeTwoQuarter)
            canShu = {
                time1Start: timeOneQuarter ? quarter1[0] : undefined,
                time1End: timeOneQuarter ? quarter1[1] : undefined,
                time2Start: timeTwoQuarter ? quarter2[0] : undefined,
                time2End: timeTwoQuarter ? quarter2[1] : undefined,
                // time1: timeOneQuarter ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoQuarter ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneQuarter,
                time2: timeTwoQuarter,
            }
        }
    } else if (temp.exhibitionType == 3) {
        //按半年度
        if (timeOneHarfYear || timeTwoHarfYear) {
            let quarter1 = timeOneHarfYear && dealQuarter(timeOneHarfYear)
            let quarter2 = timeTwoHarfYear && dealQuarter(timeTwoHarfYear)
            canShu = {
                time1Start: timeOneHarfYear ? quarter1[0] : undefined,
                time1End: timeOneHarfYear ? quarter1[1] : undefined,
                time2Start: timeTwoHarfYear ? quarter2[0] : undefined,
                time2End: timeTwoHarfYear ? quarter2[1] : undefined,
                // time1: timeOneHarfYear ? quarter1[0] + '-' + quarter1[1] : undefined,
                // time2: timeTwoHarfYear ? quarter2[0] + '-' + quarter2[1] : undefined,
                time1: timeOneHarfYear,
                time2: timeTwoHarfYear,
            }
        }
    } else {
        // 按年度
        canShu = {
            time1Start: timeOneYear ? timeOneYear[0] + '-01' : undefined,
            time1End: timeOneYear ? timeOneYear[1] + '-12' : undefined,
            time2Start: timeTwoYear ? timeTwoYear[0] + '-01' : undefined,
            time2End: timeTwoYear ? timeTwoYear[1] + '-12' : undefined,
            // time1: timeOneYear ? timeOneYear[0] + '-01' + '-' + timeOneYear[1] + '-12' : undefined,
            // time2: timeTwoYear ? timeTwoYear[0] + '-01' + '-' + timeTwoYear[1] + '-12' : undefined,
            time1: timeOneYear ? timeOneYear[0] + '-' + timeOneYear[1] : undefined,
            time2: timeTwoYear ? timeTwoYear[0] + '-' + timeTwoYear[1] : undefined,
        }
    }
    return canShu
}
// 处理季度/年度选择
const dealQuarter = (val) => {
    let mon
    let str1
    let str2
    if (val.includes('季度')) {
        switch (val.slice(6, 7)) {
            case '一':
                mon = ['01', '03']
                break
            case '二':
                mon = ['04', '06']
                break
            case '三':
                mon = ['07', '09']
                break
            case '四':
                mon = ['10', '12']
                break
        }
        str1 = val.slice(0, 4) + '-' + mon[0]
        str2 = val.slice(0, 4) + '-' + mon[1]
    } else if (val.includes('半年度')) {
        if (val.slice(5, 6) == '上') {
            str1 = val.slice(0, 4) + '-' + '01'
            str2 = val.slice(0, 4) + '-' + '06'
        } else {
            str1 = val.slice(0, 4) + '-' + '07'
            str2 = val.slice(0, 4) + '-' + '12'
        }
    }
    return [str1, str2]
}
</script>

<style scoped lang="less">
.top_bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#receivablesStatisticDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
</style>
