<template>
    <BasicEditModalSlot title="收支统计" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <SearchBar v-model="params" :options="searchOptions" :showSelectedLine="false" @change="initalChart" />
        <div id="chartDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'

const props = defineProps({
    visible: Boolean,
    clientId: Array,
})
const emit = defineEmits(['update:visible'])

const params = ref({
    clientIds: [],
    paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        params.value.clientIds = clientId?.value as any
        initalChart()
    }
})
const modalClose = () => {
    params.value = { clientIds: [], paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')] }
    emit('update:visible', false)
}

let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    chartRef && chartRef?.dispose && chartRef.dispose()
    const data = await request.post(`/api/hr-bill-totals/revenue-expenditure`, {
        clientIds: params.value.clientIds,
        paymentDateStart: params.value.paymentDate[0] ? params.value.paymentDate[0] + '-01' : undefined,
        paymentDateEnd: params.value.paymentDate[1] ? params.value.paymentDate[1] + '-01' : undefined,
    })
    chartRef = echarts.init(document.getElementById('chartDom') as HTMLElement)
    const option: echarts.EChartsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: [
            {
                type: 'value',
            },
        ],
        yAxis: [
            {
                type: 'category',
                axisTick: {
                    show: false,
                },
                data: data.map((i) => i.clientName),
            },
        ],
        series: [
            {
                name: '利润',
                type: 'bar',
                label: {
                    show: true,
                    position: 'inside',
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.profit),
            },
            {
                name: '收入',
                type: 'bar',
                stack: 'Total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.revenue),
            },
            {
                name: '支出',
                type: 'bar',
                stack: 'Total',
                label: {
                    show: true,
                    position: 'left',
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.expenditure),
            },
        ],
    }
    chartRef.setOption(option)
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'monthrange',
        allowClear: false,
    },
]
</script>

<style scoped lang="less">
#chartDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
</style>
