<template>
    <BasicEditModalSlot title="年度曲线" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <SearchBar v-model="params" :options="searchOptions" @change="initalChart" :showSelectedLine="false" />
        <div id="yearLineDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'

const props = defineProps({
    visible: Boolean,
    clientId: String,
})
const emit = defineEmits(['update:visible'])

const params = ref({
    clientId: undefined,
    paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        params.value.clientId = clientId?.value as any
        initalChart()
    }
})

const modalClose = () => {
    params.value = { clientId: undefined, paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')] }
    emit('update:visible', false)
}

let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    chartRef && chartRef?.dispose && chartRef.dispose()
    const data = await request.post(`/api/hr-bill-totals/annual-curve`, {
        clientId: params.value.clientId,
        paymentDateStart: params.value.paymentDate[0] + '-01',
        paymentDateEnd: params.value.paymentDate[1] + '-01',
    })
    chartRef = echarts.init(document.getElementById('yearLineDom') as HTMLElement)
    const option: echarts.EChartsOption = {
        tooltip: {
            trigger: 'axis',
        },
        legend: {},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((i) => moment(i.paymentDateSplicing).format('YYYY-MM')),
        },
        yAxis: {
            type: 'value',
        },
        series: [
            {
                name: '账单人数',
                type: 'line',
                data: data.map((i) => i.staffNum),
            },
            {
                name: '社保合计',
                type: 'line',
                data: data.map((i) => i.socialSecurityTotal),
            },
            {
                name: '公积金合计',
                type: 'line',
                data: data.map((i) => i.accumulationFundTotal),
            },
            {
                name: '平均工资',
                type: 'line',
                data: data.map((i) => i.averageWage),
            },
            {
                name: '工资合计',
                type: 'line',
                data: data.map((i) => i.salaryTotal),
            },
            {
                name: '服务费合计',
                type: 'line',
                data: data.map((i) => i.serviceFeeTotal),
            },
            {
                name: '结算费用合计',
                type: 'line',
                data: data.map((i) => i.total),
            },
        ],
    }
    chartRef.setOption(option)
}

onUnmounted(() => {
    chartRef && chartRef?.dispose && chartRef.dispose()
})

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientId',
        type: 'clientSelectTree',
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'monthrange',
        allowClear: false,
    },
]
</script>

<style scoped lang="less">
#yearLineDom {
    width: 100%;
    height: 50vh;
    margin-top: 20px;
}
</style>
