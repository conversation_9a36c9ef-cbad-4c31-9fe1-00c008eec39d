<template>
    <BasicEditModalSlot title="数据统计" :visible="visible" @cancel="modalClose" :footer="null" width="1200px" centered>
        <SearchBar v-model="params" :options="searchOptions" @change="initalChart" :showSelectedLine="false" />
        <div id="dataStatisticDom"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, toRefs, watch } from 'vue'
import * as echarts from 'echarts'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import moment from 'moment'

const props = defineProps({
    visible: Boolean,
    clientId: Array,
})
const emit = defineEmits(['update:visible'])

const params = ref({
    clientIds: [],
    paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')],
})

const { visible, clientId } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        params.value.clientIds = clientId?.value as any
        initalChart()
    }
})
const modalClose = () => {
    params.value = { clientIds: [], paymentDate: [moment().format('YYYY-01'), moment().format('YYYY-12')] }
    emit('update:visible', false)
}

let chartRef: echarts.EChartsType | null = null
const initalChart = async () => {
    chartRef && chartRef?.dispose && chartRef.dispose()
    const data = await request.post(`/api/hr-bill-totals/query-bill-statistics`, {
        clientIds: params.value.clientIds,
        paymentDateStart: params.value.paymentDate[0] + '-01',
        paymentDateEnd: params.value.paymentDate[1] + '-01',
    })
    console.log('data', data)

    chartRef = echarts.init(document.getElementById('dataStatisticDom') as HTMLElement)
    const option: echarts.EChartsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {
            left: 'center',
        },
        grid: {
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: {
            type: 'value',
        },
        yAxis: {
            type: 'category',
            data: data.map((i) => i.clientName || '-'),
            axisLabel: {
                formatter: function (params) {
                    let newParamsName = ''
                    const paramsNameNumber = params.length
                    const provideNumber = 5
                    const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
                    if (paramsNameNumber > provideNumber) {
                        for (let p = 0; p < rowNumber; p++) {
                            let tempStr = ''
                            const start = p * provideNumber
                            const end = start + provideNumber
                            if (p == rowNumber - 1) {
                                tempStr = params.substring(start, paramsNameNumber)
                            } else {
                                tempStr = params.substring(start, end) + '\n'
                            }
                            newParamsName += tempStr
                        }
                    } else {
                        newParamsName = params
                    }
                    return newParamsName
                },
            },
        },
        series: [
            {
                name: '账单人数',
                type: 'bar',
                barMaxWidth: 40,
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.staffNum ?? 0),
            },
            {
                name: '社保合计',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.socialSecurityTotal ?? 0),
            },
            {
                name: '公积金合计',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.accumulationFundTotal ?? 0),
            },
            {
                name: '平均工资',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.averageWage ?? 0),
            },
            {
                name: '工资合计',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.salaryTotal ?? 0),
            },
            {
                name: '服务费合计',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.serviceFeeTotal ?? 0),
            },
            {
                name: '结算费用合计',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                },
                emphasis: {
                    focus: 'series',
                },
                data: data.map((i) => i.total ?? 0),
            },
        ],

        dataZoom: [
            {
                type: 'slider', // inside slider
                show: true,
                yAxisIndex: 0,
                maxValueSpan: 5,
                start: 0,
                end: data.length < 5 ? 100 : 5,
            },
            /* {
                type: 'slider', // inside slider
                show: true,
                xAxisIndex: 0,
            }, */
        ],
    }
    chartRef.setOption(option)
}

onUnmounted(() => {
    chartRef && chartRef.dispose()
})

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'monthrange',
        allowClear: false,
    },
]
</script>

<style scoped lang="less">
#dataStatisticDom {
    width: 100%;
    height: 60vh;
    margin-top: 20px;
}
</style>
