<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
    </div>
    <!-- v-if="showTable" -->
    <Table
        class="table"
        ref="tableRef"
        :bordered="true"
        :columns="columns"
        :dataSource="
            tableData
                ? [...tableData.slice(pagination.pageSize * (pagination.current - 1), pagination.pageSize * pagination.current)]
                : []
        "
        :row-key="(record, index) => record.rid ?? index"
        size="small"
        :pagination="pagination"
        :scroll="{ x: 100 }"
        :rowSelection="{
            selectedRowKeys: selectedStaff.map((i) => i.rid),
            onChange: (keys, rows) => selStaff(rows),
        }"
    />
</template>

<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
// import DataAnalysis from './DataAnalysis.vue'
import request from '/@/utils/request'
import { getDynamicText } from '/@/utils'
import moment from 'moment'
import { formatDynamicColns } from './util'
import { v4 } from 'uuid'
import downFile from '/@/utils/downFile'

const params = ref<{ unitNumber?: string; clientId?: string; paymentDate?: string; name?: string; certificateNum?: string }>({
    paymentDate: moment().format('YYYY-MM'),
})
const columns = ref<Recordable[]>([])
const tableData = ref<Recordable[]>([])
const selectedStaff = ref<Recordable[]>([])
const tableRef = ref()
const searchData = (val, data) => {
    postTableData()
}

const exportText = computed(() => {
    return getDynamicText('导出', params.value, selectedStaff.value)
})

const exportData = async () => {
    if (selectedStaff.value.length > 0) {
        let ids: any = []
        selectedStaff.value?.forEach((el: any) => {
            ids.push(...el.strIds)
        })
        await downFile('post', 'api/hr-bill-compare-result-detail/export', '对账明细.xlsx', { ids: ids })
    } else {
        await downFile('post', 'api/hr-bill-compare-result-detail/export', '对账明细.xlsx', { ...params.value })
    }
}

watch(
    [() => params.value, () => selectedStaff.value],
    ([newParams, newSelArr], [oldParams, oldSelArr]) => {
        dynamicQuery()
    },
    { deep: true },
)
onMounted(async () => {
    await postTableData()
})
const dynamicQuery = async () => {
    if (exportText.value.indexOf('选中') != -1) {
        let body = {}
        body['ids'] = selectedStaff.value.map((item: any) => {
            return item.id
        })
        await fetchTotalTableData(body)
    } else if (exportText.value.indexOf('筛选') != -1) await fetchTotalTableData({ ...params.value })
    else await fetchTotalTableData()
}

const fetchTotalTableData = async (body = {}) => {
    // const res = await request.post('/api/hr-bill-details/get-total', body)
    // tableDataList.value = [{ ...res, unitNumber: '合计' }] as any
}

fetchTotalTableData()

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
    },
    {
        label: '员工姓名',
        key: 'staffName',
    },
    {
        label: '身份证号',
        key: 'idNo',
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
        allowClear: false,
    },
]

const pagination = ref({
    current: 1,
    pageSize: 10,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    size: 'default',
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
    onChange: (current) => {
        pagination.value.current = current
    },
    onShowSizeChange: (_current, size) => {
        pagination.value.pageSize = size
    },
})
// const selAll = (isSel) => {
//             selectedStaff.value = isSel ? [...tableData.value] : []
// }
const selStaff = (list) => {
    selectedStaff.value = list
}
const dynamicHeader = ref([])
const postTableData = async () => {
    let res = await request.post('/api/hr-bill-compare-result-detail/list', params.value)
    if (res) {
        formatDynamicColns(res, dynamicHeader)
        columns.value = dynamicHeader.value
        tableData.value = res.hrBillCompareResultDetailDTOList?.map((it) => ({
            ...it,
            rid: v4(),
        }))
        pagination.value.current = 1
        pagination.value.total = res.hrBillCompareResultDetailDTOList?.length
    }
}
</script>

<style scoped lang="less">
.table :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
