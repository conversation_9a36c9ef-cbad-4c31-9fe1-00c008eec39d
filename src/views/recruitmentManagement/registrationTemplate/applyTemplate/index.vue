<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'applyTemplateAdd'" @click="addRow('add')">新增</Button>
        <Button type="primary" v-auth="'applyTemplateDownload'" @click="templateDownload" class="downloadBtn">
            {{ exportText }}
        </Button>
        <Button type="primary" danger v-auth="'applyTemplateDele'" @click="deleteRow">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-templates/page"
        deleteApi="/api/hr-templates/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 添加简历模板 -->
    <AddModal
        v-model:visible="addVisible"
        :title="modalTitle"
        :currentValue="currentValue"
        :viewType="viewType"
        @confirm="addRowConfirm"
        @preview="seeRow"
    />
    <!-- 查看简历模板 -->
    <SeeModal v-model:visible="seeVisible" title="查看" :currentValue="seeCurrentValue" />
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import SeeModal from './seeModal.vue'
import AddModal from './addModal.vue'
import { downMultFile } from '/@/utils/downFile'
import { getDynamicText, getHaveAuthorityOperation, openNotification } from '/@/utils'

export default defineComponent({
    name: 'ApplyTemplate',
    components: {
        SeeModal,
        AddModal,
    },

    setup() {
        const frequencyStatesOptions = ref([])
        onMounted(() => {
            //frequency使用次数
            request.get('/api/hr-templates/frequency').then((res) => {
                let newFrequencyStatesOptions = []
                newFrequencyStatesOptions = res.map((item) => {
                    return { label: item, value: item }
                })
                newFrequencyStatesOptions.sort((a: any, b: any) => {
                    return a.value - b.value
                })
                frequencyStatesOptions.value = newFrequencyStatesOptions.sort()
            })
        })

        //筛选
        const params = ref<{}>({})
        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '模板名称',
                key: 'templateName',
            },

            {
                type: 'daterange',
                label: '创建时间',
                key: 'templateStartDateQuery',
            },
            {
                type: 'select',
                label: '使用次数',
                key: 'frequencyList',
                multiple: true,
                options: frequencyStatesOptions,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '模板名称',
                dataIndex: 'templateName',
                align: 'center',
                width: 250,
            },
            {
                title: '创建时间',
                dataIndex: 'createDate',
                align: 'center',
                width: 150,
            },

            {
                title: '使用次数',
                dataIndex: 'frequency',
                align: 'center',
                width: 100,
            },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
                fixed: 'right',
            },
        ]

        // 当前查看的数据
        // const currentValue = ref(null)
        const seeVisible = ref<boolean>(false)
        const seeCurrentValue = ref<object>({})
        const addVisible = ref<boolean>(false)
        const currentValue = ref<object>({})
        const viewType = ref<string>('')
        const modalTitle = ref<string>('新增')
        // 查看内容
        // const seeRow = (record) => {
        //     console.log(record)
        //     visible.value = true
        //     currentValue.value = record
        // }
        // 查看内容
        const seeRow = (record) => {
            seeVisible.value = true
            seeCurrentValue.value = record
        }
        const addRow = (viewTypeName, record) => {
            addVisible.value = true
            currentValue.value = record
            viewType.value = viewTypeName
            modalTitle.value = viewTypeName == 'add' ? '添加模板' : viewTypeName == 'edit' ? '编辑模板' : '复制模板'
        }
        const addRowConfirm = () => {
            if (viewType.value == 'edit') {
                tableRef.value.refresh()
            } else {
                tableRef.value.refresh(1)
            }
        }
        const handleOk = () => {
            seeVisible.value = false
        }

        //模板导出
        const templateDownload = () => {
            let ids: any[] = []
            let body = {}
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (exportText.value.indexOf('选中') != -1) {
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            request.post('/api/hr-templates/download', body).then((res: any[]) => {
                let urls: any[] = []
                let names: any[] = []
                res?.forEach((element: any) => {
                    if (element?.fileUrL) {
                        urls.push(element?.fileUrL)
                        names.push(element?.fileUrLName)
                    }
                })
                if (!urls.length) {
                    message.error('暂无可供下载的报名模板')
                    return
                }
                downMultFile('报名模板表批量导出', urls, names)
            })
        }

        // 批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id, false).then((res) => {
                // console.log(row.id)
                let tip = ''
                let success = '您选择的数据已删除成功'
                if (res.error_status) {
                    tip = res.error_status
                    success = ',选择的其它数据已删除成功'
                }
                if (res.success?.length) {
                    tip += success
                }
                openNotification(tip)
            })
        }
        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'applyTemplateSee',
                    show: true,
                    click: seeRow,
                },
                {
                    neme: '编辑',
                    auth: 'applyTemplateEdit',
                    show: (record) => {
                        return !record.frequency
                    },
                    click: (record) => addRow('edit', record),
                },
                {
                    neme: '复制',
                    auth: 'applyTemplateCopy',
                    show: true,
                    click: (record) => addRow('copy', record),
                },
                // {
                //     neme: '删除',
                //     auth: '',
                //     show: true,
                //     click: seeRow,
                // },
            ]),
        )

        // 多选
        const selectedRowsArr = ref([])
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selectedRowsArr.value)
        })

        return {
            tableData,
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 新增
            addRow,
            //查看
            seeRow,
            // 查看弹框显示隐藏
            seeVisible,
            seeCurrentValue,
            // 添加弹框显示隐藏
            addVisible,
            handleOk,
            currentValue,
            viewType,
            modalTitle,
            addRowConfirm,

            templateDownload,
            deleteRow,
        }
    },
})
</script>
<style lang="less" scoped>
.box {
    margin-top: 20px;
}
</style>
