<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1000px">
        <Form ref="formInline" :model="formData">
            <FormItem
                label="模板名称"
                name="templateName"
                :rules="{ required: true, message: '请输入模板名称', trigger: ['change', 'blur'] }"
            >
                <Input v-model:value="formData.templateName" placeholder="请输入模板名称" />
            </FormItem>
        </Form>
        <div class="param-box">
            <div class="title">
                <span>须填内容</span>
            </div>
            <div class="box-body">
                <div>
                    <Checkbox
                        v-model:checked="contentCheckAll"
                        :indeterminate="contentIndeterminate"
                        @change="onContentCheckAllChange"
                    >
                        全选
                    </Checkbox>
                </div>
                <br />
                <CheckboxGroup v-model:value="contentCheckedList">
                    <Card
                        v-for="contentItem in contentOptions"
                        :key="contentItem.id"
                        size="small"
                        class="checkboxGroupCard"
                        hoverable
                    >
                        <template #title>
                            <Tooltip
                                placement="topLeft"
                                :key="Math.random()"
                                :title="contentItem.label || ''"
                                class="myCheckboxName"
                            >
                                {{ contentItem.label }}
                            </Tooltip>
                        </template>
                        <template #extra>
                            <Checkbox
                                :value="contentItem.value"
                                @change="(ev) => contentOptionsChange(ev, contentItem)"
                                :disabled="['name', 'certificateType', 'certificateNum', 'phone'].includes(contentItem.name)"
                            />
                        </template>
                        <div class="requiredBox">
                            <span>是否必填：</span>
                            <Switch
                                v-model:checked="additionalObj[contentItem.id].required"
                                :disabled="['name', 'certificateType', 'certificateNum', 'phone'].includes(contentItem.name)"
                                checked-children="是"
                                un-checked-children="否"
                            />
                        </div>
                    </Card>
                </CheckboxGroup>
            </div>
        </div>
        <div class="param-box">
            <div class="title">
                <span>须传附件</span>
            </div>
            <div class="box-body">
                <div>
                    <Checkbox
                        v-model:checked="appendixCheckAll"
                        :indeterminate="appendixIndeterminate"
                        @change="onAppendixCheckAllChange"
                    >
                        全选
                    </Checkbox>
                </div>
                <br />
                <CheckboxGroup v-model:value="appendixCheckedList">
                    <!-- <Checkbox v-for="appendixItem in appendixOptions" :key="appendixItem.id" :value="appendixItem.value">
                        <Tooltip
                            placement="topLeft"
                            :key="Math.random()"
                            :title="appendixItem.label || ''"
                            class="myCheckboxName"
                        >
                            {{ appendixItem.label }}
                        </Tooltip>
                    </Checkbox> -->
                    <Card
                        v-for="appendixItem in appendixOptions"
                        :key="appendixItem.id"
                        size="small"
                        class="checkboxGroupCard"
                        hoverable
                    >
                        <template #title>
                            <Tooltip
                                placement="topLeft"
                                :key="Math.random()"
                                :title="appendixItem.label || ''"
                                class="myCheckboxName"
                            >
                                {{ appendixItem.label }}
                            </Tooltip>
                        </template>
                        <template #extra>
                            <Checkbox :value="appendixItem.value" />
                        </template>
                        <div class="requiredBox">
                            <span>是否必填：</span>
                            <Switch
                                v-model:checked="additionalObj[appendixItem.id].required"
                                checked-children="是"
                                un-checked-children="否"
                            />
                        </div>
                    </Card>
                </CheckboxGroup>
            </div>
        </div>
        <template #footer>
            <Button @click="cancel" class="btn">取消</Button>
            <Button @click="preview" type="primary" class="btn">预览</Button>
            <Button @click="confirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message, Card } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, reactive, onMounted, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import columns from './data'
import { json } from 'stream/consumers'

interface stateInterface {
    contentIndeterminate: Boolean
    contentCheckAll: Boolean
    contentCheckedList: any[]

    appendixIndeterminate: Boolean
    appendixCheckAll: Boolean
    appendixCheckedList: any[]

    formData: inObject
    additionalObj: inObject
}
export default defineComponent({
    name: 'AddModal',
    components: {
        Card: Card,
    },
    props: {
        viewType: {
            type: String,
            default: '',
            validator: function (value: string) {
                return ['', 'add', 'edit', 'copy'].indexOf(value) !== -1
            },
        },
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['update:visible', 'confirm', 'cancel', 'preview'],
    setup(props, { emit }) {
        const { currentValue, visible, viewType } = toRefs<any>(props)

        const state = reactive<stateInterface>({
            contentIndeterminate: false,
            contentCheckAll: false,
            contentCheckedList: [],

            appendixIndeterminate: false,
            appendixCheckAll: false,
            appendixCheckedList: [],

            formData: { templateName: '' },
            additionalObj: {},
        })
        const contentOptions = ref<any[]>([])
        const appendixOptions = ref<any[]>([])
        const contentOptionsObj: inObject = {}
        const appendixOptionsObj: inObject = {}

        const groupList: any = {}
        const initialAdditionalObj = ref<inObject>({})
        onMounted(() => {
            request.get('/api/hr-content-templates').then((res) => {
                let contentList: inObject[] = []
                let appendixList: inObject[] = []
                res?.forEach((element: inObject, index) => {
                    let data = { ...element, value: element?.id }
                    if (element.type == 'appendix') {
                        appendixList.push(data)
                        appendixOptionsObj[data.value] = data
                    } else {
                        contentList.push(data)
                        if (element?.groupName) {
                            console.log(element.groupName)
                            if (!groupList[element.groupName]) {
                                groupList[element.groupName] = []
                            }
                            groupList[element.groupName]?.push(data)
                        }
                        contentOptionsObj[data.value] = data
                    }
                    initialAdditionalObj.value[element.id] = { required: !!element.required }
                })
                state.additionalObj = JSON.parse(JSON.stringify(initialAdditionalObj.value))
                contentOptions.value = contentList
                appendixOptions.value = appendixList
            })
        })
        const onContentCheckAllChange = (e: any) => {
            Object.assign(state, {
                contentCheckedList: e.target.checked
                    ? contentOptions.value.map((item: any) => item.value)
                    : [
                          'b4733a4e05888602335b5e5e509a5e1a',
                          'edbe4d75a70c2f6712353a792bc6aa82',
                          '1acb0261ea9466643d7b53160e37e4d1',
                          '99dea0299d93c370489cbb7ceb68e191',
                      ],
                contentIndeterminate: false,
            })
        }
        watch(
            () => state.contentCheckedList,
            (val) => {
                state.contentIndeterminate = !!val.length && val.length < contentOptions.value.length
                state.contentCheckAll = val.length === contentOptions.value.length
            },
        )

        const contentOptionsChange = (ev, checkedValue) => {
            if (!checkedValue?.groupName) return
            let groupNameList = groupList[checkedValue.groupName]
            let groupIndex = groupNameList?.findIndex((item) => {
                return item.value == checkedValue.value
            })
            if (ev?.target?.checked) {
                if (groupIndex != 0) {
                    let groupList0: any = groupNameList[0]
                    if (!state.contentCheckedList.includes(groupList0?.value || '')) {
                        let newContentCheckedList = state.contentCheckedList.filter((item) => {
                            return item != groupList0?.value
                        })
                        nextTick(() => {
                            state.contentCheckedList = newContentCheckedList
                        })
                        message.success(`请先选中${groupList0.label}!`)
                    }
                }
            } else {
                if (groupIndex == 0) {
                    let groupNameValueList = groupNameList.map((item) => {
                        return item.value
                    })
                    let newContentCheckedList = state.contentCheckedList.filter((item) => {
                        return !groupNameValueList.includes(item)
                    })
                    nextTick(() => {
                        state.contentCheckedList = newContentCheckedList
                    })
                }
            }
        }

        const onAppendixCheckAllChange = (e: any) => {
            Object.assign(state, {
                appendixCheckedList: e.target.checked ? appendixOptions.value.map((item: any) => item.value) : [],
                appendixIndeterminate: false,
            })
        }
        watch(
            () => state.appendixCheckedList,
            (val) => {
                state.appendixIndeterminate = !!val.length && val.length < appendixOptions.value.length
                state.appendixCheckAll = val.length === appendixOptions.value.length
            },
        )
        //编辑
        watch(visible, async () => {
            if (visible.value) {
                if (!currentValue.value?.id) {
                    state.contentCheckedList = [
                        'b4733a4e05888602335b5e5e509a5e1a',
                        'edbe4d75a70c2f6712353a792bc6aa82',
                        '1acb0261ea9466643d7b53160e37e4d1',
                        '99dea0299d93c370489cbb7ceb68e191',
                    ]
                    return
                }
                let ref: inObject = await request.get('/api/hr-templates/content', { id: currentValue.value?.id })
                let appendixCheckedList: any = []
                let contentCheckedList: any = []
                ref?.hrContentTemplateDTOList?.forEach((element: inObject) => {
                    if (element.type == 'appendix') {
                        appendixCheckedList.push(element.id)
                    } else {
                        contentCheckedList.push(element.id)
                    }
                    state.additionalObj[element.id] = { required: !!element.required }
                })
                state.appendixCheckedList = appendixCheckedList
                state.contentCheckedList = contentCheckedList
                state.formData.templateName = ref?.templateName || ''
                if (viewType.value == 'copy') {
                    state.formData.templateName = ''
                }
            }
        })

        // confirm handle

        // cancel handle
        const cancel = () => {
            Object.assign(state, {
                contentIndeterminate: false,
                contentCheckAll: false,
                contentCheckedList: [],
                appendixIndeterminate: false,
                appendixCheckAll: false,
                appendixCheckedList: [],
                formData: { templateName: '' },
                additionalObj: JSON.parse(JSON.stringify(initialAdditionalObj.value)),
            })
            emit('update:visible', false)
            emit('cancel')
        }
        const formInline = ref()
        const confirm = () => {
            preview('word')
            // return
            formInline.value
                .validate()
                .then(() => {
                    let api: any = null
                    let contentId = [...(state?.contentCheckedList || []), ...(state?.appendixCheckedList || [])].toString()

                    if (!contentId) {
                        message.warning('请选择须填内容或须填附件!')
                        return
                    }

                    if (viewType.value == 'edit') {
                        api = request.put('/api/hr-templates', {
                            ...state.formData,
                            contentId,
                            id: currentValue.value?.id,
                            preview: dom.value,
                            requiredFieldsJson: JSON.stringify(state.additionalObj),
                        })
                    } else {
                        api = request.post('/api/hr-templates', {
                            ...state.formData,
                            contentId,
                            preview: dom.value,
                            requiredFieldsJson: JSON.stringify(state.additionalObj),
                        })
                    }
                    console.log({
                        ...state.formData,
                        contentId,
                        preview: dom.value,
                        requiredFieldsJson: JSON.stringify(state.additionalObj),
                    })
                    // edit
                    api.then((ref: any) => {
                        console.log(ref)
                        if (viewType.value == 'add') {
                            message.success('新增成功!')
                        } else if (viewType.value == 'edit') {
                            message.success('编辑成功!')
                        } else {
                            message.success('复制成功!')
                        }
                        emit('confirm')
                        cancel()
                    })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        //预览并生成简历模板
        const preview = (type) => {
            let contentOptionsList = state?.contentCheckedList?.map((item) => {
                let contentOptionsItem = contentOptionsObj[item]
                return { ...contentOptionsItem, required: !!state.additionalObj[contentOptionsItem?.id]?.required }
            })
            let appendixOptionsList = state?.appendixCheckedList?.map((item) => {
                let appendixOptionsItem = appendixOptionsObj[item]
                return { ...appendixOptionsItem, required: !!state.additionalObj[appendixOptionsItem?.id]?.required }
                // return appendixOptionsObj[item]
            })
            let hrContentTemplateDTOList = [...contentOptionsList, ...appendixOptionsList].sort((a, b) => {
                return a.orders - b.orders
            })
            if (type == 'word') {
                generateView(hrContentTemplateDTOList)
            } else {
                emit('preview', { hrContentTemplateDTOList })
            }
        }
        const dom = ref('')
        const generateView = (myOptionsDto) => {
            let myOptions: any = []
            let myOptionsTable: any = []
            let avatarArr: any = []
            for (let index = 0; index < myOptionsDto.length; index++) {
                if (myOptionsDto[index].type == 'table') {
                    myOptionsTable.push(myOptionsDto[index])
                    continue
                }
                if (myOptionsDto[index].type == 'appendix') {
                    continue
                }

                let itemForm: any = {}

                if (myOptionsDto[index].name == 'avatar') {
                    avatarArr.push({
                        ...myOptionsDto[index],
                        slots: 'img',
                        type: 'slots',
                    })
                    continue
                } else {
                    let width = parseInt(myOptionsDto[index]?.width) ? parseInt(myOptionsDto[index]?.width) * 33.33 + '%' : ''

                    itemForm = {
                        ...myOptionsDto[index],
                        width: width,
                    }
                }
                myOptions.push(itemForm)
            }
            let TopTenDom = ''
            if (avatarArr.length) {
                let TopTen = myOptions.splice(0, 8)
                TopTenDom =
                    '<table border="1" cellspacing="0" cellpadding="0" class="table" style="width: 100%;border-spacing: 0">'
                for (let index = 0; index < TopTen.length; index += 2) {
                    TopTenDom += '<tr>'
                    let TopTenName = TopTen[index].name
                    if (TopTen[index]?.type == 'change' || TopTen[index]?.type == 'rangePicker') {
                        TopTenName = TopTen[index].name + 'Label'
                    }

                    TopTenDom += `<td style=" height: 45px;width: 16.66%;text-align: center;" >${TopTen[index].label}</td><td  style=" height: 45px;width: 16.66%;text-align: center;" pname="${TopTenName}"></td>`
                    if (TopTen[index + 1]?.label) {
                        let TopTenName1 = TopTen[index + 1].name
                        if (TopTen[index + 1]?.type == 'change' || TopTen[index + 1]?.type == 'rangePicker') {
                            TopTenName1 = TopTen[index + 1].name + 'Label'
                        }
                        TopTenDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;">${
                            TopTen[index + 1].label
                        }</td><td  style=" height: 45px;width: 16.66%;text-align: center;" pname="${TopTenName1}"></td>`
                    } else {
                        TopTenDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;"></td><td  style=" height: 45px;width: 16.66%;text-align: center;"></td>`
                    }
                    if (index == 0) {
                        TopTenDom += '<td width="20%" rowspan="5" pname="avatar">照片</td>'
                    }
                    TopTenDom += '</tr>'
                }
                TopTenDom += '</table>'
            }
            let contentDom = ''
            if (myOptions.length) {
                contentDom =
                    '<table border="1" cellspacing="0" cellpadding="0" class="table" style="width: 100%;border-spacing: 0">'
                for (let index = 0; index < myOptions.length; index += 3) {
                    contentDom += '<tr>'
                    let myOptionsName = myOptions[index].name
                    if (myOptions[index]?.type == 'change' || myOptions[index]?.type == 'rangePicker') {
                        myOptionsName = myOptions[index].name + 'Label'
                    }
                    contentDom += `<td style=" height: 45px;width: 16.66%;text-align: center;" >${myOptions[index].label}</td><td  style=" height: 45px;width: 16.66%;text-align: center;" pname="${myOptionsName}"></td>`
                    if (myOptions[index + 1]?.label) {
                        let myOptionsName1 = myOptions[index + 1].name
                        if (myOptions[index + 1]?.type == 'change' || myOptions[index + 1]?.type == 'rangePicker') {
                            myOptionsName1 = myOptions[index + 1].name + 'Label'
                        }
                        contentDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;">${
                            myOptions[index + 1].label
                        }</td><td  style=" height: 45px;width: 16.66%;text-align: center;" pname="${myOptionsName1}"></td>`
                    } else {
                        contentDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;"></td><td  style=" height: 45px;width: 16.66%;text-align: center;"></td>`
                    }
                    if (myOptions[index + 2]?.label) {
                        let myOptionsName2 = myOptions[index + 2].name
                        if (myOptions[index + 2]?.type == 'change' || myOptions[index + 2]?.type == 'rangePicker') {
                            myOptionsName2 = myOptions[index + 2].name + 'Label'
                        }
                        contentDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;">${
                            myOptions[index + 2].label
                        }</td><td  style=" height: 45px;width: 16.66%;text-align: center;" pname="${myOptionsName2}"></td>`
                    } else {
                        contentDom += `<td  style=" height: 45px;width: 16.66%;text-align: center;"></td><td  style=" height: 45px;width: 16.66%;text-align: center;"></td>`
                    }
                    contentDom += '</tr>'
                }
                contentDom += '</table>'
            }
            let myOptionsTableDom = ''
            if (myOptionsTable.length) {
                for (let index = 0; index < myOptionsTable.length; index++) {
                    let myOptionsTableItem = columns[myOptionsTable[index].name + 'Columns'] || []
                    if (!myOptionsTableItem.length) {
                        continue
                    }
                    myOptionsTableDom +=
                        '<table border="1" cellspacing="0" cellpadding="0" class="table" style="width: 100%;border-spacing: 0">'
                    myOptionsTableDom += '<tr>'
                    myOptionsTableDom += `<td rowspan="5" style="width: 15%;text-align: center;" >${myOptionsTable[index].label}</td>`
                    myOptionsTableItem?.forEach((element) => {
                        myOptionsTableDom += `<td style=" height: 45px;width: 16.66%;text-align: center;" >${element?.title}</td>`
                    })
                    myOptionsTableDom += '</tr>'

                    if (myOptionsTable[index].name == 'firstEducation' || myOptionsTable[index].name == 'highestEducationLists') {
                        myOptionsTableDom += '<tr>'
                        myOptionsTableItem?.forEach((element) => {
                            myOptionsTableDom += `<td style=" height: 45px;width: 16.66%;text-align: center;"  pname="${
                                myOptionsTable[index].name + '[' + 0 + '].' + element.dataIndex
                            }"></td>`
                        })
                        myOptionsTableDom += '</tr>'
                    } else {
                        for (let idx = 0; idx < 4; idx++) {
                            myOptionsTableDom += '<tr>'
                            myOptionsTableItem?.forEach((element) => {
                                myOptionsTableDom += `<td style=" height: 45px;width: 16.66%;text-align: center;"  pname="${
                                    myOptionsTable[index].name + '[' + idx + '].' + element.dataIndex
                                }"></td>`
                            })
                            myOptionsTableDom += '</tr>'
                        }
                    }
                    myOptionsTableDom += '</table>'
                }
            }

            dom.value = TopTenDom + contentDom + myOptionsTableDom
            console.log(dom.value)
        }
        return {
            formInline,
            ...toRefs(state),
            confirm,
            cancel,
            preview,

            contentOptions,
            onContentCheckAllChange,
            contentOptionsChange,

            appendixOptions,
            onAppendixCheckAllChange,
        }
    },
})
</script>
<style scoped lang="less">
.param-box {
    margin: 20px 0px;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    box-sizing: border-box;
    .title {
        height: 43px;
        background-color: #6894fe;
        color: #fff;
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .btns {
            .ant-btn-link {
                color: #fff;
            }
        }
    }
    .box-body {
        padding: 20px;
    }
}
:deep(.ant-checkbox-group) {
    width: 100%;
    .ant-checkbox-wrapper {
        width: calc(25% - 8px);
        // line-height: 37px;
        margin-left: 0px;
    }
}
:deep(.myCheckboxName) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: calc(100% - 50px);
    vertical-align: bottom;
}
.checkboxGroupCard {
    display: inline-block;
    width: 32.33%;
    margin-top: 1%;
    &:nth-child(3n - 1) {
        margin: 1% 1% 0 1%;
    }
}
.requiredBox {
    display: flex;
    justify-content: space-between;
}
</style>
