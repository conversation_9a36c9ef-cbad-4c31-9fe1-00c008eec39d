<template>
    <BasicEditModalSlot
        :zIndex="1009"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <slot name="header"></slot>
        <div class="header-wrapper">
            <div>
                <span class="label">招聘单位：</span><span>{{ isAudit ? currentValue?.clientName : '' }}</span>
            </div>
            <div>
                <span class="label">岗位名称：</span><span>{{ isAudit ? currentValue?.stationName : '' }}</span>
            </div>
            <div>
                <span class="label">报名编号：</span><span>{{ isAudit ? currentValue?.numbers : '' }}</span>
            </div>
        </div>
        <Form ref="formInline" class="form-flex" :model="formData" :label-col="{ style: { width: '130px' } }" :rules="rules">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem
                    :width="itemForm.width"
                    :item="itemForm"
                    v-model:value="formData[itemForm.name]"
                    :class="itemForm.name"
                    :disabled="true"
                    :label-col="labelCol"
                >
                    <template #img>
                        <ImportImg v-model:imageUrl="formData.avatar" :disabled="true" />
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <div class="tableBox" v-for="tableItem in myOptionsTable" :key="tableItem.name">
            <label>
                {{ tableItem.label }}
            </label>
            <Table
                :columns="columns[tableItem.name + 'Columns']"
                :data-source="formData[tableItem.name] || []"
                :pagination="false"
                :row-key="(record) => record.id + tableItem.name"
                class="smallTable"
                bordered
            />
        </div>
        <div class="tableBox">
            <label>附件</label>
            <div
                v-for="appendixItem in myOptionsAppendix"
                :key="appendixItem.name"
                class="hrAppendixListBox"
                @click="previewFile(appendixItem?.appendixId?.fileUrl || '')"
            >
                <Tooltip placement="top">
                    <template #title>
                        <span>{{ appendixItem?.label }}</span>
                    </template>
                    <a class="enclosure"> <PaperClipOutlined />{{ appendixItem?.label }} </a>
                </Tooltip>
            </div>
        </div>
        <slot name="footer"></slot>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { previewFile } from '/@/utils'

import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'

import columns from './data'
import downFile from '/@/utils/downFile'

export default defineComponent({
    name: 'SeeModal',
    components: { PaperClipOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        isAudit: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['update:visible', 'confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/users'
        const { title, currentValue, visible, isAudit } = toRefs(props)

        watch(visible, async () => {
            if (visible.value) {
                console.log(currentValue.value)
                let data: any = {}
                if (isAudit.value) {
                    data = await request.get('/api/hr-templates/content', { id: currentValue.value?.registerTemplateId })
                } else {
                    if (currentValue.value?.id && !isAudit.value) {
                        data = await request.get('/api/hr-templates/content', { id: currentValue.value?.id })
                    } else {
                        data = currentValue.value
                    }
                }
                generateView(data?.hrContentTemplateDTOList || [])
            }
        })

        let myOptionsTable = ref<inObject>([])
        let myOptionsAppendix = ref<inObject>([])

        let myOptions = ref<valuesAndRules[]>([])
        let avatarIndex: any = null
        let externalGroupName: any = ''
        // Form 实例
        const formInline = ref(null) as any
        // Form Data
        const formData = ref<any>({})
        const rules = ref<any>([])
        const generateView = (myOptionsDto) => {
            for (let index = 0; index < myOptionsDto.length; index++) {
                if (myOptionsDto[index].type == 'table') {
                    myOptionsTable.value.push(myOptionsDto[index])
                    continue
                }
                if (myOptionsDto[index].type == 'appendix') {
                    myOptionsAppendix.value.push(myOptionsDto[index])
                    continue
                }

                let itemForm: any = {}

                if (myOptionsDto[index].name == 'avatar') {
                    avatarIndex = index
                    itemForm = {
                        ...myOptionsDto[index],
                        slots: 'img',
                        type: 'slots',
                    }
                } else {
                    let width = parseInt(myOptionsDto[index]?.width) ? parseInt(myOptionsDto[index]?.width) * 33.33 + '%' : ''

                    itemForm = {
                        ...myOptionsDto[index],
                        width: width,
                    }
                }
                if (itemForm.type == 'switch') {
                    itemForm.checkText = '是'
                    itemForm.unCheckText = '否'
                }

                if (typeof avatarIndex == 'number') {
                    if ([avatarIndex + 2, avatarIndex + 4, avatarIndex + 6].includes(index)) {
                        itemForm.showbr = true //换行
                    }
                }

                let insideGroupName = myOptionsDto[index]?.groupName || ''

                if (externalGroupName != insideGroupName) {
                    if (index > 0) {
                        if (itemForm?.showbr == true) {
                            myOptions.value[myOptions.value.length - 1].showbr = false
                        } else {
                            myOptions.value[myOptions.value.length - 1].showbr = true
                        }
                    }
                    externalGroupName = insideGroupName
                }
                if (itemForm.optionsName) {
                    itemForm.options = ref([])
                    dictionaryDataStore()
                        .setDictionaryData(itemForm.optionsName)
                        .then((res: LabelValueOptions) => {
                            itemForm.options.value = res
                        })
                }
                myOptions.value.push(itemForm)
            }

            // FormData rules 初始值
            const { values: initFormData, rules: myRules } = getValuesAndRules(myOptions.value)
            rules.value = myRules
            if (!isAudit.value) formData.value = initFormData
            if (isAudit.value) {
                let appendixJson = JSON.parse(currentValue.value?.appendixJson || '{}')
                let newMyOptionsAppendix: any = []
                myOptionsAppendix.value.forEach((item) => {
                    if (appendixJson[item?.name]) {
                        if (appendixJson[item?.name]?.appendixIds?.length) {
                            appendixJson[item?.name]?.appendixIds.forEach((element, index) => {
                                let originName = ''
                                if (appendixJson[item?.name]?.appendixIds?.length == 1) {
                                    originName = item?.label
                                } else if (item?.hrCertificateDTO?.certificateAttribute == 1) {
                                    originName = index == 0 ? '身份证正面照片' : '身份证反面照片'
                                } else {
                                    originName = item?.label + '(' + (index + 1) + ')'
                                }
                                newMyOptionsAppendix.push({
                                    label: originName,
                                    appendixId: element,
                                    name: item?.name,
                                })
                            })
                        }
                    }
                })
                console.log(newMyOptionsAppendix, 911)
                let registrationJson = JSON.parse(currentValue.value?.registrationJson || '{}')
                if (registrationJson.sex) {
                    registrationJson.sex = +registrationJson.sex
                }
                formData.value = Object.assign({}, initFormData, registrationJson)
                myOptionsAppendix.value = newMyOptionsAppendix
            }
        }

        //表格数据
        const tableData = ref<inObject[]>([])
        // reset formData
        const resetFormData = () => {
            myOptionsTable.value = []
            myOptionsAppendix.value = []
            myOptions.value = []
            avatarIndex = null
            externalGroupName = ''

            formData.value = {}
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            resetFormData()
            emit('update:visible', false)
        }
        const downUrl = (item) => {
            if (item?.appendixId) {
                downFile('get', item?.appendixId?.fileUrl || '', item.label, {})
            }
        }
        return {
            previewFile,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,

            tableData,

            myOptionsTable,
            columns,
            myOptionsAppendix,

            downUrl,

            resetFormData,

            labelCol: {
                style: { width: '130px' },
            },
        }
    },
})
</script>
<style scoped lang="less">
.header-wrapper {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 20px;
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    :deep(.avatar) {
        position: absolute;
        right: 1%;
    }
}

.ant-modal-body {
    padding: 24px 0 0 !important;
    & > .ant-form.ant-form-horizontal {
        margin: 0 24px 24px;
    }
}
.tableBox {
    display: flex;
    margin: 0 24px 24px;
    label {
        text-align: center;
        width: 130px;
    }
    .smallTable {
        flex: 1;
        // margin: 0 15px 30px;
        // border: 1px solid #e8e8e8;
        :deep(.ant-table-thead > tr > th) {
            background-color: #fafafa;
            color: #000;
        }
    }
}
.hrAppendixListBox {
    display: inline-block;
    padding-right: 10px;
    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
</style>
