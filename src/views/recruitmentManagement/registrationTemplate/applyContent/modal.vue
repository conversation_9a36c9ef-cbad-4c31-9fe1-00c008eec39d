<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" :disabled="viewType == 'see'" v-model:value="formData[item.name]">
                    <template #optionsList>
                        <div style="width: 100%">
                            <div
                                class="flex-input"
                                v-for="(itemNote, idx) in formData.optionsList"
                                :key="itemNote.itemValue + 'qeqwe'"
                            >
                                <Input class="other-input" :disabled="viewType == 'see'" v-model:value="itemNote.itemName" />
                                <template v-if="viewType != 'see'">
                                    <MinusCircleOutlined
                                        style="margin-left: 10px; color: #ef5959"
                                        v-if="!itemNote.id && formData.optionsList.length > 1"
                                        @click="delStaffNode(itemNote, idx)"
                                    />
                                    <PlusCircleOutlined
                                        style="margin-left: 10px; color: #1890ff"
                                        v-if="idx == formData.optionsList.length - 1"
                                        @click="addStaffNode"
                                    />
                                </template>
                            </div>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, computed, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { formItemTypeList } from '/@/utils/dictionaries'
import { valuesAndRules } from '/#/component'
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import { Item } from 'ant-design-vue/lib/menu'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'ApplyContentModal',
    components: { PlusCircleOutlined, MinusCircleOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: String,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const { title, currentValue, visible, viewType } = toRefs(props)
        const templateWidth: Array<Object> = [
            { label: '33%', value: 1 },
            { label: '66%', value: 2 },
            { label: '100%', value: 3 },
        ]
        const myFormItemTypeList = formItemTypeList.map((item) => {
            let disabled = false
            // if (item.value == 'table' || item.value == 'appendix' || item.value == 'img') {
            //     disabled = true
            // }
            return { ...item, disabled }
        })

        const formData = ref<any>({})
        const myOptions = ref<any>({})
        const optionsList = ref([])
        watch(
            () => {
                return { type: formData.value?.optionsList }
            },
            () => {
                optionsList.value =
                    formData.value?.optionsList
                        ?.map((item) => {
                            return {
                                label: item.itemName,
                                value: item.itemValue,
                                ...item,
                            }
                        })
                        ?.filter((item) => {
                            return !!item.itemName
                        }) || []
            },
            { immediate: true, deep: true },
        )
        watch(
            () => {
                return { type: formData.value?.type, value: viewType.value }
            },
            () => {
                myOptions.value = [
                    {
                        label: '内容标题',
                        name: 'label',
                    },
                    {
                        label: '内容类型',
                        name: 'type',
                        type: 'change',
                        ruleType: 'string',
                        options: myFormItemTypeList,
                        onChange: (val) => {
                            formData.value.defaults = null
                            if (val == 'switch') formData.value.defaults = false
                        },
                        disabled: viewType.value == 'edit',
                    },
                    {
                        label: '默认内容',
                        name: 'defaults',
                        type: formData.value?.type || 'string',
                        required: false,
                        checkText: '是',
                        unCheckText: '否',
                        options: optionsList,
                        ruleType: 'any',
                        show: !['table', 'appendix', 'img'].includes(formData.value?.type),
                    },
                    {
                        label: '展示宽度',
                        name: 'width',
                        required: false,
                        type: 'change',
                        ruleType: 'number',
                        options: templateWidth,
                        default: 1,
                        show: viewType.value == 'add',
                    },
                    {
                        label: '单选选择项',
                        name: 'optionsList',
                        slots: 'optionsList',
                        type: 'slots',
                        width: '100%',
                        required: false,
                        ruleType: 'array',
                        show: formData.value?.type == 'change',
                        default: [
                            {
                                itemName: '',
                                itemValue: 1,
                                displayOrder: 1,
                            },
                        ],
                    },
                ] as valuesAndRules[]
            },
            { immediate: true, deep: true },
        )

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        // formData.value = initFormData
        function randomString(e) {
            e = e || 32
            let t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
                a = t.length,
                n = ''
            for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
            return n
        }
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, currentValue.value) }
                if (currentValue.value?.id) {
                    formData.value.defaults = JSON.parse(currentValue.value?.defaults || '{}')?.value
                    if (formData.value.type == 'change') {
                        dictionaryDataStore()
                            .setDictionaryData(formData.value?.optionsName, '', 'get', true)
                            .then((res: LabelValueOptions) => {
                                let newRes: any = res
                                if (!res?.length) {
                                    newRes = [
                                        {
                                            itemName: '',
                                            itemValue: 1,
                                            displayOrder: 1,
                                        },
                                    ]
                                }
                                formData.value.optionsList = newRes
                            })
                    }
                } else {
                    formData.value.name = randomString(6)
                }
            }
        })
        //添加员工标签
        const addStaffNode = () => {
            let maxItemValue = Math.max(...formData.value?.optionsList.map((item) => item.itemValue)) + 1
            formData.value?.optionsList?.push({
                itemName: '',
                itemValue: maxItemValue,
                displayOrder: maxItemValue,
            })
        }
        // 删除员工标签
        const delStaffNode = (item, index) => {
            formData.value.optionsList?.splice(index, 1)
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formData.value.optionsList = [
                {
                    itemName: '',
                    itemValue: 1,
                    displayOrder: 1,
                },
            ]
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    // @PostMapping("/hr-content-templates/index")
                    if (formData.value.type == 'change') {
                        let aa = formData.value?.optionsList?.every(function (ite) {
                            return !!ite.itemName
                        })
                        if (!aa) {
                            message.error('请将单选选择项添加完整!')
                            return
                        }
                    }
                    let defaults =
                        formData.value?.defaults || formData.value?.defaults == false
                            ? JSON.stringify({ value: formData.value?.defaults })
                            : null
                    request.post('/api/hr-content-templates/index', { ...formData.value, defaults: defaults }).then((ref) => {
                        console.log(ref)
                        message.success('新增成功!')
                        cancel()
                        emit('confirm')
                    })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            addStaffNode,
            delStaffNode,

            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
<style lang="less">
.flex-input {
    display: flex;
    align-items: center;
    margin-top: 10px;
    &:first-child {
        margin-top: 0;
    }
    .other-input {
        width: 85%;
    }
}
</style>
