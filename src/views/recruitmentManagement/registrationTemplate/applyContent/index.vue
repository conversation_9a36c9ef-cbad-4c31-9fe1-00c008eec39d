<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'applyContent_add'" @click="modifyRow('add')">新增</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-content-templates/page"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #Switch="{ record }">
            <Switch
                v-model:checked="record.required"
                checked-children="是"
                un-checked-children="否"
                :checkedValue="1"
                :unCheckedValue="0"
                @click="onOf(record)"
                :disabled="switchDisabled || ['certificateType', 'certificateNum', 'phone'].includes(record.name)"
            />
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 查看弹框 -->
    <BasicEditModalSlot class="aaa" title="查看内容详情" v-model:visible="visible" @ok="handleOk" width="500px" :footer="null">
        <div class="box">
            <p><span>内容标题：</span>{{ watchData.label }}</p>
        </div>
        <div class="box">
            <p><span>内容类型：</span>{{ watchData.newLable }}</p>
        </div>
        <!-- <div class="box3">
            <span>是否必填：</span>
            <Switch
                v-model:checked="watchData.required"
                checked-children="是"
                un-checked-children="否"
                :checkedValue="1"
                :unCheckedValue="0"
                :disabled="true"
            />
        </div> -->
    </BasicEditModalSlot>
    <MyModal
        v-model:visible="modifyVisible"
        :title="modalTitle"
        :currentValue="currentValue"
        :viewType="viewType"
        @confirm="modifyRowConfirm"
        @cancel="modifyRowCancel"
    />
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import { formItemTypeList } from '/@/utils/dictionaries'
import { useAuth } from '/@/utils/hooks'
import { getHaveAuthorityOperation } from '/@/utils'
import modal from './modal.vue'
export default defineComponent({
    name: 'ApplyContentIndex',
    components: {
        MyModal: modal,
    },

    setup() {
        const switchDisabled = ref(!useAuth.value.buttons.find((i) => i == 'applyContentRequired'))
        console.log(!useAuth.value.buttons.find((i) => i == 'applyContentRequired'))
        const checked = ref<boolean>(false)

        //筛选
        const params = ref<{}>({})
        const requiredList = ref<LabelValueOptions>([
            {
                label: '是',
                value: 1,
            },
            {
                label: '否',
                value: 0,
            },
        ])
        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '内容标题',
                key: 'label',
            },

            {
                type: 'select',
                label: '内容类型',
                key: 'typeList',
                options: formItemTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '是否必填',
                key: 'requiredList',
                options: requiredList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '内容标题',
                dataIndex: 'label',
                align: 'center',
                width: 250,
            },
            {
                title: '内容类型',
                dataIndex: 'type',
                align: 'center',
                width: 150,
                customRender: ({ text }) => {
                    return formItemTypeList.find((item) => {
                        return text == item.value
                    })?.label
                },
            },

            // {
            //     title: '是否必填',
            //     dataIndex: 'required',
            //     align: 'center',
            //     width: 100,
            //     slots: { customRender: 'Switch' },
            // },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
                fixed: 'right',
            },
        ]

        // 当前查看的数据
        const visible = ref<boolean>(false)
        const watchData = ref<object>({})
        // 查看内容
        const seeRow = (record) => {
            // visible.value = true
            // let { type } = record
            // const obj: any = formItemTypeList.find((item) => {
            //     return item.value === type
            // })
            // record.newLable = obj?.label || ''
            // watchData.value = record
            modalTitle.value = '查看模板内容'
            viewType.value = 'see'
            currentValue.value = record
            modifyVisible.value = true
        }
        const handleOk = () => {
            visible.value = false
        }
        const viewType = ref()
        const modifyVisible = ref(false)
        const currentValue = ref(null)
        const modalTitle = ref('')
        const modifyRow = (viewTypeParameter, record) => {
            modalTitle.value = viewTypeParameter == 'add' ? '新增模板内容' : '编辑模板内容'
            viewType.value = viewTypeParameter
            currentValue.value = record
            modifyVisible.value = true
        }
        const modifyRowConfirm = () => {
            if (viewType.value == 'edit') {
                tableRef.value.refresh()
            } else {
                tableRef.value.refresh(1)
            }
        }
        const modifyRowCancel = () => {
            modifyVisible.value = false
        }

        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'applyContent_see',
                    show: true,
                    click: seeRow,
                },
                {
                    neme: '编辑',
                    auth: 'applyContent_edit',
                    show: true,
                    click: (record) => modifyRow('edit', record),
                },
            ]),
        )
        // 表格内开关
        const onOf = (record) => {
            console.log(record)
            request.put('/api/hr-content-templates', { id: record.id, required: record.required }).then((res) => {
                console.log(res)
            })
        }
        // 多选
        const selectedRowsArr = ref([])

        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 新增
            seeRow,
            // switch默认值
            checked,
            // 查看弹框显示隐藏
            visible,
            handleOk,
            // 查看数据
            watchData,
            // switch开关
            onOf,
            // 是否必填
            requiredList,
            switchDisabled,

            //新增编辑
            modifyRow,
            modifyVisible,
            modalTitle,
            currentValue,
            viewType,
            modifyRowConfirm,
            modifyRowCancel,
        }
    },
})
</script>
<style lang="less" scoped>
.box {
    margin-top: 30px;
}
.box3 {
    margin-top: 30px;
    margin-bottom: 50px;
}
</style>
