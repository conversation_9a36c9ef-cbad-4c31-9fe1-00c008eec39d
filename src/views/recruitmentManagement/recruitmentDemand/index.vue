<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData">
        <template #recruitmentStationIdList="itemForm">
            <PostTree
                v-model:value="params.recruitmentStationIdList"
                v-model:itemForm="searchOptions[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'recruitmentDemand_create'" @click="createRow">新建</Button>
        <Button type="primary" v-auth="'recruitmentDemand_sure'" @click="sureRow">批量确认</Button>
        <Button type="primary" v-auth="'recruitmentDemand_delete'" danger @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-recruitment-needs/page"
        deleteApi="/api/hr-recruitment-needs/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
        <template #jobName="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStationDTOList" :key="index">
                <div class="lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentStationName }}</div>
            </div>
        </template>
        <template #jobNumber="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStationDTOList" :key="index">
                <div class="lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentPeopleNumber }}</div>
            </div>
        </template>
        <template #examinationForm="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStationDTOList" :key="index">
                <div class="lines" v-if="index != 0"></div>
                <div>{{ item.examFormatName }}</div>
            </div>
        </template>
        <template #Requirement="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStationDTOList" :key="index">
                <div class="lines1" v-if="index != 0"></div>
                <Tooltip placement="top">
                    <template #title>
                        <span>{{ item.recruitmentTerm }}</span>
                    </template>
                    <div class="condition">{{ item.recruitmentTerm }}</div>
                </Tooltip>
            </div>
        </template>
    </BasicTable>

    <VModal
        v-model:visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <CreateModal :visible="showCreate" :title="modalTitle" :item="currentValue" @cancel="createClose" @confirm="createConfirm" />
</template>

<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import vModal from './vModal.vue'
import CreateModal from './CreateModal.vue'
import PostTree from '/@/views/user/postManage/postTree.vue'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, openNotification } from '/@/utils'
export default defineComponent({
    name: 'RecruitmentDemandIndex',
    components: {
        VModal: vModal,
        CreateModal,
        PostTree,
    },
    setup() {
        const examFormatList = ref<LabelValueOptions>([]) //考试形式
        const stationList = ref<LabelValueOptions>([]) //岗位
        onMounted(() => {
            //考试形式
            request.get('/api/com-code-tables/getCodeTableByInnerName/examFormat', {}).then((res) => {
                examFormatList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 岗位
            request.get('/api/hr-stations/list', {}).then((res) => {
                stationList.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
        })
        // 状态
        const applyStatusList = ref<LabelValueOptions>([
            {
                label: '待确认',
                value: 0,
            },
            {
                label: '已确认',
                value: 1,
            },
        ])
        //筛选
        const params = ref<{}>({})
        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '需求编号',
                key: 'recruitmentNumber',
            },
            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIdList',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'selectSlot',
                label: '招聘岗位',
                key: 'recruitmentStationIdList',
                placeholder: '招聘岗位',
                // options: stationList,
                maxTag: '0',
                multiple: true,
            },
            {
                type: 'string',
                label: '招聘人数',
                key: 'recruitmentPeopleNumber',
            },
            {
                type: 'select',
                label: '考试形式',
                key: 'examFormatList',
                options: examFormatList,
                ruleType: 'number',
                multiple: true,
            },

            {
                type: 'string',
                label: '招聘条件',
                key: 'recruitmentTerm',
            },
            {
                type: 'daterange',
                label: '创建日期',
                key: 'createdDateList',
            },

            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                options: applyStatusList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '需求编号',
                dataIndex: 'recruitmentNumber',
                align: 'center',
                width: 150,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 200,
            },
            {
                title: '招聘岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 200,
                slots: { customRender: 'jobName' },
            },
            {
                title: '招聘人数',
                dataIndex: 'total',
                align: 'center',
                width: 150,
                slots: { customRender: 'jobNumber' },
            },

            {
                title: '考试形式',
                dataIndex: 'examFormat',
                align: 'center',
                width: 200,
                slots: { customRender: 'examinationForm' },
            },
            {
                title: '招聘条件',
                dataIndex: 'recruitmentTerm',
                align: 'center',
                width: 300,
                slots: { customRender: 'Requirement' },
            },
            {
                title: '创建日期',
                dataIndex: 'createdDate',
                type: 'date',
                width: 150,
                align: 'center',
            },

            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                width: 150,
                customRender: ({ text }) => {
                    if (text == 0) {
                        text = '待确认'
                    } else {
                        text = '已确认'
                    }
                    return text
                },
            },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]
        //查看编辑
        const showEdit = ref(false)
        const modalTitle = ref('查看')
        const viewType = ref('')
        // 当前编辑的数据
        const currentValue = ref<any>(null)
        const editRow = (viewTypeName, record?) => {
            showEdit.value = true
            modalTitle.value = viewTypeName == 'needs' ? '确认需求' : viewTypeName == 'cost' ? '费用协议' : '查看招聘需求'

            currentValue.value = record ? { ...record } : null
            viewType.value = viewTypeName
        }

        const showCreate = ref(false)
        // 新增弹框显示
        const createRow = (record) => {
            showCreate.value = true
            modalTitle.value = '新增招聘需求'
            currentValue.value = null
        }
        // 复制
        const copyRow = (record) => {
            showCreate.value = true
            modalTitle.value = '复制招聘需求'
            currentValue.value = { ...record }
        }
        // 关闭新增弹框
        const createClose = () => {
            showCreate.value = false
            tableRef.value.refresh()
            currentValue.value = null
        }
        // 新增弹框确认
        const createConfirm = () => {
            if (modalTitle.value.includes('新增招聘需求')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
            showCreate.value = false
        }

        const modalCancel = () => {
            currentValue.value = null
        }
        const modalConfirm = () => {
            tableRef.value.refresh(1)
        }
        // 批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                // console.log(row.id)
            })
        }

        // 批量确认
        const sureRow = () => {
            if (selectedRowsArr.value.length === 0) {
                return message.warning('请最少选择一条数据')
            }
            request.post('/api/hr-recruitment-needs/update', selectedRowsArr.value).then((res) => {
                let tip = ''
                let success = '您选择的数据已修改成功'
                if (res.error_status) {
                    tip = res.error_status
                    success = ',选择的其它数据已修改成功'
                }
                if (res.success?.length) {
                    tip += success
                }
                openNotification(tip)
                tableRef.value.refresh()
            })
        }

        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'recruitmentDemand_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '确认',
                    auth: 'recruitmentDemand_sure',
                    // show: true,
                    show: (record) => {
                        return record.status == '0'
                    },
                    click: (record) => editRow('needs', record),
                },
                {
                    neme: '费用协议',
                    auth: 'recruitmentDemand_sure',
                    // show: true,
                    show: (record) => {
                        return record.status == '2' || record.status == '1'
                    },
                    click: (record) => editRow('cost', record),
                },
                {
                    neme: '复制',
                    auth: 'recruitmentDemand_copy',
                    show: true,
                    click: (record) => copyRow(record),
                },
                // {
                //     neme: '删除',
                //     auth: 'recruitmentDemand_delete',
                //     show: true,
                //     click: (record) => deleteRow(record),
                // },
            ]),
        )

        // 多选
        const selectedRowsArr = ref<inObject[]>([])

        return {
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            //编辑查看
            editRow,
            // 新增
            createRow,
            //弹窗开关
            showEdit,
            //弹窗类型
            viewType,
            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            // 关闭新增
            createClose,
            createConfirm,
            // 新增弹框是否展开
            showCreate,
            sureRow, //批量确认
            // 复制
            copyRow,

            // 删除
            deleteRow,
            applyStatusList,

            examFormatList,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
.lines {
    height: 10px;
    width: 200px;
    border-top: 1px solid #f0f0f0;
    margin-top: 10px;
    position: relative;
    left: -10px;
}
.lines1 {
    height: 10px;
    width: 300px;
    border-top: 1px solid #f0f0f0;
    margin-top: 10px;
    position: relative;
    left: -10px;
}
.condition {
    cursor: default;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
