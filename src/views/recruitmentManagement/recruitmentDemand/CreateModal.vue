<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1000px">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            style="min-height: 40vh; overflow-y: auto"
        >
            <template v-for="(item, i) in myOptions" :key="i">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]">
                    <template #Cascader>
                        <ClientSelectTree
                            v-model:value="formData[item.name]"
                            v-model:itemForm="myOptions[i]"
                            style="width: 50%"
                        />
                    </template>

                    <template #inputdouble>
                        <div class="inputdouble">
                            <div class="other-info" v-for="(Item, index) in formData.hrRecruitmentStationDTOList" :key="index">
                                <FormItem
                                    style="width: 100%"
                                    :name="['hrRecruitmentStationDTOList', index]"
                                    :rules="validateTopic"
                                    class="inputdouble"
                                >
                                    <StationCascader
                                        v-model:value="Item.recruitmentStationId"
                                        :placeholder="'请选择岗位名称'"
                                        style="width: 180px"
                                        class="other-input"
                                        allowClear
                                        @change="getRecruitmentStationName(Item, index)"
                                    />
                                    <!-- <Select
                                        :options="stationList"
                                        allowClear
                                        showSearch
                                        class="other-input"
                                        placeholder="请选择招聘岗位"
                                        v-model:value="Item.recruitmentStationId"
                                        @change="getRecruitmentStationName(Item, index)"
                                        :getPopupContainer="getPopupContainer"
                                    /> -->

                                    <InputNumber
                                        class="other-input"
                                        :min="1"
                                        placeholder="请输入招聘人数"
                                        v-model:value="Item.recruitmentPeopleNumber"
                                        @change="formValidateOptional(['hrRecruitmentStationDTOList', index])"
                                    />
                                    <Select
                                        class="other-input"
                                        allowClear
                                        showSearch
                                        placeholder="请选择考试形式"
                                        v-model:value="Item.examFormat"
                                        optionFilterProp="label"
                                        :options="examFormatList"
                                        @change="formValidateOptional(['hrRecruitmentStationDTOList', index])"
                                        :getPopupContainer="getPopupContainer"
                                    />
                                    <PlusCircleOutlined class="dynamic-add-button" @click="addJob" v-if="index === 0" />
                                    <MinusCircleOutlined
                                        class="dynamic-delete-button"
                                        @click="delJob(item, index)"
                                        v-if="index != 0"
                                    />
                                </FormItem>

                                <div class="textarea">
                                    <FormItem
                                        style="width: 100%"
                                        :name="['hrRecruitmentStationDTOList', index]"
                                        :rules="validaterecRuitmentTerm"
                                        class="inputdouble"
                                    >
                                        <Textarea
                                            style="height: 170px"
                                            placeholder="请输入招聘条件..."
                                            v-model:value="Item.recruitmentTerm"
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, toRefs, watch, onMounted, nextTick } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'CreateModal',
    components: {
        PlusCircleOutlined,
        MinusCircleOutlined,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const value = ref<string[]>([])
        //请求

        const { title, item, visible } = toRefs(props)

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '客户名称',
                name: 'clientId',
                slots: 'Cascader',
                type: 'slots',
                placeholder: '请选择客户',
            },

            {
                label: '招聘岗位',
                name: 'hrRecruitmentStationDTOList',
                slots: 'inputdouble',
                type: 'slots',
                ruleType: 'array',
                default: [
                    {
                        // recruitmentStationId: '',
                        recruitmentPeopleNumber: '',
                        // examFormat: '',
                        recruitmentTerm: '',
                        recruitmentStationName: '',
                    },
                ],
                // required: false,
            },
        ])
        const clientId = ref<string[]>([])

        const examFormatList = ref<inObject[]>([]) //考试形式
        const stationList = ref<inObject[]>([]) //岗位
        onMounted(() => {
            //考试形式
            request.get('/api/com-code-tables/getCodeTableByInnerName/examFormat', {}).then((res) => {
                examFormatList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // 岗位
            request.get('/api/hr-stations/list', {}).then((res) => {
                stationList.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
        })

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
            formData.value.hrRecruitmentStationDTOList = [
                {
                    // recruitmentStationId: '',
                    recruitmentPeopleNumber: '',
                    // examFormat: '',
                    recruitmentTerm: '',
                    recruitmentStationName: '',
                },
            ]
        }
        const formValidateOptional = (nameList: string[]) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }
        // 校验招聘岗位
        const validateTopic = {
            required: true,

            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentStationDTOList[rule.field.split('.')[1]]
                if (!formDataItem?.recruitmentStationId || !formDataItem?.recruitmentPeopleNumber || !formDataItem?.examFormat) {
                    return Promise.reject('请将招聘岗位设置完整')
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validaterecRuitmentTerm = {
            required: true,
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentStationDTOList[rule.field.split('.')[1]]
                if (!formDataItem?.recruitmentTerm) {
                    return Promise.reject()
                } else {
                    return Promise.resolve()
                }
            },
        }

        const getRecruitmentStationName = (item: inObject, index) => {
            item.recruitmentStationName = stationList.value.find((el: any) => {
                return el.value == item.recruitmentStationId
            })?.label
            formValidateOptional(['hrRecruitmentStationDTOList', index])
        }

        // confirm handle
        const confirm = () => {
            // 校验是否有相同岗位名称
            const jobObj = {}
            formData.value?.hrRecruitmentStationDTOList.forEach((item) => {
                console.log('console.log(jobObj[item.recruitmentStationName])', jobObj[item.recruitmentStationName])
                if (!jobObj[item.recruitmentStationName]) {
                    jobObj[item.recruitmentStationName] = 1
                } else {
                    jobObj[item.recruitmentStationName]++
                }
            })
            const jobName = Object.values(jobObj).every((item) => item === 1)
            if (!jobName) {
                return message.error('添加岗位的名称相同，请重新选择！')
            }

            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request
                            .post('/api/hr-recruitment-needs', {
                                ...formData.value,
                            })
                            .then((res) => {
                                message.success('新增成功!')
                            })
                    } else if (title.value?.includes('复制')) {
                        const params = {}
                        params['clientId'] = formData.value.clientId
                        params['hrRecruitmentStationDTOList'] = formData.value.hrRecruitmentStationDTOList.map((item) => {
                            const obj = { ...item }
                            obj.id = null
                            return obj
                        })
                        await request.post('/api/hr-recruitment-needs', { ...params, id: null }).then((res) => {
                            console.log(res)
                        })
                        message.success('复制成功!')
                    }
                    cancel()
                    emit('confirm')
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        // 添加招聘
        const addJob = () => {
            if (!formData.value?.hrRecruitmentStationDTOList) {
                formData.value.hrRecruitmentStationDTOList = []
            }
            formData.value.hrRecruitmentStationDTOList.push({
                recruitmentStationId: formData.value.hrRecruitmentStationDTOList.recruitmentStationId,
                recruitmentPeopleNumber: formData.value.hrRecruitmentStationDTOList.recruitmentPeopleNumber,
                examFormat: formData.value.hrRecruitmentStationDTOList.examFormat,
                recruitmentTerm: formData.value.hrRecruitmentStationDTOList.recruitmentTerm,
                recruitmentStationName: '',
            })
        }
        // 删除招聘
        const delJob = (item, index) => {
            formData.value.hrRecruitmentStationDTOList.splice(index, 1)
        }
        return {
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            value,
            // clientList,
            clientId,

            // 添加岗位
            addJob,
            delJob,
            examFormatList, //考试形式
            stationList, //岗位
            validateTopic,
            validaterecRuitmentTerm,
            getRecruitmentStationName,
            formValidateOptional,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style lang="less" scoped>
.inputdouble {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .other-info {
        .other-input {
            width: 180px;
            margin-right: 15px;
        }

        .textarea {
            width: 100%;
        }
    }
}
.dynamic-add-button {
    cursor: pointer;
    position: relative;
    top: 6px;
    font-size: 20px;
    color: #999;
    transition: all 0.3s;
    color: @primary-color;
}
.dynamic-add-button:hover {
    color: @primary-color;
}
.dynamic-add-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}
.dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 6px;
    font-size: 20px;
    color: #999;
    transition: all 0.3s;

    color: @dangerous-color;
}
.dynamic-delete-button:hover {
    color: @dangerous-color;
}
.dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}
</style>
