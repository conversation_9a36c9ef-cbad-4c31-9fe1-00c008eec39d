<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1300px" :footer="null">
        <div class="seeInfoBox">
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>需求信息</span>
                <div class="item">
                    <span>客户名称：</span>
                    <span>{{ detailData.clientName }}</span>
                </div>
                <div class="examine-flex" v-for="(item, index) in detailData.hrRecruitmentStationDTOList" :key="index">
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>招聘岗位：</span>
                        <span>{{ item.recruitmentStationName }}</span>
                    </div>

                    <div class="item-flex">
                        <span>招聘人数：</span>
                        <span>{{ item.recruitmentPeopleNumber }}</span>
                    </div>
                    <div class="item-flex">
                        <span>考试形式：</span>
                        <span>{{ item.examFormatName }}</span>
                    </div>

                    <p class="linefeed"></p>
                    <div class="condition">
                        <span class="label">招聘条件：</span>
                        <span class="value">{{ item.recruitmentTerm }}</span>
                    </div>
                    <Divider v-if="index !== detailData.hrRecruitmentStationDTOList.length - 1" />
                    <p class="linefeed"></p>
                </div>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>操作信息</span>
                <div class="examine-list" v-for="(item, index) in applyOpLogsList" :key="index">
                    <div class="list-item">
                        <span style="width: 40px">{{ index + 1 }}</span>
                        <div class="item-flex">
                            <span>操作人：</span>
                            <span>{{ item.realName }}</span>
                        </div>
                        <div class="item-flex2">
                            <span>操作时间：</span>
                            <span>{{ item.createdDate }}</span>
                        </div>
                        <div class="item-flex3">
                            <div class="box1">操作信息：</div>
                            <div class="box2">
                                {{ item.message }}
                                <div v-if="item?.hrAppendixList?.length" style="display: inline-block">
                                    &nbsp; &nbsp; &nbsp; &nbsp; 附：
                                    <div
                                        v-for="AppendixItem in item.hrAppendixList"
                                        :key="AppendixItem.id"
                                        class="hrAppendixListBox"
                                    >
                                        <Tooltip placement="top">
                                            <template #title>
                                                <span>{{ AppendixItem?.originName }}</span>
                                            </template>
                                            <a class="enclosure" @click="downloadFil(AppendixItem)"
                                                ><PaperClipOutlined />{{ AppendixItem?.originName }}</a
                                            >
                                        </Tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="examine" v-if="viewType === 'needs' || viewType === 'cost'">
            <Divider type="vertical" class="divid" />
            <span>操作</span>
            <div class="box">
                <span class="span">备注</span>
                <div class="examine-area">
                    <Textarea v-model:value="checkerReason" placeholder="请输入备注..." />
                </div>
            </div>
            <div class="file">
                <span>附件</span>
                <ImportFile v-model:fileUrls="fileUrls" ref="refImportFile" />
            </div>
        </div>

        <div class="ant-modal-footer">
            <template v-if="viewType === 'needs' || viewType === 'cost'">
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="pass" v-if="viewType === 'needs'">确认</Button>
                <Button type="primary" @click="submit" v-if="viewType === 'cost'">提交</Button>
            </template>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { openNotification } from '/@/utils'
import downFile from '/@/utils/downFile'
import request from '/@/utils/request'
import { previewFile } from '/@/utils/index'
export default defineComponent({
    name: 'ShowModal',
    components: { PaperClipOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，确认需求，费用协议
                return ['', 'see', 'needs', 'cost'].indexOf(value) !== -1
            },
        },
    },

    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { item, viewType } = toRefs<any>(props)
        const refImportFile = ref()
        const fileUrls = ref<inObject[]>([])

        const detailData = ref<inObject>({})
        const applyOpLogsList = ref<object[]>([])
        const checkerReason = ref('')

        const izRefundSocialSecurity = ref<number>(1)
        watch(
            item,
            () => {
                if (item.value) {
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )
        const getDetail = (value) => {
            request.post('/api/hr-recruitment-needs/detail', { id: value }).then((res) => {
                console.log(res)
                detailData.value = res
                detailData.value.clientName = item.value?.clientName

                applyOpLogsList.value = res.applyOpLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        onMounted(() => {})
        const a = ref()

        // cancel handle
        const cancel = () => {
            // inductionApplyStore().setEmployedStaffList([])
            checkerReason.value = ''
            emit('cancel')
            emit('update:visible', false)
            fileUrls.value = []
        }

        const downloadFil = (item) => {
            // downFile('get', item.fileUrl, item.originName, {})
            previewFile(item.fileUrl)
        }
        // 确认需求
        const pass = () => {
            let appendixIds = []
            if (viewType.value == 'needs') {
                appendixIds = refImportFile.value?.getFileUrls().map((item) => {
                    return item.id
                })
            }
            request
                .put('/api/hr-recruitment-needs', {
                    id: detailData.value.id,
                    remark: checkerReason.value,
                    appendixIds: appendixIds,
                    recruitmentNumber: detailData.value.recruitmentNumber,
                    clientId: detailData.value.clientId,
                    status: 1,
                })
                .then((res) => {
                    console.log(res)
                    message.success('确认成功!')
                    cancel()
                    emit('confirm')
                })
        }
        // 费用协议提交
        const submit = () => {
            let appendixIds = []
            if (viewType.value == 'cost') {
                appendixIds = refImportFile.value?.getFileUrls().map((item) => {
                    return item.id
                })
            }
            if (appendixIds.length === 0) {
                return message.warning('请上传费用协议！')
            }
            request
                .put('/api/hr-recruitment-needs', {
                    id: detailData.value.id,
                    remark: checkerReason.value,
                    appendixIds: appendixIds,
                    recruitmentNumber: detailData.value.recruitmentNumber,
                    clientId: detailData.value.clientId,
                    status: 2,
                })
                .then((res) => {
                    console.log(res)
                    message.success('费用协议提交成功!')
                    cancel()
                    emit('confirm')
                })
        }
        return {
            refImportFile,
            applyOpLogsList,
            confirm,
            cancel,
            detailData,
            checkerReason,
            izRefundSocialSecurity,
            fileUrls,
            //下载
            downloadFil,
            // 确认
            pass,
            // 提交
            submit,
        }
    },
})
</script>
<style scoped lang="less">
.examine {
    margin: 20px 0px 0px;
    .span {
        padding-left: 10px;
    }
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 250px;
            }
            .item-flex2 {
                width: 300px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;

        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 33%;
            margin: 10px 0px;
        }
        .condition {
            widows: 100%;
            margin: 10px 0px;
            display: flex;
            .label {
                width: 70px;
            }
            .value {
                flex: 1;
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.leave {
    padding-left: 20px;
    margin: 5px 0px;
    display: flex;
}
.file {
    // padding-left: 15px;
    padding: 20px;
    display: flex;
    span {
        padding-right: 15px;
        padding-left: 15px;
    }
}
.hrAppendixListBox {
    display: inline-block;

    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
.box {
    margin: 0px 0px 0px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    span {
        width: 40px;
        padding-left: 15px;
    }
    .examine-area {
        width: 97%;
    }
}
.item {
    padding: 16px 0 16px 16px;
}
</style>
