<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="getModalWidth()">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" :rules="rules" class="form-flex">
            <template v-if="viewType == 'treasure'">
                <FormItem label="公示说明" name="hiringPublicity" style="width: 100%">
                    <WangEditor
                        :value="formData.hiringPublicity"
                        @on-change="handleChange"
                        ref="editorRef"
                        :editorConfig="editorConfig"
                    />
                </FormItem>
                <FormItem label="公示日期" name="publicityDate" :rules="validateDate">
                    <DatePicker
                        v-model:value="formData.publicityDate[0]"
                        format="YYYY-MM-DD"
                        placeholder="请选择开始日期"
                        valueFormat="YYYY-MM-DD"
                        style="margin-right: 20px"
                        @change="changeDate('publicityDate', 0)"
                        :getCalendarContainer="getPopupContainer"
                    />
                    <DatePicker
                        v-model:value="formData.publicityDate[1]"
                        format="YYYY-MM-DD"
                        placeholder="请选择结束日期"
                        valueFormat="YYYY-MM-DD"
                        @change="changeDate('publicityDate', 1)"
                        :disabled-date="disableDate"
                        :getCalendarContainer="getPopupContainer"
                    />
                </FormItem>
                <FormItem label="附件" name="appendixIdList" style="width: 100%">
                    <ImportFile v-model:fileUrls="formData.appendixIdList" ref="refImportFile" />
                </FormItem>
            </template>
            <template v-if="viewType == 'noticeMedical'">
                <FormItem label="体检时间" name="checkupDate" style="width: 100%">
                    <DatePicker
                        style="width: 362px"
                        v-model:value="formData.checkupDate"
                        format="YYYY-MM-DD"
                        placeholder="请选择体检时间"
                        valueFormat="YYYY-MM-DD"
                        :getCalendarContainer="getPopupContainer"
                        :disabled-date="disableCheckupDate"
                    />
                </FormItem>
                <FormItem label="体检地点" name="checkupPlace" style="width: 100%">
                    <Input v-model:value="formData.checkupPlace" placeholder="请输入体检地点" />
                </FormItem>
            </template>
            <template v-if="viewType == 'tryEvaluation'">
                <div class="detail-wrapper">
                    <div class="item-flex">
                        <span class="label">姓名：</span>
                        <span>{{ detailData?.name }}</span>
                    </div>
                    <div class="item-flex">
                        <span class="label">岗位：</span>
                        <span>{{ detailData?.stationName }}</span>
                    </div>
                </div>
                <FormItem label="应试环节" name="evaluationStatus" style="width: 50%">
                    <Select
                        v-model:value="formData.evaluationStatus"
                        showSearch
                        :options="linkOptions"
                        optionFilterProp="label"
                        allowClear
                        placeholder="请选择应试环节"
                        @change="onChange"
                        :getPopupContainer="getPopupContainer"
                    />
                </FormItem>
                <FormItem label="评价内容" name="evaluation" style="width: 100%">
                    <Textarea v-model:value="formData.evaluation" :rows="3" allowClear placeholder="请输入评价内容" />
                </FormItem>
            </template>
            <template v-if="viewType == 'enter' || viewType == 'update'">
                <template v-for="(ele, ind) in formData.interviewScore" :key="ind + 'interviewScore'">
                    <FormItem
                        :label="`考官${SectionToChinese(ind + 1)}`"
                        :rules="scoreValidate"
                        :name="['interviewScore', ind]"
                        style="width: 100%"
                    >
                        <InputNumber
                            v-model:value="formData.interviewScore[ind]"
                            placeholder="面试成绩"
                            style="width: 280px"
                            :min="0"
                            @change="formValidateOptional(['interviewScore', ind])"
                        />
                        <span class="icon-wrapper" v-if="ind == 0">
                            <PlusCircleOutlined class="dynamic-add-button" @click="addDomain()" />
                        </span>
                        <span class="icon-wrapper" v-else>
                            <MinusCircleOutlined class="dynamic-delete-button" @click="delDomain(ind)" />
                        </span>
                    </FormItem>
                </template>
                <div style="width: 100%" v-if="switchVisible">
                    <FormItem label="是否去极值" :labelCol="{ span: 5 }" name="izExtremum" style="width: 100%">
                        <Switch
                            v-model:checked="formData.izExtremum"
                            checked-children="是"
                            un-checked-children="否"
                            :checkedValue="1"
                            :unCheckedValue="0"
                            :getPopupContainer="getPopupContainer"
                        />
                    </FormItem>
                </div>
                <div class="totalTip">
                    <img src="~//@/assets/bulb.png" alt="" class="tipImg" />
                    <span>平均成绩</span>
                    <span class="average">{{ getAvgScore }}</span>
                </div>
            </template>
            <template
                v-if="
                    viewType == 'enter_investigate' ||
                    viewType == 'enter_health_check' ||
                    viewType == 'update_investigate' ||
                    viewType == 'update_health_check'
                "
            >
                <FormItem
                    v-if="viewType == 'enter_investigate' || viewType == 'update_investigate'"
                    label="考察结果"
                    name="examResult"
                    style="width: 100%"
                >
                    <Select
                        v-model:value="formData.examResult"
                        showSearch
                        :options="resultList"
                        optionFilterProp="label"
                        allowClear
                        placeholder="请选择考察结果"
                        :getPopupContainer="getPopupContainer"
                    />
                </FormItem>
                <FormItem
                    v-if="viewType == 'enter_health_check' || viewType == 'update_health_check'"
                    label="体检结果"
                    name="physicalExaminationResult"
                    style="width: 100%"
                >
                    <Select
                        v-model:value="formData.physicalExaminationResult"
                        showSearch
                        :options="resultList"
                        optionFilterProp="label"
                        allowClear
                        placeholder="请选择体检结果"
                        :getPopupContainer="getPopupContainer"
                    />
                </FormItem>
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn" v-if="viewType !== 'treasure'">确定</Button>
            <Button @click="onConfirm" type="primary" class="btn" :disabled="treasureDisabled" v-if="viewType == 'treasure'">
                发布
            </Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, watch, computed, toRefs, h, nextTick } from 'vue'
import { message, notification } from 'ant-design-vue'
import { SmileOutlined } from '@ant-design/icons-vue'
import { SectionToChinese, deepClone } from '/@/utils/index'
import { valuesAndRules } from '/#/component'
import { getValuesAndRules } from '/@/utils/index'
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import { Moment } from 'moment'
import moment from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import request from '/@/utils/request'
import { resultList } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'OthersModal',
    components: {
        MinusCircleOutlined,
        PlusCircleOutlined,
    },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
        detailData: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, viewType, detailData } = toRefs(props)
        const treasureDisabled = ref(false)
        const refImportFile = ref()
        const linkOptions = ref<inObject[]>([])
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '附件',
                name: 'appendixIdList',
                default: [],
                ruleType: 'array',
                required: false,
            },
            {
                label: '公示说明',
                name: 'hiringPublicity',
                required: false,
            },
            {
                label: '公示日期',
                name: 'publicityDate',
                ruleType: 'array',
                default: [],
                width: '100%',
                required: true,
            },
            {
                label: '体检时间',
                name: 'checkupDate',
                width: '100%',
                required: true,
            },
            {
                label: '体检地点',
                name: 'checkupPlace',
                width: '100%',
                required: true,
            },
            {
                label: '应试环节',
                name: 'evaluationStatus',
                type: 'select',
                ruleType: 'number',
            },
            {
                label: '评价内容',
                name: 'evaluation',
                required: false,
            },
            {
                label: '面试成绩',
                name: 'interviewScore',
                ruleType: 'array',
                default: [undefined],
            },
            {
                label: '考察成绩',
                name: 'examResult',
                ruleType: 'number',
                default: undefined,
            },
            {
                label: '体检成绩',
                name: 'physicalExaminationResult',
                ruleType: 'number',
                default: undefined,
            },
            {
                label: '是否去极值',
                name: 'izExtremum',
                ruleType: 'number',
                default: 0,
                required: false,
            },
        ])
        const getAvgScore = computed(() => {
            let scoreArr = formData.value['interviewScore']
            let [sum, length] = [0, 0]
            if (
                (formData.value.izExtremum &&
                    scoreArr.filter((el) => {
                        return el != '' && !!el
                    }).length) >= 3
            ) {
                let tempArr = deepClone(
                    scoreArr.filter((el) => {
                        return Number(el)
                    }),
                )
                tempArr.sort((a, b) => {
                    return a - b
                })
                tempArr.pop()
                tempArr.shift()
                scoreArr = tempArr
            }
            sum = scoreArr.reduce((pre, cur) => {
                return (pre += Number(cur) || 0)
            }, 0)
            length =
                scoreArr.filter((el) => {
                    return el != '' && !!el
                }).length || scoreArr.length
            return Math.round((sum / length) * 100) / 100
        })
        const switchVisible = computed(() => {
            if (viewType.value == 'enter' || viewType.value == 'update') {
                if (
                    formData.value.interviewScore.filter((el) => {
                        return el
                    }).length >= 3
                )
                    return true
                else return false
            } else return false
        })
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
            formData.value.interviewScore = ['']
            formData.value.izExtremum = 0
            formData.value.examResult = undefined
            formData.value.physicalExaminationResult = undefined
        }
        // cancel handle
        const onCancel = () => {
            if (viewType.value == 'treasure') emit('cancel', true)
            else emit('cancel')
            resetData()
        }
        const handleChange = (html) => {
            formData.value.hiringPublicity = html
        }
        const onConfirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (viewType.value == 'enter' || viewType.value == 'update') {
                        emit(
                            'confirm',
                            {
                                ...detailData.value,
                                interviewScore: formData.value.interviewScore.join(),
                                interviewScoreResult: getAvgScore.value,
                            },
                            viewType.value,
                        )
                    } else if (
                        viewType.value == 'enter_investigate' ||
                        viewType.value == 'update_investigate' ||
                        viewType.value == 'enter_health_check' ||
                        viewType.value == 'update_health_check'
                    ) {
                        emit(
                            'confirm',
                            {
                                id: detailData.value.id,
                                types:
                                    viewType.value == 'enter_investigate' || viewType.value == 'update_investigate' ? '0' : '1',
                                examName: detailData.value.recruitBrochureName,
                                professionName: detailData.value.stationName,
                                staffId: detailData.value.staffId,
                                examResult: formData.value.examResult,
                                physicalExaminationResult: formData.value.physicalExaminationResult,
                            },
                            viewType.value,
                        )
                    } else if (viewType.value == 'tryEvaluation') {
                        emit(
                            'confirm',
                            {
                                detailsId: detailData.value.id,
                                brochureId: detailData.value.brochureId,
                                staffId: detailData.value.staffId,
                                stationName: detailData.value.stationName,
                                evaluation: formData.value.evaluation,
                                evaluationStatus: formData.value.evaluationStatus,
                            },
                            viewType.value,
                        )
                    } else if (viewType.value == 'noticeMedical') {
                        emit(
                            'confirm',
                            {
                                checkupDate: formData.value.checkupDate,
                                checkupPlace: formData.value.checkupPlace,
                            },
                            viewType.value,
                        )
                    } else {
                        let myUrl = []
                        if (formData.value.appendixIdList?.length) {
                            myUrl = refImportFile.value.getFileUrls().map((item) => {
                                return item.id
                            })
                        }
                        emit(
                            'confirm',
                            {
                                publicityDate: formData.value.publicityDate[0],
                                publicityDateEnd: formData.value.publicityDate[1],
                                hiringPublicity: formData.value.hiringPublicity,
                                appendixId: myUrl.join(),
                            },
                            viewType.value,
                        )
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        const getModalWidth = () => {
            if (
                viewType.value == 'enter' ||
                viewType.value == 'noticeMedical' ||
                viewType.value == 'update' ||
                viewType.value == 'enter_investigate' ||
                viewType.value == 'enter_health_check' ||
                viewType.value == 'update_investigate' ||
                viewType.value == 'update_health_check'
            )
                return '500px'
            else if (viewType.value == 'tryEvaluation') return '800px'
            else return '1200px'
        }

        const addDomain = () => {
            formData.value['interviewScore'].push('')
        }
        const delDomain = (ind) => {
            formData.value['interviewScore'].splice(ind, 1)
        }
        watch(visible, (val, old) => {
            if (val && viewType.value == 'treasure') {
                notification.open({
                    message: '当前公示无法发布到官网，敬请期待……',
                    icon: h(SmileOutlined, { style: 'color: #108ee9' }),
                })
                request
                    .post('/api/hr-registration-evaluation-exel', detailData.value.ids)
                    .then((res) => {
                        if (!res?.success.length) {
                            treasureDisabled.value = false
                            formData.value.appendixIdList = res.sc
                        } else {
                            formData.value.appendixIdList = []
                            treasureDisabled.value = true
                            notification.error({
                                message: `无法生成入围名单,${res.error_status}`,
                            })
                        }
                    })
                    .catch((err) => {
                        console.dir(err)
                    })
            }
        })
        watch(
            detailData,
            (val, old) => {
                if (val && viewType.value == 'tryEvaluation') {
                    linkOptions.value = []
                    // 获取应试环节
                    dictionaryDataStore()
                        .setDictionaryData('interviewLink', '', 'get', true)
                        .then((data: inObject[]) => {
                            data.forEach((item) => {
                                if (item.itemValue == 1 && val.score != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 2 && val.interviewScoreResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 6 && val.addResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 7 && val.examResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                                if (item.itemValue == 8 && val.physicalExaminationResult != null)
                                    linkOptions.value.push({ label: item.itemName, value: item.itemValue })
                            })
                        })
                } else if (viewType.value == 'update') {
                    val.interviewScore = val.interviewScore.replace('，', ',')
                    formData.value.interviewScore = (val.interviewScore && val.interviewScore.split(',')) || ['']
                    formData.value.izExtremum = val.izExtremum
                } else if (viewType.value == 'update_investigate') {
                    formData.value.examResult = val.examResult
                } else if (viewType.value == 'update_health_check') {
                    formData.value.physicalExaminationResult = val.physicalExaminationResult
                }
            },
            {
                deep: true,
            },
        )

        const onChange = (val) => {
            formData.value.evaluation =
                detailData.value.hrRegistrationDetailsEvaluation.find((el) => {
                    return el.evaluationStatus == val
                })?.evaluation || ''
        }

        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }
        const changeDate = (key, index) => {
            formValidateOptional(key)
            if (index == 0) formData.value[key][1] = ''
        }
        const disableCheckupDate = (endValue: Moment) => {
            return endValue && endValue < moment().startOf('day')
        }
        const disableDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.publicityDate[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            return new Date(formData.value?.publicityDate[0]).valueOf() >= endValue.endOf('day').valueOf()
        }
        const validateDate = {
            required: true,
            trigger: ['change'],
            type: 'array',
            validator: async (rule: inObject, value: any) => {
                if (value[0] && value[1]) {
                    return Promise.resolve()
                } else {
                    return Promise.reject('请将公示日期设置完整')
                }
            },
        }

        const scoreValidate = {
            required: true,
            trigger: ['change'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let no = Number(rule.field.split('.')[1]) + 1
                if (value) {
                    return Promise.resolve()
                } else {
                    return Promise.reject(`请将考官${SectionToChinese(no)}的打分填写完整`)
                }
            },
        }

        return {
            changeDate,
            onChange,
            linkOptions,
            refImportFile,
            rules,
            formInline,
            formData,
            myOptions,
            onCancel,
            editorConfig: {
                height: 300,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            SectionToChinese,
            addDomain,
            delDomain,
            handleChange,
            onConfirm,
            getAvgScore,
            getPopupContainer: () => {
                return document.body
            },
            getModalWidth,
            formValidateOptional,
            disableDate,
            disableCheckupDate,
            validateDate,
            scoreValidate,
            resetData,
            treasureDisabled,
            resultList,
            switchVisible,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    .detail-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        .item-flex {
            margin: 5px 0px;
            display: flex;
        }
        .label {
            text-align: right;
            width: 80px;
            color: rgba(153, 153, 153, 1);
        }
    }
    .totalTip {
        margin-top: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        .tipImg {
            width: 24px;
            height: 24px;
        }
        .average {
            margin: 0 15px;
            color: @dangerous-color;
        }
    }
    .icon-wrapper {
        width: 20px;
        margin-left: 10px;
        display: inline-block;
    }
    .dynamic-add-button {
        cursor: pointer;
        position: relative;
        font-size: 17px;
        color: #999;
        transition: all 0.3s;
        color: @primary-color;
    }
    .dynamic-add-button:hover {
        color: @primary-color;
    }
    .dynamic-add-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
    .dynamic-delete-button {
        cursor: pointer;
        position: relative;
        font-size: 17px;
        color: #999;
        transition: all 0.3s;

        color: @dangerous-color;
    }
    .dynamic-delete-button:hover {
        color: @dangerous-color;
    }
    .dynamic-delete-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
}
</style>
