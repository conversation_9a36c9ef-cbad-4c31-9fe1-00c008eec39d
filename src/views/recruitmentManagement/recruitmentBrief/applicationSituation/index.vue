<template>
    <div class="wrapper">
        <SearchBar v-model="searchParams" :options="options" @change="searchData" />
        <div class="btns">
            <Button type="primary" v-auth="'recruitmentBrief-look_import'" @click="showModal(null, 'import')">导入</Button>
            <Button type="primary" v-auth="'recruitmentBrief-look_export'" @click="showExportModal">{{ exportText }}</Button>
            <Button type="primary" v-auth="'recruitmentBrief-look_pass'" @click="batchAudit(0)"> 批量通过 </Button>
            <Button type="primary" v-auth="'recruitmentBrief-look_reject'" class="rejectBtn" @click="showRejectModal">
                批量拒绝
            </Button>
            <Button type="primary" v-auth="'recruitmentBrief_batch_inspectionNotice'" @click="inspectionNotice(null, true)">
                批量考察通知
            </Button>
            <Button
                type="primary"
                v-auth="'recruitmentBrief_batch_noticeMedical'"
                @click="showModal(null, 'noticeMedical', true)"
            >
                批量体检通知
            </Button>
            <Button type="primary" v-auth="'recruitmentBrief_treasure'" @click="showModal(null, 'treasure')">拟聘用公示</Button>
            <Button type="primary" v-auth="'recruitmentBrief_inquiry_notice'" @click="inquiryNotice"> 转到入职 </Button>
            <Button type="primary" class="successBtn" v-auth="'recruitmentBrief_batch_transfer'" @click="batchInquiry">
                批量入职
            </Button>
        </div>
        <BasicTable
            class="basic_table"
            ref="tableRef"
            api="/api/hr-registration-detailses/page"
            deleteApi="/api/hr-registration-detailses/deletes"
            :exportUrl="'/api/hr-registration-detailses/export'"
            :scroll="{ x: 1600 }"
            :params="params"
            :columns="columns"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
            @getData2="(data) => (tableData = data)"
        >
            <template #operation="{ record }">
                <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            </template>
            <template #score="{ record }">
                <Tooltip placement="top">
                    <template #title>
                        <span v-if="getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 1)">
                            {{ getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 1) }}
                        </span>
                    </template>
                    <div :style="{ color: getTextColor(record, 'score') }" class="arrow">
                        <b>{{ record.score ?? '/' }}</b>
                    </div>
                </Tooltip>
            </template>
            <template #interviewScoreResult="{ record }">
                <Tooltip placement="top">
                    <template #title>
                        <span v-if="getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 2)">
                            {{ getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 2) }}
                        </span>
                    </template>
                    <div :style="{ color: getTextColor(record, 'interviewScoreResult') }" class="arrow">
                        <b>{{ record.interviewScoreResult ?? '/' }}</b>
                    </div>
                </Tooltip>
            </template>
            <template #addResult="{ record }">
                <Tooltip placement="top">
                    <template #title>
                        <span v-if="getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 6)">
                            {{ getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 6) }}
                        </span>
                    </template>
                    <b class="arrow">{{ record.addResult ?? '/' }}</b>
                </Tooltip>
            </template>
            <template #examResult="{ record }">
                <Tooltip placement="top">
                    <template #title>
                        <span v-if="getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 7)">
                            {{ getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 7) }}
                        </span>
                    </template>
                    <b class="arrow">{{ getResultLabel(record, 'examResult') }}</b>
                </Tooltip>
            </template>
            <template #physicalExaminationResult="{ record }">
                <Tooltip placement="top">
                    <template #title>
                        <span v-if="getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 8)">
                            {{ getCurrentEvaluation(record.hrRegistrationDetailsEvaluation, 8) }}
                        </span>
                    </template>
                    <b class="arrow">{{ getResultLabel(record, 'physicalExaminationResult') }}</b>
                </Tooltip>
            </template>
            <template #finalResult="{ record }">
                <div :style="{ color: getTextColor(record, 'finalResult') }">
                    <b>{{ record.finalResult ?? '/' }}</b>
                </div>
            </template>
        </BasicTable>
        <SeeModal :visible="seeModalVisible" :detailData="currentValue" :title="modalTitle" @cancel="cancelModal" />
        <AuditModal
            ref="refAuditModal"
            v-model:visible="auditModalVisible"
            :currentValue="currentValue"
            :title="modalTitle"
            isAudit
        >
            <template #footer v-if="modalTitle == '审核简历'">
                <div class="foot-flex">
                    <span class="label">拒绝原因:</span>
                    <Textarea v-model:value="checkerReason" :rows="3" allowClear placeholder="拒绝原因" />
                </div>
                <div class="foot-btns">
                    <Button @click="onApprove(0)" class="delBtn">拒绝</Button>
                    <Button @click="onApprove(1)" type="primary" class="btn">通过</Button>
                </div>
            </template>
        </AuditModal>

        <BasicEditModalSlot :visible="rejectVisible" @cancel="cancelModal" title="批量拒绝" width="600px">
            <div class="foot-flex2">
                <span class="label">拒绝原因:</span>
                <Textarea v-model:value="checkerReason" :rows="5" allowClear placeholder="拒绝原因" />
            </div>
            <template #footer>
                <Button @click="batchAudit(1)" type="primary" class="btn">确定</Button>
            </template>
        </BasicEditModalSlot>

        <OtherModal
            ref="otherModal"
            :visible="otherModalVisible"
            :viewType="detailType"
            :detailData="currentValue"
            :title="modalTitle"
            @cancel="cancelModal"
            @confirm="othersConfirm"
        />
        <SelectCommon
            :title="modalTitle"
            :visible="selectVisible"
            :btnLoading="selectBtnLoading"
            :viewType="selectType"
            v-model:commonList="commonList"
            @cancel="cancelModal"
        />
        <ImportModal
            v-model:visible="importVisible"
            :temUrl="importTemUrl"
            :importUrl="importUrl"
            :importUrlParam="importUrlParam"
            :temParams="temParams"
            :downTempMethod="downTempMethod"
            @getResData="searchData"
            @importComplete="importComplete"
        />
    </div>
</template>
<script lang="ts">
import { message, notification } from 'ant-design-vue'
import { ref, defineComponent, onMounted, computed, watch } from 'vue'
import request from '/@/utils/request'
import { SearchBarOption } from '/#/component'
import { sexList, resultList } from '/@/utils/dictionaries'
import SeeModal from './seeModal.vue'
import OtherModal from './othersModal.vue'
import SelectCommon from '../component/SelectCommon.vue'
import AuditModal from '/@/views/recruitmentManagement/registrationTemplate/applyTemplate/seeModal.vue'

import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { getDynamicText, getHaveAuthorityOperation, isEmpty } from '/@/utils'
import moment from 'moment'
import downFile from '/@/utils/downFile'
import { useRouter } from 'vue-router'
import inductionApplyStore from '/@/store/modules/inductionApply'
export default defineComponent({
    name: 'ApplicationSituation',
    components: {
        SeeModal,
        OtherModal,
        SelectCommon,
        AuditModal,
    },
    setup() {
        const router = useRouter()
        const otherModal = ref()
        const tipRef = ref()
        const checkerReason = ref('')
        const selectBtnLoading = ref(false)
        // 筛选
        let statusTypeList = ref<LabelValueOptions>([]) // 状态
        let educationStateList = ref<LabelValueOptions>([]) // 最高学历
        let marriageStateList = ref<LabelValueOptions>([]) // 婚姻状态
        let stationNameLists = ref<LabelValueOptions>([]) // 岗位
        const currentItem = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        const examModeList = computed(() => {
            return recruitmentBriefStore().getExamModeList
        })
        const modalViewType = computed(() => {
            return recruitmentBriefStore().getModalViewType
        })
        const exportList = computed(() => {
            return recruitmentBriefStore().getExportList
        })

        let options: SearchBarOption[] = [
            {
                label: '抽签号',
                key: 'number',
                type: 'string',
            },
            {
                label: '应聘岗位',
                key: 'stationNameLists',
                type: 'select',
                multiple: true,
                options: stationNameLists,
            },
            {
                type: 'select',
                label: '考试形式',
                key: 'examFormatList',
                options: examModeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexList,
                multiple: true,
            },
            {
                type: 'select',
                label: '最高学历',
                key: 'highestEducationList',
                multiple: true,
                options: educationStateList,
            },
            {
                type: 'select',
                label: '婚姻状态',
                key: 'maritalStatusList',
                multiple: true,
                options: marriageStateList,
            },
            {
                type: 'daterange',
                label: '出生日期',
                key: 'birthdayListQuery',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'phone',
            },
            {
                type: 'numberrange',
                label: '笔试成绩',
                key: 'scoreList',
            },
            {
                type: 'numberrange',
                label: '面试平均成绩',
                key: 'interviewScoreResultList',
            },
            {
                type: 'numberrange',
                label: '最终成绩',
                key: 'finalResultList',
            },
            {
                type: 'numberrange',
                label: '加试成绩',
                key: 'addResultList',
            },
            {
                type: 'select',
                label: '考察结果',
                key: 'examResultList',
                multiple: true,
                options: resultList,
            },
            {
                type: 'select',
                label: '体检结果',
                key: 'physicalExaminationResultList',
                multiple: true,
                options: resultList,
            },
            {
                type: 'string',
                label: '备注',
                key: 'remark',
            },
            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                multiple: true,
                options: statusTypeList,
            },
        ]

        // 表格数据
        const columns = ref([
            {
                title: '抽签号',
                dataIndex: 'number',
                align: 'center',
                fixed: 'left',
                width: 90,
            },
            {
                title: '应聘岗位',
                dataIndex: 'stationName',
                align: 'center',
                fixed: 'left',
                width: 180,
            },
            {
                title: '考试形式',
                dataIndex: 'examFormat',
                align: 'center',
                fixed: 'left',
                width: 160,
                customRender: ({ record }) => {
                    return examModeList.value.find((el) => {
                        return record.examFormat == el.value
                    })?.label
                },
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                fixed: 'left',
                width: 110,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '报名时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 160,
                customRender: ({ record }) => {
                    return moment(record.createdDate).format('YYYY-MM-DD HH:mm')
                },
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                width: 80,
                customRender: ({ record }) => {
                    return sexList.find((el) => {
                        return record.sex == el.value
                    })?.label
                },
            },
            {
                title: '最高学历',
                dataIndex: 'highestEducation',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return educationStateList.value.find((el) => {
                        return record.highestEducation == el.value
                    })?.label
                },
            },
            {
                title: '婚姻状态',
                dataIndex: 'maritalStatus',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return marriageStateList.value.find((el) => {
                        return record.maritalStatus == el.value
                    })?.label
                },
            },
            {
                title: '出生日期',
                dataIndex: 'birthday',
                align: 'center',
                width: 110,
            },
            {
                title: '联系方式',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '笔试成绩',
                dataIndex: 'score',
                align: 'center',
                width: 100,
                slots: { customRender: 'score' },
            },
            {
                title: '面试考官打分',
                dataIndex: 'interviewScore',
                align: 'center',
                width: 90,
                customRender: ({ record }) => {
                    return record.interviewScore?.replace('，', ',') || '/'
                },
            },
            {
                title: '面试平均成绩',
                dataIndex: 'interviewScoreResult',
                align: 'center',
                width: 90,
                slots: { customRender: 'interviewScoreResult' },
            },
            {
                title: '最终成绩',
                dataIndex: 'finalResult',
                align: 'center',
                width: 100,
                slots: { customRender: 'finalResult' },
            },
            {
                title: '加试成绩',
                dataIndex: 'addResult',
                align: 'center',
                width: 100,
                slots: { customRender: 'addResult' },
            },
            {
                title: '考察结果',
                dataIndex: 'examResult',
                align: 'center',
                width: 100,
                slots: { customRender: 'examResult' },
            },
            {
                title: '体检结果',
                dataIndex: 'physicalExaminationResult',
                align: 'center',
                width: 100,
                slots: { customRender: 'physicalExaminationResult' },
            },
            {
                title: '备注',
                dataIndex: 'remark',
                align: 'center',
                ellipsis: true,
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                fixed: 'right',
                width: 160,
                customRender: ({ record }) => {
                    return statusTypeList.value.find((el) => {
                        return record.status == el.value
                    })?.label
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 280,
                fixed: 'right',
            },
        ])

        onMounted(() => {
            // 状态
            dictionaryDataStore()
                .setDictionaryData('applicationSituationState', '')
                .then((data: inObject[]) => {
                    statusTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 最高学历
            dictionaryDataStore()
                .setDictionaryData('educationStates', '')
                .then((data: inObject[]) => {
                    educationStateList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 婚姻状态
            dictionaryDataStore()
                .setDictionaryData('marriageStates', '')
                .then((data: inObject[]) => {
                    marriageStateList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 岗位
            request
                .get(`/api/hr-registration-detailsesStation/${currentItem.value?.id}`)
                .then((res) => {
                    stationNameLists.value = res.map((item) => {
                        return { label: item.stationName, value: item.stationName }
                    })
                })
                .catch((err) => {
                    console.log(err)
                })
        })

        // 表格dom
        const tableRef = ref()
        // 筛选
        const searchParams = ref({})
        const params = computed(() => {
            return {
                ...searchParams.value,
                id: currentItem.value?.id,
                stationNameList: currentItem.value?.hrRecruitmentStation.map((el) => {
                    return el.recruitmentStationName
                }),
            }
        })

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const getTextColor = (record, type) => {
            if (type == 'score') {
                if (!record.writtenPassLine || !record.score) return ''
                else {
                    if (record[type] >= record.writtenPassLine) return '#3eb889'
                    else return '#eb3333'
                }
            } else if (type == 'interviewScoreResult') {
                if (!record.interviewPassLine || !record.interviewScoreResult) return ''
                else {
                    if (record[type] >= record.interviewPassLine) return '#3eb889'
                    else return '#eb3333'
                }
            } else {
                if (!record.interviewPassLine || !record.interviewPassLine || !record.finalResult) return ''
                else {
                    if (record['score'] >= record.writtenPassLine && record['interviewScoreResult'] >= record.interviewPassLine)
                        return '#3eb889'
                    else return '#eb3333'
                }
            }
        }

        const getCurrentEvaluation = (record, state) => {
            if (record)
                return (
                    record.find((el) => {
                        return el.evaluationStatus == state
                    })?.evaluation || ''
                )
            else return ''
        }

        const modalTitle = ref('')
        const seeModalVisible = ref(false)
        const otherModalVisible = ref(false)
        const selectVisible = ref(false)
        const auditModalVisible = ref(false)
        const rejectVisible = ref(false)
        const detailType = ref('')
        const selectType = ref('')
        const commonList = ref<inObject[]>([])
        // 当前编辑的数据
        const currentValue = ref<inObject>({})

        watch(modalViewType, (val, old) => {
            if (val == 'import_ballotNo' || val == 'import_writtenScore') importVisible.value = true
            if (val == 'import_ballotNo') {
                temParams.value = { brochureId: currentItem.value?.id }
                importUrlParam.value = {}
                importTemUrl.value = '/api/hr-registration-detailses/template'
                importUrl.value = `/api/hr-registration-detailses/import-number?brochureId=${currentItem.value?.id}`
                downTempMethod.value = 'post'
            } else if (val == 'import_writtenScore') {
                temParams.value = {}
                importTemUrl.value = '/api/hr-exam-results/profile-template'
                importUrl.value = '/api/hr-exam-results/profile-import'
                downTempMethod.value = 'post'
                importUrlParam.value = { examName: currentItem.value?.recruitBrochureName }
            } else {
                temParams.value = {}
                importUrlParam.value = {}
                importTemUrl.value = ''
                importUrl.value = ''
                downTempMethod.value = 'get'
            }
        })
        watch(exportList, (val, old) => {
            if (val.length) {
                exportRecord(
                    val.map((el: any) => {
                        return commonList.value.find((ele) => {
                            return ele.type == el
                        })?.id
                    }),
                )
            }
        })

        const getResultLabel = (record, type) => {
            if (record[type] || record[type] == 0)
                return resultList.find((el) => {
                    return record[type] == el.value
                })?.label
            else return '/'
        }

        // 显示弹窗
        const showModal = async (record, type, isMultiple = false) => {
            detailType.value = type
            if (isMultiple) {
                if (type == 'noticeMedical') {
                    if (selectedRowsArr.value.length <= 0) {
                        message.error('请先至少选择一条数据!')
                        return
                    }
                    currentValue.value = {
                        isMultiple: true,
                    }
                    seeModalVisible.value = false
                    otherModalVisible.value = true
                    selectVisible.value = false
                    modalTitle.value = '体检通知'
                } else {
                    console.log(type)
                }
            } else {
                if (type == 'audit' || type == 'resumeLook') {
                    await getDetails(record.id)
                        .then((res: inObject) => {
                            currentValue.value = { ...res, examFormat: record.examFormat, isNeedPay: record.isNeedPay }
                            auditModalVisible.value = true
                            seeModalVisible.value = false
                            otherModalVisible.value = false
                            selectVisible.value = false
                            if (type == 'audit') modalTitle.value = '审核简历'
                            else modalTitle.value = '查看简历'
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                } else if (type == 'treasure') {
                    if (selectedRowsArr.value.length <= 0) {
                        message.error('请先至少选择一条数据!')
                        return
                    } else {
                        currentValue.value = {
                            ids: selectedRowsArr.value.map((el: any) => {
                                return el.id
                            }),
                        }
                        seeModalVisible.value = false
                        otherModalVisible.value = true
                        selectVisible.value = false
                        modalTitle.value = '拟聘用公示'
                    }
                } else if (type == 'import') {
                    commonList.value = [
                        {
                            name: '导入抽签号',
                            url: getImageUrl('ballot_no'),
                            type: 'import_ballotNo',
                        },
                        {
                            name: '导入成绩',
                            url: getImageUrl('written_score'),
                            type: 'import_writtenScore',
                        },
                    ]
                    selectType.value = 'import'
                    seeModalVisible.value = false
                    otherModalVisible.value = false
                    selectVisible.value = true
                    modalTitle.value = '导入'
                } else {
                    if (type == 'look') {
                        seeModalVisible.value = true
                        otherModalVisible.value = false
                        selectVisible.value = false
                        modalTitle.value = '查看个人报名信息'
                    } else {
                        otherModalVisible.value = true
                        seeModalVisible.value = false
                        selectVisible.value = false
                        switch (type) {
                            case 'enter':
                                if (record.status == 7) {
                                    modalTitle.value = '录入面试成绩'
                                } else if (record.status == 10) {
                                    modalTitle.value = '录入考察成绩'
                                    detailType.value = 'enter_investigate'
                                } else {
                                    modalTitle.value = '录入体检成绩'
                                    detailType.value = 'enter_health_check'
                                }
                                break
                            case 'update':
                                if (canUpdate(record)) {
                                    modalTitle.value = '修改面试成绩'
                                } else if (canUpdateInvestigate(record)) {
                                    modalTitle.value = '修改考察成绩'
                                    detailType.value = 'update_investigate'
                                } else {
                                    modalTitle.value = '修改体检成绩'
                                    detailType.value = 'update_health_check'
                                }
                                break
                            case 'tryEvaluation':
                                modalTitle.value = '应试评价'
                                break
                            case 'noticeMedical':
                                modalTitle.value = '体检通知'
                                break
                        }
                    }
                    currentValue.value = {
                        ...record,
                        isMultiple: isMultiple,
                        sexLabel: sexList.find((el) => {
                            return el.value == record.sex
                        })?.label,
                        highestEducationLabel: educationStateList.value.find((el) => {
                            return el.value == record.highestEducation
                        })?.label,
                        maritalStatusLabel: marriageStateList.value.find((el) => {
                            return el.value == record.maritalStatus
                        })?.label,
                    }
                }
            }
        }

        const getImageUrl = (name) => {
            return new URL(`../../../../assets/${name}.svg`, import.meta.url).href
        }

        const importComplete = () => {
            recruitmentBriefStore().setModalViewType('')
        }

        const cancelModal = (flag = false) => {
            rejectVisible.value = false
            auditModalVisible.value = false
            seeModalVisible.value = false
            otherModalVisible.value = false
            selectVisible.value = false
            selectBtnLoading.value = false
            modalTitle.value = ''
            checkerReason.value = ''
            flag && tableRef.value.checkboxReset()
        }

        const othersConfirm = (info, type) => {
            if (type == 'enter' || type == 'update') {
                request
                    .put('/api/hr-registration-detailses-update', info)
                    .then((res) => {
                        message.success(`面试成绩${type == 'enter' ? '录入' : '修改'}成功`)
                        otherModal.value.resetData()
                        cancelModal()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else if (
                type == 'enter_investigate' ||
                type == 'update_investigate' ||
                type == 'enter_health_check' ||
                type == 'update_health_check'
            ) {
                request
                    .post('/api/hr-registration-ReviewCheckup', info)
                    .then((res) => {
                        message.success(
                            `${type.split('_')[1] == 'investigate' ? '考察' : '体检'}成绩
                            ${type.split('_')[0] == 'enter' ? '录入' : '修改'}成功`,
                        )
                        otherModal.value.resetData()
                        cancelModal()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else if (type == 'tryEvaluation') {
                request
                    .put('/api/hr-registration-evaluation', info)
                    .then((res) => {
                        message.success('评价成功')
                        otherModal.value.resetData()
                        cancelModal()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else if (type == 'noticeMedical') {
                request
                    .post(
                        '/api/hr-registration-detailsesCheckup',
                        currentValue.value.isMultiple
                            ? selectedRowsArr.value.map((el: inObject) => {
                                  return {
                                      ...info,
                                      id: el.id,
                                      brochureId: el.brochureId,
                                      stationId: el.stationId,
                                      staffId: el.staffId,
                                      phone: el.phone,
                                  }
                              })
                            : [
                                  {
                                      id: currentValue.value.id,
                                      brochureId: currentValue.value.brochureId,
                                      stationId: currentValue.value.stationId,
                                      staffId: currentValue.value.staffId,
                                      phone: currentValue.value.phone,
                                      ...info,
                                  },
                              ],
                    )
                    .then((res) => {
                        if (!res || !res?.success.length) {
                            message.success('体检通知发送成功')
                        } else {
                            if (selectedRowsArr.value.length > res.success.length) {
                                notification.warning({
                                    message: `体检通知发送成功,但${res.error_status}`,
                                })
                            } else {
                                notification.error({
                                    message: `体检通知发送失败,${res.error_status}`,
                                })
                            }
                        }
                        otherModal.value.resetData()
                        currentValue.value.isMultiple && tableRef.value.checkboxReset()
                        cancelModal(true)

                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        currentValue.value.isMultiple && tableRef.value.checkboxReset()
                        console.log(err)
                    })
            } else {
                request
                    .put('/api/hr-registration-detailses', {
                        idList: selectedRowsArr.value.map((el: inObject) => {
                            return el.id
                        }),
                        ...info,
                    })
                    .then((res) => {
                        if (!res || !res?.success.length) {
                            message.success('拟聘用公示发布成功')
                        } else {
                            if (selectedRowsArr.value.length > res.success.length) {
                                notification.warning({
                                    message: `拟聘用公示发布成功,但${res.error_status}`,
                                })
                            } else {
                                notification.error({
                                    message: `拟聘用公示发布失败,${res.error_status}`,
                                })
                            }
                        }
                        otherModal.value.resetData()
                        cancelModal(true)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }
        const refAuditModal = ref()

        const showRejectModal = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先至少选择一条数据!')
                return
            } else {
                rejectVisible.value = true
            }
        }

        const batchAudit = (opt) => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先至少选择一条数据!')
                return
            } else {
                if (opt && isEmpty(checkerReason.value)) {
                    message.warn('请输入拒绝原因')
                } else {
                    request
                        .post('/api/hr-registration-Review', {
                            ids: selectedRowsArr.value.map((el: inObject) => el.id),
                            types: opt + '',
                            denialReason: checkerReason.value,
                        })
                        .then((res: inObject) => {
                            if (!res || !res?.success.length) {
                                message.success(!opt ? '审核已通过' : '审核已拒绝')
                            } else {
                                if (selectedRowsArr.value.length > res.success.length) {
                                    notification.warning({
                                        message: `简历审核${!opt ? '已通过' : '已拒绝'},但${res.error_status}`,
                                    })
                                } else {
                                    notification.error({
                                        message: `简历审核失败,${res.error_status}`,
                                    })
                                }
                            }
                            cancelModal()
                            tableRef.value.checkboxReset()
                            tableRef.value.refresh()
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                }
            }
        }
        const onApprove = (opt) => {
            if (!opt && isEmpty(checkerReason.value)) {
                message.warn('请输入拒绝原因')
            } else {
                request
                    .put('/api/hr-registration-detailsesJson', {
                        id: currentValue.value.id,
                        brochureId: currentItem.value?.id,
                        stationId: currentValue.value?.stationId,
                        staffId: currentValue.value.staffId,
                        denialReason: checkerReason.value,
                        status: opt,
                        examFormat: currentValue.value.examFormat,
                        isNeedPay: currentValue.value.isNeedPay,
                    })
                    .then((res: inObject) => {
                        message.success(opt ? '已通过审核' : '已拒绝')
                        cancelModal()
                        refAuditModal.value.resetFormData()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        // 获取详细信息
        const getDetails = (id) => {
            return new Promise((resolve, reject) => {
                request
                    .get(`/api/hr-registration-detailses/${id}`)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }

        // 多选
        const selectedRowsArr = ref<any[]>([])
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('导出', searchParams.value, selectedRowsArr.value)
        })
        const showExportModal = () => {
            commonList.value = [
                {
                    id: 1,
                    name: '导出报名记录',
                    url: getImageUrl('registration_record'),
                    checked: false,
                    type: 'export_registrationRecord',
                },
                {
                    id: 2,
                    name: '导出桌贴',
                    url: getImageUrl('table_sticker'),
                    checked: false,
                    type: 'export_tableSticker',
                },
                {
                    id: 3,
                    name: '导出报名表',
                    url: getImageUrl('application_form'),
                    checked: false,
                    type: 'export_applicationForm',
                },
            ]
            selectType.value = 'export'
            seeModalVisible.value = false
            otherModalVisible.value = false
            selectVisible.value = true
            modalTitle.value = '导出'
        }
        // 导出报名记录/桌贴/报名表
        const exportRecord = async (typeList) => {
            selectBtnLoading.value = true
            let ids: any[] = []
            let body = {}
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (exportText.value.indexOf('选中') != -1) {
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            body['typeList'] = typeList
            if (typeList.find((el) => el == 2)) {
                body['deskStickerHtml'] =
                    '<div style="display: inline-block; font-size: 24px; width: 33%; border: 1px solid #333; box-sizing: border-box; padding: 10px"><div>报考岗位：${postRegistration}</div><div>姓名：${name}</div><div>准考证号：${admissionNumber}</div></div>'
            }
            await downFile('post', '/api/hr-registration-detailses/export', '', {
                ...body,
                recruitmentBrochureId: currentItem.value?.id,
            })
            cancelModal(true)
        }
        // 入职通知
        const inquiryNotice = () => {
            if (selectedRowsArr.value.length > 0) {
                request
                    .post(
                        '/api/hr-registration-detailses-onboarding',
                        selectedRowsArr.value.map((el: inObject) => {
                            return el.id
                        }),
                    )
                    .then((res) => {
                        if (!res || !res?.success.length) {
                            message.success('入职通知发送成功')
                        } else {
                            if (selectedRowsArr.value.length > res.success.length) {
                                notification.warning({
                                    message: `入职通知发送成功,但${res.error_status}`,
                                })
                            } else {
                                notification.error({
                                    message: `入职通知发送失败,${res.error_status}`,
                                })
                            }
                        }
                        tableRef.value.checkboxReset()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                message.error('请先至少选择一条数据!')
                return
            }
        }
        // 考察通知
        const inspectionNotice = (record, isMultiple) => {
            let params = {}
            if (isMultiple) {
                if (selectedRowsArr.value.length <= 0) {
                    message.error('请先至少选择一条数据!')
                    return
                }
                params = selectedRowsArr.value.map((el: inObject) => {
                    return {
                        id: el.id,
                        brochureId: el.brochureId,
                        stationId: el.stationId,
                        staffId: el.staffId,
                        examFormat: el.examFormat,
                    }
                })
            } else {
                params = {
                    id: record.id,
                    brochureId: record.brochureId,
                    stationId: record.stationId,
                    staffId: record.staffId,
                    examFormat: record.examFormat,
                }
            }
            request
                .post('/api/hr-registration-detailses/notice-investigation', isMultiple ? params : [params])
                .then((res) => {
                    if (!res || !res?.success.length) {
                        message.success('考察通知发送成功')
                    } else {
                        if (selectedRowsArr.value.length > res.success.length) {
                            notification.warning({
                                message: `考察通知发送成功,但${res.error_status}`,
                            })
                        } else {
                            notification.error({
                                message: `考察通知发送失败,${res.error_status}`,
                            })
                        }
                    }
                    isMultiple && tableRef.value.checkboxReset()
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    isMultiple && tableRef.value.checkboxReset()
                    console.log(err)
                })
        }
        // 批量入职
        const batchInquiry = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请先至少选择一条数据!')
                return
            } else {
                if (
                    selectedRowsArr.value.every((el: inObject) => {
                        return el.status == 19
                    })
                ) {
                    let hrApplyDepartureStaffDTOS: any[] = []
                    let sameArr: inObject = []
                    let systemStationId = selectedRowsArr.value[0]?.systemStationId
                    selectedRowsArr.value.forEach((item) => {
                        if (item.systemStationId != systemStationId) {
                            systemStationId = ''
                        }
                        let sexLabel = sexList.find((el) => {
                            return item.sex == el.value
                        })?.label
                        let tempObj = hrApplyDepartureStaffDTOS.find((el) => {
                            return el.certificateNum == item.certificateNum
                        })
                        if (!tempObj) {
                            hrApplyDepartureStaffDTOS.push({
                                name: item.name || '',
                                certificateNum: item.certificateNum || '',
                                sex: item.sex || '',
                                sexLabel: sexLabel || '',
                                phone: item.phone || '',
                                stationId: item.systemStationId || '',
                                professionName: item.stationName || '',
                                contractStartDate: moment(new Date()).format('YYYY-MM-DD'),
                            })
                        } else {
                            let tempObj2 = sameArr.find((el) => {
                                return el.certificateNum == item.certificateNum
                            })
                            if (!tempObj2) sameArr.push(item)
                        }
                    })
                    if (sameArr.length) {
                        notification.warning({
                            message: `${sameArr.map((el: inObject) => el.name).join()}重复选择`,
                        })
                        tableRef.value.checkboxReset()
                    } else {
                        inductionApplyStore().setEmployedStaffList(hrApplyDepartureStaffDTOS)
                        router.push({
                            name: 'inductionApply',
                            query: { clientId: selectedRowsArr.value[0]?.clientId, stationId: systemStationId, openAdd: 1 },
                        })
                    }
                } else {
                    notification.warning({
                        message: `${selectedRowsArr.value
                            .filter((el: inObject) => {
                                return el.status != 19
                            })
                            .map((el: inObject) => el.name)
                            .join()}未到<待办理入职>状态,无法转到入职,请重新选择`,
                    })
                    tableRef.value.checkboxReset()
                }
            }
        }

        const judgeAuditDate = (start, end) => {
            if (start && end) {
                if (
                    moment(start).startOf('day').valueOf() <= moment().endOf('day').valueOf() &&
                    moment().startOf('day').valueOf() <= moment(end).endOf('day').valueOf()
                )
                    return true
                else return false
            } else return false
        }
        const canKeyIn = (record) => {
            let temp = null
            if (record.status == 7) {
                temp = record.hrRecruitmentBulletinList.find((el) => {
                    return el.noticeType == 3 && (el.achievementType == 2 || el.achievementType == 3)
                })
                if (temp) {
                    return false
                } else return true
            } else if (record.status == 10) {
                temp = record.hrRecruitmentBulletinList.find((el) => {
                    return el.noticeType == 3 && el.achievementType == 4
                })
                if (temp) {
                    return false
                } else return true
            } else if (record.status == 13) {
                temp = record.hrRecruitmentBulletinList.find((el) => {
                    return el.noticeType == 3 && el.achievementType == 5
                })
                if (temp) {
                    return false
                } else return true
            } else return false
        }

        const canUpdate = (record) => {
            if (record.examFormat == 3) {
                let temp = record.hrRecruitmentBulletinList.find((el) => {
                    return el.noticeType == 3 && el.achievementType == 2
                })
                if (temp) {
                    return false
                } else {
                    if (record.status == 8 || record.status == 17 || record.status == 23) return true
                    else return false
                }
            } else if (record.examFormat == 2 || record.examFormat == 4) {
                let temp = record.hrRecruitmentBulletinList.find((el) => {
                    return (el.noticeType == 3 && el.achievementType == 3) || (el.noticeType == 3 && el.achievementType == 2)
                })
                if (temp) {
                    return false
                } else {
                    if (record.status == 8 || record.status == 9 || record.status == 20 || record.status == 21) return true
                    else return false
                }
            } else return false
        }
        const canUpdateInvestigate = (record) => {
            let temp = record.hrRecruitmentBulletinList.find((el) => {
                return el.noticeType == 3 && el.achievementType == 4
            })
            if (temp) {
                return false
            } else {
                if (record.status == 11 || record.status == 12) return true
                else return false
            }
        }
        const canUpdateHealthCheck = (record) => {
            let temp = record.hrRecruitmentBulletinList.find((el) => {
                return el.noticeType == 3 && el.achievementType == 5
            })
            if (temp) {
                return false
            } else {
                if (record.status == 14 || record.status == 15) return true
                else return false
            }
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看进度',
                    auth: 'recruitmentBrief-look_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '查看简历',
                    auth: 'recruitmentBrief-look_resumeLook',
                    show: (record) => {
                        return record.status != 0
                    },
                    click: (record) => showModal(record, 'resumeLook'),
                },
                {
                    neme: '审核',
                    auth: 'recruitmentBrief-look_audit',
                    show: (record) => {
                        return record.status == 0
                    },
                    click: (record) => showModal(record, 'audit'),
                },
                {
                    neme: '录入成绩',
                    auth: 'recruitmentBrief_enterScore',
                    show: (record) => {
                        return canKeyIn(record)
                    },
                    click: (record) => showModal(record, 'enter'),
                },
                {
                    neme: '修改成绩',
                    auth: 'recruitmentBrief_enterScoreUpdate',
                    show: (record) => {
                        return canUpdate(record) || canUpdateInvestigate(record) || canUpdateHealthCheck(record)
                    },
                    click: (record) => showModal(record, 'update'),
                },
                {
                    neme: '应试评价',
                    auth: 'recruitmentBrief_tryEvaluation',
                    show: (record) => {
                        return (
                            record.score != null ||
                            record.interviewScoreResult != null ||
                            record.addResult != null ||
                            record.examResult != null ||
                            record.physicalExaminationResult != null
                        )
                    },
                    click: (record) => showModal(record, 'tryEvaluation'),
                },
                {
                    neme: '考察通知',
                    auth: 'recruitmentBrief_inspectionNotice',
                    show: (record) => {
                        return record.status == 9 || record.status == 20
                    },
                    click: (record) => inspectionNotice(record, false),
                },
                {
                    neme: '体检通知',
                    auth: 'recruitmentBrief_noticeMedical',
                    show: (record) => {
                        return record.status == 12
                    },
                    click: (record) => showModal(record, 'noticeMedical'),
                },
            ]),
        )
        //导入
        const importVisible = ref(false)
        let importTemUrl = ref('')
        let importUrl = ref('')
        let downTempMethod = ref('')
        const importUrlParam = ref({})
        const temParams = ref({})

        return {
            selectBtnLoading,
            tableData,
            exportText,
            showRejectModal,
            rejectVisible,
            batchAudit,
            getResultLabel,
            currentItem,
            checkerReason,
            otherModal,
            tableRef,
            tipRef,
            columns,
            searchParams,
            importUrlParam,
            temParams,
            params,
            options,
            modalTitle,
            seeModalVisible,
            auditModalVisible,
            otherModalVisible,
            selectVisible,
            importVisible,
            commonList,
            currentValue,
            selectType,
            detailType,
            // 事件
            showModal,
            searchData,
            othersConfirm,
            onApprove,

            //操作
            myOperation,
            selectedRowsArr,
            importTemUrl,
            importUrl,
            downTempMethod,

            showExportModal,
            cancelModal,
            getTextColor,
            getCurrentEvaluation,
            importComplete,
            inquiryNotice,
            batchInquiry,
            inspectionNotice,

            refAuditModal,
        }
    },
})
</script>
<style scoped lang="less">
.rejectBtn {
    background-color: @dangerous-color;
    border: none;
}
.arrow {
    cursor: default;
}
.wrapper {
    padding: 20px;
}
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.successBtn {
    background-color: @success-color;
    border: none;
}
.foot-flex {
    display: flex;
    margin: 0 24px 24px;
    .label {
        width: 140px;
        margin-right: 10px;
        text-align: right;
    }
}
.foot-flex2 {
    display: flex;
    // margin: 0 24px 24px;
    .label {
        width: 70px;
        margin-right: 10px;
        text-align: right;
    }
}
.foot-btns {
    border-top: 1px solid #f0f0f0;
    margin-top: 15px;
    padding-top: 10px;
    display: flex;
    justify-content: flex-end;
    .delBtn {
        margin-right: 15px;
        background-color: @dangerous-color;
        color: rgba(255, 255, 255, 1);
        border: none;
    }
}
.basic_table {
    :deep(.ant-table-tbody) > tr > td.ant-table-column-sort {
        background: #fafafa;
    }
}
</style>
