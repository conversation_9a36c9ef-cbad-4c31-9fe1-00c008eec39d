<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'" :footer="null">
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span class="title">报名信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">招聘简章：</span>
                    <span>{{ currentValue?.recruitBrochureName }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">客户名称：</span>
                    <span>{{ currentValue?.clientName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">姓名：</span>
                    <span>{{ detailData?.name }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">身份证号：</span>
                    <span>{{ detailData?.certificateNum }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">性别：</span>
                    <span>{{ detailData?.sexLabel }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">学历：</span>
                    <span>{{ detailData?.highestEducationLabel }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">婚姻状态：</span>
                    <span>{{ detailData?.maritalStatusLabel }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">出生日期：</span>
                    <span>{{ detailData?.birthday }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">联系方式：</span>
                    <span>{{ detailData?.phone }}</span>
                </div>
            </div>
        </div>
        <OperationInfo
            v-if="detailData?.applyOpLogsList && detailData?.applyOpLogsList?.length"
            :applyOpLogs="detailData?.applyOpLogsList"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import OperationInfo from '/@/views/serviceCentre/retirement/component/OperationInfo.vue'
export default defineComponent({
    name: 'ApplicationSeeModal',
    components: { OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        detailData: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        // 当前编辑的数据
        const currentValue = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        // cancel handle
        const onCancel = () => {
            emit('cancel')
        }

        return {
            onCancel,
            currentValue,
        }
    },
})
</script>
<style scoped lang="less">
.examine {
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .title {
        font-weight: bold;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .item-flex-detail {
            width: 100%;
            margin: 5px 0px;
            display: flex;
            .details {
                display: flex;
                flex-direction: column;
                & span > span {
                    margin-right: 25px;
                }
            }
        }
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            display: flex;
        }
        .label {
            text-align: right;
            width: 120px;
            color: rgba(153, 153, 153, 1);
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
