<template>
    <div class="wrapper">
        <SearchBar v-model="params" :options="searchOptions" @change="searchData">
            <!-- <template #stationIdList="itemForm">
                <PostTree
                    v-model:value="params.stationIdList"
                    v-model:itemForm="searchOptions[itemForm.index]"
                    @change="searchData"
                    style="width: 190px; margin-right: 10px"
                />
            </template> -->
        </SearchBar>
        <div class="btns">
            <Button type="primary" @click="exportData" v-auth="'recruitmentBrief-look_export_notice'">{{ exportText }}</Button>
        </div>

        <BasicTable
            ref="tableRef"
            api="/api/hr-recruitment-bulletins/announcement-overview"
            :params="{ ...params, recruitmentBrochureId: recruitmentBrochureId }"
            :columns="columns"
            :exportUrl="exportUrl"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
            @getData2="(data) => (tableData = data)"
        >
            <template #hrAppendixDTOList="{ record }">
                <template v-for="val in record.hrAppendixDTOList" :key="val">
                    <a href="javascript: void(0)" @click="previewFile(val.fileUrl)" style="margin-right: 10px">
                        {{ val.originName }} </a
                    ><br />
                </template>
            </template>
            <template #operation="{ record }">
                <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            </template>
        </BasicTable>
        <SeeModal
            :visible="showEditSee"
            :title="modalTitleSee"
            :item="currentValueSee"
            @cancel="modalCancelSee"
            @confirm="modalConfirmSee"
        />
        <!-- 面试 笔试弹框 -->
        <NoticeModal ref="noticeModal" isAdjust :visible="noticeVisible" @cancel="cancelModal" @confirm="noticePublish" />
    </div>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted, toRefs, watch, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import SeeModal from './seeModal.vue'
import NoticeModal from '../component/NoticeModal.vue'
import request from '/@/utils/request'
import recruitmentBriefStore from '/@/store/modules/recruitmentBrief'
// import PostTree from '/@/views/user/postManage/postTree.vue'
import { getDynamicText, getHaveAuthorityOperation, previewFile } from '/@/utils'
import moment from 'moment'
import downFile from '/@/utils/downFile'
export default defineComponent({
    name: 'AnnouncementOverview',
    components: { SeeModal, NoticeModal },
    props: {
        announcementOverview: String,
    },
    setup(props, { emit }) {
        const { announcementOverview } = toRefs(props)
        const recruitmentBrochureId = ref<any>('')

        let noticeTypeList = ref<LabelValueOptions>([
            {
                label: '笔试公告',
                value: 1,
            },
            {
                label: '面试公告',
                value: 2,
            },
            {
                label: '成绩公告',
                value: 3,
            },
            {
                label: '其他公告',
                value: 4,
            },
        ]) //公告类型

        onMounted(() => {})

        watch(
            announcementOverview,
            () => {
                if (announcementOverview.value) {
                    recruitmentBrochureId.value = announcementOverview.value
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        //筛选
        const params = ref<{}>({})

        const searchOptions: SearchBarOption[] = [
            {
                label: '公告名称',
                key: 'recruitmentBulletinName',
            },
            {
                type: 'select',
                label: '公告类型',
                key: 'noticeTypeQuery',
                options: noticeTypeList,
                multiple: true,
            },
            {
                label: '岗位名称',
                key: 'recruitmentStationName',
            },
            {
                type: 'daterange',
                label: '发布时间',
                key: 'createdDateQuery',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '公告名称',
                dataIndex: 'recruitmentBulletinName',
                align: 'center',
                width: 180,
            },
            {
                title: '公告类型',
                dataIndex: 'noticeType',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return record?.noticeType == 3
                        ? record?.noticeTypeLabel + '_' + record?.achievementTypeLabel
                        : record?.noticeTypeLabel
                },
            },
            {
                title: '岗位名称',
                dataIndex: 'recruitmentStationName',
                align: 'center',
                // width: 150,
            },

            {
                title: '发布时间',
                dataIndex: 'createdDate',
                align: 'center',
                // width: 100,
            },
            {
                title: '内容',
                dataIndex: 'noticeContent',
                align: 'center',
                width: 300,
                customRender: ({ text }) => {
                    return text?.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '')
                },
                ellipsis: true,
                sorter: false,
            },
            {
                title: '附件',
                dataIndex: 'hrAppendixDTOList',
                align: 'center',
                slots: { customRender: 'hrAppendixDTOList' },
                width: 400,
                sorter: false,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]
        //新增编辑
        const showEditSee = ref(false)
        const modalTitleSee = ref('查看')
        // 当前编辑的数据
        const currentValueSee: any = ref(null)
        //查看
        const see = (record?) => {
            showEditSee.value = true
            currentValueSee.value = record ? { ...record } : null
            modalTitleSee.value = '查看'
        }
        const modalCancelSee = () => {
            showEditSee.value = false
            currentValueSee.value = null
        }
        const modalConfirmSee = () => {
            searchData()
        }
        //公告调整
        const adjustment = (record?) => {
            // console.log(record)
            getDetails(record.id)
                .then((res) => {
                    recruitmentBriefStore().setModalViewType(
                        record.noticeType == 1 ? 'written_announcement' : 'interview_announcement',
                    )
                    recruitmentBriefStore().setNoticeData(res)
                    noticeVisible.value = true
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // 获取详细信息
        const getDetails = (id) => {
            return new Promise((resolve, reject) => {
                request
                    .get(`/api/hr-recruitment-bulletins/view-announcements?id=` + id)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }

        const selectedRowsArr = ref([])
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        //导出
        const exportUrl = '/api/hr-recruitment-bulletins/export'
        const exportData = async () => {
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            let postParam = { recruitmentBrochureId: recruitmentBrochureId.value }
            if (exportText.value.indexOf('选中') != -1) {
                let body: inObject = []
                selectedRowsArr.value.forEach((item: inObject) => {
                    body.push(item.id)
                })
                await downFile('post', exportUrl, '', { ids: body, ...postParam })
            } else if (exportText.value.indexOf('筛选') != -1)
                await downFile('post', exportUrl, '', { ...params.value, ...postParam })
            else await downFile('post', exportUrl, '', { ...postParam })
        }

        const noticeModal = ref()
        const noticeVisible = ref(false)
        const cancelModal = (flag = false) => {
            noticeVisible.value = false
            flag && recruitmentBriefStore().setModalViewType('')
        }
        // 公告发布
        const noticePublish = (obj, type) => {
            console.log('公告发布', obj)
            console.log('公告发布', type)
            let msg = ''
            switch (type) {
                case 'written_announcement':
                    msg = '笔试公告'
                    break
                case 'interview_announcement':
                    msg = '面试公告'
                    break
            }
            request
                .put('/api/hr-recruitment-bulletins/update', obj)
                .then((res: inObject) => {
                    console.log(res)
                    message.success(`${msg}公告调整成功`)
                    cancelModal(true)
                    // type != 'score_announcement' && noticeModal.value.resetData()
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'recruitmentBrief-look_see_notice',
                    show: true,
                    click: (record) => see(record),
                },
                {
                    neme: '公告调整',
                    auth: 'recruitmentBrief-look_adjustment_notice',
                    show: (record) => {
                        return record.noticeType != 3 && record.noticeType != 4
                    },
                    click: (record) => adjustment(record),
                },
            ]),
        )

        return {
            tableData,
            selectedRowsArr,
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //查看
            see,
            //查看弹窗开关
            showEditSee,
            //查看弹窗标题
            modalTitleSee,
            //查看弹窗数据
            currentValueSee,
            //查看弹窗取消
            modalCancelSee,
            //查看弹窗确认
            modalConfirmSee,
            recruitmentBrochureId,
            previewFile,
            exportData,
            exportUrl,
            adjustment,
            noticeModal,
            noticeVisible,
            cancelModal,
            noticePublish,
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    padding: 20px;
}
.btn {
    background: @upload-color;
    border: none;
}
</style>
