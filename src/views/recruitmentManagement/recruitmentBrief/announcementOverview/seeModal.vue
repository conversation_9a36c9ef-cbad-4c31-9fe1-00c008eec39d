<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'" :footer="null">
        <div class="wrapper">
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span class="title">公告信息</span>
                <div class="examine-flex">
                    <p class="linefeed"></p>
                    <div class="item-flex1">
                        <span class="label">公告名称：</span>
                        <span style="flex: 1">{{ item?.recruitmentBulletinName }}</span>
                    </div>
                    <div class="item-flex1">
                        <span class="label">公告类型：</span>
                        <span v-show="seeModalInfoList?.noticeType != 3">{{ seeModalInfoList?.noticeTypeLabel }}</span>
                        <span v-show="seeModalInfoList?.noticeType == 3">
                            {{ `${seeModalInfoList?.noticeTypeLabel}_${seeModalInfoList?.achievementTypeLabel}` }}
                        </span>
                    </div>
                    <template v-if="seeModalInfoList?.noticeType == 3">
                        <div class="flex-wrapper">
                            <div style="flex: 1" class="item-flex" v-if="seeModalInfoList?.investigationStartTime">
                                <span class="label">考察时间：</span>
                                <span>
                                    {{
                                        `${seeModalInfoList?.investigationStartTime} ~ ${seeModalInfoList?.investigationEndTime}`
                                    }}
                                </span>
                            </div>
                            <div style="flex: 1" class="item-flex" v-if="seeModalInfoList?.investigationPlace">
                                <span class="label">考察地点：</span>
                                <span style="flex: 1">{{ seeModalInfoList?.investigationPlace }}</span>
                            </div>
                        </div>
                    </template>
                    <!-- 岗位 -->
                    <template v-else v-for="item in seeModalInfoList?.hrRecruitmentStation" :key="item">
                        <div class="item-flex">
                            <span class="label">岗位：</span>
                            <span>{{ item?.recruitmentStationName }}</span>
                        </div>
                        <div class="item-flex">
                            <span class="label">考试类型：</span>
                            <span>{{ item?.examFormatName }}</span>
                        </div>
                        <div class="item-flex" v-show="seeModalInfoList?.noticeType != 3">
                            <span class="label">考试时间：</span>
                            <span v-show="seeModalInfoList?.noticeType == 1">{{ item?.writtenExamStartTime }}</span>
                            <span v-show="seeModalInfoList?.noticeType == 2">{{ item?.interviewExamStartTime }}</span>
                        </div>
                        <template v-if="seeModalInfoList?.noticeType == 1">
                            <!-- 考场 -->
                            <template v-for="(val, index) in item?.hrRecruitmentExamRoomList" :key="val">
                                <div class="item-flex">
                                    <span class="label" v-if="index == 0">考场：</span>
                                    <span class="label" v-else></span>
                                    <span style="flex: 1">{{ val?.examRoomName }}</span>
                                </div>
                                <div class="item-flex">
                                    <span class="label" v-if="index == 0">容纳人数：</span>
                                    <span class="label" v-else></span>
                                    <span>{{ val?.containNum }}</span>
                                </div>
                                <div class="item-flex">
                                    <span class="label" v-if="index == 0">考场地点：</span>
                                    <span class="label" v-else></span>
                                    <span style="flex: 1">{{ val?.examRoomPlace }}</span>
                                </div>
                            </template>
                            <div
                                class="item-flex"
                                v-show="item?.examFormat == 1 || (item?.examFormat == 3 && seeModalInfoList?.noticeType == 1)"
                            >
                                <span class="label">是否等额考察：</span>
                                <Switch
                                    v-model:checked="item.isEqualInvestigate"
                                    checked-children="是"
                                    un-checked-children="否"
                                    :checkedValue="true"
                                    :unCheckedValue="false"
                                    disabled
                                    :getPopupContainer="getPopupContainer"
                                />
                            </div>
                            <div
                                class="item-flex"
                                v-show="item?.examFormat == 1 || (item?.examFormat == 3 && seeModalInfoList?.noticeType == 1)"
                            >
                                <span class="label">考察比例：</span>
                                <span>{{ `1 : ${item?.investigationRatio}` }}</span>
                            </div>
                            <div class="item-flex" v-show="item?.examFormat == 4 && seeModalInfoList?.noticeType == 1">
                                <span class="label">晋升比例：</span>
                                <span>{{ `1 : ${item?.promotedRatio}` }}</span>
                            </div>
                            <div class="item-flex">
                                <span class="label">及格线：</span>
                                <span>{{ item?.writtenPassLine }}</span>
                            </div>
                            <div class="item-flex">
                                <span class="label">成绩权重：</span>
                                <span>{{ `${item?.writtenScoreWeight} %` }}</span>
                            </div>
                            <div class="item-flex1">
                                <span class="label">考生须知：</span>
                                <div style="width: 90%" v-html="item?.examNotice"></div>
                            </div>
                        </template>
                        <template v-if="seeModalInfoList?.noticeType == 2">
                            <div class="item-flex">
                                <span class="label">面试地点：</span>
                                <span style="flex: 1">{{ item?.interviewLocation }}</span>
                            </div>
                            <div
                                class="item-flex"
                                v-show="item?.examFormat == 2 || (item?.examFormat == 4 && seeModalInfoList?.noticeType == 2)"
                            >
                                <span class="label">是否等额考察：</span>
                                <Switch
                                    v-model:checked="item.isEqualInvestigate"
                                    checked-children="是"
                                    un-checked-children="否"
                                    :checkedValue="true"
                                    :unCheckedValue="false"
                                    disabled
                                    :getPopupContainer="getPopupContainer"
                                />
                            </div>
                            <div
                                class="item-flex"
                                v-show="item?.examFormat == 2 || (item?.examFormat == 4 && seeModalInfoList?.noticeType == 2)"
                            >
                                <span class="label">考察比例：</span>
                                <span>{{ `1 : ${item?.investigationRatio}` }}</span>
                            </div>
                            <div class="item-flex" v-show="item?.examFormat == 3 && seeModalInfoList?.noticeType == 2">
                                <span class="label">晋升比例：</span>
                                <span>{{ `1 : ${item?.promotedRatio}` }}</span>
                            </div>
                            <div class="item-flex">
                                <span class="label">及格线：</span>
                                <span>{{ item?.interviewPassLine }}</span>
                            </div>
                            <div class="item-flex">
                                <span class="label">成绩权重：</span>
                                <span>{{ `${item?.interviewScoreWeight} %` }}</span>
                            </div>
                        </template>
                    </template>
                    <div class="item-flex1">
                        <span class="label" style="width: 110px">公告内容：</span>
                        <!-- <Textarea :rows="6" v-model:value="seeModalInfoList.noticeContent" placeholder="公告内容" disabled /> -->
                        <div
                            v-show="seeModalInfoList.noticeContent"
                            class="content"
                            v-html="seeModalInfoList.noticeContent"
                        ></div>
                        <div v-show="!seeModalInfoList.noticeContent" class="content" style="color: #bbbbbb">暂无公告内容</div>
                    </div>
                    <div class="item-flex1">
                        <span class="label">附件：</span>
                        <template v-if="seeModalInfoList?.hrAppendixDTOList && seeModalInfoList?.hrAppendixDTOList.length">
                            <span v-for="ele in seeModalInfoList.hrAppendixDTOList" :key="ele.id">
                                <a href="javascript: void(0)" @click="previewFile(ele.fileUrl)" style="margin-right: 10px">
                                    {{ ele.originName }}
                                </a>
                            </span>
                        </template>
                    </div>
                </div>
            </div>
            <OperationInfo
                v-if="seeModalInfoList?.applyOpLogsList && seeModalInfoList?.applyOpLogsList?.length"
                :applyOpLogs="seeModalInfoList?.applyOpLogsList"
            />
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, watch, toRefs } from 'vue'
import request from '/@/utils/request'
import { previewFile, SectionToChinese } from '/@/utils'
import OperationInfo from '/@/views/serviceCentre/retirement/component/OperationInfo.vue'
export default defineComponent({
    name: 'AnnouncementOverviewSeeModal',
    components: { OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        item: {
            type: Object,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, title, visible } = toRefs(props)
        const seeModalInfoList = ref<any>({})

        // cancel handle
        const onCancel = () => {
            emit('cancel')
        }
        //获取数据
        const getData = () => {
            request.get(`/api/hr-recruitment-bulletins/view-announcements?id=` + item.value?.id).then((res) => {
                seeModalInfoList.value = { ...res }
                seeModalInfoList.value.noticeContent = seeModalInfoList.value?.noticeContent?.replace(
                    /<img/g,
                    '<img style="overflow: hidden;width: 100%"',
                )
            })
        }
        watch(visible, () => {
            if (visible.value) {
                getData()
            }
        })

        return {
            onCancel,
            seeModalInfoList,
            previewFile,
            SectionToChinese,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    padding: 20px;
}
.examine {
    .flex-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    // margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        margin-bottom: 20px;
        padding-left: 15px;
        .item-flex {
            width: 33%;
            margin: 5px 0px;
            display: flex;
            .label {
                width: 100px;
                display: inline-block;
                text-align: right;
                color: rgba(153, 153, 153, 1);
            }
        }
        .item-flex1 {
            width: 90%;
            margin: 5px 0px;
            display: flex;
            .label {
                width: 100px;
                display: inline-block;
                text-align: right;
                color: rgba(153, 153, 153, 1);
            }
            .content {
                border: #bbbbbb solid 1px;
                width: 100%;
                min-height: 200px;
                // padding: 10px;
                padding: 5px 10px 10px 10px;
                border-radius: 4px;
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.examine-wrap {
    margin-top: 10px;
    padding-left: 15px;
    .linefeed {
        width: 100%;
        padding: 0;
        margin: 0;
    }
}
.operation_item {
    font-size: 14px;
    .label {
        color: rgba(153, 153, 153, 1);
    }
    .value {
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        margin-right: 20px;
    }
    .divid_item {
        margin: 10px 0;
    }
}
</style>
