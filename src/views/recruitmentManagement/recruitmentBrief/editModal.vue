<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="1200px">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '150px' } }" :rules="rules" class="form-flex">
            <template v-for="(ele, index) in myOptions" :key="ele.name + 'editModal'">
                <MyFormItem v-if="!ele.external" :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #clientId>
                        <ClientSelectTree v-model:value="formData[ele.name]" v-model:itemForm="myOptions[index]" />
                    </template>
                </MyFormItem>
                <template v-else>
                    <template v-if="ele.name == 'hrRecruitmentStation'">
                        <template v-for="(stationItem, ind) in formData.hrRecruitmentStation" :key="ind + 'hrRecruitmentStation'">
                            <FormItem
                                style="width: 100%"
                                :class="ind == 0 ? '' : 'hide'"
                                :label="ele.label"
                                :rules="validateStation"
                                :name="['hrRecruitmentStation', ind]"
                                v-if="(stationItem.isDelete == 0 && viewType !== 'add') || viewType == 'add'"
                            >
                                <div style="display: flex; align-items: center">
                                    <StationCascader
                                        v-model:value="stationItem.recruitmentStationId"
                                        :placeholder="'请选择岗位名称'"
                                        allowClear
                                        style="width: 25%"
                                        @change="getRecruitmentStationName(['hrRecruitmentStation', ind], stationItem)"
                                    />
                                    <!-- <Select
                                    :options="stationList"
                                    allowClear
                                    showSearch
                                    placeholder="请选择岗位名称"
                                    style="width: 240px"
                                    v-model:value="stationItem.recruitmentStationId"
                                    :getPopupContainer="getPopupContainer"
                                    @change="getRecruitmentStationName(['hrRecruitmentStation', ind], stationItem)"
                                /> -->
                                    <InputNumber
                                        v-model:value="stationItem.recruitmentPeopleNumber"
                                        placeholder="招聘人数"
                                        :formatter="(value) => limitNumber(value)"
                                        :parser="(value) => limitNumber(value)"
                                        :min="1"
                                        style="width: 25%; margin: 0 15px"
                                        @change="formValidateOptional(['hrRecruitmentStation', ind])"
                                        @blur="formValidateOptional(['hrRecruitmentStation', ind])"
                                    />
                                    <InputNumber
                                        v-model:value="stationItem.recruitmentFee"
                                        placeholder="报名费"
                                        style="width: 25%; margin-right: 15px"
                                        :min="0"
                                        :formatter="(value) => limitRMB(value)"
                                        :parser="(value) => value.replace('元', '')"
                                        @change="formValidateOptional(['hrRecruitmentStation', ind])"
                                        @blur="formValidateOptional(['hrRecruitmentStation', ind])"
                                    />
                                    <Select
                                        placeholder="考试形式"
                                        v-model:value="stationItem.examFormat"
                                        showSearch
                                        optionFilterProp="label"
                                        style="width: 25%"
                                        allowClear
                                        :options="examModeList"
                                        :getPopupContainer="getPopupContainer"
                                        @change="examFormatValidate(ind)"
                                    />
                                    <span class="icon-wrapper" v-if="ind == 0">
                                        <PlusCircleOutlined class="dynamic-add-button" @click="addDomain()" />
                                    </span>
                                    <span class="icon-wrapper" v-else>
                                        <MinusCircleOutlined class="dynamic-delete-button" @click="delDomain(ind)" />
                                    </span>
                                </div>
                            </FormItem>
                            <FormItem
                                style="width: 100%"
                                class="hide"
                                label="报名模板"
                                :rules="validateTemplate"
                                :name="['registerTemplateId', ind]"
                                v-if="(stationItem.isDelete == 0 && viewType !== 'add') || viewType == 'add'"
                            >
                                <Select
                                    :options="recruitmentTemplateList"
                                    allowClear
                                    showSearch
                                    placeholder="请选择报名模板"
                                    v-model:value="stationItem.registerTemplateId"
                                    style="width: 42%"
                                    @change="formValidateOptional(['registerTemplateId', ind])"
                                />
                                <Select
                                    v-if="
                                        (stationItem.isDelete == 0 &&
                                            viewType !== 'add' &&
                                            stationItem.examFormat &&
                                            stationItem.examFormat != 2) ||
                                        (viewType == 'add' && stationItem.examFormat && stationItem.examFormat != 2)
                                    "
                                    :options="writtenPaperList"
                                    allowClear
                                    showSearch
                                    placeholder="请选择笔试试卷"
                                    v-model:value="stationItem.paperId"
                                    :getPopupContainer="getPopupContainer"
                                    @change="formValidateOptional(['registerTemplateId', ind])"
                                    style="width: calc(29% - 15px); margin-left: 15px"
                                />
                                <Select
                                    v-if="
                                        (stationItem.isDelete == 0 &&
                                            viewType !== 'add' &&
                                            stationItem.examFormat &&
                                            stationItem.examFormat != 1) ||
                                        (viewType == 'add' && stationItem.examFormat && stationItem.examFormat != 1)
                                    "
                                    :options="interviewPaperList"
                                    allowClear
                                    showSearch
                                    placeholder="请选择面试试卷"
                                    v-model:value="stationItem.interviewPaperId"
                                    :getPopupContainer="getPopupContainer"
                                    @change="formValidateOptional(['registerTemplateId', ind])"
                                    style="width: calc(29% - 15px); margin-left: 15px"
                                />
                            </FormItem>
                        </template>
                    </template>
                    <template v-if="ele.name == 'registerDate'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="registerDate" :rules="validateDate">
                            <DatePicker
                                v-model:value="formData.registerDate[0]"
                                format="YYYY-MM-DD"
                                placeholder="请选择开始日期"
                                valueFormat="YYYY-MM-DD"
                                style="width: 190px; margin-right: 20px"
                                @change="changeDate('registerDate', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.registerDate[1]"
                                format="YYYY-MM-DD"
                                placeholder="请选择结束日期"
                                valueFormat="YYYY-MM-DD"
                                style="width: 190px"
                                @change="changeDate('registerDate', 1)"
                                :disabled-date="disableRegisterDate"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'auditDate'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="auditDate" :rules="validateDate">
                            <DatePicker
                                v-model:value="formData.auditDate[0]"
                                format="YYYY-MM-DD"
                                placeholder="请选择开始日期"
                                valueFormat="YYYY-MM-DD"
                                style="width: 190px; margin-right: 20px"
                                @change="changeDate('auditDate', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.auditDate[1]"
                                format="YYYY-MM-DD"
                                placeholder="请选择结束日期"
                                style="width: 190px"
                                valueFormat="YYYY-MM-DD"
                                @change="changeDate('auditDate', 1)"
                                :disabled-date="disableAuditDate"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'paymentDate'">
                        <FormItem
                            :style="'width:' + ele.width"
                            :label="ele.label"
                            name="paymentDate"
                            :rules="paymentDateRequired ? validateDate : undefined"
                        >
                            <DatePicker
                                v-model:value="formData.paymentDate[0]"
                                format="YYYY-MM-DD"
                                placeholder="请选择开始日期"
                                valueFormat="YYYY-MM-DD"
                                style="width: 190px; margin-right: 20px"
                                @change="changeDate('paymentDate', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.paymentDate[1]"
                                format="YYYY-MM-DD"
                                placeholder="请选择结束日期"
                                valueFormat="YYYY-MM-DD"
                                style="width: 190px"
                                @change="changeDate('paymentDate', 1)"
                                :disabled-date="disablePaymentDate"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'enterpriseWechatUrl'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="enterpriseWechatUrl">
                            <ImportImg v-model:imageUrl="formData.enterpriseWechatUrl" @fetchImgInfo="fetchImgInfo" />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'detailContent'">
                        <div v-if="viewType !== 'adjust'" class="copy_link">
                            <span @click="copyMiniEntryLink"> 点击复制小程序报名入口链接 </span>
                        </div>
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="detailContent">
                            <WangEditor
                                :value="formData.detailContent"
                                @on-change="handleChange"
                                ref="editorRef"
                                :editorConfig="editorConfig"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'appendixIdList'">
                        <FormItem label="附件" name="appendixIdList" style="width: 100%">
                            <ImportFile v-model:fileUrls="formData.appendixIdList" ref="refImportFile" />
                        </FormItem>
                    </template>
                </template>
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm('enter')" type="primary" class="btn" v-if="viewType == 'adjust'">确定</Button>
            <Button @click="onConfirm('save')" type="primary" class="btn" v-if="viewType !== 'adjust'">保存</Button>
            <Button @click="onConfirm('submit')" type="primary" class="btn" v-if="viewType !== 'adjust'"> 提交 </Button>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed, nextTick, h, onMounted } from 'vue'
import { MinusCircleOutlined, PlusCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import { valuesAndRules } from '/#/component'
import { getValuesAndRules, isEmptyNull } from '/@/utils/index'
import request from '/@/utils/request'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { message, Modal } from 'ant-design-vue'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import { Moment } from 'moment'
import moment from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'EditModal',
    components: { ClientSelectTree, MinusCircleOutlined, PlusCircleOutlined },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { visible, viewType } = toRefs(props)
        const refImportFile = ref()
        const writtenPaperList = ref<inObject[]>([])
        const interviewPaperList = ref<inObject[]>([])
        const currentItem = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        const paymentDateRequired = ref(false)
        const recruitmentNeedDisabled = ref(true)
        const examModeList = computed(() => {
            return recruitmentBriefStore().getExamModeList
        })
        const stationList = computed(() => {
            return recruitmentBriefStore().getStationList
        })
        const recruitmentTemplateList = computed(() => {
            return recruitmentBriefStore().getRecruitmentTemplateList.map((el) => {
                return { label: el.templateName, value: el.id }
            })
        })
        const brochureNameDisabled = computed(() => {
            return viewType.value == 'adjust'
        })
        // 校验招聘简章名称是否唯一
        const verifyNameUnique = async (rule: RuleObject, value) => {
            if (rule.required) {
                if (!value) {
                    return Promise.reject('请输入招聘简章名称')
                }
            }
            if (brochureNameDisabled.value) {
                Promise.resolve()
            } else {
                if (value.length > 50) {
                    return Promise.reject('超出字数限制，最多输入50个字！')
                } else {
                    await request
                        .post(
                            '/api/hr-recruitment-brochures/duplicate-query-data',
                            {
                                id: currentItem.value?.id,
                                recruitBrochureName: value,
                            },
                            { loading: false },
                        )
                        .then((res) => {
                            if (res != '操作成功') return Promise.reject(res)
                            else return Promise.resolve()
                        })
                        .catch((err) => {
                            console.log(err)
                            return Promise.reject(err || err.message)
                        })
                }
            }
        }
        const recruitmentNeedList = ref<LabelValueOptions[]>([]) // 需求编号
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '招聘简章名称',
                name: 'recruitBrochureName',
                width: '100%',
                trigger: 'blur',
                disabled: brochureNameDisabled,
                validator: verifyNameUnique,
            },
            {
                label: '招聘单位',
                name: 'clientId',
                width: '50%',
                type: 'slots',
                slots: 'clientId',
                disabled: brochureNameDisabled,
                onChange: (val) => {
                    recruitmentNeedDisabled.value = isEmptyNull(val)
                    if (val) {
                        // 需求编号
                        request
                            .get('/api/hr-recruitment-brochures/unit-recruitment-demand', { clientId: val })
                            .then((res) => {
                                recruitmentNeedList.value = res.map((item) => {
                                    return { label: item.recruitmentNumber, value: item.id, ...item }
                                })
                            })
                            .catch((err) => {
                                console.log(err)
                            })
                    } else recruitmentNeedList.value = []
                    formData.value.recruitmentNeedId = undefined
                    formData.value.hrRecruitmentStation = [
                        {
                            recruitmentStationId: undefined,
                            recruitmentStationName: undefined,
                            recruitmentPeopleNumber: undefined,
                            recruitmentFee: undefined,
                            examFormat: undefined,
                            isDelete: 0,
                            paperId: undefined,
                            interviewPaperId: undefined,
                            registerTemplateId: undefined,
                        },
                    ]
                },
            },
            {
                label: '需求编号',
                name: 'recruitmentNeedId',
                width: '50%',
                type: 'select',
                options: recruitmentNeedList,
                showbr: true,
                required: false,
                disabled: recruitmentNeedDisabled,
                onChange: (val) => {
                    let tempObj: inObject | undefined = recruitmentNeedList.value.find((el: inObject) => {
                        return el.value == val
                    })
                    if (tempObj) {
                        tempObj?.hrRecruitmentStationDTOList.forEach((el, index) => {
                            formData.value.hrRecruitmentStation[index] = {
                                recruitmentStationId: el.recruitmentStationId,
                                recruitmentStationName: el.recruitmentStationName,
                                recruitmentPeopleNumber: el.recruitmentPeopleNumber,
                                examFormat: el.examFormat,
                                recruitmentFee: undefined,
                                isDelete: 0,
                                paperId: undefined,
                                interviewPaperId: undefined,
                            }
                            formValidateOptional(['hrRecruitmentStation', index])
                        })
                    } else {
                        formData.value.hrRecruitmentStation = [
                            {
                                recruitmentStationId: undefined,
                                recruitmentStationName: undefined,
                                recruitmentPeopleNumber: undefined,
                                recruitmentFee: undefined,
                                examFormat: undefined,
                                isDelete: 0,
                                paperId: undefined,
                                interviewPaperId: undefined,
                                registerTemplateId: undefined,
                            },
                        ]
                    }
                },
            },
            {
                label: '招聘岗位',
                name: 'hrRecruitmentStation',
                ruleType: 'array',
                default: [
                    {
                        recruitmentStationId: undefined,
                        recruitmentStationName: undefined,
                        recruitmentPeopleNumber: undefined,
                        recruitmentFee: undefined,
                        examFormat: undefined,
                        isDelete: 0,
                        paperId: undefined,
                        interviewPaperId: undefined,
                        registerTemplateId: undefined,
                    },
                ],
                width: '100%',
                required: true,
                external: true,
            },
            {
                label: '可多岗同报',
                name: 'isOverstate',
                type: 'switch',
                ruleType: 'boolean',
                default: false,
                checkText: '√',
                unCheckText: '×',
                width: '100%',
            },
            {
                label: '报名日期',
                name: 'registerDate',
                ruleType: 'array',
                default: ['', ''],
                width: '100%',
                external: true,
            },
            {
                label: '简历审核日期',
                name: 'auditDate',
                ruleType: 'array',
                default: ['', ''],
                width: '100%',
                external: true,
            },
            {
                label: '缴费日期',
                name: 'paymentDate',
                ruleType: 'array',
                default: ['', ''],
                width: '100%',
                required: paymentDateRequired,
                external: true,
            },
            {
                label: '企业微信',
                name: 'enterpriseWechatUrl',
                width: '100%',
                trigger: 'change',
                required: false,
                external: true,
            },
            {
                label: '招聘简章详细内容',
                name: 'detailContent',
                width: '100%',
                required: false,
                external: true,
            },
            {
                label: '附件',
                name: 'appendixIdList',
                default: [],
                ruleType: 'array',
                width: '100%',
                required: false,
                external: true,
            },
        ])
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('', '/api/hr-recruitment-stations/choice-test-paper', 'get', true)
                .then((data: inObject) => {
                    writtenPaperList.value = data.writtenExamList.map((item) => {
                        return { value: item.id, label: item.paperName }
                    })
                    interviewPaperList.value = data.interviewList.map((item) => {
                        return { value: item.id, label: item.paperName }
                    })
                })
        })

        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
            formData.value['hrRecruitmentStation'] = [
                {
                    recruitmentStationId: undefined,
                    recruitmentStationName: undefined,
                    recruitmentPeopleNumber: undefined,
                    recruitmentFee: undefined,
                    examFormat: undefined,
                    isDelete: 0,
                    paperId: undefined,
                    interviewPaperId: undefined,
                    registerTemplateId: undefined,
                },
            ]
        }
        const onCancel = () => {
            emit('cancel')
        }

        const onConfirm = (type) => {
            let myUrl = []
            if (formData.value.appendixIdList?.length) {
                myUrl = refImportFile.value.getFileUrls().map((item) => {
                    return item.id
                })
            }
            let params = formData.value
            params.registerStartDate = formData.value?.registerDate[0]
            params.registerEndDate = formData.value?.registerDate[1]
            params.auditStartDate = formData.value?.auditDate[0]
            params.auditEndDate = formData.value?.auditDate[1]
            params.paymentStartDate = formData.value?.paymentDate[0]
            params.paymentEndDate = formData.value?.paymentDate[1]
            // type == 'enter' && delete params.appendixIdList
            formInline.value
                .validate()
                .then(async () => {
                    if (type == 'submit' || type == 'enter') {
                        type == 'submit' ? (params.identification = 2) : (params.identification = 3)
                        if (type == 'enter') {
                            if (
                                await judgeTemplateHasChange(params.hrRecruitmentStation, currentItem.value?.hrRecruitmentStation)
                            ) {
                                Modal.confirm({
                                    title: '若更改报名模板，已报名用户信息将会删除',
                                    icon: h(ExclamationCircleOutlined),
                                    onOk() {
                                        emit('confirm', params, type)
                                    },
                                    onCancel() {
                                        console.log('Cancel')
                                    },
                                })
                            } else if (
                                await judgeStationHasChange(params.hrRecruitmentStation, currentItem.value?.hrRecruitmentStation)
                            ) {
                                Modal.confirm({
                                    title: '若更改招聘岗位，已报名用户信息将会删除',
                                    icon: h(ExclamationCircleOutlined),
                                    onOk() {
                                        emit('confirm', params, type)
                                    },
                                    onCancel() {
                                        console.log('Cancel')
                                    },
                                })
                            } else {
                                emit('confirm', { ...params, appendixIdList: myUrl }, type)
                            }
                        } else {
                            emit('confirm', { ...params, appendixIdList: myUrl }, type)
                        }
                    } else {
                        params.identification = 1
                        emit('confirm', { ...params, appendixIdList: myUrl }, type)
                    }
                })
                .catch((err) => {
                    if (type == 'save') message.error('请先将必填信息填写完整！')
                    console.log(`表单验证失败${err}`)
                })
        }

        // 判断报名模板是否有改变
        const judgeTemplateHasChange = async (newArr, arr) => {
            let flag = false
            for (let i = 0; i < arr.length; i++) {
                if (arr[i].registerTemplateId !== newArr[i].registerTemplateId) {
                    flag = true
                    break
                }
            }
            return flag
        }

        // 判断招聘岗位是否有改变
        const judgeStationHasChange = async (newArr, arr) => {
            let flag = false
            for (let i = 0; i < arr.length; i++) {
                if (arr[i].recruitmentStationId !== newArr[i].recruitmentStationId) {
                    flag = true
                    break
                }
            }
            return flag
        }

        const handleChange = (html) => {
            formData.value.detailContent = html
        }

        watch(
            [visible, currentItem],
            ([newVisible, newCurrent], [oldVisible, oldCurrent]) => {
                console.log(viewType.value)
                if (newVisible && newCurrent?.id && !oldVisible) {
                    formData.value = Object.assign({}, initFormData, JSON.parse(JSON.stringify(newCurrent)))
                    formData.value.registerDate = [newCurrent?.registerStartDate, newCurrent?.registerEndDate]
                    formData.value.auditDate = [newCurrent?.auditStartDate, newCurrent?.auditEndDate]
                    formData.value.paymentDate = [newCurrent?.paymentStartDate, newCurrent?.paymentEndDate]
                    formData.value.appendixIdList = newCurrent?.hrAppendixDTOS
                    recruitmentNeedDisabled.value = isEmptyNull(newCurrent.clientId)
                    if (newCurrent.clientId) {
                        // 需求编号
                        request
                            .get('/api/hr-recruitment-brochures/unit-recruitment-demand', { clientId: newCurrent.clientId })
                            .then((res) => {
                                recruitmentNeedList.value = res.map((item) => {
                                    return { label: item.recruitmentNumber, value: item.id }
                                })
                            })
                            .catch((err) => {
                                console.log(err)
                            })
                    }
                }
                if (newVisible && !newCurrent?.id) {
                    formData.value = initFormData
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        watch(
            () => formData.value.hrRecruitmentStation,
            (val, old) => {
                paymentDateRequired.value = val.some((el) => {
                    return Number(el.recruitmentFee ?? 0) > 0
                })
            },
            { deep: true },
        )
        const fetchImgInfo = (info) => {
            formValidateOptional('enterpriseWechatUrl')
        }

        const getRecruitmentStationName = (nameList, item) => {
            formValidateOptional(nameList)
            item.recruitmentStationName = stationList.value.find((el) => {
                return el.value == item.recruitmentStationId
            })?.label
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        const limitRMB = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? `${value}元` : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? `${String(value)}元` : ''
            } else {
                return ''
            }
        }
        const changeDate = (key, index) => {
            formValidateOptional(key)
            if (key == 'registerDate') {
                formData.value['auditDate'][0] = ''
                formData.value['auditDate'][1] = ''
                formData.value['paymentDate'][0] = ''
                formData.value['paymentDate'][1] = ''
            }
            if (key == 'auditDate') {
                formData.value['paymentDate'][0] = ''
                formData.value['paymentDate'][1] = ''
            }
            if (index == 0) formData.value[key][1] = ''
        }
        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }
        const examFormatValidate = (ind) => {
            formData.value['hrRecruitmentStation'][ind].paperId = undefined
            formData.value['hrRecruitmentStation'][ind].interviewPaperId = undefined
            formValidateOptional(['hrRecruitmentStation', ind])
            formValidateOptional(['registerTemplateId', ind])
        }
        const validateStation = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentStation[rule.field.split('.')[1]]
                // 校验岗位是否唯一
                if (formDataItem?.recruitmentStationId) {
                    let count = 0
                    formData.value.hrRecruitmentStation.forEach((el) => {
                        if (el.recruitmentStationId == formDataItem.recruitmentStationId) count++
                    })
                    if (count > 1) {
                        return Promise.reject('招聘岗位不能重复')
                    } else {
                        if (
                            !formDataItem?.recruitmentPeopleNumber ||
                            isEmptyNull(formDataItem?.recruitmentFee) ||
                            !formDataItem?.examFormat
                        ) {
                            return Promise.reject('请将岗位信息设置完整')
                        } else {
                            return Promise.resolve()
                        }
                    }
                } else {
                    return Promise.reject('请将岗位信息设置完整')
                }
            },
        }
        const validateTemplate = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentStation[rule.field.split('.')[1]]
                // 2面试 1笔试
                if (!formDataItem?.registerTemplateId) {
                    return Promise.reject('请设置报名模板')
                } else if (!formDataItem?.paperId && formDataItem?.examFormat && formDataItem?.examFormat != 2) {
                    return Promise.reject('请设置笔试的试卷')
                } else if (!formDataItem?.interviewPaperId && formDataItem?.examFormat && formDataItem?.examFormat != 1) {
                    return Promise.reject('请设置面试的试卷')
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validatePaper = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentStation[rule.field.split('.')[1]]
                if (!formDataItem?.paperId) {
                    return Promise.reject('请设置考试的试卷')
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validateDate = {
            required: true,
            trigger: ['change'],
            type: 'array',
            validator: async (rule: inObject, value: any) => {
                let msg = ''
                switch (rule.field) {
                    case 'registerDate':
                        msg = '报名'
                        break
                    case 'auditDate':
                        msg = '简历审核'
                        break
                    case 'paymentDate':
                        msg = '缴费'
                        break
                }
                if (!value) {
                    return Promise.reject(`请将${msg}日期设置完整`)
                } else {
                    if (value?.[0] && value?.[1]) {
                        return Promise.resolve()
                    } else {
                        return Promise.reject(`请将${msg}日期设置完整`)
                    }
                }
            },
        }
        const validateImg = {
            required: true,
            trigger: ['change'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请填入企业微信')
                } else {
                    return Promise.resolve()
                }
            },
        }
        const validateEditor = {
            required: true,
            trigger: ['change'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请填写招聘简章详细内容')
                } else {
                    return Promise.resolve()
                }
            },
        }

        const disableRegisterDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.registerDate[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            return new Date(formData.value?.registerDate[0]).valueOf() >= endValue.endOf('day').valueOf()
        }
        const disableAuditDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.auditDate[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            if (formData.value?.registerDate[1])
                return endValue.endOf('day').valueOf() <= new Date(formData.value?.registerDate[1]).valueOf()
            return new Date(formData.value?.auditDate[0]).valueOf() >= endValue.endOf('day').valueOf()
        }
        const disablePaymentDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.paymentDate[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            if (formData.value?.auditDate[1])
                return endValue.endOf('day').valueOf() <= new Date(formData.value?.auditDate[1]).valueOf()
            return new Date(formData.value?.paymentDate[0]).valueOf() >= endValue.endOf('day').valueOf()
        }

        const addDomain = () => {
            formData.value['hrRecruitmentStation'].push({
                recruitmentStationId: undefined,
                recruitmentStationName: undefined,
                recruitmentPeopleNumber: undefined,
                recruitmentFee: undefined,
                examFormat: undefined,
                isDelete: 0,
                paperId: undefined,
                interviewPaperId: undefined,
                registerTemplateId: undefined,
            })
        }
        const delDomain = (ind) => {
            if (viewType.value == 'add') formData.value['hrRecruitmentStation'].splice(ind, 1)
            else formData.value['hrRecruitmentStation'][ind].isDelete = 1
        }
        const copyMiniEntryLink = () => {
            const locationParams = import.meta.env.VITE_UNI_PATH || ''
            let text = `${window.location.origin + locationParams ?? ''}/openScheme/signUp.html`
            if (navigator.clipboard) {
                // clipboard api 复制
                navigator.clipboard.writeText(text)
            } else {
                let textarea = document.createElement('textarea')
                document.body.appendChild(textarea)
                // 隐藏此输入框
                textarea.style.position = 'fixed'
                textarea.style.clip = 'rect(0 0 0 0)'
                textarea.style.top = '10px'
                // 赋值
                textarea.value = text
                // 选中
                textarea.select()
                // 复制
                document.execCommand('copy', true)
                // 移除输入框
                document.body.removeChild(textarea)
            }
            console.log(text)
            message.info('已复制到剪切板')
        }

        return {
            changeDate,
            currentItem,
            resetData,
            refImportFile,
            rules,
            formData,
            myOptions,
            formInline,
            onConfirm,
            onCancel,
            limitNumber,
            limitRMB,
            handleChange,
            editorConfig: {
                height: 300,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            stationList,
            examModeList,
            recruitmentTemplateList,
            addDomain,
            delDomain,
            validateStation,
            examFormatValidate,
            formValidateOptional,
            copyMiniEntryLink,
            getRecruitmentStationName,
            validateDate,
            validateTemplate,
            validatePaper,
            validateImg,
            disableRegisterDate,
            disableAuditDate,
            disablePaymentDate,
            fetchImgInfo,
            paymentDateRequired,
            writtenPaperList,
            interviewPaperList,
            getPopupContainer: (triggerNode) => {
                return triggerNode.parentNode
            },
        }
    },
})
</script>
<style lang="less" scoped>
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    :deep(.ant-form-item-control) {
        width: calc(100% - 150px);
    }
    .hide {
        :deep(.ant-form-item-required) {
            display: none;
        }
        :deep(.ant-col.ant-form-item-label) {
            label {
                color: rgba(0, 0, 0, 0) !important;
            }
        }
    }
    .icon-wrapper {
        width: 20px;
        margin-left: 10px;
        display: inline-block;
    }
    .dynamic-add-button {
        cursor: pointer;
        position: relative;
        top: 2px;
        font-size: 20px;
        color: #999;
        transition: all 0.3s;
        color: @primary-color;
    }
    .dynamic-add-button:hover {
        color: @primary-color;
    }
    .dynamic-add-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
    .dynamic-delete-button {
        cursor: pointer;
        position: relative;
        top: 3px;
        font-size: 20px;
        color: #999;
        transition: all 0.3s;
        color: @dangerous-color;
    }
    .dynamic-delete-button:hover {
        color: @dangerous-color;
    }
    .dynamic-delete-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
}
.copy_link {
    color: @primary-color;
    display: flex;
    width: 100%;
    justify-content: flex-end;
    span {
        cursor: pointer;
    }
}
</style>
