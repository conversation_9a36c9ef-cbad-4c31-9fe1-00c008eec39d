<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #recruitmentStationIdList="itemForm">
            <PostTree
                v-model:value="params.recruitmentStationIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
                style="width: 190px; margin-right: 10px"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'recruitmentBrief_add'" @click="showModal(null, 'add')">新建</Button>
        <Button type="primary" v-auth="'recruitmentBrief_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" class="rejectBtn" v-auth="'recruitmentBrief_batch_delete'" @click="batchDelete()">
            批量删除
        </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-recruitment-brochures/page"
        deleteApi="/api/hr-recruitment-brochures/deletes"
        :exportUrl="'/api/hr-recruitment-brochures/export'"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
        <template #templateName="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="other_lines" v-if="index != 0"></div>
                <div>{{ item.templateName }}</div>
            </div>
        </template>
        <template #professionName="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="other_lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentStationName }}</div>
            </div>
        </template>
        <template #recruitmentPeopleNumber="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="num_lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentPeopleNumber }}</div>
            </div>
        </template>
        <template #recruitmentNum="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="num_lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentNum }}</div>
            </div>
        </template>
        <template #passNum="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="num_lines" v-if="index != 0"></div>
                <div>{{ item.passNum }}</div>
            </div>
        </template>
        <template #paymentNum="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="num_lines" v-if="index != 0"></div>
                <div>{{ item.paymentNum }}</div>
            </div>
        </template>
        <template #examFormat="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="other_lines" v-if="index != 0"></div>
                <div>{{ item.examFormatName }}</div>
            </div>
        </template>
        <template #recruitmentFee="{ record }">
            <div v-for="(item, index) in record.hrRecruitmentStation" :key="index">
                <div class="num_lines" v-if="index != 0"></div>
                <div>{{ item.recruitmentFee }}</div>
            </div>
        </template>
    </BasicTable>
    <EditModal
        ref="editModal"
        :title="modalTitle"
        :viewType="detailType"
        :visible="editVisible"
        @cancel="cancelModal"
        @confirm="brochuresHandle"
    />
    <DetailsModal :title="modalTitle" :visible="detailsVisible" @cancel="cancelModal" :item="modalValue" />
    <AuditModal ref="auditModal" :title="modalTitle" :visible="auditVisible" @cancel="cancelModal" @confirm="approveHandle" />
    <SelectCommon
        title="发布公告"
        :visible="selectVisible"
        viewType="notice"
        v-model:commonList="commonList"
        @cancel="cancelModal"
    />
    <!-- 面试 笔试弹框 -->
    <NoticeModal ref="noticeModal" :visible="noticeVisible" @cancel="cancelModal" @confirm="noticePublish" />
    <!-- 成绩弹框 -->
    <MarkModal ref="markModal" :title="modalTitle" :visible="markVisible" @cancel="cancelModal" @confirm="noticePublish" />
    <!-- 草稿箱 -->
    <DraftBox ref="draftModal" />
</template>
<script lang="ts">
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, onMounted, computed, watch, h } from 'vue'
import request from '/@/utils/request'
import PostTree from '/@/views/user/postManage/postTree.vue'
import recruitmentBriefStore from '/@/store/modules/recruitmentBrief'
import { SearchBarOption } from '/#/component'
import EditModal from './editModal.vue'
import DetailsModal from './detailsModal.vue'
import AuditModal from './component/AuditModal.vue'
import SelectCommon from './component/SelectCommon.vue'
import NoticeModal from './component/NoticeModal.vue'
import MarkModal from './component/MarkModal.vue'
import DraftBox from './component/DraftBox.vue'

import dictionaryDataStore from '/@/store/modules/dictionaryData'
import moment from 'moment'
import { getDynamicText, getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'RecruitmentBrief',
    components: {
        EditModal,
        PostTree,
        DetailsModal,
        AuditModal,
        SelectCommon,
        NoticeModal,
        MarkModal,
        DraftBox,
    },
    setup() {
        const editModal = ref()
        const auditModal = ref()
        const noticeModal = ref()
        const markModal = ref()
        const modalViewType = computed(() => {
            return recruitmentBriefStore().getModalViewType
        })
        const isOuter = computed(() => {
            return recruitmentBriefStore().getIsOuter
        })
        const recruitmentTemplateList = computed(() => {
            return recruitmentBriefStore().getRecruitmentTemplateList.map((el) => {
                return { label: el.templateName, value: el.id }
            })
        })

        // 筛选
        let stateList = ref<LabelValueOptions>([]) // 状态
        const examModeList = computed(() => {
            return recruitmentBriefStore().getExamModeList
        })

        let options: SearchBarOption[] = [
            {
                label: '需求编号',
                key: 'recruitmentNumber',
                type: 'string',
            },
            {
                label: '招聘单位',
                key: 'clientIds',
                placeholder: '招聘单位',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '招聘简章名称',
                key: 'recruitBrochureName',
            },
            {
                type: 'select',
                label: '报名模板',
                key: 'templateIdList',
                multiple: true,
                options: recruitmentTemplateList,
            },
            {
                type: 'selectSlot',
                label: '岗位',
                key: 'recruitmentStationIdList',
                placeholder: '岗位名称',
                maxTag: '0',
            },
            {
                type: 'numberrange',
                label: '招聘人数',
                integerOnly: true,
                key: 'recruitmentPeopleNumberQuery',
            },
            {
                type: 'numberrange',
                label: '报名人数',
                integerOnly: true,
                key: 'recruitmentNumQuery',
            },
            {
                type: 'numberrange',
                label: '简历通过人数',
                integerOnly: true,
                key: 'passNumQuery',
            },
            {
                type: 'numberrange',
                label: '缴费人数',
                integerOnly: true,
                key: 'paymentNumQuery',
            },
            {
                type: 'select',
                label: '考试形式',
                key: 'examFormatList',
                options: examModeList,
                multiple: true,
            },
            {
                type: 'numberrange',
                label: '报名费',
                key: 'recruitmentFeeQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'registerStateList',
                options: stateList,
                multiple: true,
            },
        ]

        //表格数据
        const columns = ref([
            {
                title: '需求编号',
                dataIndex: 'recruitmentNumber',
                align: 'center',
                width: 150,
            },
            {
                title: '招聘单位',
                dataIndex: 'clientName',
                align: 'center',
                width: 180,
            },
            {
                title: '招聘简章名称',
                dataIndex: 'recruitBrochureName',
                align: 'center',
                width: 180,
            },
            {
                title: '岗位',
                dataIndex: 'professionName',
                align: 'center',
                width: 160,
                ellipsis: true,
                slots: { customRender: 'professionName' },
            },
            {
                title: '报名模板',
                dataIndex: 'registerTemplateId',
                align: 'center',
                width: 160,
                slots: { customRender: 'templateName' },
            },
            {
                title: '招聘人数',
                dataIndex: 'recruitmentPeopleNumber',
                align: 'center',
                width: 100,
                slots: { customRender: 'recruitmentPeopleNumber' },
            },
            {
                title: '报名人数',
                dataIndex: 'recruitmentNum',
                align: 'center',
                width: 100,
                slots: { customRender: 'recruitmentNum' },
            },
            {
                title: '简历通过人数',
                dataIndex: 'passNum',
                align: 'center',
                width: 100,
                slots: { customRender: 'passNum' },
            },
            {
                title: '缴费人数',
                dataIndex: 'paymentNum',
                align: 'center',
                width: 100,
                slots: { customRender: 'paymentNum' },
            },
            {
                title: '考试形式',
                dataIndex: 'examFormat',
                align: 'center',
                width: 160,
                ellipsis: true,
                slots: { customRender: 'examFormat' },
            },
            {
                title: '报名费',
                dataIndex: 'recruitmentFee',
                align: 'center',
                width: 100,
                slots: { customRender: 'recruitmentFee' },
            },
            {
                title: '状态',
                dataIndex: 'registerState',
                align: 'center',
                width: 130,
                customRender: ({ record }) => {
                    return stateList.value.find((el) => {
                        return record.registerState == el.value
                    })?.label
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ])

        onMounted(() => {
            recruitmentBriefStore().typeListInit()
            // 获取状态
            dictionaryDataStore()
                .setDictionaryData('recruitBrochureState', '', 'get', true)
                .then((data: inObject[]) => {
                    stateList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        //表格dom
        const tableRef = ref()

        //筛选
        const params = ref({})

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const modalTitle = ref('')
        const editVisible = ref(false)
        const detailsVisible = ref(false)
        const auditVisible = ref(false)
        const selectVisible = ref(false)
        const noticeVisible = ref(false)
        const markVisible = ref(false)
        const detailType = ref('')
        const commonList = ref<inObject[]>([])
        const modalValue = ref({})

        // 当前编辑的数据
        const currentValue = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })

        // 显示弹窗
        const showModal = (record, type) => {
            detailType.value = type
            if (type == 'add') {
                editVisible.value = true
                detailsVisible.value = false
                auditVisible.value = false
                modalTitle.value = '新建招聘简章'
                recruitmentBriefStore().setCurrentData(record)
            } else {
                getDetails(record.id)
                    .then((res) => {
                        recruitmentBriefStore().setCurrentData(res)
                        switch (type) {
                            case 'look':
                                editVisible.value = false
                                detailsVisible.value = true
                                auditVisible.value = false
                                modalTitle.value = '查看'
                                modalValue.value = record
                                break
                            case 'edit':
                                editVisible.value = true
                                detailsVisible.value = false
                                auditVisible.value = false
                                modalTitle.value = '编辑招聘简章'
                                break
                            case 'adjust':
                                editVisible.value = true
                                detailsVisible.value = false
                                auditVisible.value = false
                                modalTitle.value = '调整招聘简章'
                                break
                            case 'audit':
                                editVisible.value = false
                                detailsVisible.value = false
                                auditVisible.value = true
                                modalTitle.value = '简章审核'
                                break
                            case 'notice_publish':
                                if (record.paymentEndDate) {
                                    if (moment(record.paymentEndDate).endOf('day').valueOf() >= moment().endOf('day').valueOf())
                                        message.warning('请在缴费日期后发布公告')
                                    else {
                                        editVisible.value = false
                                        detailsVisible.value = false
                                        auditVisible.value = false
                                        selectVisible.value = true
                                        commonList.value = [
                                            {
                                                name: '笔试公告',
                                                url: getImageUrl('written_announcement'),
                                                type: 'written_announcement',
                                            },
                                            {
                                                name: '面试公告',
                                                url: getImageUrl('interview_announcement'),
                                                type: 'interview_announcement',
                                            },
                                            {
                                                name: '成绩公告',
                                                url: getImageUrl('score_announcement'),
                                                type: 'score_announcement',
                                            },
                                            {
                                                name: '其他公告',
                                                url: getImageUrl('other_announcement'),
                                                type: 'other_announcement',
                                            },
                                        ]
                                    }
                                } else {
                                    if (moment(record.auditEndDate).endOf('day').valueOf() >= moment().endOf('day').valueOf())
                                        message.warning('请在简历审核日期后发布公告')
                                    else {
                                        editVisible.value = false
                                        detailsVisible.value = false
                                        auditVisible.value = false
                                        selectVisible.value = true
                                        commonList.value = [
                                            {
                                                name: '笔试公告',
                                                url: getImageUrl('written_announcement'),
                                                type: 'written_announcement',
                                            },
                                            {
                                                name: '面试公告',
                                                url: getImageUrl('interview_announcement'),
                                                type: 'interview_announcement',
                                            },
                                            {
                                                name: '成绩公告',
                                                url: getImageUrl('score_announcement'),
                                                type: 'score_announcement',
                                            },
                                            {
                                                name: '其他公告',
                                                url: getImageUrl('other_announcement'),
                                                type: 'other_announcement',
                                            },
                                        ]
                                    }
                                }
                                break
                        }
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const getImageUrl = (name) => {
            return new URL(`../../../assets/${name}.${name.includes('other') ? 'png' : 'svg'}`, import.meta.url).href
        }

        watch(modalViewType, (val, old) => {
            if (val && isOuter.value) {
                switch (val) {
                    case 'written_announcement':
                        noticeVisible.value = true
                        break
                    case 'interview_announcement':
                        noticeVisible.value = true
                        break
                    case 'score_announcement':
                        markVisible.value = true
                        modalTitle.value = '成绩公告'
                        break
                    case 'other_announcement':
                        markVisible.value = true
                        modalTitle.value = '其他公告'
                        break

                    default:
                        break
                }
            }
        })

        const cancelModal = (flag = false) => {
            noticeVisible.value = false
            markVisible.value = false
            editVisible.value = false
            detailsVisible.value = false
            auditVisible.value = false
            selectVisible.value = false
            modalTitle.value = ''
            flag && recruitmentBriefStore().setModalViewType('')
        }

        // 获取详细信息
        const getDetails = (id) => {
            return new Promise((resolve, reject) => {
                request
                    .get('/api/hr-recruitment-brochures/brochure-info', { id: id })
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }

        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // 批量删除
        const batchDelete = (row = { id: '' }) => {
            tableRef.value
                .deleteRow(row.id)
                .then((ref) => {
                    tableRef.value.checkboxReset()
                })
                .catch((err) => {
                    tableRef.value.checkboxReset()
                })
        }
        // 审核
        const approveHandle = (obj) => {
            request
                .post('/api/hr-recruitment-brochures/examine-approve', obj)
                .then((res: inObject) => {
                    message.success(obj.opt ? '已通过审核' : '已拒绝')
                    auditModal.value.resetData()
                    cancelModal()
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // 招聘简章
        const brochuresHandle = (obj, type) => {
            let msg = ''
            switch (type) {
                case 'save':
                    msg = '保存成功'
                    break
                case 'enter':
                    msg = '简章调整成功'
                    break
                case 'submit':
                    msg = '提交成功'
                    break
            }
            if (detailType.value == 'add') {
                request
                    .post('/api/hr-recruitment-brochures/create', obj)
                    .then((res: inObject) => {
                        message.success(msg)
                        cancelModal()
                        tableRef.value.refresh(1)
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                request
                    .put('/api/hr-recruitment-brochures/update', obj)
                    .then((res: inObject) => {
                        message.success(msg)
                        cancelModal()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        // 公告发布
        const noticePublish = (obj, type) => {
            let msg = ''
            switch (type) {
                case 'written_announcement':
                    msg = '笔试公告'
                    break
                case 'interview_announcement':
                    msg = '面试公告'
                    break
                case 'score_announcement':
                    msg = '成绩公告'
                    break
                case 'other_announcement':
                    msg = '其他公告'
                    break
            }
            if (type == 'score_announcement') {
                request
                    .post('/api/hr-recruitment-bulletins/publish-achievement-announcement', {
                        recruitmentStationId: obj.hrRecruitmentStation[0].id,
                        achievementType: obj.achievementType,
                    })
                    .then((res: inObject) => {
                        if (!res) {
                            publishScoreNotice(msg, obj)
                        } else {
                            Modal.confirm({
                                // 该岗位下还有考生未到此节点，是否确认发布
                                title: `还有考生未录入${obj.achievementTypeLabel}，是否确认发布`,
                                icon: h(ExclamationCircleOutlined),
                                onOk() {
                                    publishScoreNotice(msg, obj)
                                },
                                onCancel() {
                                    console.log('Cancel')
                                },
                            })
                        }
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                request
                    .post(
                        type == 'other_announcement'
                            ? '/api/hr-recruitment-bulletins/save'
                            : '/api/hr-recruitment-bulletins/create',
                        obj,
                    )
                    .then((res: inObject) => {
                        message.success(`${msg}公告发布成功`)
                        type == 'other_announcement' ? markModal.value.resetData() : noticeModal.value.resetData()
                        cancelModal(true)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const publishScoreNotice = (msg, obj) => {
            request
                .post('/api/hr-recruitment-brochures/publish-results-announcement', obj)
                .then((res: inObject) => {
                    typeof res == 'string' && message.warning(`${msg}_${obj.achievementTypeLabel}发布成功,但${res}`)
                    typeof res != 'string' && message.success(`${msg}_${obj.achievementTypeLabel}发布成功`)
                    markModal.value.resetData()
                    cancelModal(true)
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 提交
        const brochuresSubmit = (obj) => {
            request
                .get('/api/hr-recruitment-brochures/brochure-submit', { id: obj.id })
                .then((res: inObject) => {
                    message.success('简章提交成功')
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 发布
        const brochuresPublish = (obj) => {
            request
                .post('/api/hr-recruitment-brochures/release-official-website', [obj.id])
                .then((res: inObject) => {
                    message.success('招聘简章发布成功')
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 招聘结束
        const brochuresFinish = (obj) => {
            request
                .get('/api/hr-recruitment-brochures/query-data', { id: obj.id })
                .then((res: inObject) => {
                    if (res) {
                        Modal.confirm({
                            title: '有员工未转到入职，是否确认结束招聘？',
                            icon: h(ExclamationCircleOutlined),
                            onOk() {
                                confirmFinish(obj, res)
                            },
                            onCancel() {
                                console.log('Cancel')
                            },
                        })
                    } else {
                        confirmFinish(obj, res)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const confirmFinish = (obj, opt) => {
            request
                .post('/api/hr-recruitment-brochures/update-recruitment-end', { applyId: obj.id, opt: opt })
                .then((res: inObject) => {
                    message.success('该招聘已成功结束')
                    tableRef.value.refresh()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'recruitmentBrief_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '审核',
                    auth: 'recruitmentBrief_audit',
                    show: (record) => {
                        return record.registerState == 2
                    },
                    click: (record) => showModal(record, 'audit'),
                },
                {
                    neme: '编辑',
                    auth: 'recruitmentBrief_edit',
                    show: (record) => {
                        return record.registerState == 1 || record.registerState == 3 || record.registerState == 4
                    },
                    click: (record) => showModal(record, 'edit'),
                },
                {
                    neme: '发布',
                    auth: 'recruitmentBrief_publish',
                    show: (record) => {
                        return record.registerState == 4
                    },
                    click: (record) => brochuresPublish(record),
                },
                {
                    neme: '公告发布',
                    auth: 'recruitmentBrief_notice_publish',
                    show: (record) => {
                        return record.registerState == 5
                    },
                    click: (record) => showModal(record, 'notice_publish'),
                },
                {
                    neme: '简章调整',
                    auth: 'recruitmentBrief_adjust',
                    show: (record) => {
                        return record.registerState == 5 && record.isChapterAdjustment !== 0
                    },
                    click: (record) => showModal(record, 'adjust'),
                },
                {
                    neme: '提交',
                    auth: 'recruitmentBrief_submit',
                    show: (record) => {
                        return record.registerState == 1
                    },
                    click: (record) => brochuresSubmit(record),
                },
                {
                    neme: '招聘结束',
                    auth: 'recruitmentBrief_finish',
                    show: (record) => {
                        return record.registerState == 5
                    },
                    click: (record) => brochuresFinish(record),
                },
            ]),
        )

        return {
            exportText,
            tableRef,
            editModal,
            auditModal,
            noticeModal,
            markModal,
            columns,
            params,
            options,
            modalTitle,
            editVisible,
            detailsVisible,
            auditVisible,
            selectVisible,
            noticeVisible,
            markVisible,
            currentValue,
            detailType,
            commonList,
            modalValue,
            // 事件
            showModal,
            searchData,
            confirm,

            //操作
            myOperation,
            selectedRowsArr,

            batchExport,
            cancelModal,
            batchDelete,
            approveHandle,
            brochuresHandle,
            noticePublish,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    border: none;
}
.num_lines {
    height: 10px;
    width: 100px;
    border-top: 1px solid #f0f0f0;
    margin-top: 10px;
    position: relative;
    left: -10px;
}
.other_lines {
    height: 10px;
    width: 160px;
    border-top: 1px solid #f0f0f0;
    margin-top: 10px;
    position: relative;
    left: -10px;
}
</style>
