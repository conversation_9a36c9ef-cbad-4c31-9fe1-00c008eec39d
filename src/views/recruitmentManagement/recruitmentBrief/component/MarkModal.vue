<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="1000px">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '100px' } }" :rules="rules" class="form-flex">
            <template v-for="ele in myOptions" :key="ele.name + title">
                <MyFormItem
                    v-if="!ele.external && title?.includes('成绩')"
                    :width="ele.width"
                    :item="ele"
                    v-model:value="formData[ele.name]"
                />
                <template v-else>
                    <template v-if="ele.name == 'applyDate' && inspectVisible">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="applyDate">
                            <DatePicker
                                v-model:value="formData.applyDate[0]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择开始时间"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                style="margin-right: 20px"
                                @change="changeDate('applyDate', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.applyDate[1]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择结束时间"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                :disabled-date="disableDate"
                                @change="changeDate('applyDate', 1)"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'investigationPlace' && inspectVisible">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="investigationPlace">
                            <Input v-model:value="formData.investigationPlace" :placeholder="`请输入${ele.label}`" />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'recruitmentBulletinName'">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="recruitmentBulletinName">
                            <Input v-model:value="formData.recruitmentBulletinName" :placeholder="`请输入${ele.label}`" />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'noticeContent'">
                        <div class="draft_box">
                            <Button @click="showDraftBox" type="link" size="large">
                                <template #icon><RestOutlined /></template>
                                草稿箱
                            </Button>
                        </div>
                        <FormItem
                            :style="'width:' + ele.width"
                            :label="ele.label"
                            name="noticeContent"
                            :rules="{
                                required: title?.includes('其他'),
                                trigger: ['change', 'blur'],
                                type: 'string',
                                message: '请将公告内容设置完整',
                            }"
                        >
                            <WangEditor
                                :value="formData.noticeContent"
                                @on-change="handleChange"
                                ref="editorRef"
                                :editorConfig="editorConfig"
                            />
                        </FormItem>
                    </template>
                    <template v-if="ele.name == 'appendixIdList'">
                        <FormItem label="附件" name="appendixIdList" style="width: 100%">
                            <ImportFile v-model:fileUrls="formData.appendixIdList" ref="refImportFile" />
                        </FormItem>
                    </template>
                </template>
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn" :disabled="publishDisabled">发布</Button>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import moment from 'moment'
import { Moment } from 'moment'
import { ref, defineComponent, toRefs, computed, onMounted, watch, nextTick } from 'vue'
import { RestOutlined } from '@ant-design/icons-vue'

import { valuesAndRules } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { getValuesAndRules } from '/@/utils/index'
import request from '/@/utils/request'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'MarkModal',
    components: { RestOutlined },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { title, visible } = toRefs<any>(props)
        let appendList: inObject = []
        const publishDisabled = ref(false)
        const refImportFile = ref()
        const inspectVisible = ref(false)
        const achievementTypeDisabled = ref(true)
        const achievementTypeList = ref<LabelValueOptions>([])
        const changeAchievementTypeList = ref<LabelValueOptions>([])
        const stationList = ref<LabelValueOptions>([])
        const noticeData = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        const modalViewType = computed(() => {
            return recruitmentBriefStore().getModalViewType
        })
        const noticeContent = computed(() => {
            return recruitmentBriefStore().getDraftContent
        })
        // 表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '岗位名称',
                name: 'stationId',
                width: '40%',
                type: 'select',
                ruleType: 'string',
                options: stationList,
                onChange: (val) => {
                    afterSelectStation(val)
                    nextTick(() => {
                        formData.value.noticeContent = ''
                        editorRef.value.setHtml('')
                    })
                },
            },
            {
                label: '成绩类型',
                name: 'achievementType',
                width: '40%',
                type: 'select',
                ruleType: 'number',
                disabled: achievementTypeDisabled,
                options: changeAchievementTypeList,
                onChange: (val) => {
                    if (!val) inspectVisible.value = false
                    nextTick(() => {
                        formData.value.noticeContent = ''
                        editorRef.value.setHtml('')
                    })
                },
            },
            {
                label: '考察时间',
                name: 'applyDate',
                ruleType: 'array',
                default: [],
                width: '100%',
                external: true,
            },
            {
                label: '考察地点',
                name: 'investigationPlace',
                width: '80%',
                external: true,
            },
            {
                label: '公告名称',
                name: 'recruitmentBulletinName',
                external: true,
                width: '60%',
            },
            {
                label: '公告内容',
                name: 'noticeContent',
                width: '100%',
                external: true,
            },
            {
                label: '附件',
                name: 'appendixIdList',
                width: '100%',
                default: [],
                ruleType: 'array',
                required: false,
                external: true,
            },
        ])
        onMounted(() => {
            // 成绩类型
            dictionaryDataStore()
                .setDictionaryData('achievementType', '')
                .then((data: inObject[]) => {
                    achievementTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        // Form 实例
        const formInline = ref(null) as any
        const editorRef = ref() as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        watch(
            noticeData,
            (val, old) => {
                if (val) {
                    console.log(val)
                    // 岗位
                    request
                        .get('/api/hr-recruitment-brochures/recruitment-station', { id: noticeData.value?.id })
                        .then((data: inObject) => {
                            stationList.value = data.map((item) => {
                                return { label: item.recruitmentStationName, value: item.id, ...item }
                            })
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                }
            },
            {
                deep: true,
                immediate: true,
            },
        )
        watch(
            noticeContent,
            (val, old) => {
                if (val && visible.value) {
                    formData.value.noticeContent = val
                    editorRef.value.setHtml(val)
                }
            },
            {
                immediate: true,
            },
        )
        watch(
            [() => formData.value.achievementType, () => formData.value.stationId],
            ([newType, newId], [oldType, oldId]) => {
                if (newType && newId) {
                    let [list, params] = <[inObject, inObject]>[[], []]
                    if (newId) {
                        let stationObj = stationList.value.find((el) => {
                            return el.value == newId
                        })
                        if (stationObj?.examFormat == 1) {
                            if (newType == 1) inspectVisible.value = true
                            else inspectVisible.value = false
                        }
                        if (stationObj?.examFormat == 2) {
                            if (newType == 2) inspectVisible.value = true
                            else inspectVisible.value = false
                        }
                        if (stationObj?.examFormat == 3 || stationObj?.examFormat == 4) {
                            if (newType == 3) inspectVisible.value = true
                            else inspectVisible.value = false
                        }
                        params = [
                            stationList.value.find((el) => {
                                return el.value == newId
                            }),
                        ]
                    }
                    getAttachment({
                        recruitmentBrochureId: noticeData.value?.id,
                        achievementType: newType,
                        hrRecruitmentStation: params,
                    })
                }
            },
            {
                deep: true,
            },
        )

        const getAttachment = (obj) => {
            request
                .post('/api/hr-recruitment-brochures/return-results-appendix', obj)
                .then((res: inObject) => {
                    publishDisabled.value = false
                    if (res) {
                        formData.value.appendixIdList = res
                        appendList = res
                    } else {
                        formData.value.appendixIdList = []
                        appendList = []
                    }
                })
                .catch((err) => {
                    if (err.response.status == 500) {
                        publishDisabled.value = true
                    }
                })
        }
        const showDraftBox = () => {
            if (title.value.includes('其他')) {
                recruitmentBriefStore().setDraftObj({
                    visible: true,
                    title: title.value,
                    content: '',
                })
            } else {
                let title = ''
                if (!formData.value.achievementType) message.warn('请先选择成绩类型')
                else {
                    switch (formData.value.achievementType) {
                        case 1:
                            title = '笔试成绩'
                            break
                        case 2:
                            title = '面试成绩'
                            break
                        case 3:
                            title = '最终成绩'
                            break
                        case 4:
                            title = '考察成绩'
                            break
                        case 5:
                            title = '体检成绩'
                            break
                    }
                    recruitmentBriefStore().setDraftObj({
                        visible: true,
                        title: title + '公告',
                        content: '',
                    })
                }
            }
        }
        const formValidateOptional = (nameList: string[] | string) => {
            nextTick(() => {
                formInline.value?.validate([nameList])
            })
        }
        const changeDate = (key, index) => {
            formValidateOptional(key)
            if (index == 0) formData.value[key][1] = ''
        }
        const afterSelectStation = (id) => {
            formData.value.achievementType = undefined
            if (id) {
                achievementTypeDisabled.value = false
                let stationObj = stationList.value.find((el) => {
                    return el.value == id
                })
                if (stationObj?.examFormat == 1 || stationObj?.examFormat == 2) {
                    if (stationObj?.examFormat == 1) {
                        changeAchievementTypeList.value = achievementTypeList.value.filter((el) => {
                            return el.value != 2 && el.value != 3
                        })
                    }
                    if (stationObj?.examFormat == 2) {
                        changeAchievementTypeList.value = achievementTypeList.value.filter((el) => {
                            return el.value != 1 && el.value != 3
                        })
                    }
                } else {
                    changeAchievementTypeList.value = achievementTypeList.value
                    formData.value.achievementType = undefined
                }
            } else {
                inspectVisible.value = false
                achievementTypeDisabled.value = true
                formInline.value.resetFields()
            }
        }
        const validateDate = {
            required: true,
            trigger: ['change'],
            type: 'array',
            validator: async (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请将考察时间设置完整')
                } else {
                    if (value?.[0] && value?.[1]) {
                        return Promise.resolve()
                    } else {
                        return Promise.reject('请将考察时间设置完整')
                    }
                }
            },
        }
        const isPlaceRequired = {
            required: true,
            trigger: ['change'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请将考察时间设置完整')
                } else {
                    if (value?.[0] && value?.[1]) {
                        return Promise.resolve()
                    } else {
                        return Promise.reject('请将考察时间设置完整')
                    }
                }
            },
        }

        const disableDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.applyDate[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            return new Date(formData.value?.applyDate[0]).valueOf() >= endValue.clone().endOf('day').valueOf()
        }

        const getAchievementTypeLabel = (type) => {
            return achievementTypeList.value.find((el) => {
                return el.value == type
            })?.label
        }

        const resetData = () => {
            inspectVisible.value = false
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const onCancel = () => {
            emit('cancel', true)
            resetData()
        }

        const onConfirm = () => {
            let [list, params] = <[inObject, inObject]>[[], []]
            if (formData.value.stationId) {
                params = [
                    stationList.value.find((el) => {
                        return el.value == formData.value.stationId
                    }),
                ]
            }
            formInline.value
                .validate()
                .then(async () => {
                    let myUrl = []
                    if (formData.value.appendixIdList?.length) {
                        myUrl = refImportFile.value.getFileUrls().map((item) => {
                            return item.id
                        })
                    }
                    emit(
                        'confirm',
                        {
                            recruitmentBrochureId: noticeData.value?.id,
                            achievementType: formData.value.achievementType,
                            achievementTypeLabel: getAchievementTypeLabel(formData.value.achievementType),
                            investigationStartTime: formData.value.applyDate[0],
                            investigationEndTime: formData.value.applyDate[1],
                            appendixIdList: myUrl,
                            recruitmentBulletinName: formData.value.recruitmentBulletinName,
                            noticeContent: formData.value.noticeContent,
                            investigationPlace: formData.value.investigationPlace,
                            hrRecruitmentStation: params,
                        },
                        modalViewType.value,
                    )
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        const handleChange = (html) => {
            formData.value.noticeContent = html
        }

        return {
            editorRef,
            showDraftBox,
            changeDate,
            formValidateOptional,
            resetData,
            refImportFile,
            rules,
            formData,
            myOptions,
            formInline,
            onConfirm,
            onCancel,
            validateDate,
            inspectVisible,
            disableDate,
            publishDisabled,

            handleChange,
            editorConfig: {
                height: 300,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            getPopupContainer: (triggerNode) => {
                return triggerNode.parentNode
            },
        }
    },
})
</script>
<style lang="less" scoped>
.form-flex {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    .draft_box {
        display: flex;
        width: 100%;
        justify-content: flex-end;
        height: 0;
        position: relative;
        top: -25px;
    }
}
</style>
