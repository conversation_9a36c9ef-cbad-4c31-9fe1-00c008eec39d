<template>
    <div class="wrapper">
        <Form
            :style="{ height: modalViewType == 'written_announcement' ? '450px' : '' }"
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '110px' } }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="ele in myOptions" :key="ele.name + 'noticeForm'">
                <MyFormItem v-if="!ele.external" :width="ele.width" :item="ele" v-model:value="formData[ele.name]" />
                <template v-else>
                    <template v-if="ele.name == 'hrRecruitmentExamRoomList' && modalViewType == 'written_announcement'">
                        <template
                            v-for="(stationItem, ind) in formData.hrRecruitmentExamRoomList"
                            :key="ind + 'hrRecruitmentExamRoomList'"
                        >
                            <FormItem
                                style="width: 100%"
                                :label="ele.label"
                                :name="['hrRecruitmentExamRoomList', ind]"
                                :rules="validateStation"
                                :class="ind == 0 ? '' : 'hide'"
                            >
                                <Input
                                    placeholder="考场名称"
                                    style="width: 200px"
                                    v-model:value="stationItem.examRoomName"
                                    @blur="formValidateOptional(['hrRecruitmentExamRoomList', ind])"
                                />
                                <InputNumber
                                    v-model:value="stationItem.containNum"
                                    placeholder="考场可容纳人数"
                                    style="width: 130px; margin: 0 15px"
                                    :formatter="(value) => limitNumber(value)"
                                    :parser="(value) => limitNumber(value)"
                                    @blur="formValidateOptional(['hrRecruitmentExamRoomList', ind])"
                                />
                                <Input
                                    v-model:value="stationItem.examRoomPlace"
                                    placeholder="考场地点"
                                    style="width: 280px"
                                    @blur="formValidateOptional(['hrRecruitmentExamRoomList', ind])"
                                />

                                <span class="icon-wrapper" v-if="ind == 0">
                                    <PlusCircleOutlined class="dynamic-add-button" @click="addDomain()" />
                                </span>
                                <span class="icon-wrapper" v-else>
                                    <MinusCircleOutlined class="dynamic-delete-button" @click="delDomain(ind)" />
                                </span>
                            </FormItem>
                        </template>
                    </template>
                    <!-- 笔试时间 -->
                    <template v-if="ele.name == 'writtenExamTime' && ele.show">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="writtenExamTime" :rules="validateDate">
                            <DatePicker
                                v-model:value="formData.writtenExamTime[0]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择开始时间"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                style="margin-right: 20px"
                                @change="changeDate('writtenExamTime', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.writtenExamTime[1]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择结束时间"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                @change="changeDate('writtenExamTime', 1)"
                                :disabled-date="disableDate"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <!-- 面试时间 -->
                    <template v-if="ele.name == 'interviewExamTime' && ele.show">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="interviewExamTime" :rules="validateDate">
                            <DatePicker
                                v-model:value="formData.interviewExamTime[0]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择开始日期"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                style="margin-right: 20px"
                                @change="changeDate('interviewExamTime', 0)"
                                :getCalendarContainer="getPopupContainer"
                            />
                            <DatePicker
                                v-model:value="formData.interviewExamTime[1]"
                                format="YYYY-MM-DD HH:mm"
                                :show-time="{ format: 'HH:mm' }"
                                placeholder="请选择结束日期"
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                @change="changeDate('interviewExamTime', 1)"
                                :disabled-date="disableInterviewDate"
                                :getCalendarContainer="getPopupContainer"
                            />
                        </FormItem>
                    </template>
                    <!-- 晋级比例 -->
                    <template v-if="ele.name == 'promotedRatio' && percentageType == 2">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="promotedRatio" style="display: flex">
                            <div>
                                <div style="display: flex">
                                    <InputNumber :defaultValue="1" disabled style="width: 100px" />
                                    <span style="margin: 0 8px">：</span>
                                    <InputNumber
                                        v-model:value="formData.promotedRatio"
                                        :min="0"
                                        :max="100"
                                        style="width: 100px"
                                        :formatter="(value) => limitNumber2(value)"
                                        :parser="(value) => limitNumber2(value)"
                                    />
                                </div>
                                <span class="tips">注：比的前项为“招聘人数”</span>
                            </div>
                        </FormItem>
                    </template>
                    <!-- 考察比例 -->
                    <template v-if="ele.name == 'investigationRatio' && percentageType == 1">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="investigationRatio">
                            <div>
                                <div style="display: flex">
                                    <InputNumber :defaultValue="1" disabled style="width: 100px" />
                                    <span style="margin: 0 8px">：</span>
                                    <InputNumber
                                        v-model:value="formData.investigationRatio"
                                        :min="0"
                                        :max="100"
                                        style="width: 100px"
                                        :formatter="(value) => limitNumber2(value)"
                                        :parser="(value) => limitNumber2(value)"
                                    />
                                </div>
                                <span class="tips">注：比的前项为“招聘人数”</span>
                            </div>
                        </FormItem>
                    </template>
                    <!-- 是否等额考察 -->
                    <template v-if="ele.name == 'isEqualInvestigate' && percentageType == 1">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="isEqualInvestigate">
                            <Switch
                                v-model:checked="formData.isEqualInvestigate"
                                :checked-children="ele.checkText"
                                :un-checked-children="ele.unCheckText ?? '禁用'"
                                :checkedValue="true"
                                :unCheckedValue="false"
                            />
                        </FormItem>
                        <p class="linefeed" v-if="percentageType == 1"></p>
                    </template>
                    <!-- 笔试权重 -->
                    <template v-if="ele.name == 'writtenScoreWeight' && ele.show">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="writtenScoreWeight">
                            <InputNumber
                                v-model:value="formData.writtenScoreWeight"
                                :placeholder="`请输入${ele.label}`"
                                :disabled="ele.disabled"
                                :max="ele.max"
                                :min="ele.min"
                                :formatter="ele.formatter"
                                :parser="ele.parser"
                            />
                        </FormItem>
                    </template>
                    <!-- 面试权重 -->
                    <template v-if="ele.name == 'interviewScoreWeight' && ele.show">
                        <FormItem :style="'width:' + ele.width" :label="ele.label" name="interviewScoreWeight">
                            <InputNumber
                                v-model:value="formData.interviewScoreWeight"
                                :placeholder="`请输入${ele.label}`"
                                :disabled="ele.disabled"
                                :max="ele.max"
                                :min="ele.min"
                                :formatter="ele.formatter"
                                :parser="ele.parser"
                            />
                        </FormItem>
                    </template>
                </template>
            </template>
        </Form>
        <Form
            v-if="modalViewType == 'written_announcement'"
            ref="noticeFormRef"
            :model="noticeFormData"
            :label-col="{ style: { width: '80px' } }"
            :rules="rules2"
            class="notice-form-flex"
        >
            <FormItem :style="'width:' + noticeOptions[0].width" label="考试须知" name="examNotice" :rules="validateExamNotice">
                <div class="draft_box">
                    <Button @click="showDraftBox" type="link" size="large">
                        <template #icon><RestOutlined /></template>
                        草稿箱
                    </Button>
                </div>
                <WangEditor
                    class="my_editor"
                    height="660px"
                    width="421px"
                    :value="noticeFormData.examNotice"
                    @on-change="handleChange"
                    ref="editorRef"
                    :editorConfig="examEditorConfig"
                />
            </FormItem>
        </Form>
    </div>
</template>
<script lang="ts">
import { times, minus } from 'number-precision'
import { ref, defineComponent, toRefs, watch, computed, nextTick } from 'vue'
import { MinusCircleOutlined, PlusCircleOutlined, RestOutlined } from '@ant-design/icons-vue'
import { valuesAndRules } from '/#/component'
import { getValuesAndRules } from '/@/utils/index'
import request from '/@/utils/request'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { Moment } from 'moment'
import moment from 'moment'
import { notification } from 'ant-design-vue'
export default defineComponent({
    name: 'NoticeForm',
    components: { MinusCircleOutlined, PlusCircleOutlined, RestOutlined },
    props: {
        isAdjust: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['change'],
    setup(props, { emit }) {
        const { isAdjust } = toRefs<any>(props)
        const editorRef = ref(null) as any
        let lastNotice = ''
        const modalViewType = computed(() => {
            return recruitmentBriefStore().getModalViewType
        })
        const noticeData = computed(() => {
            return recruitmentBriefStore().getNoticeData
        })
        // 当前编辑的数据
        const currentValue = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        const examModeList = computed(() => {
            return recruitmentBriefStore().getExamModeList
        })
        const noticeContent = computed(() => {
            return recruitmentBriefStore().getDraftContent
        })
        const draftTitle = computed(() => {
            return recruitmentBriefStore().getDraftTitle
        })
        const stationList = computed(() => {
            if (noticeData.value?.hrRecruitmentStation && noticeData.value?.hrRecruitmentStation.length > 0) {
                return noticeData.value.hrRecruitmentStation.map((el) => {
                    return { label: el.recruitmentStationName, value: el.recruitmentStationId, ...el }
                })
            } else return []
        })
        const currentItem = ref<any>(null)
        const writtenScoreWeightDisabled = computed(() => {
            if (isAdjust.value) {
                if (currentItem.value?.examFormat == 4) {
                    let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                        return el.recruitmentStationId == currentItem.value.recruitmentStationId
                    })
                    if (tempObj?.interviewScoreWeight) return true
                    else return false
                } else return true
            } else {
                if (currentItem.value?.examFormat == 1) return true
                else if (currentItem.value?.examFormat == 3) {
                    let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                        return el.recruitmentStationId == currentItem.value.recruitmentStationId
                    })
                    if (tempObj?.interviewScoreWeight) return true
                    else return false
                } else return false
            }
        })
        const interviewScoreWeightDisabled = computed(() => {
            if (isAdjust.value) {
                if (currentItem.value?.examFormat == 3) {
                    let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                        return el.recruitmentStationId == currentItem.value.recruitmentStationId
                    })
                    if (tempObj?.writtenScoreWeight) return true
                    else return false
                } else return true
            } else {
                if (currentItem.value?.examFormat == 2) return true
                else if (currentItem.value?.examFormat == 4) {
                    let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                        return el.recruitmentStationId == currentItem.value.recruitmentStationId
                    })
                    if (tempObj?.writtenScoreWeight) return true
                    else return false
                } else return false
            }
        })
        // 1笔试 2面试 3先面试后笔试 4先笔试后面试
        // 1=>考察比例 2=>晋级比例
        const percentageType = ref(0)
        // 表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '岗位名称',
                name: 'recruitmentStationId',
                width: '50%',
                type: 'select',
                options: stationList,
                required: true,
                disabled: isAdjust,
                onChange: (val) => {
                    emit('change')
                    if (val) {
                        currentItem.value = stationList.value.find((el) => {
                            return el.recruitmentStationId == val
                        })
                        formData.value = Object.assign({}, initFormData)
                        formData.value.recruitmentStationId = val
                        if (currentItem.value?.examFormat == 1) formData.value.writtenScoreWeight = 100
                        else if (currentItem.value?.examFormat == 2) formData.value.interviewScoreWeight = 100
                        else if (currentItem.value?.examFormat == 3) {
                            let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                                return el.recruitmentStationId == val
                            })
                            if (tempObj?.interviewScoreWeight)
                                formData.value.writtenScoreWeight = minus(100, times(Number(tempObj.interviewScoreWeight), 100))
                            else {
                                formData.value.interviewScoreWeight = undefined
                                formData.value.writtenScoreWeight = undefined
                            }
                        } else {
                            let tempObj = currentValue.value?.hrRecruitmentStation?.find((el) => {
                                return el.recruitmentStationId == val
                            })
                            if (tempObj?.writtenScoreWeight)
                                formData.value.interviewScoreWeight = minus(100, times(Number(tempObj.writtenScoreWeight), 100))
                            else {
                                formData.value.writtenScoreWeight = undefined
                                formData.value.interviewScoreWeight = undefined
                            }
                        }
                        formData.value.examFormatName = currentItem.value.examFormatName
                    } else {
                        formData.value = Object.assign({}, initFormData)
                        currentItem.value = null
                        formData.value.examFormatName = ''
                    }
                    formInline.value.resetFields()
                    nextTick(() => {
                        noticeFormData.value.examNotice = ''
                        if (modalViewType.value == 'written_announcement') {
                            editorRef.value.setHtml('')
                        }
                    })
                    formData.value.hrRecruitmentExamRoomList = [
                        {
                            examRoomName: undefined,
                            containNum: undefined,
                            examRoomPlace: undefined,
                        },
                    ]
                    formData.value.writtenExamTime = []
                    formData.value.interviewExamTime = []
                },
            },
            {
                label: '考试类型',
                name: 'examFormatName',
                width: '50%',
                disabled: true,
                required: false,
            },
            {
                label: '笔试时间',
                name: 'writtenExamTime',
                ruleType: 'array',
                default: [],
                width: '100%',
                external: true,
                show: modalViewType.value == 'written_announcement',
            },
            {
                label: '面试时间',
                name: 'interviewExamTime',
                ruleType: 'array',
                default: [],
                width: '100%',
                external: true,
                show: modalViewType.value == 'interview_announcement',
            },
            {
                label: '考场',
                name: 'hrRecruitmentExamRoomList',
                ruleType: 'array',
                required: true,
                default: [
                    {
                        examRoomName: undefined,
                        containNum: undefined,
                        examRoomPlace: undefined,
                    },
                ],
                width: '100%',
                external: true,
            },
            {
                label: '面试地点',
                name: 'interviewLocation',
                width: '100%',
                show: modalViewType.value == 'interview_announcement',
            },
            {
                label: '晋级比例',
                name: 'promotedRatio',
                ruleType: 'number',
                width: '100%',
                external: true,
            },
            {
                label: '考察比例',
                name: 'investigationRatio',
                ruleType: 'number',
                width: '50%',
                external: true,
            },
            {
                label: '是否等额考察',
                name: 'isEqualInvestigate',
                width: '50%',
                type: 'switch',
                ruleType: 'boolean',
                external: true,
                default: false,
                checkText: '是',
                unCheckText: '否',
            },
            {
                label: '及格线',
                name: 'writtenPassLine',
                width: '50%',
                type: 'number',
                ruleType: 'number',
                show: modalViewType.value == 'written_announcement',
                showbr: true,
                min: 0,
                max: 100,
                parser: (value) => value.replace('分', ''),
                formatter: (value) => {
                    if (typeof value === 'string') {
                        return !isNaN(Number(value)) ? `${value}分` : ''
                    } else if (typeof value === 'number') {
                        return !isNaN(value) ? `${String(value)}分` : ''
                    } else {
                        return ''
                    }
                },
            },
            {
                label: '及格线',
                name: 'interviewPassLine',
                width: '30%',
                type: 'number',
                ruleType: 'number',
                show: modalViewType.value == 'interview_announcement',
                min: 0,
                max: 100,
                parser: (value) => value.replace('分', ''),
                formatter: (value) => {
                    if (typeof value === 'string') {
                        return !isNaN(Number(value)) ? `${value}分` : ''
                    } else if (typeof value === 'number') {
                        return !isNaN(value) ? `${String(value)}分` : ''
                    } else {
                        return ''
                    }
                },
            },
            {
                label: '成绩权重',
                name: 'writtenScoreWeight',
                width: '50%',
                type: 'number',
                ruleType: 'number',
                show: modalViewType.value == 'written_announcement',
                disabled: writtenScoreWeightDisabled,
                min: 0,
                max: 100,
                external: true,
                parser: (value) => value.replace('%', ''),
                formatter: (value) => {
                    if (typeof value === 'string') {
                        return !isNaN(Number(value)) ? `${value}%` : ''
                    } else if (typeof value === 'number') {
                        return !isNaN(value) ? `${String(value)}%` : ''
                    } else {
                        return ''
                    }
                },
            },
            {
                label: '成绩权重',
                name: 'interviewScoreWeight',
                width: '30%',
                type: 'number',
                ruleType: 'number',
                show: modalViewType.value == 'interview_announcement',
                min: 0,
                max: 100,
                external: true,
                disabled: interviewScoreWeightDisabled,
                parser: (value) => value.replace('%', ''),
                formatter: (value) => {
                    if (typeof value === 'string') {
                        return !isNaN(Number(value)) ? `${value}%` : ''
                    } else if (typeof value === 'number') {
                        return !isNaN(value) ? `${String(value)}%` : ''
                    } else {
                        return ''
                    }
                },
            },
        ])
        const noticeOptions = ref<valuesAndRules[]>([
            {
                label: '考试须知',
                name: 'examNotice',
                width: '100%',
            },
        ])
        // Form 实例
        const formInline = ref(null) as any
        const noticeFormRef = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        const { values: initFormData2, rules: rules2 } = getValuesAndRules(noticeOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        const noticeFormData = ref<any>(initFormData2)
        watch(
            currentItem,
            (val, old) => {
                if (val) {
                    if (isAdjust.value) {
                        formData.value = Object.assign({}, initFormData, val)
                        noticeFormData.value.examNotice = val.examNotice
                        formData.value.writtenExamTime = [val?.writtenExamStartTime, val?.writtenExamStartTime]
                        formData.value.interviewExamTime = [val?.interviewExamStartTime, val?.interviewExamEndTime]
                        if (!val?.hrRecruitmentExamRoomList?.length)
                            formData.value.hrRecruitmentExamRoomList = [
                                {
                                    examRoomName: undefined,
                                    containNum: undefined,
                                    examRoomPlace: undefined,
                                },
                            ]
                    }
                    formData.value.recruitmentStationName = val?.recruitmentStationName
                    formData.value.examFormatName = val?.examFormatName
                }
            },
            { immediate: true, deep: true },
        )
        watch(
            () => currentItem.value?.examFormat,
            (val, old) => {
                if (val) {
                    if (val == 1 || val == 2) percentageType.value = 1
                    else if (val == 3) {
                        if (modalViewType.value == 'written_announcement') percentageType.value = 1
                        else percentageType.value = 2
                    } else {
                        if (modalViewType.value == 'written_announcement') percentageType.value = 2
                        else percentageType.value = 1
                    }
                }
            },
        )
        watch(
            () => noticeData.value,
            (val, old) => {
                if (val && isAdjust.value) {
                    currentItem.value = val.hrRecruitmentStation[0]
                }
            },
            {
                deep: true,
                immediate: true,
            },
        )
        watch(
            [noticeContent, draftTitle],
            ([newContent, newTitle], [oldContent, oldTitle]) => {
                if (newContent && newTitle == '考试须知') {
                    noticeFormData.value.examNotice = newContent
                    editorRef.value.setHtml(newContent)
                }
            },
            {
                immediate: true,
            },
        )
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const formValidateOptional = (nameList: string[] | string, isNotice = false) => {
            nextTick(() => {
                !isNotice && formInline.value?.validate([nameList])
                isNotice && noticeFormRef.value?.validate([nameList])
            })
        }
        const showDraftBox = () => {
            recruitmentBriefStore().setDraftObj({
                visible: true,
                title: '考试须知',
                content: '',
            })
        }
        const validateStation = {
            required: true,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.hrRecruitmentExamRoomList[rule.field.split('.')[1]]
                // 校验考场名称是否唯一
                if (formDataItem?.examRoomName) {
                    if (!formDataItem?.containNum || !formDataItem?.examRoomPlace) {
                        return Promise.reject('请将考场信息设置完整')
                    } else {
                        let count = 0
                        formData.value.hrRecruitmentExamRoomList.forEach((el) => {
                            if (el.examRoomName == formDataItem.examRoomName) count++
                        })
                        if (count > 1) {
                            return Promise.reject('考场名称不能重复')
                        } else {
                            await request
                                .post(
                                    '/api/hr-recruitment-brochures/exam-room-query-data',
                                    {
                                        recruitmentStationId: currentItem.value.recruitmentStationId,
                                        examRoomName: formDataItem.examRoomName,
                                    },
                                    { loading: false },
                                )
                                .then((res) => {
                                    if (res != '操作成功') return Promise.reject(res)
                                    else return Promise.resolve()
                                })
                                .catch((err) => {
                                    console.log(err)
                                    return Promise.reject(err || err.message)
                                })
                        }
                    }
                } else {
                    return Promise.reject('请将考场信息设置完整')
                }
            },
        }
        const validateDate = {
            required: true,
            trigger: ['change'],
            type: 'array',
            validator: async (rule: inObject, value: any) => {
                if (value[0] && value[1]) {
                    return Promise.resolve()
                } else {
                    return Promise.reject('请将考试时间设置完整')
                }
            },
        }
        const changeDate = (key, index) => {
            formValidateOptional(key)
            if (index == 0) formData.value[key][1] = ''
        }
        const validateExamNotice = {
            required: true,
            trigger: ['change'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                if (!value) {
                    return Promise.reject('请将考试须知设置完整')
                } else {
                    return Promise.resolve()
                }
            },
        }

        const disableDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.writtenExamTime[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            return new Date(formData.value?.writtenExamTime[0]).valueOf() >= endValue.clone().endOf('day').valueOf()
        }
        const disableInterviewDate = (endValue: Moment) => {
            if (!endValue || !formData.value?.interviewExamTime[0]) {
                return endValue && endValue < moment().startOf('day')
            }
            return new Date(formData.value?.interviewExamTime[0]).valueOf() >= endValue.clone().endOf('day').valueOf()
        }

        const handleChange = (html) => {
            let dom: inObject | null = document.querySelector('.w-e-text')
            if (dom?.scrollHeight > dom?.clientHeight) {
                nextTick(() => {
                    if (lastNotice != html) editorRef.value.setHtml(lastNotice)
                })
                notification.warning({
                    message: '考试须知已超出限定字数',
                })
            } else {
                lastNotice = html
            }
            noticeFormData.value.examNotice = html
            formValidateOptional('examNotice', true)
        }

        const addDomain = () => {
            formData.value['hrRecruitmentExamRoomList'].push({
                examRoomName: undefined,
                containNum: undefined,
                examRoomPlace: undefined,
            })
        }
        const delDomain = (ind) => {
            formData.value['hrRecruitmentExamRoomList'].splice(ind, 1)
        }
        /* 限制数字输入框只能输入整数 */
        const limitNumber = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : ''
            } else {
                return ''
            }
        }
        /* 限制数字输入框只能输入 > 0的数 */
        const limitNumber2 = (value) => {
            if (typeof value === 'string') {
                return !isNaN(Number(value)) ? value.replace(/^(0{2,})|[^\d.]/g, '') : ''
            } else if (typeof value === 'number') {
                return !isNaN(value) ? String(value).replace(/^(0{2,})|[^\d.]/g, '') : ''
            } else {
                return ''
            }
        }

        const onFormSubmit = () => {
            return new Promise((resolve, reject) => {
                if (modalViewType.value == 'written_announcement') {
                    formInline.value
                        .validate()
                        .then(async () => {
                            if (!noticeFormData.value.examNotice) {
                                reject({ type: 1, msg: '请将考试须知填写完整！' })
                                return
                            } else {
                                const sum = formData.value.hrRecruitmentExamRoomList.reduce((pre, cur) => {
                                    return (pre += Number(cur.containNum))
                                }, 0)
                                if (currentItem.value.paymentNum > sum) {
                                    reject({ type: 2, msg: '当前考场不能容纳下所有考生，请重新编辑！' })
                                    return
                                }
                                let dom: inObject | null = document.querySelector('.w-e-text')
                                if (dom?.scrollHeight > dom?.clientHeight) {
                                    reject({ type: 2, msg: '当前考试须知在准考证中将不能正常显示，请重新编辑！' })
                                    return
                                }
                                let admissionTicketStr =
                                    '<div style="width: 842px; height: 595px; box-sizing: border-box; border: 1px solid #333; padding: 10px"><div style="width: 100%; position: relative; height: 100%; box-sizing: border-box; border: 1px solid #333"><div style="display: flex"><div style="width: 50%; height: 100%; box-sizing: border-box; padding: 20px"><div style="font-size: 20px; font-weight: bold; text-align: center; margin-bottom: 40px">准考证</div><div style="margin-bottom: 10px"><span>姓名：${name}</span></div><div style="margin-bottom: 10px"><span>性别：${sex}</span></div><div style="margin-bottom: 10px"><span>身份证号：${idNumber}</span></div><div style="margin-bottom: 10px;"><span>准考证号：${admissionNumber}</span><span style="margin-left: 40px"><span>考场：${examRoom}</span></span></div><div style="margin-bottom: 10px"><span>报考岗位：${postRegistration}</span></div><div style="margin-bottom: 10px"><span>考试地点：${examRoomPlace}</span></div><div style="margin-bottom: 10px"><span>考试时间：${examTime}</span></div></div><div style="width: 50%; height: 100%; box-sizing: border-box; padding: 20px"><div style="font-size: 20px; font-weight: bold; text-align: center; margin-bottom: 10px;">考生须知</div><div style="overflow: hidden; height:465px">'
                                let examNotice = `${noticeFormData.value.examNotice}</div></div></div><div style="width: 100%; position: absolute; bottom: 20px; text-align: center; margin-top: 20px"><span style="padding-right: 20px">青岛市黄岛区人力资源有限公司</span><span>制</span></div></div></div>`
                                admissionTicketStr += examNotice
                                resolve({
                                    id: currentItem.value.id,
                                    ...formData.value,
                                    admissionTicket: admissionTicketStr,
                                    examNotice: noticeFormData.value.examNotice,
                                    writtenExamStartTime: formData.value.writtenExamTime[0],
                                    writtenExamEndTime: formData.value.writtenExamTime[1],
                                    interviewExamStartTime: formData.value.interviewExamTime[0],
                                    interviewExamEndTime: formData.value.interviewExamTime[1],
                                })
                            }
                        })
                        .catch((err) => {
                            reject({ type: 1, msg: '请将必填信息填写完整！' })
                            console.log(`表单验证失败${err}`)
                        })
                } else {
                    formInline.value
                        .validate()
                        .then(async () => {
                            resolve({
                                id: currentItem.value.id,
                                ...formData.value,
                                interviewExamStartTime: formData.value.interviewExamTime[0],
                                interviewExamEndTime: formData.value.interviewExamTime[1],
                            })
                        })
                        .catch((err) => {
                            reject({ type: 1, msg: '请将必填信息填写完整！' })
                            reject('2')
                            console.log(`表单验证失败${err}`)
                        })
                }
            })
        }

        return {
            showDraftBox,
            currentItem,
            changeDate,
            editorRef,
            resetData,
            modalViewType,
            rules,
            rules2,
            formData,
            noticeFormData,
            myOptions,
            noticeOptions,
            noticeFormRef,
            formInline,
            handleChange,
            examEditorConfig: {
                height: 595,
                menus: [
                    'head',
                    'bold',
                    'fontSize',
                    'fontName',
                    'italic',
                    'underline',
                    'strikeThrough',
                    'indent',
                    'list',
                    'todo',
                    'justify',
                    'quote',
                    'table',
                    'splitLine',
                    'undo',
                    'redo',
                ],
                placeholder: '',
                showLinkImg: false,
                showFullScreen: false,
                pasteIgnoreImg: true,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/(<\/?a.*?>)|(<\/?span.*?>)/g, '')
                },
            },
            stationList,
            examModeList,
            addDomain,
            delDomain,
            limitNumber,
            limitNumber2,
            formValidateOptional,
            validateStation,
            validateDate,
            validateExamNotice,
            disableDate,
            disableInterviewDate,
            percentageType,
            onFormSubmit,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style lang="less" scoped>
.my_editor {
    :deep(.w-e-text) {
        overflow: hidden !important;
        p {
            margin: 0;
            line-height: 1.15 !important;
        }
    }
}
.linefeed {
    width: 100%;
    padding: 0;
    margin: 0;
}
.wrapper {
    display: flex;
    width: 100%;
}
.notice-form-flex {
    display: flex;
    .draft_box {
        display: flex;
        width: 100%;
        height: 0;
        justify-content: flex-end;
        position: relative;
        top: -30px;
    }
}
.form-flex {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    left: -30px;
    padding-right: 0;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    .hide {
        :deep(.ant-form-item-required) {
            display: none;
        }
        :deep(.ant-col.ant-form-item-label) {
            label {
                color: rgba(0, 0, 0, 0) !important;
            }
        }
    }
    .tips {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
        font-weight: bold;
    }
    .icon-wrapper {
        width: 20px;
        margin-left: 10px;
        display: inline-block;
    }
    .dynamic-add-button {
        cursor: pointer;
        position: relative;
        top: 4px;
        font-size: 20px;
        color: #999;
        transition: all 0.3s;
        color: @primary-color;
    }
    .dynamic-add-button:hover {
        color: @primary-color;
    }
    .dynamic-add-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
    .dynamic-delete-button {
        cursor: pointer;
        position: relative;
        top: 4px;
        font-size: 20px;
        color: #999;
        transition: all 0.3s;

        color: @dangerous-color;
    }
    .dynamic-delete-button:hover {
        color: @dangerous-color;
    }
    .dynamic-delete-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
}
</style>
