<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="1200px">
        <div class="examine" style="margin-top: 20px">
            <Divider type="vertical" class="divid" />
            <span class="title">简章信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">简章名称：</span>
                    <span>{{ detailData?.recruitBrochureName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex" style="width: 100%">
                    <span class="label">简章内容：</span>
                    <div class="content">
                        <WangEditor
                            :value="detailData?.detailContent"
                            @on-change="handleChange"
                            ref="editorRef"
                            :editorConfig="editorConfig"
                        />
                    </div>
                </div>
            </div>
        </div>
        <OperationInfo
            v-if="detailData?.applyOpLogsList && detailData?.applyOpLogsList?.length"
            :applyOpLogs="detailData?.applyOpLogsList"
        />
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span class="title">操作</span>
            <Form ref="formInline" :model="formData" :label-col="{ style: { width: '120px' } }" :rules="rules">
                <FormItem label="拒绝原因" name="checkerReason" style="width: 100%">
                    <Textarea v-model:value="formData.checkerReason" :rows="3" allowClear placeholder="拒绝原因" />
                </FormItem>
            </Form>
        </div>
        <template #footer>
            <Button @click="onApprove(false)" class="delBtn">拒绝</Button>
            <Button @click="onApprove(true)" type="primary" class="btn">通过</Button>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, computed, watch, nextTick } from 'vue'
import { valuesAndRules } from '/#/component'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { getValuesAndRules, isEmpty } from '/@/utils'
import OperationInfo from '/@/views/serviceCentre/retirement/component/OperationInfo.vue'
export default defineComponent({
    name: 'AuditModal',
    components: {
        OperationInfo,
    },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { visible } = toRefs<any>(props)
        const editorRef = ref()
        const detailData = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '拒绝原因',
                name: 'checkerReason',
                required: false,
            },
        ])
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const onCancel = () => {
            emit('cancel')
            resetData()
        }
        const onApprove = (opt) => {
            if (!opt && isEmpty(formData.value.checkerReason)) {
                message.warn('请输入拒绝原因')
            } else {
                emit('confirm', { applyIdList: [detailData.value?.id], opt: opt, ...formData.value })
            }
        }
        watch(visible, (val) => {
            nextTick(() => {
                if (val) editorRef.value.disable()
            })
        })
        const handleChange = (html) => {
            // console.log(html)
        }

        return {
            editorRef,
            handleChange,
            editorConfig: {
                height: 300,
                menus: [],
                showFullScreen: false,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            onCancel,
            onApprove,
            rules,
            formData,
            formInline,
            detailData,
            resetData,
        }
    },
})
</script>
<style scoped lang="less">
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .title {
        font-weight: bold;
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .item-flex-detail {
            width: 100%;
            margin: 5px 0px;
            display: flex;
            .details {
                display: flex;
                flex-direction: column;
                & span > span {
                    margin-right: 25px;
                }
            }
        }
        .item-flex {
            margin: 5px 0px;
            display: flex;
        }
        .label {
            text-align: right;
            width: 120px;
            color: rgba(153, 153, 153, 1);
        }
        .content {
            width: calc(100% - 120px);
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
</style>
