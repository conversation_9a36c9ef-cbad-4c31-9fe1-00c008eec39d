<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="600px">
        <template v-for="(item, index) in commonList" :key="item.name">
            <div
                class="wrapper"
                @click="goToShowModal(item)"
                @mouseover="mouseEvent(index)"
                :class="[activeIdx == index ? 'focusBg' : '', viewType == 'export' && item.checked ? 'checkedBorder' : '']"
                @mouseleave="mouseEvent"
            >
                <div class="left_icon">
                    <img :src="item.url" alt="" />
                </div>
                <div class="right_box">
                    <div class="text">{{ item.name }}</div>
                    <div style="width: 50px" v-if="viewType == 'export'">
                        <Checkbox v-model:checked="item.checked" />
                    </div>
                    <div style="width: 50px" v-else>
                        <ArrowRightOutlined class="imgicon" />
                    </div>
                </div>
            </div>
        </template>
        <template #footer>
            <div v-if="viewType == 'export'">
                <Button @click="onCancel" class="btn">取消</Button>
                <Button @click="onConfirm" type="primary" :loading="btnLoading" class="btn">确定</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ArrowRightOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, defineComponent, ref, toRefs } from 'vue'
import recruitmentBriefStore from '/@/store/modules/recruitmentBrief'
import { openNotification } from '/@/utils'
import request from '/@/utils/request'

export default defineComponent({
    name: 'SelectCommon',
    components: { ArrowRightOutlined },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        btnLoading: {
            type: Boolean,
            default: false,
        },
        commonList: {
            type: Array,
            default: () => [],
        },
        viewType: {
            type: String,
            default: '',
        },
    },
    emits: ['confirm', 'cancel', 'update:commonList'],
    setup(props, { emit }) {
        const { viewType, commonList } = toRefs(props)
        const activeIdx = ref(null)
        // 当前编辑的数据
        const currentValue = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })
        const goToShowModal = (item) => {
            if (viewType.value !== 'export') {
                if (item.type == 'written_announcement' || item.type == 'interview_announcement') {
                    getDetails(item.type == 'written_announcement' ? 1 : 2)
                        .then((res) => {
                            recruitmentBriefStore().setNoticeData(res)
                            if (judgeIsSingle()) {
                                if (!getAnnouncementVisible(currentValue.value, item.type == 'written_announcement' ? 2 : 1)) {
                                    openNotification(
                                        `该招聘简章所有招聘岗位不包含${item.name.substr(0, 2)}环节，无法发布${item.name}`,
                                        '提示',
                                    )
                                    return
                                }
                                if (!getAnnouncementVisible(res, item.type == 'written_announcement' ? 2 : 1)) {
                                    openNotification(
                                        `该招聘简章所有招聘岗位的${item.name}已发布或未进行到${item.name.substr(0, 2)}环节，
                                        无法发布${item.name}`,
                                        '提示',
                                    )
                                    return
                                }
                            } else {
                                if (!getAnnouncementVisible(res, item.type == 'written_announcement' ? 2 : 1)) {
                                    openNotification(
                                        `该招聘简章所有招聘岗位的${item.name}已发布或未进行到${item.name.substr(0, 2)}环节，
                                        无法发布${item.name}`,
                                        '提示',
                                    )
                                    return
                                }
                            }
                            recruitmentBriefStore().setModalViewType(item.type)
                            onCancel()
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                } else {
                    recruitmentBriefStore().setModalViewType(item.type)
                    onCancel()
                }
            } else {
                item.checked = !item.checked
                emit('update:commonList', commonList.value)
            }
        }
        // 弹框关闭
        const onCancel = () => {
            if (viewType.value == 'export') {
                recruitmentBriefStore().setExportList([])
                emit('cancel', true)
            } else {
                emit('cancel')
            }
        }
        const onConfirm = () => {
            emit('confirm')
            let checkedArr = commonList.value
                .filter((el: any) => {
                    return el.checked
                })
                .map((el: any) => {
                    return el.type
                })
            if (!checkedArr.length) {
                message.warning('请选择要导出的内容!')
                return
            } else {
                recruitmentBriefStore().setExportList(checkedArr)
            }
        }
        // 判读是否只有面试或者笔试
        const judgeIsSingle = () => {
            return currentValue.value?.hrRecruitmentStation.every((el) => {
                return el.examFormat == 1 || el.examFormat == 2
            })
        }
        const getAnnouncementVisible = (noticeData, examType) => {
            if (!noticeData.hrRecruitmentStation?.length) return false
            return !noticeData.hrRecruitmentStation.every((el) => {
                return el.examFormat == examType
            })
        }
        const getDetails = (type) => {
            return new Promise((resolve, reject) => {
                request
                    .get('/api/hr-recruitment-brochures/matching-recruitment-stations', {
                        recruitmentBrochureId: currentValue.value?.id,
                        noticeType: type,
                    })
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        console.log(err)
                        reject(err)
                    })
            })
        }
        const mouseEvent = (idx = null) => {
            activeIdx.value = idx
        }
        return {
            activeIdx,
            onCancel,
            goToShowModal,
            mouseEvent,
            onConfirm,
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid #eef2ff;
    width: 100%;
    height: 70px;
    margin: 15px 0;
    border-radius: 10px;
    cursor: pointer;
    .right_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        .text {
            font-size: 17px;
        }
        .imgicon {
            color: #6894fe;
            font-size: 20px;
        }
    }
    .left_icon {
        display: flex;
        align-items: center;
        margin: 0 15px;
        height: 48px;
        width: 48px;
        text-align: center;
        font-size: 30px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
.focusBg {
    background-color: rgba(245, 245, 245, 0.85);
}
.checkedBorder {
    border: 1px solid @primary-color;
}
</style>
