<template>
    <BasicEditModalSlot :visible="draftVisible" @cancel="onCancel" title="草稿箱" width="800px" :zIndex="1009">
        <div class="wrapper">
            <div class="header">{{ draftTitle }}</div>
            <div class="content">
                <WangEditor :value="draftContent" @on-change="handleChange" ref="editorRef" :editorConfig="editorConfig" />
            </div>
        </div>
        <template #footer>
            <div>
                <Button @click="copyContent" class="copyBtn">
                    <template #icon><CopyOutlined /></template>复制
                </Button>
                <Button @click="saveContent" type="primary" class="btn">保存</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, watch, computed, nextTick } from 'vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import recruitmentBriefStore from '/@/store/modules/recruitmentBrief'
import request from '/@/utils/request'
export default defineComponent({
    name: 'DraftBox',
    components: { CopyOutlined },
    props: {},
    emits: ['copy'],
    setup(props, { emit }) {
        const draftVisible = computed(() => {
            return recruitmentBriefStore().getDraftVisible
        })
        const draftTitle = computed(() => {
            return recruitmentBriefStore().getDraftTitle
        })
        const draftContent = ref('')
        const editorRef = ref()
        watch(draftVisible, (val) => {
            if (val) {
                fetchNoticeContent()
            }
        })
        const handleChange = (html, text) => {
            draftContent.value = html
        }
        const DRAFT_OBJ = {
            笔试成绩公告: 1,
            面试成绩公告: 2,
            最终成绩公告: 3,
            考察成绩公告: 4,
            体检成绩公告: 5,
            笔试公告: 6,
            面试公告: 7,
            考试须知: 8,
            其他公告: 9,
        }
        const fetchNoticeContent = () => {
            request
                .get('/api/hr-recruitment-draftses/latest-content', {
                    draftType: DRAFT_OBJ[draftTitle.value as any],
                })
                .then((res) => {
                    editorRef.value.setHtml(res?.draftContent)
                    draftContent.value = res?.draftContent
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // cancel handle
        const onCancel = (flag = false) => {
            recruitmentBriefStore().setDraftObj({
                visible: false,
                title: draftTitle.value,
                content: flag ? editorRef.value.getEditorContent('html') : '',
            })
            nextTick(() => {
                draftContent.value = ''
                editorRef.value?.setHtml('')
            })
        }

        const copyContent = () => {
            onCancel(true)
        }

        const saveContent = () => {
            request
                .post('/api/hr-recruitment-draftses/preservation', {
                    draftType: DRAFT_OBJ[draftTitle.value as any],
                    draftContent: draftContent.value,
                })
                .then((res: inObject) => {
                    console.log(res)
                    copyContent()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        return {
            onCancel,
            handleChange,
            copyContent,
            saveContent,

            draftTitle,
            draftVisible,
            draftContent,
            editorRef,
            editorConfig: {
                height: 550,
                menus: [
                    'head',
                    'bold',
                    'fontSize',
                    'fontName',
                    'italic',
                    'underline',
                    'strikeThrough',
                    'indent',
                    'list',
                    'todo',
                    'justify',
                    'quote',
                    'table',
                    'splitLine',
                    'undo',
                    'redo',
                ],
                showFullScreen: false,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    width: 100%;
    .header {
        text-align: center;
        font-size: 16px;
        font-weight: bold;
    }
    .content {
    }
}
.copyBtn {
    color: #fff;
    background-color: @success-color;
    border: none;
}
</style>
