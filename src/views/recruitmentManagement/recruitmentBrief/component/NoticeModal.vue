<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" @confirm="onConfirm" :title="modalTitle" width="1300px">
        <!-- 头部 -->
        <Form ref="headFormInline" :model="formData" class="form-flex" :label-col="{ style: { width: '120px' } }">
            <FormItem label="招聘简章名称" name="recruitBrochureName" style="width: 30%">
                <Input
                    style="width: 200px"
                    v-model:value="formData.recruitBrochureName"
                    placeholder="暂无招聘简章名称"
                    disabled
                />
            </FormItem>
            <FormItem label="客户名称" name="clientName" style="width: 30%">
                <Input style="width: 200px" v-model:value="formData.clientName" placeholder="暂无客户名称" disabled />
            </FormItem>
        </Form>

        <!-- 岗位 -->
        <div style="margin: 30px 20px">
            <Divider type="vertical" class="divid" />
            <span class="title">岗位</span>
        </div>
        <NoticeForm :isAdjust="isAdjust" @change="changeStation" ref="noticeFormRef" />

        <!-- 底部 -->
        <template v-if="!isAdjust">
            <div style="margin: 30px 20px">
                <Divider type="vertical" class="divid" />
                <span class="title">公告</span>
            </div>
            <Form
                ref="footFormInline"
                :model="formData"
                :label-col="{ style: { width: '110px' } }"
                :rules="rules"
                class="form-flex"
            >
                <FormItem label="公告名称" name="recruitmentBulletinName" style="width: 100%">
                    <!-- :rules="{ required: true, trigger: ['change', 'blur'], type: 'string', message: '请将公告名称设置完整' }" -->
                    <Input style="width: 250px" v-model:value="formData.recruitmentBulletinName" placeholder="请输入公告名称" />
                </FormItem>
                <div class="draft_box">
                    <Button @click="showDraftBox" type="link" size="large">
                        <template #icon><RestOutlined /></template>
                        草稿箱
                    </Button>
                </div>
                <FormItem label="公告内容" name="noticeContent" style="width: 100%">
                    <WangEditor
                        :value="formData?.noticeContent"
                        @on-change="handleChange"
                        ref="editorRef"
                        :editorConfig="editorConfig"
                    />
                </FormItem>
                <FormItem label="附件" name="appendixIdList" style="width: 100%">
                    <ImportFile v-model:fileUrls="formData.appendixIdList" ref="refImportFile" />
                </FormItem>
            </Form>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { message, notification } from 'ant-design-vue'
import { SmileOutlined, RestOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, toRefs, watch, computed, h, onBeforeUpdate, nextTick } from 'vue'

import NoticeForm from './NoticeForm.vue'
import { valuesAndRules } from '/#/component'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
import { getValuesAndRules } from '/@/utils/index'
export default defineComponent({
    name: 'NoticeModal',
    components: { NoticeForm, RestOutlined },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        isAdjust: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { isAdjust } = toRefs(props)
        const refImportFile = ref()
        const noticeFormRef = ref()
        const modalViewType = computed(() => {
            return recruitmentBriefStore().getModalViewType
        })
        const noticeData = computed(() => {
            return recruitmentBriefStore().getNoticeData
        })
        const stationList = computed(() => {
            return recruitmentBriefStore().getStationList
        })
        const examModeList = computed(() => {
            return recruitmentBriefStore().getExamModeList
        })
        const modalTitle = computed(() => {
            return `${modalViewType.value == 'written_announcement' ? '笔试' : '面试'}公告`
        })
        const noticeContent = computed(() => {
            return recruitmentBriefStore().getDraftContent
        })
        const draftTitle = computed(() => {
            return recruitmentBriefStore().getDraftTitle
        })
        const { visible } = toRefs<any>(props)
        // 表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '招聘简章名称',
                name: 'recruitBrochureName',
            },
            {
                label: '客户名称',
                name: 'clientName',
            },
            {
                label: '公告名称',
                name: 'recruitmentBulletinName',
            },
            {
                label: '公告内容',
                name: 'noticeContent',
                width: '100%',
                required: false,
            },
            {
                label: '附件',
                width: '100%',
                name: 'appendixIdList',
                default: [],
                ruleType: 'array',
                required: false,
            },
        ])

        // Form 实例
        const headFormInline = ref(null) as any
        const footFormInline = ref(null) as any
        const editorRef = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const changeStation = () => {
            nextTick(() => {
                formData.value.noticeContent = ''
                formData.value.appendixIdList = []
                editorRef.value.setHtml('')
            })
        }

        const resetData = () => {
            formData.value = initFormData
            headFormInline.value.resetFields()
            footFormInline.value.resetFields()
        }
        const onCancel = () => {
            emit('cancel', true)
        }

        const onConfirm = async () => {
            footFormInline.value
                .validate()
                .then(async () => {
                    if (isAdjust.value) {
                        singleConfirm()
                    } else {
                        await noticeFormRef.value
                            .onFormSubmit()
                            .then((res) => {
                                let myUrl = []
                                if (formData.value.appendixIdList?.length) {
                                    myUrl = refImportFile.value.getFileUrls().map((item) => {
                                        return item.id
                                    })
                                }
                                emit(
                                    'confirm',
                                    {
                                        recruitmentBrochureId: noticeData.value?.id,
                                        noticeContent: formData.value.noticeContent,
                                        recruitmentBulletinName: formData.value.recruitmentBulletinName,
                                        hrRecruitmentStation: [res],
                                        appendixIdList: myUrl,
                                        noticeType: modalViewType.value == 'written_announcement' ? 1 : 2,
                                    },
                                    modalViewType.value,
                                )
                            })
                            .catch((err) => {
                                console.log(err)
                                if (err.type == 1) message.error(err.msg)
                                else notification.warning({ message: err.msg })
                            })
                    }
                })
                .catch((err) => {
                    console.log('表单校验失败', err)
                })
        }

        const singleConfirm = () => {
            noticeFormRef.value
                .onFormSubmit()
                .then((res) => {
                    emit(
                        'confirm',
                        {
                            id: noticeData.value?.id,
                            recruitmentBrochureId: noticeData.value?.recruitmentBrochureId,
                            recruitmentBulletinName: formData.value.recruitmentBulletinName,
                            hrRecruitmentStation: [res],
                        },
                        modalViewType.value,
                    )
                })
                .catch((err) => {
                    if (err.type == 1) message.error(err.msg)
                    else notification.warning({ message: err.msg })
                })
        }

        const handleChange = (html) => {
            formData.value.noticeContent = html
        }

        watch(
            noticeData,
            (val, old) => {
                if (val) {
                    formData.value = Object.assign({}, initFormData, val)
                }
            },
            {
                deep: true,
            },
        )
        watch(
            [noticeContent, draftTitle],
            ([newContent, newTitle], [oldContent, oldTitle]) => {
                if (newContent && newTitle != '考试须知' && visible.value) {
                    formData.value.noticeContent = newContent
                    editorRef.value.setHtml(newContent)
                }
            },
            {
                immediate: true,
            },
        )
        const showDraftBox = () => {
            recruitmentBriefStore().setDraftObj({
                visible: true,
                title: modalViewType.value == 'written_announcement' ? '笔试公告' : '面试公告',
                content: '',
            })
        }

        return {
            showDraftBox,
            changeStation,
            editorRef,
            noticeFormRef,
            refImportFile,
            modalViewType,
            modalTitle,
            rules,
            formData,

            headFormInline,
            footFormInline,
            onConfirm,
            onCancel,

            handleChange,
            editorConfig: {
                height: 300,
                pasteTextHandle: (pasteStr) => {
                    return pasteStr.replace(/<img/g, '<img style="max-width: 100%"')
                },
            },
            stationList,
            examModeList,
            noticeData,
            resetData,
        }
    },
})
</script>
<style lang="less" scoped>
.form-flex {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 0;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    .draft_box {
        display: flex;
        width: 100%;
        justify-content: flex-end;
        height: 0;
        position: relative;
        top: -25px;
    }
}
.divid {
    border-left: 5px solid #1890ff;
    height: 26px;
}
.title {
    font-weight: bold;
}
.ant-tabs {
    margin-left: 24px;
}
</style>
