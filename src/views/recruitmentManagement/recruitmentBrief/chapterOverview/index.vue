<template>
    <div class="wrapper">
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>简章信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">报名日期：</span>
                    <span v-if="brochureInformation?.registerStartDate && brochureInformation?.registerEndDate">{{
                        brochureInformation.registerStartDate + '~' + brochureInformation.registerEndDate
                    }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">审核日期：</span>
                    <span v-if="brochureInformation?.auditStartDate && brochureInformation?.auditEndDate">{{
                        brochureInformation.auditStartDate + '~' + brochureInformation.auditEndDate
                    }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">缴费日期：</span>
                    <span v-if="brochureInformation?.paymentStartDate && brochureInformation?.paymentEndDate">{{
                        brochureInformation.paymentStartDate + '~' + brochureInformation.paymentEndDate
                    }}</span>
                </div>
                <!-- <div class="item-flex">
                    <span class="label">报名模板：</span>
                    <span>{{ brochureInformation?.templateName }}</span>
                </div> -->
                <div class="item-flex1">
                    <span class="label">企业微信：</span>
                    <!-- <span>{{ brochureInformation.enterpriseWechatUrl }}</span> -->
                    <img
                        class="image"
                        :src="brochureInformation?.enterpriseWechatUrl"
                        v-if="brochureInformation?.enterpriseWechatUrl"
                    />
                </div>
                <div class="item-flex1">
                    <div class="label">简章内容：</div>
                    <!-- <Textarea :rows="6" v-model:value="brochureInformation.detailContent" placeholder="简章内容" disabled /> -->
                    <div
                        v-show="brochureInformation.detailContent"
                        class="content"
                        v-html="brochureInformation.detailContent"
                    ></div>
                    <div v-show="!brochureInformation.detailContent" class="content" style="color: #bbbbbb">暂无简章内容</div>
                </div>
                <div class="item-flex1">
                    <span class="label">附件：</span>
                    <!-- <span>{{ brochureInformation?.hrAppendixDTOS[0] }}</span> -->
                    <template v-if="brochureInformation?.hrAppendixDTOS && brochureInformation?.hrAppendixDTOS.length">
                        <span v-for="ele in brochureInformation.hrAppendixDTOS" :key="ele.id">
                            <a href="javascript: void(0)" @click="previewFile(ele.fileUrl)" style="margin-right: 10px">
                                {{ ele.originName }}
                            </a>
                        </span>
                    </template>
                </div>
            </div>
        </div>
        <OperationInfo
            v-if="brochureInformation?.applyOpLogsList && brochureInformation?.applyOpLogsList?.length"
            :applyOpLogs="brochureInformation?.applyOpLogsList"
        />
    </div>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import OperationInfo from '/@/views/serviceCentre/retirement/component/OperationInfo.vue'
import request from '/@/utils/request'
import { previewFile } from '/@/utils'
export default defineComponent({
    name: 'RetireModal',
    // components: { StepBar, BasicInfo },
    components: { OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
        },
        chapterOverview: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { viewType, item, chapterOverview } = toRefs(props)

        const { visible } = toRefs(props)
        //获取数据
        const getData = () => {
            request.get(`/api/hr-recruitment-brochures/brochure-info?id=` + chapterOverview.value).then((res) => {
                brochureInformation.value = { ...res }
                // brochureInformation.value.detailContent = brochureInformation.value?.detailContent
                //     ?.replace(/<[^<>]+>/g, '')
                //     .replace(/&nbsp;/gi, '')
            })
        }

        watch(
            chapterOverview,
            () => {
                if (chapterOverview.value) {
                    // console.log(chapterOverview.value, 555555)
                    getData()
                }
            },
            {
                immediate: true,
                deep: true,
            },
        )

        //基本信息数据
        const brochureInformation = ref<any>({})

        const operationLog = ref<inObject[]>([])
        return {
            brochureInformation,
            operationLog,
            previewFile,
        }
    },
})
</script>
<style scoped lang="less">
.wrapper {
    padding: 20px;
}
.examine {
    // margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        margin-bottom: 20px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
                display: inline-block;
                text-align: right;
            }
        }
        .item-flex1 {
            width: 90%;
            margin: 5px 0px;
            display: flex;
            .label {
                width: 75px;
                color: rgba(153, 153, 153, 1);
                display: inline-block;
                text-align: right;
            }
            .image {
                width: 142px;
                height: 142px;
            }
            .content {
                border: #bbbbbb solid 1px;
                width: calc(100% - 75px);
                min-height: 200px;
                // padding: 10px;
                padding: 5px 10px 10px 10px;
                border-radius: 4px;
            }
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.examine-wrap {
    margin-top: 10px;
    padding-left: 15px;
    .linefeed {
        width: 100%;
        padding: 0;
        margin: 0;
    }
}
.operation_item {
    font-size: 14px;
    .label {
        color: rgba(153, 153, 153, 1);
    }
    .value {
        font-weight: 600;
        color: rgba(51, 51, 51, 1);
        margin-right: 20px;
    }
    .divid_item {
        margin: 10px 0;
    }
}
</style>
