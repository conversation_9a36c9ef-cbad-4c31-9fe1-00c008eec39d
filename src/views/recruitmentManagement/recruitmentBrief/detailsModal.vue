<template>
    <BasicEditModalSlot class="Nofooter" :visible="visible" @cancel="cancel" :title="title" width="1600px" :footer="null">
        <div class="details_header">
            <div>
                <span class="label">招聘简章名称：</span>
                <span class="value" :title="currentItem?.recruitBrochureName">{{ currentItem?.recruitBrochureName }}</span>
            </div>
            <div>
                <span class="label">招聘单位：</span>
                <span class="value" :title="currentItem?.clientName">{{ currentItem?.clientName }}</span>
            </div>
        </div>
        <RadioGroup class="RadioGroup" :default-value="tabs" @change="tabsChange">
            <RadioButton :value="'tab1'">简章概览</RadioButton>
            <RadioButton :value="'tab2'">公告概览</RadioButton>
            <RadioButton :value="'tab3'">报名情况</RadioButton>
        </RadioGroup>
        <ChapterOverview v-show="tabs == 'tab1'" :chapterOverview="item.id" />
        <AnnouncementOverview v-show="tabs == 'tab2'" :announcementOverview="item.id" />
        <ApplicationSituation v-show="tabs == 'tab3'" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import ApplicationSituation from './applicationSituation/index.vue'
import ChapterOverview from './chapterOverview/index.vue'
import AnnouncementOverview from './announcementOverview/index.vue'
import { recruitmentBriefStore } from '/@/store/modules/recruitmentBrief'
export default defineComponent({
    name: 'DetailsModal',
    components: {
        ApplicationSituation,
        ChapterOverview,
        AnnouncementOverview,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs<any>(props)
        const currentItem = computed(() => {
            return recruitmentBriefStore().getCurrentData
        })

        const tabs = ref('tab3')
        // tab切换
        const tabsChange = (value) => {
            tabs.value = value.target.value
        }

        const cancel = () => {
            tabs.value = 'tab3'
            emit('cancel')
            recruitmentBriefStore().setIsOuter(true)
        }
        watch(visible, () => {
            if (visible.value) {
                recruitmentBriefStore().setIsOuter(false)
            }
        })

        return {
            tabs,
            tabsChange,
            cancel,
            currentItem,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
<style scoped lang="less">
.RadioGroup {
    margin: 0 24px 24px;
}
.details_header {
    padding: 0 20px 20px 20px;
    width: 50%;
    display: flex;
    justify-content: space-between;
    div {
        display: flex;
        .label {
            text-align: right;
            width: 120px;
            color: rgba(153, 153, 153, 1);
        }
        .value {
            width: 350px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}
</style>
