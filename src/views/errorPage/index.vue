<template>
    <div class="main">
        <!-- <div class="errorCode" :class="errCode == 500 ? 'dangerous' : 'primary'">{{ errCode }}</div> -->
        <img class="errImg" src="~//@/assets/401.svg" alt="" />
        <div class="links">
            <RouterLink :to="redirectPath">
                <Button type="primary">返回上一页</Button>
            </RouterLink>
            <RouterLink to="/">
                <Button>返回登录页</Button>
            </RouterLink>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import config from '/@/config'

export default defineComponent({
    components: {
        RouterLink,
    },
    setup() {
        const route = useRoute()
        const errCode = ref(route.query.code || 500)
        const redirectPath = ref(route.query.redirect || config.loginPath)

        return {
            errCode,
            redirectPath,
        }
    },
})
</script>

<style scoped lang="less">
.errImg {
    width: 40vw;
    text-align: center;
    margin-top: 100px;
}
.main {
    margin: 0 auto;
    width: 1000px;
    height: 100vh;
    overflow-y: auto;
}
.errorCode {
    margin: 180px 0;
    text-align: center;
    font-size: 10vw;
    font-weight: bold;
    letter-spacing: 2vw;
}
.dangerous {
    color: @dangerous-color;
}
.primary {
    color: @primary-color;
}
.links {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
</style>
