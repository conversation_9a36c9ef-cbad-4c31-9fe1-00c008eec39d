<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button v-auth="'certificateTemplate_add'" type="primary" @click="showModal(null)">新建</Button>
        <Button type="primary" @click="batchExport" v-auth="'certificateTemplate_batch_download'">{{ exportText }}</Button>
        <Button v-auth="'certificateTemplate_del'" type="primary" @click="batchDel" class="delBtn">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-proof-templates/page"
        deleteApi="/api/hr-proof-templates/deletes"
        exportUrl="/api/hr-proof-templates/batch-download"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selectedRowsArr) => (selectIds = selectedRowsArr)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        ref="modalRef"
        :visible="modalVisible"
        :title="modalTitle"
        :templateTypeList="templateTypeList"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts" setup="CertificateTemplateManage">
import { ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import { message } from 'ant-design-vue'

import request from '/@/utils/request'
import MyModal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getDynamicText, getHaveAuthorityOperation } from '/@/utils'
import dwonFile from '/@/utils/downFile'
// 获取模板类型
let templateTypeList = ref<LabelValueOptions>([])
onMounted(() => {
    dictionaryDataStore()
        .setDictionaryData('certificateTemplateType', '')
        .then((data: inObject[]) => {
            templateTypeList.value = data.map((item) => {
                return { label: item.itemName, value: item.itemValue }
            })
        })
})

//筛选
const params = ref<any>({})
const options: SearchBarOption[] = [
    {
        type: 'string',
        label: '模板标题',
        key: 'title',
    },
    {
        type: 'select',
        label: '模板类型',
        key: 'templateTypeList',
        options: templateTypeList,
        multiple: true,
    },
]

//表格dom
const tableRef = ref()
const modalRef = ref()
const searchData = async () => {
    tableRef.value.refresh(1)
}
//表格数据
const columns = [
    {
        title: '模板标题',
        dataIndex: 'title',
        align: 'center',
        width: 250,
    },
    {
        title: '模板类型',
        dataIndex: 'templateType',
        align: 'center',
        width: 120,
        customRender: ({ record }) => {
            return templateTypeList.value.find((el) => {
                return el.value == record.templateType
            })?.label
        },
    },
    {
        title: '更新时间',
        dataIndex: 'lastModifiedDate',
        align: 'center',
        width: 150,
    },
    {
        title: '应用次数',
        dataIndex: 'useNum',
        align: 'center',
        width: 120,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        width: 150,
        slots: { customRender: 'operation' },
        fixed: 'right',
    },
]

const modalVisible = ref(false)
const modalTitle = ref('')
// 当前编辑的数据
const currentValue = ref(null)
const showModal = (record) => {
    modalVisible.value = true
    currentValue.value = record
    if (record) {
        modalTitle.value = '编辑证明模板'
    } else {
        modalTitle.value = '新建证明模板'
    }
}
const batchDel = () => {
    tableRef.value.deleteRow().then((ref) => {
        console.log(ref)
    })
}

const modalCancel = () => {
    modalVisible.value = false
    modalTitle.value = ''
    currentValue.value = null
}

const modalConfirm = async (record, type) => {
    if (type == 'EDIT') {
        request
            .put('/api/hr-proof-templates/update', {
                ...record,
            })
            .then((res) => {
                message.success('修改证明模板成功')
                modalCancel()
                modalRef.value.resetFormData()
                tableRef.value.refresh()
            })
            .catch((err) => {
                console.log(err)
            })
    } else {
        request
            .post('/api/hr-proof-templates/create', {
                ...record,
            })
            .then((res) => {
                message.success('新增成功！')
                modalCancel()
                modalRef.value.resetFormData()
                tableRef.value.refresh(1)
            })
            .catch((err) => {
                console.log(err)
            })
    }
}
// 选中的ID
const selectIds: any = ref([])
const exportText = computed(() => {
    return getDynamicText('下载', params.value, selectIds.value)
})
//批量下载
const batchExport = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

// 下载
const downloadRow = (record) => {
    dwonFile('get', record.fileUrl, record.fileName)
}

// 操作按钮
let myOperation = ref<inObject[]>(
    getHaveAuthorityOperation([
        {
            neme: '编辑',
            auth: 'certificateTemplate_edit',
            show: (record) => record.type != 11,
            click: (record) => showModal(record),
        },
        {
            neme: '下载',
            auth: 'certificateTemplate_download',
            show: (record) => record.fileUrl,
            click: (record) => downloadRow(record),
        },
    ]),
)
</script>

<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
</style>
