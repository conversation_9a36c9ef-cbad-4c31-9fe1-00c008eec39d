<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" width="500px">
        <Form ref="formInline" :model="formData" :rules="rules">
            <template v-for="ele in myOptions" :key="ele.name + 'materialModal'">
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #attachment>
                        <div class="attachment_wrapper">
                            <ImportFile
                                v-model:fileUrls="formData.fileId"
                                :accept="'.pdf'"
                                :multiple="false"
                                :count="1"
                                ref="refImportFile"
                            />
                            <span class="tips">只可上传pdf文件</span>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn">保存</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
export default defineComponent({
    name: 'CertificateTemplateModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        templateTypeList: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { item, visible, templateTypeList } = toRefs(props)
        const refImportFile = ref()
        const disabled = computed(() => {
            return item.value ? !item.value.canDeleted : false
        })

        // 表单数据
        const myOptions = ref([
            {
                label: '模板标题',
                name: 'title',
                required: true,
                // disabled: disabled,
            },
            {
                label: '模板类型',
                name: 'templateType',
                type: 'change',
                ruleType: 'number',
                required: true,
                options: templateTypeList,
            },
            {
                label: '合同模板',
                name: 'fileId',
                type: 'slots',
                slots: 'attachment',
                default: [],
                ruleType: 'array',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                if (item.value) {
                    formData.value = { ...Object.assign({}, initFormData, item.value) }
                    if (item.value.appendixId || item.value.fileName || item.value.fileUrl) {
                        formData.value.fileId = [
                            { id: item.value.appendixId || '', name: item.value.fileName, fileUrl: item.value.fileUrl },
                        ]
                    } else {
                        formData.value.fileId = []
                    }
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const onConfirm = () => {
            let myUrl = refImportFile.value.getFileUrls()[0]

            formInline.value
                .validate()
                .then(() => {
                    if (item.value) {
                        emit('confirm', { ...formData.value, appendixId: myUrl?.id || '', fileUrl: myUrl?.url || '' }, 'EDIT')
                    } else {
                        emit('confirm', { ...formData.value, appendixId: myUrl?.id || '', fileUrl: myUrl?.url || '' }, 'ADD')
                    }
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const onCancel = () => {
            resetFormData()
            emit('cancel')
        }

        return {
            onConfirm,
            onCancel,
            resetFormData,

            refImportFile,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
<style lang="less" scoped>
.attachment_wrapper {
    display: flex;
    align-items: center;
    .tips {
        margin-left: 15px;
        color: rgba(187, 187, 190, 1);
        font-size: 14px;
    }
}
</style>
