<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="日志明细" :width="'800px'">
        <table border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td class="tableTitle">标题</td>
                <td class="tableContent">{{ formData.sendTitle }}</td>
            </tr>
            <tr>
                <td class="tableTitle">内容</td>
                <td class="tableContent">{{ formData.sendContent }}</td>
            </tr>
            <tr>
                <td class="tableTitle">接收手机号</td>
                <td class="tableContent">{{ formData.sendPhone }}</td>
            </tr>
            <tr>
                <td class="tableTitle">发送人</td>
                <td class="tableContent">{{ formData.sendName }}</td>
            </tr>
            <tr>
                <td class="tableTitle">发送时间</td>
                <td class="tableContent">{{ formData.createdDate }}</td>
            </tr>
            <tr>
                <td class="tableTitle" style="height: 120px">结果明细</td>
                <td class="tableContent">{{ formData.responsBody }}</td>
            </tr>
        </table>
        <template #footer>
            <Button key="back" @click="cancel" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'messageLogModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        typeList: {
            type: Array,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '操作内容',
                name: 'title',
                required: false,
            },
            {
                label: '操作类型',
                name: 'businessTypeStr',
                required: false,
            },
            {
                label: '操作明细',
                name: 'operDetail',
                required: false,
            },
            {
                label: '操作人',
                name: 'operName',
                required: false,
            },
            {
                label: '操作时间',
                name: 'operTime',
                required: false,
            },
        ])
        //请求
        const api: string = '/api/users'
        const { title, item, visible } = toRefs(props)
        // const title = ref(props.props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })

        // reset formData
        // const resetFormData = () => {
        //     formData.value = initFormData
        //     formInline.value.resetFields()
        // }

        // cancel handle
        const cancel = () => {
            emit('cancel')
            // resetFormData()
        }

        return {
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
<style scoped lang="less">
table {
    width: 100%;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
td {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;
}
.tableTitle {
    width: 105px;
    height: 45px;
    text-align: center;
    background-color: #1890ff;
    color: #fff;
}
.tableContent {
    padding: 0px 20px 0px 20px;
}
</style>
