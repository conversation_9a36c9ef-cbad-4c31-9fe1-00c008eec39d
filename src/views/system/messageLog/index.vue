<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="batchExport">{{ exportText }}</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-send-sms/page"
        :params="params"
        exportUrl="/api/hr-send-sms/export"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="seeRow(record)">查看</Button>
            &nbsp;
            <Button type="primary" size="small" @click="downloadRow(record)" v-if="record.fileUrl">下载</Button>
        </template>
    </BasicTable>

    <myModal :visible="showEdit" :item="currentValue" @cancel="modalCancel" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import myModal from './modal.vue'
import moment from 'moment'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'MessageLog',
    components: { myModal: myModal },
    setup() {
        // 获取标题
        let sendTitleList = ref<any>([])
        onMounted(() => {
            request.get('/api/hr-send-sms/title', {}).then((res) => {
                const list = [...new Set(res)]
                sendTitleList.value = list.map((item, index) => {
                    return { key: index, label: item, value: item }
                })
            })
        })
        //筛选
        interface params {
            sendTitle?: string | number | null
        }
        const params = ref<any>({
            sendTitle: null,
        })
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '标题',
                key: 'sendTitleList',
                options: sendTitleList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '发送时间',
                key: 'validTimeList',
                allowClear: true,
            },
        ]

        const changeRoleId = (value: any) => {
            if (value.option.length != 0) {
                ;(params.value as any).businessTypes = value.option.map((item) => item.value)
            }
        }

        //操作开始时间
        const operTimeStartChange = (value: any) => {
            let startTime = moment(value).format('YYYY-MM-DD')
            params.value.operTimeStart = startTime + ' ' + '00:00:00'
        }
        //操作结束时间
        const operTimeEndChange = (value: any) => {
            let endTime = moment(value).format('YYYY-MM-DD')
            params.value.operTimeEnd = endTime + ' ' + '00:00:00'
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '标题',
                dataIndex: 'sendTitle',
                align: 'center',
                width: 150,
            },
            {
                title: '内容',
                dataIndex: 'sendContent',
                align: 'center',
                width: 300,
                ellipsis: true,
            },
            {
                title: '接收手机号',
                dataIndex: 'sendPhone',
                align: 'center',
                width: 150,
            },
            {
                title: '发送人',
                dataIndex: 'sendName',
                align: 'center',
                width: 100,
            },
            {
                title: '发送时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 150,
            },
            {
                title: '发送结果',
                dataIndex: 'izSucceed',
                align: 'center',
                customRender: ({ text }) => {
                    return text ? '发送成功' : '发送不成功'
                },
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        // 当前编辑的数据
        const currentValue = ref(null)
        // 选中的ID
        const selectIds: any = ref([])
        // 多选
        const selectedRowsArr = (arr) => {
            selectIds.value = arr
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectIds.value)
        })
        //批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const seeRow = (record) => {
            showEdit.value = true
            currentValue.value = { ...record }
        }
        const downloadRow = (row) => {
            console.log(row.fileUrl, '下载')
            // window.location.href = 'row.fileUrl'
        }
        const modalCancel = () => {
            showEdit.value = false
            currentValue.value = null
        }

        return {
            exportText,
            sendTitleList,
            options,
            modalCancel,
            showEdit,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            batchExport,
            seeRow,
            downloadRow,
            operTimeStartChange,
            operTimeEndChange,
            selectedRowsArr,
            selectIds,

            //事件
            changeRoleId,
        }
    },
})
</script>

<style scoped lang="less"></style>
