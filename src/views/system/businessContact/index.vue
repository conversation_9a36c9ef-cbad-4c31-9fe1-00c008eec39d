<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button v-auth="'businessContact_add'" type="primary" @click="showModal(null)">新建</Button>
        <Button danger type="primary" @click="deleteRow" v-auth="'businessContact_del'">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-business-contacts/page"
        deleteApi="/api/hr-business-contacts/deletes"
        :params="params"
        :columns="columns"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        ref="modalRef"
        :visible="modalVisible"
        :title="modalTitle"
        :serviceList="serviceList"
        :currentItem="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import { message } from 'ant-design-vue'

import request from '/@/utils/request'
import modal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, previewFile } from '/@/utils'
export default defineComponent({
    name: 'BusinessContact',
    components: { MyModal: modal },
    setup() {
        // 获取服务分类
        let serviceList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('serviceType', '')
                .then((data: inObject[]) => {
                    serviceList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '业务类型',
                key: 'businessType',
            },
            {
                type: 'string',
                label: '联系人姓名',
                key: 'contacts',
            },
            {
                type: 'string',
                label: '联系方式',
                key: 'contactInformation',
            },
        ]

        //表格dom
        const tableRef = ref()
        const modalRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '业务类型',
                dataIndex: 'businessType',
                align: 'center',
                width: 250,
            },
            {
                title: '联系人姓名',
                dataIndex: 'contacts',
                align: 'center',
                width: 120,
            },
            {
                title: '联系方式',
                dataIndex: 'contactInformation',
                align: 'center',
                width: 450,
            },
            {
                title: '排序',
                dataIndex: 'sort',
                align: 'center',
                width: 450,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                width: 150,
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]

        const modalVisible = ref(false)
        const modalTitle = ref('')
        const viewType = ref('add')
        // 当前编辑的数据
        const currentValue = ref(null)
        const showModal = (record) => {
            modalVisible.value = true
            currentValue.value = record
            if (record) {
                modalTitle.value = '编辑联系人'
                viewType.value = 'edit'
            } else {
                modalTitle.value = '新增联系人'
                viewType.value = 'add'
            }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        const modalCancel = () => {
            modalVisible.value = false
            modalTitle.value = ''
            currentValue.value = null
            viewType.value = 'add'
        }

        const modalConfirm = async (record, type) => {
            if (viewType.value == 'add') {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                // {
                //     neme: '查看',
                //     show: (record) => {
                //         return record.type == 11
                //     },
                //     click: (record) => previewFile(record.fileUrl),
                // },
                {
                    neme: '编辑',
                    auth: 'businessContact_edit',
                    show: (record) => record.type != 11,
                    click: (record) => showModal(record),
                },
                // {
                //     neme: '删除',
                //     auth: 'BusinessContact_del',
                //     show: (record) => record.type != 11 && record.canDeleted,
                //     click: (record) => deleteRow(record),
                // },
            ]),
        )

        return {
            serviceList,
            options,
            modalVisible,
            modalTitle,
            currentValue,
            viewType,
            columns,
            params,
            tableRef,
            modalRef,
            //操作按钮
            myOperation,

            searchData,
            modalCancel,
            modalConfirm,
            showModal,
            deleteRow,
            previewFile,
        }
    },
})
</script>

<style scoped lang="less"></style>
