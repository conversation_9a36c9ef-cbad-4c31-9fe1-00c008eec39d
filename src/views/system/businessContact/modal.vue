<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" />
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import ImportFile from '/@/components/ImportFile/src/ImportFile.vue'
export default defineComponent({
    name: 'BusinessContactModal',
    components: {},
    props: {
        title: String,
        currentItem: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: String,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/hr-business-contacts'
        const { title, currentItem, visible, viewType } = toRefs(props)

        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '业务类型',
                name: 'businessType',
            },
            {
                label: '联系人姓名',
                name: 'contacts',
            },
            {
                label: '联系方式',
                name: 'contactInformation',
                validator: validatePhone,
            },
            {
                label: '序号',
                name: 'sort',
                type: 'number',
                ruleType: 'number',
                min: 1,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, currentItem.value) }
                if (viewType.value == 'add') {
                    getMaxSort()
                }
            }
        })
        const getMaxSort = function () {
            request.get('/api/hr-business-contacts/max-sort', formData.value).then((ref) => {
                formData.value.sort = (ref || 0) + 1
            })
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
