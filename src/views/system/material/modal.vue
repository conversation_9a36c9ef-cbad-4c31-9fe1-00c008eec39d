<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title">
        <Form ref="formInline" :model="formData" :rules="rules">
            <template v-for="ele in myOptions" :key="ele.name + 'materialModal'">
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #attachment>
                        <div class="attachment_wrapper">
                            <ImportFile
                                v-model:fileUrls="formData.fileId"
                                :accept="'.doc,.docx,.pdf'"
                                :multiple="false"
                                :count="1"
                                ref="refImportFile"
                            />
                            <span class="tips">可上传word/pdf文件</span>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <template #footer>
            <Button @click="onCancel" class="btn">取消</Button>
            <Button @click="onConfirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
export default defineComponent({
    name: 'MaterialModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        serviceList: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { item, visible, serviceList } = toRefs(props)
        const refImportFile = ref()
        const disabled = computed(() => {
            return item.value ? !item.value.canDeleted : false
        })

        // 表单数据
        const myOptions = ref([
            {
                label: '材料名称',
                name: 'name',
                required: true,
                disabled: disabled,
            },
            {
                label: '所属服务',
                name: 'type',
                type: 'select',
                required: true,
                options: serviceList,
                ruleType: 'number',
                disabled: disabled,
            },
            {
                label: '附件上传',
                name: 'fileId',
                required: false,
                type: 'slots',
                slots: 'attachment',
                default: [],
                ruleType: 'array',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                if (item.value) {
                    formData.value = { ...Object.assign({}, initFormData, item.value) }
                    if (item.value.fileId || item.value.fileName || item.value.fileUrl) {
                        formData.value.fileId = [
                            { id: item.value.fileId || '', name: item.value.fileName, fileUrl: item.value.fileUrl },
                        ]
                    } else {
                        formData.value.fileId = []
                    }
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const onConfirm = () => {
            let myUrl = refImportFile.value.getFileUrls()[0]

            formInline.value
                .validate()
                .then(() => {
                    if (item.value) {
                        emit('confirm', { ...formData.value, fileId: myUrl?.id || '' }, 'EDIT')
                    } else {
                        emit('confirm', { ...formData.value, fileId: myUrl?.id || '' }, 'ADD')
                    }
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const onCancel = () => {
            resetFormData()
            emit('cancel')
        }

        return {
            onConfirm,
            onCancel,
            resetFormData,

            refImportFile,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
<style lang="less" scoped>
.attachment_wrapper {
    display: flex;
    align-items: center;
    .tips {
        margin-left: 15px;
        color: rgba(187, 187, 190, 1);
        font-size: 14px;
    }
}
</style>
