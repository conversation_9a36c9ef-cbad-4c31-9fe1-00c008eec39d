<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button v-auth="'materialManage_add'" type="primary" @click="showModal(null)">新建</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-materials/page"
        deleteApi="/api/hr-materials/deletes"
        :params="params"
        :columns="columns"
        :rowSelectionShow="false"
        useIndex
    >
        <template #attachment="{ record }">
            <a href="javascript: void(0)" @click="previewFile(record.fileUrl)" style="margin-right: 10px">
                {{ record.fileName }}
            </a>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        ref="modalRef"
        :visible="modalVisible"
        :title="modalTitle"
        :serviceList="serviceList"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import { message } from 'ant-design-vue'

import request from '/@/utils/request'
import modal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, previewFile } from '/@/utils'
export default defineComponent({
    name: 'MaterialManage',
    components: { MyModal: modal },
    setup() {
        // 获取服务分类
        let serviceList = ref<LabelValueOptions>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('serviceType', '')
                .then((data: inObject[]) => {
                    serviceList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '材料名称',
                key: 'name',
            },
            {
                type: 'select',
                label: '所属服务',
                key: 'typeList',
                options: serviceList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const modalRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '名称',
                dataIndex: 'name',
                align: 'center',
                width: 250,
            },
            {
                title: '所属服务',
                dataIndex: 'type',
                align: 'center',
                width: 120,
                customRender: ({ record }) => {
                    return serviceList.value.find((el) => {
                        return el.value == record.type
                    })?.label
                },
            },
            {
                title: '附件',
                dataIndex: 'file',
                align: 'center',
                width: 450,
                slots: { customRender: 'attachment' },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                width: 150,
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]

        const modalVisible = ref(false)
        const modalTitle = ref('')
        // 当前编辑的数据
        const currentValue = ref(null)
        const showModal = (record) => {
            modalVisible.value = true
            currentValue.value = record
            if (record) {
                modalTitle.value = '编辑材料'
            } else {
                modalTitle.value = '新增材料'
            }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
            })
        }

        const modalCancel = () => {
            modalVisible.value = false
            modalTitle.value = ''
            currentValue.value = null
        }

        const modalConfirm = async (record, type) => {
            if (type == 'EDIT') {
                request
                    .put('/api/hr-materials', {
                        ...record,
                    })
                    .then((res) => {
                        message.success('修改材料成功')
                        modalCancel()
                        modalRef.value.resetFormData()
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        modalCancel()
                    })
            } else {
                request
                    .post('/api/hr-materials', {
                        ...record,
                    })
                    .then((res) => {
                        message.success('新增成功！')
                        modalCancel()
                        modalRef.value.resetFormData()
                        tableRef.value.refresh(1)
                    })
                    .catch((err) => {
                        modalCancel()
                    })
            }
        }

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    show: (record) => {
                        return record.type == 11
                    },
                    click: (record) => previewFile(record.fileUrl),
                },
                {
                    neme: '编辑',
                    auth: 'materialManage_edit',
                    show: (record) => record.type != 11,
                    click: (record) => showModal(record),
                },
                {
                    neme: '删除',
                    auth: 'materialManage_del',
                    show: (record) => record.type != 11 && record.canDeleted,
                    click: (record) => deleteRow(record),
                },
            ]),
        )

        return {
            serviceList,
            options,
            modalVisible,
            modalTitle,
            currentValue,
            columns,
            params,
            tableRef,
            modalRef,
            //操作按钮
            myOperation,

            searchData,
            modalCancel,
            modalConfirm,
            showModal,
            deleteRow,
            previewFile,
        }
    },
})
</script>

<style scoped lang="less"></style>
