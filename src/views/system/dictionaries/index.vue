<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />

    <div class="btns" v-if="isDeveloper">
        <Button type="primary" @click="createChild({ id: 0, itemName: '一级' })">添加字典</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/com-code-tables/page"
        :params="params"
        :columns="columns"
        :tableDataFormat="tableDataFormat"
        :rowSelectionShow="false"
        :getChildrenMethod="getChildrenMethod"
    >
        <template #operation="{ record }">
            <Button
                v-if="(isAuth('dictionaries_edit') && record.izDefault == 1) || isDeveloper"
                type="primary"
                size="small"
                @click="editRow(record)"
                >编辑</Button
            >
            &nbsp;
            <Button
                v-if="(isAuth('dictionaries_edit') && record.izDefault == 1) || isDeveloper"
                type="primary"
                size="small"
                @click="createChild(record)"
            >
                新增子项
            </Button>
            &nbsp;
            <Button
                v-if="(isAuth('dictionaries_edit') && record.izDefault == 1) || isDeveloper"
                type="primary"
                size="small"
                @click="importChild(record)"
            >
                批量导入
            </Button>
            &nbsp;
            <Button v-if="isDeveloper" type="primary" size="small" danger @click="deleteRow(record)">删除</Button>
        </template>
    </BasicTable>

    <BasicEditModal
        :visible="showEdit"
        api="/api/com-code-tables"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :options="
            isDeveloper
                ? [
                      ...options,
                      {
                          label: '内部名',
                          name: 'innerName',
                          required: false,
                      },
                  ]
                : options
        "
    />

    <ImportModal
        v-model:visible="showImport"
        temUrl="/api/com-code-tables/template"
        importUrl="/api/com-code-tables/import"
        :importUrlParam="currentValue"
        @getResData="searchData"
    />
</template>

<script lang="ts">
import { Modal } from 'ant-design-vue'
import { defineComponent, h, ref } from 'vue'
import { EditModalOption } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'
import { isAuth } from '/@/utils/directive'

export default defineComponent({
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'Dictionaries',
    setup() {
        const params = ref({
            itemName: undefined,
        })

        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const columns = [
            {
                title: '字典名称',
                dataIndex: 'itemName',
                align: 'left',
            },
            {
                title: '内部名',
                dataIndex: 'innerName',
                align: 'center',
            },
            {
                title: '数据项',
                dataIndex: 'countNum',
                align: 'center',
                customRender: ({ text }) => {
                    return h('span', text ? `${text}项` : '')
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        const modalTitle = ref('新增菜单')
        const currentValue = ref<Recordable>({})
        const showImport = ref(false)

        const createChild = (record) => {
            modalTitle.value = `新增${record.itemName}子项`
            currentValue.value = { parentId: record.id } as any
            showEdit.value = true
        }

        const editRow = (record) => {
            modalTitle.value = '编辑'
            currentValue.value = { ...record }
            showEdit.value = true
        }

        const modalCancel = () => {
            showEdit.value = false
            currentValue.value = {}
        }
        const modalConfirm = async () => {
            showEdit.value = false
            currentValue.value = {}
            tableRef.value.refresh()
            //置空缓存字典数据
            dictionaryDataStore().emptyDictionaryData()
        }

        const tableDataFormat = (item) => {
            return item.map((i) => ({
                ...i,
                children: i.countNum ? [] : null,
            }))
        }

        const options: EditModalOption[] = [
            {
                label: '字典名称',
                name: 'itemName',
            },
        ]
        const deleteRow = (row) => {
            Modal.confirm({
                title: '确认',
                content: '您确定要删除该条数据吗？',
                onOk() {
                    confirmDelete(row.id)
                },
                onCancel() {},
            })
        }
        const confirmDelete = async (id: string) => {
            await request.post(`/api/com-code-tables/deletes`, [id])
            tableRef.value.refresh(1)
        }

        const importChild = (record) => {
            currentValue.value = { parentId: record.id } as any
            showImport.value = true
        }

        const getChildrenMethod = ({ id }) => {
            return new Promise(async (resolve) => {
                const res = await request.get(`/api/com-code-tables/get-children-list/${id}`)
                resolve(
                    res.map((i) => ({
                        ...i,
                        children: i.countNum ? [] : null,
                    })),
                )
            })
        }

        const isDeveloper = useUserStore().getUserInfo?.roles[0]?.roleKey === 'developer'

        return {
            isDeveloper,
            showImport,
            getChildrenMethod,
            importChild,
            deleteRow,
            createChild,
            modalTitle,
            options,
            modalCancel,
            isAuth,
            modalConfirm,
            showEdit,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            editRow,
            tableDataFormat,
            searchOptions: [
                {
                    label: '字典名称',
                    key: 'itemName',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less"></style>
