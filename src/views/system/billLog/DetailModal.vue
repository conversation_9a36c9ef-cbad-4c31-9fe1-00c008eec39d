<template>
    <BasicEditModalSlot :visible="visible" title="日志明细" @cancel="modalClose" :footer="null" width="1100px">
        <BasicTable
            v-if="visible"
            ref="tableRef"
            :api="`/api/hr-bill-content-records/record-page`"
            :params="{
                billId: currentRecord?.billId,
            }"
            :columns="columns"
            :rowSelectionShow="false"
            :sorter="false"
            :useIndex="true"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { h } from 'vue-demi'

defineProps({
    visible: Boolean,
    currentRecord: Object,
})
const emit = defineEmits(['update:visible'])

const modalClose = () => {
    emit('update:visible', false)
}

const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
    },
    {
        title: '身份证号',
        dataIndex: 'certificateNum',
        width: 180,
    },
    {
        title: '操作类型',
        dataIndex: 'businessTypeStr',
        width: 100,
    },
    {
        title: '操作明细',
        dataIndex: 'operDetail',
        width: 400,
        customRender: ({ text }) => {
            const strs = text.split('；')
            return strs?.length
                ? h(
                      'div',
                      strs.map((i) => h('div', i)),
                  )
                : text
        },
    },
    {
        title: '操作人',
        dataIndex: 'operName',
        width: 120,
    },
    {
        title: '操作时间',
        dataIndex: 'createdDate',
        width: 170,
    },
]
</script>

<style scoped lang="less"></style>
