<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-content-records/page"
        exportUrl="/api/hr-bill-content-records/download-batch"
        :columns="columns"
        :params="params"
        @selectedRowsArr="selHandle"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showRecord(record)">查看</Button>
        </template>
    </BasicTable>
    <DetailModal v-model:visible="showDetail" :currentRecord="currentRecord" />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { SearchBarOption } from '/#/component'
import { billTypeList } from '/@/utils/dictionaries'
import DetailModal from './DetailModal.vue'
import downFile from '/@/utils/downFile'
import { getDynamicText } from '/@/utils'

const params = ref({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
const exportData = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

const showDetail = ref(false)
const currentRecord = ref(undefined)
const showRecord = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}

const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 120,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 90,
    },
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 100,
        customRender: ({ text }) => {
            return billTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '标题',
        dataIndex: 'title',
        width: 170,
        ellipsis: true,
    },
    {
        title: '日志数量',
        dataIndex: 'num',
        width: 100,
        sorter: false,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientId',
        type: 'clientSelectTree',
    },
    {
        label: '费用年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '账单类型',
        key: 'billType',
        type: 'select',
        options: billTypeList,
    },
    {
        label: '标题',
        key: 'title',
    },
]
</script>

<style scoped lang="less"></style>
