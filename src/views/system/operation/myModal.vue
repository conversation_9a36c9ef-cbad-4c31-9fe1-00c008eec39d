<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="日志明细" :width="'800px'">
        <table border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td class="tableTitle">操作内容</td>
                <td class="tableContent">{{ formData.title }}</td>
            </tr>
            <tr>
                <td class="tableTitle">操作类型</td>
                <td class="tableContent">{{ formData.businessTypeStr }}</td>
            </tr>
            <tr>
                <td class="tableTitle">操作明细</td>
                <td class="tableContent">{{ formData.operDetail }}</td>
            </tr>
            <tr>
                <td class="tableTitle">操作人</td>
                <td class="tableContent">{{ formData.operName }}</td>
            </tr>
            <tr>
                <td class="tableTitle">IP地址</td>
                <td class="tableContent">{{ formData.operIp }}</td>
            </tr>
            <tr>
                <td class="tableTitle">操作时间</td>
                <td class="tableContent">{{ formData.operTime }}</td>
            </tr>
            <tr>
                <td class="tableTitle" style="height: 120px">数据明细</td>
                <td class="tableContent">
                    {{ formData.operParam }} <br />
                    {{ formData.jsonResult }}
                </td>
            </tr>
        </table>
        <template #footer>
            <Button key="back" @click="cancel" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'operationModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        typeList: {
            type: Array,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '操作内容',
                name: 'title',
                required: false,
            },
            {
                label: '操作类型',
                name: 'businessTypeStr',
                required: false,
            },
            {
                label: '操作明细',
                name: 'operDetail',
                required: false,
            },
            {
                label: '操作人',
                name: 'operName',
                required: false,
            },
            {
                label: '操作时间',
                name: 'operTime',
                required: false,
            },
        ])
        //请求
        const api: string = '/api/users'
        const { title, item, visible } = toRefs(props)
        // const title = ref(props.props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }

                // if (item.value) {
                //     formData.value.dataDetail = item.value.operParam + item.value.jsonResult
                // }
            }
        })

        // reset formData
        // const resetFormData = () => {
        //     formData.value = initFormData
        //     formInline.value.resetFields()
        // }

        // cancel handle
        const cancel = () => {
            emit('cancel')
            // resetFormData()
        }

        return {
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
<style scoped lang="less">
table {
    width: 100%;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
td {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;
}
.tableTitle {
    width: 105px;
    height: 45px;
    text-align: center;
    background-color: #1890ff;
    color: #fff;
}
.tableContent {
    padding: 0px 20px 0px 20px;
    word-wrap: break-word;
    word-break: break-all;
}
</style>
