<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="batchExport">导出</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/sys-oper-logs/page"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="seeRow(record)">查看</Button>
            &nbsp;
            <Button type="primary" size="small" @click="downloadRow(record)" v-if="record.fileUrl">下载</Button>
        </template>
    </BasicTable>

    <myModal :visible="showEdit" :item="currentValue" :typeList="typeList" @cancel="modalCancel" />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import myModal from './myModal.vue'
import dwonFile, { downMultFile } from '/@/utils/downFile'
export default defineComponent({
    name: 'LogOperation',
    components: { myModal: myModal },
    setup() {
        // 获取操作类型
        const typeList = ref<LabelValueOptions>([])
        // 获取操作内容
        const titleList = ref<LabelValueOptions>([])
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/operationType', {}).then((res) => {
                typeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })

            request.get('/api/sys-oper-logs/drop-down-list', {}).then((res) => {
                titleList.value = Object.keys(res).map((val) => ({
                    label: res[val],
                    value: res[val],
                }))
            })
        })
        //筛选
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '操作内容',
                key: 'titleList',
                options: titleList,
                multiple: true,
            },
            {
                type: 'select',
                label: '操作类型',
                key: 'businessTypes',
                options: typeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '操作明细',
                key: 'operDetail',
            },
            {
                type: 'string',
                label: '操作账号',
                key: 'operAccount',
            },
            {
                type: 'string',
                label: '操作人',
                key: 'operName',
            },
            // {
            //     type: 'datetime',
            //     label: '操作开始时间',
            //     key: 'operTimeStart',
            //     allowClear: true,
            // },
            // {
            //     type: 'datetime',
            //     label: '操作结束时间',
            //     key: 'operTimeEnd',
            //     allowClear: true,
            // },
            {
                type: 'datetimerange',
                label: '操作时间',
                key: 'operTimeQuery',
                allowClear: true,
            },
        ]
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '操作内容',
                dataIndex: 'title',
                align: 'center',
                width: 100,
            },
            {
                title: '操作类型',
                dataIndex: 'businessTypeStr',
                align: 'center',
                width: 100,
            },
            {
                title: '操作明细',
                dataIndex: 'operDetail',
                align: 'center',
                width: 300,
                ellipsis: true,
            },
            {
                title: '操作账号',
                dataIndex: 'operAccount',
                align: 'center',
                width: 110,
            },
            {
                title: '操作人',
                dataIndex: 'operName',
                align: 'center',
                width: 120,
            },
            {
                title: 'IP地址',
                dataIndex: 'operIp',
                align: 'center',
                width: 170,
            },
            {
                title: '操作时间',
                dataIndex: 'operTime',
                align: 'center',
                width: 170,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 150,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        // 当前编辑的数据
        const currentValue = ref(undefined)
        // 选中的ID
        const selectIds = ref<(string | number)[]>([])
        // 多选
        const selectedRowsArr = (item) => {
            let ids: (string | number)[] = []
            ids = item.map((val: inObject) => {
                return val.id
            })
            selectIds.value = ids
        }
        //批量导出
        const batchExport = () => {
            if (selectIds.value == null || selectIds.value?.length == 0) {
                message.error('请先选择一条数据!')
                return
            }
            let exportUrl = '/api/sys-oper-logs/export'
            let exportTitle = '操作日志导出'
            let body = selectIds.value
            dwonFile('post', exportUrl, exportTitle, body)
        }
        const seeRow = (record) => {
            showEdit.value = true
            currentValue.value = { ...record }
        }
        const downloadRow = (row) => {
            let fileUrls = row.fileUrl?.split(',') || []
            if (fileUrls.length) {
                if (fileUrls.length == 1) {
                    dwonFile('get', fileUrls[0], '')
                } else {
                    downMultFile('', fileUrls, [])
                }
            }
        }
        const modalCancel = () => {
            showEdit.value = false
            currentValue.value = undefined
        }

        return {
            typeList,
            options,
            modalCancel,
            showEdit,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            batchExport,
            seeRow,
            downloadRow,
            selectedRowsArr,
            selectIds,
        }
    },
})
</script>

<style scoped lang="less"></style>
