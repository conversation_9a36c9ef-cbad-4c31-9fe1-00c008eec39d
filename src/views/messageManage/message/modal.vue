<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1000px">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]">
                    <template #TreeSelect>
                        <RoleTree ref="roleTreeRef" v-model:value="formData[itemForm.name]" :itemForm="itemForm" />
                    </template>
                    <template #WangEditor>
                        <div style="line-height: 32px">  <WangEditor @on-change="handleChange" ref="editorRef" /></div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message, TreeSelect } from 'ant-design-vue'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'

import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import RoleTree from '/@/views/authorization/role/roleTree.vue'
const SHOW_PARENT = TreeSelect.SHOW_PARENT

export default defineComponent({
    name: 'Modal',
    components: {
        RoleTree,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const treeData = ref<TreeDataItem[]>([])
        const value = ref<string[]>([])

        //表单数据
        //请求
        const api = '/api/hr-message-lists'

        const { title, item, visible } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            console.log(value, option)
            formData.value.roleIdList = [value]
        }

        const editDisabled = ref<boolean>(false)

        const roleTreeRef = ref()

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '标题',
                name: 'title',
            },
            {
                label: '接收对象',
                name: 'roleList',
                slots: 'TreeSelect',
                type: 'slots',
                message: '请选择接收对象',
                ruleType: 'array',
                default: [],
            },
            {
                label: '内容',
                name: 'content',
                type: 'slots',
                slots: 'WangEditor', // ruleType: 'number',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        const editorRef = ref()

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
                if (title.value?.includes('新增')) {
                    editDisabled.value = false
                } else {
                    editDisabled.value = true
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(11111)

            let { newRoleList } = roleTreeRef.value.getData()
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', { ...formData.value, roleList: newRoleList })
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', { ...formData.value, roleList: newRoleList })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
            editorRef.value.setHtml('')
        }
        const handleChange = (html, value) => {
            formData.value.content = html
            console.log(value)
            console.log(html)
        }

        // Function(inputValue: string, treeNode: TreeNode) (函数需要返回 bool 值)
        const filterTreeNode = (inputValue: string, treeNode) => {
            console.log(inputValue, treeNode)
        }

        return {
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            handleChange,
            editorRef,
            value,
            treeData,
            SHOW_PARENT,

            filterTreeNode,

            roleTreeRef, //选择器
        }
    },
})
</script>
