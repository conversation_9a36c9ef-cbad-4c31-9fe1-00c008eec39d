<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'message_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'message_read'" class="btn" @click="read">批量已读</Button>
        <Button danger type="primary" v-auth="'message_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-message-lists/page"
        deleteApi="/api/hr-message-lists/deletes"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        @getData2="getData2"
        :params="params"
    >
        <template #icon="{ record }">
            <div class="table-title">
                <div>
                    <VerticalAlignTopOutlined v-if="record.top == 1" />
                </div>
                <span class="titles">{{ record.title }}</span>
            </div>
        </template>
        <template #customName> </template>
        <template #operation="{ record }">
            <!-- <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" /> -->
            <Button type="primary" size="small" v-auth="'message_see'" @click="showModal(record)" class="show">查看</Button>
            <Button v-if="record.top == 0" v-auth="'message_Stick'" type="primary" size="small" @click="Stick(record)">
                置顶
            </Button>
            <Button v-else type="primary" v-auth="'message_inaesthetic'" size="small" @click="inaesthetic(record)">
                取消置顶
            </Button>
        </template>
    </BasicTable>
    <!-- 查看弹框 -->
    <BasicEditModalSlot
        class="aaa"
        centered
        title="查看消息详情"
        v-model:visible="visible"
        @ok="handleOk"
        width="800px"
        :footer="null"
    >
        <div class="title">
            <p>{{ watchData.title }}</p>
            <p class="main" v-if="watchData.realName !== null">
                {{ watchData.realName }}:&nbsp;&nbsp; {{ watchData.createdDate }}
            </p>
            <p class="main" v-else>{{ watchData.createdBy }}:&nbsp;&nbsp; {{ watchData.createdDate }}</p>
        </div>

        <div class="content" v-html="watchData.content"></div>
    </BasicEditModalSlot>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import { VerticalAlignTopOutlined } from '@ant-design/icons-vue'
import modal from './modal.vue'
import { message } from 'ant-design-vue'
import useAppStore from '/@/store/modules/app'

export default defineComponent({
    components: { MyModal: modal, VerticalAlignTopOutlined },
    setup() {
        //筛选
        const params = ref<{}>({})
        // 获取查看数据
        const watchData = ref<object>({})
        // 获取全部状态
        let statesTypeList = ref<LabelValueOptions>([
            {
                label: '未读',
                value: 0,
            },
            {
                label: '已读',
                value: 1,
            },
        ])
        // 获取类型
        let expenseTypeList = ref<LabelValueOptions>([])
        const getData2 = (data) => {
        }
        onMounted(() => {
            request.get('/api/hr-message-liststype').then((res) => {
                expenseTypeList.value = res.map((item) => {
                    return { label: item, value: item }
                })
            })
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '标题',
                key: 'title',
            },
            {
                type: 'select',
                label: '类型',
                key: 'typeList',
                options: expenseTypeList,
                multiple: true,
            },
            {
                type: 'select',
                label: '状态',
                key: 'statesList',
                options: statesTypeList,
                multiple: true,
            },

            {
                type: 'daterange',
                label: '创建时间',
                key: 'contractStartDateQuery',
            },
        ]

        //表格dom
        const tableRef = ref()

        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '标题',
                dataIndex: 'title',
                align: 'center',
                width: 350,
                slots: { customRender: 'icon' },
            },
            {
                title: '接收对象',
                dataIndex: 'roleName',
                align: 'center',
                width: 250,
                ellipsis: true,
            },
            {
                title: '类型',
                dataIndex: 'type',
                align: 'center',
                width: 100,
            },
            {
                title: '状态',
                dataIndex: 'states',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return text ? '已读' : '未读'
                },
            },
            {
                title: '创建人',
                dataIndex: 'realName',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    if (record.realName === null) {
                        return record.createdBy
                    } else {
                        return record.realName
                    }
                },
            },
            {
                title: '创建时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 180,
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(row.id)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增消息')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增消息'
            currentValue.value = null
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增消息'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增消息')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        // 查看
        const visible = ref<boolean>(false)

        const showModal = ({ id }) => {
            request.get(`/api/hr-message-lists?id=${id}`).then((res) => {
                watchData.value = res
                tableRef.value.refresh()
                useAppStore().setReadCount()
            })

            visible.value = true
        }
        const handleOk = () => {
            visible.value = false
        }

        // 置顶
        const Stick = ({ id }) => {
            request.post('/api/hr-message-listsTop', { id, top: 1 }).then((res) => {
                tableRef.value.refresh()
            })
        }
        // 取消置顶
        const inaesthetic = ({ id }) => {
            request.post('/api/hr-message-listsTop', { id, top: 0 }).then((res) => {
                tableRef.value.refresh()
            })
        }

        // 选中的ID
        const applyIdList = ref<any>([])
        // 多选
        const selectedRowsArr = (item) => {
            let ids: (string | number)[] = []
            ids = item.map((val) => {
                return val.id
            })
            applyIdList.value = ids
        }
        // 批量已读
        const read = () => {
            if (applyIdList.value.length === 0) {
                return message.warning('请最少选择一条数据')
            }
            request.post('/api/hr-message-lists/updateBatch', applyIdList.value).then((res) => {
                tableRef.value.refresh()
                useAppStore().setReadCount()
            })
        }

        return {
            getData2,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            watchData,
            searchData,
            tableRef,
            createRow,

            deleteRow,
            showModal,
            visible,
            handleOk,
            applyIdList,
            selectedRowsArr, //多选
            Stick, //置顶
            inaesthetic, //取消置顶
            read, //批量已读

            //事件

            statesTypeList,
            //操作按钮
        }
    },
})
</script>

<style scoped lang="less">
.btn {
    background: #3eb889;
    border: none;
}
.title {
    width: 100%;
    height: 64px;
    text-align: center;
    // line-height: 64px;
    border-bottom: 1px solid rgba(224, 219, 219, 0.85);
    font-size: 20px;
    font-weight: 500;
    padding-bottom: 64px;
}
.content {
    min-height: 400px;
    padding-top: 20px;
}
.main {
    text-align: end;
    font-size: 14px;
    padding-right: 10px;
}
.show {
    margin-right: 5px;
}
.data-v-741aec9d {
    width: 15px;
    height: 15px;
}
.table-title {
    display: flex;
    justify-content: start;
    width: 100%;
    div {
        color: red;
        width: 15px;
    }
    .titles {
        width: 90%;
        text-align: center;
    }
}
</style>
