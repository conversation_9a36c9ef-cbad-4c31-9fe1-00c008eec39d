<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'850px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '80px' } }"
            :rules="rules"
            style="max-height: 60vh"
            class="form-flex"
        >
            <template v-for="ele in myOptions" :key="ele.name + 'configModal'">
                <template v-if="ele.name == 'parameterContent'">
                    <template v-for="(paramItem, index) in formData.parameterContent" :key="index + 'parameterContent'">
                        <FormItem
                            :label="ele.label + (index + 1)"
                            :name="['parameterContent', index]"
                            v-if="formData.dateIndex - 1 !== index"
                        >
                            <Input
                                v-model:value="formData.parameterContent[index]"
                                :placeholder="`请输入${ele.label + (index + 1)}`"
                                @change="(value) => paramsOnChange(value, index)"
                            />
                        </FormItem>
                        <FormItem v-else :label="ele.label + (index + 1)" :name="['parameterContent', index]">
                            <InputNumber
                                v-model:value="formData.parameterContent[index]"
                                :placeholder="`请输入${ele.label + (index + 1)}`"
                                @change="(value) => paramsOnChange(value, index)"
                            />
                            <span class="postfixSlot">
                                <span class="unit"> 天 </span>
                                <Tooltip color="rgba(255,255,255,.7)" placement="topRight" arrowPointAtCenter>
                                    <template #title>
                                        <p style="color: rgba(0, 0, 0, 1); width: 240px">
                                            请设置计算限期天数，例如设置3天，将按短信发送日期+3天显示。
                                        </p>
                                    </template>
                                    <QuestionCircleOutlined :style="{ color: '#999999' }" />
                                </Tooltip>
                            </span>
                        </FormItem>
                    </template>
                </template>
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]" v-else />
            </template>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel" class="btn">取消</Button>
            <Button key="submit" @click="confirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
// import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import { validatePhone } from '/@/utils/format'
import { templateTypeOptions, statusOptions } from '/@/utils/dictionaries'
import useSmsStore from '/@/store/modules/sms'
import moment from 'moment'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'ConfigModal',
    components: { QuestionCircleOutlined },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const SMSState = computed(() => {
            return useSmsStore().getSms
        })
        //表单数据
        let myOptions = ref<valuesAndRules[]>([
            {
                label: '模板ID',
                name: 'templateId',
                disabled: true,
                required: false,
            },
            {
                label: '模板类型',
                type: 'select',
                name: 'templateType',
                disabled: true,
                required: false,
                options: templateTypeOptions,
            },
            {
                label: '状态',
                name: 'status',
                type: 'select',
                disabled: true,
                required: false,
                options: statusOptions,
            },
            {
                label: '标题',
                name: 'sendTitle',
                required: false,
                disabled: true,
            },
            /* {
                label: '日期参数',
                name: 'date_index',
                required: false,
                type: 'number',
                ruleType: 'number',
                integerOnly: true,
                width: '30%',
            }, */
            {
                label: '模板内容',
                name: 'sendContent',
                type: 'textarea',
                disabled: true,
                required: false,
                width: '100%',
            },
            {
                label: '参数',
                name: 'parameterContent',
                ruleType: 'array',
                disabled: true,
                required: false,
                default: [],
            },
            {
                label: '短信预览',
                name: 'smsContent',
                type: 'textarea',
                disabled: true,
                required: false,
                width: '100%',
            },
        ])
        const { visible } = toRefs(props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, SMSState.value) }
                // formData.value.date_index = SMSState.value.dateIndex
            }
        })
        const getSmsContent = () => {
            let lastStr = formData.value.sendContent
            formData.value.parameterContent.forEach((item, ind) => {
                lastStr = lastStr.replace(`{${ind + 1}}`, item)
            })
            return lastStr
        }
        const paramsOnChange = (e, index) => {
            if (formData.value.dateIndex == index + 1) {
                let date = moment().add(formData.value.parameterContent[index], 'days').format('YYYY年MM月DD日')
                let lastStr = formData.value.sendContent
                formData.value.smsContent = lastStr.replace(`{${index + 1}}`, date)
            } else {
                let lastStr = getSmsContent()
                formData.value.smsContent = lastStr.replace(`{${index + 1}}`, formData.value.parameterContent[index])
            }
        }
        // cancel handle
        const cancel = () => {
            resetData()
            emit('cancel', 1)
        }

        // confirm handle
        const confirm = () => {
            emit('confirm', 'config')
            useSmsStore().setSmsStateObj(formData.value)
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        return {
            cancel,
            confirm,
            rules,
            formData,
            myOptions,
            formInline,
            paramsOnChange,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
    .postfixSlot {
        display: flex;
        align-items: center;
        .unit {
            margin: 0 5px;
        }
    }
}
</style>
