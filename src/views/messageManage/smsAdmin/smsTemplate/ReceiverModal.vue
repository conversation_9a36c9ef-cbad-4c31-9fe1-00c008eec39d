<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'900px'">
        <div class="btns">
            <Button type="primary" @click="showModal('staff')">新增</Button>
            <Button type="primary" class="sendBtn" @click="showModal('import')">导入</Button>
            <Button type="primary" class="delBtn" @click="batchDel">批量删除</Button>
        </div>
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRef"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="columns"
            :data-source="currentReceiverList"
            :row-key="(record) => record.phone"
            :pagination="pagination"
            :loading="loading"
            :row-selection="selectionRowConfig"
            @change="tableChange"
        />
        <template #footer>
            <Button @click="confirm" type="primary" class="btn">确认</Button>
        </template>
    </BasicEditModalSlot>
    <ImportModal
        v-model:visible="importVisible"
        :temUrl="'/api/hr-sms-templates/batch-templates'"
        :importUrl="'/api/hr-sms-templates/batch-import'"
        :downTempMethod="'post'"
        @getResData="searchData"
    />
</template>

<script lang="ts">
import { ref, defineComponent, computed, watch, toRefs } from 'vue'
import useSmsStore from '/@/store/modules/sms'
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
export default defineComponent({
    name: 'ReceiverModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible } = toRefs(props)
        const { resetReceiverOptions, removeSomeReceiverOptions, setReceiverOptions } = useSmsStore()
        const receiverList = computed(() => {
            return useSmsStore().getReceiverOptions
        })
        const currentReceiverList = ref([])
        // 表格dom
        const tableRef = ref()
        // 选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const loading = ref(false)
        const pagination = ref<any>({
            current: 1,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '30', '50', '100', '200', '300'],
            total: 0,
        })
        watch(
            receiverList,
            (val, old) => {
                if (val.length) getTableData()
                else refresh(1)
            },
            {
                deep: true,
            },
        )
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }
        const refresh = (page = pagination.value.current) => {
            pagination.value.current = page
            getTableData()
        }
        const searchData = async (res) => {
            setReceiverOptions(getImportDistinctArr(res.hrTalentStaffDTOList))
            refresh(1)
        }
        const getImportDistinctArr = (arr) => {
            let tempArr: inObject = []
            arr.forEach((el) => {
                let temp = receiverList.value.find((ele) => {
                    return ele.phone == el.phone
                })
                if (!temp) tempArr.push(el)
            })
            return tempArr
        }
        const importVisible = ref(false)
        // 表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 130,
                sorter: (a, b) => a.clientName.length - b.clientName.length,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 120,
                sorter: (a, b) => a.name.length - b.name.length,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 140,
                sorter: (a, b) => a.certificateNum - b.certificateNum,
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
                width: 100,
                sorter: (a, b) => a.phone - b.phone,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatusLabel',
                align: 'center',
                width: 90,
                sorter: (a, b) => a.staffStatus - b.staffStatus,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelTypeLabel',
                align: 'center',
                width: 90,
                sorter: (a, b) => a.personnelType - b.personnelType,
            },
        ]
        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current = current
            pagination.value.pageSize = pageSize
            getTableData()
        }
        const getTableData = async () => {
            loading.value = true
            try {
                currentReceiverList.value =
                    receiverList.value.slice(
                        (pagination.value.current - 1) * pagination.value.pageSize,
                        pagination.value.current * pagination.value.pageSize,
                    ) || []

                pagination.value.total = receiverList.value.length || 0
                // 复选框置空
                selectedRowsArr.value = []
                selectedRowKeysArr.value = []
            } finally {
                loading.value = false
            }
        }
        // cancel handle
        const cancel = () => {
            emit('cancel', 1)
            resetReceiverOptions()
        }

        const showModal = (type) => {
            if (type == 'staff') emit('confirm', type)
            else importVisible.value = true
        }
        const batchDel = () => {
            removeSomeReceiverOptions(selectedRowsArr.value)
        }
        const confirm = () => {
            emit('cancel', 1)
        }

        return {
            cancel,
            showModal,
            batchDel,
            searchData,
            confirm,
            tableChange,

            columns,
            tableRef,
            importVisible,
            receiverList,
            currentReceiverList,
            selectedRowsArr,
            loading,
            pagination,
            selectionRowConfig,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
:deep(.ant-modal-body) {
    padding-top: 0 !important;
}
.sendBtn {
    background-color: @success-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
