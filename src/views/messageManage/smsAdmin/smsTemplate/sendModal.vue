<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'800px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '120px' } }"
            :rules="rules"
            style="max-height: 60vh"
            class="form-flex"
        >
            <template v-for="ele in myOptions" :key="ele.name + 'sendModal'">
                <template v-if="ele.name == 'parameterContent'">
                    <template v-for="(paramItem, index) in formData.parameterContent" :key="index + 'parameterContent'">
                        <FormItem :label="ele.label + (index + 1)" :name="['parameterContent', index]">
                            <Input
                                v-model:value="formData.parameterContent[index]"
                                :placeholder="`请输入${ele.label + (index + 1)}`"
                                @change="(value) => paramsOnChange(value, index)"
                            />
                        </FormItem>
                    </template>
                </template>
                <template v-else-if="ele.name == 'receiver'">
                    <FormItem :label="ele.label" name="receiver" :style="`width:${ele.width}`">
                        <Select
                            v-model:value="formData.receiver"
                            mode="multiple"
                            :maxTagCount="7"
                            style="width: 100%; margin-right: 8px"
                            :placeholder="`请选择${ele.label}`"
                            :disabled="ele.disabled"
                        />
                        <Button @click="addReceiver" type="primary" class="btn" size="small">添加对象</Button>
                    </FormItem>
                </template>
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]" v-else />
            </template>
            <div class="tips" v-if="receiverOptions.length > 1">
                <WarningOutlined />
                {{ '系统检测到当前接收对象为多个，请不要填写<称呼类>参数' }}
            </div>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel" class="btn">取消</Button>
            <Button key="submit" @click="confirm" type="primary" class="btn">发送预览</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { getValuesAndRules } from '/@/utils/index'
import { WarningOutlined } from '@ant-design/icons-vue'
import { ref, defineComponent, toRefs, watch, computed } from 'vue'
import useSmsStore from '/@/store/modules/sms'
import moment from 'moment'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'SendModal',
    components: { WarningOutlined },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { resetReceiverOptions } = useSmsStore()
        const SMSState = computed(() => {
            return useSmsStore().getSms
        })
        const receiverOptions = computed(() => {
            return useSmsStore().getReceiverOptions
        })

        //表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '标题',
                name: 'sendTitle',
                required: false,
                disabled: true,
                width: '100%',
            },
            {
                label: '接收对象',
                name: 'receiver',
                required: true,
                disabled: true,
                ruleType: 'array',
                default: [],
                width: '100%',
            },
            {
                label: '模板内容',
                name: 'sendContent',
                type: 'textarea',
                required: false,
                disabled: true,
                width: '100%',
            },
            {
                label: '参数',
                name: 'parameterContent',
                ruleType: 'array',
                disabled: true,
                required: false,
                default: [],
            },
        ])
        //请求
        const { visible } = toRefs(props)

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, useSmsStore().getSms)
                if (formData.value.dateIndex) {
                    let date = moment().add(formData.value.dateNumber, 'days').format('YYYY年MM月DD日')
                    formData.value.parameterContent[formData.value.dateIndex - 1] = date
                }
                if (receiverOptions.value.length) {
                    formData.value.receiver = receiverOptions.value.map((el) => {
                        return el.name
                    })
                } else formData.value.receiver = []
            }
        })

        const addReceiver = () => {
            emit('confirm', 'receiver')
        }

        // cancel handle
        const cancel = () => {
            emit('cancel', 1)
            resetData()
            resetReceiverOptions()
        }
        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    // send
                    useSmsStore().setSmsStateObj(formData.value)
                    emit('confirm', 'send')
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const getSmsContent = () => {
            let lastStr = formData.value.sendContent
            formData.value.parameterContent.forEach((item, ind) => {
                lastStr = lastStr.replace(`{${ind + 1}}`, item)
            })
            return lastStr
        }
        const paramsOnChange = (e, index) => {
            let lastStr = getSmsContent()
            formData.value.smsContent = lastStr.replace(`{${index + 1}}`, formData.value.parameterContent[index])
        }

        return {
            cancel,
            confirm,
            rules,
            formData,
            myOptions,
            formInline,
            receiverOptions,
            paramsOnChange,
            addReceiver,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    .tips {
        font-size: 12px;
        font-weight: bold;
        color: @dangerous-color;
        margin-left: 120px;
    }
    :deep(.ant-form-item) {
        width: 33.33%;
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
        align-items: center;
    }
    .postfixSlot {
        display: flex;
        align-items: center;
        .unit {
            margin: 0 5px;
        }
    }
}
</style>
