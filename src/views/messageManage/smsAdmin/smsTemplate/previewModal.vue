<template>
    <BasicEditModalSlot :visible="visible" :title="title" @cancel="cancel" :width="'800px'" :zIndex="1009">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '80px' } }" class="form-flex">
            <template v-for="ele in myOptions" :key="ele.name + 'previewModal'">
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]" />
            </template>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel" class="btn">取消</Button>
            <Button key="submit" @click="confirm" type="primary" class="btn">确认发送</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { getValuesAndRules } from '/@/utils/index'
import { ref, defineComponent, computed, watch, toRefs } from 'vue'
import useSmsStore from '/@/store/modules/sms'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'PreviewModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        //表单数据
        const SMSState = computed(() => {
            return useSmsStore().getSms
        })
        const { visible } = toRefs(props)
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '短信预览',
                name: 'smsContent',
                type: 'textarea',
                required: false,
                disabled: true,
                width: '100%',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData } = getValuesAndRules(myOptions.value)
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, SMSState.value) }
            }
        })
        // cancel handle
        const cancel = () => {
            emit('cancel', 2)
            resetData()
        }
        // confirm handle
        const confirm = () => {
            emit('confirm', 'send')
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        return {
            cancel,
            confirm,
            formInline,
            myOptions,
            formData,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
</style>
