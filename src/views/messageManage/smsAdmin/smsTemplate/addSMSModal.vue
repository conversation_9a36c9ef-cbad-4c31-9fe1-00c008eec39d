<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'850px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '80px' } }"
            :rules="rules"
            style="max-height: 60vh"
            class="form-flex"
        >
            <div class="topTip">
                <div>模板规则：</div>
                <div>
                    本平台短信模板应与容联·云通讯平台（https://www.yuntongxun.com）的短信模板保持完全一致，否则短信将无法发送。
                </div>
            </div>
            <template v-for="ele in myOptions" :key="ele.name + 'addModal'">
                <MyFormItem :width="ele.width" :item="ele" v-model:value="formData[ele.name]">
                    <template #templateType>
                        <RadioGroup v-model:value="formData[ele.name]" :options="templateTypeOptions" @change="ele.onChange" />
                    </template>
                    <template #status>
                        <RadioGroup v-model:value="formData[ele.name]" :options="statusOptions" @change="ele.onChange" />
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <template #footer>
            <Button @click="cancel" class="btn">取消</Button>
            <Button @click="confirm" type="primary" class="btn">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent } from 'vue'
import { getValuesAndRules } from '/@/utils/index'
import { templateTypeOptions, statusOptions } from '/@/utils/dictionaries'
import { valuesAndRules } from '/#/component'
export default defineComponent({
    name: 'AddModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        //表单数据
        let myOptions = ref<valuesAndRules[]>([
            {
                label: '模板ID',
                name: 'templateId',
                width: '100%',
                required: true,
            },
            {
                label: '模板类型',
                name: 'templateType',
                type: 'slots',
                slots: 'templateType',
                default: 0,
                ruleType: 'number',
                width: '100%',
                required: true,
            },
            {
                label: '状态',
                name: 'status',
                type: 'slots',
                slots: 'status',
                ruleType: 'number',
                default: 0,
                width: '100%',
                required: true,
            },
            {
                label: '标题',
                name: 'sendTitle',
                width: '100%',
                required: true,
            },
            {
                label: '模板内容',
                name: 'sendContent',
                type: 'textarea',
                width: '100%',
                required: true,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        // cancel handle
        const cancel = () => {
            resetData()
            emit('cancel', 1)
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    let parameterContent: string[] = []
                    const { length } = formData.value.sendContent.split('{')
                    for (let i = 0; i < length - 1; i++) {
                        parameterContent.push('')
                    }
                    // add
                    emit('confirm', { ...formData.value, parameterContent: JSON.stringify(parameterContent) })
                    resetData()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const resetData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        return {
            cancel,
            confirm,
            rules,
            formData,
            myOptions,
            formInline,
            templateTypeOptions,
            statusOptions,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
    .topTip {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100px;
        line-height: 20px;
        border-radius: 10px;
        background-color: rgba(255, 248, 248, 100);
        text-align: center;
        border: 1px solid rgba(254, 104, 104, 100);
        font-size: 14px;
        text-align: left;
        color: rgba(51, 51, 51, 100);
        padding: 15px;
        margin: 15px 0;
        box-sizing: border-box;
    }
    .postfixSlot {
        display: flex;
        align-items: center;
        .unit {
            margin: 0 5px;
        }
    }
}
</style>
