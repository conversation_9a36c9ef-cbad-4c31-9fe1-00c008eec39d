<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'smsTemplate_add'" @click="showModal('add', null)">新增</Button>
        <Button type="primary" v-auth="'smsTemplate_export'" @click="exportSMS">{{ exportText }}</Button>
        <Button type="primary" v-auth="'smsTemplate_del'" class="delBtn" @click="batchDel">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-sms-templates/page"
        deleteApi="/api/hr-sms-templates/deletes"
        exportUrl="/api/hr-sms-templates/export"
        :params="params"
        :columns="columns"
        :useIndex="true"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <Button
                type="primary"
                size="small"
                v-auth="'smsTemplate_config'"
                @click="showModal('config', record)"
                v-if="record.status === 0"
            >
                配置
            </Button>
            &nbsp;
            <Button
                type="primary"
                danger
                :class="{ sendBtn: record.status === 1 }"
                size="small"
                v-auth="'smsTemplate_config'"
                @click="modalConfirm('status', record)"
            >
                {{ record.status === 1 ? '启用' : '禁用' }}
            </Button>
            &nbsp;
            <Button
                v-if="record.status === 0"
                type="primary"
                size="small"
                v-auth="'smsTemplate_send'"
                class="sendBtn"
                @click="showModal('send', record)"
            >
                发送
            </Button>
        </template>
    </BasicTable>

    <AddModal :visible="showAdd" :title="modalTitle" @cancel="modalCancel" @confirm="addSMSTemplate" />
    <ConfigModal :visible="showConfig" :title="modalTitle" @cancel="modalCancel" @confirm="showNextModal" />
    <SendModal :visible="showSend" :title="modalTitle" @cancel="modalCancel" @confirm="showNextModal" />
    <ConfigNextModal :visible="showWarn" :title="nextModalTitle" @cancel="modalCancel" @confirm="modalConfirm" />
    <PreviewModal :visible="showPreview" :title="nextModalTitle" @cancel="modalCancel" @confirm="modalConfirm" />
    <ReceiverModal :visible="showReceiver" :title="nextModalTitle" @cancel="receiverModalClose" @confirm="showNextModal" />
    <StaffModal :visible="showStaff" :title="nextModalTitle" @cancel="receiverModalClose" @confirm="modalConfirm" />
    <ErrorImportModal :visible="errorModalVisible" title="错误数据下载" @cancel="modalCancel" />
</template>

<script lang="ts">
import { computed, defineComponent, ref, watchEffect } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import { message } from 'ant-design-vue'
import AddModal from './addSMSModal.vue'
import ConfigModal from './configModal.vue'
import SendModal from './sendModal.vue'
import PreviewModal from './previewModal.vue'
import ReceiverModal from './ReceiverModal.vue'
import StaffModal from './StaffModal.vue'
import ErrorImportModal from './ErrorImportModal.vue'
import ConfigNextModal from './myConfigNextModal.vue'
import { templateTypeOptions, statusOptions } from '/@/utils/dictionaries'
import useSmsStore from '/@/store/modules/sms'
import downFile from '/@/utils/downFile'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'SMSTemplate',
    components: { AddModal, ConfigModal, SendModal, PreviewModal, ConfigNextModal, ReceiverModal, StaffModal, ErrorImportModal },
    setup() {
        const { setSmsState, resetReceiverOptions, setErrorData } = useSmsStore()
        const SMSState = ref(useSmsStore().getSms)
        const receiverOptions = computed(() => {
            return useSmsStore().getReceiverOptions
        })
        watchEffect(() => {
            SMSState.value = useSmsStore().getSms
        })
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '模板类型',
                key: 'templateTypeList',
                multiple: true,
                options: templateTypeOptions,
            },
            {
                type: 'string',
                label: '标题',
                key: 'sendTitle',
            },
            {
                type: 'select',
                label: '状态',
                key: 'statusList',
                multiple: true,
                options: statusOptions,
            },
            {
                type: 'string',
                label: '内容',
                key: 'sendContent',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '模板ID',
                dataIndex: 'templateId',
                align: 'center',
                width: 130,
            },
            {
                title: '模板类型',
                dataIndex: 'templateType',
                align: 'center',
                width: 140,
                customRender: ({ text }) => {
                    return templateTypeOptions.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '标题',
                dataIndex: 'sendTitle',
                align: 'center',
                width: 180,
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                customRender: ({ text }) => {
                    return statusOptions.find((item) => {
                        return text == item.value
                    })?.label
                },
                width: 90,
            },
            {
                title: '内容',
                dataIndex: 'sendContent',
                align: 'left',
                width: 500,
                customHeaderCell: (header) => {
                    header.align = 'center'
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 190,
                align: 'center',
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]

        const errorModalVisible = ref(false) // 错误数据弹窗
        const showAdd = ref(false) // 配置
        const showConfig = ref(false) // 配置
        const showSend = ref(false) // 发送
        const showWarn = ref(false) // 警告
        const showPreview = ref(false) // 预览
        const showReceiver = ref(false) // 接收对象
        const showStaff = ref(false) // 添加对象

        const modalTitle = ref('短信模板配置') // 弹窗名称
        const nextModalTitle = ref('警告') // 二级弹窗名称

        // modal关闭
        const modalCancel = (step) => {
            if (step == 1) {
                showAdd.value = false
                showConfig.value = false
                showSend.value = false
                showWarn.value = false
                showPreview.value = false
                modalTitle.value = '短信模板配置'
                nextModalTitle.value = '警告'
            } else {
                showWarn.value = false
                showPreview.value = false
                modalTitle.value = '短信模板配置'
                nextModalTitle.value = '警告'
            }
            errorModalVisible.value = false
        }

        const receiverModalClose = (step) => {
            if (step == 1) {
                showReceiver.value = false
                showSend.value = true
                modalTitle.value = '短信发送'
            } else {
                showReceiver.value = true
                showStaff.value = false
                nextModalTitle.value = '接收对象'
            }
        }

        const modalConfirm = (type, record) => {
            if (type == 'config') {
                request
                    .put('/api/hr-sms-templates', {
                        id: SMSState.value.id,
                        dateNumber: Number(SMSState.value.parameterContent[SMSState.value.dateIndex - 1]),
                        parameterContent: JSON.stringify(SMSState.value.parameterContent),
                        // dateIndex: SMSState.value.date_index,
                    })
                    .then(() => {
                        message.success('配置成功!')
                        modalCancel(1)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else if (type == 'send') {
                request
                    .post('/api/hr-send-sms/template', {
                        templateId: SMSState.value.templateId,
                        phoneList: receiverOptions.value.map((el) => {
                            return {
                                name: el.name,
                                phone: el.phone,
                            }
                        }),
                        dateList: SMSState.value.parameterContent,
                    })
                    .then((res) => {
                        resetReceiverOptions()
                        modalCancel(1)
                        if (!res.length) {
                            message.success('发送成功')
                        } else {
                            console.log(res)
                            errorModalVisible.value = true
                            setErrorData(res)
                        }
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            } else {
                request
                    .put('/api/hr-sms-templates', {
                        id: record.id,
                        status: Number(record.status) == 0 ? 1 : 0,
                    })
                    .then(() => {
                        message.success(`已${Number(record.status) == 0 ? '禁用' : '启用'}!`)
                        tableRef.value.refresh()
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }
        }

        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        // 导出
        const exportSMS = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 批量删除
        const batchDel = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }
        // 添加
        const addSMSTemplate = (record) => {
            request
                .post('/api/hr-sms-templates', record)
                .then((res) => {
                    console.log(res)
                    message.success('添加短信模板成功!')
                    modalCancel(1)
                    searchData()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 显示一级弹窗
        const showModal = (type, record) => {
            if (type == 'config') {
                // 配置
                showConfig.value = true
                modalTitle.value = '短信模板配置'
            } else if (type == 'send') {
                // 发送
                showSend.value = true
                modalTitle.value = '短信发送'
            } else {
                // 新增
                showAdd.value = true
                modalTitle.value = '新增短信模板'
            }
            type !== 'add' && setSmsState({ ...record })
        }
        // 显示二级弹窗
        const showNextModal = (type) => {
            if (type == 'config') {
                showWarn.value = true
                nextModalTitle.value = '警告'
            } else if (type == 'receiver') {
                showSend.value = false
                showReceiver.value = true
                nextModalTitle.value = '接收对象'
            } else if (type == 'staff') {
                showReceiver.value = false
                showStaff.value = true
                nextModalTitle.value = '添加对象'
            } else {
                showPreview.value = true
                nextModalTitle.value = '短信预览'
            }
        }

        return {
            selectedRowsArr,
            exportText,
            errorModalVisible,
            params,
            options,

            showAdd,
            showConfig,
            showSend,
            showWarn,
            showPreview,
            showReceiver,
            showStaff,
            modalTitle,
            nextModalTitle,

            columns,
            tableRef,

            searchData,
            modalCancel,
            modalConfirm,
            addSMSTemplate,
            exportSMS,
            batchDel,
            showModal,
            showNextModal,
            receiverModalClose,
        }
    },
})
</script>

<style scoped lang="less">
.sendBtn {
    background-color: @success-color;
    border: none;
}
.delBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
