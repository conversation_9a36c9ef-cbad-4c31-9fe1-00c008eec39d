<template>
    <BasicEditModalSlot :visible="visible" :title="title" @cancel="onCancel" :zIndex="1009" :footer="null">
        <div class="wrapper">
            <p>
                本次短信批量发送错误数据共
                <span>{{ resData.length }}</span>
                条
            </p>
            <div>
                <Button class="download_btn" danger type="primary" @click="downloadErrorData"> 下载错误数据 </Button>
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { defineComponent, computed } from 'vue'
import useSmsStore from '/@/store/modules/sms'
import downFile from '/@/utils/downFile'

export default defineComponent({
    name: 'ErrorImportModal',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const resData = computed(() => {
            return useSmsStore().getErrorData
        })

        const downloadErrorData = () => {
            downFile('post', '/api/hr-sms-templates/export-batch', '', resData.value).then((res) => {
                emit('cancel')
            })
        }
        const onCancel = () => {
            emit('cancel')
        }

        return {
            resData,
            onCancel,
            downloadErrorData,
        }
    },
})
</script>

<style scoped lang="less">
.wrapper {
    width: 100%;
    padding: 8px 15px;
    font-size: 16px;
    font-weight: bold;
    span {
        color: #ff0000;
        font-weight: bold;
        margin: 0 10px;
    }
    div {
        display: flex;
        justify-content: center;
        align-self: center;
        margin-top: 20px;
    }
}
</style>
