<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'600px'" :zIndex="1009">
        <div class="content">
            <div class="img-wrapper">
                <img src="~//@/assets/hint.png" alt="" />
            </div>
            <div class="tips">该配置将应用于后续所有使用该模板的短信，是否继续？</div>
        </div>
        <template #footer>
            <div class="btns_wrapper">
                <Button key="back" @click="cancel" class="btn">取消</Button>
                <Button key="submit" @click="confirm" type="primary" class="btn">继续</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'ConfigNextModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        // cancel handle
        const cancel = () => {
            emit('cancel', 2)
        }
        // confirm handle
        const confirm = () => {
            emit('confirm', 'config')
        }

        return {
            cancel,
            confirm,
        }
    },
})
</script>
<style scoped lang="less">
.content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img-wrapper {
        width: 210px;
        height: 210px;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .btns_wrapper {
        display: flex;
        justify-content: center;
    }
    .tips {
        font-weight: 600;
    }
}
</style>
