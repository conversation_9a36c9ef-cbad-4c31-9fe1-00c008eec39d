<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'900px'">
        <SearchBar v-model="params" :options="options" @change="searchData" />
        <Button type="primary" @click="selectSMS" style="margin-bottom: 20px">{{ selectText }}</Button>
        <BasicTable
            ref="tableRef"
            :params="{ ...params, izDefault: false }"
            api="/api/hr-talent-staffs/page"
            :columns="columns"
            @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
            :customSelectedKeys="customSelectedKeys"
            :checkboxProps="checkboxProps"
            :tableDataFormat="tableDataFormat"
        />
        <template #footer>
            <Button @click="confirm" type="primary" class="btn">添加到发送对象</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import useSmsStore from '/@/store/modules/sms'
import { getDynamicText } from '/@/utils'
import request from '/@/utils/request'
export default defineComponent({
    name: 'StaffModal',
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { setReceiverOptions } = useSmsStore()
        const staffStateList = ref<LabelValueOptions>([]) // 员工状态
        const staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        const customSelectedKeys = ref([])
        const receiverList = computed(() => {
            return useSmsStore().getReceiverOptions
        })
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('staffStates', '', 'get', true)
                .then((data: inObject[]) => {
                    staffStateList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            dictionaryDataStore()
                .setDictionaryData('staffType', '', 'get', true)
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIds',
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '手机号码',
                key: 'phone',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                multiple: true,
                options: staffStateList,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                multiple: true,
                options: staffTypeList,
            },
        ]

        const selectText = computed(() => {
            return getDynamicText('选中', params.value, [])
        })

        const selectSMS = async () => {
            request
                .post('/api/hr-talent-staff/not-page', { ...params.value })
                .then((res) => {
                    if (res.length) {
                        let arr: any = []
                        res.forEach((ele) => {
                            const temp = receiverList.value.find((el) => {
                                return el.id == ele.id
                            })
                            if (!temp) arr.push(ele)
                        })
                        selectedRowsArr.value = arr
                        customSelectedKeys.value = arr.map((item) => item.id)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        // 选择行
        const selectedRowsArr = ref<any[]>([])
        // 表格数据
        const columns = [
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 130,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 120,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 140,
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
                width: 100,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 90,
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 90,
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
            },
        ]
        const tableDataFormat = (data) => {
            data.map((item) => {
                item.disabled = !!receiverList.value.find((el) => {
                    return el.id == item.id
                })
                return item
            })

            return data
        }

        const checkboxProps = (record: inObject) => {
            return {
                disabled: record.disabled,
            }
        }
        // cancel handle
        const cancel = () => {
            emit('cancel', 2)
        }

        // confirm handle
        const confirm = () => {
            emit('cancel', 2)
            setReceiverOptions(selectedRowsArr.value)
            params.value = {}
            customSelectedKeys.value = []
            tableRef.value.checkboxReset()
        }
        return {
            cancel,
            confirm,
            searchData,
            checkboxProps,
            tableDataFormat,
            selectSMS,

            params,
            options,
            columns,
            tableRef,
            selectedRowsArr,
            selectText,
            customSelectedKeys,
        }
    },
})
</script>
<style scoped lang="less">
:deep(.ant-modal-body) {
    padding-top: 0;
}
</style>
