<template>
    <div class="btns">
        <Button type="primary" v-auth="'documentType_add'" @click="createRow">新增</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/menus/list"
        :params="{}"
        :columns="columns"
        :isPage="false"
        :tableDataFormat="tableDataFormat"
        :pageSize="50"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp;
            <Button type="primary" size="small" @click="createChild(record)">新增子菜单</Button>
            &nbsp;
            <Button :disabled="!!record?.children?.length" danger type="primary" size="small" @click="deleteRow(record)">
                删除
            </Button> -->
        </template>
    </BasicTable>

    <BasicEditModal
        :visible="showEdit"
        api="/api/menus"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :options="options"
        :defaultParams="defaultParams"
    />
</template>

<script lang="ts">
import { Modal } from 'ant-design-vue'
import { defineComponent, ref } from 'vue'
import { EditModalOption } from '/#/component'
import { getHaveAuthorityOperation } from '/@/utils'
import request from '/@/utils/request'
export default defineComponent({
    name: 'AuthRole',
    setup() {
        const tableRef = ref()
        const showEdit = ref(false)
        const modalTitle = ref('新增菜单')
        // 当前编辑的数据
        const currentValue = ref(null)
        const defaultParams = ref<any>({})
        const createRow = () => {
            modalTitle.value = '新增菜单'
            currentValue.value = null
            defaultParams.value = {
                pid: 0,
                hasDataAuthority: false,
            }
            showEdit.value = true
        }
        const editRow = (record) => {
            modalTitle.value = '编辑菜单'
            currentValue.value = { ...record }
            showEdit.value = true
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增菜单'
            currentValue.value = null
        }
        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
            showEdit.value = false
            currentValue.value = null
        }

        const createChild = (record) => {
            modalTitle.value = '新增子菜单'
            currentValue.value = null
            defaultParams.value = {
                pid: record.id,
                hasDataAuthority: false,
            }
            showEdit.value = true
        }

        const options: EditModalOption[] = [
            {
                label: '菜单类型',
                name: 'menuType',
                type: 'select',
                ruleType: 'number',
                option: [
                    {
                        label: '系统菜单',
                        value: 1,
                    },
                    {
                        label: '按钮',
                        value: 2,
                    },
                    // {
                    //     label: '按钮',
                    //     value: 3,
                    // },
                ],
            },
            {
                label: '菜单名称',
                name: 'title',
            },
            {
                label: '排序',
                type: 'number',
                name: 'sort',
            },
            {
                label: '唯一标识',
                name: 'permission',
            },
        ]

        const deleteRow = (row) => {
            Modal.confirm({
                title: '确认',
                content: '您确定要删除该条数据吗？',
                onOk() {
                    confirmDelete(row.id)
                },
                onCancel() {},
            })
        }
        const confirmDelete = async (id: string) => {
            await request.delete(`/api/menus/${id}`)
            tableRef.value.refresh(1)
        }

        const getTreeChoose = (data) => {
            for (const i in data) {
                if (data[i]?.menuList && data[i]?.menuList.length) {
                    data[i].children = data[i]?.menuList
                    getTreeChoose(data[i].menuList)
                }
            }
        }

        const tableDataFormat = (item) => {
            getTreeChoose(item)
            return item
        }

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'documentType_edit',
                    show: true,
                    click: editRow,
                },
                {
                    neme: '新增子菜单',
                    auth: 'documentType_create',
                    show: true,
                    click: createChild,
                },
                {
                    neme: '删除',
                    auth: 'documentType_delete',
                    show: true,
                    click: deleteRow,
                    type: 'delete',
                },
            ]),
        )

        return {
            deleteRow,
            defaultParams,
            options,
            modalConfirm,
            modalCancel,
            tableRef,
            showEdit,
            currentValue,
            modalTitle,
            createRow,
            editRow,
            createChild,
            tableDataFormat,
            columns: [
                {
                    title: '菜单名称',
                    dataIndex: 'title',
                    align: 'left',
                },
                {
                    title: '唯一标识',
                    dataIndex: 'permission',
                    align: 'center',
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    slots: { customRender: 'operation' },
                    width: 230,
                    fixed: 'right',
                },
            ],
            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less"></style>
