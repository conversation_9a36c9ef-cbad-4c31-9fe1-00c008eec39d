<template>
    <SearchBar v-model="searchParams" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/roles/page"
        :params="searchParams"
        :columns="columns"
        :useIndex="true"
        :pageSize="50"
        @selectedRowsArr="selectedRowsArr"
        :tableDataFormat="tableDataFormat"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <template v-if="record.roleKey != 'super_admin'">
                <template v-if="record.roleKey != 'customer_service_staff'">
                    <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
                    &nbsp;
                </template>

                <Button type="primary" size="small" @click="allotRow(record)">分配菜单</Button>

  
                <template v-if="record.isRemove">
                    &nbsp;
                    <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button>
                </template>
            </template>
            <template v-else>
                <Button type="primary" size="small" @click="editRow(record, 'see')">查看</Button>
            </template> -->
        </template>
    </BasicTable>

    <!-- <BasicEditModal
        :visible="showEdit"
        api="/api/roles"
        :title="modalTitle"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :options="options"
    /> -->
    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <AllotMenu :item="allotItem" :visible="showAllotMenu" @confirm="allotConfirm" @cancel="allotClose" />
</template>

<script lang="ts">
import { Modal, Tag } from 'ant-design-vue'
import { defineComponent, ref, h, onMounted } from 'vue'
import AllotMenu from './AllotMenu.vue'

import { valuesAndRules, SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import modal from './modal.vue'
import { getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'AuthRole',
    components: { AllotMenu, MyModal: modal },
    setup() {
        // 获取全部角色
        let roleDeptsList = ref<LabelValueOptions>([])
        onMounted(() => {
            // 所属部门
            request.get('/api/com-code-tables/getCodeTableByInnerName/department', {}).then((res) => {
                roleDeptsList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const searchParams = ref({})
        const searchOptions: SearchBarOption[] = [
            {
                //dai
                type: 'select',
                label: '所属部门',
                key: 'deptList',
                options: roleDeptsList,
                multiple: true,
            },
            {
                type: 'string',
                label: '角色名称/角色备注',
                key: 'roleName',
            },
        ]
        const searchData = () => {
            tableRef.value.refresh(1)
        }

        const selectedRowsArr = (item) => {
            console.log(item)
        }
        const viewType = ref('edit')
        const tableRef = ref()
        const showEdit = ref(false)
        const modalTitle = ref('新增角色')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增角色'
            currentValue.value = null
            viewType.value = 'edit'
        }
        const editRow = (record, type?) => {
            console.log(type)
            viewType.value = type || 'edit'
            showEdit.value = true
            modalTitle.value = '编辑角色'
            if (type == 'see') {
                modalTitle.value = '查看角色'
            }
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增角色'
            currentValue.value = null
        }
        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        const showAllotMenu = ref(false)
        const allotItem = ref(null)
        const allotRow = (record) => {
            allotItem.value = record
            showAllotMenu.value = true
        }
        // let customerType = ref<any>([])
        // request.get(`/api/com-code-tables/getCodeTableByInnerName/department`).then((item) => {
        //     customerType.value = item.map((item) => {
        //         return { label: item.itemName, value: item.itemValue }
        //     })
        // })
        const options: valuesAndRules[] = [
            {
                label: '角色名称',
                name: 'roleName',
            },
            {
                label: '所属部门',
                type: 'SelectDic',
                name: 'dept',
                // option: customerType,
                required: false,
                disType: 'department',
                disParentId: 151,
                ruleType: 'number',
            },
            {
                label: '角色备注',
                name: 'remark',
                required: false,
            },
            {
                label: '客户数据限制',
                name: 'isRestrictions',
                type: 'switch',
                default: false,
                required: false,
                checkText: '开启',
                unCheckText: '关闭',
            },

            {
                label: '职责介绍',
                name: 'introduce',
                required: false,
            },
            {
                label: '排序',
                name: 'sort',
                type: 'number',
                required: false,
            },
        ]

        const allotConfirm = () => {
            tableRef.value.refresh()
            showAllotMenu.value = false
        }

        const allotClose = () => {
            showAllotMenu.value = false
        }

        const deleteRow = (row) => {
            Modal.confirm({
                title: '确认',
                content: '您确定要删除该条数据吗？',
                onOk() {
                    confirmDelete(row.id)
                },
                onCancel() {},
            })
        }
        const confirmDelete = async (id: string) => {
            await request.post(`/api/roles/deletes`, [id])
            tableRef.value.refresh(1)
        }
        //             <template v-if="record.roleKey != 'super_admin'">
        //         <template v-if="record.roleKey != 'customer_service_staff'">
        //             <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
        //             &nbsp;
        //         </template>

        //         <Button type="primary" size="small" @click="allotRow(record)">分配菜单</Button>

        //         <!-- //删除 -->
        //         <template v-if="record.isRemove">
        //             &nbsp;
        //             <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button>
        //         </template>
        //     </template>
        //     <template v-else>
        //         <Button type="primary" size="small" @click="editRow(record, 'see')">查看</Button>
        //     </template>
        // </template>
        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'documentType_edit',
                    show: (record) => {
                        return record.isEdit
                    },
                    click: editRow,
                },
                {
                    neme: '查看',
                    auth: 'documentType_delete',
                    show: (record) => {
                        return !record.isEdit
                    },
                    click: (record) => editRow(record, 'see'),
                },
                {
                    neme: '删除',
                    auth: 'documentType_delete',
                    show: (record) => {
                        return record.isRemove
                    },
                    click: deleteRow,
                    type: 'delete',
                },
                {
                    neme: '分配菜单',
                    auth: 'documentType_delete',
                    show: (record) => {
                        // return true
                        return record.roleKey != 'super_admin' && record.roleKey != 'developer'
                    },
                    click: allotRow,
                },
            ]),
        )

        const tableDataFormat = (data) => {
            // fix: 生产环境看不到开发者
            return import.meta.env.DEV ? data : data.filter((i) => i.roleKey != 'developer')
        }

        return {
            tableDataFormat,
            //查询
            searchParams,
            searchOptions,
            searchData,

            deleteRow,
            confirmDelete,
            allotClose,
            allotConfirm,
            allotItem,
            showAllotMenu,
            options,
            modalConfirm,
            modalCancel,
            tableRef,
            showEdit,
            currentValue,
            modalTitle,
            createRow,
            editRow,
            allotRow,
            selectedRowsArr,
            columns: [
                {
                    title: '所属部门',
                    dataIndex: 'dept',
                    align: 'center',
                    customRender: ({ record }) => {
                        return record.deptName
                    },
                    width: 150,
                },

                {
                    title: '角色名称',
                    dataIndex: 'roleName',
                    align: 'center',
                    width: 150,
                },
                {
                    title: '角色备注',
                    dataIndex: 'remark',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '客户数据限制',
                    dataIndex: 'isRestrictions',
                    align: 'center',
                    customRender: ({ text }) => {
                        return h(
                            Tag,
                            {
                                color: text ? 'green' : 'red',
                            },
                            {
                                default: () => h('span', text ? '开启' : '关闭'),
                            },
                        )
                    },
                    width: 100,
                },
                {
                    title: '职责介绍',
                    dataIndex: 'introduce',
                    align: 'center',
                    width: 200,
                },
                {
                    title: '排序',
                    dataIndex: 'sort',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 230,
                    fixed: 'right',
                    slots: { customRender: 'operation' },
                },
            ],
            myOperation,
            viewType,
        }
    },
})
</script>

<style scoped lang="less"></style>
