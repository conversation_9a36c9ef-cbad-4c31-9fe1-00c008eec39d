<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="分配菜单">
        <Tree
            checkable
            autoExpandParent
            :replaceFields="{
                children: 'menuList',
                key: 'id',
            }"
            :checkStrictly="true"
            :tree-data="treeData"
            v-model:expandedKeys="expandedKeys"
            v-model:checkedKeys="checkedKeys"
        />
        <template #footer>
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" :loading="loading" @click="confirm">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import { Tree } from 'ant-design-vue'
import request from '/@/utils/request'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'

export default defineComponent({
    name: 'AllotMenu',
    components: {
        Tree,
    },
    props: {
        visible: <PERSON>olean,
        item: {
            type: Object,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const treeData = ref<TreeDataItem[]>([])
        const expandedKeys = ref<string[]>([])
        interface DefaultEvent {
            checked: (string | number)[]
            halfChecked: (string | number)[]
        }
        const checkedKeys = ref<DefaultEvent>({ checked: [], halfChecked: [] })

        const { visible, item } = toRefs(props)
        // open modal 赋值
        watch(visible, () => {
            visible.value && item.value?.id && getData()
        })
        const getData = async () => {
            // 嵌套获取tree中的choose
            const getTreeChoose = (data, result: string[] = []) => {
                for (const i in data) {
                    data[i].choose && result.push(data[i].id as string)
                    if (data[i].menuList && data[i].menuList.length) {
                        result = getTreeChoose(data[i].menuList, result)
                    }
                }
                return result
            }
            const data = await request.get(`/api/roles/${item.value?.id}/menus`)
            treeData.value = data
            const res = getTreeChoose(data)
            expandedKeys.value = res
            checkedKeys.value = { checked: res, halfChecked: [] }
        }
        const loading = ref(false)
        const confirm = async () => {
            console.log(checkedKeys.value)
            loading.value = true
            try {
                // await request.post(`/api/${item.value?.id}/menus`, checkedKeys.value)
                await request.post(`/api/roles/${item.value?.id}/menus`, checkedKeys.value.checked)
                emit('confirm')
                resetData()
            } finally {
                loading.value = false
            }
        }
        const cancel = () => {
            emit('cancel')
            resetData()
        }
        const resetData = () => {
            treeData.value = []
            expandedKeys.value = []
        }
        return {
            loading,
            treeData,
            confirm,
            cancel,
            expandedKeys,
            checkedKeys,
        }
    },
})
</script>
