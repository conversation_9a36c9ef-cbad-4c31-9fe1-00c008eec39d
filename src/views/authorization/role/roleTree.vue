<template>
    <div style="width: 100%">
        <div :id="roleTreeId" style="width: 100%">
            <TreeSelect
                ref="TreeSelect"
                class="myTreeSelect"
                v-model:value="dataValue"
                style="width: 100%"
                :tree-data="treeData"
                tree-checkable
                allow-clear
                showSearch
                :show-checked-strategy="SHOW_PARENT"
                treeNodeFilterProp="title"
                treeDefaultExpandAll
                :getPopupContainer="getPopupContainer"
                :placeholder="itemForm?.placeholder || (itemForm.disabled ? `暂无${itemForm.label}` : `请选择${itemForm.label}`)"
                :disabled="disabled ? disabled : itemForm.disabled"
            />
        </div>
    </div>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue'
import { TreeSelect } from 'ant-design-vue'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import request from '/@/utils/request'
function randomString(len) {
    len = len || 32
    var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678' /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
    var maxPos = $chars.length
    var pwd = ''
    for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return pwd
}
export default defineComponent({
    name: 'PostTree',
    components: {},
    props: {
        isAll: {
            type: Boolean,
            default: false,
        },
        value: {
            type: [Array, Object, String, Number],
            default: () => [],
        },
        itemForm: {
            type: Object,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:value'],
    setup(props, { emit }) {
        const roleTreeId = `roleTree${randomString(5)}`
        const { value } = toRefs(props)
        const SHOW_PARENT = TreeSelect.SHOW_PARENT

        const dataValue = computed({
            get: () => value.value,
            set: (val) => {
                emit('update:value', val)
            },
        })
        //请求
        let objDept = {}
        let arrDeptList: inObject[] = []

        // const TreeSelect = ref()
        const treeData = ref<TreeDataItem[]>([])
        onMounted(() => {
            request.get('/api/roles/list', {}).then((res) => {
                objDept = {}
                arrDeptList = res
                res.forEach((element) => {
                    if (!element?.dept) {
                        objDept['js' + element.id] = {
                            title: element.roleName,
                            value: element.id,
                            key: element.id,
                            children: [],
                        }
                        return
                    }
                    if (!objDept['qd' + element.dept]) {
                        objDept['qd' + element.dept] = {
                            title: element.deptName,
                            value: 'qd' + element.dept,
                            key: 'qd' + element.dept,
                            children: [],
                        }
                    }
                    objDept['qd' + element.dept].children.push({
                        title: element.roleName,
                        value: element.id,
                        key: element.id,
                        children: [],
                    })
                })
                treeData.value = [
                    {
                        title: '全部',
                        value: 'qdall',
                        key: 'qdall',
                        children: Object.values(objDept),
                    },
                ]
            })
        })
        const getData = () => {
            let newRoleList: any[] = []
            let newDept: any[] = []
            console.log(roleTreeId)
            let deptDom = document
                .getElementById(roleTreeId)
                ?.getElementsByClassName('ant-select-selector')[0]
                ?.getElementsByClassName('ant-select-selection-item')
            if (deptDom?.length) {
                for (let i = 0; i < deptDom.length; i++) {
                    let dept = deptDom[i].getElementsByClassName('ant-select-selection-item-content')[0].innerHTML
                    newDept.push(dept)
                }
            }

            dataValue.value?.forEach((element: any) => {
                if (element == 'qdall') {
                    // newDept.push('全部')
                    newRoleList = arrDeptList.map((item: inObject) => {
                        return item.id
                    })
                } else if (element.toString()?.indexOf('qd') != -1) {
                    newRoleList.push(
                        ...(objDept[element]?.children?.map((item: inObject) => {
                            return item.value
                        }) || []),
                    )
                } else {
                    newRoleList.push(element)
                }
            })
            console.log(deptDom)
            return { newRoleList, newDept }
        }
        return {
            treeData,
            dataValue,
            getData,
            roleTreeId,
            SHOW_PARENT,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
