<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="itemForm in myOptions" :key="itemForm">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]" />
            </template>
        </Form>
        <template #footer>
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" v-show="viewType != 'see'" @click="confirm">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'AllotMenu',
    props: {
        viewType: String,
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/roles'
        const { title, item, visible, viewType } = toRefs(props)

        const editDisabled = ref<boolean>(false)
        // const rules: Array<Object> = []
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '角色名称',
                name: 'roleName',
                disabled: editDisabled,
            },
            {
                label: '所属部门',
                type: 'SelectDic',
                name: 'dept',
                // option: customerType,
                required: false,
                disType: 'department',
                disParentId: 151,
                disabled: editDisabled,
                ruleType: 'number',
            },
            {
                label: '角色备注',
                name: 'remark',
                required: false,
                disabled: editDisabled,
            },
            {
                label: '客户数据限制',
                name: 'isRestrictions',
                type: 'switch',
                required: false,
                ruleType: 'boolean',
                // type: 'isTrue',
                default: false,
                disabled: editDisabled,
                checkText: '开启',
                unCheckText: '关闭',
            },

            {
                label: '职责介绍',
                name: 'introduce',
                required: false,
                disabled: editDisabled,
            },
            {
                label: '排序',
                name: 'sort',
                type: 'number',
                ruleType: 'number',
                required: false,
                disabled: editDisabled,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                if (viewType.value == 'see') {
                    editDisabled.value = true
                } else {
                    editDisabled.value = false
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
        }
    },
})
</script>
