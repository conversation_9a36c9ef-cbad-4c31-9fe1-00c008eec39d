<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'900px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template
                v-for="i in title?.includes('新增')
                    ? myOptions
                    : [
                          ...myOptions,
                          {
                              label: '缴费年月',
                              name: 'paymentDate',
                              type: 'month',
                              attrs: {
                                  disabledDate,
                              },
                          },
                      ]"
                :key="i"
            >
                <MyFormItem :item="i" v-model:value="formData[i.name]" v-if="i.label != '单位比例' && i.label != '个人比例'" />
                <template v-if="i.label == '单位比例'">
                    <div class="proportions">单位比例</div>
                </template>
                <template v-if="i.label == '个人比例'">
                    <div class="proportions">个人比例</div>
                </template>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validateProportion, validateAccount } from '/@/utils/format'
import moment from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { RuleObject } from 'ant-design-vue/lib/form/interface'

export default defineComponent({
    name: 'SocialSecurityModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-social-securities'
        const { title, item, visible } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }

        const tableRef = ref()
        let issuingBankList = ref<LabelValueOptions>([])
        onMounted(() => {
            //发放银行
            dictionaryDataStore()
                .setDictionaryData('ownedBank', '')
                .then((res: LabelValueOptions) => {
                    // issuingBankList.value = res
                    issuingBankList.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemName }
                    })
                })
        })
        //发放银行修改
        const issuingBankChanged = (value: string, option: inObject) => {
            formData.value.accountBank = option.label
        }

        const validateNo = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.resolve()
            } else {
                return validateAccount(rule, value)
            }
        }

        const myOptions = ref([
            {
                label: '社保类型名称',
                name: 'socialSecurityName',
            },
            {
                label: '地区',
                name: 'area',
            },
            {
                label: '收款单位名称',
                name: 'nameOfBeneficiary',
                required: false,
            },
            {
                label: '收款单位账号',
                name: 'receivingAccount',
                validator: validateNo,
                required: false,
            },
            {
                label: '收款单位开户行',
                name: 'accountBank',
                showbr: true, //换行
                type: 'change',
                options: issuingBankList,
                onChange: issuingBankChanged,
                required: false,
            },
            {
                label: '单位比例',
                name: 'accountBank',
                showbr: true, //换行
                required: false,
            },
            {
                label: '单位养老',
                name: 'unitPension',
                validator: validateProportion,
            },
            {
                label: '单位医疗',
                name: 'unitMedical',
                validator: validateProportion,
            },
            {
                label: '单位工伤',
                name: 'workInjury',
                validator: validateProportion,
            },
            {
                label: '单位生育',
                name: 'unitMaternity',
                validator: validateProportion,
            },
            {
                label: '单位失业',
                name: 'unitUnemployment',
                showbr: true,
                validator: validateProportion,
            },
            {
                label: '个人比例',
                name: 'account',
                showbr: true, //换行
                required: false,
            },
            {
                label: '个人养老',
                name: 'personalPension',
                validator: validateProportion,
            },
            {
                label: '个人医疗',
                name: 'personalMedical',
                validator: validateProportion,
            },
            {
                label: '个人失业',
                name: 'personalUnemployment',
                validator: validateProportion,
            },
            {
                label: '个人生育',
                name: 'personalMaternity',
                validator: validateProportion,
            },
        ])
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                formData.value.unitPension = formData.value.unitPension ? formData.value.unitPension + '%' : ''
                formData.value.unitMedical = formData.value.unitMedical ? formData.value.unitMedical + '%' : ''
                formData.value.workInjury = formData.value.workInjury ? formData.value.workInjury + '%' : ''
                formData.value.unitUnemployment = formData.value.unitUnemployment ? formData.value.unitUnemployment + '%' : ''
                formData.value.personalPension = formData.value.personalPension ? formData.value.personalPension + '%' : ''
                formData.value.personalMedical = formData.value.personalMedical ? formData.value.personalMedical + '%' : ''
                formData.value.unitMaternity = formData.value.unitMaternity ? formData.value.unitMaternity + '%' : ''
                formData.value.personalMaternity = formData.value.personalMaternity ? formData.value.personalMaternity + '%' : ''
                formData.value.personalUnemployment = formData.value.personalUnemployment
                    ? formData.value.personalUnemployment + '%'
                    : ''
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    // let str = JSON.stringify(formData.value)
                    // str = str.replace(/%/g, '')
                    // formData.value = JSON.parse(str)
                    let params = {
                        unitPension: formData.value.unitPension.replace(/%/g, ''),
                        unitMedical: formData.value.unitMedical.replace(/%/g, ''),
                        workInjury: formData.value.workInjury.replace(/%/g, ''),
                        unitUnemployment: formData.value.unitUnemployment.replace(/%/g, ''),
                        personalPension: formData.value.personalPension.replace(/%/g, ''),
                        personalMedical: formData.value.personalMedical.replace(/%/g, ''),
                        personalUnemployment: formData.value.personalUnemployment.replace(/%/g, ''),
                        personalMaternity: formData.value.personalMaternity.replace(/%/g, ''),
                        unitMaternity: formData.value.unitMaternity.replace(/%/g, ''),
                    }
                    let res
                    if (title.value?.includes('新增')) {
                        res = await request.post(api || '', { ...formData.value, ...params })
                        message.success('新增成功!')
                    } else {
                        res = await request.put(api || '', { ...formData.value, ...params })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value, res)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const disabledDate = (date) => {
            // 当前年已经过去的月份
            return moment(date).format('YYYY') !== moment().format('YYYY') || moment(date) > moment()
        }
        return {
            disabledDate,
            tableRef,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.proportions {
    width: 1000px;
    padding-left: 30px;
    font-size: 16px;
    font-weight: 800;
    padding-bottom: 10px;
}
</style>
