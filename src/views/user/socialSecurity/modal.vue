<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" :title="title" :width="'1200px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template
                v-for="i in title?.includes('新增')
                    ? myOptions
                    : [
                          ...myOptions,
                          //   {
                          //       label: '缴费年月',
                          //       name: 'paymentDate',
                          //       type: 'month',
                          //       attrs: {
                          //           disabledDate,
                          //       },
                          //   },
                      ]"
                :key="i"
            >
                <MyFormItem :item="i" v-model:value="formData[i.name]" />
            </template>
        </Form>
        <!-- <Row gutter="15" :align="'bottom'">
            <Col span="22"> -->
        <BasicTable
            ref="tableRef"
            :columns="columns1"
            :rowSelectionShow="false"
            :sorter="false"
            :tableDataList="multiplePaymentTableList"
        />
        <!-- </Col>
        </Row> -->
        <hr style="margin: 20px 0; width: 100%" />
        <div>
            <BasicTable
                ref="tableRef2"
                :columns="columns2"
                :rowSelectionShow="false"
                :sorter="false"
                :tableDataList="tableDataList2"
            >
                <template #action="{ index, text, record }">
                    <div v-if="index == 0 || index == 3">/</div>
                    <Button v-else-if="index == 1 || index == 2" type="primary" size="small" @click="oneClick(index)"
                        >一键填入</Button
                    >
                    <template v-else>
                        <Button v-if="index == 4" type="primary" size="small" @click="addRow()"> 增加 </Button>
                        <Button v-if="index > 4" type="primary" deger size="small" @click="delRow(index, record)"> 删除 </Button>
                    </template>
                </template>
            </BasicTable>
            <div style="margin: 20px 0; text-align: center">
                <Button type="primary" @click="confirmAdd()">确定社保缴费类型</Button>
            </div>
        </div>
        <BasicEditModalSlot :visible="showModal" @cancel="cancelModal" :title="'一键填入'" :width="'500px'">
            <InputNumber v-model:value="oneValue" style="width: 200px" />
            <template #footer>
                <div>
                    <Button type="primary" @click="confirmModal()">确定</Button>
                </div>
            </template>
        </BasicEditModalSlot>
        <template #footer>
            <div></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted, h, computed, nextTick } from 'vue'
import request from '/@/utils/request'
import { InputNumber, message, Modal, Checkbox, Input } from 'ant-design-vue'
import { getDynamicText, getValuesAndRules } from '/@/utils/index'
import { validateProportion, validateAccount } from '/@/utils/format'
import moment from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import lodash from 'lodash'
import { object } from 'vue-types'

export default defineComponent({
    name: 'SocialSecurityModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: String,
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        let specialFieldDTOList = ref<any[]>([])
        let aloneCardinalDTOList = ref<any[]>([])
        let mergeCardinalDTOList = ref<any[]>([])
        const api = '/api/hr-social-securities'
        const { title, item, visible, viewType } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }

        const tableRef = ref()
        let issuingBankList = ref<LabelValueOptions>([])
        onMounted(() => {
            //发放银行
            dictionaryDataStore()
                .setDictionaryData('ownedBank', '')
                .then((res: LabelValueOptions) => {
                    // issuingBankList.value = res
                    issuingBankList.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemName }
                    })
                })
        })
        //发放银行修改
        const issuingBankChanged = (value: string, option: inObject) => {
            formData.value.accountBank = option.label
        }

        const validateNo = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.resolve()
            } else {
                return validateAccount(rule, value)
            }
        }

        // Form 实例
        const formInline = ref(null) as any

        //进出
        watch(visible, () => {
            if (visible.value) {
                item.value && getSocialSecuritiesTablesList()
            } else {
                closeModalData()
                resetFormData()
            }
        })
        const insertObj = ref<any>({})

        /**
         * 编辑给表格赋值
         */
        const getSocialSecuritiesTablesList = () => {
            request.get(`/api/hr-social-securities?id=${item.value?.id}`).then((res) => {
                insertObj.value = { ...res }
                updataModal()
            })
        }
        const updataModal = () => {
            // 给 社保类型名称， 地区，收款单位名称，收款单位账号，收款单位开户行， 缴费年月赋值
            Object.keys(formData.value).forEach((el) => {
                formData.value[el] = insertObj.value[el]
            })
            // 额外险  单位大额医疗， 个人大额医疗， 补充工伤， 单位商业险，单位企业年金，个人企业年金
            specialFieldDTOList.value = insertObj.value.specialFieldDTOList ?? []
            let specialKeys = insertObj.value.specialFieldDTOList?.map((el) => el.fieldKey)
            specialKeys?.forEach((el) => {
                multiplePaymentTableList.value[0][el] = true
            })
            console.log('Object.keys(ratioDic)', Object.keys(ratioDic))
            // 给缴费比例赋值
            Object.keys(ratioDic).forEach((el) => {
                if (insertObj.value[el] !== null) {
                    tableDataList2.value[0][ratioDic[el]] = insertObj.value[el]
                    console.log('tableDataList2.value[0][ratioDic[el]]', tableDataList2.value[0][ratioDic[el]])
                }
            })
            // 上限赋值
            Object.keys(upperDic).forEach((el) => {
                if (insertObj.value[el] !== null) {
                    let str = upperDic[el]
                    tableDataList2.value[1][str] = insertObj.value[el]
                }
            })
            // 下限赋值
            Object.keys(lowerDic).forEach((el) => {
                if (insertObj.value[el] !== null) {
                    tableDataList2.value[2][lowerDic[el]] = insertObj.value[el]
                }
            })

            // 单独基数赋值
            let alone = insertObj.value?.aloneCardinalDTOList
            Object.keys(cardInalKeys).forEach((el) => {
                alone?.forEach((row) => {
                    if (row.fieldKey == el) {
                        tableDataList2.value[3][cardInalKeys[el]] = true
                    }
                })
            })

            // 合并基数赋值
            let merge = insertObj.value?.mergeCardinalDTOList
            let maxIdx = 0
            merge?.forEach((el) => {
                if (el.relation > maxIdx) {
                    maxIdx = el.relation
                }
            })
            let arr1 = []
            let newArr = []
            for (let i = 4; i < maxIdx; i++) {
                addRow()
            }
            merge?.forEach((el) => {
                el.fieldKeyList.forEach((k) => {
                    tableDataList2.value[el.relation][cardInalKeys[k]] = true
                })
            })
            t1Select()
        }

        // 根据表格2判断表格一是否选中
        const t1Select = () => {
            if (item.value) {
                for (const key in t1andt2) {
                    tableDataList2.value?.forEach((item, index) => {
                        if (index > 2) {
                            if (item[key] === true) {
                                multiplePaymentTableList.value[0][t1andt2[key]] = true
                            }
                        }
                    })
                }
            } else {
                return
            }
        }

        const tempKey = ref('')
        const detailChange = (val, idx, key, type = 'number', keys = '') => {
            if (idx == 3 && val.target?.checked) {
                tempKey.value = key
            }
            if (idx < 3) {
                tableDataList2.value[idx][key] = val
            } else {
                tableDataList2.value[idx][key] = val.target?.checked
            }

            console.log('tableDataList2.value[idx][key]', tableDataList2.value[idx][key])
        }
        /**
         * @文本
         */
        // 特殊类型

        const text = {
            unitLargeMedicalExpense: '单位大额医疗',
            personalLargeMedicalExpense: '个人大额医疗',
            replenishWorkInjuryExpense: '补充工伤',
            commercialInsurance: '单位商业保险',
            unitEnterpriseAnnuity: '单位企业年金',
            personalEnterpriseAnnuity: '个人企业年金',
        }
        //基数
        const text2 = {
            unitPension: '单位养老基数',
            workInjury: '单位工伤基数',
            unitUnemployment: '单位失业基数',
            unitMaternity: '单位生育基数',
            unitMedical: '单位医疗基数',
            personalPension: '个人养老基数',
            personalUnemployment: '个人失业基数',
            personalMaternity: '个人生育基数',
            personalMedical: '个人医疗基数',
        }
        const t1andt2 = {
            unitPension: 'Pension',
            workInjury: 'workInjury',
            unitUnemployment: 'Unemployment',
            unitMaternity: 'Maternity',
            unitMedical: 'Medical',
            personalPension: 'Pension',
            personalUnemployment: 'Unemployment',
            personalMaternity: 'Maternity',
            personalMedical: 'Medical',
        }
        //基数keys  cardInalKeys
        const cardInalKeys = {
            unitPensionCardinal: 'unitPension',
            workInjuryCardinal: 'workInjury',
            unitUnemploymentCardinal: 'unitUnemployment',
            medicalInsuranceCardinal: 'unitMedical',
            unitMaternityCardinal: 'unitMaternity',
            personalPensionCardinal: 'personalPension',
            personalUnemploymentCardinal: 'personalUnemployment',
            personalMaternityCardinal: 'personalMaternity',
            medicalInsuranceCardinalPersonal: 'personalMedical',
        }

        /**
         * @一按键填入
         *
         */
        // 表格一的五险
        const table5 = computed(() => {
            return {
                Maternity: multiplePaymentTableList.value[0].Maternity,
                Medical: multiplePaymentTableList.value[0].Medical,
                Pension: multiplePaymentTableList.value[0].Pension,
                Unemployment: multiplePaymentTableList.value[0].Unemployment,
                workInjury: multiplePaymentTableList.value[0].workInjury,
            }
        })
        const oneValue = ref(0)
        const showModal = ref(false)
        const cancelModal = () => {
            oneValue.value = 0
            showModal.value = false
        }
        const tempIndex = ref(0)
        const oneClick = (index) => {
            tempIndex.value = index
            showModal.value = true
        }
        const confirmModal = () => {
            let item = tableDataList2.value[tempIndex.value]
            for (const key in item) {
                if (Object.prototype.hasOwnProperty.call(item, key)) {
                    if (key === 'socialSecurityPaymentType') {
                        continue
                    }
                    for (const key2 in table5.value) {
                        if (Object.prototype.hasOwnProperty.call(table5.value, key2)) {
                            const element = table5.value[key2]
                            if (element && key.includes(key2)) {
                                tableDataList2.value[tempIndex.value][key] = oneValue.value
                            }
                        }
                    }
                }
            }
            oneValue.value = 0
            showModal.value = false
        }
        /**
         * @state表格111111111的逻辑
         */

        const change1 = (val, idx, key) => {
            multiplePaymentTableList.value[idx][key] = val.target?.checked
            // 表格一特殊类传后端
            if (Object.keys(text).includes(key)) {
                if (val.target?.checked) {
                    specialFieldDTOList.value.push({
                        fieldName: text[key],
                        fieldKey: key,
                        groupName: key.includes('personal') ? 'personal' : 'unit',
                    })
                } else {
                    specialFieldDTOList.value = specialFieldDTOList.value.filter((el) => el.fieldKey != key)
                }
            }
            table2Col()
        }
        // empt  根据表格1未选中 清掉表格2的数据 tabledate
        const table2Col = () => {
            tableDataList2.value.forEach((item, index) => {
                for (const key in item) {
                    if (Object.prototype.hasOwnProperty.call(item, key)) {
                        for (const key2 in table5.value) {
                            if (Object.prototype.hasOwnProperty.call(table5.value, key2)) {
                                const element = table5.value[key2]
                                if (index == 0) {
                                    if (!element && key.includes(key2)) {
                                        item[key] = 0
                                    }
                                } else if (0 < index && index < 3) {
                                    if (!element && key.includes(key2)) {
                                        item[key] = null
                                    }
                                } else {
                                    if (!element && key.includes(key2)) {
                                        item[key] = false
                                    }
                                }
                            }
                        }
                    }
                }
            })
        }
        const columns1 = ref([
            { title: '社保缴费类型', dataIndex: 'socialSecurityPaymentType', width: 120 },
            {
                title: '生育',
                dataIndex: 'Maternity',
                width: 50,
                customRender: ({ index, text, record, column }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'Maternity')
                        },
                    })
                },
            },
            {
                title: '医疗',
                dataIndex: 'Medical',
                width: 50,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'Medical')
                        },
                    })
                },
            },
            {
                title: '养老',
                dataIndex: 'Pension',
                width: 50,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'Pension')
                        },
                    })
                },
            },
            {
                title: '失业',
                dataIndex: 'Unemployment',
                width: 50,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'Unemployment')
                        },
                    })
                },
            },
            {
                title: '工伤',
                dataIndex: 'workInjury',
                width: 50,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'workInjury')
                        },
                    })
                },
            },
            {
                title: '单位大额医疗',
                dataIndex: 'unitLargeMedicalExpense',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'unitLargeMedicalExpense')
                        },
                    })
                },
            },
            {
                title: '个人大额医疗',
                dataIndex: 'personalLargeMedicalExpense',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'personalLargeMedicalExpense')
                        },
                    })
                },
            },
            {
                title: '补充工伤',
                dataIndex: 'replenishWorkInjuryExpense',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'replenishWorkInjuryExpense')
                        },
                    })
                },
            },
            {
                title: '单位商业保险',
                dataIndex: 'commercialInsurance',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'commercialInsurance')
                        },
                    })
                },
            },
            {
                title: '单位企业年金',
                dataIndex: 'unitEnterpriseAnnuity',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'unitEnterpriseAnnuity')
                        },
                    })
                },
            },
            {
                title: '个人企业年金',
                dataIndex: 'personalEnterpriseAnnuity',
                width: 110,
                customRender: ({ index, text, record }) => {
                    return h(Checkbox, {
                        checked: text,
                        onChange: (e) => {
                            change1(e, index, 'personalEnterpriseAnnuity')
                        },
                    })
                },
            },
        ])
        const defTable = () => [
            {
                socialSecurityPaymentType: '可多选缴费类型',
                Maternity: false,
                Medical: false,
                Pension: false,
                Unemployment: false,
                workInjury: false,
                unitLargeMedicalExpense: false,
                personalLargeMedicalExpense: false,
                replenishWorkInjuryExpense: false,
                commercialInsurance: false,
                unitEnterpriseAnnuity: false,
                personalEnterpriseAnnuity: false,
            },
        ]

        // 可多选缴费类型列表
        const multiplePaymentTableList = ref([...defTable()])

        //合并基数数组长度要自动计算
        const arrLength = computed(() => {
            return tableDataList2.value.length - 4
        })
        /**
         * @state表格2222222的逻辑
         */
        const basetable = computed((val, ff) => {
            return tableDataList2.value.filter((el, index) => index > 2)
        })

        /**
         * @params index 每一行索引
         *         text  值
         *         record: 列表每一行数据
         *         column: 表格属性值
         *         rowkey: 每一个键值
         *         key2: 分类的值
         * @description  处理表格数据的显示隐藏
         */
        const rowRender = (index, text, record, column, rowkey, key2) => {
            if (index == 0) {
                let flag = ref<Boolean>(false)
                if (multiplePaymentTableList.value[0][key2]) {
                    flag.value = false
                } else {
                    flag.value = true
                }
                return h(InputNumber, {
                    value: text,
                    disabled: flag.value,
                    formatter: (value) => `${value}%`,
                    parser: (value) => value.replace('%', ''),
                    onChange: (e) => {
                        detailChange(e, index, rowkey)
                    },
                })
            } else if (index > 0 && index < 3) {
                let flag = ref<Boolean>(false)
                if (multiplePaymentTableList.value[0][key2]) {
                    flag.value = false
                } else {
                    flag.value = true
                }
                return h(InputNumber, {
                    type: 'number',
                    value: text,
                    disabled: flag.value,
                    onChange: (e) => {
                        detailChange(e, index, rowkey)
                    },
                })
            } else if (index >= 3) {
                let flag = ref(false)
                let isTrue = basetable.value.some((el) => el[rowkey] !== false)
                let trueIdx = tableDataList2.value.findIndex((row, index) => index > 2 && row[rowkey])
                if (!multiplePaymentTableList.value[0][key2] || (isTrue && trueIdx !== index)) {
                    flag.value = true
                } else {
                    flag.value = false
                }
                return h(Checkbox, {
                    checked: text,
                    disabled: flag.value,
                    onChange: (e) => {
                        detailChange(e, index, rowkey)
                    },
                })
            }
        }
        const columns2 = ref([
            {
                title: '社保缴费类型',
                dataIndex: 'socialSecurityPaymentType',
                width: 120,
                customRender: ({ text, index, record }) => {
                    const obj = {
                        children: text,
                        props: {} as any,
                    }
                    if (index === 4) {
                        obj.props.rowSpan = arrLength.value
                    }
                    if (index > 4) {
                        obj.props.rowSpan = 0
                    }
                    return obj
                },
            },
            {
                title: '单位社保缴费类型配置',
                dataIndex: 'unitSocialSecurity',
                children: [
                    {
                        title: '生育',
                        dataIndex: 'unitMaternity',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'unitMaternity', 'Maternity')
                        },
                    },
                    {
                        title: '医疗',
                        dataIndex: 'unitMedical',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'unitMedical', 'Medical')
                        },
                    },
                    {
                        title: '养老',
                        dataIndex: 'unitPension',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'unitPension', 'Pension')
                        },
                    },
                    {
                        title: '失业',
                        dataIndex: 'unitUnemployment',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'unitUnemployment', 'Unemployment')
                        },
                    },
                    {
                        title: '工伤',
                        dataIndex: 'workInjury',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'workInjury', 'workInjury')
                        },
                    },
                ],
            },
            {
                title: '个人社保缴费类型配置',
                dataIndex: 'personalSocialSecurity',
                children: [
                    {
                        title: '生育',
                        dataIndex: 'personalMaternity',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'personalMaternity', 'Maternity')
                        },
                    },
                    {
                        title: '医疗',
                        dataIndex: 'personalMedical',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'personalMedical', 'Medical')
                        },
                    },
                    {
                        title: '养老',
                        dataIndex: 'personalPension',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'personalPension', 'Pension')
                        },
                    },
                    {
                        title: '失业',
                        dataIndex: 'personalUnemployment',
                        width: 80,
                        align: 'center',
                        customRender: ({ index, text, record, column }) => {
                            return rowRender(index, text, record, column, 'personalUnemployment', 'Unemployment')
                        },
                    },
                ],
            },
            {
                title: '操作',
                dataIndex: 'action',
                width: 80,
                slots: { customRender: 'action' },
            },
        ])
        const initTable2 = () => [
            {
                socialSecurityPaymentType: '缴费比例',
                unitMaternity: 0,
                unitMedical: 0,
                unitPension: 0,
                unitUnemployment: 0,
                workInjury: 0,
                personalMaternity: 0,
                personalMedical: 0,
                personalPension: 0,
                personalUnemployment: 0,
            },
            {
                socialSecurityPaymentType: '上限',
                unitMaternity: null,
                unitMedical: null,
                unitPension: null,
                unitUnemployment: null,
                workInjury: null,
                personalMaternity: null,
                personalMedical: null,
                personalPension: null,
                personalUnemployment: null,
            },
            {
                socialSecurityPaymentType: '下限',
                unitMaternity: null,
                unitMedical: null,
                unitPension: null,
                unitUnemployment: null,
                workInjury: null,
                personalMaternity: null,
                personalMedical: null,
                personalPension: null,
                personalUnemployment: null,
            },
            {
                socialSecurityPaymentType: '单独基数',
                unitMaternity: false,
                unitMedical: false,
                unitPension: false,
                unitUnemployment: false,
                workInjury: false,
                personalMaternity: false,
                personalMedical: false,
                personalPension: false,
                personalUnemployment: false,
            },
            {
                socialSecurityPaymentType: '合并基数',
                unitMaternity: false,
                unitMedical: false,
                unitPension: false,
                unitUnemployment: false,
                workInjury: false,
                personalMaternity: false,
                personalMedical: false,
                personalPension: false,
                personalUnemployment: false,
            },
        ]
        const tableDataList2 = ref([...initTable2()])
        let table2top = ref<any>([])
        let table2lower = ref<any>([])

        let deepArr = ref<any>([]) //深拷贝数组
        watch(
            () => tableDataList2.value[3],
            (newVal, oldVal) => {
                for (const key in tableDataList2.value[3]) {
                    if (Object.prototype.hasOwnProperty.call(tableDataList2.value[3], key)) {
                        if (key == 'socialSecurityPaymentType') {
                            continue
                        }
                        const element = tableDataList2.value[3][key]
                        if (element && key === tempKey.value) {
                            for (let i = 4; i < tableDataList2.value.length; i++) {
                                const item = tableDataList2.value[i]
                                item[key] = false
                            }
                        }
                    }
                }

                let isTrue = Object.keys(tableDataList2.value[3]).every((el) => tableDataList2.value[3][el] != false)
                if (isTrue && table2lower.value.length == 0) {
                    deepArr.value = lodash.cloneDeep(tableDataList2.value)
                    table2top.value = [...deepArr.value.slice(0, 4)]
                    table2lower.value = [...deepArr.value.slice(4)]
                    tableDataList2.value = table2top.value
                } else if (!isTrue && table2lower.value.length !== 0) {
                    table2lower.value.length != 0 && tableDataList2.value.push(...table2lower.value)
                    table2top.value = []
                    table2lower.value = []
                }
            },
            {
                deep: true,
            },
        )
        //添加行
        const addRow = () => {
            tableDataList2.value.push({
                socialSecurityPaymentType: '合并基数',
                unitMaternity: false,
                unitMedical: false,
                unitPension: false,
                unitUnemployment: false,
                workInjury: false,
                personalMaternity: false,
                personalMedical: false,
                personalPension: false,
                personalUnemployment: false,
            })
        }
        //删除行
        const delRow = (index, item) => {
            tableDataList2.value.splice(index, 1)
        }
        /**
         * @stateform表单的逻辑
         */

        const myOptions = ref([
            {
                label: '社保类型名称',
                name: 'socialSecurityName',
            },
            {
                label: '地区',
                name: 'area',
            },
            {
                label: '收款单位名称',
                name: 'nameOfBeneficiary',
                required: false,
            },
            {
                label: '收款单位账号',
                name: 'receivingAccount',
                validator: validateNo,
                required: false,
            },
            {
                label: '收款单位开户行',
                name: 'accountBank',
                // showbr: true, //换行
                type: 'change',
                options: issuingBankList,
                onChange: issuingBankChanged,
                required: false,
            },
            {
                label: '缴费年月',
                name: 'paymentDate',
                type: 'month',
                required: false,
            },
        ])
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const closeModalData = () => {
            tempKey.value = ''
            nextFlag.value = false
            showModal.value = false
            multiplePaymentTableList.value = [...defTable()]
            tableDataList2.value = [...initTable2()]
            table2top.value = []
            table2lower.value = []
            specialFieldDTOList.value = []
            aloneCardinalDTOList.value = []
            mergeCardinalDTOList.value = []
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // 缴费比例
        const ratioDic = {
            unitPension: 'unitPension',
            workInjury: 'workInjury',
            unitUnemployment: 'unitUnemployment',
            unitMaternity: 'unitMaternity',
            unitMedical: 'unitMedical',
            personalPension: 'personalPension',
            personalUnemployment: 'personalUnemployment',
            personalMaternity: 'personalMaternity',
            personalMedical: 'personalMedical',
        }
        // 上限
        const upperDic = {
            unitPensionUpperLimit: 'unitPension',
            workInjuryUpperLimit: 'workInjury',
            unitUnemploymentUpperLimit: 'unitUnemployment',
            unitMaternityUpperLimit: 'unitMaternity',
            unitMedicalUpperLimit: 'unitMedical',
            personalPensionUpperLimit: 'personalPension',
            personalUnemploymentUpperLimit: 'personalUnemployment',
            personalMaternityUpperLimit: 'personalMaternity',
            personalMedicalUpperLimit: 'personalMedical',
        }

        // 下限
        const lowerDic = {
            unitPensionLowerLimit: 'unitPension',
            workInjuryLowerLimit: 'workInjury',
            unitUnemploymentLowerLimit: 'unitUnemployment',
            unitMaternityLowerLimit: 'unitMaternity',
            unitMedicalLowerLimit: 'unitMedical',
            personalPensionLowerLimit: 'personalPension',
            personalUnemploymentLowerLimit: 'personalUnemployment',
            personalMaternityLowerLimit: 'personalMaternity',
            personalMedicalLowerLimit: 'personalMedical',
        }

        // 合并函数
        const margeFun = () => {
            tableDataList2.value.forEach((el: any, index) => {
                el.i = index
            })
            let aloneCardInal = tableDataList2.value[3]
            for (const k in cardInalKeys) {
                for (const key in aloneCardInal) {
                    if (key === cardInalKeys[k]) {
                        if (aloneCardInal[key] === true) {
                            aloneCardinalDTOList.value.push({
                                fieldName: text2[key],
                                fieldKey: k,
                                groupName: key.includes('personal') ? 'personal' : 'unit',
                            })
                        }
                    }
                }
            }

            let mergeTable2 = tableDataList2.value.filter((el, index) => index > 3)
            // mergeCardinalDTOList   cardInalKeys
            mergeTable2?.forEach((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (key == 'socialSecurityPaymentType') {
                        continue
                    }
                    if (element === true) {
                        if (key.includes('personal')) {
                            let row = mergeCardinalDTOList.value.find((el) => el.relation == item.i && el.groupName == 'personal')
                            if (row) {
                                for (const jkey in cardInalKeys) {
                                    if (cardInalKeys[jkey] == key) {
                                        row.fieldKeyList.push(jkey)
                                        row.fieldNameList.push(text2[key])
                                    }
                                }
                            } else {
                                let fieldNameList: any = []
                                let fieldKeyList: any = []
                                for (const jkey in cardInalKeys) {
                                    if (cardInalKeys[jkey] == key) {
                                        fieldKeyList.push(jkey)
                                        fieldNameList.push(text2[key])
                                    }
                                }
                                mergeCardinalDTOList.value.push({
                                    fieldNameList: fieldNameList,
                                    fieldKeyList: fieldKeyList,
                                    groupName: 'personal',
                                    relation: item.i,
                                })
                            }
                        } else {
                            let row = mergeCardinalDTOList.value.find((el) => el.relation == item.i && el.groupName == 'unit')
                            if (row) {
                                for (const jkey in cardInalKeys) {
                                    if (cardInalKeys[jkey] == key) {
                                        row.fieldKeyList.push(jkey)
                                        row.fieldNameList.push(text2[key])
                                    }
                                }
                            } else {
                                let fieldNameList: any = []
                                let fieldKeyList: any = []
                                for (const jkey in cardInalKeys) {
                                    if (cardInalKeys[jkey] == key) {
                                        fieldKeyList.push(jkey)
                                        fieldNameList.push(text2[key])
                                    }
                                }
                                mergeCardinalDTOList.value.push({
                                    fieldNameList: fieldNameList,
                                    fieldKeyList: fieldKeyList,
                                    groupName: 'unit',
                                    relation: item.i,
                                })
                            }
                        }
                    }
                }
            })
        }
        let nextFlag = ref(false)
        // 校验函数
        const validateFu = () => {
            let ta1 = { ...multiplePaymentTableList.value[0] }
            for (const key in t1andt2) {
                if (ta1[t1andt2[key]]) {
                    if (
                        tableDataList2.value[2][key] &&
                        tableDataList2.value[1][key] &&
                        tableDataList2.value[2][key] > tableDataList2.value[1][key]
                    ) {
                        nextFlag.value = true
                        break
                    } else {
                        nextFlag.value = false
                    }
                }
            }
            if (nextFlag.value) {
                message.warning({
                    content: '上限的值要大于下限的值',
                })
                return
            }
            for (const key in t1andt2) {
                if (ta1[t1andt2[key]]) {
                    let f = tableDataList2.value.some((item, index) => {
                        return index > 2 && item[key] === true
                    })
                    if (f == true) {
                        nextFlag.value = false
                    } else {
                        nextFlag.value = true
                        break
                    }
                }
            }
            if (nextFlag.value) {
                message.warning({
                    content: '请选择单独基数或合并基数',
                })
                return
            }
        }
        const confirmAdd = () => {
            validateFu()

            if (nextFlag.value) {
                return
            }
            margeFun()
            // return
            let table1: any = { ...multiplePaymentTableList.value[0] }
            let ratio: any = { ...tableDataList2.value[0] } //比例
            let upper: any = { ...tableDataList2.value[1] } //上限
            let lower: any = { ...tableDataList2.value[2] } //下限

            let params = {
                id: item.value ? item.value?.id : undefined,
                socialSecurityConfigId: insertObj.value?.socialSecurityConfigId ?? undefined,
                ...formData.value,
                specialFieldDTOList: specialFieldDTOList.value,
                aloneCardinalDTOList: aloneCardinalDTOList.value,
                mergeCardinalDTOList: mergeCardinalDTOList.value,
            }

            let res1 = fn1(ratio, ratioDic)
            let res2 = fn1(upper, upperDic)
            let res3 = fn1(lower, lowerDic)
            params = Object.assign(params, res1, res2, res3)
            formInline.value
                .validate()
                .then(async () => {
                    let res
                    if (title.value?.includes('新增')) {
                        res = await request.post(api || '', { ...params })
                        message.success('新增成功!')
                    } else {
                        res = await request.put(api || '', { ...params })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value, res)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        //循环处理参数 params 数组的某行数据 字典   return obj
        const fn1 = (ratio, dic) => {
            let obj = {}
            Object.keys(ratio).forEach((el) => {
                for (const key in dic) {
                    if (Object.prototype.hasOwnProperty.call(dic, key)) {
                        if (dic[key] === el) {
                            obj[key] = ratio[el]
                        }
                    }
                }
            })
            return obj
        }

        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const disabledDate = (date) => {
            // 当前年已经过去的月份
            return moment(date).format('YYYY') !== moment().format('YYYY') || moment(date) > moment()
        }
        return {
            disabledDate,
            tableRef,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            columns1,
            multiplePaymentTableList,
            columns2,
            tableDataList2,
            confirmAdd,
            addRow,
            delRow,
            oneValue,
            showModal,
            cancelModal,
            confirmModal,
            oneClick,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.proportions {
    width: 1000px;
    padding-left: 30px;
    font-size: 16px;
    font-weight: 800;
    padding-bottom: 10px;
}
</style>
