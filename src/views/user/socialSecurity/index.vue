<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />

    <div class="btns">
        <Button type="primary" v-auth="'socialSecurity_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'socialSecurity_import'" @click="ImportData">导入</Button>
        <Button type="primary" v-auth="'socialSecurity_export'" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" v-auth="'socialSecurity_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-social-securities/page"
        deleteApi="/api/hr-social-securities/deletes"
        :params="params"
        :columns="columns"
        :exportUrl="exportUrl"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #quantity="{ record }">
            <Button type="link" size="small" @click="clickQuantity(record)">{{ record.clientNumber }}</Button>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp; -->
            <!-- //删除 -->
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />

    <!--客户列表-->
    <CustomerModal :visible="customerVisible" :item="customerValue" @cancel="customerCancel" />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
    <!-- 补差 -->
    <DiffModal :visible="showDiff" :diffIds="diffIds" :columns="diffColumns" @cancel="diffClose" @confirm="diffConfirm" />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { SearchBarOption } from '/#/component'

import modal from './modal.vue'
import customerModal from './customerModal.vue'
import DiffModal from '../accumulation/DiffModal.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'SocialSecurity',
    components: { MyModal: modal, CustomerModal: customerModal, DiffModal },
    setup() {
        //筛选
        const params = ref<{}>({
            socialSecurityName: null,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '社保类型名称',
                key: 'socialSecurityName',
            },
        ]
        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '社保类型名称',
                dataIndex: 'socialSecurityName',
                align: 'center',
                width: 150,
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
                width: 150,
            },
            {
                title: '收款单位名称',
                dataIndex: 'nameOfBeneficiary',
                align: 'center',
                width: 150,
            },
            {
                title: '收款单位账号',
                dataIndex: 'receivingAccount',
                align: 'center',
                width: 150,
            },
            {
                title: '收款单位开户行',
                dataIndex: 'accountBank',
                align: 'center',
                width: 150,
            },
            {
                title: '单位养老',
                dataIndex: 'unitPension',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位医疗',
                dataIndex: 'unitMedical',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位生育',
                dataIndex: 'unitMaternity',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位工伤',
                dataIndex: 'workInjury',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '单位失业',
                dataIndex: 'unitUnemployment',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人养老',
                dataIndex: 'personalPension',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人医疗',
                dataIndex: 'personalMedical',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人失业',
                dataIndex: 'personalUnemployment',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '个人生育',
                dataIndex: 'personalMaternity',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '客户数量',
                dataIndex: 'userStatus',
                align: 'center',
                slots: { customRender: 'quantity' },
                width: 100,
                sorter: false,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增社保')
        // 当前编辑的数据
        const currentValue = ref(undefined)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增社保'
            currentValue.value = undefined
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑社保'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            currentValue.value = undefined
        }

        const diffIds = ref([])
        const modalConfirm = async (form, arr) => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
                diffIds.value = arr || []
                showDiff.value = Array.isArray(arr) && !!arr.length
            }
        }

        // 客户列表数据
        const customerValue = ref(undefined)
        // 客户列表显示隐藏
        const customerVisible = ref(false)
        // 点击客户数量
        const clickQuantity = (row) => {
            if (row.clientNumber != 0) {
                customerVisible.value = true
                customerValue.value = row
            }
        }

        // 关闭客户列表
        const customerCancel = () => {
            customerVisible.value = false
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-social-securities/template'
        const importUrl = '/api/hr-social-securities/import'
        const exportUrl = '/api/hr-social-securities/export'
        // 导入
        const ImportData = () => {
            importVisible.value = true
        }
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
            tableRef.value.refresh(1)
        }

        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'socialSecurity_edit',
                    show: true,
                    click: editRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'socialSecurity_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )

        const showDiff = ref(false)
        const diffClose = () => {
            diffIds.value = []
            showDiff.value = false
        }
        const diffConfirm = () => {
            diffIds.value = []
            showDiff.value = false
            tableRef.value.refresh()
        }

        return {
            selectedRowsArr,
            exportText,
            diffIds,
            showDiff,
            diffClose,
            diffConfirm,
            diffColumns: [
                {
                    title: '单位编号',
                    dataIndex: 'unitNumber',
                    width: 150,
                },
                {
                    title: '单位名称',
                    dataIndex: 'clientName',
                    width: 170,
                    ellipsis: true,
                },
                {
                    title: '姓名',
                    dataIndex: 'staffName',
                    width: 120,
                },
                {
                    title: '证件号码',
                    dataIndex: 'certificateNum',
                    width: 180,
                },
                {
                    title: '缴费年月',
                    dataIndex: 'paymentDate',
                    width: 110,
                },
                {
                    title: '个人社保编号',
                    dataIndex: 'socialSecurityNum',
                    width: 170,
                },
                {
                    title: '个人医保编号',
                    dataIndex: 'medicalInsuranceNum	',
                    width: 170,
                },
                {
                    title: '单位缴纳部分',
                    sorter: false,
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'unitPension',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '失业',
                            dataIndex: 'unitUnemployment',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '医疗',
                            dataIndex: 'unitMedical',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '生育',
                            dataIndex: 'unitMaternity',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '工伤',
                            dataIndex: 'unitInjury',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '小计',
                            dataIndex: 'unitSubtotal',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                    ],
                },
                {
                    title: '个人缴纳部分',
                    sorter: false,
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'personalPension',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '失业',
                            dataIndex: 'personalUnemployment',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '医疗',
                            dataIndex: 'personalMedical',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '生育',
                            dataIndex: 'personalMaternity',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                        {
                            title: '小计',
                            dataIndex: 'personalSubtotal',
                            align: 'center',
                            width: 100,
                            sorter: true,
                        },
                    ],
                },
                {
                    title: '总计',
                    dataIndex: 'socialSecurityTotal',
                    width: 100,
                },
            ],
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            customerValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            clickQuantity,
            customerVisible,
            customerCancel,
            //事件
            changeRoleId,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            ImportData,
            exportData,
            exportUrl,

            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less"></style>
