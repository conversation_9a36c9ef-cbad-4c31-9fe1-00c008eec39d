<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem
                    v-if="!itemForm.external"
                    :width="itemForm.width"
                    :item="itemForm"
                    v-model:value="formData[itemForm.name]"
                >
                    <template #TreeSelect>
                        <RoleTree ref="roleTreeRef" v-model:value="formData[itemForm.name]" :itemForm="itemForm" />
                    </template>
                    <template #ruleDay>
                        <div style="line-height: 32px">
                            {{ arr[0] }}
                            <InputNumber
                                style="width: 60px"
                                v-model:value="formData[itemForm.name]"
                                :placeholder="itemForm.placeholder || `请输入${itemForm.label}`"
                                :disabled="itemForm.disabled"
                                :max="itemForm.max"
                                :min="1"
                            />
                            {{ arr[1] }}
                        </div>
                    </template>
                </MyFormItem>
                <!-- 退休年龄 -->
                <template v-else>
                    <FormItem
                        style="width: 100%"
                        :label="itemForm.label"
                        :name="['retirementAge', i]"
                        :rules="validateRetirementAge"
                        v-if="formData.remindKey == 'staff_retire'"
                    >
                        <div style="display: flex; align-items: center">
                            <div style="margin-right: 20px">
                                男
                                <InputNumber
                                    style="width: 60px"
                                    v-model:value="formData.manAge"
                                    :placeholder="itemForm.placeholder || `请输入${itemForm.label}`"
                                    :disabled="itemForm.disabled"
                                    :max="itemForm.max"
                                    :min="1"
                                />
                                周岁
                            </div>
                            <div>
                                女
                                <InputNumber
                                    style="width: 60px"
                                    v-model:value="formData.womanAge"
                                    :placeholder="itemForm.placeholder || `请输入${itemForm.label}`"
                                    :disabled="itemForm.disabled"
                                    :max="itemForm.max"
                                    :min="1"
                                />
                                周岁
                            </div>
                        </div>
                    </FormItem>
                </template>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message, TreeSelect } from 'ant-design-vue'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import { validatePhone } from '/@/utils/format'
import RoleTree from '/@/views/authorization/role/roleTree.vue'
const SHOW_PARENT = TreeSelect.SHOW_PARENT

export default defineComponent({
    name: 'AllotMenu',
    components: {
        RoleTree,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],

    setup(props, { emit }) {
        const { title, item, visible } = toRefs(props)

        const treeData = ref<TreeDataItem[]>([])
        const value = ref<string[]>([])
        watch(value, () => {})
        const arr = ref<any>([])
        watch(item, (newVal: any) => {
            if (newVal && newVal.ruleCont) {
                arr.value = newVal.ruleCont.split('{}')
            }
        })

        //表单数据
        //请求
        const api = '/api/hr-remind-confs'
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }

        const userNameDisabled = ref<boolean>(false)
        const editDisabled = ref<boolean>(false)

        const roleTreeRef = ref()

        const myOptions = ref([
            {
                label: '标题',
                name: 'title',
                disabled: editDisabled,
            },
            {
                label: '接收对象',
                name: 'roleList',
                slots: 'TreeSelect',
                type: 'slots',
                message: '请选择接收对象',
                ruleType: 'array',
            },
            {
                label: '退休年龄',
                name: 'retirementAge',
                ruleType: 'array',
                external: true,
                default: [
                    {
                        manAge: undefined,
                        womanAge: undefined,
                    },
                ],
                show: true,
            },
            {
                label: '规则',
                name: 'ruleDay',
                type: 'slots',
                slots: 'ruleDay',
                ruleType: 'number',
            },

            {
                label: '状态',
                name: 'status',
                ruleType: 'boolean',
                type: 'switch',
                reverse: true,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
                editDisabled.value = true
                formData.value.manAge = JSON.parse(formData.value?.ruleAppend)?.manAge
                formData.value.womanAge = JSON.parse(formData.value?.ruleAppend)?.womanAge
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            let { newRoleList, newDept } = roleTreeRef.value.getData()
            // getData
            formInline.value
                .validate()
                .then(async () => {
                    formData.value.ruleAppend = {
                        manAge: formData.value.manAge,
                        womanAge: formData.value.womanAge,
                    }
                    formData.value.ruleAppend = JSON.stringify(formData.value.ruleAppend)
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', { ...formData.value, roleList: newRoleList, dept: newDept?.join() || '' })
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', { ...formData.value, roleList: newRoleList, dept: newDept?.join() || '' })
                        message.success('编辑成功!')
                    }
                    // console.log(formData.value)

                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        // Function(inputValue: string, treeNode: TreeNode) (函数需要返回 bool 值)
        const filterTreeNode = (inputValue: string, treeNode) => {
            console.log(inputValue, treeNode)
        }

        // 退休年龄校验
        const validateRetirementAge = {
            required: true,
            trigger: 'change',
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                if (formData.value?.manAge) {
                    if (!formData.value?.womanAge) {
                        return Promise.reject('请将退休年龄信息设置完整')
                    } else {
                        return Promise.resolve()
                    }
                } else {
                    return Promise.reject('请将退休年龄信息设置完整')
                }
            },
        }

        return {
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            arr,
            myOptions,
            formInline,
            selectChanged,

            value,
            treeData,
            SHOW_PARENT,

            filterTreeNode,

            roleTreeRef, //选择器
            validateRetirementAge,
        }
    },
})
</script>
