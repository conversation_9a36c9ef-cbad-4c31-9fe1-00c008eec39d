<template>
    <div class="btns">
        <Button type="primary" v-auth="'warn_batchStart'" class="btn" @click="qiyong">批量启用</Button>
        <Button danger type="primary" v-auth="'warn_batchStop'" @click="jinyong">批量禁用</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-remind-confs/page"
        deleteApi="/api/hr-remind-confs/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button> -->
            <!-- &nbsp; -->
            <!-- //删除 -->
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'

import request from '/@/utils/request'

import modal from './modal.vue'
import { message } from 'ant-design-vue'
import { getHaveAuthorityOperation } from '/@/utils'

export default defineComponent({
    name: 'WarnIndex',
    components: { MyModal: modal },
    setup() {
        //筛选
        const params = ref<{}>({
            typeName: null,
        })

        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '标题',
                dataIndex: 'title',
                align: 'center',
                width: 200,
            },
            {
                title: '对象',
                dataIndex: 'dept',
                align: 'center',
                width: 150,
            },

            {
                title: '规则',
                dataIndex: 'ruleDay',
                align: 'center',
                width: 500,
                customRender: ({ record }) => {
                    // console.log(record)
                    return record.ruleContLayout
                },
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                width: 100,
                customRender: ({ text }) => {
                    return text ? '禁用' : '启用'
                },
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
            })
        }
        // 选中的ID
        const applyIdList = ref<any>([])
        // 多选
        const selectedRowsArr = (item) => {
            let ids: (string | number)[] = []
            ids = item.map((val) => {
                return val.id
            })
            applyIdList.value = ids
        }

        // 启用
        const qiyong = () => {
            if (applyIdList.value == null || applyIdList.value?.length == 0) {
                message.error('请先选择一条数据!')
                return
            }
            request.put('/api/hr-remind-confs/update?stateBoolean=false', applyIdList.value).then((res) => {
                console.log(res)
                message.success('启用成功!')

                tableRef.value.refresh()
            })
        }
        const jinyong = () => {
            if (applyIdList.value == null || applyIdList.value?.length == 0) {
                message.error('请先选择一条数据!')
                return
            }
            request.put('/api/hr-remind-confs/update?stateBoolean=true', applyIdList.value).then((res) => {
                message.success('禁用成功!')

                tableRef.value.refresh()
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('提醒配置')
        // 当前编辑的数据
        const currentValue = ref(null)

        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '提醒配置'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '提醒配置'
            // currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('提醒配置')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'warn_edit',
                    show: true,
                    click: editRow,
                },
            ]),
        )

        return {
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            applyIdList,
            selectedRowsArr,
            qiyong,
            jinyong,

            columns,
            params,
            searchData,
            tableRef,

            editRow,
            deleteRow,

            //操作按钮
            myOperation,
            // myOperationClick,
        }
    },
})
</script>

<style scoped lang="less">
.btn {
    background: #3eb889;
    border: none;
}
</style>
