<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />

    <div class="btns">
        <Button type="primary" v-auth="'platform_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'platform_import'" @click="ImportData">导入</Button>
        <Button type="primary" v-auth="'platform_export'" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" v-auth="'platform_edit'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-platform-accounts/page"
        deleteApi="/api/hr-platform-accounts/deletes"
        :params="params"
        :columns="columns"
        :exportUrl="exportUrl"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #quantity="{ record }">
            <Button type="link" size="small" @click="clickQuantity(record)">{{
                record.platformType == 0 ? '0' : record.clientNumber
            }}</Button>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />

            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp; -->
            <!-- //删除 -->
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />

    <!--    客户列表-->
    <CustomerModal :visible="customerVisible" :item="customerValue" @cancel="customerCancel" />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchResData" />
</template>

<script lang="ts">
import { Modal, Tag, message } from 'ant-design-vue'
import { defineComponent, h, ref, onMounted, computed } from 'vue'
import { EditModalOption, SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import modal from './modal.vue'
import customerModal from './customerModal.vue'
import dwonFile from '/@/utils/downFile'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'platform',
    components: { MyModal: modal, CustomerModal: customerModal },
    setup() {
        // 获取全部账号类型
        let accountTypeList = ref<LabelValueOptions>([
            {
                label: '其他',
                value: '其他',
            },
            {
                label: '社保缴纳账户',
                value: '社保缴纳账户',
            },
            {
                label: '医保缴纳账户',
                value: '医保缴纳账户',
            },
            {
                label: '公积金缴纳账户',
                value: '公积金缴纳账户',
            },
            {
                label: '工资发放账户',
                value: '工资发放账户',
            },
        ])
        // 请求所有账号类型
        // function getAccountType() {
        //     dictionaryDataStore()
        //         .setDictionaryData('list', '/api/hr-platform-accounts/list', 'get', true)
        //         .then((data: inObject[]) => {
        //             accountTypeList.value = data.map((item) => {
        //                 return { label: item.accountType, value: item.accountType }
        //             })
        //         })
        // }
        //筛选const getRoleBySex = ({ sex }) => {
        const params = ref<{}>({
            accountType: null,
        })
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '账号类型',
                key: 'accountTypeList',
                options: accountTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '账户号',
                key: 'accountNumber',
            },
        ]
        // const changeAccountType = ({ accountType }) => {
        //     // ;(params.value as any).accountType = value.option?.label
        // }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        const searchResData = () => {
            // getAccountType()
            searchData()
        }
        //表格数据
        const columns = [
            {
                title: '账号类型',
                dataIndex: 'accountType',
                align: 'center',
                width: 150,
            },
            {
                title: '发放银行',
                dataIndex: 'issuingBank',
                align: 'center',
                width: 150,
            },
            {
                title: '账户号',
                dataIndex: 'accountNumber',
                align: 'center',
                width: 150,
            },
            {
                title: '密码',
                dataIndex: 'password',
                align: 'center',
                width: 100,
            },
            {
                title: '客户数量',
                align: 'center',
                slots: { customRender: 'quantity' },
                width: 100,
                sorter: false,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                // getAccountType()
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增平台账号')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增平台账号'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑平台账号'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增平台账号'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                // getAccountType()

                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 客户列表数据
        const customerValue = ref(null)
        // 客户列表显示隐藏
        const customerVisible = ref(false)
        // 点击客户数量
        const clickQuantity = (row) => {
            if (row.clientNumber != 0 && row.platformType != 0) {
                customerVisible.value = true
                customerValue.value = row
            }
        }

        // 关闭客户列表
        const customerCancel = () => {
            customerVisible.value = false
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-platform-accounts/template'
        const importUrl = '/api/hr-platform-accounts/import'
        const exportUrl = '/api/hr-platform-accounts/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'platform_edit',
                    show: true,
                    click: editRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'platform_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        return {
            // accountTypeList,
            selectedRowsArr,
            exportText,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            customerValue,
            columns,
            params,
            searchData,
            searchResData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            clickQuantity,
            customerVisible,
            customerCancel,

            //事件

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            ImportData,
            exportData,

            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less"></style>
