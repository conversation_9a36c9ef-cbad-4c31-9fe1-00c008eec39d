<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="客户列表" :footer="null" :width="'800px'">
        <BasicTable
            ref="tableRefCustomer"
            api="/api/hr-accumulation-funds/client-page"
            :useIndex="true"
            :rowSelectionShow="false"
            :params="params"
            :columns="columns"
            :tableDataList="tableDataList"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
export default defineComponent({
    name: 'socialSecurityModal',
    props: {
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs(props)

        //  Data
        const tableDataList = ref([])
        const tableRefCustomer = ref()
        //筛选
        const params = ref<any>({})
        watch(visible, () => {
            if (visible.value) {
                // tableDataList.value = [...item.value]
                // 平台类型 0：其他 1：社保 2：医保 3：公积金 4：工资
                params.value = {}
                if (item.value?.platformType == '1') {
                    params.value.socialSecurityAccountId = item.value?.id
                } else if (item.value?.platformType == '2') {
                    params.value.medicalInsuranceAccountId = item.value?.id
                } else if (item.value?.platformType == '3') {
                    params.value.providentFundAccountId = item.value?.id
                } else if (item.value?.platformType == '4') {
                    params.value.payrollAccountId = item.value?.id
                } else {
                }
                nextTick(() => {
                    tableRefCustomer.value.refresh(1)
                })
            }
        })

        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                sorter: false,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                sorter: false,
            },
            {
                title: '客户类型',
                dataIndex: 'customerType',
                align: 'center',
                sorter: false,
            },
            {
                title: '员工数量',
                dataIndex: 'peoplesum',
                align: 'center',
                sorter: false,
            },
        ]

        // cancel handle
        const cancel = () => {
            emit('cancel')
        }

        return {
            tableRefCustomer,
            columns,
            params,
            tableDataList,
            cancel,
        }
    },
})
</script>
