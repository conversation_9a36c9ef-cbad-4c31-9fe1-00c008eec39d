<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'800px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                    <template #img>111</template>
                    <!-- <template #myPassword>
                        <InputPassword :value="formData.password" placeholder="密码" />
                    </template> -->
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message, InputPassword } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validateProportion, validateAccount } from '/@/utils/format'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'platformModal',
    // components: { InputPassword },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        let issuingBankList = ref<LabelValueOptions>([])
        onMounted(() => {
            //发放银行
            dictionaryDataStore()
                .setDictionaryData('ownedBank', '')
                .then((res: LabelValueOptions) => {
                    // issuingBankList.value = res
                    issuingBankList.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemName }
                    })
                })
        })
        //表单数据
        //请求
        const api = '/api/hr-platform-accounts'
        const { title, item, visible } = toRefs(props)
        // 平台类型
        const platformList = ref([
            {
                label: '其他',
                value: '0',
            },
            {
                label: '社保缴纳账户',
                value: '1',
            },
            {
                label: '医保缴纳账户',
                value: '2',
            },
            {
                label: '公积金缴纳账户',
                value: '3',
            },
            {
                label: '工资发放账户',
                value: '4',
            },
        ])

        //平台类型修改
        const platformChanged = (value: string, option: inObject) => {
            formData.value.platformType = value
            formData.value.accountType = option.label
        }
        //发放银行修改
        const issuingBankChanged = (value: string, option: inObject) => {
            formData.value.issuingBank = option.label
        }
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '账号类型',
                name: 'platformType',
                type: 'change',
                options: platformList,
                onChange: platformChanged,
            },
            // {
            //     label: '平台类型',
            //     name: 'platformType',
            //     type: 'change',

            //     options: platformList,
            //     onChange: platformChanged,
            // },
            {
                label: '发放银行',
                name: 'issuingBank',
                type: 'change',
                options: issuingBankList,
                onChange: issuingBankChanged,
                required: false,
            },
            {
                label: '账户号',
                name: 'accountNumber',
                validator: validateAccount,
            },
            {
                label: '密码',
                name: 'password',
                // type: 'slots',
                // slots: 'myPassword',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            platformList,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
</style>
