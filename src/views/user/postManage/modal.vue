<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :className="item.slots">
                    <template #img>111</template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
// import { validatePhone } from '/@/utils/format'
export default defineComponent({
    name: 'PostManageMenu',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //请求
        const api = '/api/hr-stations'
        const { title, item, visible } = toRefs(props)
        // 获取全部行业分类
        // let industryList = ref<LabelValueOptions>([])
        // 获取全部职业分类
        let professionList = ref<LabelValueOptions>([])
        onMounted(() => {
            // 行业分类
            // request.get('/api/com-code-tables/getCodeTableByInnerName/industryType', {}).then((res) => {
            //     industryList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemName }
            //     })
            // })
            // // 职业分类
            // request.get('/api/com-code-tables/getCodeTableByInnerName/professionType', {}).then((res) => {
            //     industryList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemName }
            //     })
            // })
        })

        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '行业分类',
                name: 'industryType',
                type: 'SelectDic',
                disType: 'industryType',
                disParentId: 181,
                ruleType: 'number',
            },
            {
                label: '职责分类',
                name: 'professionType',
                type: 'SelectDic',
                disType: 'professionType',
                disParentId: 189,
                ruleType: 'number',
            },
            {
                label: '职位名称',
                name: 'professionName',
            },
            {
                label: '标签',
                name: 'labelName',
                required: false,
            },
            {
                label: '备注',
                name: 'remark',
                required: false,
                type: 'textarea',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    //类型
                    dictionaryDataStore().setDictionaryData('hr-stations', '/api/hr-stations/list', 'get', true)
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            // industryList,
            professionList,
        }
    },
})
</script>
