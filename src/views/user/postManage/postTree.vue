<template>
    <TreeSelect
        ref="TreeSelect"
        class="myTreeSelect"
        v-model:value="dataValue"
        style="width: 100%"
        :tree-data="treeData"
        tree-checkable
        :allow-clear="allowClear"
        showSearch
        :maxTagCount="itemForm?.maxTag ? +itemForm?.maxTag : undefined"
        treeNodeFilterProp="title"
        treeDefaultExpandAll
        :getPopupContainer="getPopupContainer"
        :placeholder="itemForm?.placeholder || (itemForm.disabled ? `暂无${itemForm.label}` : `请选择${itemForm.label}`)"
        :disabled="disabled ? disabled : itemForm.disabled"
    />
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue'

import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import request from '/@/utils/request'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'PostTree',
    components: {},
    props: {
        allowClear: {
            type: Boolean,
            default: true,
        },
        isAll: {
            type: Boolean,
            default: false,
        },
        value: {
            type: [Array, Object, String, Number],
            default: () => [],
        },
        itemForm: {
            type: Object,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:value', 'update:itemForm', 'change'],
    setup(props, { emit }) {
        const { isAll, value, itemForm } = toRefs(props)
        // const SHOW_PARENT = TreeSelect.SHOW_PARENT

        const dataValue = computed({
            get: () => value.value,
            set: (val) => {
                emit('update:value', val)
                emit('change', val)
            },
        })
        //请求
        const treeData = ref<TreeDataItem[]>([])
        onMounted(() => {
            dictionaryDataStore()
                .setDictionaryData('hr-stations', '/api/hr-stations/list')
                .then((res: any) => {
                    // emit('update:itemForm', {
                    //     ...itemForm.value,
                    //     options: res.map((item) => {
                    //         return { label: item.professionName, value: item.id, ...item }
                    //     }),
                    // })
                    itemForm.value.options = res.map((item) => {
                        return { label: item.professionName, value: item.id, ...item }
                    })
                    emit('update:itemForm', itemForm.value)
                    let industryTypeObj = {}
                    let professionTypeObj = {}
                    res.forEach((element) => {
                        let industryTypeId = element.industryType + 'industryType'
                        industryTypeObj[industryTypeId] = {
                            title: element.industryTypeName,
                            value: industryTypeId,
                            key: industryTypeId,
                            children: [],
                        }

                        let professionTypeId = element.industryType + '' + element.professionType + 'professionType'
                        if (!professionTypeObj[professionTypeId]) {
                            professionTypeObj[professionTypeId] = {
                                title: element.professionTypeName,
                                value: professionTypeId,
                                key: professionTypeId,
                                children: [],
                                parentID: industryTypeId,
                            }
                        }
                        let professionId = element.id
                        professionTypeObj[professionTypeId].children.push({
                            title: element.professionName,
                            value: professionId,
                            key: professionId,
                            children: [],
                        })
                    })

                    for (let key in professionTypeObj) {
                        industryTypeObj[professionTypeObj[key].parentID].children.push(professionTypeObj[key])
                    }
                    if (isAll.value) {
                        treeData.value = [
                            {
                                title: '全部',
                                value: 'qdall',
                                key: 'qdall',
                                children: Object.values(industryTypeObj),
                            },
                        ]
                    } else {
                        treeData.value = Object.values(industryTypeObj)
                    }
                })
        })

        return {
            treeData,
            dataValue,
            getPopupContainer: () => {
                return document.body
            },
        }
    },
})
</script>
<style lang="less"></style>
