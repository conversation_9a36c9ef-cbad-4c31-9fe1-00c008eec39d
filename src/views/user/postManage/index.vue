<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
        <Button type="primary" @click="importData">导入</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-stations/page"
        deleteApi="/api/hr-stations/deletes"
        :params="params"
        :columns="columns"
        :exportUrl="exportUrl"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp;
            <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :industryList="industryList"
        :professionList="professionList"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <ImportModal
        v-model:visible="importVisible"
        temUrl="/api/hr-stations/template"
        importUrl="/api/hr-stations/import"
        @getResData="searchData"
    />
</template>

<script lang="ts">
import { Modal, Tag } from 'ant-design-vue'
import { defineComponent, h, ref, onMounted, computed } from 'vue'
import { EditModalOption, SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import modal from './modal.vue'
import { getDynamicText, getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'PostManage',
    components: { MyModal: modal },
    setup() {
        // 获取行业分类
        let industryList: any = ref([])
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/industryType', {}).then((res) => {
                industryList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        // 修改行业分类
        const changeIndustry = (value: any) => {
            ;(params.value as any).industryTypeId = value.option?.value
            ;(params.value as any).industryType = value.option?.label
        }

        // 获取职责分类
        let professionList: any = ref([])
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/professionType', {}).then((res) => {
                professionList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        // 修改职责分类
        const changeProfession = (value: any) => {
            ;(params.value as any).professionTypeId = value.option?.value
            ;(params.value as any).professionType = value.option?.label
        }

        //筛选
        const params = ref<{}>({
            industryType: null,
            professionType: null,
            professionName: null,
        })
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '行业分类',
                key: 'industryTypeList',
                options: industryList,
                multiple: true,
            },
            {
                type: 'select',
                label: '职责分类',
                key: 'professionTypeList',
                options: professionList,
                multiple: true,
            },
            {
                type: 'string',
                label: '职位名称',
                key: 'professionName',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '行业分类',
                dataIndex: 'industryType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.industryTypeName
                },
                width: 150,
            },
            {
                title: '职责分类',
                dataIndex: 'professionType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.professionTypeName
                },
                width: 150,
            },
            {
                title: '职位名称',
                dataIndex: 'professionName',
                align: 'center',
                width: 150,
            },
            {
                title: '标签',
                dataIndex: 'labelName',
                align: 'center',
                customRender: ({ record }) => {
                    return record.newLabelName.slice(0, -1)
                },
                width: 200,
            },
            {
                title: '备注',
                dataIndex: 'remark',
                align: 'center',
                width: 150,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        const modalTitle = ref('新增岗位')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增岗位'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑岗位'
            currentValue.value = { ...record }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
            })
        }

        const modalCancel = () => {
            showEdit.value = false
            // modalTitle.value = '新增岗位'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 导入
        const importVisible = ref(false)
        const exportUrl = '/api/hr-stations/export'
        const importData = () => {
            importVisible.value = true
        }
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'postManage_edit',
                    show: true,
                    click: editRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'postManage_edit',
                //     show: true,
                //     click: deleteRow,
                // },
            ]),
        )
        // const myOperationClick = (item, record) => {
        //     switch (item.auth) {
        //         case 'postManage_add':
        //             editRow(record)
        //             break
        //         case 'postManage_edit':
        //             deleteRow(record)
        //             break
        //         case 'postManage_edit':
        //             deleteRow(record)
        //             break
        //     }
        // }

        return {
            selectedRowsArr,
            exportText,
            industryList,
            professionList,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            //导入导出
            importVisible,
            importData,
            exportData,
            exportUrl,

            //事件
            changeIndustry,
            changeProfession,

            //操作按钮
            myOperation,
            // myOperationClick,
        }
    },
})
</script>

<style scoped lang="less"></style>
