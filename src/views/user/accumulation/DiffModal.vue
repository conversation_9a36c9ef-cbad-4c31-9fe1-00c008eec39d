<template>
    <BasicEditModalSlot title="补差" :visible="visible" @cancel="modalClose" width="1250px" :destroyOnClose="true">
        <div class="tip">
            <ExclamationCircleFilled style="font-size: 18px" />
            缴费年月发生变更，产生的差额将影响本期账单核算，是否继续？
        </div>
        <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
        <Button type="primary" @click="exportData">导出</Button>
        <BasicTable
            ref="tableRef"
            api="/api/hr-welfare-compensations/page"
            :columns="columns"
            :params="{
                ids: diffIds,
                ...params,
            }"
            :rowSelectionShow="false"
            :useIndex="true"
            :srcoll="{
                x: '100',
                y: '500',
            }"
        />
        <p>
            <BellFilled style="color: #6894fe; margin-right: 4px; font-size: 15px" />
            负数表示需要退还的金额，
            <span style="color: red">正数</span>
            表示需要
            <span style="color: red">补缴</span>
            的金额
        </p>
        <template #footer>
            <Button type="primary" danger @click="modalConfirm" :loading="confirmLoading">补差取消</Button>
            <Button type="primary" @click="modalClose">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, toRefs } from 'vue'
import { ExclamationCircleFilled, BellFilled } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'

export default defineComponent({
    name: 'DiffModal',
    components: { ExclamationCircleFilled, BellFilled },
    props: {
        visible: Boolean,
        diffIds: {
            type: Array,
            default: () => [],
            required: true,
        },
        columns: {
            type: Array as PropType<Recordable[]>,
            default: () => [],
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { diffIds, columns } = toRefs(props)
        const tableRef = ref()
        const params = ref<Recordable>({})
        const searchData = () => {
            tableRef.value.refresh()
        }

        const confirmLoading = ref(false)
        const modalConfirm = async () => {
            confirmLoading.value = true
            try {
                await request.post(`/api/hr-welfare-compensations/deletes`, diffIds.value)
                message.success('补差取消成功！')
                emit('confirm')
            } finally {
                confirmLoading.value = false
            }
        }
        const modalClose = () => {
            request.post(`/api/hr-welfare-compensations/confirm`, diffIds.value)
            emit('cancel')
        }

        const exportData = () => {
            // TODO 导出
        }
        return {
            exportData,
            tableRef,
            searchData,
            params,
            confirmLoading,
            modalConfirm,
            modalClose,
            searchOptions: [
                {
                    label: '单位编号',
                    key: 'unitNumber',
                },
                {
                    label: '单位名称',
                    key: 'clientName',
                },
                {
                    label: '姓名',
                    key: 'staffName',
                },
                {
                    label: '证件号码',
                    key: 'certificateNum',
                },
                {
                    label: '缴费年月',
                    key: 'paymentDateQuery',
                    type: 'monthrange',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
</style>
