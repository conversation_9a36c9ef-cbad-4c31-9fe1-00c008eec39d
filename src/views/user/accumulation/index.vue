<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'accumulation_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'accumulation_import'" @click="ImportData">导入</Button>
        <Button type="primary" v-auth="'accumulation_export'" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" v-auth="'accumulation_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-accumulation-funds/page"
        deleteApi="/api/hr-accumulation-funds/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #quantity="{ record }">
            <Button type="link" size="small" @click="clickQuantity(record)">{{ record.clientNumber }}</Button>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button> -->
            <!-- &nbsp; -->
            <!-- //删除 -->
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>
    <!-- 编辑 -->
    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
    <!-- 补差 -->
    <DiffModal :visible="showDiff" :diffIds="diffIds" :columns="diffColumns" @cancel="diffClose" @confirm="diffConfirm" />

    <!--    客户列表-->
    <CustomerModal :visible="customerVisible" :item="customerValue" @cancel="customerCancel" />

    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { SearchBarOption } from '/#/component'

import modal from './modal.vue'
import customerModal from './customerModal.vue'
import DiffModal from './DiffModal.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'AccumulationIndex',
    components: { MyModal: modal, CustomerModal: customerModal, DiffModal },
    setup() {
        //筛选
        const params = ref<{}>({
            typeName: null,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '公积金类型名称',
                key: 'typeName',
            },
        ]
        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '公积金类型名称',
                dataIndex: 'typeName',
                align: 'center',
                width: 150,
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
                width: 100,
            },
            {
                title: '收款单位名称',
                dataIndex: 'payeeName',
                align: 'center',
                width: 150,
            },
            {
                title: '收款单位账号',
                dataIndex: 'payeeAccount',
                align: 'center',
                width: 150,
            },
            {
                title: '收款单位开户行',
                dataIndex: 'payeeBank',
                align: 'center',
                width: 150,
            },
            {
                title: '单位比例',
                dataIndex: 'unitScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
                width: 100,
            },
            {
                title: '个人比例',
                dataIndex: 'personageScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
                width: 100,
            },
            {
                title: '客户数量',
                dataIndex: 'userStatus',
                align: 'center',
                slots: { customRender: 'quantity' },
                sorter: false,
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增公积金')
        // 当前编辑的数据
        const currentValue = ref(undefined)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增公积金'
            currentValue.value = undefined
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑公积金'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            currentValue.value = undefined
        }

        const diffIds = ref([])
        const modalConfirm = async (form, arr) => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
                diffIds.value = arr || []
                showDiff.value = Array.isArray(arr) && !!arr.length
            }
        }

        // 客户列表数据
        const customerValue = ref(undefined)
        // 客户列表显示隐藏
        const customerVisible = ref(false)
        // 点击客户数量
        const clickQuantity = (row) => {
            if (row.clientNumber != 0) {
                customerVisible.value = true
                customerValue.value = row
            }
        }

        // 关闭客户列表
        const customerCancel = () => {
            customerVisible.value = false
        }
        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'customerInfo_edit',
                    show: true,
                    click: editRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'customerInfo_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        // const myOperationClick = (item, record) => {
        //     switch (item.auth) {
        //         case 'customerInfo_edit':
        //             editRow(record)
        //             break
        //         case 'customerInfo_delete':
        //             deleteRow(record)
        //             break
        //     }
        // }
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-accumulation-funds/template'
        const importUrl = '/api/hr-accumulation-funds/import'
        const exportUrl = '/api/hr-accumulation-funds/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        const showDiff = ref(false)
        const diffClose = () => {
            diffIds.value = []
            showDiff.value = false
        }
        const diffConfirm = () => {
            diffIds.value = []
            showDiff.value = false
            tableRef.value.refresh()
        }

        return {
            selectedRowsArr,
            exportText,
            diffIds,
            showDiff,
            diffClose,
            diffConfirm,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            customerValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            clickQuantity,
            customerVisible,
            customerCancel,
            ImportData,
            exportData,
            //事件
            changeRoleId,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            //操作按钮
            myOperation,
            // myOperationClick,
            diffColumns: [
                {
                    title: '单位编号',
                    dataIndex: 'unitNumber',
                    align: 'center',
                    width: 160,
                    fixed: 'left',
                },
                {
                    title: '单位名称',
                    dataIndex: 'clientName',
                    align: 'center',
                    width: 150,
                    ellipsis: true,
                    fixed: 'left',
                },
                {
                    title: '姓名',
                    dataIndex: 'staffName',
                    align: 'center',
                    width: 120,
                    fixed: 'left',
                },
                {
                    title: '证件号码',
                    dataIndex: 'certificateNum',
                    align: 'center',
                    width: 180,
                    fixed: 'left',
                },
                {
                    title: '缴费年月',
                    dataIndex: 'paymentDate',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '个人公积金编号',
                    dataIndex: 'accumulationFundNum',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '单位',
                    dataIndex: 'unitAccumulationFund',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '个人',
                    dataIndex: 'personalAccumulationFund',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '总计',
                    dataIndex: 'accumulationFundTotal',
                    align: 'center',
                    width: 120,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less"></style>
