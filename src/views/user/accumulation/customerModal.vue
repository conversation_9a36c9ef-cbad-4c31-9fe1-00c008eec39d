<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="客户列表" :footer="null" :width="'800px'">
        <BasicTable
            ref="tableRefCustomer"
            api="/api/hr-accumulation-funds/client-page"
            :useIndex="true"
            :rowSelectionShow="false"
            :params="params"
            :columns="columns"
            :tableDataList="tableDataList"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同

import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'

export default defineComponent({
    name: 'CustomerModal',
    props: {
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs(props)

        //  Data
        const tableDataList = ref([])
        const tableRefCustomer = ref()
        watch(visible, () => {
            if (visible.value) {
                // tableDataList.value = [...item.value]
                params.value.providentFundTypeId = item.value?.id
                nextTick(() => {
                    tableRefCustomer.value.refresh(1)
                })
            }
        })
        //筛选
        const params = ref<any>({
            providentFundTypeId: null,
        })

        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                sorter: false,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                sorter: false,
            },
            {
                title: '客户类型',
                dataIndex: 'customerType',
                align: 'center',
                sorter: false,
            },
            {
                title: '员工数量',
                dataIndex: 'peoplesum',
                align: 'center',
                sorter: false,
            },
        ]

        // cancel handle
        const cancel = () => {
            emit('cancel')
        }

        return {
            tableRefCustomer,
            columns,
            params,
            tableDataList,
            cancel,
        }
    },
})
</script>
