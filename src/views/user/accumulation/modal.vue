<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'900px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template
                v-for="i in title?.includes('新增')
                    ? myOptions
                    : [
                          ...myOptions,
                          {
                              label: '缴费年月',
                              name: 'paymentDate',
                              type: 'month',
                              attrs: {
                                  disabledDate,
                              },
                          },
                      ]"
                :key="i"
            >
                <MyFormItem :item="i" v-model:value="formData[i.name]" />
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validateProportion, validateAccount } from '/@/utils/format'
import moment from 'moment'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import { RuleObject } from 'ant-design-vue/lib/form/interface'

export default defineComponent({
    name: 'AccumulationModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-accumulation-funds'
        const { title, item, visible } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }

        const tableRef = ref()
        let issuingBankList = ref<LabelValueOptions>([])
        onMounted(() => {
            //发放银行
            dictionaryDataStore()
                .setDictionaryData('ownedBank', '')
                .then((res: LabelValueOptions) => {
                    // issuingBankList.value = res
                    issuingBankList.value = res.map((item) => {
                        return { label: item.itemName, value: item.itemName }
                    })
                })
        })
        //发放银行修改
        const issuingBankChanged = (value: string, option: inObject) => {
            formData.value.payeeBank = option.label
        }
        const validateNo = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.resolve()
            } else {
                return validateAccount(rule, value)
            }
        }

        const myOptions = ref([
            {
                label: '公积金类型名称',
                name: 'typeName',
            },
            {
                label: '地区',
                name: 'area',
            },
            {
                label: '收款单位名称',
                name: 'payeeName',
                required: false,
            },
            {
                label: '收款单位账号',
                name: 'payeeAccount',
                validator: validateNo,
                required: false,
            },
            {
                label: '收款单位开户行',
                name: 'payeeBank',
                showbr: true, //换行
                type: 'change',
                options: issuingBankList,
                onChange: issuingBankChanged,
                required: false,
            },
            {
                label: '单位比例',
                name: 'unitScale',
                validator: validateProportion,
            },
            {
                label: '个人比例',
                name: 'personageScale',
                validator: validateProportion,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                formData.value.unitScale = formData.value.unitScale ? formData.value.unitScale + '%' : ''
                formData.value.personageScale = formData.value.personageScale ? formData.value.personageScale + '%' : ''
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    let params = {
                        unitScale: formData.value.unitScale.replace(/%/g, ''),
                        personageScale: formData.value.personageScale.replace(/%/g, ''),
                    }
                    let res
                    // return
                    if (title.value?.includes('新增')) {
                        res = await request.post(api || '', { ...formData.value, ...params })
                        message.success('新增成功!')
                    } else {
                        res = await request.put(api || '', { ...formData.value, ...params })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value, res)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        const disabledDate = (date) => {
            // 当前年已经过去的月份
            return moment(date).format('YYYY') !== moment().format('YYYY') || moment(date) > moment()
        }

        return {
            disabledDate,
            tableRef,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 50%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
</style>
