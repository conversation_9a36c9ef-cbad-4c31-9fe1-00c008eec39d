<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :width="'900px'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 24 }"
            :rules="rules"
            class="form-flex"
        >
            <FormItem label="提醒内容" name="notificationContentIdList">
                <div class="reminderMethodBox">
                    <div style="margin-right: 20px; margin-top: 5px">
                        <input
                            type="checkbox"
                            class="input-checkbox"
                            id="inputCheckboxContent"
                            :checked="contentCheckedList.length === contentList.length"
                            @click="contentCheckedAll()"
                        />
                        <label style="cursor: pointer; margin: 3px 3px; vertical-align: middle">全选</label>
                    </div>

                    <div v-for="(item, index) in contentList" :key="index" style="margin-right: 20px; margin-top: 5px">
                        <input
                            type="checkbox"
                            :checked="contentCheckedList.indexOf(item.id) >= 0"
                            name="checkboxinput"
                            class="input-checkbox"
                            @click="contentCheckedOne(item.id)"
                        />
                        <label style="cursor: pointer; margin: 3px 3px; vertical-align: middle">{{ item.value }}</label>
                    </div>
                </div>
            </FormItem>
            <FormItem label="提醒方式" name="reminderMethodList">

                <RadioGroup name="radioGroup" v-model:value="formData.reminderMethod" @change="reminderCheckedOne">
                    <template v-for="item in reminderList" :key="item.value">
                        <Radio :value="item.value">{{ item.label }}</Radio>
                    </template>
                </RadioGroup>
            </FormItem>
            <FormItem label="状态" name="states">
                <RadioGroup name="radioGroup" v-model:value="formData.states">
                    <Radio :value="0">启用</Radio>
                    <Radio :value="1">禁用</Radio>
                    <!-- <Radio :value="1">禁用</Radio> -->
                </RadioGroup>
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'AccumulationModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-notification-messages'
        const { item, visible } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }

        const tableRef = ref()

        const myOptions = ref([
            {
                label: '提醒内容',
                name: 'notificationContentIdList',
                default: [],
                ruleType: 'array',
                message: '请选择提醒内容',
            },
            {
                label: '提醒方式',
                name: 'reminderMethod',
                // default: [],
                // ruleType: 'array',
                message: '请选择提醒方式',
            },
            {
                label: '状态',
                name: 'states',
                ruleType: 'number',
            },
        ])
        //提醒方式
        const reminderList = ref([
            {
                value: '0',
                label: '不提醒',
            },
            {
                value: '1',
                label: '通知提醒框',
            },
            {
                value: '2',
                label: '消息列表',
            },
            {
                value: '3',
                label: '消息列表+提醒框',
            },
        ])
        const reminderCheckedList = ref<any[]>([])
        const isCheckedAll = ref(false)

        //提醒方式单选
        const reminderCheckedOne = (e) => {
            formData.value.reminderMethod = e.target.value
        }
        //提醒方式全选
        const reminderCheckedAll = () => {
            isCheckedAll.value = !isCheckedAll.value
            if (isCheckedAll.value) {
                // 全选时
                reminderCheckedList.value = []
                reminderList.value.map((val) => {
                    return reminderCheckedList.value.push(val.id)
                })
            } else {
                reminderCheckedList.value = []
            }
            formData.value.reminderMethodList = reminderCheckedList.value
        }

        //提醒内容
        const contentList = ref([])
        const contentCheckedList = ref<any[]>([])
        const isCheckedAllContent = ref(false)
        // 获取提醒内容
        const getContent = (data) => {
            request.get('/api/hr-notification-messages/selectcontent', { id: data.id }).then((res) => {
                contentList.value = res.map((item) => {
                    return {
                        id: item.notificationContentId,
                        value: item.reminderContent,
                    }
                })
                getData(item.value)
            })
        }

        //提醒内容单选
        const contentCheckedOne = (id: any) => {
            let idIndex = contentCheckedList.value.indexOf(id)
            let eleContent: any = document.getElementById('inputCheckboxContent')
            if (idIndex >= 0) {
                // 如果已经包含了该id, 则去除(单选按钮由选中变为非选中状态)
                contentCheckedList.value.splice(idIndex, 1)
            } else {
                // 选中该checkbox
                contentCheckedList.value.push(id)
            }
            if (contentCheckedList.value.length > 0) {
                isCheckedAllContent.value = false
                eleContent.indeterminate = true
                if (contentCheckedList.value.length == contentList.value.length) {
                    isCheckedAllContent.value = true
                    eleContent.indeterminate = false
                }
            } else {
                eleContent.indeterminate = false
            }
            formData.value.notificationContentIdList = contentCheckedList.value
        }
        //提醒内容全选
        const contentCheckedAll = () => {
            isCheckedAllContent.value = !isCheckedAllContent.value
            if (isCheckedAllContent.value) {
                // 全选时
                contentCheckedList.value = []
                contentList.value.map((val: any) => {
                    return contentCheckedList.value.push(val.id)
                })
            } else {
                contentCheckedList.value = []
            }
            // isCheckedAllContent.value = false
          
            formData.value.notificationContentIdList = contentCheckedList.value
        }
        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                reminderCheckedList.value = []
                contentCheckedList.value = []
                isCheckedAllContent.value = false
                getContent(item.value)
                // getData(item.value)
                // formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })
        //获取数据
        const getData = (data) => {
            request.get('/api/hr-notification-messages/selects', { id: data.id }).then((res) => {
                formData.value = res
                nextTick(() => {
                    //提醒内容
                 
                    if (formData.value.reminderContentid?.length == contentList.value.length) {
                        //全选
                        let eleContent: any = document.getElementById('inputCheckboxContent')
                        eleContent.indeterminate = false
                        isCheckedAllContent.value = false
                        contentCheckedAll()
                    } else {
                        let eleContent: any = document.getElementById('inputCheckboxContent')
                        eleContent.indeterminate = false
                        formData.value.reminderContentid?.map((item) => {
                            contentCheckedOne(item)
                        })
                    }
                    formData.value.reminderMethod = formData.value.reminderMethod
                })
            })
        }

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = async () => {
            formInline.value
                .validate()
                .then(async () => {
                    let myItem: any = item.value
                    formData.value.notificationId = myItem.id
                    await request.put(api || '', formData.value)
                    message.success('编辑成功!')
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            tableRef,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            reminderList,
            reminderCheckedList,
            reminderCheckedAll,
            reminderCheckedOne,
            contentList,
            contentCheckedList,
            contentCheckedAll,
            contentCheckedOne,
        }
    },
})
</script>
<style scoped lang="less">
.reminderMethodBox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.input-checkbox {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 3px;
    margin-left: 5px;
    // border-color: #d9d9d9;
}
input[type='checkbox'] {
    width: 16px;
    height: 16px;
    margin-top: 2px;
    position: relative;
    // border-color: #d9d9d9;
}
input[type='checkbox']::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;
    width: 100%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 17%;
}
input[type='checkbox']::after {
    position: absolute;
    top: 0;
    color: #000;
    width: 16px;
    height: 16px;
    display: inline-block;
    visibility: visible;
    padding-left: 0px;
    text-align: center;
    content: ' ';
    border-radius: 3px;
}
input[type='checkbox']:checked::after {
    content: '✓';
    color: #fff;
    font-size: 10px;
    line-height: 15px;
    background-color: #608ff4;
}
input[type='checkbox']:indeterminate::after {
    background-color: #6894fe;
    width: 12px;
    height: 11px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1);
}
</style>
