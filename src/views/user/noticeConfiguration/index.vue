<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="batchChangeState('enable')" v-auth="'noticeConfiguration_enable'">批量启用</Button>
        <Button type="primary" danger @click="batchChangeState('disable')" v-auth="'noticeConfiguration_disEnable'"
            >批量禁用</Button
        >
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-notification-messages/page"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="editRow(record)" v-auth="'noticeConfiguration_edit'">编辑</Button>
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'

import request from '/@/utils/request'
import modal from './modal.vue'
import { SearchBarOption } from '/#/component'
export default defineComponent({
    name: 'NoticeIndex',
    components: { MyModal: modal },
    setup() {
        onMounted(() => {})

        // 提醒方式
        const reminderMethodList = ref<LabelValueOptions>([
            {
                label: '不提醒',
                value: 0,
            },
            {
                label: '提醒框',
                value: 1,
            },
            {
                label: '消息列表',
                value: 2,
            },
            {
                label: '消息列表+提醒框',
                value: 3,
            },
        ])
        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '名称',
                key: 'notificationName',
            },
            {
                type: 'select',
                label: '提醒方式',
                key: 'reminderMethodList',
                options: reminderMethodList,
                multiple: true,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '名称',
                dataIndex: 'notificationName',
                align: 'center',
                // sorter: false,
                width: 150,
            },
            {
                title: '提醒内容',
                dataIndex: 'reminderContent',
                align: 'center',
                // slots: { customRender: 'certificateType' },
                // sorter: false,
                width: 200,
            },
            {
                title: '提醒方式',
                dataIndex: 'reminderMethod',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 1) {
                        text = '通知提醒框'
                    } else if (text == 2) {
                        text = '消息列表'
                    } else if (text == 3) {
                        text = '消息列表+提醒框'
                    } else if (text == 0) {
                        text = '不提醒'
                    }
                    return text
                },
                // sorter: false,
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'states',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 1) {
                        text = '禁用'
                    } else if (text == 0) {
                        text = '启用'
                    } else {
                        text = ''
                    }
                    return text
                },
                // sorter: false,
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 100,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        const modalTitle = ref('编辑通知配置')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '编辑通知配置'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑通知配置'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '编辑通知配置'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        // 选中的ID
        const selectIds = ref<(string | number)[]>([])
        // 多选
        const selectedRowsArr = (item) => {
            let ids: (string | number)[] = []
            ids = item.map((val: inObject) => {
                return { id: val.id, states: true }
            })
            selectIds.value = ids
        }

        //批量启用禁用
        const batchChangeState = (type) => {
            if (selectIds.value == null || selectIds.value?.length == 0) {
                message.error('请先选择一条数据!')
                return
            }
            let params = {}
            if (type == 'enable') {
                //启用
                selectIds.value.map((item: any) => {
                    item.states = 0
                })
            } else {
                //禁用
                selectIds.value.map((item: any) => {
                    item.states = 1
                })
            }
            params = selectIds.value
            request.post('/api/hr-notification-messages/updete', params).then((res) => {
                tableRef.value.refresh()
            })
        }

        return {
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            reminderMethodList,
            batchChangeState,
            selectedRowsArr,
            selectIds,

            //事件
        }
    },
})
</script>

<style scoped lang="less"></style>
