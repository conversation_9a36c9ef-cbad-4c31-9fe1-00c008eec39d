<template>
    <div class="main">
        <!-- <Row>
            <Col :span="6" v-for="item in cardList" :key="item" style="padding: 20px"> -->
        <div v-for="item in cardList" :key="item" style="padding: 20px">
            <Card class="cardForm" v-if="!item.sealUrl">
                <div class="top">
                    <div class="topIcon" v-if="item.sealName == '企业公章'">
                        <img class="iconImage" src="~//@/assets/enterprise.png" alt="" />
                    </div>
                    <div class="topIcon" v-else-if="item.sealName == '法人专用章'" style="background-color: #fe6868">
                        <img class="iconImage" src="~//@/assets/seal.png" alt="" />
                    </div>
                    <div class="topIcon" v-else-if="item.sealName == '财务专用章'" style="background-color: #e9ac23">
                        <img class="iconImage" src="~//@/assets/money.png" alt="" />
                    </div>
                    <div class="topIcon" v-else-if="item.sealName == '合同专用章'" style="background-color: #fe6868">
                        <img class="iconImage" src="~//@/assets/contract.png" alt="" />
                    </div>
                    <div class="topIcon" v-else-if="item.sealName == '发票专用章'" style="background-color: #3eb889">
                        <img class="iconImage" src="~//@/assets/plane.png" alt="" />
                    </div>
                    <div class="topIcon" v-else-if="item.sealName == '人力专用章'">
                        <img class="iconImage" src="~//@/assets/employees.png" alt="" />
                    </div>
                </div>

                <div class="title">{{ item.sealName }}</div>
                <div class="tips">图片大小≤2MB;<br />透明背景的PNG格式；签章名称需与企业名称一致</div>
                <div class="see" @click="seeImg">查看图片示例</div>
                <div class="btn" @click="showModal(item)">
                    <PlusCircleOutlined :style="{ fontSize: '24px', color: '#FFFFFF', padding: '11px' }" />
                </div>
            </Card>

            <Card class="cardForm" v-else>
                <div class="cardTop">
                    <div class="cardImg">
                        <img :src="item.sealUrl" class="image" alt="avatar" />
                    </div>
                </div>
                <div class="cardBottom">{{ item.sealName }}</div>
                <div class="cardBtnBox">
                    <div class="mask">
                        <div class="maskBtn">
                            <Button size="small" @click="download(item)" style="color: #3eb889">下载</Button>

                            <Button size="small" @click="editRow(item)" style="color: #6894fe; margin-left: 3px">更改</Button>

                            <Button size="small" @click="deleteRow(item)" style="color: #fe6868; margin-left: 3px">删除</Button>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
        <!-- </Col>

            <Col :span="6" style="padding: 20px"> -->
        <Card class="cardForm" style="margin: 20px">
            <div class="top">
                <div class="myTopIcon">
                    <PlusOutlined :style="{ fontSize: '39px', color: '#FFFFFF', padding: '15px' }" />
                </div>
            </div>

            <div class="title">{{ '自定义章' }}</div>
            <div class="tips">324*324px且≤2MB;<br />透明背景的PNG格式；签章名称需与企业名称一致</div>
            <div class="see" @click="seeImg">查看图片示例</div>
            <div class="btn" @click="createRow">
                <PlusCircleOutlined :style="{ fontSize: '24px', color: '#FFFFFF', padding: '11px' }" />
            </div>
        </Card>

        <!-- </Col>
        </Row> -->
    </div>
    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
    <!--    查看图片示例-->
    <ImgModal :visible="imgShowEdit" :title="imgModalTitle" :imageUrl="imageUrl" @cancel="imgModalCancel" />
</template>

<script lang="ts">
import { Modal, message, Card } from 'ant-design-vue'
import { defineComponent, ref, onMounted } from 'vue'
import downFile from '/@/utils/downFile'

import request from '/@/utils/request'
import modal from './modal.vue'
import { PlusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import samplePicture from '/@/assets/samplePicture.png'
export default defineComponent({
    name: 'SealManage',
    components: {
        MyModal: modal,
        Card,
        PlusCircleOutlined,
        PlusOutlined,
    },
    setup() {
        onMounted(() => {
            getDataList()
        })
        // 获取数据
        const getDataList = async () => {
            await request.get('/api/hr-sealses/selectsealses', {}).then((res) => {
                cardList.value = res
            })
        }

        const cardList = ref<object[]>([])

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增印章')
        // 当前编辑的数据
        const currentValue = ref(null)
        const showModal = (item) => {
            // if (item.sealUrl) { //
            editRow(item)
            // }
        }
        // 新增
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增印章'
            currentValue.value = null
        }
        // 编辑
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑印章'
            currentValue.value = { ...record }
        }
        // 删除
        const deleteRow = (record) => {
            Modal.confirm({
                title: '确认',
                content: '是否确认删除所选内容？',
                async onOk() {
                    await request
                        .put('/api/hr-sealses/delect', record)
                        .then((res) => {
                            message.success('删除成功!')
                            getDataList()
                        })
                        .catch((ref) => {
                            console.log(ref)
                        })
                },
                onCancel() {
                    // reject({ massage: '用户取消删除' })
                    message.error('用户取消删除!')
                },
            })
        }
        // 下载
        const download = (record) => {
            // window.location.href = record.sealUrl
            // downFile('open', record.sealUrl || '', '', {})
            downFile('get', record.sealUrl || '', record.sealName + record.sealUrl.substring(record.sealUrl.lastIndexOf('.')), {})
        }

        //查看图片示例
        const imgShowEdit = ref(false)
        const imgModalTitle = ref('新增印章')
        const imageUrl = ref()

        const seeImg = () => {
            imgShowEdit.value = true
            // imageUrl.value = 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png'
            imageUrl.value = samplePicture
            imgModalTitle.value = '查看图片示例'
        }

        const imgModalCancel = () => {
            imgShowEdit.value = false
            imgModalTitle.value = '查看图片示例'
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增印章'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            getDataList()
        }

        return {
            cardList,
            createRow,
            showEdit,
            modalTitle,
            currentValue,
            modalCancel,
            modalConfirm,
            editRow,
            showModal,
            deleteRow,
            download,
            seeImg,
            imgShowEdit,
            imgModalTitle,
            imgModalCancel,
            imageUrl,
        }
    },
})
</script>

<style scoped lang="less">
.main {
    display: flex;
    flex-wrap: wrap;
}
.cardForm {
    width: 298px;
    height: 306px;
    line-height: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0px 2px 6px 0px rgba(213, 217, 229, 100);
    font-family: SourceHanSansSC-regular;
    position: relative;

    .top {
        width: 100%;
        display: flex;
        justify-content: center;
    }
    .topIcon {
        width: 69px;
        height: 69px;
        line-height: 20px;
        background-color: rgba(104, 148, 254, 100);
        border-radius: 50%;
        margin: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        .iconImage {
            width: 40px;
            height: 40px;
            padding: '15px';
        }
    }
    .myTopIcon {
        width: 69px;
        height: 69px;
        line-height: 20px;
        background-color: rgba(187, 187, 190, 100);
        border-radius: 50%;
        margin: 15px;
    }
    .title {
        color: rgba(51, 51, 51, 100);
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .tips {
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        margin-bottom: 10px;
    }
    .see {
        width: 100%;
        height: 24px;
        color: rgba(104, 148, 254, 100);
        font-size: 16px;
        text-align: center;
    }
    .btn {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        height: 46px;
        margin: 0 !important;
        line-height: 20px;
        border-radius: 0px 0px 10px 10px;
        background-color: rgba(104, 148, 254, 100);
        text-align: center;
    }
    .cardTop {
        border-radius: 10px;
        // border: 1px solid rgba(187, 187, 187, 100);
        margin: 15px 10px 15px 10px;
        height: 203px;
        text-align: center;

        .cardImg {
            //margin: 10px;
            width: 100%;
            height: 100%;
            // background: url('./img.png');
            border-radius: 10px;
        }

        .image {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }
    }
    .cardBottom {
        color: rgba(51, 51, 51, 100);
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .cardBtnBox {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        .mask {
            width: 100%;
            height: 306px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            .maskBtn {
                position: absolute;
                bottom: 25px;
                left: 0;
                width: 100%;
            }
        }
    }
    &:hover {
        .cardBtnBox {
            display: block;
        }
    }
}
</style>
