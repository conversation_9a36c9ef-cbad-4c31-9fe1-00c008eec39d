<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                    <template #img>
                        <div class="sealUrlBox">
                            <ImportImg
                                v-model:imageUrl="formData.sealUrl"
                                class="sealUrl"
                                :size="2"
                                accept=".png"
                                @fetchImgInfo="imgVerify"
                            />
                            <!-- :limitWH="{ width: 324, height: 324 }" -->
                            <span class="sealUrlSpan"
                                ><br />图片大小≤2MB<br />图片透明背景的PNG格式<br />签章名称需与企业名称一致
                                <div class="sealUrlBtn" @click="seeImg">查看图片示例</div>
                            </span>
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>

    <!--    查看图片示例-->
    <ImgModal :visible="imgShowEdit" :title="imgModalTitle" :imageUrl="imageUrl" @cancel="imgModalCancel" />
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import samplePicture from '/@/assets/samplePicture.png'
export default defineComponent({
    name: 'SealManageModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/hr-sealses'
        const { title, item, visible } = toRefs(props)
        // const rules: Array<Object> = []
        const myOptions: any = ref([
            {
                label: '印章名称',
                name: 'sealName',
                // disabled: true
            },
            {
                label: '印章上传',
                name: 'sealUrl',
                slots: 'img',
                type: 'slots',
                message: '请上传印章',
                // default: 'https://cba-player-home-test.oss-cn-beijing.aliyuncs.com/2_1630925511470.png',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
             
                if (formData.value.sealType == false) {
                    //默认印章类型
                    myOptions.value[0].disabled = true
                } else {
                    //自定义印章
                    myOptions.value[0].disabled = false
                    formData.value.sealType = true
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        const imgVerify = () => {
            nextTick(() => {
                formInline.value?.validate('sealUrl')
            })
        }

        //查看图片示例
        const imgShowEdit = ref(false)
        const imgModalTitle = ref('新增印章')
        const imageUrl = ref()

        const seeImg = () => {
            imgShowEdit.value = true
            imageUrl.value = samplePicture
            imgModalTitle.value = '查看图片示例'
        }
        const imgModalCancel = () => {
            imgShowEdit.value = false
            imgModalTitle.value = '查看图片示例'
        }

        return {
            imgVerify,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            seeImg,
            imgShowEdit,
            imgModalTitle,
            imgModalCancel,
            imageUrl,
        }
    },
})
</script>
<style scoped lang="less">
.sealUrlBox {
    display: flex;
    .sealUrl {
        display: inline-block;
    }
    .sealUrlSpan {
        display: inline-block;
        vertical-align: top;
        color: #bbbbbe;
        font-size: 14px;
    }
    .sealUrlBtn {
        margin-top: 5px;
        font-size: 16px;
        color: #6894fe;
    }
}
</style>
