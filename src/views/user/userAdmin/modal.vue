<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]" />
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone, validateUserName } from '/@/utils/format'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { number } from 'vue-types'
export default defineComponent({
    name: 'AllotMenu',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/users'
        const billMaker = ref()
        const reimburseType = ref()
        const { title, item, visible, roleList } = toRefs(props)
        const selectChanged = (value: string, option: any) => {
            if (value == '34' && option.roleKey == 'accounting') {
                reimburseState.value = true
            }
            formData.value.roleIdList = [value]
        }
        // 工作电话校验
        const validateTelephone = (rule: RuleObject, value) => {
            const teTelephone = /^(([0-9]{3,4}-)?[0-9]{7,8}|(1[34578]\d{9}))$/ //校验手机号和固定电话

            if (!teTelephone.test(value) && value) {
                return Promise.reject('请正确填写电话号码')
            } else {
                return Promise.resolve()
            }
        }
        const reimburseState = ref(false)
        const userNameDisabled = ref<boolean>(false)
        const editDisabled = ref<boolean>(false)
        const roleIdsDisabled = ref<boolean>(false)
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '用户名',
                name: 'userName',
                disabled: userNameDisabled,
                validator: validateUserName,
            },
            {
                label: '密码',
                name: 'password',
                default: 'c123456',
                show: false,
                required: false,
            },
            {
                label: '角色',
                name: 'roleIds',
                type: 'change',
                ruleType: 'number',
                options: roleList,
                onChange: selectChanged,
                disabled: roleIdsDisabled,
            },
            {
                label: '姓名',
                name: 'realName',
                disabled: userNameDisabled,
            },
            {
                label: 'NC制单人编码',
                name: 'billmaker',
                show: reimburseState,
                required: false,
            },
            {
                label: '报销类型',
                name: 'reimburseType',
                type: 'select',
                options: [
                    {
                        label: '派遣',
                        value: 1,
                    },
                    {
                        label: '外包',
                        value: 2,
                    },
                ],
                required: false,
                show: reimburseState,
                ruleType: 'number',
            },
            {
                label: '手机号',
                name: 'phone',
                required: false,
                validator: validatePhone,
            },
            {
                label: '工作电话',
                name: 'workPhone',
                required: false,
                validator: validateTelephone,
            },
            {
                label: '工作地址',
                name: 'workAddress',
                required: false,
            },
            {
                label: '状态',
                name: 'userStatus',
                ruleType: 'boolean',
                type: 'switch',
                default: true,
                disabled: userNameDisabled,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
                roleIdsDisabled.value = false
                // 应要求虽然是单选但是传数组
                if (item.value) {
                    // editDisabled.value = true
                    if (item.value?.roleKeys == 'super_admin' || item.value?.roleKeys == 'developer') {
                        userNameDisabled.value = true
                        roleIdsDisabled.value = true
                    } else {
                        userNameDisabled.value = false
                        roleIdsDisabled.value = true
                    }
                    if (item.value?.roleKeys == 'client') {
                        roleIdsDisabled.value = true
                    }
                    let roleId = parseInt(item.value?.roleIds.split()[0])
                    formData.value.roleIds = roleId
                    formData.value.roleIdList = [roleId]
                } else {
                    userNameDisabled.value = false
                    // editDisabled.value = false
                }
            }
            if (formData.value.roleKeys == 'accounting' && title.value == '编辑用户') {
                reimburseState.value = true
            } else {
                reimburseState.value = false
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            resetFormData()
            emit('cancel')
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            billMaker,
            reimburseType,
        }
    },
})
</script>
