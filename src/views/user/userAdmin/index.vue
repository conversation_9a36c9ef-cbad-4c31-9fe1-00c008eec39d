<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'userAdmin_add'" @click="createRow">新增</Button>
        <Button type="primary" @click="importData">导入</Button>
        <Button type="primary" v-auth="'userAdmin_delete'" danger @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/users/page"
        deleteApi="/api/users/deletes"
        :params="params"
        :columns="columns"
        :checkboxProps="checkboxProps"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="myOperationClick" />
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :roleList="roleListEdit"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <CustomerAssignment :currentData="allotItem" :visible="showAllotMenu" @confirm="allotConfirm" @cancel="allotClose" />
    <ImportModal
        v-model:visible="importVisible"
        temUrl="/api/users/template"
        importUrl="/api/users/import"
        @getResData="searchData"
    />
</template>

<script lang="ts">
import { Tag, message } from 'ant-design-vue'
import { defineComponent, h, ref, onMounted } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import modal from './modal.vue'
import CustomerAssignment from './customerAssignment.vue'

import { userStatusList } from '/@/utils/dictionaries'
import { getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'UserAdmin',
    components: { MyModal: modal, CustomerAssignment },
    setup() {
        // 获取全部角色
        let roleList = ref<LabelValueOptions>([])
        let roleListEdit = ref<LabelValueOptions>([])

        let roleDeptsList = ref<LabelValueOptions>([])

        const isClientList = ref<LabelValueOptions>([
            {
                value: 0,
                label: '否',
            },
            { value: 1, label: '是' },
        ])
        onMounted(() => {
            request.get('/api/roles/list', {}).then((res) => {
                let roleArr = res.map((item) => {
                    return { label: item.roleName, value: item.id, ...item }
                })
                roleList.value = roleArr.map((item) => {
                    return { ...item, value: item.label }
                })
                roleListEdit.value = roleArr
                    ?.map((item) => {
                        let disabled = false
                        if (item.roleKey == 'client' || item.roleKey == 'super_admin' || item.roleKey == 'developer') {
                            disabled = true
                        }
                        return { ...item, disabled }
                    })
                    ?.filter((item: any) => {
                        return !item.isClientRole
                    })

            })
            // 所属部门
            request.get('/api/com-code-tables/getCodeTableByInnerName/department', {}).then((res) => {
                roleDeptsList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const params = ref({
            isClient: 0,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '用户名',
                key: 'userName',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'realName',
            },
            {
                type: 'string',
                label: '手机号',
                key: 'phone',
            },
            {
                //dai
                type: 'select',
                label: '所属部门',
                key: 'deptList',
                options: roleDeptsList,
                multiple: true,
            },
            {
                type: 'select',
                label: '角色',
                key: 'roleNameList',
                options: roleList,
                multiple: true,
            },
            {
                //dai
                type: 'select',
                label: '状态',
                key: 'userStatusList',
                options: userStatusList,
                multiple: true,
            },
            {
                //dai
                type: 'select',
                label: '是否客户',
                key: 'isClient',
                options: isClientList,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '用户名',
                dataIndex: 'userName',
                align: 'center',
                width: 150,
            },
            {
                title: '姓名',
                dataIndex: 'realName',
                align: 'center',
                width: 150,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '所属部门',
                dataIndex: 'roleDepts',
                align: 'center',
                width: 150,
            },
            {
                title: '工作电话',
                dataIndex: 'workPhone',
                align: 'center',
                width: 150,
            },
            {
                title: '工作地址',
                dataIndex: 'workAddress',
                align: 'center',
                width: 250,
                ellipsis: true,
            },
            {
                title: '角色',
                dataIndex: 'roleNames',
                align: 'center',
                width: 200,
            },
            {
                title: '状态',
                width: 100,
                dataIndex: 'userStatus',
                align: 'center',
                customRender: ({ text }) => {
                    return h(
                        Tag,
                        {
                            color: text ? 'green' : 'red',
                        },
                        {
                            default: () => h('span', text ? '启用' : '禁用'),
                        },
                    )
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }
        //修改密码
        const resetPassword = async (row: { id: string }) => {
            const api = '/api/users'
            await request.put(api, { id: row.id, password: 'c123456' })
            message.success('密码修改成功!')
        }
        //启用禁用修改
        const enableDisable = async (row: { id: string; userStatus: boolean }) => {
            const api = '/api/users'
            let newUserStatus = !row.userStatus
            await request.put(api, { id: row.id, userStatus: newUserStatus })
            message.success(`当前用户${newUserStatus ? '启用' : '禁用'}成功!`)
            tableRef.value.refresh()
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增用户')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增用户'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑用户'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增用户'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        //分配客户权限
        const showAllotMenu = ref(false)
        const allotItem = ref(null)
        const allotRow = (record) => {
            allotItem.value = record
            showAllotMenu.value = true
        }
        const allotConfirm = () => {
            tableRef.value.refresh()
            showAllotMenu.value = false
        }

        const allotClose = () => {
            showAllotMenu.value = false
        }
        // 导入
        const importVisible = ref(false)
        const importData = () => {
            importVisible.value = true
        }

        //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'userAdmin_edit',
                    show: true,
                },
                {
                    neme: '分配',
                    auth: 'userAdmin_distribute',
                    show: (record) => {
                        return record.isRestrictions
                    },
                },

                // {
                //     neme: '删除',
                //     auth: 'userAdmin_delete',
                //     show: (record) => {
                //         return record.isRemove
                //     },
                //     type: 'delete',
                // },
                {
                    neme: '禁用',
                    auth: 'userAdmin_disable',
                    show: (record) => {
                        return record.userStatus
                    },
                },
                {
                    neme: '启用',
                    auth: 'userAdmin_disable',
                    show: (record) => {
                        return !record.userStatus
                    },
                },
                {
                    neme: '重置密码',
                    auth: 'userAdmin_resetPassword',
                    show: (record) => {
                        return record.roleKeys != 'super_admin' && record.roleKeys != 'developer'
                    },
                },
            ]),
        )
        const myOperationClick = (item, record) => {
            switch (item.auth) {
                case 'userAdmin_edit':
                    editRow(record)
                    break
                case 'userAdmin_distribute':
                    allotRow(record)
                    break
                case 'userAdmin_delete':
                    deleteRow(record)
                    break
                case 'userAdmin_disable':
                    enableDisable(record)
                    break
                case 'userAdmin_resetPassword':
                    resetPassword(record)
                    break
            }
        }
        return {
            roleList,
            roleListEdit,
            // options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,

            params,
            options,

            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,

            //事件
            // changeRoleId,

            resetPassword,
            enableDisable,

            //分配客户权限
            showAllotMenu,
            allotItem,

            allotRow,
            allotConfirm,
            allotClose,
            //操作按钮
            myOperation,
            myOperationClick,
            checkboxProps: (record: inObject) => {
                return {
                    disabled: !record.isRemove || record.roleKeys == 'client', // Column configuration not to be checked
                }
            },
            //导入
            importVisible,
            importData,
        }
    },
})
</script>

<style scoped lang="less"></style>
