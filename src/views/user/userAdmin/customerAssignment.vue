<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="分配客户">
        <InputSearch
            v-model:value="filterName"
            @search="onSearch"
            @pressEnter="onSearch"
            placeholder="客户名称"
            style="width: 200px; margin-bottom: 10px"
        />
        <Tree
            v-if="treeData.length"
            checkable
            autoExpandParent
            :replaceFields="{ children: 'children', title: 'clientName', key: 'id' }"
            :checkStrictly="true"
            :tree-data="treeData"
            v-model:checkedKeys="checkedKeys"
            defaultExpandAll
        />
        <template #footer>
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" :loading="loading" @click="confirm">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, onMounted } from 'vue'
import { Tree } from 'ant-design-vue'
import request from '/@/utils/request'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { ArrToTree } from '/@/utils/index'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
export default defineComponent({
    name: 'AllotMenu',
    components: {
        Tree,
    },
    props: {
        visible: Boolean,
        currentData: {
            type: Object,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const filterName = ref('')
        const onSearch = async (key) => {
            try {
                await getClientList(key)
            } catch (error) {
                console.log(error)
            }
        }
        const treeData = ref<TreeDataItem[]>([])
        interface DefaultEvent {
            checked: (string | number)[]
            halfChecked: (string | number)[]
        }
        const checkedKeys = ref<DefaultEvent>({ checked: [], halfChecked: [] })

        const { visible, currentData } = toRefs<any>(props)

        let dataList: inObject[] = []
        // open modal 赋值
        watch(visible, async () => {
            try {
                await getClientList()
            } catch (error) {
                console.log(error)
            }
            if (visible.value && currentData.value?.id) {
                try {
                    await getData()
                } catch (error) {
                    console.log(error)
                }
            }
        })
        const getClientList = (key?) => {
            dictionaryDataStore()
                .setSelectclients('clients', true, '/api/hr-selectclients', key)
                .then((res: inObject[]) => {
                    dataList = res.map((item) => {
                        return { ...item, title: item.clientName }
                    })
                    treeData.value = dictionaryDataStore().dictionaryTreeData
                })
        }
        const getData = async () => {
            const data = await request.post(`/api/users/distribute`, {
                id: currentData.value.id,
                roleKeys: currentData.value.roleKeys,
            })

            treeData.value = ArrToTree(
                dataList.map((item: any) => {
                    let disabled = false
                    if (currentData.value.roleKeys == 'customer_service_staff') {
                        let unchecked = data.unchecked.filter((item) => {
                            return data.checked.every((x: string | number) => {
                                return x != item
                            })
                        })
                        disabled = unchecked.some((x: string | number) => {
                            return x == item.id
                        })
                    }
                    return { ...item, disabled }
                }),
                { id: 'id', pid: 'parentId' },
            )

            checkedKeys.value = { checked: data.checked, halfChecked: [] }
        }

        const loading = ref(false)
        const confirm = async () => {
            loading.value = true
            try {
                await request.post(`/api/hr-user-clients`, {
                    userId: currentData.value.id,
                    roleKeys: currentData.value.roleKeys,
                    clientIds: checkedKeys.value.checked,
                })
                emit('confirm')
                resetData()
            } finally {
                loading.value = false
            }
        }
        const cancel = () => {
            emit('cancel')
            resetData()
        }
        const resetData = () => {
            checkedKeys.value = { checked: [], halfChecked: [] }
            filterName.value = ''
            // treeData.value = []
        }
        return {
            filterName,
            onSearch,
            loading,
            treeData,
            confirm,
            cancel,
            checkedKeys,
        }
    },
})
</script>
