<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-certificates/page"
        deleteApi="/api/hr-certificates/deletes"
        :params="params"
        :columns="columns"
        :useIndex="true"
        :rowSelectionShow="false"
    >
        <template #certificateType="{ record }">
            <span v-if="record.certificateType == 0">{{ '不限' }}</span>
            <span v-if="record.certificateType == 1">{{ '图片' }}</span>
            <span v-else>{{ '文件' }}</span>
        </template>

        <template #operation="{ record }">
            <div v-if="record.id != 1 && record.id != 2 && record.id != 3 && record.id != 4 && record.id != 5">
                <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
                &nbsp;
                <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button>
            </div>
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'

import modal from './modal.vue'
import { SearchBarOption } from '/#/component'
export default defineComponent({
    name: 'DocumentType',
    components: { MyModal: modal },
    setup() {
        //筛选
        const params = ref<{}>({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '证件名称',
                key: 'certificateName',
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '名称',
                dataIndex: 'certificateName',
                align: 'center',
                // sorter: false,
            },
            {
                title: '文件类型',
                dataIndex: 'certificateType',
                align: 'center',
                slots: { customRender: 'certificateType' },
                // sorter: false,
            },
            {
                title: '文件数量',
                dataIndex: 'certificateQuantity',
                align: 'center',
                // sorter: false,
                customRender: ({ record }) => {
                    // 0是不限,1是限定,2是范围
                    if (record.certificateQuantity == 1) {
                        return record?.certificateLimitedQuantity
                    } else if (record.certificateQuantity == 2) {
                        return record?.certificateScopeStartQuantity + '-' + record?.certificateScopeEndQuantity
                    }
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        const showEdit = ref(false)
        const modalTitle = ref('新增证件类型')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增证件类型'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑证件类型'
            currentValue.value = { ...record }
        }
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增证件类型'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        return {
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,

            //事件
        }
    },
})
</script>

<style scoped lang="less"></style>
