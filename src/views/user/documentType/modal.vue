<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <!--            <template v-for="item in myOptions" :key="item">-->
            <!--                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :className="item.slots">-->
            <!--                    <template #img>111</template>-->
            <!--                </MyFormItem>-->
            <!--            </template>-->
            <FormItem label="名称" name="certificateName">
                <!-- 默认为string input -->
                <Input v-model:value="formData.certificateName" placeholder="请输入名称" />
            </FormItem>

            <FormItem label="文件类型" name="certificateType">
                <RadioGroup name="radioGroup" v-model:value="formData.certificateType">
                    <Radio :value="0">不限</Radio>
                    <Radio :value="1">图片</Radio>
                    <Radio :value="2">文件</Radio>
                </RadioGroup>
            </FormItem>

            <FormItem label="文件数量" name="certificateQuantity">
                <RadioGroup name="radioGroup" v-model:value="formData.certificateQuantity">
                    <Radio :value="0">不限</Radio>
                    <Radio :value="1">限定</Radio>
                    <Radio :value="2">范围</Radio>
                </RadioGroup>
            </FormItem>

            <FormItem label="限定数量" name="certificateLimitedQuantity" v-if="formData.certificateQuantity == 1">
                <InputNumber
                    v-model:value="formData.certificateLimitedQuantity"
                    placeholder="请输入限定数量"
                    :min="1"
                    style="width: 100%"
                    :precision="0"
                />
            </FormItem>

            <Row>
                <Col :span="12">
                    <FormItem
                        label="数量范围"
                        name="certificateScopeStartQuantity"
                        v-if="formData.certificateQuantity == 2"
                        style="width: 530px"
                    >
                        <InputNumber
                            v-model:value="formData.certificateScopeStartQuantity"
                            placeholder="请输最小数量"
                            :min="1"
                            :max="20"
                            :precision="0"
                            style="width: 45%"
                        />
                    </FormItem>
                </Col>
                <Col :span="12">
                    <FormItem
                        label=""
                        name="certificateScopeEndQuantity"
                        v-if="formData.certificateQuantity == 2"
                        style="margin-left: 50px"
                    >
                        <InputNumber
                            v-model:value="formData.certificateScopeEndQuantity"
                            placeholder="请输入最大数量"
                            :min="1"
                            :max="20"
                            :precision="0"
                            style="width: 100%"
                        />
                    </FormItem>
                </Col>
            </Row>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import { RuleObject } from 'ant-design-vue/es/form/interface'
export default defineComponent({
    name: 'documentTypeModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //请求
        const api = '/api/hr-certificates'
        const { title, item, visible } = toRefs(props)

        // 最大值最小值校验
        const validatePassNum = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.reject('请输入最小数量')
            }
            if (formData.value.certificateScopeEndQuantity < value) {
                return Promise.reject('最大数量必须大于最小数量')
            } else {
                return Promise.resolve()
            }
        }

        //表单数据
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '名称',
                name: 'certificateName',
            },
            {
                label: '文件类型',
                name: 'certificateType',
                default: 0,
                ruleType: 'number',
            },
            {
                label: '文件数量',
                name: 'certificateQuantity',
                default: 1,
                ruleType: 'number',
            },
            {
                label: '限定数量',
                name: 'certificateLimitedQuantity',
                ruleType: 'number',
            },
            {
                label: '最小数量',
                name: 'certificateScopeStartQuantity',
                ruleType: 'number',
                validator: validatePassNum,
            },
            {
                label: '最大数量',
                name: 'certificateScopeEndQuantity',
                ruleType: 'number',
                // validator: validatePassNum,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                formData.value.certificateLimitedQuantity = formData.value.certificateLimitedQuantity
                    ? formData.value.certificateLimitedQuantity
                    : 1
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }
        const myShow = ref(false)
        //文件数量
        // const certificateQuantityChange = (item) => {

        //     // myOptions.value[3].required = false
        //     // myOptions.value[4].required = false

        //     if (item.target.value == 1) {
        //         // 限定

        //         // myOptions.value[3].required = true
        //     } else if (item.target.value == 2) {
        //         //范围

        //         // myOptions.value[4].required = true
        //     }
        // }
        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    if (!formData.value.id) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            myShow,
        }
    },
})
</script>
