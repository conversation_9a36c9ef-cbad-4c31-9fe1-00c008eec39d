<template>
    <BasicEditModalSlot :visible="visible" @cancel="onCancel" :title="title" :width="'1200px'">
        <div class="examine" style="margin-top: 0">
            <Divider type="vertical" class="divid" />
            <span>报告信息</span>
            <div class="examine-flex">
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">员工姓名：</span>
                    <span>{{ item?.name }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">身份证号：</span>
                    <span>{{ item?.certificateNum }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">联系方式：</span>
                    <span>{{ item?.phone }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">所属单位：</span>
                    <span class="value" :title="item?.clientName">{{ item?.clientName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex">
                    <span class="label">报告类型：</span>
                    <span>{{ item?.reportTypeLabel }}</span>
                </div>
                <div class="item-flex">
                    <span class="label">报告名称：</span>
                    <span :title="item?.reportName">{{ item?.reportName }}</span>
                </div>
                <p class="linefeed"></p>
                <div class="item-flex" style="width: 100%">
                    <span class="label">报告内容：</span>
                    <span class="content">
                        <template v-if="item?.hrAppendixList && item?.hrAppendixList.length">
                            <span v-for="ele in item.hrAppendixList" :key="ele.id" class="imgWrapper">
                                <a href="javascript: void(0)" @click="previewFile(ele.fileUrl)">
                                    <img :src="ele.fileUrl" alt="" />
                                </a>
                            </span>
                        </template>
                    </span>
                </div>
            </div>
        </div>
        <OperationInfo :applyOpLogs="item?.hrApplyOpLogsList" v-if="item?.hrApplyOpLogsList?.length" />
        <div class="examine" v-if="modalType !== 'look'">
            <Divider type="vertical" class="divid" />
            <span>审批</span>
            <div class="examine-wrap">
                <p class="linefeed"></p>
                <Form ref="formInline" :model="formData" :rules="rules" class="form-flex">
                    <!-- 审批 -->
                    <FormItem name="denialReason" style="width: 100%">
                        <Textarea
                            v-model:value="formData.denialReason"
                            :rows="3"
                            allowClear
                            placeholder="若拒绝，请输入拒绝理由"
                        />
                    </FormItem>
                </Form>
            </div>
        </div>
        <template #footer>
            <div v-if="modalType !== 'look'">
                <Button @click="onConfirm(2)" class="delBtn">拒绝</Button>
                <Button @click="onConfirm(1)" type="primary" class="btn">通过</Button>
            </div>
            <div v-else></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import OperationInfo from '/@/views/serviceCentre/retirement/component/OperationInfo.vue'
import { valuesAndRules } from '/#/component'
import { message } from 'ant-design-vue'
import { previewFile } from '/@/utils/index'
export default defineComponent({
    name: 'ReportManagementModal',
    components: { OperationInfo },
    props: {
        title: String,
        visible: {
            type: Boolean,
            default: false,
        },
        modalType: {
            type: String,
            default: '',
        },
        item: {
            type: Object,
            default: () => {},
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { item, modalType } = toRefs(props)
        const refImportFile = ref()
        // 表单数据
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '拒绝理由',
                name: 'denialReason',
                required: false,
                default: '',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // cancel handle
        const onCancel = () => {
            modalType.value !== 'look' && resetFormData()
            emit('cancel')
        }
        // confirm handle
        const onConfirm = (flag) => {
            // 表单验证
            formInline.value
                .validate()
                .then(async () => {
                    if (isEmpty(formData.value.denialReason) && flag == 2) {
                        message.warn('请输入拒绝理由')
                    } else {
                        emit('confirm', {
                            ...formData.value,
                            id: item.value.id,
                            status: Number(flag),
                        })
                        resetFormData()
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }

        return {
            onCancel,
            onConfirm,
            rules,
            formData,
            myOptions,
            refImportFile,
            formInline,
            previewFile,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
    }
}
.examine {
    margin: 50px 0px 0px;
    .divid {
        border-left: 5px solid #1890ff;
        height: 26px;
    }
    .examine-wrap {
        margin-top: 10px;
        padding-left: 15px;
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}
.examine-flex {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    padding-left: 15px;
    .item-flex {
        width: 25%;
        margin: 5px 0px;
        .label {
            width: 75px;
            color: rgba(153, 153, 153, 1);
        }
        .value {
            width: 280px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
    .linefeed {
        width: 100%;
        padding: 0;
        margin: 0;
    }
}
.delBtn {
    background-color: @dangerous-color;
    color: rgba(255, 255, 255, 1);
    border: none;
}
.content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    .imgWrapper {
        margin-right: 10px;
        margin-bottom: 10px;
        width: 150px;
        height: 150px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
