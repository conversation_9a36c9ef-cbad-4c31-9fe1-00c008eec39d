<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'reportManagement_export'" @click="batchExport()">{{ exportText }}</Button>
        <Button type="primary" v-auth="'reportManagement_batch_pass'" @click="batchPassRow()">批量通过</Button>
        <Button type="primary" class="rejectBtn" v-auth="'reportManagement_batch_reject'" @click="batchReject()">
            批量拒绝
        </Button>
        <Button type="primary" class="rejectBtn" v-auth="'reportManagement_batch_remove'" @click="batchRemove()">
            批量删除
        </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-reports/page"
        deleteApi="/api/hr-reports/deletes"
        :exportUrl="'/api/hr-reports/export'"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="rejectCancelHandle"
        @confirm="rejectConfirm"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
    <MyModal
        :visible="modalVisible"
        :item="currentValue"
        :modalType="detailType"
        :title="modalTitle"
        @confirm="executeOperation"
        @cancel="cancelHandle"
    />
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, onMounted, computed } from 'vue'
import request from '/@/utils/request'
import { SearchBarOption } from '/#/component'
import { getDynamicText, getHaveAuthorityOperation, openNotification } from '/@/utils/index'

import modal from './modal.vue'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import downFile from '/@/utils/downFile'
export default defineComponent({
    name: 'ReportManagement',
    components: {
        MyModal: modal,
    },
    setup() {
        // 筛选
        let staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        let staffStatesList = ref<LabelValueOptions>([]) // 员工状态
        let reportTypeList = ref<LabelValueOptions>([]) // 报告类型

        let options: SearchBarOption[] = [
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '手机号码',
                key: 'phone',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                options: staffStatesList,
                multiple: true,
            },
            {
                label: '用工单位',
                key: 'clientIdList',
                placeholder: '用工单位',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '报告名称',
                key: 'reportName',
            },
            {
                type: 'select',
                label: '报告类型',
                key: 'reportTypeList',
                options: reportTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '上传时间',
                key: 'contractStartDateQuery',
            },
        ]

        // 表格数据
        const columns = ref([
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 110,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                width: 110,
                customRender: ({ record }) => {
                    return staffStatesList.value.find((el) => {
                        return record.staffStatus == el.value
                    })?.label
                },
            },
            {
                title: '用工单位',
                dataIndex: 'clientName',
                width: 180,
                align: 'center',
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return staffTypeList.value.find((el) => {
                        return record.personnelType == el.value
                    })?.label
                },
            },
            {
                title: '报告名称',
                dataIndex: 'reportName',
                align: 'center',
                width: 180,
                ellipsis: true,
            },
            {
                title: '报告类型',
                dataIndex: 'reportType',
                align: 'center',
                width: 100,
                customRender: ({ record }) => {
                    return reportTypeList.value.find((el) => {
                        return record.reportType == el.value
                    })?.label
                },
            },
            {
                title: '上传时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 180,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ])

        onMounted(() => {
            // 人员类型
            dictionaryDataStore()
                .setDictionaryData('staffType', '')
                .then((data: inObject[]) => {
                    staffTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 员工状态
            dictionaryDataStore()
                .setDictionaryData('staffStates', '')
                .then((data: inObject[]) => {
                    staffStatesList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
            // 员工状态
            dictionaryDataStore()
                .setDictionaryData('reportType', '')
                .then((data: inObject[]) => {
                    reportTypeList.value = data.map((item) => {
                        return { label: item.itemName, value: item.itemValue }
                    })
                })
        })

        // 表格dom
        const tableRef = ref()

        // 筛选
        const params = ref({})

        // 搜索
        const searchData = async () => {
            tableRef.value.refresh(1)
        }

        const rejectCancelHandle = () => {
            modalVisible.value = false
            checkerReason.value = ''
            showReject.value = false
            tableRef.value.checkboxReset()
        }

        const modalTitle = ref('')
        const modalVisible = ref(false)
        const detailType = ref('')

        // 当前编辑的数据
        const currentValue = ref<inObject>({})

        // 显示弹窗
        const showModal = (record, type) => {
            switch (type) {
                case 'look':
                    modalVisible.value = true
                    modalTitle.value = '查看'
                    break
                case 'audit':
                    modalVisible.value = true
                    modalTitle.value = '审阅'
                    break
            }
            getDetails(record.id)
            detailType.value = type
        }

        // 获取详细信息
        const getDetails = (id) => {
            request
                .get('/api/hr-reportsssa', { id: id })
                .then((res) => {
                    currentValue.value = {
                        ...res,
                        reportTypeLabel: reportTypeList.value.find((el) => {
                            return res.reportType == el.value
                        })?.label,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const cancelHandle = () => {
            modalVisible.value = false
        }

        // 多选
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        // 批量导出
        const batchExport = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        // 批量通过
        const batchPassRow = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.error('请至少选择一条数据!')
                return
            }
            request
                .post(
                    '/api/hr-reports/batch?type=0',
                    selectedRowsArr.value.map((el: any) => {
                        return { id: el.id, staffId: el.staffId, reportName: el.reportName }
                    }),
                )
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '所选数据审核已通过'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已审核通过成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    rejectCancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        const executeOperation = (obj) => {
            let msg = ''
            let params: inObject = { ...obj }
            switch (obj.status) {
                case 1:
                    msg = '审核已通过'
                    break
                case 2:
                    msg = '审核已拒绝'
                    break
            }
            request
                .put('/api/hr-reports', { ...currentValue.value, ...params })
                .then((res) => {
                    tableRef.value.refresh()
                    cancelHandle()
                    message.success(msg)
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        // 下载
        const downLoadFile = (record) => {
            downFile('get', `/api/hr-reports/download?id=${record.id}`, '', {})
        }

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'reportManagement_look',
                    show: true,
                    click: (record) => showModal(record, 'look'),
                },
                {
                    neme: '审阅',
                    auth: 'reportManagement_audit',
                    show: (record) => {
                        return record.status == 0
                    },
                    click: (record) => showModal(record, 'audit'),
                },
                {
                    neme: '下载',
                    auth: 'reportManagement_download',
                    show: true,
                    click: (record) => downLoadFile(record),
                },
            ]),
        )

        // 批量删除
        const batchRemove = () => {
            tableRef.value.deleteRow().then((ref) => {
                tableRef.value.checkboxReset()
            })
        }

        // 批量拒绝
        const showReject = ref(false)
        const batchReject = () => {
            if (selectedRowsArr.value.length <= 0) {
                message.warning('请至少选择一条数据')
                return false
            }
            modalTitle.value = '批量拒绝'
            showReject.value = true
        }
        const rejectRow = () => {
            request
                .post(
                    '/api/hr-reports/batch?type=1',
                    selectedRowsArr.value.map((item: inObject) => {
                        return {
                            id: item.id,
                            staffId: item.staffId,
                            denialReason: checkerReason.value,
                            reportName: item.reportName,
                        }
                    }),
                )
                .then((res: inObject) => {
                    tableRef.value.refresh()
                    let tip = ''
                    let success = '您选择的数据已拒绝'
                    if (res.error_status) {
                        tip = res.error_status
                        success = ',选择的其他数据已拒绝成功'
                    }
                    if (res.success?.length) {
                        tip += success
                    }
                    openNotification(tip)
                    rejectCancelHandle()
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        const checkerReason = ref('')
        // 确认弹窗
        const rejectConfirm = async () => {
            if (!checkerReason.value) {
                message.warning('请填写拒绝理由')
                return false
            }
            rejectRow()
            showReject.value = false
        }

        return {
            exportText,
            checkerReason,
            tableRef,
            columns,
            params,
            options,
            modalTitle,
            modalVisible,
            currentValue,
            detailType,
            showReject,
            // 事件
            searchData,
            confirm,
            cancelHandle,
            batchReject,
            rejectCancelHandle,

            //操作
            myOperation,
            selectedRowsArr,

            executeOperation,
            batchExport,
            rejectRow,
            batchPassRow,
            rejectConfirm,
            batchRemove,
        }
    },
})
</script>
<style scoped lang="less">
.downloadBtn {
    background-color: @upload-color;
    border: none;
}
.rejectBtn {
    background-color: @dangerous-color;
    border: none;
}
</style>
