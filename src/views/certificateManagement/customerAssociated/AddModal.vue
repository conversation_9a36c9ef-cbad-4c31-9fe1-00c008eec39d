<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="700px" :footer="null">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <div class="info">
                <template v-for="(itemForm, i) in myOptions" :key="i">
                    <MyFormItem
                        :width="itemForm.width"
                        :item="itemForm"
                        v-model:value="formData[itemForm.name]"
                        v-if="itemForm.label != 'NC制单人编码'"
                    >
                        <template #client>
                            <ClientSelectTree v-model:value="formData.clientId" :itemForm="{ placeholder: '客户名称' }" />
                        </template>
                        <template #ncClient>
                            <Select
                                :value="{ value: formData.ncCode }"
                                placeholder="请选择NC客户"
                                :options="qryResult"
                                showSearch
                                @change="(val, option) => selectChange(val, option)"
                                allowClear
                                labelInValue
                                :filterOption="filterTreeNode"
                            />
                        </template>
                    </MyFormItem>
                </template>
            </div>
        </Form>

        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="confirm">保存</Button>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { merchantstype } from '/@/utils/dictionaries'
import ClientSelectTree from '/@/components/ClientSelectTree/index'

export default defineComponent({
    name: 'Modal',
    components: { ClientSelectTree },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const value = ref<string[]>([])

        //表单数据
        //请求
        const api = '/api/nc-customers'
        const qryResult = ref<inObject[]>([])
        const billMaker = ref()
        const { title, item, visible } = toRefs(props)
        const ncClientName = ref({
            key: '',
            label: '',
            value: '',
        })

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '客户',
                name: 'clientId',
                type: 'slots',
                slots: 'client',
            },

            {
                label: 'NC客户',
                name: 'ncCode',
                type: 'slots',
                slots: 'ncClient',
                // ruleType: 'object',
            },
            {
                label: '客户简称',
                name: 'shortName',
                required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        const editorRef = ref()
        const filterTreeNode = (input, node) => {
            return node.label.includes(input)
        }
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            getNcQryCustomer()
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
            }
            console.log(formData.value)
        })
        const getNcQryCustomer = () => {
            request.get(`/api/nc/qryCustomer`).then((res) => {
                qryResult.value = res.map((el) => {
                    return { label: el.name, value: el.code, type: el.custsuptype }
                })
            })
        }
        const ncType = ref<number>()
        const selectChange = (val, option) => {
            console.log('sasa', val, option)
            formData.value.ncCode = val.key
            formData.value.name = val.label
            ncType.value = option.type
            // record.name = val.name
            // record.ncCode = val.code
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            let postBody = {
                shortName: formData.value.shortName,
                ncCode: formData.value.ncCode,
                ncName: formData.value.name,
                clientId: formData.value.clientId,
                custsuptype: ncType.value,
            }
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', postBody)
                        message.success('新增成功!')
                    } else if (title.value?.includes('编辑')) {
                        await request.put(api || '', {
                            id: formData.value.id,
                            shortName: formData.value.shortName,
                            ncCode: formData.value.ncCode,
                            ncName: formData.value.name,
                            clientId: formData.value.clientId,
                            custsuptype: ncType.value,
                        })
                        message.success('编辑成功!')
                    }

                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
            // editorRef.value.setHtml('')
        }
        const handleChange = (html, value) => {
            formData.value.informationContent = html
        }
        // 发布
        const issue = async () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (formData.value.informationTitle.length < 1 || formData.value.informationTitle.length >= 50) {
                        return message.warning('资讯标题字数在1~50之间')
                    }
                    if (formData.value.state === true) {
                        return message.warning('资讯已经发布')
                    }
                    if (title.value?.includes('新增资讯')) {
                        await request.post('/api/hr-informations', { ...formData.value, state: true }).then((res) => {
                            console.log(res)
                            message.success('发布成功!')
                        })
                    } else if (title.value?.includes('编辑资讯')) {
                        await request.put('/api/hr-informations', { ...formData.value, state: true }).then((res) => {
                            console.log(res)
                            message.success('发布成功!')
                        })
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((err) => {
                    console.log('表单验证失败', err)
                })
        }
        return {
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            handleChange,
            editorRef,
            value,
            issue,
            qryResult,
            getNcQryCustomer,
            selectChange,
            ncClientName,
            billMaker,
            filterTreeNode,
        }
    },
})
</script>
<style lang="less" scoped>
.info {
    height: 300px;
}
</style>
