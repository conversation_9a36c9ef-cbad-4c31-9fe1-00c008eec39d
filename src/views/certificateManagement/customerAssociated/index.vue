<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData(1)" />
    <div class="btns">
        <!-- <Button v-auth="'arriveRecord_create'" type="primary" @click="addNew">新建</Button> -->
        <Button type="primary" @click="addModal">新增</Button>
        <Button type="primary" @click="deleteRow" danger>批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/nc-customers/page"
        exportUrl="/api/hr-arrival-records/export"
        deleteApi="/api/nc-customers/deletes"
        :params="params"
        :columns="getColumns('details')"
        @selectedRowsArr="selHandle"
        :sorter="false"
    >
        <template #operation="{ record }">
            <Button size="small" type="primary" @click="showRow(record)">编辑</Button>
        </template>
    </BasicTable>
    <AddModal :visible="showAddModal" :item="currentRecord" :title="modalTitle" @cancel="addCancel" @confirm="addConfirm" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import AddModal from './AddModal.vue'

const tableRef = ref()
const params = ref({})
const showAddModal = ref(false)
const modalTitle = ref('')
const searchData = (num) => {
    tableRef.value.refresh(num)
}

const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    console.log(record)
    currentRecord.value = { ...record }
    modalTitle.value = '编辑'
    showAddModal.value = true
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const addModal = () => {
    showAddModal.value = true
    modalTitle.value = '新增'
    currentRecord.value = {}
}

const addCancel = () => {
    showAddModal.value = false
    tableRef.value.refresh()
}
const addConfirm = () => {
    showAddModal.value = false
    tableRef.value.refresh()
}

// 批量删除
const deleteRow = (row = { id: '' }) => {
    tableRef.value.deleteRow(row.id).then((ref) => {
        // console.log(row.id)
    })
}
const tableDataList = ref([])

const fetchTotalTableData = async (body = {}) => {
    const res = await request.post('/api/hr-arrival-records/total', body)
    tableDataList.value = [{ ...res, unitNumber: '合计' }] as any
}

fetchTotalTableData()

const getColumns = (type) => {
    const arr = [
        {
            title: '客户名称',
            dataIndex: 'clientName',
            width: type == 'total' ? 80 : 160,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '客户简称',
            dataIndex: 'shortName',
            width: type == 'total' ? 80 : 100,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: 'NC客户编号',
            dataIndex: 'ncCode',
            width: type == 'total' ? 500 : 100,
        },
        {
            title: 'NC客户名称',
            dataIndex: 'ncName',
            width: 150,
        },
        {
            title: 'NC客商类型',
            dataIndex: 'custsuptype',
            width: 60,
            customRender: ({ text }) => {
                if (text == 1) {
                    text = '客户'
                } else if (text == 2) {
                    text = '供应商'
                } else {
                    text = '客商'
                }
                return text
            },
        },
    ]
    if (type == 'total') {
        return [
            ...arr,
            {
                title: '操作',
                dataIndex: 'operation',
                width: 50,
                slots: { customRender: 'operation' },
            },
        ]
    } else {
        return [
            ...arr,

            {
                title: '操作',
                dataIndex: 'operation',
                width: 110,
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]
    }
}

const searchOptions: SearchBarOption[] = [
    {
        type: 'clientSelectTree',
        label: '客户名称',
        key: 'clientId',
        placeholder: '客户名称',
        maxTag: '0',
        multiple: false,
        checkStrictly: false,
    },
]
</script>

<style scoped lang="less"></style>
