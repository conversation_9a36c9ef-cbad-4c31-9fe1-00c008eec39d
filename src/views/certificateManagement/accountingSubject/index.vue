<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData(1)" />
    <div class="btns">
        <Button v-auth="'arriveRecord_create'" type="primary" @click="addNew">新增</Button>
        <Button v-auth="'arriveRecord_delete'" type="primary" danger @click="removeSome">批量删除</Button>
        <!-- <Button type="primary" @click="exportData">{{ exportText }}</Button> -->
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/nc-accounts/page"
        :columns="columns"
        :params="params"
        @selectedRowsArr="selHandle"
        :sorter="false"
    >
        <template #operation="{ record }">
            <Button v-auth="'arriveRecord_edit'" size="small" type="primary" @click="showRow(record)">编辑</Button>
            &nbsp;
            <Button v-auth="'arriveRecord_edit'" size="small" type="primary" danger @click="delRow(record)">删除</Button>
        </template>
    </BasicTable>

    <!-- 新增 -->
    <CreateModal
        v-model:visible="showCreate"
        :viewType="viewType"
        :currentRecord="currentRecord"
        :codeList="codeList"
        @confirm="searchData"
        @cancel="modalCancel"
    />
</template>

<script lang="ts" setup>
import { message, Modal } from 'ant-design-vue'
import { computed, ref, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import request from '/@/utils/request'
import CreateModal from './CreateModal.vue'

const showCreate = ref(false)
const tableRef = ref()
const params = ref({})

const searchData = (num) => {
    tableRef.value.refresh(num)
}
const viewType = ref<any>('')

const addNew = () => {
    viewType.value = 'add'
    showCreate.value = true
    currentRecord.value = {}
}

const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    viewType.value = 'edit'
    currentRecord.value = { ...record }
    showCreate.value = true
}
const delRow = (record) => {
    currentRecord.value = { ...record }
    Modal.confirm({
        title: '确认',
        content: '确认删除数据吗？',
        onOk: async () => {
            await request.delete(`/api/nc-accounts/${record.id}`, {})
            searchData(1)
        },
    })
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的数据！')
        return
    }
    Modal.confirm({
        title: '确认',
        content: '确认删除所选数据吗？',
        onOk: async () => {
            await request.post(
                `/api/nc-accounts/deletes`,
                selArr.value.map((i) => i.id),
            )
            searchData(1)
        },
    })
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
//导出
const exportData = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

const totalTableRef = ref()

watch(
    [() => params.value, () => selArr.value],
    ([newParams, newSelArr], [oldParams, oldSelArr]) => {
        // dynamicQuery()
    },
    { deep: true },
)

const dynamicQuery = async () => {
    if (exportText.value.indexOf('选中') != -1) {
        let body = {}
        body['ids'] = selArr.value.map((item: any) => {
            return item.id
        })
        await fetchTotalTableData(body)
    } else if (exportText.value.indexOf('筛选') != -1) await fetchTotalTableData({ ...params.value })
    else await fetchTotalTableData()
}

const tableDataList = ref([])

const fetchTotalTableData = async (body = {}) => {
    const res = await request.post('/api/hr-arrival-records/total', body)
    tableDataList.value = [{ ...res, unitNumber: '合计' }] as any
}

fetchTotalTableData()
//NC查询会计科目及辅助
const codeList = ref([])
const getSelectList = async () => {
    try {
        const res: any = await request.get(`/api/nc/qryAccountAuxiliary`)
        codeList.value = res.map((el) => {
            return {
                ...el,
                label: el.accountname,
                value: el.accountcode,
            }
        })
    } catch (error) {
        console.log('error')
    }
}
// 因为数据大所以提前请求
getSelectList()
const modalCancel = () => {
    showCreate.value = false
    tableRef.value.refresh(1)
}
const columns = [
    {
        title: '发票内容',
        dataIndex: 'content',
        width: 200,
        customRender: ({ text }) => {
            return text
        },
    },
    {
        title: '类型',
        dataIndex: 'type',
        width: 110,
        customRender: ({ record, text }) => {
            if (record.type == 1) {
                return '发票'
            } else if (record.type == 2) {
                return '报销'
            } else if (record.type == 3) {
                return '派遣报销'
            } else if (record.type == 4) {
                return '外包报销'
            }
        },
    },
    {
        title: '方向',
        dataIndex: 'debt',
        width: 110,
        customRender: ({ record, text }) => {
            if (record.debt == 'D') {
                return '借'
            } else if (record.debt == 'C') {
                return '贷'
            }
        },
    },
    {
        title: '科目名称',
        dataIndex: 'accountname',
        customRender: ({ text }) => {
            return text
        },
    },
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 200,
        customRender: ({ text }) => {
            return text
        },
    },
    {
        title: '排序',
        dataIndex: 'sort',
        width: 110,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 150,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '发票内容',
        key: 'content',
    },
    {
        label: '科目编码',
        key: 'accountcode',
        type: 'select',
        options: codeList,
    },
]
</script>

<style scoped lang="less"></style>
