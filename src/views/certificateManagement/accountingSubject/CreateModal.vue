<template>
    <BasicEditModalSlot
        :title="viewType == 'add' ? '新增' : '编辑'"
        :visible="visible"
        @cancel="modalClose"
        width="800px"
        centered
    >
        <div class="viewView"></div>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" :rules="rules">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]">
                    <template #subjectCode>
                        <Select
                            :value="{ value: formData[itemForm.name] }"
                            :options="codeList"
                            placeholder="请选择科目编码"
                            showSearch
                            @change="(val, option) => selectChange(val, option)"
                            :filterOption="filterTreeNode"
                            allowClear
                            labelInValue
                        />
                        <!-- @labelChange="labelChange" -->
                    </template>
                    <template #client>
                        <ClientSelectTree v-model:value="formData[itemForm.name]" :itemForm="{ placeholder: '请选择客户名称' }" />
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <div class="viewView"></div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm()">确认</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { valuesAndRules } from '/#/component'
import { ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

const confirmLoading = ref(false)
const formInline = ref()

const props = defineProps({
    visible: Boolean,
    viewType: String,
    currentRecord: Object,
    codeList: Array,
})
const { visible, viewType, currentRecord, codeList } = toRefs(props)
const emit = defineEmits(['update:visible', 'confirm'])

watch(visible, () => {
    if (visible.value) {
        currentRecord?.value && updataform(currentRecord?.value)
    }
})

const debtOptions = [
    {
        label: '借',
        value: 'D',
    },
    {
        label: '贷',
        value: 'C',
    },
]
const typeOptions = [
    {
        label: '发票',
        value: 1,
    },
    {
        label: '报销',
        value: 2,
    },
    {
        label: '派遣报销',
        value: 3,
    },
    {
        label: '外包报销',
        value: 4,
    },
]

const myOptions = ref<valuesAndRules[]>([
    {
        label: '发票内容',
        name: 'content',
        disabled: false,
    },
    {
        label: '科目编码',
        name: 'accountcode',
        type: 'slots',
        slots: 'subjectCode',
        // ruleType: 'object',
    },
    {
        label: '关联客户',
        name: 'clientId',
        slots: 'client',
        type: 'slots',
        required: false,
    },
    {
        label: '排序',
        name: 'sort',
        type: 'number',
        required: true,
        ruleType: 'number',
        // onChange: (value) => {
        // },
    },
    {
        label: '分录借贷方标志',
        name: 'debt',
        type: 'change',
        required: true,
        options: debtOptions,
    },
    {
        label: '类型',
        name: 'type',
        type: 'change',
        required: true,
        ruleType: 'number',
        options: typeOptions,
    },
])

// FormData rules 初始值
const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

const formData = ref<Recordable>(initFormData)

//科目编码change
const selectChange = (val, option) => {
    formData.value.accountname = val.label
    formData.value.accountcode = val.value
    if (viewType?.value == 'edit') {
        formData.value.auxiliary = JSON.stringify(option.auxiliary)
        console.log(formData.value.auxiliary)
    } else {
        formData.value.auxiliary = option.auxiliary
    }
    formData.value.pkAccasoa = option.pk_accasoa
}

// 输入搜索函数
const filterTreeNode = (input, node) => {
    return node.label.includes(input)
}

// 编辑进入数据
const updataform = (record: any) => {
    formData.value = Object.assign({}, initFormData, record)
}

// reset formData
const resetFormData = () => {
    formData.value = initFormData
    formInline.value.resetFields()
}
const modalClose = () => {
    resetFormData()
    emit('update:visible', false)
}

const modalConfirm = async () => {
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const params = {
            content: formData.value.content,
            accountcode: formData.value.accountcode,
            pkAccasoa: formData.value.pkAccasoa,
            accountname: formData.value.accountname,
            auxiliary: viewType?.value == 'edit' ? formData.value.auxiliary : JSON.stringify(formData.value.auxiliary),
            clientId: formData.value.clientId,
            sort: formData.value.sort,
            debt: formData.value.debt,
            type: formData.value.type,
            id: viewType?.value == 'edit' ? currentRecord?.value?.id : undefined,
        }
        let res
        if (viewType?.value == 'add') {
            res = await request.post(`/api/nc-accounts`, params)
        } else {
            res = await request.put(`/api/nc-accounts`, params)
        }

        modalClose()
        emit('confirm')
    } finally {
        confirmLoading.value = false
    }
}
</script>

<style scoped lang="less">
.viewView {
    height: 20px;
}
</style>
