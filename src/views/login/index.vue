<template>
    <div class="main">
        <Card class="loginForm" title="欢迎登录">
            <Tabs default-active-key="0" @change="tabsChange" style="margin-bottom: 24px">
                <TabPane key="0" tab="账号密码登录">
                    <Form ref="loginFormRefAcc" :model="formDataAcc" :rules="formRulesAcc">
                        <FormItem name="account">
                            <Input
                                size="large"
                                v-model:value="formDataAcc.account"
                                placeholder="请输入账号"
                                @pressEnter="loginHandle('0')"
                            >
                                <template #prefix>
                                    <UserOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                            </Input>
                        </FormItem>
                        <FormItem name="password">
                            <Input
                                type="password"
                                size="large"
                                v-model:value="formDataAcc.password"
                                placeholder="请输入密码"
                                v-if="!ifDisplay"
                                @pressEnter="loginHandle('0')"
                            >
                                <template #prefix>
                                    <LockOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                                <template #suffix>
                                    <EyeInvisibleOutlined
                                        v-show="!ifDisplay"
                                        :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }"
                                        @click="ifDisplay = !ifDisplay"
                                    />
                                </template>
                            </Input>

                            <Input
                                type="text"
                                size="large"
                                v-model:value="formDataAcc.password"
                                placeholder="请输入密码"
                                @pressEnter="loginHandle('0')"
                                v-else
                            >
                                <template #prefix>
                                    <LockOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                                <template #suffix>
                                    <EyeOutlined
                                        v-show="ifDisplay"
                                        :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }"
                                        @click="ifDisplay = !ifDisplay"
                                    />
                                </template>
                            </Input>
                        </FormItem>
                        <FormItem name="accCode">
                            <Input
                                size="large"
                                v-model:value="formDataAcc.accCode"
                                placeholder="请输入验证码"
                                style="height: 40px; user-select: none"
                                @pressEnter="loginHandle('0')"
                            >
                                <template #prefix>
                                    <CheckCircleOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                                <template #suffix>
                                    <div class="ValidCode disabled-select">
                                        <span
                                            v-for="(item, index) in myCode"
                                            :key="index"
                                            flag="true"
                                            @click="getCode"
                                            :style="getStyle(item)"
                                            onselectstart="return false"
                                        >
                                            {{ item.code }}
                                        </span>
                                    </div>
                                </template>
                            </Input>
                        </FormItem>
                        <!-- <Checkbox v-model:checked="formDataAcc.checked">记住密码</Checkbox> -->
                        <Button class="loginBtn" type="primary" size="large" @click="loginHandle('0')" :loading="loading">
                            登录
                        </Button>
                    </Form>
                </TabPane>

                <TabPane key="1" tab="手机号登录">
                    <Form ref="loginFormRefPhone" :model="formDataPhone" :rules="formRulesPhone">
                        <FormItem name="phone">
                            <Input
                                size="large"
                                v-model:value="formDataPhone.phone"
                                placeholder="请输入手机号"
                                @pressEnter="loginHandle('1')"
                            >
                                <template #prefix>
                                    <MobileOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                            </Input>
                        </FormItem>
                        <FormItem name="code">
                            <Input
                                size="large"
                                v-model:value="formDataPhone.code"
                                placeholder="请输入手机号验证码"
                                @pressEnter="loginHandle('1')"
                            >
                                <template #addonAfter>
                                    <Button class="codeBtn" id="send_phone" @click="getCodePhone" :disabled="flag">
                                        获取验证码
                                    </Button>
                                </template>
                                <template #prefix>
                                    <SafetyOutlined :style="{ fontSize: '25px', color: '#BEBEBE', paddingRight: '10px' }" />
                                </template>
                            </Input>
                        </FormItem>
                        <Button class="loginBtn" type="primary" size="large" @click="loginHandle('1')" :loading="loading">
                            登录
                        </Button>
                    </Form>
                </TabPane>
            </Tabs>
        </Card>
    </div>
</template>
<script lang="ts">
import { defineComponent, reactive, ref, onMounted } from 'vue'
import { Card, message, notification } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import config from '/@/config'
import useUserStore from '/@/store/modules/user'
import {
    UserOutlined,
    LockOutlined,
    CheckCircleOutlined,
    MobileOutlined,
    SafetyOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
} from '@ant-design/icons-vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import request from '/@/utils/request'
import { validatePhone } from '/@/utils/format'
export default defineComponent({
    components: {
        Card,
        UserOutlined,
        LockOutlined,
        CheckCircleOutlined,
        MobileOutlined,
        SafetyOutlined,
        EyeInvisibleOutlined,
        EyeOutlined,
    },
    setup() {
        onMounted(() => {
            getCode()
        })

        const route = useRoute()
        const router = useRouter()

        //账号密码登录
        const loginFormRefAcc = ref()

        const loginFormRefPhone = ref()

        const loginType = ref(0)

        //账号密码登录
        const formDataAcc = reactive({
            account: '',
            password: '',
            accCode: '',
            checked: false,
            loginType: 0,
        })

        // 手机号登录
        const formDataPhone = reactive({
            phone: '',
            code: '',
            loginType: 1,
        })
        const loading = ref(false)
        // 是否使用短信验证码登录
        const useCodeWay = ref(false)

        // 确认验证码校验
        const validatePassCode = (rule: RuleObject, value) => {
            let codeStr = myCode.value
                .map((obj) => {
                    return obj.code
                })
                .join('')
            console.log(codeStr)
            if (!value) {
                return Promise.reject('验证码不能为空')
            }
            if (value.toUpperCase() !== codeStr.toUpperCase()) {
                return Promise.reject('请输入正确的验证码')
            } else {
                return Promise.resolve()
            }
        }
        // 账号密码登录校验
        const formRulesAcc = reactive({
            account: [{ required: true, message: '账号不能为空', trigger: ['change', 'blur'] }],
            password: [{ required: true, message: '密码不能为空', trigger: ['change', 'blur'] }],
            accCode: [{ required: true, trigger: ['change', 'blur'], validator: validatePassCode }],
        })
        // 手机号登录校验
        const formRulesPhone = reactive({
            phone: [{ required: true, trigger: ['change', 'blur'], validator: validatePhone }],
            code: [{ required: true, trigger: ['change', 'blur'], message: '手机号验证码不能为空' }],
        })

        const tabsChange = (val) => {
            loginType.value = Number(val)
        }

        const loginHandle = async (type) => {
            if (type == '0') {
                //账号密码登录
                loginFormRefAcc.value
                    .validate()
                    .then(async () => {
                        loading.value = true
                        formDataAcc.loginType = loginType.value
                        await useUserStore().loginAction(formDataAcc as any)
                        await useUserStore().getUserInfoAction()
                    })
                    .then(async () => {
                        const path = (route.query.redirect || config.homePath) as string
                        router.push({ path })

                        const toDoList = await request.get(`/api/hr-upcomings/warn`, {}, { loading: false })
                        toDoList.length &&
                            notification.info({
                                message: '待办提醒',
                                description: '您今天有' + toDoList.length + '条待办！',
                                onClick: () => {
                                    router.push({
                                        name: 'toDoList',
                                    })
                                    notification.destroy()
                                },
                            })
                    })
                    .finally(() => {
                        loading.value = false
                    })
            } else {
                //手机号登录
                loginFormRefPhone.value
                    .validate()
                    .then(async () => {
                        loading.value = true
                        formDataPhone.loginType = loginType.value
                        await useUserStore().loginAction(formDataPhone as any)
                        await useUserStore().getUserInfoAction()
                    })
                    .then(async () => {
                        const path = (route.query.redirect || config.homePath) as string
                        router.push({ path })
                    })
                    .finally(() => {
                        loading.value = false
                    })
            }
        }

        // 账号密码登录获取验证码
        const myCode = ref(null) as any
        const getCode = () => {
            myCode.value = []
            const len = 4
            const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz0123456789'
            const charsLen = chars.length

            // 生成
            for (let i = 0; i < len; i++) {
                const rgb = [Math.round(Math.random() * 220), Math.round(Math.random() * 240), Math.round(Math.random() * 200)]
                myCode.value.push({
                    code: chars.charAt(Math.floor(Math.random() * charsLen)),
                    color: `rgb(${rgb})`,
                    fontSize: `2${[Math.floor(Math.random() * 10)]}px`,
                    // padding: `${[Math.floor(Math.random() * 10)]}px`,
                    padding: `3px`,
                    transform: `rotate(${Math.floor(Math.random() * 90) - Math.floor(Math.random() * 90)}deg)`,
                })
            }
        }

        const flag = ref(false)
        // 手机号登录获取验证码
        const getCodePhone = async () => {
            let phone = formDataPhone.phone
            if (!phone) {
                return message.error('手机号不能为空!')
            }
            await request.post('/api/verification/code', { phone })

            let time, timer
            let btn: any = document.querySelector('#send_phone')
            time = 60
            clearInterval(timer) //清除计时器
            btn.innerText = time + 's'
            timer = setInterval(function () {
                time--
                btn.innerText = time + 's'
                flag.value = true
                if (time <= 0) {
                    //重置获取验证码按钮状态，变为可点击，即可再次获取
                    clearInterval(timer) //清除计时器
                    btn.innerText = '获取验证码'
                    flag.value = false
                }
            }, 1000)
        }

        const getStyle = (data) => {
            return `color: ${data.color}; font-size: ${data.fontSize}; padding: ${data.padding}; transform: ${data.transform}`
        }
        const ifDisplay = ref(false)

        return {
            useCodeWay,
            config,
            loading,
            loginFormRefAcc,
            loginFormRefPhone,
            formDataAcc,
            formDataPhone,
            formRulesAcc,
            formRulesPhone,
            loginHandle,
            tabsChange,
            getCode,
            getCodePhone,
            myCode,
            loginType,
            flag,
            getStyle,
            ifDisplay,
        }
    },
})
</script>
<style lang="less" scoped>
.main {
    width: 100vw;
    height: 100vh;
    background: url('./img.png');
    background-size: cover;
    background-repeat: no-repeat;
    .loginForm {
        position: fixed;
        top: 50%;
        transform: translateY(-50%);
        right: 10vw;
        width: 391px;
        .tip {
            display: flex;
            justify-content: space-between;
            & .item {
                font-size: 13px;
                margin-bottom: 10px;
                color: @primary-color;
                cursor: pointer;
            }
        }
        .getcode {
            width: 137px;
            height: 30px;
            line-height: 30px;
            background-color: rgba(221, 221, 221, 100);
            text-align: center;
        }
        .loginBtn {
            width: 100%;
            margin-top: 25px;
        }
        .codeBtn {
            font-size: 16px;
            color: #1890ff;
            border: 0;
        }
    }
}
.notclick {
    pointer-events: none;
}
:deep(.ant-card-head) {
    border-bottom: 0px solid #f0f0f0;
    font-size: 24px;
    text-align: center;
}
:deep(.ant-card-body) {
    padding: 0 24px 24px 24px;
}
:deep(.ant-tabs-bar) {
    text-align: center;
    font-size: 16px;
}
:deep(.ant-tabs-nav-container) {
    font-size: 16px;
}
:deep(.ant-input-affix-wrapper) {
    border: none;
    border-bottom: 1px solid #ddd;
    box-shadow: none;
}
:deep(.ant-input-group-addon) {
    border: none;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
}
.ValidCode {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span {
        display: inline-block;
    }
}
</style>
