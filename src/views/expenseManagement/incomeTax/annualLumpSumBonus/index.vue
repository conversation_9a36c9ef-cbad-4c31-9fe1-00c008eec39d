<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'annualLumpSumBonus_export'" type="primary" @click="exportData">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bills/annual-lump-sum-bonus"
        exportUrl="/api/hr-bills/annual-lump-sum-bonus/export-check"
        :columns="columns"
        :params="params"
        @selectedRowsArr="selHandle"
    />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
const params = ref({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
const exportData = async () => {
    await tableRef.value.exportRow('ids', exportText.value, params.value)
    tableRef.value.refresh()
}

const exportStateList = [
    {
        label: '已导出',
        value: 1,
    },
    {
        label: '未导出',
        value: 0,
    },
]
const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 120,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 90,
    },
    {
        title: '人数',
        dataIndex: 'staffNum',
        width: 170,
        ellipsis: true,
    },
    {
        title: '奖金合计',
        dataIndex: 'bonusTotal',
        width: 170,
        ellipsis: true,
    },

    {
        title: '导出状态',
        dataIndex: 'isBonusExportStr',
        width: 170,
        ellipsis: true,
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '费用年月',
        key: 'paymentDate',
        type: 'month',
    },

    {
        label: '导出状态',
        key: 'isBonusExport',
        type: 'select',
        options: exportStateList,
    },
]
</script>

<style scoped lang="less"></style>
