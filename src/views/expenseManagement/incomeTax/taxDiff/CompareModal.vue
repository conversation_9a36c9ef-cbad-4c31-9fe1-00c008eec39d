<template>
    <BasicEditModalSlot :visible="visible" title="对账" @cancel="modalClose" width="1200px">
        <div class="cell">
            <div class="title">表单数据</div>
            <div class="main">
                <div class="row">
                    <div class="col">
                        <div class="name">类型</div>
                        <Select v-model:value="type" :options="exportTypeList" placeholder="选择类型" style="width: 200px" />
                    </div>
                    <div class="col">
                        <div class="name">表单</div>
                        <div class="val">
                            <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                                <Button type="primary" size="small">{{ resTable?.length ? '重新上传' : '上传文件' }}</Button>
                            </Upload>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">数据开始行</div>
                        <InputNumber
                            v-model:value.number="startRow"
                            placeholder="数据开始行"
                            style="width: 200px"
                            @blur="changeStartRow"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="cell">
            <div class="title">原始表单</div>
            <div class="main">
                <BasicTable :tableDataList="tableData" :columns="tableColumns" :rowSelectionShow="false" :sorter="false" />
            </div>
        </div>
        <div class="cell">
            <div class="title">表单映射</div>
            <div class="main">
                <div class="table">
                    <div class="headerCols">
                        <div class="th" v-for="i in resColumns" :key="i.title" :style="{ width: i.cols * 100 + 'px' }">
                            {{ i.title }}
                        </div>
                    </div>
                    <div class="bodyCols">
                        <div class="tr" v-for="(j, idx) in resTable" :key="idx">
                            <div class="tb" v-for="(v, index) in j" :key="v">
                                <a v-if="idx == 1 && v.value != '表单字段'" @click="removeField(v, index)">{{ v.value }}</a>
                                <span v-else>{{ v.value }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <Button
                v-if="modalType != '个税'"
                type="primary"
                :disabled="!billConfigId"
                :loading="confirmLoading"
                @click="startCompare"
                >开始对账</Button
            >
            <Button
                v-if="modalType == '个税'"
                type="primary"
                :disabled="!billConfigIds"
                :loading="confirmLoading"
                @click="startCompare"
                >开始对账</Button
            >
        </template>
    </BasicEditModalSlot>

    <!-- 设置费用项 -->
    <BasicEditModalSlot
        :visible="showSetExpense"
        title="设置费用项"
        @cancel="showSetExpense = false"
        @ok="setExpenseConfirm"
        width="400px"
    >
        <Form :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem label="名称">
                <Input v-model:value="setForm.expenseName" placeholder="名称" />
            </FormItem>
            <FormItem label="类型">
                <Select
                    v-model:value="setForm.expenseType"
                    placeholder="类型"
                    :options="expenseTypeList"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot>

    <!-- 提示 -->
    <BasicEditModalSlot :visible="showTip" title="提示" cancalText="" @cancel="tipNext" width="600px">
        <div style="margin-bottom: 20px">
            <Button type="primary" @click="tipExportBtn">导出</Button>
        </div>
        <div class="tipBox">
            <div>本次对账结果中：</div>
            <div class="tip1">导盘中不存在员工数量 {{ guideDiscArr.length }}人；</div>
            <div class="tip1">系统中不存在员工数量 {{ systemArr.length }}人；</div>
            <div class="tip1">当月不存在薪酬账单的员工数量 {{ nonentity.length }}人；</div>
        </div>

        <template #footer>
            <Button type="primary" @click="tipNext">继续</Button>
        </template>
    </BasicEditModalSlot>

    <!-- 对账结果 -->
    <BasicEditModalSlot :visible="showResult" title="对账结果" @cancel="resultClose" width="1200px">
        <div class="search">
            <Input v-model:value="keyWords" @change="searchChange()" placeholder="姓名搜索" />
        </div>
        <div class="header">
            <Button type="primary" style="margin-bottom: 10px; margin-right: 20px" @click="exportData">导出</Button>
            <Button type="primary" style="margin-bottom: 10px" @click="lockFun">
                <!-- {{ isLock ? '锁定' : '解锁' }} -->
                <LockOutlined v-if="isLock" />
                <UnlockOutlined v-if="!isLock" />
            </Button>
            <!-- <div>* 深色背景行表示业务数据，浅色背景行表示excel数据，红色字体意为数据不一致</div> -->
        </div>

        <Table
            :bordered="true"
            :columns="columnChildrenTitleList"
            :rowKey="(record, index) => record.id || index"
            size="small"
            :scroll="{ x: 100 }"
            :rowSelection="false"
            :dataSource="resultData"
        />

        <template #footer>
            <Button :loading="loading" @click="resultClose">取消</Button>
            <Button type="primary" :loading="loading" @click="saveResult">仅保存结果</Button>
            <Button :loading="loading" type="primary" v-if="isLock" @click="saveCompensation">保存补差</Button>
        </template>
    </BasicEditModalSlot>

    <!-- 是否保存补差 -->
    <BasicEditModalSlot :visible="saveComShow" title="是否保存补差" cancalText="" @cancel="saveComShow = false" width="600px">
        <div>是否当月使用补差</div>
        <template #footer>
            <Button @click="noSave">否</Button>
            <Button type="primary" @click="yesSave">是</Button>
        </template>
    </BasicEditModalSlot>

    <!-- 不可当月使用补差的客户信息 -->
    <BasicEditModalSlot
        :visible="noUseShow"
        title="不可当月使用补差的客户信息"
        cancalText=""
        @cancel="noUseSaveAndConfirm"
        width="1200px"
    >
        <div class="header">
            <Button type="primary" style="margin-bottom: 10px" @click="noUseExport">导出</Button>
        </div>
        <BasicTable
            ref="tableRefCustomer"
            :useIndex="true"
            :tableDataList="tableDataList"
            :rowSelectionShow="false"
            :columns="compensationColumns"
        />
        <template #footer>
            <Button @click="noUseSaveAndConfirm" type="primary" class="btn"> 确定 </Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { message, Modal, Upload } from 'ant-design-vue'
import { ref, toRefs, watch } from 'vue'
import { formatMapData, formatOriginForm } from './util'
import { formatDynamicColns } from '/@/utils/dynamicHeader'

import downFile from '/@/utils/downFile'
import request from '/@/utils/request'
import { UnlockOutlined, LockOutlined } from '@ant-design/icons-vue'

// import type { TableColumnsType } from 'ant-design-vue';
const body = document.body
const expenseTypeList = ref<LabelValueOptions>([])
const getExpenseTypeList = async () => {
    const res = await request.get(`/api/hr-bill-compare-configs/expense-type-item/${type.value}`)
    expenseTypeList.value = res.map((i: { value: any; key: any }) => ({
        label: i.value,
        value: i.key,
    }))
}

const props = defineProps({
    visible: Boolean,
    billId: {
        type: String,
        default: '',
    },
    exportTypeList: Array,
    batchOpt: {
        type: Object,
        default: () => {},
    },
    modalType: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update:visible', 'cancel'])

const { billId, batchOpt, visible, modalType, exportTypeList } = toRefs(props)

type Name = {
    name: string
}

type User = Name & {
    age: number
}

const resetData = () => {
    tableColumns.value = []
    tableData.value = []
    resTable.value = []
    resColumns.value = []
    expenseTypeList.value = []
    startRow.value = undefined
    type.value = 3
    billConfigId.value = undefined
}
watch(visible, () => {
    if (visible) {
    }
})
const modalClose = () => {
    emit('update:visible', false)
    resetData()
}

const type = ref(3)
const startRow = ref()

const changeStartRow = async () => {
    if (!startRow.value && startRow.value !== 0) {
        return
    }
    await request.put(`/api/hr-bill-compare-configs`, {
        id: billConfigId.value,
        dataStartRow: startRow.value - 1,
    })
}

const billConfigId = ref()
const billConfigIds = ref<any>()
const tableData = ref<Recordable[]>([])
const tableColumns = ref<Recordable[]>([])

const resTable = ref<Recordable[]>([])
const resColumns = ref<Recordable[]>([])

const beforeUpload = async (file: string | Blob) => {
    if (!type.value && type.value != 0) {
        message.warning('请选择导盘类型！')
        return
    }
    let api = `/api/hr-bill-dynamic-fieldses/importGuideFile/${billId.value}/${type.value}`
    if (batchOpt.value?.paymentDate)
        api = `api/hr-bill-compare-configs/batch-reconciliation/${batchOpt.value.paymentDate}/${type.value}`
    try {
        const formData = new FormData()
        formData.append('file', file)
        const res = await request.post(api, formData)
        console.log(res)
        startRow.value = res.dataStartRow + 1
        billConfigId.value = res.billConfigId
        billConfigIds.value = res.billConfigIds
        const { columns: originColumns, data: originData } = formatOriginForm(res.cellItemList, setExpense)
        tableColumns.value = originColumns
        tableData.value = [originData]
        // 获取费用项
        await getExpenseTypeList()
        const { columns, data } = formatMapData(res.tableMappingList)
        resTable.value = data
        resColumns.value = columns
    } catch (error) {}

    return false
}

const setForm = ref({
    expenseName: undefined,
    expenseType: undefined,
    sortValue: undefined,
})
const showSetExpense = ref(false)
const currentColKey = ref(undefined)
const setExpense = (name: any, colKey: undefined, order: any) => {
    currentColKey.value = colKey
    setForm.value = {
        expenseName: name,
        expenseType: undefined,
        sortValue: order,
    }
    showSetExpense.value = true
}
const setExpenseConfirm = async () => {
    if (!setForm.value.expenseName || !setForm.value.expenseType) {
        message.warn('请输入完整费用项!')
        return
    }
    const setBillConfigIds = billConfigId.value ? billConfigId.value : billConfigIds.value[0]
    const res = await request.post(
        `/api/hr-bill-dynamic-fieldses/guidePlate/setFieldMappingRelation/${setBillConfigIds}`,
        setForm.value,
    )
    showSetExpense.value = false
    tableData.value[0][currentColKey.value || '-'] = setForm.value.expenseName // 改变原单数据 状态为已映射
    const { columns, data } = formatMapData(res)
    resColumns.value = columns
    resTable.value = data
}

const removeField = (i: { value: string; order: any }, idx: string | number) => {
    Modal.confirm({
        title: '确认',
        content: '是否删除该字段映射?',
        onOk: async () => {
            const colKey = resTable.value[0][idx].value // 该列的key
            const removeBillConfigIds = billConfigId.value ? billConfigId.value : billConfigIds.value[0]
            const res = await request.post(`/api/hr-bill-dynamic-fieldses/delMappingRelationWithCompare/${removeBillConfigIds}`, {
                expenseName: i.value,
                sortValue: i.order,
            })
            const { columns, data } = formatMapData(res)
            resColumns.value = columns
            resTable.value = data
            tableData.value[0][colKey] = i.value + '&' // 改变原单数据 状态为未映射
        },
    })
}

const showTip = ref(false)

const confirmLoading = ref(false)
let expostId: any //导出Id
let columnChildrenTitle: any
let columnChildrenTitleList = ref<Recordable[]>([])
const bonusColumns = ref<Recordable[]>([])
let searchList: any = []
const startCompare = async () => {
    confirmLoading.value = true
    try {
        const res = await request.post(`/api/hr-bill-compare-configs/generateBillCompareResult`, {
            configId: billConfigId.value,
            billConfigIds: billConfigIds?.value,
            paymentDate: batchOpt.value?.paymentDate,
            identification: batchOpt.value?.paymentDate ? true : false,
            type: type.value,
        })

        billConfigIds.value = res.billConfigIds
        expostId = res.id
        // recursionChild(res.hrBillCompareResultDetailDTOList)
        //遍历表头数据，动态加载
        formatDynamicColns(res, columnChildrenTitleList)
        searchList = res.hrBillCompareResultDetailDTOList
        resultData.value = searchList

        if (res.abnormalDetailMapList) {
            showTip.value = true
            tipObj.value = res.abnormalDetailMapList
            guideDiscArr.value = tipObj.value['2'] || []
            systemArr.value = tipObj.value['3'] || []
            nonentity.value = tipObj.value['4'] || []
        } else {
            showResult.value = true
        }
        emit('update:visible', false)
    } finally {
        confirmLoading.value = false
    }
}

// 搜索
const keyWords = ref('')
const searchChange = () => {
    if (keyWords.value == '') {
        resultData.value = searchList
    }
    resultData.value = searchList.filter((el) => {
        if (el.staffName.includes(keyWords.value)) {
            return el
        }
    })
}

const showResult = ref(false)
// const resultData = ref<SBTableDataType[]>([])

const resultColumns = ref<Recordable[]>([])
const resultData = ref<Recordable[]>([])

const loading = ref(false)
const resultConfirm = async (val) => {
    try {
        loading.value = true
        // const randomConfigIds = billConfigIds.value[Math.floor(Math.random() * billConfigIds.value.length)]
        if (modalType.value == '个税') {
            const res = await request.post(`/api/hr-bill-compare-configs/saveBatchCompareMakeUp`, {
                billConfigIds: billConfigIds.value,
                currentMonthUsed: val,
            })
            if (val == 1) {
                tableDataList.value = res
            }
            if (res.status == 500) {
                message.warn(res.msg)
            }
        } else {
            const res = await request.post(
                `/api/hr-bill-compare-configs/saveCompareMakeUp/${
                    batchOpt.value?.paymentDate && modalType.value == '个税' ? billConfigIds.value[0] : billConfigId.value
                }/${val}`,
                {},
            )
            if (res.status == 500) {
                message.warn(res.msg)
            }
        }

        // showResult.value = false
        // columnChildrenTitleList.value = []
        // resultColumns.value = []
        // modalClose()
    } finally {
        loading.value = false
    }
}
const saveResult = async () => {
    loading.value = true
    await request.post(`/api/hr-bill-compare-configs/save-only`, {
        billConfigIds: [...billConfigIds.value],
        lockStatus: isLock.value ? 1 : 0, //0未锁定 1已锁定
    })
    loading.value = false
    showResult.value = false
    columnChildrenTitleList.value = []
    resultColumns.value = []
    emit('cancel')
    modalClose()
}
const resultClose = async () => {
    try {
        showResult.value = false
        loading.value = true
        if (modalType.value == '个税') {
            await request.post(`/api/hr-bill-compare-configs/deletes`, billConfigIds.value)
        } else {
            await request.delete(`/api/hr-bill-compare-configs/${billConfigId.value}`)
        }

        columnChildrenTitleList.value = []
        resultColumns.value = []
    } finally {
        loading.value = false
        batchOpt.value?.paymentDate && modalClose()
        saveResult()
    }
}

const exportData = () => {
    downFile('post', `/api/hr-bill-compare-results/export`, '对账结果.xlsx', { billCompareConfigIds: [expostId] })
}

/**
 *
 * @开始对账提示和是否保存补差
 */
//开始对账提示&&&是否保存补差
const guideDiscArr = ref([])
const systemArr = ref([])
const nonentity = ref([])
const tipObj = ref({})
const isLock = ref(false)
const lockFun = () => {
    isLock.value = !isLock.value
}

const tipNext = () => {
    showTip.value = false
    showResult.value = true
}

const tipExportBtn = () => {
    let arr = [...guideDiscArr.value, ...systemArr.value, ...nonentity.value]
    downFile('post', `/api/hr-bill-compare-configs/exportBillCompareResult`, '不可对账账户信息.xlsx', {
        type: type.value,
        hrBillCompareResultDetailDTOList: arr || [],
    })
}

const saveComShow = ref(false)
const noUseShow = ref(false)
const saveCompensation = () => {
    saveComShow.value = true
}
const yesSave = async () => {
    saveComShow.value = false
    await resultConfirm(1)
    showResult.value = false
    noUseShow.value = true
}
const noSave = async () => {
    await resultConfirm(0)
    showResult.value = false
    columnChildrenTitleList.value = []
    resultColumns.value = []
    saveComShow.value = false
    modalClose()
}
// ***  不可当月使用补差的客户信息 关闭和确认
const tableDataList = ref([])
const noUseSaveAndConfirm = () => {
    noUseShow.value = false
    showResult.value = false
    columnChildrenTitleList.value = []
    resultColumns.value = []
    saveComShow.value = false
    emit('cancel')
    modalClose()
}

//表格数据
const compensationColumns = ref([
    {
        title: '客户名称',
        dataIndex: 'clientName',
        align: 'center',
        sorter: false,
    },
    {
        title: '客户编号',
        dataIndex: 'unitNumber',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '账单类型',
        dataIndex: 'billTypeStr',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '锁定状态',
        dataIndex: 'billStateStr',
        align: 'center',
        width: 150,
        sorter: false,
    },
])
const noUseExport = () => {
    if (tableDataList.value?.length == 0) {
        message.warning({ content: '没有数据' })
        return
    }
    downFile('post', `api/hr-bill-compare-result-detail/export-excludes-client`, '不可对账账户信息.xlsx', {
        ids: tableDataList.value?.map((el: any) => el.id),
    })
}
</script>

<style lang="less">
.redClass {
    color: @dangerous-color;
}
</style>
<style scoped lang="less">
.row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
}
.col {
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .name {
        width: 150px;
        text-align: right;
        &::after {
            content: '：';
        }
    }
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon {
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }
    }
    .main {
        padding: 10px 0;
    }
}

.header {
    display: flex;
    justify-content: start;
    align-items: center;
}
.search {
    width: 250px;
    margin: 10px 0;
}
.table {
    width: 100%;
    overflow-x: auto;
    .headerCols {
        display: flex;
        justify-content: flex-start;
        align-items: stretch;
        flex-wrap: nowrap;
        .th {
            flex-shrink: 0;
            display: inline-block;
            background: @primary-color;
            color: white;
            padding: 10px;
            text-align: center;
            border: 1px solid transparent;
            border-right-color: white;
        }
    }
    .bodyCols {
        .tr {
            display: flex;
            justify-content: flex-start;
            align-items: stretch;
            flex-wrap: nowrap;
            .tb {
                flex-shrink: 0;
                width: 100px;
                display: inline-block;
                padding: 10px;
                text-align: center;
                border: 1px solid #eee;
            }
        }
    }
}

.tipBox {
    .tip1 {
        margin-top: 6px;
        text-indent: 2em;
    }
}
// .ant-table-fixed {
//     .ant-table-thead>tr>th {
//         background-color: white;
//         color: black;
//     }
// }
</style>
