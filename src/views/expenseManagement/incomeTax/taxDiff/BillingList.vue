<template>
    <BasicEditModalSlot
        :visible="visible"
        @cancel="cancel"
        title="批量对账"
        :zIndex="1009"
        :width="'1050px'"
        @confirm="insertTopic"
    >
        <Table
            class="basicTable"
            style="width: 100%"
            ref="tableRef"
            :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            size="small"
            bordered
            :indentSize="30"
            :scroll="{ x: '100' }"
            :columns="columns"
            :data-source="tableData"
            :rowKey="(record) => record.id"
            :pagination="pagination"
            :loading="loading"
            @change="tableChange"
        />
        <template #footer>
            <Button @click="insertTopic" type="primary" class="btn">下一步</Button>
        </template>
    </BasicEditModalSlot>
    <CompareModal v-model:visible="showCompare" :exportTypeList="currentExportTypeList" />
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
import { ref, defineComponent, computed, toRefs, watch, h } from 'vue'
import { message } from 'ant-design-vue'
import CompareModal from '../../salarySettlement/billAccounting/CompareModal.vue'
import request from '/@/utils/request'
import { exportTypeList } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'PresetExamAdd',
    components: { CompareModal },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        currentRecord: {
            type: String,
            default: '',
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, currentRecord } = toRefs(props)
        console.log(visible.value)
        //表格数据
        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                align: 'center',
                customRender: (record) => {
                    return h('span', record.index + 1)
                },
                width: 80,
            },
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 150,
                sorter: false,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                sorter: false,
                width: 250,
            },
            {
                title: '当前协议编号',
                dataIndex: 'agreementNumber',
                align: 'center',
                width: 250,
                sorter: false,
            },
            {
                title: '协议类型',
                dataIndex: 'agreementTypekey',
                align: 'center',
                sorter: false,
                width: 120,
            },
            {
                title: '员工数量',
                dataIndex: 'peoplesum',
                align: 'center',
                width: 90,
                sorter: false,
            },
        ]

        const sortParams = ref({})
        //选择行
        const selectedRowsArr = ref<any[]>([])
        const selectedRowKeysArr = ref<any[]>([])
        const tableRef = ref()
        const pagination = ref<any>({})
        const tableData = ref<any[]>([])
        const showCompare = ref(false)
        const currentExportTypeList = ref<LabelValueOptions>([])
        let tableDataFormat = (data) => {
            data.map((item) => {
                item.disabled = !!topicList.value.find((el) => {
                    return el.id == item.id
                })
                return item
            })

            return data
        }
        watch(visible, (val) => {
            if (val) {
                sortParams.value = {
                    field: undefined,
                    order: undefined,
                }
                pagination.value = {
                    current: 1,
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条`,
                    pageSizeOptions: ['5', '10', '20', '30', '50', '100', '200', '300', '500'],
                    total: 0,
                }
                getTableData()
            }
        })

        const loading = ref(false)

        const tableChange = ({ current, pageSize }, _filters, sorter = { field: undefined, order: undefined }) => {
            pagination.value.current =
                sorter.field === sortParams.value.field && sorter.order === sortParams.value.order ? current : 1
            pagination.value.pageSize = pageSize

            sortParams.value =
                sorter.field && sorter.order
                    ? {
                          field: sorter.field,
                          order: sorter.order,
                      }
                    : {
                          field: undefined,
                          order: undefined,
                      }

            getTableData()
        }
        const getTableData = async () => {
            // loading.value = true
            try {
                const res = await request.get(
                    `/api/hr-bill-compare-configs/existence-security-bill?paymentDate=${currentRecord.value}&billType=0`,
                )
                console.log(res)
                tableData.value = res
            } catch (err) {
                console.log(err)
            }
        }
        const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedRowKeysArr.value = selectedRowKeys
            selectedRowsArr.value = selectedRows
        }
        const selectionRowConfig = {
            selectedRowKeys: selectedRowKeysArr,
            onChange: onSelectChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }

        const cancel = () => {
            emit('cancel')
        }
        const insertTopic = () => {
            showCompare.value = true
            emit('confirm')
            currentExportTypeList.value = exportTypeList.filter((i) => i.value != 1 && i.value != 4)
        }

        return {
            loading,
            columns,
            tableRef,
            tableData,
            selectedRowsArr,
            selectedRowKeysArr,
            pagination,
            selectionRowConfig,
            tableDataFormat,
            cancel,
            insertTopic,
            tableChange,
            showCompare,
            currentExportTypeList,
        }
    },
})
</script>
<style scoped lang="less">
/* 表格斑马纹 */
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
</style>
