<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportExcel">{{ exportText }}</Button>
        <!-- <Button type="primary" @click="individualTaxCheck">批量个税核对</Button> -->
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-welfare-compensations/salary-tax/page"
        :params="params"
        :columns="columns"
        :exportUrl="exportUrl"
        :sortered="changeSortered"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #operation="{ record }">
            <Button size="small" type="primary" @click="showRow(record)">查看</Button>
        </template>
    </BasicTable>

    <DetailModal :visible="showDetail" :diffId="currentId" @cancel="showDetail = false" />

    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
    <BatchCompareModal
        v-if="taxCheckVisible"
        :viewType="batchType"
        :visible="taxCheckVisible"
        @cancel="cancel"
        @next="batchNext"
    />

    <CompareModal
        v-model:visible="showCompare"
        :exportTypeList="currentExportTypeList"
        :batchOpt="batchOpt"
        :billConfigIds="currentIds"
        :modalType="modalType"
        @cancel="compareClose"
    />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import BatchCompareModal from './BatchCompareModal.vue'
import DetailModal from './DetailModal.vue'
import { getDynamicText } from '/@/utils'
import { message } from 'ant-design-vue'
import { exportTypeList, lockStatusList } from '/@/utils/dictionaries'
import CompareModal from './CompareModal.vue'
export default defineComponent({
    name: 'TaxDiff',
    components: { DetailModal, BatchCompareModal, CompareModal },
    setup() {
        const tableRef = ref()
        const params = ref({})
        let applyOptions = [
            { label: '未使用', value: 0 },
            { label: '已使用', value: 1 },
        ]

        const searchData = () => {
            tableRef.value.refresh(1)
        }
        const batchType = ref('start')
        const importSome = () => {
            importVisible.value = true
        }

        const selectedRowsArr = ref<any[]>([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportExcel = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        const showDetail = ref(false)
        const currentId = ref('')
        const modalType = ref('个税')
        const currentIds = ref<any[]>([])
        const getListId = () => {
            console.log(selectedRowsArr.value)
            currentIds.value = selectedRowsArr.value.map((el) => {
                return el.id
            })
        }
        console.log(currentIds.value)
        const showRow = (record) => {
            currentId.value = record.id
            showDetail.value = true
        }
        const showCompare = ref(false)
        const formInline = ref<any>(null)
        const formData = ref<any>({ date: '' })
        const currentRecord = ref<Recordable | undefined>(undefined)
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = ''
        const importUrl = ''
        const exportUrl = '/api/hr-welfare-compensations/salary-tax/export '

        // 批量个税核对
        const taxCheckVisible = ref(false)
        const taxCheckList = ref(false)
        const batchOpt = ref(null)
        const individualTaxCheck = () => {
            getListId()
            taxCheckVisible.value = true
        }
        const cancel = () => {
            taxCheckVisible.value = false
            batchType.value = 'start'
        }

        const confirm = (date, ids: string[]) => {
            selectedRowsArr.value = ids
            console.log(formData.value.date)
            console.log(ids)
            taxCheckVisible.value = false
            taxCheckList.value = true
            tableRef.value.refresh(1)
        }

        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
            },
            {
                title: '姓名',
                dataIndex: 'staffName',
            },
            {
                title: '身份证号',
                width: 200,
                dataIndex: 'certificateNum',
            },
            {
                title: '缴费年月',
                dataIndex: 'paymentDate',
            },
            {
                title: '社保补差',
                dataIndex: 'socialSecurityTotal',
            },
            {
                title: '公积金补差',
                dataIndex: 'accumulationFundTotal',
            },
            {
                title: '个税补差',
                dataIndex: 'personalTax',
            },
            {
                title: '状态',
                dataIndex: 'isUsedStr',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 110,
                slots: { customRender: 'operation' },
            },
        ])
        const changeSortered = (item) => {
            if (item) {
                if (item?.field == 'staffName') {
                    item.field = 'name'
                } else if (item?.field == 'isUsedStr') {
                    item.field = 'is_used'
                }
            }
            console.log(item)
        }
        const currentExportTypeList = ref<LabelValueOptions>([])
        const batchNext = (opt) => {
            if (opt.type == 'start') {
                batchType.value = 'next'
            } else {
                showCompare.value = true
                currentExportTypeList.value = exportTypeList.filter((i) => i.value == 3)
                batchOpt.value = opt
                cancel()
            }
            console.log(opt)
        }
        const tableListCancel = () => {
            taxCheckList.value = false
            tableRef.value.refresh()
        }
        const tableListConfirm = () => {
            taxCheckList.value = false
            tableRef.value.refresh(1)
        }
        const compareClose = () => {
            tableRef.value.refresh(1)
        }
        return {
            selectedRowsArr,
            exportText,
            showDetail,
            currentId,
            showRow,
            importSome,
            exportExcel,
            getListId,
            tableRef,
            currentIds,
            params,
            modalType,
            columns,
            compareClose,
            changeSortered,
            searchOptions: [
                {
                    label: '客户名称',
                    key: 'clientIds',
                    multiple: true,
                    type: 'clientSelectTree',
                    options: [],
                    checkStrictly: false,
                },
                {
                    label: '姓名',
                    key: 'staffName',
                },
                {
                    label: '身份证号',
                    key: 'certificateNum',
                },
                {
                    label: '缴费年月',
                    key: 'paymentDate',
                    type: 'month',
                },
                {
                    label: '状态',
                    key: 'isUsed',
                    type: 'select',
                    options: applyOptions,
                },
            ],
            searchData,
            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            individualTaxCheck,
            taxCheckVisible,
            formInline,
            formData,
            cancel,
            confirm,
            taxCheckList,
            tableListCancel,
            currentRecord,
            tableListConfirm,
            batchType,
            batchNext,
            currentExportTypeList,
            batchOpt,
            showCompare,
        }
    },
})
</script>

<style scoped lang="less">
.date {
    height: 300px;
}
</style>
