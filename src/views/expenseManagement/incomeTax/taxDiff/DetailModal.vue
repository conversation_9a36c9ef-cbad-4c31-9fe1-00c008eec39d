<template>
    <BasicEditModalSlot :visible="visible" title="查看详情" :footer="null" @cancel="modalClose" width="1300px">
        <div class="modalMain" v-if="currentData">
            <div class="cell">
                <div class="title">基本信息</div>
                <div class="main">
                    <div class="row">
                        <div class="col">
                            <div class="label">客户编号</div>
                            <div class="val">{{ currentData.unitNumber }}</div>
                        </div>
                        <div class="col">
                            <div class="label">客户名称</div>
                            <div class="val">{{ currentData.clientName }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <div class="label">姓名</div>
                            <div class="val">{{ currentData.staffName }}</div>
                        </div>
                        <div class="col">
                            <div class="label">性别</div>
                            <div class="val">{{ currentData.sexStr }}</div>
                        </div>
                        <div class="col">
                            <div class="label">年龄</div>
                            <div class="val">{{ currentData.age }}</div>
                        </div>
                        <div class="col">
                            <div class="label">身份证号</div>
                            <div class="val">{{ currentData.certificateNum }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <div class="label">手机号</div>
                            <div class="val">{{ currentData.phone }}</div>
                        </div>
                        <div class="col">
                            <div class="label">缴费年月</div>
                            <div class="val">{{ currentData.paymentDate }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cell">
                <div class="title">表单信息</div>
                <div class="main">
                    <BasicTable :tableDataList="[currentData]" :columns="columns" :sorter="false" :rowSelectionShow="false" />
                </div>
            </div>
            <div class="cell">
                <div class="title">差额产生原因</div>
                <div class="main">
                    <div class="row" v-for="(item, i) in currentData.recordList" :key="i">
                        <div class="tit">
                            <div>{{ i + 1 }}、</div>
                            <div class="colB">
                                <div class="tit">操作人</div>
                                <div class="info">{{ item.createdBy }}</div>
                            </div>
                            <div class="colB">
                                <div class="tit">操作时间</div>
                                <div class="info">{{ item.createdDate }}</div>
                            </div>
                        </div>
                        <div class="content">
                            <div class="colB">
                                <div class="tit">操作内容</div>
                                <div class="info">{{ item.changeMsg }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, watch, ref, toRefs } from 'vue'
import request from '/@/utils/request'
export default defineComponent({
    name: 'DetailModal',
    props: {
        visible: Boolean,
        diffId: String,
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const currentData = ref<any>(undefined)
        const { visible, diffId } = toRefs(props)
        const modalClose = () => {
            emit('cancel')
        }
        watch(visible, () => {
            getDetail()
        })

        const getDetail = () => {
            if (visible.value == true) {
                request.get(`/api/hr-welfare-compensations/salary-tax?id=${diffId.value}`).then((res) => {
                    console.log('getDefaultMonth', res)
                    currentData.value = res
                })
            } else {
                currentData.value = undefined
            }
        }

        return {
            modalClose,
            currentData,
            columns: [
                {
                    title: '单位缴纳部分',
                    dataIndex: 'status',
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'unitPension',
                            align: 'center',
                        },
                        {
                            title: '失业',
                            dataIndex: 'unitUnemployment',
                            align: 'center',
                        },
                        {
                            title: '医疗',
                            dataIndex: 'unitMedical',
                            align: 'center',
                        },
                        {
                            title: '工伤',
                            dataIndex: 'unitInjury',
                            align: 'center',
                        },
                        {
                            title: '滞纳金',
                            dataIndex: 'unitLateFee',
                            align: 'center',
                            width: 80,
                        },
                        {
                            title: '小计',
                            dataIndex: 'unitSubtotal',
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '个人缴纳部分',
                    dataIndex: 'status',
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'personalPension',
                            align: 'center',
                        },
                        {
                            title: '失业',
                            dataIndex: 'personalUnemployment',
                            align: 'center',
                        },
                        {
                            title: '医疗',
                            dataIndex: 'personalMedical',
                            align: 'center',
                        },
                        {
                            title: '小计',
                            dataIndex: 'personalSubtotal',
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保总计',
                    dataIndex: 'socialSecurityTotal',
                },
                {
                    title: '住房公积金',
                    dataIndex: '',
                    children: [
                        {
                            title: '单位',
                            dataIndex: 'unitAccumulationFund',
                            align: 'center',
                        },
                        {
                            title: '个人',
                            dataIndex: 'personalAccumulationFund',
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金总计',
                    dataIndex: 'accumulationFundTotal',
                },
                {
                    title: '个税',
                    dataIndex: 'personalTax',
                },
                {
                    title: '总计',
                    dataIndex: 'total',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.modalMain {
    height: 68vh;
    overflow-y: auto;
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
    }
    .main {
        padding: 10px 0;
        .row {
            margin: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            .tit {
                display: flex;
                align-items: center;
            }
            .content {
                padding-left: 20px;
                display: flex;
                .colB {
                    display: flex;
                    align-items: flex-start;
                    .tit {
                        white-space: nowrap;
                    }
                }
            }
            .col {
                width: 260px;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .label {
                    width: 100px;
                    text-align: right;
                    &:after {
                        content: '：';
                    }
                }
            }
            .colB {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .tit {
                    text-align: right;
                    &:after {
                        content: '：';
                    }
                }
                .info {
                    padding-right: 30px;
                }
            }
        }
    }
}
</style>
