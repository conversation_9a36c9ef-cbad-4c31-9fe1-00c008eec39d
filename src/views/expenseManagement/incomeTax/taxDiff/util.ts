import { InputNumber } from 'ant-design-vue'
import { minus, plus, round, times } from 'number-precision'
import { h } from 'vue'

// eslint-disable-next-line no-unused-vars
export const formatOriginForm = (
    src,
    // eslint-disable-next-line no-unused-vars
    callback: (val, label, order) => void,
    columns: Recordable[] = [],
    data: Recordable = { id: 1 },
) => {
    src.forEach((i) => {
        const col: Recordable = {
            title: i.columnLabel,
            dataIndex: i.columnLabel,
            align: 'center',
            width: 100,
            customRender: ({ text }) => {
                if (text.includes('&')) {
                    return h(
                        'a',
                        {
                            onClick: () => {
                                callback(text.slice(0, -1), i.columnLabel, i.order)
                            },
                        },
                        text.slice(0, -1),
                    )
                } else {
                    return text
                }
            },
        }
        data[i.columnLabel] = i.value + (i.hasMapping ? '' : '&')
        if (i.children && i.children.length) {
            const { columns: childColumns, data: childData } = formatOriginForm(i.children, callback)
            data = { ...data, ...childData }
            col.children = childColumns
        }
        columns.push(col)
    })
    return {
        columns,
        data,
    }
}

export const formatMapData = (data) => {
    console.log(data)
    return {
        columns: [
            {
                title: '#',
                cols: 1,
            },
            ...data[0].map((i) => ({
                title: i.value,
                cols: i.lastCol - i.firstCol + 1,
            })),
        ],
        data: [
            [
                {
                    value: '表单列',
                },
                ...data[1],
            ],
            [
                {
                    value: '表单字段',
                },
                ...data[2],
            ],
        ],
    }
}

export const formatDynamicCols = (cols, callback, showText = false, pagination) => {
    return cols.map((i) => ({
        title: i.value,
        dataIndex: i.key,
        children: i.children && i.children.length ? formatDynamicCols(i.children, callback, showText, pagination) : undefined,
        width: i.children && i.children.length ? i.children.length * 110 : 120,
        align: 'center',
        customRender: ({ record, text, index }) => {
            record[i.key] = record.hrBillDetailItemsList?.find((v) => v.expenseName == i.key)?.amount
            return showText
                ? text
                : h(InputNumber, {
                      value: text,
                      onChange: (e) => {
                          callback(e, (pagination.value.current - 1) * pagination.value.pageSize + index, i.key, true)
                      },
                      style: {
                          width: '100%',
                      },
                  })
        },
    }))
}

//formatCompareDynamicCols 递归children
export const recursionChild = (item) => {
    return item.map((item, key) => ({
        //    columnChildrenTitleList.value.push({
        title: item.title,
        dataIndex: item.dataIndex,
        key: item.key,
        align: 'center',
        width: item.children?.length ? item.children?.length * 110 : 120,
        customHeaderCell: (column) => {
            return {
                style: {
                    //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                    color: item.color ? '#FFFFFF' : '#000000', //如果color
                    'background-color': item.color ? item.color : '#FFFFFF',
                },
            }
        },
        children: item.children && item.children?.length ? recursionChild(item.children) : undefined,
        //    })
    }))
}
export const formatDynamicColns = (res, columnList) => {
    columnList.value = []
    res.dynamicHeaders.map((item, key) => {
        columnList.value.push({
            title: item.title,
            dataIndex: item.dataIndex,
            key: item.key,
            align: 'center',
            fixed: key < 4 ? 'left' : '',
            width: item.children?.length ? item.children?.length * 110 : 120,
            customHeaderCell: (column) => {
                return {
                    style: {
                        //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                        color: item.color ? '#FFFFFF' : '#000000', //如果color
                        'background-color': item.color ? item.color : '#FFFFFF',
                    },
                }
            },
            children: item.children && item.children?.length ? recursionChild(item.children) : undefined,
        })
    })
    return columnList
}

export const formatCompareDynamicCols = (cols, data) => {
    const res: Recordable[] = []
    cols.forEach((i, idx) => {
        data[i.key] = i.value
        res.push({
            title: idx === 0 ? '动态表头列' : '',
            dataIndex: i.key,
            colSpan: idx === 0 ? cols.length : 0,
            width: 120,
        })
    })
    return {
        columns: res,
        data,
    }
}

