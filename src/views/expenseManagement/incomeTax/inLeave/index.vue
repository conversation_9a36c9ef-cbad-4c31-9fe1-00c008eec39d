<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button
            type="primary"
            v-auth="'inLeave_onboarding_import'"
            v-show="params?.staffStatusArtificial != 5"
            @click="exportData(4)"
        >
            {{ exportText }}
        </Button>
        <Button
            type="primary"
            v-auth="'inLeave_resign_export'"
            v-show="params?.staffStatusArtificial != 4"
            @click="exportData(5)"
        >
            {{ exportText }}
        </Button>
    </div>
    <!-- 弹框 -->
    <BasicTable
        ref="tableRef"
        api="/api/hr-talent-staffs/page"
        deleteApi="/api/hr-quick-deductions/deletes"
        :params="{ ...params, entryExitPage: 1, izDefault: false, staffStatusList: staffStatusList }"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getTableData="getTableData"
        @getData2="(data) => (tableData = data)"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, nextTick, watch } from 'vue'
import { SearchBarOption } from '/#/component'

// import modal from './modal.vue'
import request from '/@/utils/request'
import { message, notification } from 'ant-design-vue'
import downFile from '/@/utils/downFile'
import moment from 'moment'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'InLeaveIndex',
    // components: { MyModal: modal },

    setup() {
        let tableRequestCont = 1
        let staffStatesOptions = [
            { label: '入职', value: 4 },
            { label: '离职', value: 5 },
        ] // 用工单位
        let clientPayOptions = [
            { label: '不发薪', value: 0 },
            { label: '发薪', value: 1 },
        ] // 用工单位
        onMounted(() => {})
        //默认当前月
        let currentMonth = [moment().startOf('month').format('YYYY-MM'), moment().endOf('month').format('YYYY-MM')]
        //上个月
        let previousMonth = [
            moment().subtract(1, 'months').startOf('month').format('YYYY-MM'),
            moment().subtract(1, 'months').endOf('month').format('YYYY-MM'),
        ]
        //筛选
        const params = ref<inObject>({
            staffStatusArtificial: 4,
            boardDateQuery: currentMonth,
            clientPay: 1,
            // departureDateQuery: currentMonth,
        })
        const staffStatusList = ref<Number[]>([3, 4, 9])
        const departureDateQueryShow = ref<Boolean>(false)
        const boardDateQueryShow = ref<Boolean>(true)
        const options: SearchBarOption[] = [
            {
                type: 'clientSelectTree',
                label: '客户名称',
                key: 'clientIds',
                // options: selectclientsOptions,
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },

            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'select',
                label: '是否发薪',
                key: 'clientPay',
                options: clientPayOptions,
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusArtificial',
                allowClear: false,
                options: staffStatesOptions,
            },
            {
                type: 'monthrange',
                label: '入职时间',
                key: 'boardDateQuery',
                allowClear: true,
                show: boardDateQueryShow,
            },
            {
                type: 'monthrange',
                label: '离职时间',
                key: 'departureDateQuery',
                allowClear: true,
                show: departureDateQueryShow,
            },
        ]

        //表格dom
        const tableRef = ref()
        let boardDateQuery = null
        let departureDateQuery = null
        watch(
            () => params.value.staffStatusArtificial,
            (newValue, oldValue) => {
                if (newValue == 4) {
                    departureDateQuery = params.value.departureDateQuery

                    params.value.departureDateQuery = null
                    params.value.boardDateQuery = boardDateQuery || currentMonth
                    departureDateQueryShow.value = false
                    boardDateQueryShow.value = true
                    tableRequestCont = boardDateQuery ? 2 : 1

                    staffStatusList.value = [3, 4, 9]
                } else if (newValue == 5) {
                    boardDateQuery = params.value.boardDateQuery

                    params.value.boardDateQuery = null
                    params.value.departureDateQuery = departureDateQuery || currentMonth
                    departureDateQueryShow.value = true
                    boardDateQueryShow.value = false
                    tableRequestCont = departureDateQuery ? 2 : 1

                    staffStatusList.value = [5]
                } else {
                    params.value.boardDateQuery = null
                    params.value.departureDateQuery = null
                    departureDateQueryShow.value = false
                    boardDateQueryShow.value = false

                    staffStatusList.value = []
                }
            },
        )
        const searchData = async () => {
            console.log(params.value)
            nextTick(() => {
                tableRef.value.refresh(1)
            })
        }
        const getTableData = (data) => {
            if (tableRequestCont == 1) {
                if (data.total == 0) {
                    if (params.value.staffStatusArtificial == 4) {
                        params.value.departureDateQuery = null
                        params.value.boardDateQuery = previousMonth
                        // tableRequestCont = 1
                    } else if (params.value.staffStatusArtificial == 5) {
                        params.value.boardDateQuery = null
                        params.value.departureDateQuery = previousMonth
                        // tableRequestCont = 1
                    } else {
                        params.value.boardDateQuery = null
                        params.value.departureDateQuery = null
                    }

                    // params.value.boardDateQuery = currentMonth
                    // params.value.departureDateQuery = currentMonth
                    nextTick(() => {
                        searchData()
                    })
                }
            }
            tableRequestCont++
        }
        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                sorter: false,
            },

            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '系统编号',
                dataIndex: 'systemNum',
                align: 'center',
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
            },
            {
                title: '手机号码',
                dataIndex: 'phone',
                align: 'center',
            },
            {
                title: '入职时间',
                dataIndex: 'boardDate',
                align: 'center',
            },
            {
                title: '离职时间',
                dataIndex: 'departureDate',
                align: 'center',
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                customRender: ({ record }) => {
                    return record.staffStatusLabel
                },
            },
        ]

        //删除
        // const deleteRow = (row = { id: '' }) => {
        //     tableRef.value.deleteRow(row.id).then((ref) => {
        //         console.log(ref)
        //     })
        // }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增费用项')
        const viewType = ref('edit')

        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增费用项'
            currentValue.value = null
        }
        const editRow = (record, type?) => {
            console.log(type)
            viewType.value = type || 'edit'
            showEdit.value = true
            modalTitle.value = '查看用户'
            if (type == 'see') {
                modalTitle.value = '查看用户'
            }
            currentValue.value = { ...record }
            // showEdit.value = true
            // modalTitle.value = '查看费用项'
            // currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增费用项'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // // 客户列表数据
        // const customerValue = ref(null)
        // // 客户列表显示隐藏
        // const customerVisible = ref(false)
        // // 点击客户数量
        // const clickQuantity = (row) => {
        //     customerVisible.value = true
        //     customerValue.value = row
        // }

        // // 关闭客户列表
        // const customerCancel = () => {
        //     customerVisible.value = false
        // }

        // //操作按钮
        // let myOperation = ref<inObject[]>(
        //     getHaveAuthorityOperation([
        //         {
        //             neme: '查看',
        //             auth: 'customerInfo_edit',

        //             show: (record) => {
        //                 return !record.isEdit
        //             },
        //             click: (record) => editRow(record, 'see'),
        //         },
        //     ]),
        // )
        // //导入导出
        // const importVisible = ref(false)
        // const importTemUrl = '/api/hr-quick-deductions/template'
        // const importUrl = '/api/hr-quick-deductions/import'
        // const exportUrl = '/api/hr-quick-deductions/export'
        // const handleOk = () => {
        //     visible.value = false
        //     importVisible.value = true
        // }
        // // 导入
        // const ImportData = () => {
        //     importVisible.value = true
        // }

        //导出
        //提示
        const openNotification = (tip) => {
            notification.open({
                message: '导出信息提示',
                description: tip,
                onClick: () => {
                    console.log('Notification Clicked!')
                },
            })
        }
        // 下载
        const selectedRowsArr = ref([])
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        const exportData = (staffStatus) => {
            let ids: any[] = []
            let body = { ...params.value }
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (exportText.value.indexOf('选中') != -1) {
                ids = selectedRowsArr.value.map((el: inObject) => {
                    return el.id
                })
                body['ids'] = ids
            }
            request
                .post('/api/hr-staff/search-export-entry', {
                    ...body,
                    entryExitPage: 1,
                    izDefault: false,
                    staffStatus,
                })
                .then((res) => {
                    if (res?.failureList?.length || res?.errorList?.length) {
                        let failureListTip = res?.failureList?.length
                            ? `${res?.failureList.join('、')}。
                                为${staffStatus == '5' ? '入职' : '离职'}人员
                                不可导出。`
                            : ''
                        let errorListTip = res?.errorList?.length
                            ? `${res?.errorList.join('、')}
                                的证件类型不一致
                                不可导出。`
                            : ''
                        let successTip = res?.successList?.length ? `正在为您导出${res?.successList?.length}条数据。` : ''
                        openNotification('您选择的信息当中' + failureListTip + errorListTip + successTip)
                    }

                    if (res?.successList?.length) {
                        downFile('post', '/api/hr-staff/export-entry', staffStatus == '5' ? '离职' : '入职', {
                            ids: res.successList,
                            excelName: staffStatus == '5' ? '离职' : '入职',
                        })
                        // request
                        //     .post(
                        //         '/api/hr-staff/export-entry',
                        //         {
                        //             ids: res.successList,
                        //             excelName: staffStatus == '5' ? '离职' : '入职',
                        //         },
                        //         { loading: false },
                        //     )
                        //     .then((ref) => {
                        //         downFile('get', ref, staffStatus == '5' ? '离职' : '入职')
                        //     })
                    }
                })
        }

        return {
            tableData,
            exportText,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            // customerValue,
            columns,
            params,
            staffStatusList,
            searchData,
            tableRef,
            createRow,
            editRow,

            //获取列表接口数据
            getTableData,

            // clickQuantity,
            // customerVisible,
            // customerCancel,

            //操作按钮
            // myOperation,
            // myOperationClick,

            viewType,

            // 导出
            selectedRowsArr,
            exportData,
            // 列表查询参数
            // basicTableParams,
        }
    },
})
</script>

<style scoped lang="less">
.text {
    padding: 20px;
    text-align: center;
}
</style>
