<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportAll">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bills/normal-salary"
        exportUrl="/api/hr-bills/normal-salary/export"
        :params="params"
        :columns="columns"
        :rowSelectionShow="true"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import moment from 'moment'
import request from '/@/utils/request'
import { getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'NormalSalary',
    setup() {
        const tableRef = ref()
        const params = ref({
            paymentDate: moment().startOf('month').format('YYYY-MM'),
        })
        const exportStatesOptions = [
            { label: '未导出', value: 0 },
            { label: '已导出', value: 1 },
        ]

        onMounted(() => {
            //获取默认费用年用
            getDefaultMonth()
        })

        const getDefaultMonth = () => {
            request.get('/api/hr-bills/normal-salary/paymentDate').then((res) => {
                console.log('getDefaultMonth', res)
                if (params.value.paymentDate != res) {
                    params.value.paymentDate = res
                    tableRef.value.refresh(1)
                }
            })
        }

        const searchData = () => {
            tableRef.value.refresh(1)
        }

        const tableCheckboxProps = (record) => {
            return {
                disabled: record.billStateStr != '已锁定',
            }
        }

        // 下载
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //后台判断，如果不传ids集，默认下载所有数据
        const exportAll = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            exportText,
            exportAll,
            tableRef,
            params,
            tableCheckboxProps,
            columns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                },
                {
                    title: '费用年月',
                    dataIndex: 'paymentDate',
                },
                {
                    title: '人数',
                    dataIndex: 'staffNum',
                },
                {
                    title: '本期收入合计',
                    dataIndex: 'totalIncome',
                },
                {
                    title: '本期福利合计',
                    dataIndex: 'totalWelfare',
                },
                {
                    title: '导出状态',
                    dataIndex: 'isExport',
                    sortField: 'isExport',
                    customRender: ({ record }) => {
                        return record.isExport == 0 ? '未导出' : '已导出'
                    },
                },
            ],
            searchOptions: [
                {
                    label: '客户名称',
                    key: 'clientIds',
                    multiple: true,
                    type: 'clientSelectTree',
                    placeholder: '客户名称',
                    maxTag: '0',
                    checkStrictly: false,
                },
                {
                    label: '费用年月',
                    key: 'paymentDate',
                    type: 'month',
                },
                {
                    label: '导出状态',
                    key: 'isExport',
                    type: 'select',
                    options: exportStatesOptions,
                },
            ],
            searchData,
            selectedRowsArr,
        }
    },
})
</script>

<style scoped lang="less">
.bill_normal {
    color: #eb3333;
}
.bill_block {
    color: #000;
}
</style>
