<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'salaryPayment_export'" type="primary" @click="exportData">{{ exportText }}</Button>
        <Button v-auth="'salaryPayment_bankExport'" type="primary" @click="exportBank">{{ exportBankText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bills/salary-payment"
        :exportUrl="exportApi"
        :columns="columns"
        :params="params"
        @selectedRowsArr="selHandle"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="editRecord(record)">员工明细</Button>
            <!-- &nbsp;
            <Button type="primary" size="small" @click="editRecord(record)">查看发放记录</Button> -->
        </template>
    </BasicTable>
    <!-- 员工明细 -->
    <DeatilModal v-model:visible="showCreate" @cancel="resetData"  :currentRecord="currentRecord" @confirm="searchData" />
</template>

<script lang="ts" setup>
import { computed, ref, nextTick } from 'vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText, getDynamicBankText } from '/@/utils'
import { billStateList } from '/@/utils/dictionaries'
import DeatilModal from './detailModal.vue'
import downFile from '/@/utils/downFile'
import CreateModal from './CreateModal.vue'
const exportApi = ref('')
const params = ref<{ [x: string]: any }>({})
const tableRef = ref()
const showCreate = ref(false)
const searchData = () => {
    tableRef.value.refresh(1)
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}

const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
const exportBankText = computed(() => {
    return getDynamicBankText('导出', params.value, selArr.value)
})

const exportData = async () => {
    exportApi.value = '/api/hr-bills/salary-payment/export'
    nextTick(async () => {
        await tableRef.value.exportRow('ids', exportText.value, params.value)
        tableRef.value.refresh()
    })
}
const exportBank = async () => {
    exportApi.value = '/api/hr-bills/salary-payment/export/bank-report'
    nextTick(async () => {
        await tableRef.value.exportRow('ids', exportBankText.value, params.value)
        tableRef.value.refresh()
    })
}

const showDetail = ref(false)
const currentRecord = ref(undefined)
const showRecord = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}
const editRecord = (record) => {
    currentRecord.value = { ...record }
    showCreate.value = true
}

const resetData = () => {
    tableRef.value.refresh()
}
const exportStateList = [
    {
        label: '已导出',
        value: 1,
    },
    {
        label: '未导出',
        value: 0,
    },
]
// grantStateList
const grantStateList = [
    {
        label: '未维护',
        value: 0,
    },
    {
        label: '已发放',
        value: 1,
    },
    {
        label: '部分发放',
        value: 2,
    },
    {
        label: '未发放',
        value: 3,
    },
]
const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 120,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 90,
    },
    {
        title: '人数',
        dataIndex: 'staffNum',
        width: 90,
        ellipsis: true,
    },
    {
        title: '本期薪酬合计',
        dataIndex: 'realSalary',
        width: 170,
        ellipsis: true,
    },
    {
        title: '账单状态',
        dataIndex: 'billState',
        width: 170,
        ellipsis: true,
        customRender: ({ text }) => {
            return billStateList.find((item) => {
                return text == item.value
            })?.label
        },
    },
    {
        title: '导出状态',
        dataIndex: 'isSalaryExportStr',
        sortField: 'isSalaryExport',
        width: 170,
        ellipsis: true,
    },
    {
        title: '发放状态',
        dataIndex: 'grantStateLabel',
        sortField: 'grantState',
        width: 170,
        ellipsis: true,
    },
    // {
    //     title: '发放时间',
    //     dataIndex: 'grantDate',
    //     width: 170,
    //     ellipsis: true,
    //     sorter: false,
    // },
    // {
    //     title: '未发金额',
    //     dataIndex: 'amount',
    //     width: 170,
    //     ellipsis: true,
    // },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 90,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '费用年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '账单状态',
        key: 'billState',
        type: 'select',
        options: billStateList,
    },
    {
        label: '导出状态',
        key: 'isSalaryExport',
        type: 'select',
        options: exportStateList,
    },
    {
        label: '发放状态',
        key: 'grantState',
        type: 'select',
        options: grantStateList,
    },
]
</script>

<style scoped lang="less"></style>
