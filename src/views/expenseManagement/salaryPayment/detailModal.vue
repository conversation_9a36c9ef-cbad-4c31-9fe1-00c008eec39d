<template>
    <BasicEditModalSlot title="员工明细" :visible="visible" @cancel="modalClose" width="1400px" centered>
        <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
        <div class="btns">
            <Button type="primary" @click="exportBank">{{ exportBankText }}</Button>
            <Button type="primary" @click="batchDistribution">批量发放</Button>
        </div>
        <div>
            <Button v-if="selectedStaff.length == tableData.length" size="small" type="primary" @click="selAll(false)">
                取消全选
            </Button>
            <Button v-else size="small" type="primary" @click="selAll(true)"> 选择全部 </Button>
        </div>
        <Table
            :bordered="true"
            :columns="columns"
            ref="tableRef"
            :dataSource="[
                ...tableData.slice(pagination.pageSize * (pagination.current - 1), pagination.pageSize * pagination.current),
            ]"
            rowKey="id"
            size="small"
            :pagination="pagination"
            :scroll="{ x: 100 }"
            :rowSelection="{
                selectedRowKeys: selectedStaff.map((i) => i.id),
                onChange: (keys, rows) => selStaff(rows),
            }"
        >
            <template #operation="{ record }">
                <Button type="primary" size="small" @click="editRecord(record)">发放状态</Button>
                &nbsp;
                <Button type="primary" size="small" @click="lookHistory(record)">查看发放记录</Button>
            </template>
        </Table>
        <template #footer>
            <div>
                <!-- <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">确定</Button> -->
            </div>
        </template>
        <!-- 发放状态 -->
        <CreateModal v-model:visible="showCreate" :billDetailIds="billDetailIds" @confirm="searchData" />
        <!-- 查看发放记录 -->
    </BasicEditModalSlot>
    <BasicEditModalSlot title="发放记录" :visible="seeStatus" @cancel="historyClose" width="1200px" centered>
        <div class="btns">
            <Button type="primary" @click="exportHistory">导出</Button>
            <Button type="primary" @click="batchDownload">批量下载</Button>
        </div>
        <BasicTable
            ref="table2Ref"
            :columns="batchColumns"
            :sorter="false"
            :isPage="false"
            :tableDataList="table2DataList"
            @selectedRowsArr="selectFun"
        >
            <template #operation="{ record }">
                <Button type="primary" size="small" @click="itemDownLoad(record)">下载</Button>
            </template>
        </BasicTable>
        <template #footer>
            <div></div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, watch } from 'vue'
import { SearchBarOption } from '/#/component'
import { getDynamicBankText } from '/@/utils'
import downFile from '/@/utils/downFile'
import CreateModal from './CreateModal.vue'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'

const props = defineProps({
    visible: Boolean,
    currentRecord: {
        type: Object,
        default: () => {},
    },
})
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

const { visible, currentRecord } = toRefs(props)
watch(visible, () => {
    if (visible.value) {
        billId.value = currentRecord.value?.id
        getTable()
    } else {
        remodal()
    }
})
const pagination = ref({
    current: 1,
    pageSize: 10,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    onChange: (current) => {
        pagination.value.current = current
    },
})
const remodal = () => {
    pagination.value.current = 1
    params.value = {}
    selectedStaff.value = []
}
const selectedStaff = ref<Recordable[]>([])
const tableData = ref<any>([])
const billId = ref('')
const billDetailIds = ref<any[]>([])
const params = ref<{ [x: string]: any }>({})
const tableRef = ref()
const showCreate = ref(false)
const searchData = () => {
    pagination.value.current = 1
    getTable()
}

const getTable = () => {
    request
        .post(`/api/hr-bill-details/salary-payment`, {
            ...params.value,
            billId: billId.value,
        })
        .then((res) => {
            tableData.value = [...res]
            pagination.value.total = tableData.value.length
        })
}

const selAll = (isSel) => {
    selectedStaff.value = isSel ? [...tableData.value] : []
}
const selStaff = (list) => {
    selectedStaff.value = list
}

const selArr2 = ref<Recordable[]>([])
const selectFun = (arr) => {
    selArr2.value = arr
}

const modalClose = () => {
    emit('cancel')
    emit('update:visible', false)
}

const exportBankText = computed(() => {
    return getDynamicBankText('导出', params.value, selectedStaff.value)
})

const exportBank = async () => {
    //   nextTick(async () => {
    let ids = selectedStaff.value.map((el) => el.id)
    downFile('post', `/api/hr-bill-details/salary-payment/bank-report`, '员工明细银行报盘.xlsx', {
        ...params.value,
        billId: billId.value,
        ids: ids.length == 0 ? undefined : ids,
    })
}

const editRecord = (record) => {
    billDetailIds.value[0] = record.id
    showCreate.value = true
}

// 批量发放状态
const batchDistribution = () => {
    if (selectedStaff.value.length == 0) {
        message.warning({ content: '至少选择一条数据' })
        return
    }
    billDetailIds.value = [...selectedStaff.value.map((el) => el.id)]
    showCreate.value = true
}
/**
 * @查看发放记录逻辑
 */
const table2DataList = ref<any[]>([])
const batchId = ref('')
const seeStatus = ref(false)
const lookHistory = (record) => {
    // console.log('查看发放记录==》', record)
    batchId.value = record.id
    seeStatus.value = true
}
const historyClose = () => {
    seeStatus.value = false
}
watch(seeStatus, () => {
    if (seeStatus.value === true) {
        getBatchList()
    }
})
// 单个下载
const itemDownLoad = (record) => {
    if (!record.hrAppendixDTOList || record.hrAppendixDTOList.length == 0) {
        message.warning({ content: '暂无附件！' })
        return
    }
    downFile('post', `/api/hr-bill-details/grant/download`, '', {
        ids: [record.id],
    })
}
// 记录导出
const exportHistory = () => {
    downFile('post', `/api/hr-bill-details/grant/export`, '发放记录.xlsx', {
        billDetailId: batchId.value,
        ids: selArr2.value.length == 0 ? selArr2.value.map((el) => el.id) : selArr2.value?.map((el) => el.id),
    })
    selArr2.value = []
}
// 批量下载
const batchDownload = () => {
    if (selArr2.value.length == 0) {
        message.warning({ content: '至少选择一条数据' })
        return
    }
    downFile('post', `/api/hr-bill-details/grant/download`, '', {
        ids: selArr2.value?.map((el) => el.id),
    })
}
// 获取表格数据
const getBatchList = () => {
    request.get(`/api/hr-bill-details/grant-list?billDetailId=${batchId.value}`, {}).then((res) => {
        table2DataList.value = [...res]
    })
}

// grantStateList
const grantStateList = [
    {
        label: '未维护',
        value: 0,
    },
    {
        label: '已发放',
        value: 1,
    },
    {
        label: '未发放',
        value: 3,
    },
]
const columns = ref([
    {
        title: '姓名',
        dataIndex: 'name',
        width: 90,
        align: 'center',
    },
    {
        title: '身份证号',
        dataIndex: 'certificateNum',
        width: 150,
        align: 'center',
    },
    {
        title: '银行卡号',
        dataIndex: 'salaryCardNum',
        width: 150,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '本期薪酬合计',
        dataIndex: 'realSalary',
        width: 100,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '工资发放状态',
        dataIndex: 'grantState',
        // sortField: 'grantState',
        width: 100,
        align: 'center',
        customRender: ({ text }) => {
            return grantStateList.find((el) => el.value == text)?.label
        },
    },
    {
        title: '纳税所属账期',
        dataIndex: 'taxPeriod',
        width: 100,
        align: 'center',
    },
    {
        title: '工资发放时间',
        dataIndex: 'grantDate',
        width: 100,
        align: 'center',
        customRender: ({ text }) => {
            return text
        },
    },

    {
        title: '操作',
        dataIndex: 'operation',
        width: 200,
        align: 'center',
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
])

const batchColumns = ref([
    {
        title: '工资发放状态',
        dataIndex: 'grantState',
        width: 125,
        customRender: ({ text }) => {
            return grantStateList.find((el) => el.value == text)?.label
        },
    },
    {
        title: '纳税所属账期',
        dataIndex: 'taxPeriod',
        width: 125,
    },
    {
        title: '发放时间',
        dataIndex: 'grantDate',
        width: 100,
    },
    {
        title: '备注',
        dataIndex: 'remark',
        width: 150,
    },
    {
        title: '操作角色',
        dataIndex: 'createdBy',
        width: 120,
        ellipsis: true,
    },
    {
        title: '操作时间',
        dataIndex: 'createdDate',
        width: 150,
        ellipsis: true,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
])
const searchOptions: SearchBarOption[] = [
    {
        label: '姓名',
        key: 'name',
    },
    {
        label: '身份证号',
        key: 'certificateNum',
    },
    {
        label: '银行卡号',
        key: 'salaryCardNum',
    },
    {
        label: '工资发放状态',
        key: 'grantState',
        type: 'select',
        options: grantStateList,
    },
]
</script>

<style scoped lang="less"></style>
