<template>
    <BasicEditModalSlot title="发放时间" :visible="visible" @cancel="modalClose" width="800px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
            <FormItem
                label="费用内容"
                name="accountType"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 19 }"
                :rules="{ required: false, message: '请选择费用内容', trigger: 'blur' }"
            >
                <RadioGroup v-model:value="formData.grantState" @change="radioChange" name="radioGroup">
                    <Radio :value="t.value" v-for="t in salaryPostStatus" :key="t">{{ t.label }}</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem
                label="发放时间"
                v-if="visGrantTime"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 19 }"
                :rules="{ required: true, message: '请选择发放时间', trigger: 'change' }"
            >
                <DatePicker
                    v-model:value="formData.grantDate"
                    placeholder="发放时间"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                />
            </FormItem>
            <FormItem
                label="纳税所属账期"
                v-if="visGrantTime"
                :rules="{ required: true, message: '请选择纳税所属账期', trigger: 'change' }"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 19 }"
            >
                <MonthPicker
                    v-model:value="formData.paymentDay"
                    placeholder="纳税所属账期"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                />
            </FormItem>
            <FormItem label="备注：" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <Textarea :rows="3" v-model:value="formData.remark" />
            </FormItem>
            <FormItem label="附件" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                    <Button type="primary" size="small">上传文件</Button>
                </Upload>
                <div class="files">
                    <span class="item" v-for="(i, idx) in formData.hrAppendixDTOList" :key="i.id">
                        <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                        <span @click="removeFile(idx)">x</span>
                    </span>
                </div>
            </FormItem>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { Upload } from 'ant-design-vue'
import moment from 'moment'
import { plus } from 'number-precision'
import { computed, ref, toRefs, watch } from 'vue'
import { salaryPostStatus } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils/index'
import request from '/@/utils/request'
import { uploadFile } from '/@/utils/upload'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object,
    billDetailIds: {
        type: Array,
        default: () => [],
    },
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, billDetailIds } = toRefs(props)

watch(visible, () => {
    if (visible.value) {
    } else {
        selArr.value = []
    }
})

const formData = ref<Recordable>({
    // billId: '',
    grantState: salaryPostStatus[0].value,
    grantDate: moment().format('YYYY-MM-DD'),
    paymentDay: moment().format('YYYY-MM'),
    // amount: '',
    remark: '',
    hrAppendixDTOList: [],
})
const tableData = ref<Recordable[]>([])

let totalAmount = computed(() => {
    let arr = selArr.value.map((i) => i.amount ?? 0)
    return arr.reduce((pre, cur) => plus(pre, cur), 0)
})

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.hrAppendixDTOList.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.hrAppendixDTOList.splice(idx, 1)
}
const visGrantTime = ref<boolean>(true)
const visNoSendMoney = ref<boolean>(false)

const selArr = ref<Recordable[]>([])
const radioChange = (e) => {
    formData.value.grantState = e.target.value
    if (e.target.value == 1) {
        //已发放  不显示发放时间 不显示未发金额
        visGrantTime.value = true
        visNoSendMoney.value = false
    } else if (e.target.value == 2) {
        //部分发放  只显示 未发金额
        visGrantTime.value = true
        visNoSendMoney.value = true
    } else if (e.target.value == 3) {
        //未发放 都不显示
        visGrantTime.value = false
        visNoSendMoney.value = false
    }
}

const resetData = () => {
    formData.value = {
        // billId: '',
        grantState: salaryPostStatus[0].value,
        grantDate: moment().format('YYYY-MM-DD'),
        paymentDay: moment().format('YYYY-MM'),
        // amount: '',
        remark: '',
        hrAppendixDTOList: [],
    }
    tableData.value = []
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async () => {
    let params
    if (formData.value.grantState == '1') {
        params = {
            ...formData.value,
            payYear: formData.value.paymentDay.split('-')[0],
            payMonthly: formData.value.paymentDay.split('-')[1],
            billDetailIds: billDetailIds.value,
        }
    } else if (formData.value.grantState == '3') {
        params = {
            grantState: formData.value.grantState,
            hrAppendixDTOList: formData.value.hrAppendixDTOList,
            remark: formData.value.remark,
            billDetailIds: billDetailIds.value,
        }
    }

    if (params?.paymentDay) {
        delete params?.paymentDay
    }
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const res = await request.post(`/api/hr-bill-details/maintain-grant-state`, params)
        emit('confirm', 1)
        modalClose()
    } finally {
        confirmLoading.value = false
    }
}
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
