<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="600px">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            style="max-height: 60vh; overflow-y: auto"
            class="form-flex"
        >
            <template v-for="(item, i) in myOptions" :key="i">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]">
                    <template #Cascader>
                        <ClientSelectTree v-model:value="formData[item.name]" :itemForm="item" :renderInBody="true" />
                    </template>
                    <template #file>
                        <ImportFile
                            v-model:fileUrls="formData.salaryUrl"
                            ref="refImportFile"
                            :multiple="false"
                            :count="1"
                            :accept="'.xlsx,.xls'"
                            @changeResponse="uploadComplete"
                        />
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'

import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import ImportFile from '/@/components/ImportFile/src/ImportFile.vue'

export default defineComponent({
    name: 'CreateModal',
    components: {
        ImportFile,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        interface Option {
            value: string
            label: string
            loading?: boolean
            isLeaf?: boolean
            children?: Option[]
        }
        const value = ref<string[]>([])
        //请求
        const api = '/api/hr-original-salaries'
        // const api1 = '/api/hr-message-lists/page'
        const { title, item, visible } = toRefs(props)
        const editDisabled = ref<boolean>(false)
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '客户名称',
                name: 'clientId',
                slots: 'Cascader',
                type: 'slots',
            },
            {
                label: '费用年月',
                name: 'costDate',
                type: 'month',
            },
            {
                label: '标题',
                name: 'title',
            },
            {
                label: '薪酬原单',
                name: 'salaryUrl',
                slots: 'file',
                type: 'slots',
                ruleType: 'array',
                default: [],
                message: '请选择薪酬原单',
            },
        ])
        const clientId = ref<string[]>([])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        formData.value.salaryUrl = []

        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const uploadComplete = (res) => {
            if (!formData.value.title) {
                formData.value.title = res.originName.split('.')[0]
            }
        }

        // confirm handle
        const confirm = () => {
            const fileUrl: any = formData.value.salaryUrl[0] && formData.value.salaryUrl[0].response.fileUrl
            // formData.value.salaryUrl = [{ fileUrl: fileUrl }]
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', { ...formData.value, salaryUrl: fileUrl })
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', { ...formData.value, salaryUrl: fileUrl })
                        message.success('编辑成功!')
                    }
                    emit('cancel')
                    emit('confirm')
                    // cancel()
                    // 表单关闭后的其它操作 如刷新表
                    // emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            uploadComplete,
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            value,
            // clientList,
            clientId,

            // loadData,
        }
    },
})
</script>
<style lang="less">
:deep(.ant-form-item) {
    width: 53%;
}
</style>
