<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="createRow">新增</Button>
        <Button type="primary" @click="download" class="down">{{ exportText }}</Button>
        <Button type="primary" danger @click="deleteRow">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-original-salaries/page"
        deleteApi="/api/hr-original-salaries/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <!-- {{ record.id + 'url' }} -->
            <!-- <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" /> -->
            <Button type="primary" size="small" @click="previewFile(record.salaryUrl)" style="margin-right: 10px">查看 </Button>
        </template>
    </BasicTable>

    <!-- 新增 -->
    <CreateModal :visible="showCreate" :title="modalTitle" :item="currentValue" @cancel="createClose" @confirm="createConfirm" />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { computed, defineComponent, onMounted, ref } from 'vue'
import CreateModal from './CreateModal.vue'

import { SearchBarOption } from '/#/component'

import { previewFile, getDynamicText } from '/@/utils/index'
import downFile, { downMultFile } from '/@/utils/downFile'
import request from '/@/utils/request'
export default defineComponent({
    components: { CreateModal },
    setup() {
        const params = ref<{}>({})
        const currentValue = ref(null)
        const tableRef = ref() //表格dom
        const showCreate = ref(false)

        const modalTitle = ref('新增')
        const currentItem = ref<any>(null)
        // 新增弹框显示
        const createRow = () => {
            showCreate.value = true
            modalTitle.value = '新增薪酬原单'
            currentValue.value = null
        }
        // 关闭新增弹框
        const createClose = () => {
            showCreate.value = false
        }
        const createConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }

        }
        const searchData = () => {
            tableRef.value.refresh(1)
        }

        // 批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
            })
        }
        const options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                type: 'clientSelectTree',
                multiple: true,
                // options: selectclientsOptions,
                placeholder: '客户名称',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                label: '费用年月',
                key: 'costDate',
                type: 'month',
            },

            {
                label: '标题',
                key: 'title',
            },
            {
                label: '上传日期',
                key: 'createdStartDateQuery',
                type: 'daterange',
            },
        ]

        // 下载
        // 选中的数据
        const myUrls = ref<inObject[]>([])
        // 多选框
        const selectedRowsArr = (item) => {
            myUrls.value = item
        }
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, myUrls.value)
        })
        const tableData = ref<any>([])

        const download = async () => {
            let resList: any[] = []
            let body = {}
            let ids: any[] = []
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (exportText.value.indexOf('选中') != -1) {
                myUrls.value.forEach((item: inObject) => {
                    ids.push(item.id)
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            resList = await request.post('/api/hr-original-salaries/list', body)
            let urls: string[] = []
            let urlsName: string[] = []
            resList.forEach((element) => {
                urlsName.push(element.clientName)
                urls.push(element.salaryUrl)
            })
            if (!urls.length) {
                message.error('暂无可供下载的薪酬原单')
                return
            }
            request.post(
                '/api/sys-oper-logs/download',
                {
                    title: '费用',
                    operDetail: '薪酬原单下载:' + urlsName.join(),
                    fileUrl: urls.join(),
                },
                { loading: false },
            )
            if (urls.length > 1) downMultFile('薪酬原单批量导出', urls, urlsName)
            else downFile('get', urls[0], urlsName[0], {})
        }

        return {
            tableData,
            exportText,
            previewFile,
            currentValue,
            deleteRow,
            showCreate,
            createConfirm,
            createClose,
            selectedRowsArr,
            download,
            searchData,
            modalTitle,
            createRow,
            currentItem,
            params,
            tableRef,
            options,
            myOperation: [
                {
                    neme: '查看',
                    show: true,
                },
            ],
            columns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '费用年月',
                    dataIndex: 'costDate',
                    align: 'center',
                    width: 100,
                    sorter: false,
                },

                {
                    title: '标题',
                    dataIndex: 'title',
                    align: 'center',
                    width: 220,
                },
                {
                    title: '上传日期',
                    dataIndex: 'createdDate',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 100,
                    slots: { customRender: 'operation' },
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
.down {
    background: #3eb889;
    border: none;
}
</style>
