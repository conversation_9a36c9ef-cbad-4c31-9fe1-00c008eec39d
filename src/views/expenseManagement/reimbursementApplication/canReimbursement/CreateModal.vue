<template>
    <BasicEditModalSlot :title="currentRecord ? '编辑' : '新增'" :visible="visible" @cancel="modalClose" width="800px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <Row>
                <Col span="12">
                    <FormItem label="申请人">
                        <Input :value="formData.applyRealName" disabled placeholder="申请人" />
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="申请日期">
                        <DatePicker
                            v-model:value="formData.applyDate"
                            placeholder="申请日期"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
            </Row>
            <div v-if="showClientName">
                <Row>
                    <Col span="12">
                        <FormItem label="缴费年月">
                            <MonthPicker
                                v-model:value="formData.paymentDate"
                                placeholder="缴费年月"
                                format="YYYY-MM"
                                valueFormat="YYYY-MM"
                                disabled
                                @change="getBillData"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="申请金额" name="amount">
                            <!-- amount -->
                            <InputNumber v-model:value="total" :min="0" disabled placeholder="申请金额" style="width: 90%" />
                            <span style="margin-left: 5px">元</span>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem name="title" label="标题" :rules="{ required: true, message: '请输入标题', trigger: 'change' }">
                            <Input v-model:value="formData.title" placeholder="标题" />
                        </FormItem>
                    </Col>
                </Row>
            </div>
            <div v-if="!showClientName">
                <Row>
                    <Col span="12">
                        <FormItem label="客户名称" name="clientId">
                            <ClientSelectTree
                                v-model:value="formData.clientId"
                                :itemForm="{ placeholder: '客户名称' }"
                                disabled
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="缴费年月">
                            <MonthPicker
                                v-model:value="formData.paymentDate"
                                placeholder="缴费年月"
                                format="YYYY-MM"
                                valueFormat="YYYY-MM"
                                disabled
                                @change="getBillData"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem label="申请金额" name="amount">
                            <InputNumber v-model:value="total" :min="0" disabled placeholder="申请金额" style="width: 90%" />
                            <span style="margin-left: 5px">元</span>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem name="title" label="标题" :rules="{ required: true, message: '请输入标题', trigger: 'change' }">
                            <Input v-model:value="formData.title" placeholder="标题" />
                        </FormItem>
                    </Col>
                </Row>
            </div>
            <FormItem label="费用明细" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <div class="btns" v-if="formData.accountType == 6 && formData.accountType != null">
                    <Button size="small" type="primary" @click="clientSelect">客户选择</Button>
                </div>
                <BasicTable
                    ref="tableRef"
                    :tableDataList="tableData"
                    :columns="columns"
                    :sorter="false"
                    :useIndex="true"
                    @selectedRowsArr="selHandle"
                    :checkboxProps="getCheckboxProps"
                >
                    <template #invoiceType="{ record, text }">
                        <Select
                            :value="{ value: record.invoiceType }"
                            :options="contentList"
                            labelInValue
                            @change="(val) => selectChange(record, val)"
                            placeholder="费用内容"
                            :getPopupContainer="() => body"
                        />
                    </template>
                </BasicTable>
            </FormItem>
            <FormItem label="附件" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                    <Button type="primary" size="small">上传文件</Button>
                </Upload>
                <div class="files">
                    <span class="item" v-for="(i, idx) in formData.enclosures" :key="i.id">
                        <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                        <span @click="removeFile(idx)">x</span>
                    </span>
                </div>
            </FormItem>
            <FormItem label="其它说明" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <Textarea :rows="3" v-model:value="formData.memo" />
            </FormItem>
            <FormItem label="审核结果通知" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <CheckboxGroup v-model:value="formData.noticeRoles" :options="roleList" />
            </FormItem>
        </Form>
        <myShowModel v-model:visible="showMyModel" :dataList="dataList" @confirm="myModelConfirm" @cancel="myModelCancel" />
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(0)">暂存</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">发送</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ref, toRefs, watch, computed } from 'vue'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'
import { message, Modal, Row, Upload } from 'ant-design-vue'
import { getReimbursementContentList, getReimbursementNoticeRoleList } from '/@/utils/api'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
import { getSpecialCustomer } from '/@/utils/api'
import { plus } from 'number-precision'
import myShowModel from './component/myShowModel.vue'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord } = toRefs(props)
const isData = ref(false) //用来判断是否是编辑进来还是客户选择返回的
const selArr = ref<Recordable[]>([])
const showClientName = ref(false)
watch(visible, () => {
    if (visible.value && currentRecord?.value) {
        getList()
        getData()
        selHandle
        console.log(currentRecord?.value)
        if (
            currentRecord?.value?.accountType == 1 ||
            currentRecord?.value?.accountType == 2 ||
            currentRecord?.value?.accountType == 3 ||
            currentRecord?.value?.accountType == 5
        ) {
            showClientName.value = true
        } else {
            showClientName.value = false
        }
    }

    if (visible.value) {
        isData.value = true
    } else {
        isData.value = false
        selArr.value = []
        isSpecialCustomer.value = false
        customerSelectList = []
    }
})
const isSpecialCustomer = ref(false)
const dataList = ref<any>([])
const specialCustomerList = ref<any>([])
// 获取model详情
const getData = async () => {
    specialCustomerList.value = await getSpecialCustomer()
    const res = await request.get(`/api/hr-bill-reimbursement-applies/${currentRecord?.value?.id}`)
    specialCustomerList.value.forEach((el) => {
        if (el.key == res.clientId) {
            isSpecialCustomer.value = true
        }
    })
    formData.value = {
        ...res,
        noticeRoles: res.noticeRoles?.split(',') || [],
        applyRealName: useUserStore().getUserInfo?.realName,
    }
    res.hrBillReimbursementClientDTOList = res.hrBillReimbursementClientDTOList ? res.hrBillReimbursementClientDTOList : []

    dataList.value = res.hrBillReimbursementClientDTOList || []
    res.detailDTOList.forEach((el) => {
        el.hrBillReimbursementDetailClientDTOList = res.hrBillReimbursementClientDTOList
    })
    // 代发工资默认选中判断
    res.detailDTOList?.forEach((el) => {
        if (el.checkOn === null) {
            el.checkOn = 0
        }
        if (el.invoiceTypeName === '代发工资' && (el.state != 2 || el.state != 4)) {
            el.checkOn = 1
        }
        if (el.invoiceType == 5 || el.invoiceType == 3 || el.invoiceType == 2 || el.invoiceType == 1) {
            total.value = res.amount
        }
    })

    tableData.value = res.detailDTOList || []
}

const formData = ref<Recordable>({
    applyRealName: useUserStore().getUserInfo?.realName,
    applyDate: moment().format('YYYY-MM-DD'),
    noticeRoles: [],
    enclosures: [],
})
const tableData = ref<Recordable[]>([])

const showMyModel = ref(false)
const defaultChecked = ref(false)
const total = ref()
// 如果未发送就默认不可选中的代码
const getCheckboxProps = (record: inObject) => {
    defaultChecked.value =
        currentRecord != undefined && record.checkOn == 1 && record.state != 2 && record.state != 4 ? true : false
    if (defaultChecked.value || record.checkOn == 1) {
        total.value = record.amount
    }
    return {
        disabled: currentRecord != undefined && (record.state == 2 || record.state == 4),
        defaultChecked: defaultChecked.value,
        id: record.id + '',
    }
}

const selHandle = (arr, keys) => {
    tableData.value.forEach((el) => {
        if (keys.includes(el.id)) {
            el.checkOn = 1
        } else {
            el.checkOn = 0
        }
    })
    selArr.value = arr
    let arr1 = selArr.value.map((i) => i.amount ?? 0)
    total.value = arr1.reduce((pre, cur) => plus(pre, cur), 0)
    console.log(total.value)
}
let totalAmount = computed(() => {
    let arr = selArr.value.map((i) => i.amount ?? 0)
    return arr.reduce((pre, cur) => plus(pre, cur), 0)
})

const clientSelect = () => {
    isData.value = false
    let ids = customerSelectList.map((el: any) => el.id)
    dataList.value.forEach((el) => {
        if (ids?.includes(el.id)) {
            el.checkOn = 1
        } else {
            el.checkOn = 0
        }
    })
    showMyModel.value = true
}

let customerSelectList = []

const myModelConfirm = (arr) => {
    // console.log("选中的数组=》",arr);

    if (arr.length == 0) {
        isData.value = false
    }
    showMyModel.value = false
    customerSelectList = arr
    tableData.value.map((el) => {
        el.hrBillReimbursementDetailClientDTOList = arr
    })
}
const myModelCancel = () => {
    isData.value = false
    showMyModel.value = false
}

const selectChange = (record, val) => {
    let index = tableData.value.findIndex((el) => {
        return el.id == record.id
    })
    //1."代发工资" 2"代缴社保" 3"代缴公积金" 4"其他" 5"代缴医保"
    if (!isData.value) {
        tableData.value[index].amount = amontFun1(record)
    }
    record.invoiceType = val.value
    record.invoiceTypeName = val.label
}

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.enclosures.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.enclosures.splice(idx, 1)
}

const getBillData = async () => {
    if (!formData.value.clientId || !formData.value.paymentDate) {
        return
    }
    formData.value.title =
        (formData.value.clientName || '') +
        (formData.value.paymentDate ? moment(formData.value.paymentDate).format('MM月份') : '') +
        `资金发放申请`
    const res = await request.post(`/api/hr-bill-reimbursement-applies/getBillTotalInfo`, {
        clientId: formData.value.clientId,
        paymentDate: formData.value.paymentDate,
    })
    if (res.amount && res.detailDTOList) {
        Modal.confirm({
            title: '检测到相关费用明细，是否导入？',
            onOk: () => {
                formData.value.amount = res.amount ?? 0
                tableData.value = [
                    ...tableData.value,
                    ...res.detailDTOList?.map((i, idx) => ({
                        ...i,
                        id: new Date().getTime() + idx,
                    })),
                ]
            },
        })
    }
}
const tableRef = ref()
const resetData = () => {
    total.value = 0
    formData.value = {
        applyRealName: useUserStore().getUserInfo?.realName,
        applyDate: moment().format('YYYY-MM-DD'),
        noticeRoles: [],
        enclosures: [],
    }
    tableData.value = []
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async (approveStatus) => {
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const form = {
            ...formData.value,
            approveStatus,
            detailDTOList: tableData.value.map((i) => ({
                ...i,
            })),
            noticeRoles: formData.value.noticeRoles?.join(','),
            enclosures: formData.value.enclosures?.map((i) => ({
                id: i.id,
            })),
            applyUserId: approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
            amount: total.value,
        }
        if (approveStatus == 0) {
            await request.put(`/api/hr-bill-reimbursement-applies`, form)
        } else {
            if (formData.value?.reimbursementState == 1 && formData.value?.accountType != null) {
                await request.put(`/api/hr-bill-reimbursement-applies`, form)
            } else {
                await request.post(`/api/hr-bill-reimbursement-applies/save`, form)
            }
        }
        emit('confirm', 1)
        modalClose()
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '费用内容',
        dataIndex: 'invoiceTypeName',
        width: 200,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 150,
        customRender: ({ record }) => {
            if (formData.value.accountType == 6 && formData.value.accountType != null) {
                let res = amontFun1(record)
                record.amount = res
                return record.amount
            } else {
                return record.amount
            }
        },
    },
]
const amontFun1 = (record) => {
    let tnum
    if (record.invoiceType == 1) {
        tnum = computedAdd(record.hrBillReimbursementDetailClientDTOList, 'realSalaryLock', 'realSalaryAmount')
    }
    if (record.invoiceType == 2) {
        tnum = computedAdd(record.hrBillReimbursementDetailClientDTOList, 'socialSecurityLock', 'socialSecurityAmount')
    }
    if (record.invoiceType == 5) {
        tnum = computedAdd(record.hrBillReimbursementDetailClientDTOList, 'medicalInsuranceLock', 'medicalInsuranceAmount')
    }
    if (record.invoiceType == 3) {
        tnum = computedAdd(record.hrBillReimbursementDetailClientDTOList, 'accumulationFoundLock', 'accumulationFoundAmount')
    }
    return tnum
}

const computedAdd = (arr, lock, key) => {
    const res = arr
        .map((el) => {
            if (el[lock] == 2 || el[lock] == 4) {
                return Number(0)
            } else {
                el.checkOn = 1
                return Number(el[key] ?? 0)
            }
        })
        .reduce((pre, cur) => {
            return plus(pre ?? 0, cur ?? 0)
        }, 0)
    return res
}
const body = document.body

const contentList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getReimbursementContentList()) as LabelValueOptions
    if (formData.value?.accountType == 6 && formData.value?.accountType != null) {
        const ind = contentList.value.findIndex((el) => el.invoiceType == 4)
        contentList.value.splice(ind, 1)
    }
    roleList.value = (await getReimbursementNoticeRoleList()) as LabelValueOptions
}
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
