<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="1100px" centered>
        <Steps :current="formData.approveStatus" size="small" labelPlacement="vertical">
            <Step title="发起申请" />
            <Step title="客服经理审核" />
            <Step title="会计确认" />
            <Step title="已结束" />
        </Steps>
        <div class="cell" style="margin-top: 20px">
            <div class="title">申请信息</div>
            <div class="main">
                <Row>
                    <Col span="12" class="col">
                        <div class="tit">标题</div>
                        <div class="val ellipsis">{{ formData.title }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请人</div>
                        <div class="val">{{ formData.applyRealName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请日期</div>
                        <div class="val">{{ formData.applyDate }}</div>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <div class="tit">客户名称</div>
                        <div class="val">{{ formData.clientName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">缴费年月</div>
                        <div class="val">{{ formData.paymentDate }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请金额</div>
                        <div class="val">{{ formData.amount }}</div>
                    </Col>
                </Row>
                <BasicTable
                    :tableDataList="formData.detailDTOList || []"
                    :columns="columns"
                    :sorter="false"
                    :rowSelectionShow="false"
                    :useIndex="true"
                />
                <Row class="row">
                    <div class="tit">附件</div>
                    <div class="val">
                        <a
                            v-for="i in formData.enclosures"
                            :key="i.id"
                            @click="previewFile(i.fileUrl)"
                            style="margin-right: 10px"
                        >
                            {{ i.originName }}
                        </a>
                    </div>
                </Row>
                <Row>
                    <div class="tit">其它说明</div>
                    <div class="val">{{ formData.memo }}</div>
                </Row>
                <Row>
                    <div class="tit">审核结果通知</div>
                    <div class="val">{{ formData.noticeRolesName }}</div>
                </Row>
            </div>
        </div>
        <div class="cell">
            <div class="title">审核流程</div>
            <div class="main">
                <div class="logItem" v-for="(i, idx) in formData.applyOpLogsDTOS || []" :key="i.id">
                    <div class="name ellipsis">{{ idx + 1 }}、 操作人：（{{ i.roleName }}) {{ i.realName }}</div>
                    <div class="date ellipsis">操作时间：{{ i.createdDate }}</div>
                    <div class="msg ellipsis">操作信息：{{ i.remark ? i.message + '（' + i.remark + '）' : i.message }}</div>
                </div>
            </div>
        </div>
        <div class="cell" v-if="title == '审核'">
            <div class="title">
                {{ reimbursementApproveStatusList.find((i) => i.value === formData.approveStatus)?.label }}
            </div>
            <div class="main">
                <Textarea :rows="3" v-model:value="formData.rejectMsg" placeholder="若拒绝，请填写拒绝原因" />
            </div>
        </div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <!-- 审核 -->
            <template v-if="title == '审核'">
                <Button :loading="confirmLoading" type="primary" danger @click="modalConfirm(2)">拒绝</Button>
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">通过</Button>
            </template>
            <!-- 确认 -->
            <template v-if="title == '确认'">
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(3)">确认</Button>
            </template>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { Steps, Step, message } from 'ant-design-vue'
import { reimbursementApproveStatusList } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils/index'

const props = defineProps({
    visible: Boolean,
    currentRecord: {
        type: Object,
    },
    title: String,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord, title } = toRefs(props)

const formData = ref<Recordable>({
    approveStatus: 0,
})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-reimbursement-applies/${currentRecord?.value?.id}`)
    formData.value = { ...res, approveStatus: [0, 1, 4, 4, 4, 4, 4, 2]?.[res.approveStatus] ?? res.approveStatus }
}

watch(visible, () => {
    visible.value && currentRecord?.value?.id && getData()
})

const modalClose = () => {
    emit('update:visible', false)
    formData.value = {}
}
const confirmLoading = ref(false)
const modalConfirm = async (auditType) => {
    // 审核类型 1 通过, 2 拒绝, 3 确认
    if (auditType === 2 && !formData.value.rejectMsg) {
        message.warn('请填写拒绝原因！')
        return
    }
    try {
        confirmLoading.value = true
        await request.put(`/api/hr-bill-reimbursement-applies/audit`, {
            ids: [formData.value.id],
            auditType,
            rejectMsg: formData.value.rejectMsg,
        })
        modalClose()
        emit('confirm', 1)
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '内容',
        dataIndex: 'invoiceTypeName',
        width: 120,
        ellipsis: true,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
    },
]
</script>

<style scoped lang="less">
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .main {
        padding: 10px;
        .col {
            display: flex;
            .val {
                width: 80%;
            }
        }

        .tit {
            display: inline-block;
            margin: 10px 0;
            color: #999999;
            &::after {
                content: '：';
            }
        }
        .val {
            display: inline-block;
            margin: 10px 0;
        }
    }
}
.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: nowrap;
    .val {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
    }
}

.logItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    color: #666;
    .name {
        width: 30%;
    }
    .date {
        width: 24%;
    }
    .msg {
        width: 35%;
    }
}
</style>
