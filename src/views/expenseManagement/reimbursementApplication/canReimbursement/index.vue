<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData()" />
    <div class="btns">
        <Button type="primary" @click="createRecord" v-auth="'reimbursementApplication_create'">新建</Button>
        <Button type="primary" @click="batchCheck()" danger v-auth="'reimbursementApplication_del'"> 批量删除 </Button>
        <Button type="primary" @click="batchDownload"> 下载报销单 </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-reimbursement-applies/page"
        :params="{ ...params, reimbursementState: 2 }"
        :columns="columns"
        @selectedRowsArr="
            (arr) => {
                selectedRowsArr = arr
            }
        "
    >
        <template #operation="{ record }">
            <Button v-if="getEditBtnVisible(record)" type="primary" size="small" @click="editRecord(record)">编辑</Button>
            <Button v-else type="primary" size="small" @click="showRecord(record, '查看')">查看</Button>
            &nbsp;
            <Button type="primary" size="small" @click="itemDownload(record)">下载</Button>
        </template>
    </BasicTable>
    <!-- 只有点击编辑 -->
    <CreateModal v-model:visible="showCreate" :currentRecord="currentRecord" @confirm="searchData" />
    <!-- 审核 -->
    <CheckModal v-model:visible="showCheck" :title="modalTitle" :currentRecord="currentRecord" @confirm="searchData" />
    <!-- 新建 -->
    <newReimbursement v-model:visible="showNewReimbur" ref="newReimburseRef" @confirm="newReimburseConfirm" />
    <!-- 新建报销的详情 -->
    <addDatailModel
        v-model:visible="showReimburse"
        :detailCont="detailCont"
        :costType="costType"
        :prevData="prevData"
        :clientList="clientList"
        @confirm="searchData"
    />
    <!-- 新建选择 工资代发 时 -->
    <wagePaying
        v-model:visible="showWagePay"
        :wagepayData="wagepayData"
        @prevClick="prevClick"
        :prevData="prevData"
        @confirm="wagePayNext"
    />
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import { SearchBarOption } from '/#/component'
import CreateModal from './CreateModal.vue'
import CheckModal from './CheckModal.vue'
import newReimbursement from './newReimbursement.vue'
import useUserStore from '/@/store/modules/user'
import { getUrgeUserList } from '/@/utils/api'
import request from '/@/utils/request'
import { message, notification } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import downFile from '/@/utils/downFile'
import addDatailModel from './addDatailModel.vue'
import wagePaying from './wagePaying.vue'

const route = useRoute()
const params = ref<inObject>({
    approveStatusList: route.query?.approveStatusList ? JSON.parse(route.query?.approveStatusList as string) : undefined,
})
const tableRef = ref()
const checkerReason = ref('')

const searchData = (isCur?) => {
    if (isCur) tableRef.value.refresh()
    else tableRef.value.refresh(1)
}

const showCreate = ref(false)
const showNewReimbur = ref(false)
const createRecord = () => {
    showNewReimbur.value = true
}

// 新建报销的弹窗下一步按钮
const showReimburse = ref(false)
const showWagePay = ref(false)
const detailCont = ref<any>()
const wagepayData = ref<any>()
let costType = ref('') //前端自定义费用类型
const prevData = ref<Object>({})
const newReimburseConfirm = (val, payType, obj) => {
    //  4代发工资 1代缴社保 3代缴公积金  2代缴医保
    costType.value = payType
    prevData.value = obj
    if (payType == '4') {
        wagepayData.value = val
        showWagePay.value = true
    } else {
        detailCont.value = val
        showReimburse.value = true
    }
}
let newReimburseRef = ref()
const prevClick = (val) => {
    showReimburse.value = false
    showNewReimbur.value = true
    nextTick(() => {
        newReimburseRef.value.updataform(val)
    })
}
const clientList = ref<any>([])
const wagePayNext = (res, prevFormData, prevList) => {
    clientList.value = prevList
    costType.value = prevFormData.accountType
    prevData.value = prevFormData
    detailCont.value = res
    showWagePay.value = false
    showReimburse.value = true
}

const showCheck = ref(false)
const showReject = ref(false)
const currentRecord = ref<Recordable | undefined>(undefined)
const editRecord = (record) => {
    currentRecord.value = { ...record }
    showCreate.value = true
}
const roles = useUserStore().getUserInfo.roles?.map((i) => i.roleKey)
const canCheck = (record) => {
    let flag = false
    switch (record.approveStatus) {
        // 0 未发起, 1 待客服经理审批, 2 待总经理审批, 3 待财务负责人审批, 4 待总裁审批, 5 待监事会主席审批, 6 待董事长审批, 7 待会计确认, 8 同过, 9 拒绝
        case 1:
            if (roles?.includes('customer_service_manager')) {
                flag = true
            }
            break
        case 2:
            if (roles?.includes('total_manager')) {
                flag = true
            }
            break
        case 3:
            if (roles?.includes('financial_director')) {
                flag = true
            }
            break
        case 4:
            if (roles?.includes('ceo')) {
                flag = true
            }
            break
        case 5:
            if (roles?.includes('supervisory_board_chairman')) {
                flag = true
            }
            break
        case 6:
            if (roles?.includes('chairman')) {
                flag = true
            }
            break
        default:
            break
    }
    return flag
}
const canConfirm = (record) => {
    // 会计确认
    return record.approveStatus == 7 && roles?.includes('accounting')
}
const canUrge = (record) => {
    return record.approveStatus && record.approveStatus < 8
}

const showUrge = ref(false)
const formData = ref({
    realName: undefined,
    urgeUserId: undefined,
    sendWxMsg: false,
    sendPcMsg: true,
    content: undefined,
})
const modalTitle = ref('查看')
const showRecord = (record, title) => {
    modalTitle.value = title
    currentRecord.value = { ...record }
    showCheck.value = true
}

const selectedRowsArr = ref<Recordable[]>([])
const urgeRecord = (record) => {
    currentRecord.value = { ...record }
    getList(record.id)
    showUrge.value = true
    isBatch.value = false
}
const isBatch = ref(false)
const batchUrge = () => {
    if (!selectedRowsArr.value.length) {
        message.error('请选择要催办的报销单')
        return
    } else {
        if (
            !selectedRowsArr.value.every((ele) => {
                return canUrge(ele)
            })
        ) {
            notification.warning({
                message: `${selectedRowsArr.value
                    .filter((el: inObject) => {
                        return !canUrge(el)
                    })
                    .map((el: inObject) => el.title)
                    .join()} 不在审核流程中,无法催办,请重新选择`,
            })
        } else {
            showUrge.value = true
            isBatch.value = true
        }
    }
}
const urgeClose = () => {
    showUrge.value = false
    isBatch.value = false
    formData.value = {
        realName: undefined,
        urgeUserId: undefined,
        sendWxMsg: false,
        sendPcMsg: true,
        content: undefined,
    }
}
const urgeConfirm = async () => {
    if (isBatch.value) {
        if (!formData.value.content) {
            message.warn('请填写提醒内容')
            return
        } else {
            try {
                const res = await request.post(`/api/hr-bill-reimbursement-applies/batchApplyUrge`, {
                    applyIdList: selectedRowsArr.value.map((el) => {
                        return el.id
                    }),
                    ...formData.value,
                })
                if (res.checkCode == 200) message.success(res?.checkMsg)
                if (res.checkCode == 500) message.error(res?.checkMsg)
            } catch (err: any) {
                message.error(err)
            }
        }
    } else {
        await request.post(`/api/hr-bill-reimbursement-applies/reimbursementApplyUrge`, {
            applyId: currentRecord?.value?.id,
            ...formData.value,
        })
        message.success('催办成功!')
    }
    urgeClose()
    searchData(1)
}

const batchDownload = async () => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        const url = await request.post(
            `/api/hr-bill-reimbursement-applies/download`,
            selectedRowsArr.value.map((el) => el.id),
        )
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const itemDownload = async (record) => {
    try {
        const url = await request.post(`/api/hr-bill-reimbursement-applies/download`, [record?.id])
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const cancelHandle = () => {
    showReject.value = false
    checkerReason.value = ''
}

const batchCheck = async () => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        await request.post(
            `/api/hr-bill-reimbursement-applies/deletes`,
            selectedRowsArr.value.map((el) => el.id),
            // auditType, // 2 拒绝 1 通过
            // rejectMsg: checkerReason.value,
        )
        searchData()
        cancelHandle()
    } catch (error) {
        console.log(error)
    }
}

const columns = [
    {
        title: '报销单位',
        dataIndex: 'title',
        width: 500,
    },
    {
        title: '可报销金额',
        dataIndex: 'amount',
        width: 110,
    },
    {
        title: '申请日期',
        dataIndex: 'applyDate',
        width: 110,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 130,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '报销单位',
        key: 'title',
    },
    {
        type: 'numberrange',
        label: '申请金额',
        key: 'amountQuery',
        canNegative: true,
    },
    {
        label: '申请日期',
        key: 'applyDate',
        type: 'date',
    },
]
const urgeUserList = ref<LabelValueOptions>([])
const getList = async (applyId) => {
    urgeUserList.value = (await getUrgeUserList(applyId)) as LabelValueOptions
}
const getEditBtnVisible = (record) => {
    // reimbursementState   报销类型 1报销申请记录 2可申请报销
    // reimbursementLockState 锁定状态 1已锁定 2未锁定
    if (
        (record.reimbursementState == 2 && record.reimbursementLockState == 2) ||
        (record.reimbursementState == 1 && record.approveStatus == 0)
    ) {
        return true
    }
    // if (record.approveStatus == 0 || record.approveStatus == 9) return true
    return false
}
</script>

<style scoped lang="less"></style>
