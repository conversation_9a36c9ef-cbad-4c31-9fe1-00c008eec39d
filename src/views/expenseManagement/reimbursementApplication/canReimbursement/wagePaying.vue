<template>
    <BasicEditModalSlot title="客户选择" :visible="visible" @cancel="prevBtn(0)" width="800px" centered>
        <Input type="text" v-model:value="iptValue" @change="IptChange" placeholder="请输入账户名称" class="Ipt" />
        <Table
            :bordered="true"
            :columns="columns"
            :dataSource="[
                ...tableData.slice(pagination.pageSize * (pagination.current - 1), pagination.pageSize * pagination.current),
            ]"
            :row-key="(record, index) => record.id ?? index"
            size="small"
            :pagination="pagination"
            :scroll="{ x: 100 }"
            :rowSelection="{
                selectedRowKeys: selectedStaff,
                onChange: selStaff,
            }"
        />
        <!--    
         -->
        <template #footer>
            <div style="width: 100%; display: flex; justify-content: space-between">
                <Button type="primary" @click="prevBtn(1)">上一步</Button>
                <Button type="primary" @click="modalConfirm()">下一步</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { message } from 'ant-design-vue'

const formData = ref<Recordable>({
    title: '',
    accountType: '2',
    selectValue: [],
    paymentDate: '',
})

const tableDataRes: any = ref<any>([])
const tableData = ref<Recordable[]>([])
const props = defineProps({
    visible: Boolean,
    prevData: Object,
    wagepayData: Object,
})
const emit = defineEmits(['update:visible', 'confirm', 'prevClick'])

const { visible, prevData, wagepayData } = toRefs(props)

watch(visible, () => {
    if (visible.value) {
        resetData()
        tableDataRes.value = wagepayData?.value as any
        tableData.value = [...tableDataRes.value]
        pagination.value.current = 1
        pagination.value.total = tableData.value.length
    }
})
watch(
    () => formData.value.selectValue,
    (newVal, oldval) => {
        // console.log('监听==', newVal, oldval)
    },
    { deep: true },
)

const resetData = () => {
    iptValue.value = ''
    selectedStaff.value = []
    selectedItem = []
}
let selectedItem: any = []
const selectedStaff = ref<Recordable[]>([])
const selStaff = (keys, list) => {
    selectedStaff.value = keys
    let arr = tableData.value.map((el) => {
        if (keys.includes(el.id)) {
            return {
                ...el,
                flag: true,
            }
        } else {
            return {
                ...el,
                flag: false,
            }
        }
    })
    selectedItem = arr.filter((el) => el.flag)
}

const pagination = ref({
    current: 1,
    pageSize: 5,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['3', '5', '10', '20', '30'],
    onChange: (page, size) => {
        pagination.value.current = page
    },
    onShowSizeChange: (current, size) => {
        pagination.value.pageSize = size
    },
})
const rePagination = () => {
    pagination.value.total = 0
    pagination.value.current = 1
}

const prevBtn = (v) => {
    emit('update:visible', false)
    if (v == 1) {
        emit('prevClick', prevData?.value)
    }
    rePagination()
}

const iptValue = ref<string>('')
const IptChange = async () => {
    tableData.value = []
    tableDataRes.value.forEach((el: any) => {
        if (el.clientName?.includes(iptValue.value)) {
            tableData.value.push(el)
        }
    })
    pagination.value.current = 1
    pagination.value.total = tableData.value.length
}

const confirmLoading = ref(false)
const modalConfirm = async () => {
    if (selectedItem.length == 0) {
        message.info('请选择客户')
        return
    }
    let arr = selectedItem.map((el: any) => {
        return {
            billId: el.id,
            billTitle: el.title,
            amount: el.totalAmount,
        }
    })
    //  1代发工资 2代缴社保 3代缴公积金  5代缴医保
    try {
        confirmLoading.value = true
        let res: any
        const params = {
            accountType: 1,
            paymentDate: prevData?.value?.paymentDate,
            hrBillReimbursementClientDTOList: arr,
        }
        res = await request.post(`/api/hr-bill-reimbursement-applies/generate`, params)

        emit('confirm', res, prevData?.value, selectedItem)
        rePagination()
    } finally {
        confirmLoading.value = false
    }
}
const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        key: 'clientName',
        width: 200,
        sorter: (a, b) => a.clientName.length - b.clientName.length,
    },
    {
        title: '账单标题',
        dataIndex: 'title',
        key: 'title',
        width: 150,
        sorter: (a, b) => a.title.length - b.title.length,
    },
    {
        title: '金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 150,
        sorter: (a, b) => Number(a.totalAmount) - Number(b.totalAmount),
    },
]
</script>

<style scoped lang="less">
.Ipt {
    width: 25%;
    margin-bottom: 20px;
}
</style>
