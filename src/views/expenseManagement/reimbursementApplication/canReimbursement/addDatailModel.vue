<template>
    <div>
        <BasicEditModalSlot title="新增" :visible="visible" @cancel="modalClose" width="800px" centered>
            <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
                <Row>
                    <Col span="12">
                        <FormItem label="申请人">
                            <Input :value="formData.applyRealName" disabled placeholder="申请人" />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem
                            label="申请日期"
                            :rules="{ required: true, type: 'number', message: '请选择日期', trigger: 'change' }"
                        >
                            <DatePicker
                                v-model:value="formData.applyDate"
                                placeholder="申请日期"
                                format="YYYY-MM-DD"
                                valueFormat="YYYY-MM-DD"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <!-- 是代发工资 -->
                <div v-if="costType == '4'">
                    <Row>
                        <Col span="12">
                            <FormItem label="客户数量" name="amount">
                                <InputNumber
                                    v-model:value="formData.clientNum"
                                    :min="0"
                                    placeholder="客户数量"
                                    disabled
                                    style="width: 100%"
                                />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="缴费年月">
                                <MonthPicker
                                    v-model:value="formData.paymentDate"
                                    disabled
                                    placeholder="缴费年月"
                                    format="YYYY-MM"
                                    valueFormat="YYYY-MM"
                                />
                                <!-- removeFile -->
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem
                                label="申请金额"
                                name="amount"
                                :rules="{ required: false, type: 'number', message: '请输入申请金额', trigger: 'change' }"
                            >
                                <!-- amount -->
                                <InputNumber
                                    v-model:value="formData.amount"
                                    disabled
                                    :min="0"
                                    placeholder="申请金额"
                                    style="width: 80%"
                                />
                                <span style="margin-left: 5px">元</span>
                            </FormItem>
                        </Col>
                    </Row>
                </div>
                <!-- 不是 代发工资 -->
                <div v-if="costType != '4'">
                    <Row>
                        <Col span="12">
                            <FormItem label="缴费账户" name="costCount">
                                <TreeSelect
                                    v-model:value="formData.selectValue"
                                    style="width: 100%"
                                    :tree-data="treeData"
                                    tree-checkable
                                    :maxTagCount="1"
                                    placeholder="缴纳账户"
                                    disabled
                                    :showCheckedStrategy="SHOW_PARENT"
                                />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="缴费年月">
                                <MonthPicker
                                    v-model:value="formData.paymentDate"
                                    disabled
                                    placeholder="缴费年月"
                                    format="YYYY-MM"
                                    valueFormat="YYYY-MM"
                                />
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem label="客户数量" name="amount">
                                <InputNumber
                                    v-model:value="formData.clientNum"
                                    :min="0"
                                    placeholder="客户数量"
                                    disabled
                                    style="width: 100%"
                                />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem
                                label="申请金额"
                                name="amount"
                                :rules="{ required: true, type: 'number', message: '请输入申请金额', trigger: 'change' }"
                            >
                                <!-- amount -->
                                <InputNumber
                                    v-model:value="formData.amount"
                                    disabled
                                    :min="0"
                                    placeholder="申请金额"
                                    style="width: 80%"
                                />
                                <span style="margin-left: 5px">元</span>
                            </FormItem>
                        </Col>
                    </Row>
                </div>

                <FormItem
                    name="title"
                    label="标题"
                    :label-col="{ span: 3 }"
                    :wrapper-col="{ span: 20 }"
                    :rules="{ required: true, message: '请输入标题', trigger: 'change' }"
                >
                    <Input v-model:value="formData.title" placeholder="标题" width="100%" />
                </FormItem>

                <FormItem label="费用明细" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                    <BasicTable
                        ref="tableRef"
                        :tableDataList="tableData"
                        :columns="columns"
                        :sorter="false"
                        :rowSelectionShow="false"
                        :useIndex="true"
                    >
                        <template #action="{ record, text, index }">
                            <Button type="primary" @click="seeDetail(record, text, index)">查看明细</Button>
                        </template>
                    </BasicTable>
                </FormItem>
                <FormItem label="附件" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                    <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                        <Button type="primary" size="small">上传文件</Button>
                    </Upload>
                    <div class="files">
                        <span class="item" v-for="(i, idx) in formData.enclosures" :key="i.id">
                            <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                            <span @click="removeFile(idx)">x</span>
                        </span>
                    </div>
                </FormItem>
                <FormItem label="其它说明" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                    <Textarea :rows="3" v-model:value="formData.memo" />
                </FormItem>
                <FormItem label="审核结果通知" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                    <CheckboxGroup v-model:value="formData.noticeRoles" :options="roleList" />
                </FormItem>
            </Form>
            <template #footer>
                <Button @click="modalClose">取消</Button>
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(0)">暂存</Button>
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">发送</Button>
            </template>
        </BasicEditModalSlot>
        <BasicEditModalSlot title="报销明细" :visible="childVisible" @cancel="childCancel" width="800px" centered>
            <Input type="text" v-model:value="iptValue" @change="IptChange" placeholder="请输入账户名称" class="Ipt" />
            <Table
                class="myStyle"
                :columns="childColumns"
                :row-key="(record, index) => record.id ?? index"
                :data-source="showChildrenTableData"
                @change="tableChange"
                :pagination="false"
                bordered
            />
            <template #footer>
                <Button :loading="confirmLoading" type="primary" @click="childCancel">关闭</Button>
            </template>
        </BasicEditModalSlot>
        <BasicEditModalSlot title="报销明细" :visible="wageVisible" @cancel="wageChildCancel" width="800px" centered>
            <Input type="text" v-model:value="wageIptValue" @change="wageIptChange" placeholder="请输入账户名称" class="Ipt" />
            <Table
                :bordered="true"
                class="myStyle"
                :columns="wageColumns"
                :dataSource="[
                    ...wageChildrenTableData.slice(
                        pagination.pageSize * (pagination.current - 1),
                        pagination.pageSize * pagination.current,
                    ),
                ]"
                rowKey="id"
                size="small"
                :pagination="pagination"
            />
            <template #footer>
                <Button :loading="confirmLoading" type="primary" @click="wageChildCancel">关闭</Button>
            </template>
        </BasicEditModalSlot>
    </div>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ref, toRefs, watch, computed } from 'vue'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'
import { Upload } from 'ant-design-vue'
import { getReimbursementContentList, getReimbursementNoticeRoleList, postCostAccount } from '/@/utils/api'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
import { plus } from 'number-precision'
import { TreeSelect } from 'ant-design-vue'

const { SHOW_PARENT } = TreeSelect

const props = defineProps({
    visible: Boolean,
    detailCont: Object,
    costType: String, //费用类型   1代发工资
    prevData: Object,
    clientList: Array,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, detailCont, costType, prevData, clientList } = toRefs(props)

watch(visible, () => {
    if (visible.value && detailCont?.value) {
    }
    if (visible.value) {
        if (costType?.value != '4') {
            getSelectList() //获取客户列表
        }
        initFun()
        getList()
    } else {
        selArr.value = []
    }
})

const initFun = () => {
    formData.value.paymentDate = detailCont?.value?.paymentDate
    formData.value.clientNum = detailCont?.value?.clientNum || clientList?.value?.length
    formData.value.amount = detailCont?.value?.amount
    formData.value.title = prevData?.value?.title
    formData.value.applyDate = detailCont?.value?.applyDate
    // 费用内容 1代发工资 2代缴社保 3代缴公积金 4其他 5代缴医保
    detailCont?.value?.detailDTOList.forEach((it) => {
        if (it.invoiceType == 1) {
            it.invoiceTypeLabel = '代发工资'
        } else if (it.invoiceType == 2) {
            it.invoiceTypeLabel = '代缴社保'
        } else if (it.invoiceType == 3) {
            it.invoiceTypeLabel = '代缴公积金'
        } else if (it.invoiceType == 4) {
            it.invoiceTypeLabel = '代缴其他'
        } else if (it.invoiceType == 5) {
            it.invoiceTypeLabel = '代缴医保'
        }
    })
    tableData.value = detailCont?.value?.detailDTOList
}
let treeData = ref([])
const getSelectList = async () => {
    const res: any = await postCostAccount({ platformType: costType?.value })
    treeData.value = res
    formData.value.selectValue = prevData?.value?.selectValue
}

const formData = ref<Recordable>({
    applyRealName: useUserStore().getUserInfo?.realName,
    applyDate: moment().format('YYYY-MM-DD'),
    noticeRoles: [],
    enclosures: [],
    title: '',
    selectValue: [],
    paymentDate: '',
})
const tableData = ref<Recordable[]>([])
let childrenTableData: any = []
const showChildrenTableData = ref<Recordable[]>([])
const wageChildrenTableData = ref<Recordable[]>([])

let childVisible = ref(false)
let wageVisible = ref(false)
let currentIndex
const seeDetail = (records, text, index) => {
    currentIndex = index
    if (costType?.value == '4') {
        wageVisible.value = true
        wageChildrenTableData.value = clientList?.value as any
        pagination.value.total = wageChildrenTableData.value.length
    } else {
        childVisible.value = true
        detailCont?.value?.hrBillReimbursementClientDTOList.forEach((i) => {
            i.clientNum = i.clientIds.length
        })
        childrenTableData = detailCont?.value?.hrBillReimbursementClientDTOS
        showChildrenTableData.value = childrenTableData
    }
}

const pagination = ref({
    current: 1,
    pageSize: 10,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30'],
    onChange: (page, size) => {
        pagination.value.current = page
    },
    onShowSizeChange: (current, size) => {
        pagination.value.pageSize = size
    },
})

let totalAmount = computed(() => {
    let arr = selArr.value.map((i) => i.amount ?? 0)
    return arr.reduce((pre, cur) => plus(pre, cur), 0)
})

const childCancel = async () => {
    childVisible.value = false
}
const wageChildCancel = async () => {
    wageVisible.value = false
}
let iptValue = ref('')

const IptChange = async () => {
    showChildrenTableData.value = []
    childrenTableData.forEach((el) => {
        if (el.accountNumber.includes(iptValue.value)) {
            showChildrenTableData.value.push(el)
        }
    })
}
let wageIptValue = ref('')
const wageIptChange = async () => {
    wageChildrenTableData.value = []
    if (clientList?.value) {
        clientList.value.forEach((el: any) => {
            if (el.clientName?.includes(wageIptValue.value)) {
                wageChildrenTableData.value.push(el)
            }
        })
        pagination.value.current = 1
        pagination.value.total = showChildrenTableData.value.length
    }
}
const tableChange = async () => {}

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.enclosures.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.enclosures.splice(idx, 1)
}

const selArr = ref<Recordable[]>([])
const tableRef = ref()

const amountRecalculate = () => {
    return tableData.value.reduce((pre, cur) => {
        return plus(pre, Number(cur.amount) || 0)
    }, 0)
}

const resetData = () => {
    formData.value = {
        applyRealName: useUserStore().getUserInfo?.realName,
        applyDate: moment().format('YYYY-MM-DD'),
        noticeRoles: [],
        enclosures: [],
        title: '',
        selectValue: [],
        paymentDate: '',
    }
    pagination.value.current = 1
    pagination.value.total = 0
    tableData.value = []
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async (approveStatus) => {
    let wageClientListParams: any = []
    clientList?.value?.forEach((el: any) => {
        wageClientListParams.push({
            feeReviewId: el.id,
            clientId: el.clientId,
            clientName: el.clientName,
            title: el.title,
            amount: el.totalAmount,
        })
    })
    let accountType
    // 根据费用类型
    if (prevData?.value?.accountType == 1) {
        accountType = 2
    } else if (prevData?.value?.accountType == 2) {
        accountType = 5
    } else if (prevData?.value?.accountType == 3) {
        accountType = 3
    } else if (prevData?.value?.accountType == 4) {
        accountType = 1
    } else {
        // 其他
        accountType = 4
    }
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const form = {
            applyDate: formData.value.applyDate,
            title: formData.value.title,
            amount: formData.value.amount,
            approveStatus,
            paymentDate: formData.value.paymentDate,
            memo: formData.value.memo ? formData.value.memo : null,
            detailDTOList: tableData.value.map((i) => ({
                ...i,
            })),
            noticeRoles: formData.value.noticeRoles?.join(','),
            enclosures: formData.value.enclosures?.map((i) => ({
                id: i.id,
            })),
            hrBillReimbursementClientDTOS:
                costType?.value == '4' ? wageClientListParams : detailCont?.value?.hrBillReimbursementClientDTOS,
            accountType: accountType,
            // applyUserId: approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
        }

        await request.post(`/api/hr-bill-reimbursement-applies/insert`, form)
        emit('confirm', 1)
        modalClose()
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '费用内容',
        dataIndex: 'invoiceTypeLabel',
        width: 200,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 150,
    },
    {
        title: '操作',
        dataIndex: 'action',
        width: 150,
        slots: { customRender: 'action' },
    },
]
const childColumns = [
    {
        title: '账户',
        dataIndex: 'accountNumber',
        key: 'accountNumber',
        width: 200,
        align: 'center',
    },
    {
        title: '客户名',
        dataIndex: 'clientName',
        key: 'clientName',
        width: 150,
        align: 'center',
    },
    {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        sorter: (a, b) => Number(a.amount) - Number(b.amount),
        width: 150,
    },
]

const wageColumns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        key: 'clientName',
        width: 200,
        sorter: (a, b) => a.clientName.length - b.clientName.length,
    },
    {
        title: '账单标题',
        dataIndex: 'title',
        key: 'title',
        width: 150,
        sorter: (a, b) => a.title.length - b.title.length,
    },
    {
        title: '金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 150,
        sorter: (a, b) => Number(a.totalAmount) - Number(b.totalAmount),
    },
]
const contentList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getReimbursementContentList()) as LabelValueOptions
    roleList.value = (await getReimbursementNoticeRoleList()) as LabelValueOptions
}
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
.myStyle :deep(.ant-table-thead > tr > th) {
    background-color: #6894fe;
    color: #fff;
}
.Ipt {
    width: 25%;
    margin-bottom: 20px;
}
</style>
