<template>
    <BasicEditModalSlot title="新建报销" :visible="visible" @cancel="modalClose" width="800px" centered>
        <div class="viewView"></div>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <FormItem
                label="费用内容"
                name="accountType"
                :rules="{ required: true, message: '请选择费用内容', trigger: 'change' }"
            >
                <RadioGroup v-model:value="formData.accountType" @change="radioChange" name="radioGroup">
                    <Radio :value="t.value" v-for="(t, index) in costContent" :key="index">{{ t.label }}</Radio>
                </RadioGroup>
            </FormItem>
            <div class="viewView"></div>
            <div class="item" v-if="countentFlag">
                <FormItem
                    label="缴纳账户"
                    ref="accountRef"
                    name="selectValue"
                    :rules="{ type: 'array', required: true, message: '请选择缴纳账户', trigger: ['change'] }"
                >
                    <TreeSelect
                        v-model:value="formData.selectValue"
                        style="width: 100%"
                        :tree-data="treeData"
                        tree-checkable
                        allow-clear
                        :show-checked-strategy="SHOW_PARENT"
                        :maxTagCount="2"
                        placeholder="缴纳账户"
                        treeDefaultExpandAll
                        :filterTreeNode="filterTreeNode"
                        @change="accountSelectChange"
                    />
                </FormItem>
                <div class="viewView"></div>
            </div>
            <FormItem
                label="费用年月"
                name="paymentDate"
                :rules="{ required: true, message: '请选择缴费年月', trigger: ['change'] }"
            >
                <MonthPicker
                    v-model:value="formData.paymentDate"
                    placeholder="缴费年月"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                    @change="getBillData"
                />
            </FormItem>
            <div class="viewView"></div>
            <FormItem name="title" label="标题" :rules="{ required: true, message: '请输入标题', trigger: 'change' }">
                <Input v-model:value="formData.title" placeholder="标题" />
            </FormItem>
        </Form>
        <div class="viewView"></div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm()">下一步</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ref, toRefs, watch, computed, reactive, defineExpose } from 'vue'
import { costContent } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { postCostAccount } from '/@/utils/api'
import { message, Modal, Upload } from 'ant-design-vue'
import { plus } from 'number-precision'
import { TreeSelect } from 'ant-design-vue'
import { trigger } from '@vue/reactivity'
interface TreeDataItem {
    value: string
    key: string
    title?: string
    disabled?: boolean
    children?: TreeDataItem[]
}
const formData = ref<Recordable>({
    title: '',
    accountType: '1',
    selectValue: [],
    paymentDate: '',
})

let isAllAccountType = '' //是单个账户还是全部账户
const SHOW_PARENT = TreeSelect.SHOW_PARENT
let countentFlag = ref(true)
let treeData = ref<TreeDataItem[]>([])
const props = defineProps({
    visible: Boolean,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible } = toRefs(props)

watch(visible, () => {
    if (visible.value) {
        getSelectList()
    } else {
        countentFlag.value = true
        resetData()
    }
})
watch(
    () => formData.value.accountType,
    (newVal) => {
        if (newVal == '4') {
            countentFlag.value = false
        } else {
            countentFlag.value = true
        }
    },
    { deep: true },
)

const radioChange = (e) => {
    formData.value.selectValue = []
    getSelectList()
    getBillData()
}
const getSelectList = async () => {
    treeData.value = []
    const res: any = await postCostAccount({ platformType: formData.value.accountType })
    treeData.value = res
}
const accountSelectChange = (value, label, extra) => {
    if (label[0] === '全部' || value[0] == 1) {
        isAllAccountType = '1'
    } else {
        isAllAccountType = '2'
    }
}
const filterTreeNode = (input, node) => {
    return node.props.dataRef.title.includes(input)
}
const getBillData = async () => {
    if (!formData.value.paymentDate) {
        return
    }
    let platformLabel = costContent?.find((i) => i.value == formData.value.accountType)
    formData.value.title =
        (formData.value.paymentDate ? moment(formData.value.paymentDate).format('MM月份') : '') +
        platformLabel?.label +
        `资金报销申请`
}
// 其他组件修改本组件数据
const updataform = (form) => {
    Object.keys(formData.value).forEach((key) => {
        formData.value[key] = form[key]
    })
}
// 主动暴露childMethod方法
defineExpose({ updataform })
const resetData = () => {
    formData.value = {
        title: '',
        accountType: '1',
        selectValue: [],
        paymentDate: '',
    }
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async () => {
    let arr: string[] = []
    if (isAllAccountType == '1') {
        treeData.value[0].children?.forEach((e: any) => {
            arr.push(e.id)
        })
    } else {
        arr = formData.value.selectValue
    }
    let costType = formData.value.accountType
    let accountType
    // 根据费用类型
    if (formData.value.accountType == 1) {
        accountType = 2
    } else if (formData.value.accountType == 2) {
        accountType = 5
    } else if (formData.value.accountType == 3) {
        accountType = 3
    } else if (formData.value.accountType == 4) {
        accountType = 1
    } else {
        // 其他
        accountType = 4
    }

    try {
        await formInline.value.validate()
        confirmLoading.value = true
        let res: any
        if (formData.value.accountType == '4') {
            const params = {
                paymentDate: formData.value.paymentDate,
            }
            res = await request.post(`/api/hr-bill-reimbursement-applies/search-bill`, params)
        } else {
            // 费用内容 1代发工资 2代缴社保 3代缴公积金 4其他 5代缴医保
            const params = {
                accountIds: arr,
                accountType: accountType,
                paymentDate: formData.value.paymentDate,
                paymentAccountType: isAllAccountType,
            }
            res = await request.post(`/api/hr-bill-reimbursement-applies/generate`, params)
        }
        let obj = formData.value
        modalClose()
        emit('confirm', res, costType, obj)
    } finally {
        confirmLoading.value = false
    }
}
</script>

<style scoped lang="less">
.viewView {
    height: 20px;
}
</style>
