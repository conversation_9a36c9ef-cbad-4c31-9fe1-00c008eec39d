<template>
    <BasicEditModalSlot title="客户选择" :visible="visible" @cancel="cancel" width="1000px" centered>
        <div class="searchView">
            <Input type="text" v-model:value="iptValue" placeholder="请输入账户名称" class="Ipt" @change="onChange()" />
            <Button type="primary" size="small" @click="selectClick()" v-show="iptValue">选中</Button>
        </div>
        <Table
            :bordered="true"
            :columns="columns"
            :row-key="(record, index) => record.id ?? index"
            :dataSource="[...tableData]"
            size="small"
            :scroll="{ x: 100 }"
            :pagination="false"
            :rowSelection="rowSelection"
        />
        <!--   -->
        <template #footer>
            <Button type="" @click="cancel()">取消</Button>
            <Button type="primary" @click="modalConfirm()">确认</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch, computed } from 'vue'

let tableData = ref<any>([])
let oldtableData = ref<any>([])

const props = defineProps({
    visible: Boolean,
    dataList: Array,
})
const emit = defineEmits(['cancel', 'confirm'])

const { visible, dataList } = toRefs(props)

watch(visible, () => {
    if (visible.value) {
        resetData()
        tableData.value = JSON.parse(JSON.stringify(dataList?.value))
        oldtableData.value = JSON.parse(JSON.stringify(dataList?.value))
    }
})

const resetData = () => {
    iptValue.value = ''
    selectedStaff.value = []
    selectedItem = []
}
let selectedItem = [] //搜索用选中的数据
const selectedStaff = ref<Recordable[]>([])
const selStaff = (keys, list) => {
    selectedStaff.value = keys
    oldtableData.value.forEach((el) => {
        if (keys.includes(el.id)) {
            el.checkOn = 1
        } else {
            el.checkOn = 0
        }
    })
    tableData.value = oldtableData.value
    selectedItem = list
}
let rowSelection = computed(() => {
    return {
        selectedRowKeys: selectedStaff,
        onChange: selStaff,
        getCheckboxProps: getCheckboxProps,
    }
})
const getCheckboxProps = (record) => {
    return {
        disabled: fun(record), // Column configuration not to be checked
        defaultChecked: record.checkOn == 1,
    }
}
const fun = (record) => {
    let flag = false
    if (
        (record.accumulationFoundLock == 2 || record.accumulationFoundLock == 4) &&
        (record.medicalInsuranceLock == 2 || record.medicalInsuranceLock == 4) &&
        (record.realSalaryLock == 2 || record.realSalaryLock == 4) &&
        (record.socialSecurityLock == 2 || record.socialSecurityLock == 4)
    ) {
        flag = true
    }

    return flag
}
const pagination = ref({
    current: 1,
    pageSize: 5,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '30', '40'],
    onChange: (page, size) => {
        pagination.value.current = page
    },
    onShowSizeChange: (current, size) => {
        pagination.value.pageSize = size
    },
})

const cancel = () => {
    emit('cancel')
}
// 搜索
let iptValue = ref('')
const onChange = async () => {
    tableData.value = []
    oldtableData?.value?.forEach((el: any) => {
        if (el.clientName?.includes(iptValue.value)) {
            tableData.value.push(el)
        }
    })
}

// 搜索选中
const selectClick = () => {
    iptValue.value = ''
    selectedItem.forEach((el: any) => {
        oldtableData.value.forEach((i, index) => {
            if (el.id == i.id) {
                oldtableData.value.splice(index, 1)
            }
        })
    })
    oldtableData.value = [...selectedItem, ...oldtableData.value]
    tableData.value = [...oldtableData.value]
}
// 确认
const modalConfirm = async () => {
    let params: any = []
    tableData.value.forEach((el: any) => {
        if (el.checkOn == 1) {
            params.push(el)
        }
    })

    emit('confirm', params)
}
const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        key: 'clientName',
        align: 'center',
        width: 200,
    },
    {
        title: '代发工资',
        dataIndex: 'realSalaryAmount',
        key: 'realSalaryAmount',
        align: 'center',
        width: 150,
    },
    {
        title: '代缴社保',
        dataIndex: 'socialSecurityAmount',
        key: 'socialSecurityAmount',
        align: 'center',
        width: 150,
    },
    {
        title: '代缴医保',
        dataIndex: 'medicalInsuranceAmount',
        key: 'medicalInsuranceAmount',
        align: 'center',
        width: 150,
    },
    {
        title: '代缴公积金',
        dataIndex: 'accumulationFoundAmount',
        key: 'accumulationFoundAmount',
        align: 'center',
        width: 150,
    },
]
</script>

<style scoped lang="less">
.Ipt {
    width: 25%;
    margin-right: 20px;
}
.searchView {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
</style>
