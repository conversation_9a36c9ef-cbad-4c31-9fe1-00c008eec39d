<template>
    <BasicEditModalSlot title="生成凭证" :visible="visible" @cancel="modalClose" width="1000px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
            <FormItem
                label="业务类型"
                name="reimburseType"
                :rules="{ required: true, type: 'number', message: '请选择业务类型', trigger: ['change', 'blur'] }"
            >
                <Select
                    v-model:value="formData.reimburseType"
                    @change="reimburseTypeChange"
                    :options="businessTypeList"
                    placeholder="业务类型"
                />
            </FormItem>
            <!-- /  @change="(val) => (record.invoiceType = val)" -->
            <FormItem label="制单日期" name="prepareddate" :rules="{ required: true, type: 'string', message: '请选择制单日期' }">
                <DatePicker
                    v-model:value="formData.prepareddate"
                    placeholder="制单日期"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                />
            </FormItem>
            <FormItem label="附件张数" name="attachment" :rules="{ required: true, type: 'number', message: '请输入附件张数' }">
                <InputNumber v-model:value="formData.attachment" placeholder="附件张数" style="width: 100%" />
            </FormItem>
            <FormItem label="会计期间" name="payMonthly" :rules="{ required: true, type: 'string', message: '请选择会计期间' }">
                <Select v-model:value="formData.payMonthly" :options="mothOptions" placeholder="会计期间" />
            </FormItem>

            <FormItem label="银行存款">
                <div class="btns">
                    <Button size="small" type="primary" @click="createRow">新增</Button>
                    <Button size="small" type="primary" danger @click="removeSome">删除</Button>
                </div>
                <BasicTable
                    ref="tableRef"
                    :tableDataList="tableData"
                    :columns="columns"
                    :useIndex="true"
                    :sorter="false"
                    @selectedRowsArr="selHandle"
                    :checkboxProps="getCheckboxProps"
                >
                    <template #explanation="{ record, text }">
                        <Select
                            :value="text"
                            @change="(val) => (record.explanation = val[val.length - 1])"
                            :options="explanationList"
                            placeholder="请选择摘要"
                            style="width: 100%"
                            :getPopupContainer="() => body"
                            :disabled="false"
                            mode="tags"
                            maxTagCount="1"
                        />
                    </template>
                    <template #invoiceType="{ text, record }">
                        <Select
                            :value="text"
                            @change="(val) => (record.bankAccount = val)"
                            :options="bankList"
                            placeholder="请选择银行"
                            style="width: 100%"
                            :getPopupContainer="() => body"
                            :disabled="false"
                        />
                    </template>
                    <template #amount="{ record, text }">
                        <InputNumber :value="text" :min="0" @change="amountChange($event, record)" style="width: 100%" />
                    </template>
                </BasicTable>
            </FormItem>

            <FormItem
                label="税费"
                name="taxAmount"
                v-if="taxShow"
                :rules="{ required: true, type: 'number', message: '请输入税费', trigger: ['change', 'blur'] }"
            >
                <InputNumber
                    v-model:value="formData.taxAmount"
                    :step="1"
                    :precision="2"
                    :min="0"
                    placeholder="税费"
                    style="width: 90%"
                />
                <!--     @change="totalAmountChange($event)" -->
                <span style="margin-left: 5px">元</span>
            </FormItem>

            <FormItem
                label="税率"
                v-if="taxShow"
                name="taxRate"
                :rules="{ required: true, type: 'number', message: '请选择税率', trigger: ['change', 'blur'] }"
            >
                <Select v-model:value="formData.taxRate" :options="taxRateList" placeholder="税率" />
            </FormItem>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm()">确定</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ref, toRefs, watch } from 'vue'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'
import { message, Modal } from 'ant-design-vue'
import { taxRateList, businessTypeList, mothOptions, explanationList } from './data'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord } = toRefs(props)

watch(visible, async () => {
    if (visible.value && currentRecord?.value) {
        await getList()
        updataFormData(currentRecord.value)
    } else {
        resetData()
    }
})
let dd = new Date()
const defaultValue = '37101988141051001067'
const updataFormData = (record) => {
    formData.value.prepareddate = moment().format('YYYY-MM-DD')
    formData.value.reimburseType = useUserStore().userInfo.reimburseType ?? undefined
    let accountNumber = ''
    if (bankList.value.length != 0) {
        const it: any = bankList.value.find((el: any) => el.accountNumber == defaultValue)
        accountNumber = it.accountNumber ?? ''
    }
    tableData.value.push({
        explanation: undefined,
        bankAccount: accountNumber,
        amount: record.amount ?? 0,
        id: dd.getTime(),
    })
}

const bankList = ref([])
const formData = ref<Recordable>({
    attachment: 0,
    payMonthly: String(new Date().getMonth() + 1),
})
const tableData = ref<Recordable[]>([])

// 默认选中的代码
const getCheckboxProps = (record) => {
    // selArr.value.push(record)
    return {
        // disabled: currentRecord != undefined && (record.state == 2 || record.state == 4),
        // defaultChecked: currentRecord != undefined && record.state != 2 && record.state != 4 ? true : false,
    }
}
const amountChange = (val, record) => {
    record.amount = Number(val)
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const tableRef = ref()
const createRow = () => {
    tableData.value.push({
        id: new Date().getTime(),
        explanation: undefined,
        bankAccount: defaultValue,
        amount: 0,
    })
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的费用明细！')
        return
    }
    const ids = selArr.value.map((i) => i.id)
    tableData.value = tableData.value.filter((i) => !ids.includes(i.id))
}
const taxShow = ref(false)
const reimburseTypeChange = (val) => {
    taxShow.value = val == 3 ? true : false
}
const resetData = () => {
    formData.value = {
        // applyDate: moment().format('YYYY-MM-DD'),
        explanation: undefined,
        attachment: 0,
        payMonthly: String(new Date().getMonth() + 1),
    }
    tableData.value = []
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async () => {
    try {
        await formInline.value.validate()
        if (tableData.value.length == 0) {
            Modal.warning({
                title: '提示',
                content: '至少输入一条银行存款',
            })
            return
        }
        let arr1 = tableData.value.filter((el) => !el.explanation)
        if (arr1.length != 0) {
            Modal.warning({
                title: '提示',
                content: '请输入摘要',
            })
            return
        }

        let arr = tableData.value.filter((el) => !el.bankAccount || !el.amount)

        if (arr.length != 0) {
            Modal.warning({
                title: '提示',
                content: '银行账号和金额不能为空',
            })
            return
        }

        confirmLoading.value = true
        const form = {
            id: currentRecord?.value?.id ?? null,
            ...formData.value,
            bankDepositList: tableData.value?.map((el: any) => ({
                bankAccount: el.bankAccount,
                amount: el.amount,
                explanation: el.explanation,
            })),
        }
        await request.post(`/api/hr-bill-reimbursement-applies/create-voucher`, form)
        emit('confirm', 1)
        modalClose()
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '摘要',
        dataIndex: 'explanation',
        width: 150,
        slots: { customRender: 'explanation' },
    },
    {
        title: '银行账号',
        dataIndex: 'bankAccount',
        width: 300,
        slots: { customRender: 'invoiceType' },
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 150,
        slots: { customRender: 'amount' },
    },
]

const body = document.body
const getList = async () => {
    const res = await request.get(`/api/hr-platform-accounts/list/${4}`, {})
    bankList.value = res.map((el) => {
        return {
            ...el,
            label: el.issuingBank + `(${el.accountNumber.slice(-4)})`,
            value: el.accountNumber,
        }
    })
}
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
.btns {
    margin-top: 5px;
}
</style>
