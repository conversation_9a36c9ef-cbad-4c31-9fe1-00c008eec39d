<template>
    <BasicEditModalSlot :title="currentRecord ? '编辑' : '新增'" :visible="visible" @cancel="modalClose" width="800px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <Row>
                <Col span="12">
                    <FormItem label="申请人">
                        <Input :value="formData.applyRealName" disabled placeholder="申请人" />
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="申请日期">
                        <DatePicker
                            v-model:value="formData.applyDate"
                            placeholder="申请日期"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
            </Row>
            <div v-if="!showClientName">
                <Row>
                    <Col span="12">
                        <FormItem label="缴费年月">
                            <MonthPicker
                                v-model:value="formData.paymentDate"
                                placeholder="缴费年月"
                                format="YYYY-MM"
                                valueFormat="YYYY-MM"
                                @change="getBillData"
                                :disabled="true"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="申请金额" name="amount">
                            <!--      :rules="{ required: true, type: 'number', message: '请输入申请金额', trigger: 'change' }" -->
                            <InputNumber
                                v-model:value="totalAmount"
                                @change="totalAmountChange($event)"
                                :min="0"
                                placeholder="申请金额"
                                style="width: 90%"
                                :disabled="true"
                            />
                            <span style="margin-left: 5px">元</span>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem name="title" label="标题" :rules="{ required: true, message: '请输入标题', trigger: 'change' }">
                            <Input v-model:value="formData.title" placeholder="标题" />
                        </FormItem>
                    </Col>
                </Row>
            </div>
            <div v-if="showClientName">
                <Row>
                    <Col span="12">
                        <FormItem label="客户名称" name="clientId">
                            <!--    :rules="{ required: true, message: '请选择客户名称', trigger: 'change' }" -->
                            <ClientSelectTree
                                v-model:value="formData.clientId"
                                :itemForm="{ placeholder: '客户名称' }"
                                @labelChange="labelChange"
                                :disabled="true"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="缴费年月">
                            <MonthPicker
                                v-model:value="formData.paymentDate"
                                placeholder="缴费年月"
                                format="YYYY-MM"
                                valueFormat="YYYY-MM"
                                @change="getBillData"
                                :disabled="true"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        <FormItem label="申请金额" name="amount">
                            <!--      :rules="{ required: true, type: 'number', message: '请输入申请金额', trigger: 'change' }" -->
                            <InputNumber
                                v-model:value="totalAmount"
                                @change="totalAmountChange($event)"
                                :min="0"
                                placeholder="申请金额"
                                style="width: 90%"
                                :disabled="true"
                            />
                            <span style="margin-left: 5px">元</span>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem name="title" label="标题" :rules="{ required: true, message: '请输入标题', trigger: 'change' }">
                            <Input v-model:value="formData.title" placeholder="标题" />
                        </FormItem>
                    </Col>
                </Row>
            </div>
            <FormItem label="费用明细" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <BasicTable
                    ref="tableRef"
                    :tableDataList="tableData"
                    :columns="columns"
                    :sorter="false"
                    :useIndex="true"
                    :rowSelectionShow="false"
                    @selectedRowsArr="selHandle"
                    :checkboxProps="getCheckboxProps"
                >
                    <template #amount="{ record, text }">
                        <InputNumber
                            :value="text"
                            :min="0"
                            @change="amountChange($event, record)"
                            placeholder="金额"
                            style="width: 100%"
                        />
                    </template>
                    <template #invoiceType="{ record, text }">
                        <Select
                            :value="text"
                            :options="contentList"
                            @change="(val) => (record.invoiceType = val)"
                            placeholder="费用内容"
                            :getPopupContainer="() => body"
                        />
                    </template>
                </BasicTable>
            </FormItem>
            <FormItem label="附件" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                    <Button type="primary" size="small">上传文件</Button>
                </Upload>
                <div class="files">
                    <span class="item" v-for="(i, idx) in formData.enclosures" :key="i.id">
                        <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                        <span @click="removeFile(idx)">x</span>
                    </span>
                </div>
            </FormItem>
            <FormItem label="其它说明" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <Textarea :rows="3" v-model:value="formData.memo" />
            </FormItem>
            <FormItem label="审核结果通知" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <CheckboxGroup v-model:value="formData.noticeRoles" :options="roleList" />
            </FormItem>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(0)">暂存</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">发送</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ref, toRefs, watch, computed } from 'vue'
import useUserStore from '/@/store/modules/user'
import request from '/@/utils/request'
import { message, Modal, Upload } from 'ant-design-vue'
import { getReimbursementContentList, getReimbursementNoticeRoleList } from '/@/utils/api'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
import { plus } from 'number-precision'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord } = toRefs(props)

const showClientName = ref(false)

watch(visible, () => {
    if (visible.value && currentRecord?.value) {
        getData()
        if (
            visible.value &&
            (currentRecord?.value?.accountType == 1 ||
                currentRecord?.value?.accountType == 2 ||
                currentRecord?.value?.accountType == 3 ||
                currentRecord?.value?.accountType == 5)
        ) {
            showClientName.value = false
        } else {
            showClientName.value = true
        }
    } else {
        selArr.value = []
    }
})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-reimbursement-applies/${currentRecord?.value?.id}`)
    formData.value = {
        ...res,
        noticeRoles: res.noticeRoles?.split(',') || [],
        applyRealName: useUserStore().getUserInfo?.realName,
    }
    tableData.value = res.detailDTOList || []
}
const formData = ref<Recordable>({
    applyRealName: useUserStore().getUserInfo?.realName,
    applyDate: moment().format('YYYY-MM-DD'),
    noticeRoles: [],
    enclosures: [],
})
const tableData = ref<Recordable[]>([])
let totalAmount = computed(() => {
    // let arr = selArr.value.map((i) => i.amount ?? 0)
    let arr = tableData.value.map((i) => i.amount ?? 0)
    return arr.reduce((pre, cur) => plus(pre, cur), 0)
})

// 默认选中的代码
const getCheckboxProps = (record) => {
    selArr.value.push(record)
    return {
        // disabled: currentRecord != undefined && (record.state == 2 || record.state == 4),
        defaultChecked: currentRecord != undefined && record.state != 2 && record.state != 4 ? true : false,
        id: record.id + '',
    }
}

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.enclosures.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.enclosures.splice(idx, 1)
}

const labelChange = (label) => {
    formData.value.clientName = label?.join('') || ''
    getBillData()
}

const getBillData = async () => {
    if (!formData.value.clientId || !formData.value.paymentDate) {
        return
    }
    formData.value.title =
        (formData.value.clientName || '') +
        (formData.value.paymentDate ? moment(formData.value.paymentDate).format('MM月份') : '') +
        `资金发放申请`
    const res = await request.post(`/api/hr-bill-reimbursement-applies/getBillTotalInfo`, {
        clientId: formData.value.clientId,
        paymentDate: formData.value.paymentDate,
    })
    if (res.amount && res.detailDTOList) {
        Modal.confirm({
            title: '检测到相关费用明细，是否导入？',
            onOk: () => {
                formData.value.amount = res.amount ?? 0
                tableData.value = [
                    ...tableData.value,
                    ...res.detailDTOList?.map((i, idx) => ({
                        ...i,
                        id: new Date().getTime() + idx,
                    })),
                ]
            },
        })
    }
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const tableRef = ref()
const createRow = () => {
    tableData.value.push({
        id: new Date().getTime(),
        amount: 0,
        invoiceType: undefined,
    })
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的费用明细！')
        return
    }
    const ids = selArr.value.map((i) => i.id)
    tableData.value = tableData.value.filter((i) => !ids.includes(i.id))
    tableRef.value.checkboxReset()
    formData.value.amount = amountRecalculate()
}

const totalAmountChange = (val) => {
    if (val > amountRecalculate()) formData.value.amount = amountRecalculate()
    else formData.value.amount = Number(val)
}

const amountRecalculate = () => {
    return tableData.value.reduce((pre, cur) => {
        return plus(pre, Number(cur.amount) || 0)
    }, 0)
}

const resetData = () => {
    formData.value = {
        applyRealName: useUserStore().getUserInfo?.realName,
        applyDate: moment().format('YYYY-MM-DD'),
        noticeRoles: [],
        enclosures: [],
    }
    tableData.value = []
}

const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const confirmLoading = ref(false)
const formInline = ref()
const modalConfirm = async (approveStatus) => {
    tableData.value?.forEach((el) => {
        if (el.checkOn == null) {
            el.checkOn = 0
        }
        selArr.value.forEach((it) => {
            if (el.id == it.id) {
                el.checkOn = 1
                it.checkOn = 1
            }
        })
    })
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const form = {
            ...formData.value,
            approveStatus,
            detailDTOList: tableData.value.map((i) => ({
                ...i,
            })),
            noticeRoles: formData.value.noticeRoles?.join(','),
            enclosures: formData.value.enclosures?.map((i) => ({
                id: i.id,
            })),
            applyUserId: approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
            amount: totalAmount.value,
        }
        if (currentRecord?.value) {
            await request.put(`/api/hr-bill-reimbursement-applies`, form)
        } else {
            await request.post(`/api/hr-bill-reimbursement-applies`, form)
        }
        emit('confirm', 1)
        modalClose()
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '费用内容',
        dataIndex: 'invoiceTypeName',
        width: 200,
        // slots: { customRender: 'invoiceType' },
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 150,
        // slots: { customRender: 'amount' },
    },
]

const amountChange = (val, record) => {
    record.amount = Number(val)
    formData.value.amount = amountRecalculate()
}
const body = document.body

const contentList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getReimbursementContentList()) as LabelValueOptions
    roleList.value = (await getReimbursementNoticeRoleList()) as LabelValueOptions
}
getList()
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
