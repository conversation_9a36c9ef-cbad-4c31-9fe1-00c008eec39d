<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="1100px" centered>
        <Steps :current="formData.approveStatus" size="small" labelPlacement="vertical">
            <Step title="发起申请" />
            <Step title="客服经理审核" />
            <Step title="会计确认" />
            <Step title="已结束" />
        </Steps>
        <div class="cell" style="margin-top: 20px">
            <div class="title">申请信息</div>
            <div class="main">
                <Row>
                    <Col span="12" class="col">
                        <div class="tit">标题</div>
                        <div class="val ellipsis">{{ formData.title }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请人</div>
                        <div class="val">{{ formData.applyRealName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请日期</div>
                        <div class="val">{{ formData.applyDate }}</div>
                    </Col>
                </Row>
                <Row v-if="showClientName">
                    <Col span="12">
                        <div class="tit">客户名称</div>
                        <div class="val">{{ formData.clientName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">缴费年月</div>
                        <div class="val">{{ formData.paymentDate }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请金额</div>
                        <div class="val">{{ formData.amount }}</div>
                    </Col>
                </Row>
                <Row v-if="!showClientName">
                    <Col span="12">
                        <div class="tit">缴费年月</div>
                        <div class="val">{{ formData.paymentDate }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请金额</div>
                        <div class="val">{{ formData.amount }}</div>
                    </Col>
                </Row>
                <BasicTable
                    :tableDataList="formData.detailDTOList || []"
                    :columns="showClientName ? columns : columns2"
                    :sorter="false"
                    :rowSelectionShow="false"
                    :useIndex="true"
                >
                    <template #operation="{ record, index }">
                        <div>
                            <Button type="primary" size="small" @click="lookDetail(record, index)">查看详情</Button>
                        </div>
                    </template>
                </BasicTable>
                <Row class="row">
                    <div class="tit">附件</div>
                    <div class="val">
                        <a
                            v-for="i in formData.enclosures"
                            :key="i.id"
                            @click="previewFile(i.fileUrl)"
                            style="margin-right: 10px"
                        >
                            {{ i.originName }}
                        </a>
                    </div>
                </Row>
                <Row>
                    <div class="tit">其它说明</div>
                    <div class="val">{{ formData.memo }}</div>
                </Row>
                <Row>
                    <div class="tit">审核结果通知</div>
                    <div class="val">{{ formData.noticeRolesName }}</div>
                </Row>
                <Row>
                    <div class="tit">NC凭证号</div>
                    <div class="val">{{ formData.ncVoucher }}</div>
                </Row>
            </div>
        </div>
        <div class="cell">
            <div class="title">审核流程</div>
            <div class="main">
                <div class="logItem" v-for="(i, idx) in formData.applyOpLogsDTOS || []" :key="i.id">
                    <div class="name ellipsis">{{ idx + 1 }}、 操作人：（{{ i.roleName }}) {{ i.realName }}</div>
                    <div class="date ellipsis">操作时间：{{ i.createdDate }}</div>
                    <div class="msg ellipsis">操作信息：{{ i.remark ? i.message + '（' + i.remark + '）' : i.message }}</div>
                </div>
            </div>
        </div>
        <div class="cell" v-if="title == '审核'">
            <div class="title">
                {{ reimbursementApproveStatusList.find((i) => i.value === formData.approveStatus)?.label }}
            </div>
            <div class="main">
                <Textarea :rows="3" v-model:value="formData.rejectMsg" placeholder="若拒绝，请填写拒绝原因" />
            </div>
        </div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <!-- 审核 -->
            <template v-if="title == '审核'">
                <Button :loading="confirmLoading" type="primary" danger @click="modalConfirm(2)">拒绝</Button>
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(1)">通过</Button>
            </template>
            <!-- 确认 -->
            <template v-if="title == '确认'">
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm(3)">确认</Button>
            </template>
        </template>
        <BasicEditModalSlot title="查看明细" :visible="childVisible" @cancel="childCancel" width="1000px" centered>
            <Input
                type="text"
                v-model:value="iptValue"
                @change="IptChange"
                :placeholder="isWage ? '请输入客户名称' : '请输入账户'"
                class="Ipt"
            />
            <Table
                class="myStyle"
                :columns="currentRecord?.accountType == 1 ? wageColumns : childColumns"
                :row-key="(record, index) => record.id ?? index"
                :data-source="tableData"
                @change="tableChange"
                :pagination="isPage ? pagination : false"
                bordered
            />
            <template #footer>
                <Button :loading="confirmLoading" type="primary" @click="childCancel">关闭</Button>
            </template>
        </BasicEditModalSlot>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { message, Step, Steps } from 'ant-design-vue'
import { reimbursementApproveStatusList } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils/index'

const props = defineProps({
    visible: Boolean,
    currentRecord: {
        type: Object,
    },
    title: String,
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord, title } = toRefs(props)

const formData = ref<Recordable>({
    approveStatus: 0,
})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-reimbursement-applies/${currentRecord?.value?.id}`)
    formData.value = { ...res, approveStatus: [0, 1, 4, 4, 4, 4, 4, 2]?.[res.approveStatus] ?? res.approveStatus }
}
const showClientName = ref(true)
watch(visible, () => {
    visible.value && currentRecord?.value?.id && getData()
    // accountType = 1代发工资 2代缴社保 3代缴公积金 5代缴医保
    console.log('1111:' + JSON.stringify(currentRecord))
    if (
        visible.value &&
        (currentRecord?.value?.accountType == 1 ||
            currentRecord?.value?.accountType == 2 ||
            currentRecord?.value?.accountType == 3 ||
            currentRecord?.value?.accountType == 5)
    ) {
        showClientName.value = false
        if (currentRecord?.value?.accountType == 1) {
            isWage.value = true
        } else {
            isWage.value = false
        }
    } else {
        showClientName.value = true
    }
})

const modalClose = () => {
    emit('update:visible', false)
    formData.value = {}
}
const confirmLoading = ref(false)
const modalConfirm = async (auditType) => {
    // 审核类型 1 通过, 2 拒绝, 3 确认
    if (auditType === 2 && !formData.value.rejectMsg) {
        message.warn('请填写拒绝原因！')
        return
    }
    try {
        confirmLoading.value = true
        await request.put(`/api/hr-bill-reimbursement-applies/audit`, {
            ids: [formData.value.id],
            auditType,
            rejectMsg: formData.value.rejectMsg,
        })
        modalClose()
        emit('confirm', 1)
    } finally {
        confirmLoading.value = false
    }
}
/**
 * @查看明细 特殊报销记录查看和编辑不展示客户名称，accountType = 1代发工资 2代缴社保 3代缴公积金 5代缴医保
 * */
const tableData = ref<any[]>([])
const pagination = ref({
    current: 1,
    pageSize: 10,
    showTotal: (total) => `共 ${total} 条`,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30'],
    onChange: (page, size) => {
        pagination.value.current = page
    },
    onShowSizeChange: (current, size) => {
        pagination.value.pageSize = size
    },
})
const isWage = ref(false)
const childCancel = async () => {
    childVisible.value = false
}
const iptValue = ref('')
const tableChange = () => {}
const childVisible = ref(false)
const isPage = ref(false)

const IptChange = () => {
    if (!iptValue.value) {
        tableData.value = formData.value.hrBillReimbursementClientDTOList
        return
    }
    if (isWage.value) {
        tableData.value = formData.value.hrBillReimbursementClientDTOList.filter((item) => {
            return item.clientName.includes(iptValue.value)
        })
    } else {
        tableData.value = formData.value.hrBillReimbursementClientDTOList.filter((item) => {
            return item.accountNumber.includes(iptValue.value)
        })
    }

    pagination.value.current = 1
}
const lookDetail = (record, index) => {
    // console.log('record,index==>', formData.value, index)
    if (isWage.value) {
        tableData.value = formData.value.hrBillReimbursementClientDTOList
    } else {
        tableData.value = formData.value.hrBillReimbursementClientDTOS
    }
    childVisible.value = true
}

const columns = ref([
    {
        title: '内容',
        dataIndex: 'invoiceTypeName',
        width: 120,
        ellipsis: true,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
    },
])
const columns2 = ref([
    {
        title: '内容',
        dataIndex: 'invoiceTypeName',
        width: 120,
        ellipsis: true,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 120,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 150,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
])
const wageColumns = ref([
    {
        title: '客户名称',
        dataIndex: 'clientName',
        key: 'clientName',
        width: 200,
        sorter: (a, b) => a.clientName.length - b.clientName.length,
    },
    {
        title: '账单标题',
        dataIndex: 'billTitle',
        key: 'billTitle',
        width: 150,
        sorter: (a, b) => a.title.length - b.title.length,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        width: 150,
        sorter: (a, b) => Number(a.totalAmount) - Number(b.totalAmount),
    },
])
const childColumns = ref([
    {
        title: '账户',
        dataIndex: 'accountNumber',
        key: 'accountNumber',
        width: 150,
        align: 'center',
    },
    {
        title: '客户名称',
        dataIndex: 'clientName',
        key: 'clientName',
        width: 200,
        align: 'center',
    },
    {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        sorter: (a, b) => Number(a.amount) - Number(b.amount),
        width: 150,
    },
])
</script>

<style scoped lang="less">
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .main {
        padding: 10px;
        .col {
            display: flex;
            .val {
                width: 80%;
            }
        }

        .tit {
            display: inline-block;
            margin: 10px 0;
            color: #999999;
            &::after {
                content: '：';
            }
        }
        .val {
            display: inline-block;
            margin: 10px 0;
        }
    }
}
.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: nowrap;
    .val {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
    }
}

.logItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    color: #666;
    .name {
        width: 30%;
    }
    .date {
        width: 24%;
    }
    .msg {
        width: 35%;
    }
}
.myStyle :deep(.ant-table-thead > tr > th) {
    background-color: #6894fe;
    color: #fff;
}
.Ipt {
    width: 25%;
    margin-bottom: 20px;
}
</style>
