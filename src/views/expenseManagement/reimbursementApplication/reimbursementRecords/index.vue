<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData()" />
    <div class="btns">
        <!-- <Button type="primary" @click="createRecord">新建</Button> -->
        <Button type="primary" @click="batchUrge" class="warning-btn" v-auth="'reimbursementApplication_press'">
            批量催办
        </Button>
        <Button type="primary" @click="batchCheck(1)" class="success-btn" v-auth="'reimbursementApplication_pass'">
            批量通过
        </Button>
        <Button type="primary" @click="batchCheck(2, false, 'reject')" danger v-auth="'reimbursementApplication_down'">
            批量拒绝
        </Button>
        <Button type="primary" @click="batchDownload" v-auth="'reimbursementApplication_download'"> 下载报销单 </Button>
        <Button type="primary" @click="someRemove()" danger v-auth="'reimbursementApplication_delete'"> 批量删除 </Button>
        <Button type="primary" @click="batchConfirm()" v-auth="'reimbursementApplication_confirm'"> 批量确认 </Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-reimbursement-applies/page"
        :params="{ ...params, reimbursementState: 1 }"
        :columns="columns"
        @selectedRowsArr="
            (arr) => {
                selectedRowsArr = arr
            }
        "
        :tableDataFormat="tableDataFormat"
    >
        <template #operation="{ record }">
            <Button v-if="getEditBtnVisible(record)" type="primary" size="small" @click="editRecord(record)">编辑</Button>
            <Button v-else-if="canCheck(record)" type="primary" size="small" @click="showRecord(record, '审核')">审核</Button>
            <Button v-else-if="canConfirm(record)" type="primary" size="small" @click="showRecord(record, '确认')">确认</Button>
            <Button v-else type="primary" size="small" @click="showRecord(record, '查看')">查看</Button>
            &nbsp;
            <Button
                v-auth="'reimbursementApplication_create'"
                v-if="canUrge(record)"
                class="warning-btn"
                size="small"
                @click="urgeRecord(record)"
            >
                催办
            </Button>
            &nbsp;
            <Button
                v-if="record.approveStatus == 8 && record.accountingVoucherStatus != 1"
                v-auth="'reimbursementApplication_evidece'"
                class="success-btn"
                size="small"
                type="primary"
                @click="createEvidence(record)"
                >生成凭证</Button
            >
            <Button
                v-if="record.accountingVoucherStatus == 1"
                v-auth="'reimbursementApplication_evidece'"
                class="warning-btn"
                size="small"
                @click="evideceFun(record)"
                >凭证作废</Button
            >
            &nbsp;
            <Button type="primary" size="small" @click="itemDownload(record)">下载</Button>
        </template>
    </BasicTable>
    <!--  -->
    <!-- 凭证 -->
    <evidenceModal v-model:visible="evideceShow" :currentRecord="currentRecord" @confirm="searchData" />
    <!-- 新增 -->
    <CreateModal v-model:visible="showCreate" :currentRecord="currentRecord" @confirm="searchData" />
    <!-- 催办 -->
    <BasicEditModalSlot :visible="showUrge" title="催办" okText="发送" @cancel="urgeClose" @confirm="urgeConfirm">
        <Form ref="formInline" :model="formData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <FormItem
                v-if="!isBatch"
                label="审批人"
                name="urgeUserId"
                :rules="{ required: true, message: '请选择审批人', trigger: 'change' }"
            >
                <Select v-model:value="formData.urgeUserId" :options="urgeUserList" placeholder="审批人" />
            </FormItem>
            <FormItem label="通知方式">
                <Checkbox v-model:checked="formData.sendWxMsg" disabled>小程序</Checkbox>
                <Checkbox v-model:checked="formData.sendPcMsg" disabled>站内通知</Checkbox>
            </FormItem>
            <FormItem label="提醒内容">
                <Textarea :rows="3" v-model:value="formData.content" placeholder="提醒内容" />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
    <!-- 审核 -->
    <CheckModal v-model:visible="showCheck" :title="modalTitle" :currentRecord="currentRecord" @confirm="searchData" />
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="cancelHandle"
        @confirm="batchCheck(2, true, 'reject')"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { SearchBarOption } from '/#/component'
import CreateModal from './CreateModal.vue'
import CheckModal from './CheckModal.vue'
import { reimbursementApproveStatusList, accountingVoucherStatusList } from '/@/utils/dictionaries'
import useUserStore from '/@/store/modules/user'
import { getUrgeUserList } from '/@/utils/api'
import request from '/@/utils/request'
import { message, notification, Modal } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import downFile from '/@/utils/downFile'
import evidenceModal from './evidenceModal.vue'
const route = useRoute()
const params = ref<inObject>({
    approveStatusList: route.query?.approveStatusList ? JSON.parse(route.query?.approveStatusList as string) : undefined,
})
const tableRef = ref()
const checkerReason = ref('')

const searchData = (isCur?) => {
    if (isCur) tableRef.value.refresh()
    else tableRef.value.refresh(1)
}

const showCreate = ref(false)

const showCheck = ref(false)
const showReject = ref(false)
const currentRecord = ref<Recordable | undefined>(undefined)
const editRecord = (record) => {
    currentRecord.value = { ...record }
    showCreate.value = true
}
const roles = useUserStore().getUserInfo.roles?.map((i) => i.roleKey)
const canCheck = (record) => {
    let flag = false
    switch (record.approveStatus) {
        // 0 未发起, 1 待客服经理审批, 2 待总经理审批, 3 待财务负责人审批, 4 待总裁审批, 5 待监事会主席审批, 6 待董事长审批, 7 待会计确认, 8 同过, 9 拒绝
        case 1:
            if (roles?.includes('customer_service_manager')) {
                flag = true
            }
            break
        case 2:
            if (roles?.includes('total_manager')) {
                flag = true
            }
            break
        case 3:
            if (roles?.includes('financial_director')) {
                flag = true
            }
            break
        case 4:
            if (roles?.includes('ceo')) {
                flag = true
            }
            break
        case 5:
            if (roles?.includes('supervisory_board_chairman')) {
                flag = true
            }
            break
        case 6:
            if (roles?.includes('chairman')) {
                flag = true
            }
            break
        default:
            break
    }
    return flag
}
const canConfirm = (record) => {
    // 会计确认
    return record.approveStatus == 7 && roles?.includes('accounting')
}
const canUrge = (record) => {
    return record.approveStatus && record.approveStatus < 8
}

const tableDataFormat = (record) => {
    return record
}

const showUrge = ref(false)
const formData = ref({
    realName: undefined,
    urgeUserId: undefined,
    sendWxMsg: false,
    sendPcMsg: true,
    content: undefined,
})
const modalTitle = ref('查看')
const showRecord = (record, title) => {
    modalTitle.value = title
    currentRecord.value = { ...record }
    showCheck.value = true
}

// 生成凭证
const evideceShow = ref(false)
const createEvidence = (record) => {
    currentRecord.value = { ...record }
    evideceShow.value = true
}
// 作废凭证
const evideceFun = (record) => {
    //作废凭证
    Modal.confirm({
        title: '确认',
        content: '是否确定作废凭证',
        onOk: async () => {
            await request.get(`/api/hr-bill-reimbursement-applies/delete-voucher?id=${record.id}`, {})
            searchData()
        },
    })
}

const selectedRowsArr = ref<Recordable[]>([])
const urgeRecord = (record) => {
    currentRecord.value = { ...record }
    getList(record.id)
    showUrge.value = true
    isBatch.value = false
}
const isBatch = ref(false)
const batchUrge = () => {
    if (!selectedRowsArr.value.length) {
        message.error('请选择要催办的报销单')
        return
    } else {
        if (
            !selectedRowsArr.value.every((ele) => {
                return canUrge(ele)
            })
        ) {
            notification.warning({
                message: `${selectedRowsArr.value
                    .filter((el: inObject) => {
                        return !canUrge(el)
                    })
                    .map((el: inObject) => el.title)
                    .join()} 不在审核流程中,无法催办,请重新选择`,
            })
        } else {
            showUrge.value = true
            isBatch.value = true
        }
    }
}
const urgeClose = () => {
    showUrge.value = false
    isBatch.value = false
    formData.value = {
        realName: undefined,
        urgeUserId: undefined,
        sendWxMsg: false,
        sendPcMsg: true,
        content: undefined,
    }
}
const urgeConfirm = async () => {
    if (isBatch.value) {
        if (!formData.value.content) {
            message.warn('请填写提醒内容')
            return
        } else {
            try {
                const res = await request.post(`/api/hr-bill-reimbursement-applies/batchApplyUrge`, {
                    applyIdList: selectedRowsArr.value.map((el) => {
                        return el.id
                    }),
                    ...formData.value,
                })
                if (res.checkCode == 200) message.success(res?.checkMsg)
                if (res.checkCode == 500) message.error(res?.checkMsg)
            } catch (err: any) {
                message.error(err)
            }
        }
    } else {
        await request.post(`/api/hr-bill-reimbursement-applies/reimbursementApplyUrge`, {
            applyId: currentRecord?.value?.id,
            ...formData.value,
        })
        message.success('催办成功!')
    }
    urgeClose()
    searchData(1)
}

const batchDownload = async () => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        const url = await request.post(
            `/api/hr-bill-reimbursement-applies/download`,
            selectedRowsArr.value.map((el) => el.id),
        )
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const itemDownload = async (record) => {
    try {
        const url = await request.post(`/api/hr-bill-reimbursement-applies/download`, [record?.id])
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const cancelHandle = () => {
    showReject.value = false
    checkerReason.value = ''
}

const someRemove = async () => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }

    try {
        await request.post(
            `/api/hr-bill-reimbursement-applies/deletes`,
            selectedRowsArr.value.map((el) => el.id),
        )
        searchData()
        cancelHandle()
    } catch (error) {
        console.log(error)
    }
}
// 批量通过
const batchConfirm = async () => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        await request.put(`api/hr-bill-reimbursement-applies/audit`, {
            ids: selectedRowsArr.value.map((el) => el.id),
            auditType: 3,
        })
        searchData()
        cancelHandle()
    } catch (error) {
        console.log(error)
    }
}

const batchCheck = async (auditType, reject = false, btnType?) => {
    if (!selectedRowsArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    console.log(btnType == 'reject')
    if (!auditType && !reject) {
        showReject.value = true
    } else if (auditType && !reject && btnType == 'reject') {
        showReject.value = true
    } else {
        if (reject && !auditType && !checkerReason.value) {
            message.warn('请填写拒绝原因！')
            return
        }
        try {
            await request.put(`/api/hr-bill-reimbursement-applies/audit`, {
                ids: selectedRowsArr.value.map((el) => el.id),
                auditType, // 2 拒绝 1 通过
                rejectMsg: checkerReason.value,
            })
            searchData()
            cancelHandle()
        } catch (error) {
            console.log(error)
        }
    }
}

const columns = [
    {
        title: '标题',
        dataIndex: 'title',
        width: 110,
    },
    {
        title: '申请人',
        dataIndex: 'applyRealName',
        width: 110,
    },
    {
        title: '申请金额',
        dataIndex: 'amount',
        width: 110,
    },
    {
        title: '申请日期',
        dataIndex: 'applyDate',
        width: 110,
    },
    {
        title: '审批进度',
        dataIndex: 'approveStatus',
        width: 110,
        customRender: ({ text }) => {
            return reimbursementApproveStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '凭证状态',
        dataIndex: 'accountingVoucherStatus',
        width: 110,
        customRender: ({ text }) => {
            return accountingVoucherStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 200,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '标题',
        key: 'title',
    },
    {
        label: '申请人',
        key: 'applyRealName',
    },
    {
        label: '申请日期',
        key: 'applyDate',
        type: 'date',
    },
    {
        label: '申请进度',
        key: 'approveStatusList',
        type: 'select',
        multiple: true,
        options: reimbursementApproveStatusList,
    },
    {
        type: 'numberrange',
        label: '申请金额',
        key: 'amountQuery',
        canNegative: true,
    },
]
const urgeUserList = ref<LabelValueOptions>([])
const getList = async (applyId) => {
    urgeUserList.value = (await getUrgeUserList(applyId)) as LabelValueOptions
}
const getEditBtnVisible = (record) => {
    if ((record.approveStatus == 0 || record.approveStatus == 9) && record.applyRealName == useUserStore().getUserInfo.realName)
        return true

    return false
}
</script>

<style scoped lang="less"></style>
