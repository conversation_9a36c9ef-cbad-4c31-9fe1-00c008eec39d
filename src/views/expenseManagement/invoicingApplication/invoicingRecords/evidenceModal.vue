<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="600px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" :rules="rules">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]" />
            </template>
        </Form>
        <div class="viewView"></div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="modalConfirm()">确认</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { valuesAndRules } from '/#/component'
import { ref, toRefs, watch, computed, reactive, defineExpose } from 'vue'
import { costContent } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { SelectTypes } from 'ant-design-vue/es/select'
import { getValuesAndRules } from '/@/utils/index'
import { message } from 'ant-design-vue'

const confirmLoading = ref(false)
const formInline = ref()

const props = defineProps({
    visible: Boolean,
    title: String,
    currentRecord: Object,
})
const { visible, title, currentRecord } = toRefs(props)
const emit = defineEmits(['update:visible', 'confirm'])

watch(visible, () => {
    if (visible.value) {
        defFormData(currentRecord?.value)
    }
})

const mothOptions = [
    {
        label: '一月',
        value: '1',
    },
    {
        label: '二月',
        value: '2',
    },
    {
        label: '三月',
        value: '3',
    },
    {
        label: '四月',
        value: '4',
    },
    {
        label: '五月',
        value: '5',
    },
    {
        label: '六月',
        value: '6',
    },
    {
        label: '七月',
        value: '7',
    },
    {
        label: '八月',
        value: '8',
    },
    {
        label: '九月',
        value: '9',
    },
    {
        label: '十月',
        value: '10',
    },
    {
        label: '十一月',
        value: '11',
    },
    {
        label: '十二月',
        value: '12',
    },
]
const radioOptions = [
    {
        label: '有',
        value: 1,
    },
    {
        label: '无',
        value: 0,
    },
]

const defFormData = (item) => {
    formData.value.prepareddate = item.applyDate
    formData.value.payMonthly = String(new Date().getMonth() + 1)
    formData.value.id = item.id
}

const myOptions = ref<valuesAndRules[]>([
    {
        label: '制单日期',
        name: 'prepareddate',
        type: 'date',
        required: true,
    },
    {
        label: '附件张数',
        name: 'attachment',
        type: 'number',
        required: true,
        default: 1,
        ruleType: 'number',
        min: 0,
        onChange: (value) => {
            console.log(value)
            if (value < 0) {
                message.error('不能输入负数')
                value = 0
                return
            }
        },
    },
    {
        label: '会计期间',
        name: 'payMonthly',
        type: 'change',
        required: true,
        options: mothOptions,
    },
    {
        label: '是否已回款',
        name: 'isRepayment',
        type: 'change',
        required: true,
        ruleType: 'number',
        options: radioOptions,
        default: 1,
    },
])

// FormData rules 初始值
const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

const formData = ref<Recordable>(initFormData)

// reset formData
const resetFormData = () => {
    formData.value = initFormData
    formInline.value.resetFields()
}
const modalClose = () => {
    resetFormData()
    emit('update:visible', false)
}

const modalConfirm = async () => {
    try {
        await formInline.value.validate()
        confirmLoading.value = true
        const params = {
            ...formData.value,
        }
        let res = await request.post(`/api/hr-bill-invoices/create-voucher`, params)
        modalClose()
        emit('confirm')
    } finally {
        confirmLoading.value = false
    }
}
</script>

<style scoped lang="less">
.viewView {
    height: 20px;
}
</style>
