<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData()" />
    <div class="btns">
        <!-- <Button type="primary" @click="createRecord">新建</Button> -->
        <Button type="primary" @click="batchCheck(1)" class="success-btn" v-auth="'invoicingRecords_batchAudit'">
            批量通过
        </Button>
        <Button type="primary" @click="batchCheck(0)" danger v-auth="'invoicingRecords_batchAudit'"> 批量拒绝 </Button>
        <Button type="primary" @click="batchDownload" v-auth="'invoicingRecords_createAndRemove'">下载开票申请单</Button>
        <Button type="primary" danger @click="removeSome">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-invoices/page"
        :params="{ ...params, invoiceState: 1 }"
        :columns="columns"
        @selectedRowsArr="selHandle"
    >
        <template #operation="{ record }">
            <Button v-if="getEditBtnVisible(record)" type="primary" size="small" @click="editRecord(record)">编辑 </Button>
            <template v-else>
                <Button v-if="canCheck(record)" type="primary" size="small" @click="showRecord(record, '审核')">审核</Button>
                <Button v-else-if="canConfirm(record)" type="primary" size="small" @click="showRecord(record, '确认')">
                    确认
                </Button>
                <Button v-else type="primary" size="small" @click="showRecord(record, '查看')">查看</Button>
            </template>
            <template v-if="canInvoice(record)">
                &nbsp;
                <Button v-if="canInvoice(record)" class="success-btn" size="small" @click="invoicingRecord(record)"> 开票</Button>
            </template>
            <template
                v-if="(record.invoiceStatus == 1 && record.accountingVoucherStatus != 1) || record.accountingVoucherStatus == 1"
            >
                &nbsp;
                <Button
                    v-if="record.invoiceStatus == 1 && record.accountingVoucherStatus != 1"
                    class="success-btn"
                    v-auth="'invoicingRecords_evidence'"
                    size="small"
                    @click="createEvidence(record)"
                    >生成凭证</Button
                >
                <Button
                    v-if="record.accountingVoucherStatus == 1"
                    v-auth="'invoicingRecords_evidence'"
                    class="warning-btn"
                    size="small"
                    @click="delEvidence(record)"
                    >凭证作废</Button
                >
            </template>
            &nbsp;
            <Button type="primary" size="small" @click="itemDownload(record)"> 下载</Button>
        </template>
    </BasicTable>
    <CreateModal
        v-model:visible="showCreate"
        viewType="invoicingRecordsEdit"
        :currentRecord="currentRecord"
        @confirm="searchData"
    />
    <evidenceModal2 v-model:visible="evideceShow" title="生成凭证" :currentRecord="currentRecord" @confirm="searchData" />
    <CheckModal v-model:visible="showCheck" :title="modalTitle" :currentRecord="currentRecord" @confirm="searchData" />
    <BasicEditModalSlot
        class="Nofooter"
        :visible="showReject"
        @cancel="cancelHandle"
        @confirm="batchCheck(0, true)"
        title="批量拒绝"
        width="500px"
    >
        <div style="padding: 0 15px">
            <Textarea v-model:value="checkerReason" placeholder="请输入拒绝理由" :rows="7" />
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { SearchBarOption } from '/#/component'
import CreateModal from './CreateModal.vue'
import CheckModal from './CheckModal.vue'
import { accountingVoucherStatusList, approveStatusList, invoiceStatusList, invoiceTypeList } from '/@/utils/dictionaries'
import { message, Modal } from 'ant-design-vue'
import request from '/@/utils/request'
import useUserStore from '/@/store/modules/user'
import { useRoute } from 'vue-router'
import downFile from '/@/utils/downFile'
import evidenceModal from './evidenceModal.vue'

//筛选
const route = useRoute()
const params = ref<inObject>({
    approveStatusList: route.query?.approveStatusList ? JSON.parse(route.query?.approveStatusList as string) : undefined,
})

const tableRef = ref()
const checkerReason = ref('')

const roles = useUserStore().getUserInfo.roles?.map((i) => i.roleKey)
const canCheck = (record) => {
    let flag = false
    switch (record.approveStatus) {
        // 0未进入审批节点 1客服经理待审批 2会计待审批 3 总经理待审批 4会计待确认 5审批通过 6审批未通过
        case 1:
            if (roles?.includes('customer_service_manager')) {
                flag = true
            }
            break
        case 2:
            if (roles?.includes('accounting')) {
                flag = true
            }
            break
        case 3:
            if (roles?.includes('total_manager')) {
                flag = true
            }
            break
        default:
            break
    }
    return flag
}
const canConfirm = (record) => {
    // 会计确认
    return record.approveStatus == 4 && roles?.includes('accounting')
}
const canInvoice = (record) => {
    // console.log(roles)
    // 会计审核通过后的任意时间，出纳可开具发票。
    // jigsaw 给会计开票权限
    return (roles?.includes('cashier') || roles?.includes('accounting')) && record.approveStatus == 5 && record.invoiceStatus != 1
}

const searchData = (isCur?) => {
    if (isCur) tableRef.value.refresh()
    else tableRef.value.refresh(1)
}

const showCheck = ref(false)
const showCreate = ref(false)
const showReject = ref(false)

const currentRecord = ref<Recordable | undefined>(undefined)
const createRecord = () => {
    currentRecord.value = undefined
    showCreate.value = true
}
const editRecord = (record) => {
    currentRecord.value = { ...record }
    showCreate.value = true
}
const modalTitle = ref('查看')
const showRecord = (record, title) => {
    modalTitle.value = title
    currentRecord.value = { ...record }
    showCheck.value = true
}

// 生成凭证
const evideceShow = ref(false)
const createEvidence = (record) => {
    currentRecord.value = { ...record }
    evideceShow.value = true
}
// 删除凭证
const delEvidence = (record) => {
    //作废凭证
    Modal.confirm({
        title: '确认',
        content: '是否确定作废凭证',
        onOk: async () => {
            await request.get(`/api/hr-bill-invoices/delete-voucher?id=${record.id}`, {})
            searchData()
        },
    })
}
const invoicingRecord = (record) => {
    currentRecord.value = { ...record }
    modalTitle.value = '开票'
    showCheck.value = true
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的数据！')
        return
    }
    Modal.confirm({
        title: '确认',
        content: '确认删除所选数据吗？',
        onOk: async () => {
            await request.post(
                `/api/hr-bill-invoices/deletes`,
                selArr.value.map((i) => i.id),
            )
            searchData()
        },
    })
}

const getEditBtnVisible = (record) => {
    /* if (roles.includes('super_admin') || roles.includes('maintenance')) {
        if (record.approveStatus == 0 || record.approveStatus == 6) return true
    } else {
        if ((record.approveStatus == 0 || record.approveStatus == 6) && record.applyName == useUserStore().getUserInfo.realName)
            return true
    } */
    if ((record.approveStatus == 0 || record.approveStatus == 6) && record.applyName == useUserStore().getUserInfo.realName)
        return true
    // if (record.approveStatus == 0 || record.approveStatus == 6) return true
    return false
}

const batchDownload = async () => {
    if (!selArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        const url = await request.post(
            `/api/hr-bill-invoices/download`,
            selArr.value.map((el) => el.id),
        )
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const itemDownload = async (record) => {
    try {
        const url = await request.post(`/api/hr-bill-invoices/download`, [record?.id])
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const cancelHandle = () => {
    showReject.value = false
    checkerReason.value = ''
}

const batchCheck = async (approveType, reject = false) => {
    if (!selArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    if (!approveType && !reject) {
        showReject.value = true
    } else {
        if (reject && !approveType && !checkerReason.value) {
            message.warn('请填写拒绝原因！')
            return
        }
        try {
            await request.post(`/api/hr-bill-invoices/approve`, {
                ids: selArr.value.map((el) => el.id),
                approveType, // 0 拒绝 1 通过
                reason: checkerReason.value,
            })
            searchData()
            cancelHandle()
        } catch (error) {
            console.log(error)
        }
    }
}

const columns = [
    {
        title: '开票单位',
        dataIndex: 'clientName',
        width: 140,
    },
    {
        title: '发票性质',
        dataIndex: 'invoiceType',
        width: 110,
        customRender: ({ text }) => {
            return invoiceTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '申请人',
        dataIndex: 'applyName',
        width: 110,
    },
    {
        title: '合计',
        dataIndex: 'totalAmount',
        width: 110,
    },
    {
        title: '税额合计',
        dataIndex: 'taxAmount',
        width: 110,
    },
    // {
    //     title: '到账金额',
    //     dataIndex: 'totalArrivalAmount',
    //     width: 110,
    // },
    {
        title: '申请日期',
        dataIndex: 'applyDate',
        width: 110,
    },
    {
        title: '审批流程',
        dataIndex: 'approveStatus',
        width: 120,
        customRender: ({ text }) => {
            return approveStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '发票状态',
        dataIndex: 'invoiceStatus',
        width: 110,
        customRender: ({ text }) => {
            return invoiceStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '到账金额',
        dataIndex: 'totalArrivalAmount',
        width: 110,
    },
    {
        title: '凭证状态',
        dataIndex: 'accountingVoucherStatus',
        width: 110,
        customRender: ({ text }) => {
            return accountingVoucherStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 230,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '开票单位',
        key: 'clientId',
        type: 'clientSelectTree',
    },
    {
        label: '申请人',
        key: 'applyName',
    },
    {
        label: '申请日期',
        key: 'applyDate',
        type: 'date',
    },
    {
        label: '审批流程',
        key: 'approveStatusList',
        type: 'select',
        multiple: true,
        options: approveStatusList,
    },
    {
        label: '发票状态',
        key: 'invoiceStatus',
        type: 'select',
        options: invoiceStatusList,
    },
    {
        label: '凭证状态',
        key: 'accountingVoucherStatus',
        type: 'select',
        options: accountingVoucherStatusList,
    },
    {
        type: 'numberrange',
        label: '开票金额',
        canNegative: true,
        key: 'totalAmountQuery',
    },
    {
        type: 'numberrange',
        label: '税额',
        canNegative: true,
        key: 'taxAmountQuery',
    },
]
</script>

<style scoped lang="less"></style>
