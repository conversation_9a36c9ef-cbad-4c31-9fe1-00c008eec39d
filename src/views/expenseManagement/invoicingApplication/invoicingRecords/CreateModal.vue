<template>
    <BasicEditModalSlot :title="currentRecord ? '编辑' : '新增'" :visible="visible" @cancel="modalClose" width="1000px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <Row>
                <Col span="8">
                    <FormItem label="申请人" :rules="{ required: true, message: '请输入申请人', trigger: 'change' }">
                        <Input :value="formData.realName" disabled placeholder="申请人" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="申请日期">
                        <DatePicker
                            v-model:value="formData.applyDate"
                            placeholder="申请日期"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                    <FormItem
                        label="开票单位"
                        name="clientId"
                        :rules="{ required: true, message: '请选择开票单位', trigger: 'change' }"
                    >
                        <ClientSelectTree
                            v-model:value="formData.clientId"
                            :disabled="viewType != 'canInvoiceAdd'"
                            :itemForm="{ placeholder: '开票单位' }"
                            :customDictionaryKey="viewType == 'canInvoiceAdd' ? 'epibolyClients' : undefined"
                            :customApi="viewType == 'canInvoiceAdd' ? '/api/hr-clients/non-page' : undefined"
                            :requestMethod="viewType == 'canInvoiceAdd' ? 'post' : undefined"
                            :requestParams="viewType == 'canInvoiceAdd' ? { agreementType: 2 } : undefined"
                            @change="invoiceChange"
                        />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem
                        label="发票性质"
                        name="invoiceType"
                        :rules="{ required: true, type: 'number', message: '请选择发票性质', trigger: 'change' }"
                    >
                        <Select v-model:value="formData.invoiceType" :options="invoiceTypeList" placeholder="发票性质" />
                    </FormItem>
                </Col>
            </Row>
            <!-- <div class="btns" v-if="viewType == 'canInvoiceEdit'">
                <Button size="small" type="primary" danger @click="removeSome">删除</Button>
                <Button size="small" type="primary" @click="addSome">新增</Button>
            </div> -->
            <BasicTable
                ref="tableRef"
                :tableDataList="tableData"
                :columns="columns"
                :useIndex="true"
                :sorter="false"
                @selectedRowsArr="selHandle"
                :checkboxProps="getCheckboxProps"
                :scroll="{
                    x: 100,
                    y: 350,
                }"
            >
                <template #recordTitle="{ text, record }">
                    <Input :value="text" @change="(e) => (record.title = e.target.value)" style="width: 100%" :disabled="false" />
                </template>
                <template #content="{ text, record }">
                    <Select
                        :value="text"
                        @change="(val) => (record.content = val)"
                        :options="contentList"
                        style="width: 100%"
                        :getPopupContainer="() => body"
                        :disabled="false"
                    />
                </template>
                <template #totalAmount="{ text, record }">
                    <InputNumber
                        :value="text"
                        :min="0"
                        @change="totalAmountChange($event, record)"
                        style="width: 100%"
                        :disabled="showAddButton == false ? true : false"
                    />
                </template>
                <template #taxRate="{ text, record }">
                    <Select
                        :value="text"
                        @change="taxRateChange($event, record)"
                        :options="taxRateList"
                        style="width: 100%"
                        :getPopupContainer="() => body"
                        :disabled="false"
                    />
                </template>
                <template #taxAmount="{ text, record }">
                    <InputNumber
                        :disabled="true"
                        :value="text"
                        :min="0"
                        @change="(val) => (record.taxAmount = val)"
                        style="width: 100%"
                    />
                </template>
            </BasicTable>
            <Row style="margin-top: 20px">
                <Col span="8">
                    <FormItem label="合计">
                        <Input disabled :value="totalAmount" placeholder="合计" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="税额合计">
                        <Input disabled :value="taxAmount" placeholder="税额合计" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="大写" :label-col="{ span: 2 }">
                        <Input disabled :value="number2text(totalAmount)" placeholder="大写" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="附件" :label-col="{ span: 2 }">
                        <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                            <Button type="primary" size="small">上传文件</Button>
                        </Upload>
                        <div class="files" v-if="formData.fileIds?.length">
                            <span class="item" v-for="(i, idx) in formData.fileIds" :key="i.id">
                                <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                                <span @click="removeFile(idx)">x</span>
                            </span>
                        </div>
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="备注" :label-col="{ span: 2 }">
                        <Textarea :rows="3" v-model:value="formData.remark" placeholder="备注" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="审核结果通知" :label-col="{ span: 3 }">
                        <CheckboxGroup v-model:value="formData.noticeRoles" :options="roleList" />
                    </FormItem>
                </Col>
            </Row>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <template v-if="isDefault != 1">
                <Button type="primary" :disabled="isSendBtn" @click="modalConfirm(0)">暂存</Button>
                <Button type="primary" :disabled="isSendBtn" @click="modalConfirm(1)">发送</Button>
            </template>
            <template v-if="isDefault == 1">
                <Button type="primary" :disabled="isSendBtn" @click="showMergeModal(0)">暂存</Button>
                <Button type="primary" :disabled="isSendBtn" @click="showMergeModal(1)">发送</Button>
            </template>
        </template>
    </BasicEditModalSlot>

    <BasicEditModalSlot v-model:visible="isShowMerge" :title="'提示'" @cancel="isShowMerge = false">
        <div>
            <p>是否需要合并?</p>
        </div>
        <template #footer>
            <Button type="primary" @click="showMergeConfirm(1)">是</Button>
            <Button @click="showMergeConfirm(0)">否</Button>
        </template>
    </BasicEditModalSlot>
    <!-- 从账单中选择 -->
    <BasicEditModalSlot :visible="showBill" title="选择账单" @cancel="billClose" :footer="null" width="1000px">
        <BasicTable
            ref="billTableRef"
            :api="`/api/hr-bill-invoices/hr-bills/${formData.clientId}`"
            :columns="billColumns"
            :params="{}"
            :sorter="false"
            :rowSelectionShow="false"
            @getData2="(data) => (billTableData = data)"
        >
            <template #operation="{ record }">
                <Button
                    :disabled="!(record.billState === 1 && !record.invoiceStatus)"
                    size="small"
                    type="primary"
                    @click="billConfirm(record)"
                >
                    选择
                </Button>
            </template>
        </BasicTable>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { computed, defineComponent, nextTick, onMounted, PropType, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { DatePicker, CheckboxGroup, message, Upload, Modal } from 'ant-design-vue'
import useUserStore from '/@/store/modules/user'
import moment from 'moment'
import { number2text } from '/@/utils/index'
import { invoiceTypeList } from '/@/utils/dictionaries'
import { plus, minus, times, divide } from 'number-precision'
import downFile from '/@/utils/downFile'
import { getContentList, getNoticeRoleList, getTaxRateList } from '/@/utils/api'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
import { Item } from 'ant-design-vue/lib/menu'
import { table } from 'console'

const props = defineProps({
    visible: Boolean,
    viewType: {
        type: String,
        validator: function (value: string) {
            return ['', 'canInvoiceAdd', 'canInvoiceEdit', 'invoicingRecordsEdit'].indexOf(value) !== -1
        },
        default: '',
    },
    currentRecord: {
        type: Object as PropType<Recordable> | undefined,
        default: undefined,
    },
})
defineComponent({ CheckboxGroup })
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord, viewType } = toRefs(props)
const isSendBtn = ref<any>(false)
const showAddButton = ref<any>(false)
const isDefault = ref<any>(1)
const isMerge = ref<any>(0)
const isShowMerge = ref<any>(false)
const isFlag = ref(false)
watch(visible, () => {
    if (viewType.value == 'invoicingRecordsEdit') {
        isFlag.value = true
        totalAmount.value = currentRecord?.value?.totalAmount
    }
    // if (viewType.value == 'canInvoiceEdit') {
    //     isFlag.value = true
    // }
    if (visible.value) {
    } else {
        isSendBtn.value = false
    }
    tax.value = currentRecord?.value?.taxAmount
    visible.value && currentRecord?.value && getData()
})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-invoices/${currentRecord?.value?.id}`)
    isDefault.value = res.isDefault
    isMerge.value = res.isMerge
    formData.value = {
        ...res,
        realName: useUserStore().getUserInfo?.realName,
        noticeRoles: res.noticeRoles?.split(',') || [],
        fileIds: res.appendixes,
    }
    // 0未锁定 1已锁定 3解锁 4重新锁定"
    if (viewType.value == 'invoicingRecordsEdit' && res.isDefault !== 2) {
        res.invoiceRecords.forEach((item) => {
            console.log('item', item)
            if (item.state == 2 || item.state == 4) {
                isSendBtn.value = true
            }
        })
    }

    if (isDefault.value == 2) {
        columns.value = columns.value.filter((item) => item.dataIndex !== 'paymentDate')
    } else {
        let payIdx = columns.value.findIndex((el) => el.dataIndex == 'paymentDate')
        payIdx == -1 && columns.value.splice(0, 0, { title: '费用年月', dataIndex: 'paymentDate', width: 140 })
    }
    tableData.value = res.invoiceRecords || []
    if (viewType.value == 'invoicingRecordsEdit' && res.isDefault == 1) {
        selArr.value = tableData.value.filter((el) => el.state != 2 && el.state != 4)
    }
    if (viewType.value == 'canInvoiceEdit') {
        selArr.value = tableData.value.filter((el) => {
            return el.state != 2 && el.state != 4
        })
    }
}

const formData = ref<Recordable>({
    realName: useUserStore().getUserInfo?.realName,
    applyDate: moment().format('YYYY-MM-DD'),
    clientId: undefined,
    noticeRoles: [],
    fileIds: [],
})
const tableRef = ref()
const selArr = ref<Recordable[]>([])
const tableData = ref<Recordable[]>([])
// const total = ref(0)
// const totalAmount = computed(() => {
//     if (viewType.value == 'canInvoiceAdd') {
//         return tableData.value.map((i) => i.totalAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
//     } else {
//         if (viewType.value.includes('canInvoice'))
//             return selArr.value.map((i) => i.totalAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
//         else return tableData.value.map((i) => i.totalAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
//     }
// })
const totalAmount = ref(0)

const tax = ref()
const taxAmount = computed(() => {
    // if (viewType.value == 'canInvoiceAdd') {
    //     return tableData.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    // } else {
    if (viewType.value.includes('canInvoice'))
        return selArr.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    else return tableData.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    // }
})
const checkOn = ref()
const selHandle = (arr) => {
    arr.forEach((el) => {
        el.checkOn = 1
        checkOn.value = el.checkOn
    })
    selArr.value = arr.filter((record) => record.state != 2 && record.state != 4)
    computedTotalAmount()
    // if (viewType.value == 'canInvoiceAdd') {
    //     let arr2 = selArr.value.map((i) => i.totalAmount ?? 0)
    //     total.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
    // }
    // let arr2 = selArr.value.map((i) => i.taxAmount ?? 0)
    // taxAmount.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
}
// 如果未发送完就默认不可选中的代码
const getCheckboxProps = (record) => {
    console.log(viewType.value)
    return {
        disabled:
            viewType.value == 'invoicingRecordsEdit' ||
            (viewType.value == 'canInvoiceEdit' && (record.state == 2 || record.state == 4)),
        defaultChecked:
            (viewType.value == 'canInvoiceEdit' && (record.state == 2 || record.state == 4)) ||
            viewType.value == 'invoicingRecordsEdit'
                ? true
                : false,
        id: record.id + '',
    }
}

const computedTotalAmount = () => {
    let arr2 = selArr.value.map((i) => i.totalAmount ?? 0)
    totalAmount.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
}

const createRow = () => {
    tableData.value.push({
        id: new Date().getTime(),
        title: undefined,
        content: undefined,
        totalAmount: 0,
        taxRate: 0,
        taxAmount: 0,
        noTaxAmount: undefined,
        checkOn: 0,
    })
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warning('请选择要删除的数据！')
        return
    }
    const ids = selArr.value.map((i) => i.id)
    tableData.value = tableData.value.filter((i) => !ids.includes(i.id))
    tableRef.value.checkboxReset()
}

const addSome = () => {
    tableData.value.push({
        id: new Date().getTime() + '',
        title: undefined,
        content: undefined,
        totalAmount: 0,
        taxRate: 0,
        taxAmount: 0,
        noTaxAmount: undefined,
        checkOn: 0,
        isDefault: 2,
        isMerge: 0,
    })
}
const downTemp = () => {
    downFile('get', '/api/hr-bill-invoices/record-template', '开票明细导入模板.xlsx')
}
const importSome = async (file) => {
    message.loading('导入中...')
    const form = new FormData()
    form.append('file', file)
    const res = await request.post(`/api/hr-bill-invoices/record-template/import`, form)

    tableData.value = [
        ...tableData.value,
        ...res?.map((i, idx) => ({
            ...i,
            id: new Date().getTime() + idx,
        })),
    ]
    return false
}
const billTableRef = ref()
const billTableData = ref<any>([])
watch(billTableData, () => {
    !billTableData.value.length && message.warning('该单位未存在有效账单，请重新选择')
})

const tableEleDisabled = computed(() => {
    return viewType.value.includes('Edit')
})

const selectFromBill = async () => {
    if (formData.value.billId) {
        Modal.confirm({
            title: '确认',
            content: '已从账单中导入过明细，是否继续导入?',
            okText: '继续',
            onOk: () => {
                showBill.value = true
                billTableRef.value.refresh(1)
            },
        })
    } else {
        showBill.value = true
        billTableRef.value.refresh(1)
    }
}

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.fileIds.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.fileIds.splice(idx, 1)
}

const resetData = () => {
    totalAmount.value = 0
    formData.value = {
        realName: useUserStore().getUserInfo?.realName,
        applyDate: moment().format('YYYY-MM-DD'),
        clientId: undefined,
        noticeRoles: [],
        fileIds: [],
    }
    tableData.value = []
}
const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const formInline = ref()
const modalConfirm = async (approveStatus) => {
    await formInline.value.validate()
    tableData.value.forEach((i) => {
        i.checkOn = i.checkOn == null ? 0 : i.checkOn
        selArr.value.forEach((t) => {
            if (i.id == t.id) {
                i.checkOn = 1
                t.checkOn = 1
            }
        })
    })
    /*    if (showAddButton.value) {
        const contentValue = {}
        selArr.value.forEach((item) => {
            if (!contentValue[item.content]) {
                contentValue[item.content] = 1
            } else {
                contentValue[item.content]++
            }
        })
        const contentName = Object.values(contentValue).every((el) => el === 1)
        const taxRateValue = {}
        if (!contentName) {
            selArr.value.forEach((item) => {
                if (!taxRateValue[item.taxRate]) {
                    taxRateValue[item.taxRate] = 1
                } else {
                    taxRateValue[item.taxRate]++
                }
                console.log(taxRateValue[item.taxRate])
            })
            const taxRateValues = Object.values(taxRateValue).every((el) => el === 1)
            if (!taxRateValues) {
                // message.success('相同发票内容税率相同')
            } else {
                message.error('相同发票内容的税率不相同，请修改税率')
                return
            }
        }
    } */
    await formInline.value.validate()
    const form = {
        ...formData.value,
        id: currentRecord?.value?.id,
        approveStatus,
        totalAmount: totalAmount.value,
        totalAmountCn: number2text(totalAmount.value),
        taxAmount: taxAmount.value,
        noticeRoles: formData.value.noticeRoles?.join(','),
        invoiceRecords:
            viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')
                ? selArr.value.map((i) => {
                      console.log(i)
                      return {
                          ...i,
                          createdBy: undefined,
                          createdDate: undefined,
                          lastModifiedBy: undefined,
                          lastModifiedDate: undefined,
                          state: i.state,
                          levelld: i.levelld,
                      }
                  })
                : tableData.value.map((i) => ({
                      ...i,
                      id: i.id,
                  })),
        // invoiceRecords: selArr.value.map((i) => {
        //     if (viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')) {
        //         return {
        //             ...i,
        //             createdBy: undefined,
        //             createdDate: undefined,
        //             lastModifiedBy: undefined,
        //             lastModifiedDate: undefined,
        //             state: i.state,
        //             levelld: i.levelld,
        //         }
        //     } else {
        //         return {
        //             ...i,
        //             id: i.id,
        //         }
        //     }
        // }),
        fileIds: formData.value.fileIds?.map((i) => i.id),
        applyId: approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
    }

    let api: any
    if (viewType.value == 'canInvoiceAdd') {
        await request.post('/api/hr-bill-invoices', form)
    } else {
        //编辑进入
        if (currentRecord?.value) {
            if (approveStatus == 0) {
                await request.put('/api/hr-bill-invoices', form)
            } else {
                // 1开票记录   2可开发票
                if (currentRecord?.value.invoiceState == 2) {
                    await request.post('/api/hr-bill-invoices-save', form)
                } else {
                    await request.put('/api/hr-bill-invoices', form)
                }
            }
        }
    }
    emit('confirm', 1)
    modalClose()
}

const billIds = ref<any>()
const invoiceChange = async (val) => {
    billIds.value = val
}
let tempVal: number //approveStatus
const showMergeModal = async (val) => {
    await formInline.value.validate()
    tempVal = val
    isShowMerge.value = true
}
const showMergeConfirm = async (merge) => {
    let tempMerge = merge
    //
    if (tempMerge == 1) {
        const contTax = {}
        selArr.value.forEach((item) => {
            if (contTax.hasOwnProperty(item.content)) {
                contTax[item.content].push(item.taxRate)
            } else {
                contTax[item.content] = [item.taxRate]
            }
            contTax[item.content] = [...new Set(contTax[item.content])]
        })
        console.log('去重后=》', contTax)
        for (const k in contTax) {
            if (contTax[k].length > 1) {
                message.warn('相同发票内容的税率不相同，不进行合并')
                tempMerge = 0
            }
        }
    }
    const form = {
        ...formData.value,
        id: currentRecord?.value?.id,
        approveStatus: tempVal,
        totalAmount: totalAmount.value,
        totalAmountCn: number2text(totalAmount.value),
        taxAmount: taxAmount.value,
        noticeRoles: formData.value.noticeRoles?.join(','),
        invoiceRecords:
            viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')
                ? selArr.value.map((i: any) => {
                      console.log(i)
                      return {
                          ...i,
                          createdBy: undefined,
                          createdDate: undefined,
                          lastModifiedBy: undefined,
                          lastModifiedDate: undefined,
                          state: i.state,
                          levelld: i.levelld,
                      }
                  })
                : tableData.value.map((i) => ({
                      ...i,
                      id: i.id,
                  })),
        fileIds: formData.value.fileIds?.map((i) => i.id),
        applyId: tempVal == 1 ? useUserStore().getUserInfo?.id : undefined,
        isMerge: tempMerge,
    }
    let api: any
    if (viewType.value == 'canInvoiceAdd') {
        await request.post('/api/hr-bill-invoices', form)
    } else {
        //编辑进入
        if (currentRecord?.value) {
            if (tempVal == 0) {
                await request.put('/api/hr-bill-invoices', form)
            } else {
                // 1开票记录   2可开发票
                if (currentRecord?.value.invoiceState == 2) {
                    await request.post('/api/hr-bill-invoices-save', form)
                } else {
                    await request.put('/api/hr-bill-invoices', form)
                }
            }
        }
    }
    emit('confirm', 1)
    isShowMerge.value = false
    modalClose()
}
const taxRateChange = (val, record) => {
    record.taxRate = val
    if (record.taxRate !== -1) {
        // 税额重新计算
        const base = plus(1, record.taxRate)
        const left = divide(record.totalAmount, base)
        const res = times(left, record.taxRate)
        record.taxAmount = res.toFixed(2)
    }
}
const totalAmountChange = (val, record) => {
    record.totalAmount = val
    if (record.taxRate !== -1) {
        // 税额重新计算
        const base = plus(1, record.taxRate)
        const left = divide(record.totalAmount, base)
        const res = times(left, record.taxRate)
        record.taxAmount = res.toFixed(2)
    }
}

const showBill = ref(false)
const billClose = () => {
    showBill.value = false
}
const billConfirm = (record) => {
    formData.value.billId = record.billId
    const arr: any[] = []
    record.socialSecurityTotal &&
        arr.push({
            id: new Date().getTime() + 0,
            title: record.clientName + '社保收入',
            content: '5',
            totalAmount: record.socialSecurityTotal ?? 0,
            taxRate: 0,
            taxAmount: 0,
            noTaxAmount: undefined,
        })
    record.accumulationFundTotal &&
        arr.push({
            id: new Date().getTime() + 1,
            title: record.clientName + '公积金收入',
            content: '6',
            totalAmount: record.accumulationFundTotal ?? 0,
            taxRate: 0,
            taxAmount: 0,
            noTaxAmount: undefined,
        })
    record.serviceFeeTotal &&
        arr.push({
            id: new Date().getTime() + 2,
            title: record.clientName + '区内单位代理费收入',
            content: '7',
            totalAmount: record.serviceFeeTotal ?? 0,
            taxRate: 0,
            taxAmount: 0,
            noTaxAmount: undefined,
        })
    record.realSalaryTotal &&
        arr.push({
            id: new Date().getTime() + 3,
            title: record.clientName + '工资收入',
            content: '4',
            totalAmount: (record.realSalaryTotal ?? 0) + (record.personalTaxTotal ?? 0),
            taxRate: 0,
            taxAmount: 0,
            noTaxAmount: undefined,
        })
    tableData.value = [...tableData.value, ...arr]
    billClose()
}

const billColumns = [
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 110,
        fixed: 'left',
    },
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 110,
        ellipsis: true,
        fixed: 'left',
    },
    {
        title: '工资收入',
        dataIndex: 'realSalaryTotal',
        width: 110,
        customRender: ({ text, record }) => {
            return (record.realSalaryTotal ?? 0) + (record.personalTaxTotal ?? 0)
        },
    },
    {
        title: '社保收入',
        dataIndex: 'socialSecurityTotal',
        width: 110,
    },
    {
        title: '公积金收入',
        dataIndex: 'accumulationFundTotal',
        width: 110,
    },
    {
        title: '服务费',
        dataIndex: 'serviceFeeTotal',
        width: 110,
    },
    {
        title: '费用汇总',
        dataIndex: 'total',
        width: 110,
    },
    {
        title: '账单状态',
        dataIndex: 'billState',
        width: 110,
        customRender: ({ text }) => {
            return text === 0 ? '未审核通过' : text === 1 ? '已审核通过' : ''
        },
        fixed: 'right',
    },
    {
        title: '开票状态',
        dataIndex: 'invoiceStatus',
        width: 110,
        customRender: ({ text }) => {
            return text === 1 ? '已开票' : '未开票'
        },
        fixed: 'right',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        width: 100,
        slots: { customRender: 'operation' },
    },
]

const columns = ref([
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 140,
    },
    {
        title: '摘要',
        dataIndex: 'title',
        width: 140,
        slots: { customRender: 'recordTitle' },
    },
    {
        title: '发票内容',
        dataIndex: 'content',
        width: 140,
        slots: { customRender: 'content' },
    },
    {
        title: '含税金额',
        dataIndex: 'totalAmount',
        width: 120,
        slots: { customRender: 'totalAmount' },
    },
    {
        title: '税率',
        dataIndex: 'taxRate',
        width: 120,
        slots: { customRender: 'taxRate' },
    },
    {
        title: '税额',
        dataIndex: 'taxAmount',
        width: 120,
        slots: { customRender: 'taxAmount' },
    },
    {
        title: '不含税额',
        dataIndex: 'noTaxAmount',
        width: 100,
        customRender: ({ record, text }) => {
            record.noTaxAmount = minus(record.totalAmount, record.taxAmount)
            return text
        },
    },
])

const contentList = ref<LabelValueOptions>([])
const taxRateList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getContentList()) as LabelValueOptions
    taxRateList.value = (await getTaxRateList()) as LabelValueOptions
    roleList.value = (await getNoticeRoleList()) as LabelValueOptions
}
getList()

const body = document.body
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
