<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData()" />
    <div class="btns">
        <Button type="primary" @click="createRecord" v-auth="'canInvoice_add'">新建</Button>
        <Button type="primary" danger @click="removeSome" v-auth="'canInvoice_batchDel'">批量删除</Button>
        <Button type="primary" @click="batchDownload" >下载开票申请单</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-invoices/page"
        :params="{ ...params, invoiceState: 2 }"
        :columns="columns"
        @selectedRowsArr="selHandle"
    >
        <template #operation="{ record }">
            <Button
                v-if="
                    (record.invoiceState == 2 && record.invoiceLockState == 2) ||
                    (record.invoiceState == 1 && record.approveStatus == 0)
                "
                type="primary"
                size="small"
                @click="editRecord(record)"
                >编辑</Button
            >
            <Button v-else type="primary" size="small" @click="showRecord(record, '查看')">查看</Button>
            &nbsp;
            <Button  type="primary" size="small" @click="itemDownload(record)"> 下载</Button>
        </template>
    </BasicTable>
    <CreateModal v-model:visible="showCreate" :viewType="viewType" :currentRecord="currentRecord" @confirm="searchData" />
    <CheckModal
        v-model:visible="showCheck"
        :title="modalTitle"
        :isCanInvoice="true"
        :currentRecord="currentRecord"
        @confirm="searchData"
    />
</template>

<script lang="ts" setup>
import { ref, h } from 'vue'
import { SearchBarOption } from '/#/component'
import CreateModal from './CreateModal.vue'
import CheckModal from './CheckModal.vue'
import { message, Modal } from 'ant-design-vue'
import request from '/@/utils/request'
import { useRoute } from 'vue-router'
import downFile from '/@/utils/downFile'

//筛选
const route = useRoute()
const params = ref<inObject>({
    approveStatusList: route.query?.approveStatusList ? JSON.parse(route.query?.approveStatusList as string) : undefined,
})

const tableRef = ref()

const searchData = (isCur?) => {
    if (isCur) tableRef.value.refresh()
    else tableRef.value.refresh(1)
}

const showCheck = ref(false)
const showCreate = ref(false)
const viewType = ref('')

const isPullSettle = ref<Boolean>()
const currentRecord = ref<Recordable | undefined>(undefined)
const createRecord = () => {
    // Modal.confirm({
    //     title: '提示',
    //     width: 550,
    //     content: h('div', [h('div', '是否拉取结算单金额')]),
    //     okText: '是',
    //     cancelText: '否',
    //     onOk: async () => {
    //         isPullSettle.value=false
    //         nextFn()
    //     },
    //     onCancel: () => {
    //         isPullSettle.value=true
    //         nextFn()
    //     },
    // })
    // const nextFn = () => {
    viewType.value = 'canInvoiceAdd'
    currentRecord.value = undefined
    showCreate.value = true
    // }
}
const editRecord = (record) => {
    viewType.value = 'canInvoiceEdit'
    currentRecord.value = { ...record }
    showCreate.value = true
}
const modalTitle = ref('查看')
const showRecord = (record, title) => {
    modalTitle.value = title
    currentRecord.value = { ...record }
    showCheck.value = true
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的数据！')
        return
    }
    Modal.confirm({
        title: '确认',
        content: '确认删除所选数据吗？',
        onOk: async () => {
            await request.post(
                `/api/hr-bill-invoices/deletes`,
                selArr.value.map((i) => i.id),
            )
            searchData()
        },
    })
}

const batchDownload = async () => {
    if (!selArr.value.length) {
        message.error('请至少选择一条数据!')
        return
    }
    try {
        const url = await request.post(
            `/api/hr-bill-invoices/download`,
            selArr.value.map((el) => el.id),
        )
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}

const itemDownload = async (record) => {
    try {
        const url = await request.post(`/api/hr-bill-invoices/download`, [record?.id])
        downFile('get', url, '')
    } catch (error) {
        console.log(error)
    }
}  

const columns = [
    {
        title: '开票单位',
        dataIndex: 'clientName',
        width: 140,
    },
    {
        title: '可开票金额',
        dataIndex: 'totalAmount',
        width: 110,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 110,
        sorter: false,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 90,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]

const searchOptions: SearchBarOption[] = [
    {
        label: '开票单位',
        key: 'clientId',
        type: 'clientSelectTree',
    },
    {
        type: 'numberrange',
        label: '开票金额',
        canNegative: true,
        key: 'totalAmountQuery',
    },
    {
        label: '费用年月',
        key: 'paymentDate',
        type: 'month',
    },
]
</script>

<style scoped lang="less"></style>
