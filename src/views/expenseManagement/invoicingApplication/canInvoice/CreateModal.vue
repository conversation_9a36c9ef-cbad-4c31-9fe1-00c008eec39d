<template>
    <BasicEditModalSlot :title="currentRecord ? '编辑' : '新增'" :visible="visible" @cancel="modalClose" width="1000px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <Row>
                <Col span="8">
                    <FormItem label="申请人" :rules="{ required: true, message: '请输入申请人', trigger: 'change' }">
                        <Input :value="formData.realName" disabled placeholder="申请人" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="申请日期">
                        <DatePicker
                            v-model:value="formData.applyDate"
                            placeholder="申请日期"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                    <FormItem
                        label="开票单位"
                        name="clientId"
                        :rules="{ required: true, message: '请选择开票单位', trigger: 'change' }"
                    >
                        <ClientSelectTree
                            v-model:value="formData.clientId"
                            :disabled="viewType != 'canInvoiceAdd'"
                            :itemForm="{ placeholder: '开票单位' }"
                            :customDictionaryKey="viewType == 'canInvoiceAdd' ? 'epibolyClients' : undefined"
                            :customApi="viewType == 'canInvoiceAdd' ? '/api/hr-clients/non-page' : undefined"
                            :requestMethod="viewType == 'canInvoiceAdd' ? 'post' : undefined"
                            @change="invoiceChange"
                        />
                        <!-- :requestParams="viewType == 'canInvoiceAdd' ? { agreementType: 2 } : undefined" -->
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem
                        label="发票性质"
                        name="invoiceType"
                        :rules="{ required: true, type: 'number', message: '请选择发票性质', trigger: 'change' }"
                    >
                        <Select v-model:value="formData.invoiceType" :options="invoiceTypeList" placeholder="发票性质" />
                    </FormItem>
                </Col>
            </Row>
            <div class="btns" v-if="viewType == 'canInvoiceAdd'">
                <Button size="small" type="primary" danger v-if="showAddButton" @click="removeSome">删除</Button>
                <Button size="small" type="primary" @click="addSome" v-if="showAddButton">新增</Button>
            </div>
            <div class="btns" v-if="viewType == 'canInvoiceEdit'">
                <Button size="small" type="primary" v-if="isDefault == 2" danger @click="removeSome">删除</Button>
                <Button size="small" type="primary" v-if="isDefault == 2" @click="addSome">新增</Button>
            </div>
            <BasicTable
                ref="tableRef"
                v-if="showTable"
                :tableDataList="tableData"
                :columns="columns"
                :useIndex="true"
                :sorter="false"
                @selectedRowsArr="selHandle"
                :checkboxProps="getCheckboxProps"
                :scroll="{
                    x: 10,
                    y: 350,
                }"
            >
                <template #recordTitle="{ text, record }">
                    <Input :value="text" @change="(e) => (record.title = e.target.value)" style="width: 100%" :disabled="false" />
                </template>
                <template #content="{ text, record }">
                    <Select
                        :value="text"
                        @change="(val) => (record.content = val)"
                        :options="contentList"
                        style="width: 100%"
                        :getPopupContainer="() => body"
                        :disabled="false"
                    />
                </template>
                <template #totalAmount="{ text, record }">
                    <InputNumber
                        :value="text"
                        @change="totalAmountChange($event, record)"
                        style="width: 100%"
                        :disabled="showAddButton == false ? true : false"
                    />
                </template>
                <template #taxRate="{ text, record }">
                    <Select
                        :value="text"
                        @change="taxRateChange($event, record)"
                        :options="taxRateList"
                        style="width: 100%"
                        :getPopupContainer="() => body"
                        :disabled="false"
                    />
                </template>
                <template #taxAmount="{ text, record }">
                    <InputNumber :disabled="true" :value="text" @change="(val) => (record.taxAmount = val)" style="width: 100%" />
                </template>
            </BasicTable>
            <Row style="margin-top: 20px">
                <Col span="8">
                    <FormItem label="合计">
                        <Input disabled :value="totalAmount" placeholder="合计" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="税额合计">
                        <Input disabled :value="taxAmount" placeholder="税额合计" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="大写" :label-col="{ span: 2 }">
                        <Input disabled :value="number2text(totalAmount)" placeholder="大写" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="附件" :label-col="{ span: 2 }">
                        <Upload :showUploadList="false" :beforeUpload="beforeUpload">
                            <Button type="primary" size="small">上传文件</Button>
                        </Upload>
                        <div class="files" v-if="formData.fileIds?.length">
                            <span class="item" v-for="(i, idx) in formData.fileIds" :key="i.id">
                                <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                                <span @click="removeFile(idx)">x</span>
                            </span>
                        </div>
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="备注" :label-col="{ span: 2 }">
                        <Textarea :rows="3" v-model:value="formData.remark" placeholder="备注" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <FormItem label="审核结果通知" :label-col="{ span: 3 }">
                        <CheckboxGroup v-model:value="formData.noticeRoles" :options="roleList" />
                    </FormItem>
                </Col>
            </Row>
        </Form>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button type="primary" v-if="showAddButton" @click="modalConfirm(0)">暂存</Button>
            <Button type="primary" v-if="showAddButton" @click="modalConfirm(1)">发送</Button>
            <Button type="primary" v-if="isFlag" @click="showMergeModal(0)">暂存</Button>
            <Button type="primary" v-if="isFlag" @click="showMergeModal(1)">发送</Button>
        </template>
    </BasicEditModalSlot>
    <BasicEditModalSlot v-model:visible="showModal" :title="'提示'" @cancel="showModalCancel" @confirm="showModalConfirm">
        <div>
            <p>是否拉取结算单金额</p>
        </div>
        <template #footer>
            <Button type="primary" @click="showModalConfirm">是</Button>
            <Button @click="showModalCancel">否</Button>
        </template>
    </BasicEditModalSlot>
    <BasicEditModalSlot v-model:visible="isShowMerge" :title="'提示'" @cancel="isShowMerge = false">
        <div>
            <p>是否需要合并?</p>
        </div>
        <template #footer>
            <Button @click="showMergeConfirm(0)">否</Button>
            <Button type="primary" @click="showMergeConfirm(1)">是</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
//
import { computed, defineComponent, nextTick, onMounted, PropType, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { DatePicker, CheckboxGroup, message, Upload, Modal } from 'ant-design-vue'
import useUserStore from '/@/store/modules/user'
import moment from 'moment'
import { number2text } from '/@/utils/index'
import { invoiceTypeList } from '/@/utils/dictionaries'
import { plus, minus, times, divide } from 'number-precision'
import downFile from '/@/utils/downFile'
import { getContentList, getNoticeRoleList, getTaxRateList } from '/@/utils/api'
import { uploadFile } from '/@/utils/upload'
import { previewFile } from '/@/utils/index'
import { Item } from 'ant-design-vue/lib/menu'
import { table } from 'console'
import lodash from 'lodash'

const props = defineProps({
    visible: Boolean,
    viewType: {
        type: String,
        validator: function (value: string) {
            return ['', 'canInvoiceAdd', 'canInvoiceEdit', 'invoicingRecordsEdit'].indexOf(value) !== -1
        },
        default: '',
    },
    currentRecord: {
        type: Object as PropType<Recordable> | undefined,
        default: undefined,
    },
})
defineComponent({ CheckboxGroup })
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord, viewType } = toRefs(props)
const showTable = ref(true)
const showModal = ref<any>(false)
const showAddButton = ref<any>(false)
const isDefault = ref(0) //  0 自动化   1新增   2自定义
const isMerge = ref(0) //  isMerge  1 合并 0不合并
const isDefaultValue = ref<any>(1)
const isShowMerge = ref<any>(false)
const isFlag = ref(false)
const resTable = ref<inObject>([])
watch(visible, () => {
    // if (viewType.value == 'invoicingRecordsEdit') {
    //     isFlag.value = true
    //     totalAmount.value = currentRecord?.value?.totalAmount
    // }
    if (visible.value) {
        showTable.value = true
    } else {
        selArr.value = []
    }
    // if (viewType.value == 'canInvoiceEdit') {
    //     isFlag.value = true
    // }

    tax.value = currentRecord?.value?.taxAmount
    visible.value && currentRecord?.value && getData()
})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-invoices/${currentRecord?.value?.id}`)
    // console.log('$$%请求返回==》', res)

    isDefaultValue.value = res.isDefault
    formData.value = {
        ...res,
        realName: useUserStore().getUserInfo?.realName,
        noticeRoles: res.noticeRoles?.split(',') || [],
        fileIds: res.appendixes,
    }
    isDefault.value = res.isDefault !== null ? res.isDefault : 0

    if (isDefault.value == 2) {
        showAddButton.value = true
        columns.value = columns.value.filter((item) => item.dataIndex !== 'paymentDate')
    } else {
        let payIdx = columns.value.findIndex((el) => el.dataIndex == 'paymentDate')
        payIdx == -1 && columns.value.splice(0, 0, { title: '费用年月', dataIndex: 'paymentDate', width: 140 })
    }
    //  结算单拉取
    if (res.isDefault == 1) {
        isFlag.value = true
        showAddButton.value = false
        isMerge.value = res.isMerge != null ? res.isMerge : 0
    } else {
        isFlag.value = false
        showAddButton.value = true
    }
    resTable.value = lodash.cloneDeep(res.invoiceRecords)
    if (res.isDefault == 1 && res.isMerge == 1) {
        tableData.value = dealData(res.invoiceRecords)
    } else {
        tableData.value = res.invoiceRecords || []
    }

    if (isDefault.value == 0 && viewType.value == 'canInvoiceEdit') {
        selArr.value = tableData.value.filter((el) => {
            return el.state != 2 && el.state != 4
        })
    }
}

const dealData = (data) => {
    let arr: any = []
    // console.log('deal=data==>', data)
    data.forEach((el) => {
        let idx = arr.findIndex((it) => it.content === el.content)
        if (idx >= 0) {
            el.paymentDate = null ? null : (arr[idx].paymentDate = arr[idx].paymentDate + ',' + el.paymentDate)
            arr[idx].totalAmount = plus(arr[idx].totalAmount, el.totalAmount)
            arr[idx].taxAmount = plus(arr[idx].taxAmount, el.taxAmount)
            arr[idx].taxRate = el.taxRate ?? 0
            // arr[idx].invoiceId = arr[idx].invoiceId + ',' + el.invoiceId
            arr[idx].id = arr[idx].id + ',' + el.id
        } else {
            arr.push(el)
        }
    })
    return arr
}

const formData = ref<Recordable>({
    realName: useUserStore().getUserInfo?.realName,
    applyDate: moment().format('YYYY-MM-DD'),
    clientId: undefined,
    noticeRoles: [],
    fileIds: [],
})
const tableRef = ref()
const selArr = ref<Recordable[]>([])
const tableData = ref<Recordable[]>([])
const totalAmount = ref(0)

const tax = ref()
const taxAmount = computed(() => {
    // if (viewType.value == 'canInvoiceAdd') {
    //     return tableData.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    // } else {
    if (viewType.value.includes('canInvoice'))
        return selArr.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    else return tableData.value.map((i) => i.taxAmount ?? 0).reduce((pre, cur) => plus(pre, cur), 0)
    // }
})
const checkOn = ref()
const selHandle = (arr) => {
    arr.forEach((el) => {
        el.checkOn = 1
        checkOn.value = el.checkOn
    })
    selArr.value = arr.filter((record) => record.state != 2 && record.state != 4)
    computedTotalAmount()
    // if (viewType.value == 'canInvoiceAdd') {
    //     let arr2 = selArr.value.map((i) => i.totalAmount ?? 0)
    //     total.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
    // }
    // let arr2 = selArr.value.map((i) => i.taxAmount ?? 0)
    // taxAmount.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
}
// 如果未发送完就默认不可选中的代码
const getCheckboxProps = (record) => {
    return {
        disabled: viewType.value == 'canInvoiceEdit' && (record.state == 2 || record.state == 4),
        defaultChecked: (viewType.value == 'canInvoiceEdit' && record.state == 2) || record.state == 4 ? true : false,
        id: record.id + '',
    }
}
// 合计计算
const computedTotalAmount = () => {
    let arr2 = selArr.value.map((i) => i.totalAmount ?? 0)
    totalAmount.value = arr2.reduce((pre, cur) => plus(pre, cur), 0)
}

const removeSome = () => {
    if (!selArr.value.length) {
        message.warning('请选择要删除的数据！')
        return
    }
    const ids = selArr.value.map((i) => i.id)
    tableData.value = tableData.value.filter((i) => !ids.includes(i.id))
    tableRef.value.checkboxReset()
}

const addSome = () => {
    tableData.value.push({
        id: new Date().getTime() + '',
        title: undefined,
        content: undefined,
        totalAmount: 0,
        taxRate: 0,
        taxAmount: 0,
        noTaxAmount: undefined,
        checkOn: 0,
        isDefault: 2,
        isMerge: 0,
    })
}

const tableEleDisabled = computed(() => {
    return viewType.value.includes('Edit')
})

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.fileIds.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.fileIds.splice(idx, 1)
}

const resetData = () => {
    totalAmount.value = 0
    formData.value = {
        realName: useUserStore().getUserInfo?.realName,
        applyDate: moment().format('YYYY-MM-DD'),
        clientId: undefined,
        noticeRoles: [],
        fileIds: [],
    }
    form.value = {}
    tableData.value = []
    isFlag.value = false
    showAddButton.value = false
    showAddButton.value = false
}
const modalClose = () => {
    emit('update:visible', false)
    resetData()
}
const form = ref<{ [x: string]: any }>({})
const formInline = ref()
// 发送暂存按钮只修改参数和弹出是否合并********* 另写请求函数  ********
const modalConfirm = async (approveStatus) => {
    //1 发送 0暂存
    form.value.approveStatus = approveStatus
    await formInline.value.validate()
    if (tableData.value?.length == 0 || selArr.value?.length == 0) {
        message.warning({ content: '选中数据为空' })
        return
    }
    if (selArr.value?.some((item) => !item.content)) {
        message.warning({ content: '选中数据的发票内容为空！' })
        return
    }
    // if (selArr.value?.some((item) => !item.title)) {
    //     message.warning({ content: '选中数据的摘要为空！' })
    //     return
    // }
    tableData.value.forEach((i) => {
        i.checkOn = i.checkOn == null ? 0 : i.checkOn
        selArr.value.forEach((t) => {
            if (i.id == t.id) {
                i.checkOn = 1
                t.checkOn = 1
            }
        })
    })
    sendFun()
}
// 暂存和发送的请求
const sendFun = async () => {
    form.value = {
        ...formData.value,
        id: currentRecord?.value?.id,
        totalAmount: totalAmount.value,
        totalAmountCn: number2text(totalAmount.value),
        taxAmount: taxAmount.value,
        noticeRoles: formData.value.noticeRoles?.join(','),
        invoiceRecords:
            viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')
                ? selArr.value.map((i: any) => {
                      //   console.log(i)
                      return {
                          ...i,
                          createdBy: undefined,
                          createdDate: undefined,
                          lastModifiedBy: undefined,
                          lastModifiedDate: undefined,
                          state: i.state,
                          levelld: i.levelld,
                      }
                  })
                : tableData.value.map((i) => ({
                      ...i,
                      id: i.id,
                  })),
        fileIds: formData.value.fileIds?.map((i) => i.id),
        applyId: form.value.approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
        ...form.value,
        isDefault: isDefault.value,
        isMerge: isMerge.value,
    }
    // console.log('参数=======>', form.value)
    let api: any
    if (viewType.value == 'canInvoiceAdd') {
        await request.post('/api/hr-bill-invoices', form.value)
    } else {
        //编辑进入
        if (currentRecord?.value) {
            if (form.value.approveStatus == 0) {
                await request.put('/api/hr-bill-invoices', form.value)
            } else {
                // 1开票记录   2可开发票  本页只有2
                if (currentRecord?.value.invoiceState == 2) {
                    await request.post('/api/hr-bill-invoices-save', form.value)
                } else {
                    await request.put('/api/hr-bill-invoices', form.value)
                }
            }
        }
    }
    emit('confirm', 1)
    modalClose()
}

const billIds = ref<any>()
const invoiceChange = async (val) => {
    billIds.value = val
    if (viewType.value == 'canInvoiceAdd') {
        showModal.value = true
    }
}
const showModalCancel = () => {
    isFlag.value = false
    isDefault.value = 2
    tableData.value = []
    showAddButton.value = true
    if (showAddButton.value) {
        columns.value.forEach((el, index) => {
            if (el.dataIndex == 'paymentDate') {
                columns.value.splice(index, 1)
            }
        })
    }
    showModal.value = false
    totalAmount.value = 0
    selArr.value = []
}
const showModalConfirm = async () => {
    showAddButton.value = false
    isDefault.value = 1
    isFlag.value = true
    tableData.value = []
    showTable.value = false
    await request
        .get(`/api/hr-bill-invoices/generate/${billIds.value}`)
        .then((res) => {
            // console.log(res)
            let payIdx = columns.value.findIndex((el) => el.dataIndex == 'paymentDate')
            payIdx == -1 && columns.value.splice(0, 0, { title: '费用年月', dataIndex: 'paymentDate', width: 140 })
            tableData.value = res.invoiceRecords.map((el) => {
                return el
            })
        })
        .catch((err) => {
            console.log(err)
        })
    showTable.value = true
    totalAmount.value = 0
    selArr.value = []
    showModal.value = false
}

let tempMerge = 0
const showMergeModal = async (approveStatus) => {
    form.value.approveStatus = approveStatus
    await formInline.value.validate()
    // console.log('selArr.value', selArr.value, form.value.approveStatus)

    if (selArr.value?.length == 0 || tableData.value?.length == 0) {
        message.warning({ content: '选中数据为空' })
        return
    }
    if (selArr.value?.some((item) => !item.content)) {
        message.warning({ content: '选中数据的发票内容为空！' })
        return
    }

    isShowMerge.value = true
}
// 拆合并
const sendDeal = () => {
    let arr: any = []
    // console.log('sellarr===>', selArr.value, resTable.value)
    selArr.value?.forEach((selItem, i) => {
        let ids = selItem.id?.split(',')
        if (ids.length > 1) {
            //合并的
            resTable.value.forEach((el) => {
                if (ids.includes(el.id)) {
                    el.taxRate = selItem.taxRate
                    arr.push(el)
                }
            })
        } else {
            // 不合并的
            resTable.value.forEach((el) => {
                if (el.id === selItem.id) {
                    el.taxRate = selItem.taxRate
                    arr.push(el)
                }
            })
        }
    })
    arr.forEach((row) => {
        taxRateChange(row.taxRate, row)
        row.noTaxAmount = minus(row.totalAmount, row.taxAmount)
    })

    return arr
}
const showMergeConfirm = async (val) => {
    tempMerge = val

    const contentValue = {}
    selArr.value.forEach((item) => {
        if (!contentValue[item.content]) {
            contentValue[item.content] = 1
        } else {
            contentValue[item.content]++
        }
    })
    const contentName = Object.values(contentValue).every((el) => el === 1)
    if (!contentName && tempMerge == 1) {
        //  选中的数据有相同发票内容的  且 选择合并
        const contTax = {}
        selArr.value.forEach((item) => {
            if (contTax.hasOwnProperty(item.content)) {
                contTax[item.content].push(item.taxRate)
            } else {
                contTax[item.content] = [item.taxRate]
            }
            contTax[item.content] = [...new Set(contTax[item.content])]
        })
        for (const k in contTax) {
            if (contTax[k].length > 1) {
                message.error('相同发票内容的税率不相同，请修改税率')
                isShowMerge.value = false
                return
            }
        }
        isShowMerge.value = false
        if (viewType.value == 'canInvoiceEdit' && isDefault.value == 1 && isMerge.value == 1) {
            //结算单拉取
            selArr.value = sendDeal()
        }
        form.value = {
            ...formData.value,
            id: currentRecord?.value?.id,
            totalAmount: totalAmount.value,
            totalAmountCn: number2text(totalAmount.value),
            taxAmount: taxAmount.value,
            noticeRoles: formData.value.noticeRoles?.join(','),
            invoiceRecords:
                viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')
                    ? selArr.value.map((i: any) => {
                          console.log(i)
                          return {
                              ...i,
                              createdBy: undefined,
                              createdDate: undefined,
                              lastModifiedBy: undefined,
                              lastModifiedDate: undefined,
                              state: i.state,
                              levelld: i.levelld,
                          }
                      })
                    : tableData.value.map((i) => ({
                          ...i,
                          id: i.id,
                      })),
            fileIds: formData.value.fileIds?.map((i) => i.id),
            applyId: form.value.approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
            ...form.value,
            isDefault: isDefault.value,
            isMerge: tempMerge,
        }
        let api: any
        if (viewType.value == 'canInvoiceAdd') {
            await request.post('/api/hr-bill-invoices', form.value)
        } else {
            //编辑进入
            if (currentRecord?.value) {
                if (form.value.approveStatus == 0) {
                    await request.put('/api/hr-bill-invoices', form.value)
                } else {
                    // 1开票记录   2可开发票  本页只有2
                    if (currentRecord?.value.invoiceState == 2) {
                        await request.post('/api/hr-bill-invoices-save', form.value)
                    } else {
                        await request.put('/api/hr-bill-invoices', form.value)
                    }
                }
            }
        }
        emit('confirm', 1)
        modalClose()
    } else {
        // 选中的发票内容没有相同的
        // console.log('选中的数没有相同的', selArr.value, isMerge.value)
        if (viewType.value == 'canInvoiceEdit' && isDefault.value == 1 && isMerge.value == 1) {
            //结算单拉取
            selArr.value = sendDeal()
        }

        isShowMerge.value = false
        form.value = {
            ...formData.value,
            id: currentRecord?.value?.id,
            totalAmount: totalAmount.value,
            totalAmountCn: number2text(totalAmount.value),
            taxAmount: taxAmount.value,
            noticeRoles: formData.value.noticeRoles?.join(','),
            invoiceRecords:
                viewType.value.includes('canInvoiceAdd') || viewType.value.includes('canInvoiceEdit')
                    ? selArr.value.map((i: any) => {
                          return {
                              ...i,
                              createdBy: undefined,
                              createdDate: undefined,
                              lastModifiedBy: undefined,
                              lastModifiedDate: undefined,
                              state: i.state,
                              levelld: i.levelld,
                          }
                      })
                    : tableData.value.map((i) => ({
                          ...i,
                          id: i.id,
                      })),
            fileIds: formData.value.fileIds?.map((i) => i.id),
            applyId: form.value.approveStatus == 1 ? useUserStore().getUserInfo?.id : undefined,
            ...form.value,
            isDefault: isDefault.value,
            isMerge: tempMerge,
        }
        // console.log('参数=======>', form.value)
        let api: any
        if (viewType.value == 'canInvoiceAdd') {
            await request.post('/api/hr-bill-invoices', form.value)
        } else {
            //编辑进入
            if (currentRecord?.value) {
                if (form.value.approveStatus == 0) {
                    await request.put('/api/hr-bill-invoices', form.value)
                } else {
                    // 1开票记录   2可开发票  本页只有2
                    if (currentRecord?.value.invoiceState == 2) {
                        await request.post('/api/hr-bill-invoices-save', form.value)
                    } else {
                        await request.put('/api/hr-bill-invoices', form.value)
                    }
                }
            }
        }
        emit('confirm', 1)
        modalClose()
    }
}

const taxRateChange = (val, record) => {
    record.taxRate = val
    if (record.taxRate !== -1) {
        // 税额重新计算
        const base = plus(1, record.taxRate)
        const left = divide(record.totalAmount, base)
        const res = times(left, record.taxRate)
        record.taxAmount = res.toFixed(2)
    }
}
const totalAmountChange = (val, record) => {
    record.totalAmount = val
    if (record.taxRate !== -1) {
        // 税额重新计算
        const base = plus(1, record.taxRate)
        const left = divide(record.totalAmount, base)
        const res = times(left, record.taxRate)
        record.taxAmount = res.toFixed(2)
    }
}

const columns = ref([
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        width: 140,
    },
    {
        title: '摘要',
        dataIndex: 'title',
        width: 140,
        slots: { customRender: 'recordTitle' },
    },
    {
        title: '发票内容',
        dataIndex: 'content',
        width: 140,
        slots: { customRender: 'content' },
    },
    {
        title: '含税金额',
        dataIndex: 'totalAmount',
        width: 120,
        slots: { customRender: 'totalAmount' },
    },
    {
        title: '税率',
        dataIndex: 'taxRate',
        width: 120,
        slots: { customRender: 'taxRate' },
    },
    {
        title: '税额',
        dataIndex: 'taxAmount',
        width: 120,
        slots: { customRender: 'taxAmount' },
    },
    {
        title: '不含税额',
        dataIndex: 'noTaxAmount',
        width: 100,
        customRender: ({ record, text }) => {
            record.noTaxAmount = minus(record.totalAmount, record.taxAmount)
            return text
        },
    },
])

const contentList = ref<LabelValueOptions>([])
const taxRateList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getContentList()) as LabelValueOptions
    taxRateList.value = (await getTaxRateList()) as LabelValueOptions
    roleList.value = (await getNoticeRoleList()) as LabelValueOptions
}
getList()

const body = document.body
</script>

<style scoped lang="less">
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
