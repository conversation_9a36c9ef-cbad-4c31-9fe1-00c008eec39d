<template>
    <BasicEditModalSlot :title="title" :visible="visible" @cancel="modalClose" width="1100px" centered>
        <Steps :current="formData.approveStatus" size="small" v-if="!isCanInvoice">
            <Step title="发起申请" />
            <Step title="客服经理审核" />
            <Step title="会计审核" />
            <!-- <Step title="总经理审核" /> -->
            <!-- <Step title="会计确认" /> -->
            <Step title="已结束" />
        </Steps>
        <div class="cell" style="margin-top: 20px">
            <div class="title">申请信息</div>
            <div class="main">
                <Row>
                    <Col span="5">
                        <div class="tit">申请人</div>
                        <div class="val">{{ formData.applyName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">申请日期</div>
                        <div class="val">{{ formData.applyDate }}</div>
                    </Col>
                    <Col span="9">
                        <div class="tit">开票单位</div>
                        <div class="val">{{ formData.clientName }}</div>
                    </Col>
                    <Col span="5">
                        <div class="tit">发票性质</div>
                        <div class="val">{{ invoiceTypeList.find((i) => i.value == formData.invoiceType)?.label }}</div>
                    </Col>
                </Row>
                <BasicTable
                    :tableDataList="formData.invoiceRecords || []"
                    :columns="columns"
                    :sorter="false"
                    :rowSelectionShow="false"
                    :useIndex="true"
                />
                <Row>
                    <Col span="6">
                        <div class="tit">合计</div>
                        <div class="val">{{ formData.totalAmount }}</div>
                    </Col>
                    <Col span="6">
                        <div class="tit">税额合计</div>
                        <div class="val">{{ formData.taxAmount }}</div>
                    </Col>
                </Row>
                <Row>
                    <div class="tit">大写</div>
                    <div class="val">{{ formData.totalAmountCn }}</div>
                </Row>
                <Row class="row">
                    <div class="tit">附件</div>
                    <div class="val">
                        <a
                            v-for="i in formData.fileIds || []"
                            :key="i.id"
                            @click="previewFile(i.fileUrl)"
                            style="margin-right: 10px"
                        >
                            {{ i.originName }}
                        </a>
                    </div>
                </Row>
                <Row>
                    <div class="tit">备注</div>
                    <div class="val">{{ formData.remark }}</div>
                </Row>
                <Row>
                    <div class="tit">审核结果通知</div>
                    <div class="val">{{ formData.noticeUsers }}</div>
                </Row>
                <Row>
                    <div class="tit">NC凭证号</div>
                    <div class="val">{{ formData.ncVoucher }}</div>
                </Row>
            </div>
        </div>
        <div class="cell" v-if="!isCanInvoice">
            <div class="title">发票信息</div>
            <div class="main">
                <Row>
                    <div class="tit">状态</div>
                    <div class="val">{{ invoiceStatusList.find((i) => i.value == formData.invoiceStatus)?.label }}</div>
                </Row>
                <Row>
                    <div class="tit">附件</div>
                    <div class="val">
                        <Upload v-if="title === '开票'" :showUploadList="false" :beforeUpload="beforeUpload">
                            <Button type="primary" size="small">上传附件</Button>
                        </Upload>
                        <div class="files">
                            <span class="item" v-for="(i, idx) in formData.invoiceAppendixes" :key="i.id">
                                <a @click="previewFile(i.fileUrl)"> {{ i.originName }} </a>
                                <span @click="removeFile(idx)">x</span>
                            </span>
                        </div>
                    </div>
                </Row>
                <Row>
                    <div class="tit">备注</div>
                    <div class="val">
                        <Textarea
                            v-if="title === '开票'"
                            v-model:value="formData.invoiceRemark"
                            :rows="3"
                            placeholder="开票备注"
                            style="width: 800px"
                        />
                        <span v-else>{{ formData.invoiceRemark }}</span>
                    </div>
                </Row>
            </div>
        </div>
        <div class="cell" v-if="!isCanInvoice">
            <div class="title">审核流程</div>
            <div class="main">
                <div class="logItem" v-for="(i, idx) in formData.opLogs || []" :key="i.id">
                    <div class="name ellipsis">{{ idx + 1 }}、 操作人：（{{ i.roleName }}) {{ i.realName }}</div>
                    <div class="date ellipsis">操作时间：{{ i.createdDate }}</div>
                    <div class="msg ellipsis">操作信息：{{ i.message }}</div>
                </div>
            </div>
        </div>
        <div class="cell" v-if="title == '审核'">
            <div class="title">
                {{ approveStatusList.find((i) => i.value === formData.approveStatus)?.label }}
            </div>
            <div class="main">
                <Textarea :rows="3" v-model:value="formData.reason" placeholder="若拒绝，请填写拒绝原因" />
            </div>
        </div>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <!-- 审核 -->
            <template v-if="title == '审核'">
                <Button :loading="confirmLoading" type="primary" danger @click="check(0)">拒绝</Button>
                <Button :loading="confirmLoading" type="primary" @click="check(1)">通过</Button>
            </template>
            <!-- 开票 -->
            <template v-if="title == '开票'">
                <Button :loading="confirmLoading" :disible="formData.invoiceStatus === 1" type="primary" @click="invoicing">
                    {{ formData.invoiceStatus === 1 ? '已开发票' : '开票' }}
                </Button>
            </template>
            <!-- 确认 -->
            <template v-if="title == '确认'">
                <Button :loading="confirmLoading" type="primary" @click="modalConfirm">确认</Button>
            </template>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { PropType, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { Steps, Step, message } from 'ant-design-vue'
import { invoiceTypeList, approveStatusList, invoiceStatusList } from '/@/utils/dictionaries'
import { getContentList, getNoticeRoleList, getTaxRateList } from '/@/utils/api'
import { previewFile } from '/@/utils/index'
import { uploadFile } from '/@/utils/upload'

const props = defineProps({
    visible: Boolean,
    title: String,
    currentRecord: {
        type: Object as PropType<Recordable>,
    },
    isCanInvoice: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:visible', 'confirm'])

const { visible, currentRecord } = toRefs(props)

const formData = ref<Recordable>({})

const getData = async () => {
    const res = await request.get(`/api/hr-bill-invoices/${currentRecord?.value?.id}`)
    formData.value = {
        ...res,
        fileIds: res.appendixes || [],
        invoiceAppendixes: res.invoiceAppendixes || [],
        approveStatus: [0, 1, 2]?.[res.approveStatus] ?? res.approveStatus,
    }
}

watch(visible, () => {
    visible.value && currentRecord?.value?.id && getData()
})

const beforeUpload = async (file) => {
    const res = await uploadFile(file)
    formData.value.invoiceAppendixes.push(res)
    return false
}
const removeFile = (idx) => {
    formData.value.invoiceAppendixes.splice(idx, 1)
}

const modalClose = () => {
    emit('update:visible', false)
    formData.value = {}
}
const confirmLoading = ref(false)
const modalConfirm = async () => {
    try {
        confirmLoading.value = true
        await request.put(`/api/hr-bill-invoices/confirm/${formData.value.id}`, {})
        modalClose()
        emit('confirm', 1)
    } finally {
        confirmLoading.value = false
    }
}
const invoicing = async () => {
    try {
        confirmLoading.value = true
        await request.post(`/api/hr-bill-invoices/kp`, {
            id: formData.value.id,
            ids: formData.value.invoiceAppendixes?.map((i) => i.id),
            invoiceRemark: formData.value.invoiceRemark,
        })
        modalClose()
        emit('confirm', 1)
    } finally {
        confirmLoading.value = false
    }
}
const check = async (approveType) => {
    if (approveType === 0 && !formData.value.reason) {
        message.warn('请填写拒绝原因！')
        return
    }
    try {
        confirmLoading.value = true
        await request.post(`/api/hr-bill-invoices/approve`, {
            ids: [formData.value.id],
            approveType, // 0 拒绝 1 通过
            reason: formData.value.reason,
        })
        modalClose()
        emit('confirm', 1)
    } finally {
        confirmLoading.value = false
    }
}

const columns = [
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 110,
        fixed: 'left',
    },
    {
        title: '摘要',
        dataIndex: 'title',
        width: 150,
        ellipsis: true,
    },
    // {
    //     title: '费用年月',
    //     dataIndex: 'paymentDate',
    //     width: 150,
    //     ellipsis: true,
    // },
    {
        title: '发票内容',
        dataIndex: 'content',
        width: 150,
        ellipsis: true,
        customRender: ({ text }) => {
            return contentList.value.find((i) => i.value == text)?.label
        },
    },
    {
        title: '含税金额',
        dataIndex: 'totalAmount',
        width: 120,
    },
    {
        title: '税率',
        dataIndex: 'taxRate',
        width: 120,
        customRender: ({ text }) => {
            return taxRateList.value.find((i) => i.value == text)?.label
        },
    },
    {
        title: '税额',
        dataIndex: 'taxAmount',
        width: 120,
    },
    {
        title: '不含税额',
        dataIndex: 'noTaxAmount',
        width: 120,
    },
]

const contentList = ref<LabelValueOptions>([])
const taxRateList = ref<LabelValueOptions>([])
const roleList = ref<LabelValueOptions>([])
const getList = async () => {
    contentList.value = (await getContentList()) as LabelValueOptions
    taxRateList.value = (await getTaxRateList()) as LabelValueOptions
    roleList.value = (await getNoticeRoleList()) as LabelValueOptions
}
getList()
</script>

<style scoped lang="less">
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .main {
        padding: 10px 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .tit {
            display: inline-block;
            margin: 10px 0;
            color: #999999;
            &::after {
                content: '：';
            }
        }
        .val {
            display: inline-block;
            margin: 10px 0;
        }
    }
}
.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: nowrap;
    .val {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
    }
}

.logItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    color: #666;
    .name {
        width: 30%;
    }
    .date {
        width: 24%;
    }
    .msg {
        width: 35%;
    }
}
.files {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
    .item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 20px;
        cursor: pointer;
        border: 1px solid #eee;
        padding: 0 10px;
        border-radius: 10px;
        margin-bottom: 5px;
        span {
            margin-left: 10px;
            color: @warning-color;
            font-size: 18px;
        }
    }
}
</style>
