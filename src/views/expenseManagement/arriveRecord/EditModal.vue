<template>
    <BasicEditModalSlot :visible="visible" @cancel="modalClose" @confirm="modalConfirm" title="编辑" width="1200px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <Row>
                <Col span="8">
                    <FormItem label="客户编号">
                        <Input disabled :value="formData.clientName" placeholder="客户编号" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="客户名称">
                        <Input disabled :value="formData.clientName" placeholder="客户编号" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="缴费年月">
                        <MonthPicker disabled :value="formData.paymentDate" format="YYYY-MM" valueFormat="YYYY-MM" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                    <FormItem
                        label="应收金额"
                        name="receivableAmount"
                        :rules="{ required: true, type: 'number', message: '请输入应收金额', trigger: 'change' }"
                    >
                        <InputNumber
                            v-model:value="formData.receivableAmount"
                            :min="0"
                            placeholder="应收金额"
                            style="width: 90%"
                        />
                        元
                    </FormItem>
                </Col>
            </Row>
            <FormItem label="到账记录" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }">
                <div class="btns">
                    <Button size="small" type="primary" @click="createRcord">新增</Button>
                    <Button size="small" type="primary" danger @click="removeRecord">删除</Button>
                </div>
                <BasicTable
                    ref="tableRef"
                    :tableDataList="tableData"
                    :columns="columns"
                    :sorter="false"
                    @selectedRowsArr="selHandle"
                >
                    <template #serialNo="{ record, text }">
                        <Input :value="text" @change="(e) => (record.serialNo = e.target.value)" style="width: 100%" />
                    </template>

                    <template #bankCard="{ record, text }">
                        <Select
                            :value="{ value: record.accountNumber }"
                            :options="contentList"
                            labelInValue
                            @change="(val) => selectChange(record, val)"
                            placeholder="银行账号"
                            :getPopupContainer="() => body"
                        />
                    </template>

                    <template #arrivalAmount="{ record, text }">
                        <InputNumber
                            :value="text"
                            :min="0"
                            @change="(val) => (record.arrivalAmount = val ?? 0)"
                            style="width: 100%"
                        />
                    </template>
                    <template #arrivalDate="{ record, text }">
                        <DatePicker
                            :value="text"
                            @change="(val, valStr) => (record.arrivalDate = valStr)"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                            style="width: 100%"
                            :getCalendarContainer="getPopupContainer"
                        />
                    </template>
                </BasicTable>
            </FormItem>
            <Row>
                <Col span="8">
                    <FormItem label="到账金额汇总" :label-col="{ span: 7 }">
                        <InputNumber disabled :value="totalArrivalAmount" placeholder="应收金额" style="width: 90%" />
                        元
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="差额">
                        <InputNumber disabled :value="totalDifferenceAmount" placeholder="应收金额" style="width: 90%" />
                        元
                    </FormItem>
                </Col>
            </Row>
            <FormItem label="备注" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }">
                <Textarea v-model:value="formData.remark" :rows="4" placeholder="备注" />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { computed, PropType, ref, toRefs, watch } from 'vue'
import { message, DatePicker, Modal } from 'ant-design-vue'
import request from '/@/utils/request'
import { minus, plus } from 'number-precision'
import moment from 'moment'
const body = document.body
const props = defineProps({
    visible: Boolean,
    currentRecord: {
        type: Object as PropType<Recordable>,
        required: true,
    },
})
const emit = defineEmits(['update:visible', 'confirm'])
const { visible, currentRecord } = toRefs(props)
const contentList = ref([])
const getData = async () => {
    const rr = await request.get(`/api/hr-platform-accounts/list/${4}`)

    contentList.value = rr.map((el) => {
        return {
            ...el,
            label: el.issuingBank + el.accountNumber.slice(-4),
            value: el.accountNumber,
        }
    })
    const res = await request.get(`/api/hr-arrival-records/${currentRecord.value.id}`)
    formData.value = res
    res?.hrArrivalRecordDetailList?.map((el) => {
        if (el.accountNumber == null) {
            el.accountNumber = '37101988141051001067'
        }
    })
    tableData.value =
        res?.hrArrivalRecordDetailList?.map((el) => {
            return { ...el, arrivalDate: el?.isDefault == 2 ? moment().format('YYYY-MM-DD') : el.arrivalDate }
        }) || []
}

watch(visible, () => {
    visible.value && getData()
})

const formInline = ref()
const formData = ref<Recordable>({
    receivableAmount: 0,
    remark: undefined,
})
const totalArrivalAmount = computed(() => {
    return tableData.value.map((i) => i.arrivalAmount).reduce((pre, cur) => plus(pre, cur || 0), 0)
})
const totalDifferenceAmount = computed(() => {
    return (totalArrivalAmount.value || totalArrivalAmount.value == 0) && formData.value.receivableAmount
        ? minus(totalArrivalAmount.value || 0, formData.value.receivableAmount || 0)
        : 0
})
const tableData = ref<Recordable[]>([])

const modalClose = () => {
    emit('update:visible', false)
    emit('confirm')
    formData.value = {
        receivableAmount: 0,
        remark: undefined,
    }
}
const modalConfirm = async () => {
    const arr = tableData.value.filter((el) => !el.accountNumber || !el.arrivalAmount)
    if (arr.length > 0) {
        Modal.warning({
            title: '提示',
            content: '银行账号和到账金额不能为空!',
        })
        return
    }
    try {
        await formInline.value.validate()
        await request.put(`/api/hr-arrival-records`, {
            id: formData.value.id,
            receivableAmount: formData.value.receivableAmount,
            totalArrivalAmount: totalArrivalAmount.value,
            remark: formData.value.remark,
            hrArrivalRecordDetailList: tableData.value,
        })
        modalClose()
    } catch (error) {}
}

const tableRef = ref()
const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}

const selectChange = (record, val) => {
    record.accountNumber = val.value
}
const ss = '37101988141051001067'
const createRcord = () => {
    const it: any = contentList.value.find((el: any) => el.accountNumber == ss)
    tableData.value.push({
        serialNo: undefined,
        arrivalAmount: 0,
        accountNumber: it?.accountNumber || '',
        arrivalDate: moment().format('YYYY-MM-DD'),
    })
}
const removeRecord = async () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的数据！')
        return
    }
    const ids = selArr.value.map((i) => i.id)
    tableData.value = tableData.value.filter((i) => !ids.includes(i.id))
    tableRef.value.checkboxReset()
}

const columns = [
    {
        title: '流水号',
        dataIndex: 'serialNo',
        width: 150,
        slots: { customRender: 'serialNo' },
    },
    {
        title: '银行账号',
        dataIndex: 'accountNumber',
        width: 230,
        slots: { customRender: 'bankCard' },
    },
    {
        title: '到账金额',
        dataIndex: 'arrivalAmount',
        width: 120,
        slots: { customRender: 'arrivalAmount' },
    },
    {
        title: '差额',
        dataIndex: 'differenceAmount',
        width: 130,
        customRender: ({ record, text, index }) => {
            record.differenceAmount =
                index === 0
                    ? minus(record.arrivalAmount ?? 0, formData.value.receivableAmount ?? 0)
                    : plus(record.arrivalAmount ?? 0, tableData.value[index - 1]?.differenceAmount ?? 0)

            return text
        },
    },
    {
        title: '到账时间',
        dataIndex: 'arrivalDate',
        width: 150,
        slots: { customRender: 'arrivalDate' },
    },
]

const getPopupContainer = () => document.body as HTMLElement
</script>
<style scoped lang="less"></style>
