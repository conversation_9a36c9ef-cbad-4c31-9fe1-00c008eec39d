<template>
    <BasicEditModalSlot :visible="visible" @cancel="modalClose" @confirm="modalConfirm" title="新增" width="800px" centered>
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
            <Row>
                <Col span="12">
                    <FormItem
                        label="客户名称"
                        name="clientId"
                        :rules="{ required: true, message: '请选择客户名称', trigger: 'change' }"
                    >
                        <ClientSelectTree
                            v-model:value="formData.clientId"
                            :itemForm="{ placeholder: '客户名称' }"
                            @change="getReceivableAmount"
                        />
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem
                        label="缴费年月"
                        name="paymentDate"
                        :rules="{ required: true, message: '请选择缴费年月', trigger: 'change' }"
                    >
                        <MonthPicker
                            v-model:value="formData.paymentDate"
                            format="YYYY-MM"
                            valueFormat="YYYY-MM"
                            @change="getReceivableAmount"
                        />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="12">
                    <FormItem
                        label="应收金额"
                        name="receivableAmount"
                        :rules="{ required: true, type: 'number', message: '请输入应收金额', trigger: 'change' }"
                    >
                        <InputNumber
                            v-model:value="formData.receivableAmount"
                            placeholder="应收金额"
                            :min="0"
                            style="width: 90%"
                        />
                        元
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem
                        label="到账金额"
                        name="totalArrivalAmount"
                        :rules="{ required: true, type: 'number', message: '请输入到账金额', trigger: 'change' }"
                    >
                        <InputNumber
                            v-model:value="formData.totalArrivalAmount"
                            :min="0"
                            placeholder="到账金额"
                            style="width: 90%"
                        />
                        元
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="12">
                    <FormItem label="差额" :rules="{ required: true, type: 'number', message: '请输入差额', trigger: 'change' }">
                        <InputNumber disabled :value="totalDifferenceAmount" placeholder="差额" style="width: 90%" />
                        元
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="流水号" name="serialNo">
                        <Input v-model:value="formData.serialNo" placeholder="流水号" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="12">
                    <FormItem
                        label="到账时间"
                        name="arrivalDate"
                        :rules="{ required: true, message: '请选择到账时间', trigger: 'change' }"
                    >
                        <DatePicker
                            v-model:value="formData.arrivalDate"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                            placeholder="到账时间"
                        />
                    </FormItem>
                </Col>
            </Row>
            <FormItem label="备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 19 }">
                <Textarea v-model:value="formData.remark" placeholder="备注" :rows="3" />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs } from 'vue'
import { MonthPicker, Textarea } from 'ant-design-vue'
import { minus } from 'number-precision'
import request from '/@/utils/request'
import moment from 'moment'

const props = defineProps({
    visible: Boolean,
})
const emit = defineEmits(['update:visible', 'confirm'])
const { visible } = toRefs(props)

const totalDifferenceAmount = computed(() => {
    return formData.value.totalArrivalAmount && formData.value.receivableAmount
        ? minus(formData.value.totalArrivalAmount, formData.value.receivableAmount)
        : 0
})
const formInline = ref()
const formData = ref<Recordable>({
    receivableAmount: 0,
    totalArrivalAmount: 0,
    arrivalDate: moment().format('YYYY-MM-DD'),
})

const getReceivableAmount = async () => {
    if (!formData.value.clientId || !formData.value.paymentDate) {
        return
    }
    const res = await request.post(`/api/hr-arrival-records/get-receivable-amount`, formData.value)
    formData.value.receivableAmount = res
}

const modalClose = () => {
    emit('update:visible', false)
    formData.value = {
        receivableAmount: 0,
        totalArrivalAmount: 0,
        arrivalDate: moment().format('YYYY-MM-DD'),
    }
}
const modalConfirm = async () => {
    try {
        await formInline.value.validate()
        await request.post(`/api/hr-arrival-records`, {
            ...formData.value,
            totalDifferenceAmount: totalDifferenceAmount.value,
        })
        modalClose()
        emit('confirm')
    } catch (error) {}
}
</script>
<style scoped lang="less"></style>
