<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData(1)" />
    <div class="btns">
        <!-- <Button v-auth="'arriveRecord_create'" type="primary" @click="addNew">新建</Button> -->
        <Button v-auth="'arriveRecord_delete'" type="primary" danger @click="removeSome">批量删除</Button>
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-arrival-records/page"
        exportUrl="/api/hr-arrival-records/export"
        :params="params"
        :columns="getColumns('details')"
        @selectedRowsArr="selHandle"
    >
        <template #operation="{ record }">
            <Button v-auth="'arriveRecord_edit'" size="small" type="primary" @click="showRow(record)">编辑</Button>
        </template>
    </BasicTable>
    <BasicTable
        ref="totalTableRef"
        :columns="getColumns('total')"
        :rowSelectionShow="false"
        :sorter="false"
        :tableDataList="tableDataList"
    >
        <template #operation> / </template>
    </BasicTable>
    <AddModal v-model:visible="showAdd" @confirm="searchData(1)" />
    <EditModal v-model:visible="showEdit" :currentRecord="currentRecord" @confirm="searchData" />
</template>

<script lang="ts" setup>
import { message, Modal } from 'ant-design-vue'
import { computed, ref, watch } from 'vue'
import AddModal from './AddModal.vue'
import EditModal from './EditModal.vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import { differenceTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'

const tableRef = ref()
const params = ref({})

const searchData = (num) => {
    tableRef.value.refresh(num)
}

const showAdd = ref(false)
const showEdit = ref(false)
const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    currentRecord.value = { ...record }
    showEdit.value = true
}

const selArr = ref<Recordable[]>([])
const selHandle = (arr) => {
    selArr.value = arr
}
const removeSome = () => {
    if (!selArr.value.length) {
        message.warn('请选择要删除的数据！')
        return
    }
    Modal.confirm({
        title: '确认',
        content: '确认删除所选数据吗？',
        onOk: async () => {
            await request.post(
                `/api/hr-arrival-records/deletes`,
                selArr.value.map((i) => i.id),
            )
            searchData(1)
        },
    })
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
//导出
const exportData = () => {
    tableRef.value.exportRow('ids', exportText.value, params.value)
}

const totalTableRef = ref()

watch(
    [() => params.value, () => selArr.value],
    ([newParams, newSelArr], [oldParams, oldSelArr]) => {
        dynamicQuery()
    },
    { deep: true },
)

const dynamicQuery = async () => {
    if (exportText.value.indexOf('选中') != -1) {
        let body = {}
        body['ids'] = selArr.value.map((item: any) => {
            return item.id
        })
        await fetchTotalTableData(body)
    } else if (exportText.value.indexOf('筛选') != -1) await fetchTotalTableData({ ...params.value })
    else await fetchTotalTableData()
}

const tableDataList = ref([])

const fetchTotalTableData = async (body = {}) => {
    const res = await request.post('/api/hr-arrival-records/total', body)
    tableDataList.value = [{ ...res, unitNumber: '合计' }] as any
}

fetchTotalTableData()

const getColumns = (type) => {
    const arr = [
        {
            title: '客户编号',
            dataIndex: 'unitNumber',
            width: type == 'total' ? 80 : 160,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 2 : 1,
                    },
                }
            },
        },
        {
            title: '客户名称',
            dataIndex: 'clientName',
            width: type == 'total' ? 80 : 160,
            customRender: ({ text }) => {
                return {
                    children: text,
                    props: {
                        colSpan: type == 'total' ? 0 : 1,
                    },
                }
            },
        },
        {
            title: '缴费年月',
            dataIndex: type == 'total' ? 'paymentDates' : 'paymentDate',
            width: type == 'total' ? 500 : 110,
        },
        {
            title: '应收金额',
            dataIndex: type == 'total' ? 'receivableAmountTotal' : 'receivableAmount',
            width: 110,
        },
        {
            title: '到账金额',
            dataIndex: type == 'total' ? 'totalArrivalAmountTotal' : 'totalArrivalAmount',
            width: 110,
        },
        {
            title: '差额',
            dataIndex: type == 'total' ? 'totalDifferenceAmountTotal' : 'totalDifferenceAmount',
            width: 110,
        },
    ]
    if (type == 'total') {
        return [
            ...arr,
            {
                title: '操作',
                dataIndex: 'operation',
                width: 50,
                slots: { customRender: 'operation' },
            },
        ]
    } else {
        return [
            ...arr,
            {
                title: '到账时间',
                dataIndex: 'lastModifiedDate',
                width: 170,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                width: 110,
                slots: { customRender: 'operation' },
                fixed: 'right',
            },
        ]
    }
}

const searchOptions: SearchBarOption[] = [
    {
        label: '客户编号',
        key: 'unitNumber',
    },
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '缴费年月',
        key: 'paymentDateQuery',
        type: 'monthrange',
    },
    {
        label: '差额',
        key: 'differenceType',
        type: 'select',
        options: differenceTypeList,
    },
    {
        type: 'numberrange',
        label: '应收金额',
        key: 'receivableAmountQuery',
        canNegative: true,
    },
    {
        type: 'numberrange',
        label: '到账金额',
        key: 'totalArrivalAmountQuery',
        canNegative: true,
    },
    {
        type: 'numberrange',
        label: '差额',
        key: 'totalDifferenceAmountQuery',
        canNegative: true,
    },
]
</script>

<style scoped lang="less"></style>
