<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="addExpenseApproval" v-auth="'expenseApproval_add'">新建</Button>
        <!-- <Button type="primary" @click="updateSome">批量下载</Button> -->
    </div>
    <BasicTable ref="talbeRef" api="/api/hr-fee-reviews/page" :params="params" :columns="columns">
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <!-- 新增 -->
    <AddModal v-model:visible="addVisible" @confirm="showSelectModal" />
    <SeeModal
        v-model:visible="seeVisible"
        v-if="seeVisible"
        :title="modalTitle"
        :currentValue="currentValue"
        :viewType="viewType"
        @confirm="refreshList"
        :dinamicHeader="dynamicHeaderText"
    />

    <!-- 作废 -->
    <Cancellation
        v-model:visible="cancellationVisible"
        :currentValue="currentValue"
        :viewType="cancellationviewType"
        @confirm="refreshList"
    />
    <!-- 选择 -->
    <SelectModal
        v-model:visible="selectVisible"
        v-if="selectVisible"
        :clientId="selectModalClientId"
        :clientList="clientList"
        :selectedClients="selectedClientList"
        :feeReviewDate="feeReviewDate"
        :isEpiboly="isEpiboly"
        @confirm="editRow"
        @dinamicHeader="dinamicHeader"
        :billIds="billIds"
        :reviewFlags="reviewFlags"
    />
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, onMounted, ref } from 'vue'
import AddModal from './AddModal.vue'
import SeeModal from './SeeModal.vue'
import Cancellation from './Cancellation.vue'
import SelectModal from './SelectModal.vue'

import { feeReviewStatus } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { SearchBarOption } from '/#/component'
import permissionStore from '/@/store/modules/permission'
import { downMultFile } from '/@/utils/downFile'
import { message } from 'ant-design-vue'
import { getHaveAuthorityOperation } from '/@/utils'
import { useRoute } from 'vue-router'

export default defineComponent({
    name: 'ExpenseApproval',
    components: { AddModal, SeeModal, Cancellation, SelectModal },
    setup() {
        const RoleState = permissionStore().getPermission.staffState // 客户=>false

        let specializedOptions = ref<LabelValueOptions>([])
        onMounted(() => {
            //专管员
            request.get('/api/hr-clients-specialized/selectuser').then((res) => {
                specializedOptions.value = res.map((item: inObject) => {
                    return { label: item.realName, value: item.id, ...item }
                })
            })
        })
        const route = useRoute()
        const billIds = ref()
        const params = ref<inObject>({
            statusList: route.query?.statusList ? JSON.parse(route.query?.statusList as string) : undefined,
        })
        const talbeRef = ref()
        const detailVisible = ref(false)
        // 多选
        const selectedRowsArr = ref([])
        const options: SearchBarOption[] = [
            {
                label: '客户名称',
                key: 'clientIdList',
                placeholder: '客户名称',
                multiple: true,
                type: 'clientSelectTree',
                maxTag: 0,
                checkStrictly: false,
            },
            {
                label: '费用年月',
                key: 'feeReviewDate',
                type: 'month',
            },
            {
                label: '状态',
                key: 'statusList',
                type: 'select',
                multiple: true,
                options: feeReviewStatus,
            },
            {
                label: '专管员',
                key: 'realNameIdList',
                multiple: true,
                type: 'select',
                options: specializedOptions,
            },
        ]

        const columns = ref([
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
            },
            {
                title: '费用年月',
                dataIndex: 'feeReviewDate',
                align: 'center',
                sorter: false,
            },
            {
                title: '金额总计',
                dataIndex: 'total',
                align: 'center',
            },
            {
                title: '状态',
                dataIndex: 'status',
                align: 'center',
                customRender: ({ text }) => {
                    return feeReviewStatus.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
            {
                title: '专管员',
                dataIndex: 'realName',
                align: 'center',
                width: 120,
            },
            {
                title: '创建日期',
                dataIndex: 'createdDate',
                align: 'center',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                width: 300,
                fixed: 'right',
                slots: { customRender: 'operation' },
            },
        ])

        onBeforeMount(() => {
            if (!RoleState) {
                let searchInd = options.findIndex((el) => {
                    return el.key == 'realNameIdList'
                })
                let tableInd = columns.value.findIndex((el) => {
                    return el.dataIndex == 'realName'
                })
                options.splice(searchInd, 1)
                columns.value.splice(tableInd, 1)
            }
        })

        //start 新增
        const addVisible = ref(false)
        const seeVisible = ref(false)
        const modalTitle = ref('查看')
        const viewType = ref('see')
        const currentValue = ref({})
        const addExpenseApproval = () => {
            addVisible.value = true
        }

        const dynamicHeaderText = ref<any>([])
        const dinamicHeader = (res) => {
            dynamicHeaderText.value = res
        }
        //end
        const editRow = async (viewTypeName, data?) => {
            if (viewTypeName == 'add') {
                currentValue.value = data
            } else {
                /* let feeReviewDate = moment()
                    .year(data.payYear)
                    .month(data.payMonthly - 1)
                    .format('YYYY-MM') */
                if (viewTypeName == 'again') {
                    const res = await request.post('/api/hr-fee-reviews/query-client', { clientId: data.clientId })
                    showSelectModal({
                        clientList: res,
                        selectedClientList: data.clientIdList,
                        feeReviewDate: data.feeReviewDate,
                        clientId: data.clientId,
                    })
                } else {
                    currentValue.value = await request.get('/api/hr-fee-reviews/see-settlement-document-info', { id: data.id })
                }
            }
            if (viewTypeName !== 'again') seeVisible.value = true
            modalTitle.value =
                viewTypeName == 'add'
                    ? '新增'
                    : viewTypeName == 'examine'
                    ? '审核'
                    : viewTypeName == 'again'
                    ? '重新发起'
                    : '查看'
            viewType.value = viewTypeName == 'again' ? 'add' : viewTypeName
        }
        const downloadAll = async (data) => {
            let fileUrls: any = []
            let originNames: any = []
            if (data?.detailAppendixIdUrl) {
                fileUrls.push(data?.detailAppendixIdUrl[0]?.fileUrl)
                originNames.push(data?.detailAppendixIdUrl[0]?.originName)
            }
            if (data?.summaryAppendixIdUrl) {
                fileUrls.push(data?.summaryAppendixIdUrl[0]?.fileUrl)
                originNames.push(data?.summaryAppendixIdUrl[0]?.originName)
            }
            if (data?.detailPdfAppendixUrl && data?.detailPdfAppendixUrl?.length) {
                fileUrls.push(data?.detailPdfAppendixUrl[0]?.fileUrl)
                originNames.push(data?.detailPdfAppendixUrl[0]?.originName)
            }
            if (fileUrls?.length) {
                downMultFile(
                    `${data?.clientName + ' ' + data.payYear + '-' + data.payMonthly} 费用审核数据表`,
                    fileUrls,
                    originNames,
                )
            } else {
                message.error('暂无下载数据')
            }
        }

        const modalCancel = () => {
            detailVisible.value = false
        }

        const searchData = () => {
            talbeRef.value.refresh(1)
        }
        const refreshList = (num) => {
            console.log(num)
            if (num) {
                talbeRef.value.refresh(1)
            } else {
                talbeRef.value.refresh()
            }
        }

        const cancellationVisible = ref(false)
        const cancellationviewType = ref('')
        const cancellationRow = (viewTypeName, record) => {
            currentValue.value = record
            cancellationviewType.value = viewTypeName
            cancellationVisible.value = true
        }

        const selectVisible = ref(false)
        const clientList = ref([])
        const selectedClientList = ref([])
        const feeReviewDate = ref('')
        const reviewFlags = ref<any>(0)
        const selectModalClientId = ref('')
        const isEpiboly = ref(false)
        const showSelectModal = (res) => {
            console.log(res)
            selectVisible.value = true
            clientList.value = res.clientList
            selectedClientList.value = res.selectedClientList
            feeReviewDate.value = res.agreementType == 2 ? res.feeReviewDate : res.feeReviewDate
            selectModalClientId.value = res.clientId
            isEpiboly.value = res.agreementType == 2
            billIds.value = [res.strBillIds].join(',')
            reviewFlags.value = res.clientList[0].reviewFlag
        }

        const myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'expenseApproval_see',
                    show: true,
                    click: (record) => editRow('see', record),
                },
                {
                    neme: '审核',
                    auth: 'expenseApproval_examine',
                    show: (record) => {
                        return record.status == 0
                    },
                    click: (record) => editRow('examine', record),
                },
                {
                    neme: '重新发起',
                    auth: 'expenseApproval_relaunch',
                    show: (record) => {
                        return record.status == 2 || record.status == 4
                    },
                    click: (record) => editRow('again', record),
                },
                {
                    neme: '下载',
                    auth: 'expenseApproval_download',
                    show: true,
                    click: downloadAll,
                },
                {
                    neme: '申请作废',
                    auth: 'expenseApproval_voidApply',
                    show: (record) => {
                        return record.status == 1
                    },
                    click: (record) => cancellationRow('apply', record),
                },
                {
                    neme: '作废审核',
                    auth: 'expenseApproval_voidAffirm',
                    show: (record) => {
                        return record.status == 3
                    },
                    click: (record) => cancellationRow('affirm', record),
                },
            ]),
        )

        return {
            isEpiboly,
            selectedClientList,
            selectModalClientId,
            selectVisible,
            clientList,
            feeReviewDate,
            showSelectModal,
            detailVisible,
            talbeRef,
            searchData,
            params,
            modalCancel,
            myOperation,
            columns,
            billIds,
            options,
            selectedRowsArr,
            reviewFlags,

            //start 新增
            addVisible,
            addExpenseApproval,
            //end
            seeVisible,
            modalTitle,
            viewType,
            currentValue,
            editRow,
            refreshList,
            //作废
            cancellationviewType,
            cancellationVisible,
            dinamicHeader,
            dynamicHeaderText,
        }
    },
})
</script>

<style scoped lang="less"></style>
