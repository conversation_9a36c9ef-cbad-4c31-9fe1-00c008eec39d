<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" title="账单作废" width="500px">
        <Textarea
            v-model:value="cancellationValue"
            :placeholder="viewType == 'apply' ? '请输入作废原因' : '请输入备注'"
            :rows="4"
        />
        <p class="tip" v-if="viewType == 'affirm'">
            账单作废后，与该账单相关联的开票申请、报销申请、到账记录等均会结束流程或进入作废状态，请确认是否作废该账单。
        </p>
        <template #footer>
            <div></div>
            <template v-if="viewType == 'affirm'">
                <Button type="primary" class="btn" key="back" @click="confirm(1)">同意</Button>
                <Button danger type="primary" key="back" @click="confirm(2)">拒绝</Button>
            </template>
            <template v-if="viewType == 'apply'">
                <Button key="back" @click="cancel">取消</Button>
                <Button key="submit" type="primary" @click="confirm">确认</Button>
            </template>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs } from 'vue'
import request from '/@/utils/request'

export default defineComponent({
    name: 'ExpenseApprovalCancellation',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        currentValue: {
            type: Object,
            default: () => {},
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'apply', 'affirm'].indexOf(value) !== -1
            },
        },
    },
    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { viewType, currentValue } = toRefs<any>(props)

        const cancellationValue = ref('') //费用年月

        // reset formData
        const resetFormData = () => {
            cancellationValue.value = ''
        }
        // confirm handle
        const confirm = async (opt) => {
            if (!cancellationValue.value && viewType.value == 'apply') {
                message.success('请输入作废原因!')
                return
            }
            // 申请作废   post    /hr-fee-reviews/launch-cancel-apply  参数id， cancelReason--作废原因
            // 确认作废   post    /hr-fee-reviews/confirm-cancel-apply  参数id，cancelRemark--作废备注
            if (viewType.value == 'apply') {
                await request.post('/api/hr-fee-reviews/launch-cancel-apply', {
                    id: currentValue.value.id,
                    cancelReason: cancellationValue.value,
                })
                message.success('提交成功!')
            } else {
                await request.post('/api/hr-fee-reviews/confirm-cancel-apply', {
                    id: currentValue.value.id,
                    cancelRemark: cancellationValue.value,
                    opt: opt == 1,
                })
                message.success('审核完成!')
            }
            cancel()
            // 表单关闭后的其它操作 如刷新表
            emit('confirm')
        }
        // cancel handle
        const cancel = () => {
            emit('update:visible', false)
            resetFormData()
        }
        return { cancellationValue, cancel, confirm }
    },
})
</script>
<style scoped lang="less">
.tip {
    margin-top: 10px;
    color: #101010;
    font-size: 14px;
    text-align: center;
    line-height: 18px;
}
</style>
