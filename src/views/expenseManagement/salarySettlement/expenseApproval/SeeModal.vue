<template>
    <BasicEditModalSlot :visible="visible" @cancel="dataLock" @confirm="confirm" :title="title" width="1300px">
        <p class="title">结算账单信息&nbsp;&nbsp;</p>
        <div class="seeInfoBox" v-if="viewType == 'add'">
            <div class="wtwenty">
                <p class="label">客户编号：</p>
                <p class="overflow">
                    <Tooltip placement="topLeft" :key="Math.random()" :title="currentValue?.unitNumber || ''">
                        {{ currentValue?.unitNumber }}
                    </Tooltip>
                </p>
            </div>
            <div class="wtwenty">
                <p class="label">客户名称：</p>
                <p class="overflow">
                    <Tooltip
                        placement="topLeft"
                        :key="Math.random()"
                        :getPopupContainer="getPopupContainer"
                        :title="currentValue?.clientName || ''"
                    >
                        {{ currentValue?.clientName }}
                    </Tooltip>
                </p>
            </div>
            <div class="wtwenty">
                <p class="label">费用年月：</p>
                <p class="overflow">
                    <Tooltip placement="topLeft" :key="Math.random()" :title="feeReviewDate || ''">
                        {{ currentValue?.paymentDate }}
                    </Tooltip>
                </p>
            </div>
            <div style="width: 40%; display: inline-flex">
                <!-- <p class="label"><span class="required">*</span>标题：</p> -->
                <Form style="width: 100%" :model="formData" ref="formInline">
                    <FormItem
                        label="标题"
                        name="title"
                        :rules="{ required: true, trigger: ['change', 'blur'], type: 'string', message: '请输入标题' }"
                    >
                        <Input placeholder="标题" style="width: 100%" v-model:value="formData.title" />
                    </FormItem>
                </Form>
            </div>
        </div>

        <div class="seeInfoBox" v-if="viewType == 'see' || viewType == 'examine'">
            <div class="wtwenty">
                <p class="label">客户编号：</p>
                <p class="overflow">
                    <Tooltip placement="topLeft" :key="Math.random()" :title="currentValue?.unitNumber || ''">
                        {{ currentValue?.unitNumber }}
                    </Tooltip>
                </p>
            </div>
            <div class="wtwenty">
                <p class="label">客户名称：</p>
                <p class="overflow">
                    <Tooltip
                        placement="topLeft"
                        :key="Math.random()"
                        :getPopupContainer="getPopupContainer"
                        :title="currentValue?.clientName || ''"
                    >
                        {{ currentValue?.clientName }}
                    </Tooltip>
                </p>
            </div>
            <div class="wtwenty">
                <p class="label">费用年月：</p>
                <p class="overflow">
                    <Tooltip placement="topLeft" :key="Math.random()" :title="feeReviewDate || ''">
                        {{ feeReviewDate }}
                    </Tooltip>
                </p>
            </div>
            <br />
            <div>
                <p class="label">标题：</p>
                <p style="width: calc(100% - 70px); vertical-align: top">{{ currentValue?.title }}</p>
            </div>
            <br />
            <div>
                <p class="label">备注：</p>
                <p style="width: calc(100% - 70px); vertical-align: top">{{ currentValue?.remark }}</p>
            </div>
        </div>
        <template v-if="viewType == 'add'">
            <div class="detail-title">
                <div>
                    结算账单明细&nbsp;&nbsp;
                    <Tooltip placement="top" title="将下载整个结算账单明细">
                        <DownloadOutlined
                            class="icon"
                            @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', true, 'detail')"
                        />
                    </Tooltip>
                </div>
                <Tooltip placement="top" title="统一修改服务费" v-if="billType != 3">
                    <EditOutlined @click="serviceVisible = true" />
                </Tooltip>
                <BasicEditModalSlot
                    :visible="serviceVisible"
                    @cancel="serviceCancel"
                    @confirm="confirm"
                    title="统一修改服务费"
                    width="350px"
                    centered
                >
                    <Form :model="formData" ref="serviceForm" style="width: 100%">
                        <FormItem
                            name="serviceFeeEvery"
                            label="服务费"
                            :rules="{ required: true, trigger: ['change', 'blur'], type: 'number', message: '请输入服务费' }"
                        >
                            <InputNumber style="width: 100%" placeholder="服务费" v-model:value="formData.serviceFeeEvery" />
                        </FormItem>
                    </Form>
                    <template #footer>
                        <Button @click="serviceCancel">取消</Button>
                        <Button type="primary" @click="serviceUnifyUpdate">确认</Button>
                    </template>
                </BasicEditModalSlot>
            </div>
            <div class="seeInfoBox">
                <div class="search-wrapper" v-if="currentValue?.hrBillDetailDTO?.length">
                    <Input
                        v-model:value="search.name"
                        :placeholder="billType !== 3 ? '姓名' : '项目代码'"
                        @blur="filterTableData"
                        @pressEnter="filterTableData"
                        @paste="filterTableData"
                    />
                    <Input
                        v-if="billType !== 3"
                        v-model:value="search.certificateNum"
                        :placeholder="billType !== 3 ? '身份证号' : '项目名称'"
                        @blur="filterTableData"
                        @pressEnter="filterTableData"
                        @paste="filterTableData"
                    />
                </div>
                <STable
                    v-if="showTable"
                    :columns="detailedColumns"
                    :dataSource="filterTable"
                    :pagination="false"
                    bordered
                    :rowKey="(record) => record.id || record.index || record.staffId"
                    :scroll="{ y: 500 }"
                />
            </div>
        </template>

        <p class="title">
            结算账单汇总&nbsp;&nbsp;
            <DownloadOutlined class="icon" @click="exportBill(billType != 3 ? 'summary' : 'sinopecSummary', true)" />
        </p>
        <div class="seeInfoBox">
            <Table
                :columns="summaryColumns"
                :data-source="billType != 3 ? summaryTableData : [{ ...dynamicTotalData, ...totalData }]"
                :pagination="false"
                bordered
                :row-key="(record) => record.accumulationFundCardinal || record.index"
                :scroll="{ x: '100%', y: 300 }"
            />
        </div>

        <p class="title" v-if="billType == 3">
            中石化结算费用汇总&nbsp;&nbsp;
            <DownloadOutlined class="icon" @click="exportSinopecInlandBill('sinopecInlandSummary')" />
        </p>
        <div class="seeInfoBox" v-if="billType == 3">
            <Table
                :columns="summaryColumns"
                :data-source="billType != 3 ? summaryTableData : [{ ...dynamicTotalData, ...foreignTotalData }]"
                :pagination="false"
                bordered
                :row-key="(record) => record.accumulationFundCardinal || record.index"
                :scroll="{ x: '100%', y: 300 }"
            />
        </div>
        <template v-if="viewType == 'see' || viewType == 'examine'">
            <div>
                <div style="display: inline-block; width: 50%; vertical-align: top">
                    <p class="title">
                        账单明细&nbsp;&nbsp;
                        <Tooltip placement="top" title="点击下载结算单明细">
                            <DownloadOutlined
                                class="icon"
                                @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', true)"
                            />
                        </Tooltip>
                        &nbsp;&nbsp;
                        <Tooltip placement="top" title="点击刷新结算单明细">
                            <SyncOutlined v-if="userInfo?.roleKey == 'maintenance'" class="icon" @click="reloadBillExport" />
                        </Tooltip>
                    </p>
                    <div class="seeInfoBox">
                        <div class="hrAppendixListBox">
                            <Tooltip placement="top" v-if="(detailsAppendix.id && !typeStatus) || billType == 3">
                                <template #title>
                                    <span>
                                        {{
                                            billType == 3
                                                ? `${currentValue?.clientName} ${feeReviewDate} 账单明细`
                                                : detailsAppendix?.originName
                                        }}
                                    </span>
                                </template>
                                <a
                                    class="enclosure"
                                    @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', false, 'exls')"
                                >
                                    {{
                                        billType == 3
                                            ? `${currentValue?.clientName} ${feeReviewDate} 账单明细`
                                            : detailsAppendix?.originName
                                    }}
                                </a>
                            </Tooltip>
                            &nbsp; &nbsp;
                            <Tooltip placement="top" v-if="detailPdfAppendix.id && !typeStatus">
                                <template #title>
                                    <span>
                                        {{
                                            billType == 3
                                                ? `${currentValue?.clientName} ${feeReviewDate} 账单明细`
                                                : detailPdfAppendix?.originName
                                        }}
                                    </span>
                                </template>
                                <a
                                    class="enclosure"
                                    @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', false, 'pdf')"
                                >
                                    {{ currentValue?.clientName }}{{ feeReviewDate }}账单明细.pdf
                                </a>
                            </Tooltip>
                            <Tooltip placement="top" v-if="detailsAppendix.id && typeStatus">
                                <template #title>
                                    <span>
                                        {{
                                            billType == 3
                                                ? `${currentValue?.clientName} ${feeReviewDate} 账单明细`
                                                : detailsAppendix?.originName
                                        }}
                                    </span>
                                </template>
                                <a
                                    class="enclosure"
                                    @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', false, 'exls')"
                                >
                                    {{ currentValue?.clientName }}{{ feeReviewDate }}账单明细.xlsx
                                </a>
                            </Tooltip>
                            <Tooltip placement="top" v-if="detailPdfAppendix.id && typeStatus">
                                <template #title>
                                    <span>
                                        {{
                                            billType == 3
                                                ? `${currentValue?.clientName} ${feeReviewDate} 账单明细`
                                                : detailsAppendix?.originName
                                        }}
                                    </span>
                                </template>
                                <a
                                    class="enclosure"
                                    @click="exportBill(billType != 3 ? 'detailed' : 'sinopecDetailed', false, 'pdf')"
                                >
                                    {{ currentValue?.clientName }}{{ feeReviewDate }}账单明细.pdf
                                </a>
                            </Tooltip>
                        </div>
                    </div>
                </div>
                <div style="display: inline-block; width: 50%">
                    <p class="title">其他&nbsp;&nbsp;<DownloadOutlined class="icon" @click="downloadFilAll" /></p>
                    <div class="seeInfoBox">
                        <div v-for="AppendixItem in currentValue.appendixIdList" :key="AppendixItem.id" class="hrAppendixListBox">
                            <Tooltip placement="top">
                                <template #title>
                                    <span>{{ AppendixItem?.originName }}</span>
                                </template>
                                <a class="enclosure" @click="downloadFil(AppendixItem)">{{ AppendixItem?.originName }}</a>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <p class="title">是否开发票&nbsp;&nbsp;</p>
        <div class="seeInfoBox">
            <Form :model="formData">
                <FormItem name="isDrawBill">
                    <Select
                        style="width: 80px"
                        v-model:value="formData.isDrawBill"
                        :options="[
                            { label: '是', value: 1 },
                            { label: '否', value: 0 },
                        ]"
                        :disabled="viewType == 'see'"
                        optionFilterProp="label"
                    />
                </FormItem>
            </Form>
        </div>
        <template v-if="viewType == 'add'">
            <p class="title">其他&nbsp;&nbsp;</p>
            <div class="seeInfoBox">
                <Form :model="formData">
                    <FormItem label="附件" name="profileImg">
                        <ImportFile v-model:fileUrls="formData.newAppendixIdList" listType="fileList" ref="refImportFile" />
                    </FormItem>
                    <FormItem label="备注" name="userName">
                        <Textarea v-model:value="formData.remark" :rows="3" allowClear placeholder="请输入备注" />
                    </FormItem>
                </Form>
            </div>
        </template>

        <template v-if="viewType == 'examine'">
            <p class="title">拒绝理由&nbsp;&nbsp;</p>
            <div class="seeInfoBox">
                <Textarea v-model:value="formData.refuseRemark" :rows="3" allowClear placeholder="请输入拒绝理由" />
            </div>
        </template>

        <template v-if="viewType == 'see'">
            <p class="title">审核明细&nbsp;&nbsp;</p>
            <div class="seeInfoBox">
                <div>
                    <p class="label">审核结果：</p>
                    <p>
                        {{
                            currentValue.status == 3 || currentValue.status == 4
                                ? '审核通过'
                                : feeReviewStatus.find((item) => {
                                      return currentValue.status == item.value
                                  })?.label
                        }}
                    </p>
                </div>
                <br />
                <div>
                    <p class="label">审核信息：</p>
                    <p>{{ currentValue?.refuseRemark }}</p>
                </div>
            </div>
            <template v-if="currentValue?.cancelReason">
                <p class="title">作废申请&nbsp;&nbsp;</p>
                <div class="seeInfoBox">
                    <div>
                        <p class="label">作废原因：</p>
                        <p>{{ currentValue?.cancelReason }}</p>
                    </div>
                    <br />
                    <div>
                        <p class="label">作废结果：</p>
                        <p>{{ currentValue.status == 4 ? '同意作废' : currentValue.status == 1 ? '拒绝作废' : '待确认作废' }}</p>
                    </div>
                    <br />
                    <div v-if="currentValue?.cancelRemark">
                        <p class="label">
                            {{ currentValue.status == 1 ? '拒绝原因：' : currentValue.status == 4 ? '同意备注：' : '备注信息' }}
                        </p>
                        <p>{{ currentValue?.cancelRemark }}</p>
                    </div>
                </div>
            </template>
        </template>
        <template #footer>
            <div></div>
            <template v-if="viewType == 'examine'">
                <Button type="primary" class="btn" key="back" @click="refuseOrAdopt(1)">通过</Button>
                <Button danger type="primary" key="back" @click="refuseOrAdopt(2)">拒绝</Button>
            </template>
            <template v-if="viewType == 'add'">
                <Button key="back" @click="dataLock">取消</Button>
                <Button key="submit" type="primary" @click="confirm" :loading="confirmLoading">确认</Button>
            </template>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { DownloadOutlined, EditOutlined, SyncOutlined } from '@ant-design/icons-vue'
import { Checkbox, message, Tooltip } from 'ant-design-vue'
import { ref, defineComponent, toRefs, onMounted, h, computed } from 'vue'
import request from '/@/utils/request'
import { staffTypeOptions } from '/@/utils/dictionaries'
import downFile, { downMultFile, exportTable } from '/@/utils/downFile'
import lodash from 'lodash'
import useUserStore from '/@/store/modules/user'
import {
    getDetailedColumnsInitial,
    getProportionObj,
    getSinopecDetailColumnsInitial,
    getSinopecSummaryColumnsInitial,
    getSummaryColumnsInitial,
} from './data'
import { feeReviewStatus } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils'
import moment from 'moment'
import { minus, plus, round } from 'number-precision'
import { multiSheetToExport, settlementSheetToExport } from '../billAccounting/other/export'
export default defineComponent({
    name: 'SeeModal',
    components: { DownloadOutlined, EditOutlined, SyncOutlined, Tooltip },
    props: {
        title: String,
        currentValue: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            validator: function (value: string) {
                // 这个值必须匹配下列字符串中的一个
                // 查看，新增
                return ['', 'see', 'add', 'examine'].indexOf(value) !== -1
            },
        },
        // 合并列头值
        dinamicHeader: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel', 'update:visible', 'dinamicHeader'],
    setup(props, { emit }) {
        const { visible, viewType, currentValue, dinamicHeader } = toRefs<any>(props)
        const totalData = computed(() => {
            if (!detailedTableData.value.length || billType.value != 3) return {}
            return {
                staffNum: detailedTableData.value.map((i) => i['peopleNum']).reduce((pre, cur) => plus(pre, cur ?? 0), 0) || 0,
                projectNum: detailedTableData.value.length || 0,
                ['一次性奖']: detailedTableData.value.map((i) => i['一次性奖']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人住房公积金']: detailedTableData.value
                    .map((i) => i['代扣个人住房公积金'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人保险']: detailedTableData.value
                    .map((i) => i['代扣个人保险'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['其他']: detailedTableData.value.map((i) => i['其他']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳住房公积金']: detailedTableData.value
                    .map((i) => i['单位缴纳住房公积金'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳社会保险费']: detailedTableData.value
                    .map((i) => i['单位缴纳社会保险费'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['奖金']: detailedTableData.value.map((i) => i['奖金']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['差旅补助']: detailedTableData.value.map((i) => i['差旅补助']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['应得工资']: detailedTableData.value.map((i) => i['应得工资']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['扣个人所得税']: detailedTableData.value
                    .map((i) => i['扣个人所得税'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['报销费用']: detailedTableData.value.map((i) => i['报销费用']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['税金']: detailedTableData.value.map((i) => i['税金']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['管理费']: detailedTableData.value.map((i) => i['管理费']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['职工薪酬 小计']: detailedTableData.value
                    .map((i) => i['职工薪酬 小计'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['通讯积分']: detailedTableData.value.map((i) => i['通讯积分']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['防暑降温费']: detailedTableData.value.map((i) => i['防暑降温费']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                totalFee: detailedTableData.value.map((i) => i['费用合计']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                happenTotalFee: detailedTableData.value
                    .map((i) => i['totalOccurrence'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
            }
        })

        const foreignTotalData = computed(() => {
            if (!detailedTableData.value.length) return {}
            return {
                staffNum:
                    detailedTableData.value
                        .filter((item) => item.projectType != 2)
                        .map((i) => i['peopleNum'])
                        .reduce((pre, cur) => plus(pre, cur ?? 0), 0) || 0,
                projectNum: detailedTableData.value.filter((item) => item.projectType != 2).length || 0,
                ['一次性奖']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i.projectNum ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人住房公积金']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['代扣个人住房公积金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人保险']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['代扣个人保险'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['其他']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['其他'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳住房公积金']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['单位缴纳住房公积金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳社会保险费']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['单位缴纳社会保险费'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['奖金']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['奖金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['差旅补助']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['差旅补助'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['应得工资']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['应得工资'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['扣个人所得税']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['扣个人所得税'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['报销费用']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['报销费用'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['税金']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['税金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['管理费']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['管理费'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['职工薪酬 小计']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['职工薪酬 小计'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['通讯积分']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['通讯积分'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['防暑降温费']: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['防暑降温费'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                totalFee: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['费用合计'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                happenTotalFee: detailedTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['totalOccurrence'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
            }
        })
        // 账单类型
        const billType = ref(0)
        const feeReviewDate = ref('') //费用年月
        const refImportFile = ref()
        const serviceVisible = ref(false)
        const columnsDataChecked = ref({})
        const detailedColumns = ref<inObject[]>([])
        const summaryColumns = ref<inObject[]>([])
        const reviewChoiceDTOTotal = ref<any[]>([])
        const sinopecReviewChoiceDTO = ref<any[]>([])
        let detailedTableData = ref<inObject[]>([])
        let filterTable = ref<inObject[]>([])
        let summaryTableData = ref<inObject[]>([])
        //账单明细附件
        let detailsAppendix = ref<any>({})
        //汇总账单附件
        let summaryAppendix = ref<any>({})
        const summaryDynimicHeader = ref<any[]>([])
        let detailPdfAppendix = ref<any>({})
        const formInline = ref()
        const serviceForm = ref()
        const typeStatus = ref(false)
        const userInfo = ref()
        const confirmLoading = ref(false)
        const formData = ref<any>({
            serviceFeeEvery: 0,
            newAppendixIdList: [],
            remark: '',
            isDrawBill: 1,
            refuseRemark: '',
            title: '',
        })

        const search = ref({
            name: '',
            certificateNum: '',
        })
        onMounted(() => {
            useUserStore().getUserInfo.roles.forEach((list) => {
                userInfo.value = list
            })
            let { title, refuseRemark, isDrawBill, remark, appendixIdList } = currentValue?.value
            formData.value = {
                serviceFeeEvery: 0,
                title,
                refuseRemark,
                isDrawBill: isDrawBill ?? 1,
                remark,
                newAppendixIdList: appendixIdList || [],
            }
            let firstHrBillTotalDTO = currentValue.value?.hrBillDTO[0] || {}
            billType.value = firstHrBillTotalDTO.billType
            let proportionObj = billType.value != 3 ? getProportionObj(currentValue.value?.hrBillDTO || []) : []
            // 明细
            detailedColumns.value = billType.value == 3 ? getSinopecDetailColumnsInitial() : []
            // 汇总
            summaryDynimicHeader.value = currentValue.value?.reviewChoiceDTOList
            summaryColumns.value =
                billType.value != 3 ? getSummaryColumnsInitial(proportionObj) : getSinopecSummaryColumnsInitial()
            if (currentValue.value?.billType == 2) {
                summaryColumns.value.splice(
                    -1,
                    0,
                    {
                        title: '企业税费',
                        dataIndex: 'enterpriseTaxTotal',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '其他',
                        dataIndex: 'specialOtherTotal',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '会费',
                        dataIndex: 'membershipFeeTotal',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                )
            }
            if (currentValue.value?.reviewChoiceDTOList?.length > 0) {
                currentValue.value?.reviewChoiceDTOList?.map((item) => {
                    if (
                        item.key != 'unitSubtotal' &&
                        item.key != 'personalSubtotal' &&
                        item.key != 'total' &&
                        billType.value != 3
                    ) {
                        reviewChoiceDTOTotal.value.push(item.key + 'Total')
                    } else if (item.key == 'unitSubtotal' || item.key == 'personalSubtotal') {
                        reviewChoiceDTOTotal.value.push(item.key)
                    } else {
                        reviewChoiceDTOTotal.value.push(item.key)
                    }
                    return reviewChoiceDTOTotal.value
                })
                let totalArr: any = []
                summaryColumns.value.map((el, index) => {
                    if (!el.children) {
                        if (reviewChoiceDTOTotal.value.includes(el.dataIndex)) {
                            totalArr.push(el)
                        }
                    } else {
                        el.children = el.children?.filter((item) => {
                            if (reviewChoiceDTOTotal.value.includes(item.dataIndex)) {
                                return item
                            }
                        })
                        if (el.children?.length != 0) {
                            totalArr.push(el)
                        }
                    }
                    return el
                })
                let detailedColumnsN: any[] = [
                    {
                        title: '人数',
                        dataIndex: 'staffNum',
                        align: 'center',
                        ellipsis: true,
                        fixed: 'left',
                    },
                ]
                totalArr = [...detailedColumnsN, ...totalArr]
                summaryColumns.value = totalArr
            }

            if (billType.value != 3 && currentValue?.value.clientType != 1) {
                summaryColumns.value.forEach((el, index) => {
                    if (el.dataIndex == 'taxationFeeTotal') {
                        summaryColumns.value.splice(index, 1)
                    }
                })
            }
            summaryTableData.value = [currentValue.value.hrBillTotalDTO]

            if (billType.value == 3) {
                summaryTableData.value = currentValue.value.hrBillTotalDTOList
            }

            if (firstHrBillTotalDTO?.payYear) {
                feeReviewDate.value = moment()
                    .year(firstHrBillTotalDTO?.payYear)
                    .month(firstHrBillTotalDTO.payMonthly - 1)
                    .format('YYYY-MM')
            }

            if (billType.value == 3) {
                generateSinopecDynamicColumnsAndData()
            }
            // 明细
            if (viewType.value == 'add') {
                formData.value.title =
                    currentValue?.value?.clientName +
                    (firstHrBillTotalDTO?.payMonthly ? firstHrBillTotalDTO?.payMonthly + '月份' : '') +
                    '费用审核'
                if (billType.value != 3) {
                    getDetailedColumns(proportionObj)
                    uploadFile(summaryColumns.value, summaryTableData.value, 'summary')
                }
            } else {
                if (currentValue.value?.detailAppendixIdUrl) {
                    detailsAppendix.value = currentValue.value?.detailAppendixIdUrl[0] || {}
                }
                if (currentValue.value?.summaryAppendixIdUrl) {
                    summaryAppendix.value = currentValue.value?.summaryAppendixIdUrl[0] || {}
                }
                if (currentValue.value?.detailPdfAppendixUrl) {
                    detailPdfAppendix.value = currentValue.value?.detailPdfAppendixUrl[0] || {}
                }
            }
        })

        const filterTableData = () => {
            let filterOdds
            if (!search.value.name && !search.value.certificateNum) {
                filterTable.value = detailedTableData.value
                return
            } else if (!search.value.name && search.value.certificateNum) {
                filterOdds = (el) => {
                    if (billType.value == 3) return el.projectName.includes(search.value.certificateNum)
                    else return el.certificateNum.includes(search.value.certificateNum)
                }
            } else if (search.value.name && !search.value.certificateNum) {
                filterOdds = (el) => {
                    if (billType.value == 3) return el.projectCode.includes(search.value.certificateNum)
                    else return el.name.includes(search.value.name)
                }
            } else {
                filterOdds = (el) => {
                    if (billType.value == 3)
                        return el.projectCode.includes(search.value.name) && el.projectName.includes(search.value.certificateNum)
                    else return el.name.includes(search.value.name) && el.certificateNum.includes(search.value.certificateNum)
                }
            }
            filterTable.value = detailedTableData.value.filter((el) => {
                return filterOdds(el)
            })
        }
        const generateSinopecDynamicColumnsAndData = () => {
            // 动态列
            const detailOtherCol: any = detailedColumns.value.find((el) => el.dataIndex == 'otherFee')
            const totalOtherCol: any = summaryColumns.value.find((el) => el.dataIndex == 'totalOtherFee')
            const fixedStrArr = ['通讯积分', '差旅补助', '防暑降温费', '一次性奖']

            const firstDetail = currentValue.value.hrBillDetailDTO[0]
            const dynamicArr: any = []
            firstDetail?.otherExpenseList
                ?.sort((a, b) => {
                    return fixedStrArr.indexOf(b.expenseName) - fixedStrArr.indexOf(a.expenseName)
                })
                .forEach((el) => {
                    dynamicArr.push({
                        title: el.expenseName,
                        dataIndex: el.expenseName,
                        width: 150,
                        align: 'center',
                        customRender: ({ text }) => {
                            return text || 0
                        },
                    })
                })
            let totalArr: any = []
            sinopecReviewChoiceDTO.value = currentValue.value?.reviewChoiceDTOList?.map((el) => el.key)
            detailOtherCol.children = dynamicArr.map((item) => {
                return item
            })
            if (sinopecReviewChoiceDTO.value?.length > 0) {
                console.log('adadada')
                detailedColumns.value.map((el, index) => {
                    if (index > 4) {
                        if (!el.children) {
                            if (sinopecReviewChoiceDTO.value?.includes(el.dataIndex)) {
                                totalArr.push(el)
                            }
                        } else {
                            el.children = el.children?.filter((item) => {
                                if (item.children) {
                                    item.children = item.children.filter((row) => {
                                        if (sinopecReviewChoiceDTO.value?.includes(row.dataIndex)) {
                                            return row
                                        }
                                    })
                                    if (item.children.length != 0) {
                                        return item
                                    }
                                } else {
                                    if (sinopecReviewChoiceDTO.value?.includes(item.dataIndex)) {
                                        return item
                                    }
                                    item.children = item.children?.filter((ele) => {
                                        if (sinopecReviewChoiceDTO.value?.includes(item.dataIndex)) {
                                            return ele
                                        }
                                    })
                                }
                            })
                            if (el.children.length != 0) {
                                totalArr.push(el)
                            }
                        }
                    } else {
                        totalArr.push(el)
                    }
                    return el
                })
                detailedColumns.value = lodash.cloneDeep(totalArr)
                summaryColumns.value = lodash.cloneDeep(totalArr)
                summaryColumns.value.forEach((item, index) => {
                    if (item.dataIndex == 'peopleNum') {
                        item.dataN = 'staffNum'
                        item.dataIndex = item.dataN
                        delete item.dataN
                    }
                    if (item.dataIndex == 'totalOccurrence') {
                        item.dataIndex = 'happenTotalFee'
                    }
                    if (item.dataIndex == '费用合计') {
                        item.dataIndex = 'totalFee'
                    }
                })
                summaryColumns.value.forEach((item, index) => {
                    if (
                        item.dataIndex == 'currentYear' ||
                        item.dataIndex == 'projectCode' ||
                        item.dataIndex == 'projectName' ||
                        item.dataIndex == 'index'
                    ) {
                        summaryColumns.value.splice(index, 4)
                    }
                })
            } else {
                detailOtherCol.children = dynamicArr
                totalOtherCol.children = dynamicArr
                // summaryColumns.value = getSinopecSummaryColumnsInitial()
            }

            const keys = [
                'employeeCompensationList',
                'individualHousingFundList',
                'individualIncomeTaxList',
                'individualInsuranceList',
                'managementExpenseList',
                'otherExpenseList',
                'reimbursementExpenseList',
                'taxesList',
                'totalExpensesList',
                'unitHousingFundList',
                'unitInsuranceList',
            ]

            // 数据
            detailedTableData.value = currentValue.value.hrBillDetailDTO.map((el) => {
                let obj = {}
                keys.forEach((ele) => {
                    el[ele].forEach((item) => {
                        obj[item.expenseName] = item.amount || 0
                    })
                })
                return { ...el, ...obj }
            })
            filterTable.value = detailedTableData.value
            dynamicArr.forEach((el) => {
                dynamicTotalData.value[el.title] = calcDynamicTotal(el.title)
            })
            setTimeout(() => {
                showTable.value = true
            }, 300)
        }

        const dynamicTotalData = ref<any>({})

        const calcDynamicTotal = (title) => {
            return detailedTableData.value.map((el) => el[title]).reduce((pre, cur) => plus(pre, cur ?? 0), 0)
        }

        //将客户的上级客户 添加table数据
        const funClient = (clientId, hrClientDTO) => {
            let clients: inObject = {}
            let index = -1
            function recursion(id) {
                let data = hrClientDTO[id]
                if (data) {
                    index += 1
                    clients['clientId' + (index ? index : '')] = id
                    clients['unitNumber' + (index ? index : '')] = data?.unitNumber || ''
                    clients['clientName' + (index ? index : '')] = data?.clientName
                    if (data?.parentId && data?.parentId != '0') {
                        recursion(data.parentId)
                    }
                }
            }
            recursion(clientId)
            return { clients, index }
        }
        //增项减项 生成Columns并拼装
        const funExpenseItem = (elements, detailedColumnsItem, DetailItems) => {
            elements.forEach((element) => {
                if (detailedColumnsItem?.title) {
                    const colFilterInclude =
                        detailedColumnsItem?.children.filter((item) => {
                            return item.dataIndex == element.expenseName + element.expenseType
                        }) || []
                    const colFilterExcept =
                        detailedColumnsItem?.children.filter((item) => {
                            return item.dataIndex != element.expenseName + element.expenseType
                        }) || []

                    const currentFilterData = elements.filter((el) => {
                        return el.expenseName + el.expenseType == element.expenseName + element.expenseType
                    })

                    if (colFilterInclude.length < currentFilterData.length) {
                        detailedColumnsItem.children = [
                            ...colFilterExcept,
                            ...currentFilterData.map((el) => {
                                return {
                                    title: el.expenseName,
                                    dataIndex: el.expenseName + el.expenseType,
                                    align: 'center',
                                    width: 200,
                                }
                            }),
                        ]
                    }
                } else {
                    detailedColumnsItem = {
                        title:
                            element.expenseType == 1
                                ? '工资增项'
                                : element.expenseType == 2
                                ? '工资减项'
                                : element.expenseType == 3
                                ? '其他费用项'
                                : element.expenseType == 28
                                ? '收费项'
                                : element.expenseType == 29
                                ? '退费项'
                                : '',
                        dataIndex: '',
                        align: 'center',
                        children:
                            elements.map((item) => {
                                return {
                                    title: item.expenseName,
                                    dataIndex: item.expenseName + item.expenseType,
                                    align: 'center',
                                    width: 200,
                                }
                            }) || [],
                    }
                }
            })
            if (detailedColumnsItem?.children)
                detailedColumnsItem?.children.forEach((el) => {
                    let temp = elements.find((ele) => {
                        return el.dataIndex == ele.expenseName + ele.expenseType
                    })
                    DetailItems[el.dataIndex] = temp ? temp?.amount : 0
                })
            return detailedColumnsItem
        }

        // 动态表格
        const detailedColumns2And3 = ref<any>([])
        const reviewChoiceDTO = ref<any[]>([])
        //生成账单明细详情
        const getDetailedColumns = (proportionObj) => {
            //拼接序号
            let detailedColumns1: any[] = [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    sorter: false,
                    customRender: ({ index }) => {
                        return h('span', index + 1)
                    },
                    width: 80,
                    fixed: 'left',
                },
            ]

            let haierDetailColumns: any[] = [
                {
                    title: '企业税费',
                    dataIndex: 'enterpriseTax',
                    align: 'center',
                    sorter: false,
                    width: 120,
                    customRender: ({ text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '其他',
                    dataIndex: 'specialOther',
                    align: 'center',
                    sorter: false,
                    width: 120,
                    customRender: ({ text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '会费',
                    dataIndex: 'membershipFee',
                    align: 'center',
                    sorter: false,
                    width: 120,
                    customRender: ({ text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
            ]
            //增项Columns值
            let detailedColumns2: any = {}
            //减项 Columns值
            let detailedColumns3: any = {}
            //其他费用项 由后面生成Columns，此处只存储值
            let detailedColumns4: any = {}
            //收费项
            let detailedColumns5: any = {}
            //退费项
            let detailedColumns6: any = {}

            //选择客户下的所有子客户 以id为键
            let hrClientDTO = {}
            currentValue.value?.hrClientDTO.forEach((item) => {
                hrClientDTO[item.id] = item
            })
            //所需扩展的数量
            let clientIndex = 0

            let showLastItemclientIndex: any = 0
            let showLastItem = true

            //table数据
            let tableData: inObject[] = []
            // 获取选择出来的key值
            currentValue.value?.hrBillDetailDTO.forEach((item, indexes) => {
                let client: inObject = funClient(item.clientId, hrClientDTO)
                if (client.index > clientIndex) clientIndex = client.index
                let DetailItemsDTO = {}

                //费用项
                // 工资增项 1
                detailedColumns2 = funExpenseItem(item.wageIncrease, detailedColumns2, DetailItemsDTO)
                // 工资减项 2
                detailedColumns3 = funExpenseItem(item.wageDeduction, detailedColumns3, DetailItemsDTO)
                // 其他费用项 3
                detailedColumns4 = funExpenseItem(item.otherExpenses, detailedColumns4, DetailItemsDTO)
                // 收费项 28
                detailedColumns5 = funExpenseItem(item.chargeItems, detailedColumns5, DetailItemsDTO)
                // 退费项 29
                detailedColumns6 = funExpenseItem(item.refundItems, detailedColumns6, DetailItemsDTO)

                tableData.push({ ...item, ...client.clients, ...DetailItemsDTO, hierarchyIndex: client.index })

                if (indexes > 0) {
                    if (client.index != showLastItemclientIndex) {
                        showLastItem = false
                    }
                } else {
                    showLastItemclientIndex = client.index
                }
            })

            //防止vue过度监听
            detailedTableData.value = tableData.sort((a, b) => {
                if (a.hierarchyIndex == b.hierarchyIndex) {
                    return a.clientId <= b.clientId ? 1 : -1
                }
                return a.hierarchyIndex - b.hierarchyIndex
            })
            filterTable.value = detailedTableData.value

            if (showLastItem) {
                if (clientIndex > 0) clientIndex--
            }
            //因数据是拼接而将将其分成三份，代表三个位置
            let DetailedColumnsInitial = getDetailedColumnsInitial(proportionObj, detailChange)
            // 工资增项
            if (detailedColumns2?.title) {
                detailedColumns2And3.value.push(detailedColumns2)
            }
            // 工资减项
            if (detailedColumns3?.title) {
                detailedColumns2And3.value.push(detailedColumns3)
            }
            // 其他费用项
            if (detailedColumns4?.title) {
                detailedColumns2And3.value.push(detailedColumns4)
            }
            // 收费项
            if (detailedColumns5?.title) {
                detailedColumns2And3.value.push(detailedColumns5)
            }
            // 退费项
            if (detailedColumns6?.title) {
                detailedColumns2And3.value.push(detailedColumns6)
            }
            //clientType 客户类型 0普通客户 1外包客户
            if (currentValue?.value.clientType != 1) {
                DetailedColumnsInitial[2].forEach((el, index) => {
                    if (el.dataIndex == 'taxationFee') {
                        DetailedColumnsInitial[2].splice(index, 1)
                    }
                })
            }

            detailedColumns.value = [
                ...detailedColumns1,
                // 姓名 身份证
                ...DetailedColumnsInitial[0],

                ...detailedColumns2And3.value,
                //社保公积金
                ...DetailedColumnsInitial[1],

                // //总计
                ...DetailedColumnsInitial[2],
            ]
            if (currentValue.value?.billType == 2) {
                detailedColumns.value = [
                    ...detailedColumns1,
                    ...DetailedColumnsInitial[0],
                    ...detailedColumns2And3.value,
                    ...DetailedColumnsInitial[1],
                    ...haierDetailColumns,
                    ...DetailedColumnsInitial[2],
                ]
            }
            if (currentValue.value?.reviewChoiceDTOList?.length > 0) {
                reviewChoiceDTO.value = currentValue.value?.reviewChoiceDTOList.map((el) => el.key)
                let arr: any = []
                detailedColumns.value.map((el, index) => {
                    if (index > 2) {
                        if (!el.children) {
                            if (reviewChoiceDTO.value.includes(el.dataIndex)) {
                                arr.push(el)
                            }
                        } else {
                            el.children = el.children?.filter((item) => {
                                if (reviewChoiceDTO.value.includes(item.dataIndex)) {
                                    return item
                                }
                            })
                            if (el.children.length != 0) {
                                arr.push(el)
                            }
                        }
                    } else {
                        arr.push(el)
                    }
                    return el
                })
                detailedColumns.value = arr
                detailedColumns.value.forEach((element, eleIndex) => {
                    if (element.dataIndex == 'salary' && !reviewChoiceDTO.value.includes('salary')) {
                        detailedColumns.value.splice(eleIndex, 1)
                    }
                })
                // detailedColumns.value = [...detailedColumns1, ...detailedColumns.value]
                if (dinamicHeader.value.length != 0) {
                    let Carsdarr: any = []
                    dinamicHeader.value.map((it, index) => {
                        if (!it.children) {
                            if (reviewChoiceDTO.value.includes(it.dataIndex)) {
                                Carsdarr.push(it)
                            }
                        } else {
                            it.children = it.children?.filter((ele) => {
                                if (reviewChoiceDTO.value.includes(ele.dataIndex)) {
                                    return ele
                                }
                            })
                            if (it.children.length != 0) {
                                Carsdarr.push(it)
                            }
                        }
                        return it
                    })
                    console.log('cccccccccc', Carsdarr)
                    let hasReplaced = false

                    for (let index = 0; index < detailedColumns.value.length; index++) {
                        const item = detailedColumns.value[index]
                        if ((item.dataIndex === 'status' || item.dataIndex === 'status1') && !hasReplaced) {
                            dinamicHeader.value = Carsdarr
                            if (Carsdarr.length === 1) {
                                detailedColumns.value.splice(index, 1, ...Carsdarr)
                            } else {
                                detailedColumns.value.splice(index, 2, ...Carsdarr)
                            }
                            hasReplaced = true
                        }
                    }

                }
            }
            setTimeout(() => {
                // 这个组件渲染有些问题 有些地方需要重绘
                showTable.value = true
            }, 300)
            //下载附件
            uploadFile(
                detailedColumns.value,
                detailedTableData.value?.map((item, index) => {
                    return { ...item, index: index + 1 }
                }),
                'detailed',
            )
        }
        const detailChange = (val, idx, key, type = 'number') => {
            let oldVal = detailedTableData.value[idx][key]
            detailedTableData.value[idx][key] = type == 'number' ? round(Number(val), 2) : val?.target?.value
            detailedTableData.value[idx].total = plus(
                minus(detailedTableData.value[idx].total, oldVal),
                detailedTableData.value[idx][key],
            )
            if (key == 'serviceFee') {
                summaryTableData.value[0].serviceFeeTotal = plus(
                    minus(summaryTableData.value[0].serviceFeeTotal, oldVal),
                    detailedTableData.value[idx][key],
                )
            } else if (key == 'taxationFee') {
                summaryTableData.value[0].taxationFeeTotal = plus(
                    minus(summaryTableData.value[0].taxationFeeTotal, oldVal),
                    detailedTableData.value[idx][key],
                )
            }
            summaryTableData.value[0].total = plus(
                minus(summaryTableData.value[0].total, oldVal),
                detailedTableData.value[idx][key],
            )
            detailedTableData.value = [...detailedTableData.value]
            summaryTableData.value = [...summaryTableData.value]
        }
        const uploadFile = (Columns, TableData, type) => {
            return new Promise(async (resolve, reject) => {
                let name =
                    currentValue?.value?.clientName +
                    ' ' +
                    feeReviewDate.value +
                    ' ' +
                    (type == 'detailed' ? '账单明细' : '汇总账单')
                let file
                if (type == 'detailed') {
                    file = await settlementSheetToExport(
                        Columns,
                        detailedColumns2And3.value,
                        TableData,
                        name,
                        currentValue?.value?.clientName + feeReviewDate.value + '费用明细',
                    )
                } else if (type == 'summary') {
                    file = exportTable(Columns, TableData, name, { isDown: false })
                } else if (type == 'sinopecSummary') {
                    file = await multiSheetToExport(
                        [
                            {
                                title: '当前年月',
                                dataIndex: 'currentYear',
                                width: 100,
                                align: 'center',
                            },
                            ...summaryColumns.value,
                        ],
                        [
                            {
                                ...totalData.value,
                                ...dynamicTotalData.value,
                                hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                            },
                        ],
                        `${currentValue?.value?.clientName} ${feeReviewDate.value} 汇总账单`,
                        false,
                    )
                    return
                } else if (type == 'sinopecInlandSummary') {
                    file = await multiSheetToExport(
                        [
                            {
                                title: '当前年月',
                                dataIndex: 'currentYear',
                                width: 100,
                                align: 'center',
                            },
                            ...summaryColumns.value,
                        ],
                        [
                            {
                                ...foreignTotalData.value,
                                ...dynamicTotalData.value,
                                hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                            },
                        ],
                        `${currentValue?.value?.clientName} ${feeReviewDate.value} 国内汇总账单`,
                        false,
                    )
                    return
                } else {
                    file = await multiSheetToExport(
                        detailedColumns.value.slice(1, detailedColumns.value.length),
                        [
                            {
                                ...detailedTableData.value[0],
                                hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                            },
                            ...detailedTableData.value.slice(1, detailedTableData.value.length),
                        ],
                        `${currentValue?.value?.clientName} ${feeReviewDate.value} 账单明细`,
                        false,
                    )
                }

                let FileData = new window.File([file], name + '.xlsx', {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })
                let fileformdata = new FormData() // 创建一个form类型的数据
                fileformdata.append('file', FileData) // 获取上传文件的数据
                request
                    .post('/api/hr-appendixes/upload-single-file', fileformdata)
                    .then((res) => {
                        resolve({ ...res, type })
                        if (type == 'detailed') {
                            detailPdfAppendix.value = res
                            detailsAppendix.value = res
                        } else {
                            summaryAppendix.value = res
                        }
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }
        const formatBillDetailItemsList = (list) => {
            const keys = [
                'employeeCompensationList',
                'individualHousingFundList',
                'individualIncomeTaxList',
                'individualInsuranceList',
                'managementExpenseList',
                'otherExpenseList',
                'reimbursementExpenseList',
                'taxesList',
                'totalExpensesList',
                'unitHousingFundList',
                'unitInsuranceList',
            ]
            let arr: any = []
            keys.forEach((el) => {
                arr = arr.concat([...list[el]])
            })
            return arr
        }

        /**
         * 中石化结算单导出
         * @param typeName
         */
        const exportSinopecInlandBill = async (typeName) => {
            if (typeName == 'sinopecInlandSummary') {
                if (viewType.value == 'add') {
                    typeName == 'sinopecInlandSummary' &&
                        (await uploadFile(
                            summaryColumns.value,
                            [
                                {
                                    ...dynamicTotalData,
                                    ...foreignTotalData,
                                    hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                                },
                            ],
                            typeName,
                        ))
                }
            }
        }

        /**
         * 结算单导出
         * @param typeName
         * @param download
         * @param fileType
         */
        const exportBill = async (typeName, download = false, fileType?) => {
            if (typeName == 'summary' || typeName == 'sinopecSummary') {
                if (viewType.value == 'add') {
                    typeName == 'summary' && (await uploadFile(summaryColumns.value, summaryTableData.value, typeName))
                    typeName == 'sinopecSummary' &&
                        (await uploadFile(
                            summaryColumns.value,
                            [
                                {
                                    ...dynamicTotalData,
                                    ...totalData,
                                    hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                                },
                            ],
                            typeName,
                        ))
                }
                if (viewType.value == 'see') {
                    typeName == 'sinopecSummary' &&
                        (await uploadFile(
                            summaryColumns.value,
                            [
                                {
                                    ...dynamicTotalData,
                                    ...totalData,
                                    hrBillDetailItemsList: formatBillDetailItemsList(detailedTableData.value[0]),
                                },
                            ],
                            typeName,
                        ))
                }
                if (summaryAppendix.value.fileUrl) {
                    download
                        ? downFile('get', summaryAppendix.value.fileUrl, summaryAppendix.value.originName, {})
                        : previewFile(summaryAppendix.value.fileUrl)
                } else {
                    message.error('暂无下载内容')
                }
            } else {
                if (viewType.value == 'add') {
                    typeName == 'detailed' &&
                        (await uploadFile(
                            detailedColumns.value,
                            detailedTableData.value?.map((item, index) => {
                                return { ...item, index: index + 1 }
                            }),
                            typeName,
                        ))
                    if (typeName == 'sinopecDetailed') {
                        await uploadFile(detailedColumns.value, detailedTableData.value, typeName)
                        return
                    }
                }
                if (detailsAppendix.value.fileUrl && viewType.value == 'add') {
                    download
                        ? downFile('get', detailsAppendix.value.fileUrl, detailsAppendix.value.originName, {})
                        : previewFile(detailsAppendix.value.fileUrl)
                }
                if (viewType.value != 'add') {
                    let fileUrls: any = []
                    let originNames: any = []
                    if (detailPdfAppendix.value.fileUrl && detailPdfAppendix.value.fileUrl) {
                        if (detailsAppendix.value.fileUrl) {
                            fileUrls.push(detailsAppendix.value.fileUrl)
                            originNames.push(detailsAppendix.value.originName)
                        }
                        if (detailPdfAppendix.value.fileUrl) {
                            fileUrls.push(detailPdfAppendix.value.fileUrl)
                            originNames.push(detailPdfAppendix.value.originName)
                        }
                        download
                            ? downMultFile(
                                  `${currentValue?.value?.clientName + ' ' + feeReviewDate.value}`,
                                  fileUrls,
                                  originNames,
                              )
                            : previewFile(fileType == 'exls' ? detailsAppendix.value.fileUrl : detailPdfAppendix.value.fileUrl)
                    } else if (detailsAppendix.value.fileUrl && detailPdfAppendix.value.fileUrls == null) {
                        download
                            ? downFile('get', detailsAppendix.value.fileUrl, detailsAppendix.value.originName, {})
                            : previewFile(detailsAppendix.value.fileUrl)
                    }
                }
            }
        }

        const downloadFil = (item) => {
            downFile('get', item.fileUrl, item.originName, {})
        }
        const downloadFilAll = (item) => {
            if (currentValue.value?.appendixIdList?.length) {
                currentValue.value?.appendixIdList
                downMultFile(
                    `${currentValue?.value?.clientName + ' ' + feeReviewDate.value} 费用审核附件`,
                    currentValue.value?.appendixIdList?.map((i) => i.fileUrl),
                    currentValue.value?.appendixIdList?.map((i) => i.originName),
                )
            } else {
                message.error('暂无下载数据')
            }
        }

        const cancel = () => {
            showTable.value = false
            search.value = {
                name: '',
                certificateNum: '',
            }
            emit('update:visible', false)
        }
        const dataLock = async () => {
            cancel()
        }
        const beforeConfirm = async () => {
            try {
                if (viewType.value == 'add') {
                    await uploadFile(summaryColumns.value, summaryTableData.value, 'summary')
                    await uploadFile(
                        detailedColumns.value,
                        detailedTableData.value?.map((item, index) => {
                            return { ...item, index: index + 1 }
                        }),
                        'detailed',
                    )
                }
            } catch (error) {
                console.log(error)
            }
        }

        /**
         * 刷新账单明细
         */
        const reloadBillExport = async () => {
            if (viewType.value == 'see' || viewType.value == 'examine') {
                let proportionObj = billType.value != 3 ? getProportionObj(currentValue.value?.hrBillDTO || []) : []
                let detailedColumns1: any[] = [
                    {
                        title: '序号',
                        dataIndex: 'index',
                        align: 'center',
                        sorter: false,
                        customRender: ({ index }) => {
                            return h('span', index + 1)
                        },
                        width: 80,
                        fixed: 'left',
                    },
                ]

                let haierDetailColumns: any[] = [
                    {
                        title: '企业税费',
                        dataIndex: 'enterpriseTax',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '其他',
                        dataIndex: 'specialOther',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '会费',
                        dataIndex: 'membershipFee',
                        align: 'center',
                        sorter: false,
                        width: 120,
                        customRender: ({ text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                ]
                //增项Columns值
                let detailedColumns2: any = {}
                //减项 Columns值
                let detailedColumns3: any = {}
                //其他费用项 由后面生成Columns，此处只存储值
                let detailedColumns4: any = {}
                //收费项
                let detailedColumns5: any = {}
                //退费项
                let detailedColumns6: any = {}

                //选择客户下的所有子客户 以id为键
                let hrClientDTO = {}
                currentValue.value?.hrClientDTO.forEach((item) => {
                    hrClientDTO[item.id] = item
                })
                //所需扩展的数量
                let clientIndex = 0

                let showLastItemclientIndex: any = 0
                let showLastItem = true

                //table数据
                let tableData: inObject[] = []
                // 获取选择出来的key值
                currentValue.value?.hrBillDetailDTO.forEach((item, indexes) => {
                    let client: inObject = funClient(item.clientId, hrClientDTO)
                    if (client.index > clientIndex) clientIndex = client.index
                    let DetailItemsDTO = {}

                    //费用项
                    // 工资增项 1
                    detailedColumns2 = funExpenseItem(item.wageIncrease, detailedColumns2, DetailItemsDTO)
                    // 工资减项 2
                    detailedColumns3 = funExpenseItem(item.wageDeduction, detailedColumns3, DetailItemsDTO)
                    // 其他费用项 3
                    detailedColumns4 = funExpenseItem(item.otherExpenses, detailedColumns4, DetailItemsDTO)
                    // 收费项 28
                    detailedColumns5 = funExpenseItem(item.chargeItems, detailedColumns5, DetailItemsDTO)
                    // 退费项 29
                    detailedColumns6 = funExpenseItem(item.refundItems, detailedColumns6, DetailItemsDTO)

                    tableData.push({ ...item, ...client.clients, ...DetailItemsDTO, hierarchyIndex: client.index })

                    if (indexes > 0) {
                        if (client.index != showLastItemclientIndex) {
                            showLastItem = false
                        }
                    } else {
                        showLastItemclientIndex = client.index
                    }
                })

                //防止vue过度监听
                currentValue.value.hrBillDetailDTO = tableData.sort((a, b) => {
                    if (a.hierarchyIndex == b.hierarchyIndex) {
                        return a.clientId <= b.clientId ? 1 : -1
                    }
                    return a.hierarchyIndex - b.hierarchyIndex
                })
                filterTable.value = currentValue.value?.hrBillDetailDTO

                if (showLastItem) {
                    if (clientIndex > 0) clientIndex--
                }
                //因数据是拼接而将将其分成三份，代表三个位置
                let DetailedColumnsInitial = getDetailedColumnsInitial(proportionObj, detailChange)
                // 工资增项
                if (detailedColumns2?.title) {
                    detailedColumns2And3.value.push(detailedColumns2)
                }
                // 工资减项
                if (detailedColumns3?.title) {
                    detailedColumns2And3.value.push(detailedColumns3)
                }
                // 其他费用项
                if (detailedColumns4?.title) {
                    detailedColumns2And3.value.push(detailedColumns4)
                }
                // 收费项
                if (detailedColumns5?.title) {
                    detailedColumns2And3.value.push(detailedColumns5)
                }
                // 退费项
                if (detailedColumns6?.title) {
                    detailedColumns2And3.value.push(detailedColumns6)
                }
                //clientType 客户类型 0普通客户 1外包客户
                if (currentValue?.value.clientType != 1) {
                    DetailedColumnsInitial[2].forEach((el, index) => {
                        if (el.dataIndex == 'taxationFee') {
                            DetailedColumnsInitial[2].splice(index, 1)
                        }
                    })
                }

                detailedColumns.value = [
                    ...detailedColumns1,
                    // 姓名 身份证
                    ...DetailedColumnsInitial[0],

                    ...detailedColumns2And3.value,
                    //社保公积金
                    ...DetailedColumnsInitial[1],

                    // //总计
                    ...DetailedColumnsInitial[2],
                ]
                if (currentValue.value?.billType == 2) {
                    detailedColumns.value = [
                        ...detailedColumns1,
                        ...DetailedColumnsInitial[0],
                        ...detailedColumns2And3.value,
                        ...DetailedColumnsInitial[1],
                        ...haierDetailColumns,
                        ...DetailedColumnsInitial[2],
                    ]
                }
                if (currentValue.value?.reviewChoiceDTOList?.length > 0) {
                    reviewChoiceDTO.value = currentValue.value?.reviewChoiceDTOList.map((el) => el.key)
                    let arr: any = []
                    detailedColumns.value.map((el, index) => {
                        if (index > 2) {
                            if (!el.children) {
                                if (reviewChoiceDTO.value.includes(el.dataIndex)) {
                                    arr.push(el)
                                }
                            } else {
                                el.children = el.children?.filter((item) => {
                                    if (reviewChoiceDTO.value.includes(item.dataIndex)) {
                                        return item
                                    }
                                })
                                if (el.children.length != 0) {
                                    arr.push(el)
                                }
                            }
                        } else {
                            arr.push(el)
                        }
                        return el
                    })
                    detailedColumns.value = arr
                    detailedColumns.value.forEach((element, eleIndex) => {
                        if (element.dataIndex == 'salary' && !reviewChoiceDTO.value.includes('salary')) {
                            detailedColumns.value.splice(eleIndex, 1)
                        }
                    })
                    // detailedColumns.value = [...detailedColumns1, ...detailedColumns.value]
                    if (dinamicHeader.value.length != 0) {
                    let Carsdarr: any = []
                    dinamicHeader.value.map((it, index) => {
                        if (!it.children) {
                            if (reviewChoiceDTO.value.includes(it.dataIndex)) {
                                Carsdarr.push(it)
                            }
                        } else {
                            it.children = it.children?.filter((ele) => {
                                if (reviewChoiceDTO.value.includes(ele.dataIndex)) {
                                    return ele
                                }
                            })
                            if (it.children.length != 0) {
                                Carsdarr.push(it)
                            }
                        }
                        return it
                    })
                    let hasReplaced = false // Flag to check if replacement has occurred

                    for (let index = 0; index < detailedColumns.value.length; index++) {
                        const item = detailedColumns.value[index]
                        if ((item.dataIndex === 'status' || item.dataIndex === 'status1') && !hasReplaced) {
                            dinamicHeader.value = Carsdarr
                            if (Carsdarr.length === 1) {
                                detailedColumns.value.splice(index, 1, ...Carsdarr)
                            } else {
                                detailedColumns.value.splice(index, 2, ...Carsdarr)
                            }
                            hasReplaced = true // Set flag to true after replacement
                        }
                    }

                    console.log('detailedColumns', detailedColumns.value)
                }
                }
            }
            const fileObj: any = await uploadFile(
                detailedColumns.value,
                currentValue.value.hrBillDetailDTO?.map((item, index) => {
                    return { ...item, index: index + 1 }
                }),
                'detailed',
            )
            detailPdfAppendix.value = {}
            detailsAppendix.value = {}
            await request
                .post(`/api/internal/handle-fee-review-excel-pdf`, {
                    id: currentValue.value?.id,
                    detailAppendixId: fileObj.id,
                })
                .then((res) => {
                    typeStatus.value = true
                    if (res?.detailPdfAppendixUrl) {
                        detailPdfAppendix.value = res?.detailPdfAppendixUrl[0]
                    }
                    detailsAppendix.value = res?.detailAppendixIdUrl[0]
                })
        }
        const confirm = () => {
            let { title, isDrawBill, remark } = formData.value
            let appendixIdList = refImportFile.value?.getFileUrls().map((item) => {
                return item.id
            })
            formInline.value
                .validate()
                .then(async () => {
                    confirmLoading.value = true
                    if (true) {
                        await beforeConfirm()
                        saveHandler()
                    }
                    function saveHandler() {
                        request
                            .post('/api/hr-fee-reviews/preservation-settlement-document-info', {
                                remark,
                                appendixId: appendixIdList.join(),
                                title,
                                isDrawBill,
                                feeReviewDate: feeReviewDate.value,
                                clientId: currentValue.value?.clientId,
                                billIdList: currentValue.value?.billIdList,
                                hrBillTotalDTO:
                                    billType.value == 3
                                        ? {
                                              ...totalData,
                                              total: totalData.value.totalFee,
                                              totalOccurrence: totalData.value.happenTotalFee,
                                          }
                                        : summaryTableData.value[0],
                                hrBillTotalDTOList:
                                    billType.value == 3
                                        ? [
                                              {
                                                  ...totalData,
                                                  total: totalData.value.totalFee,
                                                  totalOccurrence: totalData.value.happenTotalFee,
                                              },
                                              {
                                                  ...foreignTotalData,
                                                  total: foreignTotalData.value.totalFee,
                                                  totalOccurrence: foreignTotalData.value.happenTotalFee,
                                              },
                                          ]
                                        : [],
                                hrBillDetailDTO: detailedTableData.value,
                                detailAppendixId: detailsAppendix.value?.id,
                                summaryAppendixId: summaryAppendix.value?.id,
                                clientType: currentValue.value?.clientType,
                                reviewChoiceDTOList: summaryDynimicHeader.value,
                            })
                            .then(() => {
                                message.success('添加成功')
                                cancel()
                                emit('confirm', 1)
                            })
                            .finally(() => {
                                confirmLoading.value = false
                            })
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }
        const refuseOrAdopt = (type) => {
            let { refuseRemark, isDrawBill } = formData.value
            if (type == 2) {
                if (!refuseRemark) {
                    message.warning('请填写拒绝理由')
                    return false
                }
            }
            let api = '/api/hr-fee-reviews'
            request
                .put(api, {
                    status: type,
                    isDrawBill,
                    refuseRemark,
                    id: currentValue.value?.id,
                })
                .then((res) => {
                    cancel()
                    emit('confirm')
                })
        }
        const serviceUnifyUpdate = () => {
            serviceForm.value
                .validate()
                .then(async () => {
                    const oldTotalVal = summaryTableData.value[0].serviceFeeTotal
                    detailedTableData.value.forEach((el) => {
                        const oldVal = el.serviceFee
                        el.serviceFee = formData.value.serviceFeeEvery
                        el.total = plus(minus(el.total, oldVal), el.serviceFee)
                    })
                    summaryTableData.value[0].serviceFeeTotal = detailedTableData.value
                        .map((i) => i.serviceFee)
                        .reduce((pre, cur) => plus(pre, cur), 0)
                    summaryTableData.value[0].total = plus(
                        minus(summaryTableData.value[0].total, oldTotalVal),
                        summaryTableData.value[0].serviceFeeTotal,
                    )
                    serviceCancel()
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }
        const serviceCancel = () => {
            formData.value.serviceFeeEvery = 0
            serviceVisible.value = false
        }

        const showTable = ref(false)
        return {
            search,
            filterTable,
            filterTableData,
            dynamicTotalData,
            totalData,
            billType,
            serviceForm,
            serviceVisible,
            serviceCancel,
            serviceUnifyUpdate,
            getPopupContainer: () => {
                return document.body
            },
            refImportFile,
            feeReviewDate,

            dataLock,

            cancel,
            confirm,

            staffTypeOptions,
            detailedTableData,
            reloadBillExport,

            detailedColumns,
            // currentValue,
            exportSinopecInlandBill,
            exportBill,
            downloadFil,
            downloadFilAll,

            summaryColumns,
            summaryTableData,
            formData,

            formInline,
            detailsAppendix,
            //审核
            refuseOrAdopt,
            feeReviewStatus,

            showTable,
            confirmLoading,
            detailPdfAppendix,
            columnsDataChecked,
            foreignTotalData,
            userInfo,
            typeStatus,
        }
    },
})
</script>
<style scoped lang="less">
.search-wrapper {
    display: flex;
    margin-bottom: 10px;
    & > input {
        width: 230px;
        margin-right: 20px;
    }
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 33.33%;
        .ant-form-item-control {
            width: calc(100% - 120px) !important;
        }
    }
    :deep(.ant-form-item-control-input-content) {
        display: flex;
    }
}
.label {
    // width: 95px;
    display: inline-block;
    text-align: right;
    padding-right: 8px;
}
.detail-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid @primary-color;
    padding: 0 40px 0 10px;
    .icon,
    .anticon {
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        margin-left: 10px;
    }
}
.title {
    border-left: 5px solid @primary-color;
    padding-left: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .icon {
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        margin-left: 10px;
    }
}
.seeInfoBox {
    width: 100%;
    padding: 15px 15px 30px;
    & > div {
        line-height: 38px;
        display: inline-block;
        width: 100%;
        &.wtwenty {
            width: 20%;
            .overflow {
                overflow: hidden;
                text-overflow: ellipsis;
                width: calc(100% - 100px);
                white-space: nowrap;
                vertical-align: bottom;
            }
        }
        p {
            display: inline-block;
            color: #333;
        }
        p:first-child {
            color: #999999;
        }
    }
    .hrAppendixListBox {
        display: block;
        // display: inline-block;
        padding-right: 10px;
        .enclosure {
            line-height: 26px;
            color: @primary-color;
            display: inline-block;
            cursor: pointer;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: middle;

            &:hover {
                background: #ddd;
            }
        }
    }
    .required {
        margin: 0 5px;
        color: red;
    }
}
.smallTable {
    width: 100%;

    // border: 1px solid #e8e8e8;
    :deep(.ant-table-thead > tr > th) {
        // background-color: #fafafa;
        color: #000;
    }
}
:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
    color: #000;
    background: #fff;
}
.ant-dropdown-link {
    min-height: 18px;
    display: inline-block;
}
:deep(.surely-table-cell-inner .surely-table-cell-content) {
    padding: 10px;
}
</style>
