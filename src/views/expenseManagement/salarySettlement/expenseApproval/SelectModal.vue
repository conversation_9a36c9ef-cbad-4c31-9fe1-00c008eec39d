<template>
    <BasicEditModalSlot
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="nextType == 1 ? '选择子集客户' : nextType == 2 ? '账单选择' : '结算单模板'"
        :width="nextType == 4 || nextType == 3 ? '1600px' : '800px'"
    >
        <div class="container">
            <Table
                class="basicTable"
                v-if="nextType == 1"
                ref="clientTableRef"
                :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                size="small"
                bordered
                :indentSize="30"
                :scroll="{ x: '100' }"
                :columns="clientColumns"
                :data-source="clientList"
                :row-key="(record) => record.id"
                :pagination="false"
                :row-selection="clientSelectionRowConfig"
            />
            <Table
                class="basicTable"
                v-if="nextType == 2"
                ref="billTableRef"
                :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                size="small"
                bordered
                :indentSize="30"
                :scroll="{ x: '100' }"
                :data-source="billList"
                :pagination="false"
                :columns="billColumns"
                :loading="billTableLoading"
                :row-key="(record) => record.id"
                :row-selection="billSelectionRowConfig"
            />
            <div v-show="nextType == 3" class="expenseApprovalTipBox">
                <div class="modalMain">
                    <div class="col">
                        <div class="label" :style="{ width: title.length > 4 ? '150px' : '' }">{{ title }}</div>
                        <div class="val">
                            <Upload :beforeUpload="beforeUpload" :showUploadList="false">
                                <Button type="primary" style="margin-left: 10px">
                                    <template #icon>
                                        <UploadOutlined />
                                    </template>
                                    上传{{ title }}
                                </Button>
                            </Upload>
                        </div>
                    </div>
                </div>
                <div class="col" style="margin: 10px 0">
                    <div class="label" :style="{ width: title.length > 4 ? '150px' : '' }">数据开始行</div>
                    <div class="val">
                        <InputNumber
                            v-model:value="startRow"
                            placeholder="数据开始行"
                            :min="1"
                            @blur="changeStartRow"
                            style="width: 110px"
                        />
                    </div>
                </div>
                <div class="title">原始表单</div>
                <Table
                    :dataSource="tableData"
                    :columns="tableColumns"
                    :bordered="true"
                    :rowKey="(record) => record.id"
                    size="small"
                    :pagination="false"
                    :scroll="{
                        x: '100',
                    }"
                    style="width: 100%"
                />
                <div class="cell" style="margin-top: 20px">
                    <div class="title">表单映射</div>
                    <div class="main">
                        <div class="table">
                            <div class="headerCols">
                                <div class="th" v-for="i in resColumns" :key="i.title" :style="{ width: i.cols * 100 + 'px' }">
                                    {{ i.title }}
                                </div>
                            </div>
                            <div class="bodyCols">
                                <div class="tr" v-for="(j, idx) in resData" :key="idx">
                                    <div class="tb" v-for="(v, index) in j" :key="v">
                                        <a v-if="idx == 1 && v.value != '表单字段'" @click="removeField(v, index)">
                                            {{ v.value }}
                                        </a>
                                        <span v-else> {{ v.value }} </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <Button type="primary" v-show="nextType == 3" @click="selectedAll">全部选择</Button> -->
            <Alert v-if="nextType == 4" message="如果都不选择,默认全部选中" type="warning" />
            <div class="StableBox" :style="nextType == 4 ? 'height:250px' : 'auto'">
                <STable
                    class="basicTable"
                    v-if="nextType == 4"
                    :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                    :columns="detailedColumns"
                    :pagination="false"
                    bordered
                >
                    <template #headerCell="{ column }">
                        <span>
                            {{ column.title }}
                            &nbsp;
                            <span
                                v-if="
                                    column.dataIndex != 'status' &&
                                    column.dataIndex != 'index' &&
                                    column.dataIndex != 'certificateNum' &&
                                    column.dataIndex != 'name' &&
                                    column.dataIndex != 'paymentDate' &&
                                    column.dataIndex != 'status1' &&
                                    column.dataIndex != 'unitPayment' &&
                                    column.dataIndex != 'personalPayment' &&
                                    column.dataIndex != 'status2' &&
                                    column.dataIndex != 'personalCardinal' &&
                                    column.dataIndex != 'unitCardinal' &&
                                    column.dataIndex != 'currentYear' &&
                                    column.dataIndex != 'projectCode' &&
                                    column.dataIndex != 'projectName' &&
                                    column.dataIndex != 'peopleNum' &&
                                    column.dataIndex != '发放的职工薪酬' &&
                                    column.dataIndex != '其中' &&
                                    column.dataIndex != 'otherFee' &&
                                    column.dataIndex != ''
                                "
                            >
                                <Checkbox
                                    v-model:checked="checkDetailData[column.dataIndex]"
                                    @change="(e) => checkedChange(e, column)"
                                />
                            </span>
                        </span>
                    </template>
                </STable>
            </div>
        </div>
        <template #footer>
            <div class="foot_btn" :style="{ justifyContent: nextType == 1 ? 'flex-end' : '' }">
                <Button
                    type="primary"
                    v-if="(nextType == 2 && clientList?.length) || nextType == 4 || (nextType == 3 && reviewFlags == 1)"
                    @click="lastStep(nextType)"
                >
                    上一步
                </Button>
                <Button type="primary" v-if="nextType == 3" @click="modalConfirm">下一步</Button>
                <Button type="primary" v-if="nextType != 4 && nextType != 3" @click="nextStep(nextType)">下一步</Button>
                <Button type="primary" v-if="nextType == 4" @click="confirm" :disabled="canContinue">继续</Button>
            </div>
        </template>
    </BasicEditModalSlot>

    <!-- 提示 -->
    <BasicEditModalSlot :visible="showTip" title="提示" @cancel="tipClose" width="1000px" centered>
        <div class="tip">
            <ExclamationCircleFilled style="font-size: 18px" />
            有 {{ unuseCols.length }} 列未参与账单计算，是否继续？
        </div>
        <BasicTable
            :tableDataList="unuseColsList"
            :columns="[
                ...unuseCols.map((i, idx) => ({
                    title: idx === 0 ? '未参与账单列' : '',
                    dataIndex: idx,
                    colSpan: idx === 0 ? unuseCols.length : 0,
                    width: 120,
                })),
            ]"
            :rowSelectionShow="false"
            :sorter="false"
        />
        <template #footer>
            <Button @click="tipClose">取消</Button>
            <Button :loading="tipLoading" type="primary" @click="tipConfirm">确认</Button>
        </template>
    </BasicEditModalSlot>
    <!-- 设置费用项 -->
    <BasicEditModalSlot
        :visible="showSetExpense"
        title="设置费用项"
        @cancel="showSetExpense = false"
        @ok="setExpenseConfirm"
        width="500px"
    >
        <Form :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem label="名称">
                <Input v-model:value="setForm.expenseName" placeholder="名称" />
            </FormItem>
            <FormItem label="类型">
                <Select
                    v-model:value="setForm.expenseType"
                    placeholder="类型"
                    :options="expenseTypeList"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}
import { computed, defineComponent, ref, toRefs, watch, h } from 'vue'
import request from '/@/utils/request'
import { formatOriginForm, formatMapData } from '../billAccounting/util'
import { message, Checkbox, Upload, Modal } from 'ant-design-vue'
import { UploadOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
import { getSinopecDetailColumnsInitial, getDetailedColumnsInitial, getProportionObj } from './data'
import { minus, plus, round } from 'number-precision'
import { getFinalBillStatement } from '/@/utils/api'
export default defineComponent({
    name: 'SelectModal',
    components: { Checkbox, Upload, UploadOutlined, ExclamationCircleFilled },
    props: {
        visible: Boolean,
        clientList: Array,
        feeReviewDate: [String, Array],
        clientId: String,
        selectedClients: Array,
        isEpiboly: {
            type: Boolean,
            default: false,
        },
        reviewFlags: {
            type: Number,
            default: 0,
        },
    },
    emits: ['confirm', 'update:visible', 'dinamicHeader'],
    setup(props, { emit }) {
        //标题
        const nextType = ref(1)
        const { visible, isEpiboly, feeReviewDate, clientId, clientList, selectedClients, reviewFlags } = toRefs<any>(props)
        const billTableLoading = ref(false)
        const canContinue = ref(false)
        const clientTableRef = ref()
        const billTableRef = ref()
        const billList = ref([])
        const billIds = ref<any>()
        const columnsDataChecked = ref({})
        const startRow = ref<any>()

        // 选择行
        const selectedClientList = ref<any[]>([])
        const selectedClientKeysList = ref<any[]>([])

        const selectedBillList = ref<any[]>([])
        const selectedBillKeysList = ref<any[]>([])
        const checkDetailData = ref<any>({})
        const detailedColumns = ref<inObject[]>([])
        const slotsDynamicColumns = ref<any>([])
        const slotsArr = ref<any>([])
        const childrenPartTip = ref('')
        const billIdList = ref<string[]>([])
        const selectBillList = ref<any>({})
        const billType = ref<any>()
        const dynamicArrHeader = ref<Recordable[]>([])
        const dynamicHeaderColumns = ref<Recordable[]>([])
        const tableColumns = ref<Recordable[]>([])
        const tableData = ref<Recordable[]>([])
        const resColumns = ref<Recordable[]>([])
        const unuseCols = ref<Recordable[]>([])
        const resData = ref<Recordable[]>([])
        const dynamicFieldId = ref<Recordable>()
        const haierBillColumnsData = ref<Recordable[]>([])
        const params = computed(() => {
            return {
                clientIdList: selectedClientList.value.map((el: any) => {
                    return el.id
                }),
                feeReviewDate: feeReviewDate.value,
                // paymentDateList: isEpiboly.value ? [feeReviewDate.value] : undefined,
            }
        })
        const title = computed(() => {
            return '海尔账单'
        })
        watch(visible, () => {
            if (visible.value) {
                if (selectedClients.value && selectedClients.value.length) {
                    selectedClientList.value = clientList.value.filter((ele) => {
                        return selectedClients.value.includes(ele.id)
                    })
                    selectedClientKeysList.value = selectedClients.value
                }
                detailedColumns.value = getSinopecDetailColumnsInitial()
                if (clientList.value?.length) nextType.value = 1
                else if (reviewFlags.value == 1) {
                    nextType.value = 3
                } else nextType.value = 2

                getExpenseTypeList()
            }
        })
        const onClientSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedClientList.value = selectedRows
            selectedClientKeysList.value = selectedRowKeys
        }
        const clientSelectionRowConfig = {
            selectedRowKeys: selectedClientKeysList,
            onChange: onClientSelectChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }

        const setForm = ref({
            expenseName: undefined,
            expenseType: undefined,
            sortValue: undefined,
        })
        const showSetExpense = ref(false)
        const currentColKey = ref(undefined)
        const setExpense = (name, colKey, order) => {
            currentColKey.value = colKey
            setForm.value = {
                expenseName: name,
                expenseType: undefined,
                sortValue: order,
            }
            showSetExpense.value = true
        }
        const setExpenseConfirm = async () => {
            const res = await request.post(`/api/hr-fee-reviews/handleFieldMappingRelation/${dynamicFieldId.value}/${0}`, {
                ...setForm.value,
                clientId: clientId.value,
            })
            showSetExpense.value = false
            tableData.value[0][currentColKey.value || '-'] = setForm.value.expenseName // 改变原单数据 状态为已映射
            const { columns, data } = formatMapData(res)
            resColumns.value = columns
            resData.value = data
        }

        const removeField = (item, index) => {
            Modal.confirm({
                title: '确认',
                content: '是否删除该字段映射?',
                onOk: async () => {
                    const colKey = resData.value[0][index].value // 该列的key

                    const res = await request.post(
                        `/api/hr-fee-reviews/handleFieldMappingRelation/${dynamicFieldId.value}/${1}`,
                        {
                            expenseName: item.value,
                            sortValue: item.order,
                            clientId: clientId.value,
                        },
                    )
                    const { columns, data } = formatMapData(res)
                    resColumns.value = columns
                    resData.value = data

                    tableData.value[0][colKey] = item.value + '&' // 改变原单数据 状态为未映射
                },
            })
        }
        const beforeUpload = async (file) => {
            const form = new FormData()
            form.append('file', file)
            const res = await request.post(`/api/hr-fee-reviews/haier-origin-table-header/${clientId.value}`, form)
            dynamicFieldId.value = res.dynamicFieldId
            message.loading('解析表单中...')
            startRow.value = res.dataStartRow + 1
            const { columns: originColumns, data: originData } = formatOriginForm(res.cellItemList, setExpense)
            tableData.value = [originData]
            tableColumns.value = originColumns
            const { columns, data } = formatMapData(res.tableMappingList)
            resColumns.value = columns
            resData.value = data
            return false
        }

        const unuseColsList = computed(() => {
            let res = { id: 1 }
            unuseCols.value.forEach((i, idx) => {
                res[idx] = i.value
            })
            return [res]
        })
        const modalConfirm = async () => {
            const res = await request.get(`/api/hr-fee-reviews/getUnUsedFieldList/${dynamicFieldId.value}`)
            unuseCols.value = res
            if (res && res.length) {
                showTip.value = true
            }
        }
        const onBillSelectChange = (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
            selectedBillList.value = selectedRows
            selectedBillKeysList.value = selectedRowKeys
        }
        const billSelectionRowConfig = {
            selectedRowKeys: selectedBillKeysList,
            onChange: onBillSelectChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled,
                }
            },
        }
        const tipClose = () => {
            showTip.value = false
        }
        const expenseTypeList = ref<LabelValueOptions>([])
        const getExpenseTypeList = async () => {
            getFinalBillStatement({ billType: 4, clientId: clientId.value }).then((res) => {
                expenseTypeList.value = res as LabelValueOptions
            })
        }
        const selectedGroupName = ref<String>('')

        /**
         * 动态选择表头
         */
        const selectedHeaderText = ref<inObject[]>([])
        const checkedChange = (e, column) => {
            if (e.target?.checked) {
                selectedHeaderText.value.push({
                    title: column.title,
                    key: column.dataIndex,
                    groupName: selectedGroupName.value,
                })
                // if (selectedHeaderText.value.includes(column.dataIndex)) {
                //     selectedHeaderText.value.splice(column.dataIndex, 1)
                // }
            } else {
                selectedHeaderText.value.forEach((el, index) => {
                    if (column.dataIndex == el.key) {
                        selectedHeaderText.value.splice(index, 1)
                    }
                })
            }
        }
        /**
         * 全部选择
         */
        let summaryColumns = ref<inObject[]>([])
        const generateSinopecDynamicColumnsAndData = () => {
            // 动态列
            const detailOtherCol: any = detailedColumns.value.find((el) => el.dataIndex == 'otherFee')
            const totalOtherCol: any = summaryColumns.value.find((el) => el.dataIndex == 'totalOtherFee')
            const fixedStrArr = ['通讯积分', '差旅补助', '防暑降温费', '一次性奖']

            const firstDetail = selectBillList.value.hrBillDetailDTO[0]
            const dynamicArr: any = []
            firstDetail.otherExpenseList
                .sort((a, b) => {
                    return fixedStrArr.indexOf(b.expenseName) - fixedStrArr.indexOf(a.expenseName)
                })
                .forEach((el) => {
                    dynamicArr.push({
                        title: el.expenseName,
                        dataIndex: el.expenseName,
                        width: 175,
                        align: 'center',
                        customRender: ({ text }) => {
                            return text || 0
                        },
                    })
                })
            detailOtherCol.children = dynamicArr
        }
        const tipLoading = ref(false)
        const tipConfirm = async () => {
            try {
                // tipLoading.value = true
                showTip.value = false
                try {
                    let res: any
                    res = await request.post(`/api/hr-fee-reviews/fill-review-detail`, {
                        feeReviewDate: params.value.feeReviewDate,
                        dynamicFieldsId: dynamicFieldId.value,
                    })
                    haierBillColumnsData.value = res.hrBillDetailDTOList
                    resTableData.value = res.billTotalDTO
                    haierBillColumnsData.value.forEach((item) => {
                        item.id = uuid()
                    })
                    nextType.value = 4
                } catch {
                    nextType.value = 3
                    console.log('error')
                    return
                }
                const res = await request.post('/api/hr-fee-reviews/generate-settlement-document-info', {
                    clientId: clientId.value,
                    feeReviewDate: params.value.feeReviewDate,
                    // paymentDateList: params.value.paymentDateList,
                    billIdList: selectedBillList.value.map((el: any) => {
                        return el.id
                    }),
                })

                selectBillList.value = res
                billIdList.value = selectedBillList.value.map((el: any) => {
                    return el.id
                })
                let proportionObj = getProportionObj(selectBillList.value?.hrBillDTO || [])
                getDetailedColumns(proportionObj)
                let firstHrBillTotalDTO = res?.hrBillDTO[0] || {}
                billType.value = firstHrBillTotalDTO.billType
                const dynamicHeader = await request.post(`/api/hr-bill/dynamic/header/`, [billIds.value])
                dynamicArrHeader.value = [...dynamicMergeHeaderText(dynamicHeader)]
                if (dynamicArrHeader.value.length != 0) {
                    dynamicArrHeader.value.forEach((el) => {
                        dynamicHeaderColumns.value.push({
                            title: el.title,
                            dataIndex: el.dataIndex,
                            width: 200,
                            key: el.key,
                            align: 'center',
                            children: el.children?.map((res) => {
                                return {
                                    title: res.title,
                                    dataIndex: res.dataIndex,
                                    key: res.key,
                                    dataIndexS: res.dataIndexS,
                                    width: 'calc(60% - 250px)',
                                    align: 'center',
                                }
                            }),
                        })
                    })
                }

                const andkey = ['unitPayment', 'personalPayment']
                let dynamicHe = dynamicHeaderColumns.value?.filter(
                    (el) => el.dataIndex === andkey[0] || el.dataIndex === andkey[1],
                )
                dynamicHeaderColumns.value = dynamicHeaderColumns.value?.filter(
                    (el) => el.dataIndex != andkey[0] && el.dataIndex != andkey[1],
                )
                dynamicHeaderColumns.value.forEach((item) => {
                    detailedColumns.value.splice(5, 0, { ...item })
                })
                andkey?.forEach((el) => {
                    let item: any = detailedColumns.value?.find((item) => item.dataIndex == el) ?? {}
                    let merge: any = dynamicHe?.find((item) => item.dataIndex == el) ?? {}
                    item?.children?.forEach((row) => {
                        merge?.children?.forEach((ele) => {
                            if (row.dataIndex == ele.dataIndex) {
                                let idx = merge?.children?.findIndex((it) => it.dataIndex == ele.dataIndex)
                                merge?.children.splice(idx, 1)
                            }
                        })
                    })

                    item?.children?.length > 0 &&
                        merge?.children?.length > 0 &&
                        item?.children?.splice(item.children.length - 1, 0, ...merge?.children)
                })
                if (dynamicArrHeader.value.length != 0) {
                    detailedColumns.value = detailedColumns.value.filter(
                        (item) => item.dataIndex != 'status' && item.dataIndex != 'status1',
                    )
                }
                // detailedColumns.value.splice(5, 0, ...dynamicArrHeader.value)

                if (billType.value == 3) {
                    detailedColumns.value = billType.value == 3 ? getSinopecDetailColumnsInitial() : []
                    generateSinopecDynamicColumnsAndData()
                }
                await request.get(`/api/hr-fee-reviews/handle-choice-dynamic?clientId=${clientId.value}`).then((res) => {
                    selectedHeaderText.value = res
                    selectedHeaderText.value.forEach((item, index) => {
                        checkDetailData.value[item.key] = true
                    })
                })
                await request
                    .post('/api/hr-fee-reviews-client-effectiveness', {
                        clientId: clientId.value,
                        feeReviewDate: params.value.feeReviewDate,
                        billIdList: selectedBillList.value.map((el: any) => {
                            return el.id
                        }),
                    })
                    .then(async (res) => {
                        billTableLoading.value = false
                        childrenPartTip.value = res?.message || ''
                        if (res?.status) {
                            billIdList.value = selectedBillList.value.map((el: any) => {
                                return el.id
                            })
                            canContinue.value = false
                        } else {
                            canContinue.value = true
                        }
                    })
                    .catch((err) => {
                        console.log(err)
                        canContinue.value = true
                        billTableLoading.value = false
                    })
            } catch {
                nextType.value = 3
                billIdList.value = selectedBillList.value.map((el: any) => {
                    return el.id
                })
                // tipLoading.value = false
            }
        }
        const uuid = () => {
            let s: any = []
            let hexDigits = '0123456789abcdef'
            for (var i = 0; i < 36; i++) {
                s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
            }
            s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
            s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
            s[8] = s[13] = s[18] = s[23] = '-'

            var uuid = s.join('')
            return uuid
        }
        const resTableData = ref<any>()

        // 导入海尔账单明细
        const importHaierBill = () => {}
        const funExpenseItem = (elements, detailedColumnsItem, DetailItems) => {
            elements.forEach((element) => {
                if (detailedColumnsItem?.title) {
                    const colFilterInclude =
                        detailedColumnsItem?.children.filter((item) => {
                            return item.dataIndex == element.expenseName + element.expenseType
                        }) || []
                    const colFilterExcept =
                        detailedColumnsItem?.children.filter((item) => {
                            return item.dataIndex != element.expenseName + element.expenseType
                        }) || []

                    const currentFilterData = elements.filter((el) => {
                        return el.expenseName + el.expenseType == element.expenseName + element.expenseType
                    })

                    if (colFilterInclude.length < currentFilterData.length) {
                        detailedColumnsItem.children = [
                            ...colFilterExcept,
                            ...currentFilterData.map((el) => {
                                return {
                                    title: el.expenseName,
                                    dataIndex: el.expenseName + el.expenseType,
                                    align: 'center',
                                    width: 200,
                                }
                            }),
                        ]
                    }
                    /* detailedColumnsItem.children.push({
                        title: element.expenseName,
                        dataIndex: element.expenseName + element.expenseType,
                        align: 'center',
                        width: 100,
                    }) */
                } else {
                    detailedColumnsItem = {
                        title:
                            element.expenseType == 1
                                ? '工资增项'
                                : element.expenseType == 2
                                ? '工资减项'
                                : element.expenseType == 3
                                ? '其他费用项'
                                : element.expenseType == 28
                                ? '收费项'
                                : element.expenseType == 29
                                ? '退费项'
                                : '',
                        dataIndex: '',
                        align: 'center',
                        children:
                            elements.map((item) => {
                                return {
                                    title: item.expenseName,
                                    dataIndex: item.expenseName + item.expenseType,
                                    align: 'center',
                                    width: 300,
                                }
                            }) || [],
                    }
                }
            })
            if (detailedColumnsItem?.children)
                detailedColumnsItem?.children.forEach((el) => {
                    let temp = elements.find((ele) => {
                        return el.dataIndex == ele.expenseName + ele.expenseType
                    })
                    DetailItems[el.dataIndex] = temp ? temp?.amount : 0
                })
            return detailedColumnsItem
        }

        //将客户的上级客户 添加table数据
        const funClient = (clientId, hrClientDTO) => {
            let clients: inObject = {}
            let index = -1
            function recursion(id) {
                let data = hrClientDTO[id]
                if (data) {
                    index += 1
                    clients['clientId' + (index ? index : '')] = id
                    clients['unitNumber' + (index ? index : '')] = data?.unitNumber || ''
                    clients['clientName' + (index ? index : '')] = data?.clientName
                    if (data?.parentId && data?.parentId != '0') {
                        recursion(data.parentId)
                    }
                }
            }
            recursion(clientId)
            return { clients, index }
        }

        const detailedColumns2And3 = ref<any>([])
        // 生成账单明细详情
        const getDetailedColumns = (proportionObj) => {
            let detailedColumns1: any[] = [
                {
                    title: '序号',
                    dataIndex: 'index',
                    align: 'center',
                    sorter: false,
                    width: 120,
                    customRender: ({ index }) => {
                        return h('span', index + 1)
                    },
                    fixed: 'left',
                },
            ]

            let haierDetailColumns: any[] = [
                {
                    title: '企业税费',
                    dataIndex: 'enterpriseTax',
                    align: 'center',
                    sorter: false,
                    width: 120,
                },
                {
                    title: '其他',
                    dataIndex: 'specialOther',
                    align: 'center',
                    sorter: false,
                    width: 120,
                },
                {
                    title: '会费',
                    dataIndex: 'membershipFee',
                    align: 'center',
                    sorter: false,
                    width: 120,
                },
            ]
            //增项Columns值
            let addNewItemColumns: any = {}
            //减项 Columns值
            let itemizedDeductionColumns: any = {}
            //其他费用项 由后面生成Columns，此处只存储值
            let otherColumns: any = {}
            //收费项
            let payItemColumns: any = {}
            //退费项
            let itemRefundColumns: any = {}
            // 选择客户下所有的子客户 以id为键
            let hrClientChildrenDTO = {}
            if (reviewFlags.value != 1) {
                selectBillList.value?.hrBillDetailDTO.forEach((item) => {
                    hrClientChildrenDTO[item.id] = item
                })
            }
            //所需扩展的数量
            let clientIndex = 0

            let showLastItemclientIndex: any = 0
            let showLastItem = true

            //table数据
            let tableData: inObject[] = []
            selectBillList.value?.hrBillDetailDTO?.forEach((item, indexes) => {
                let client: inObject = funClient(item.clientId, hrClientChildrenDTO)
                if (client.index > clientIndex) clientIndex = client.index
                let DetailItemsDTO = {}
                //费用项
                // 工资增项 1
                addNewItemColumns = funExpenseItem(item.wageIncrease, addNewItemColumns, DetailItemsDTO)
                // 工资减项 2
                itemizedDeductionColumns = funExpenseItem(item.wageDeduction, itemizedDeductionColumns, DetailItemsDTO)
                // 其他费用项 3
                otherColumns = funExpenseItem(item.otherExpenses, otherColumns, DetailItemsDTO)
                // 收费项 28
                payItemColumns = funExpenseItem(item.chargeItems, payItemColumns, DetailItemsDTO)
                // 退费项 29
                itemRefundColumns = funExpenseItem(item.refundItems, itemRefundColumns, DetailItemsDTO)
                tableData.push({ ...item, ...client.clients, ...DetailItemsDTO, hierarchyIndex: client.index })

                if (indexes > 0) {
                    if (client.index != showLastItemclientIndex) {
                        showLastItem = false
                    }
                } else {
                    showLastItemclientIndex = client.index
                }
            })
            if (showLastItem) {
                if (clientIndex > 0) clientIndex--
            }
            //因数据是拼接而将将其分成三份，代表三个位置
            let DetailedColumnsInitial = getDetailedColumnsInitial(proportionObj, detailChange)

            // 工资增项
            if (addNewItemColumns?.title) {
                detailedColumns2And3.value.push(addNewItemColumns)
            }
            // 工资减项
            if (itemizedDeductionColumns?.title) {
                detailedColumns2And3.value.push(itemizedDeductionColumns)
            }
            // 其他费用项
            if (otherColumns?.title) {
                detailedColumns2And3.value.push(otherColumns)
            }
            // 收费项
            if (payItemColumns?.title) {
                detailedColumns2And3.value.push(payItemColumns)
            }
            // 退费项
            if (itemRefundColumns?.title) {
                detailedColumns2And3.value.push(itemRefundColumns)
            }
            //clientType 客户类型 0普通客户 1外包客户
            if (selectBillList?.value.clientType != 1) {
                DetailedColumnsInitial[2].forEach((el, index) => {
                    if (el.dataIndex == 'taxationFee') {
                        DetailedColumnsInitial[2].splice(index, 1)
                    }
                })
            }
            detailedColumns.value = [
                //index，客户，所属客户
                ...detailedColumns1,
                //姓名身份证
                ...DetailedColumnsInitial[0],

                ...detailedColumns2And3.value,
                //社保公积金
                ...DetailedColumnsInitial[1],
                //其他费用项
                // ...detailedColumns2List,
                //总计
                ...DetailedColumnsInitial[2],
            ]
            if (reviewFlags.value == 1) {
                detailedColumns.value = [
                    ...detailedColumns1,
                    ...DetailedColumnsInitial[0],
                    ...detailedColumns2And3.value,
                    ...DetailedColumnsInitial[1],
                    ...haierDetailColumns,
                    ...DetailedColumnsInitial[2],
                ]
            }
        }
        const detailedTableData = ref<inObject[]>([])
        const summaryTableData = ref<inObject[]>([])
        const detailChange = (val, idx, key, type = 'number') => {
            let oldVal = detailedTableData.value[idx][key]
            detailedTableData.value[idx][key] = type == 'number' ? round(Number(val), 2) : val?.target?.value
            detailedTableData.value[idx].total = plus(
                minus(detailedTableData.value[idx].total, oldVal),
                detailedTableData.value[idx][key],
            )
            if (key == 'serviceFeep') {
                summaryTableData.value[0].serviceFeeTotal = plus(
                    minus(summaryTableData.value[0].serviceFeeTotal, oldVal),
                    detailedTableData.value[idx][key],
                )
            } else if (key == 'taxationFee') {
                summaryTableData.value[0].taxationFeeTotal = plus(
                    minus(summaryTableData.value[0].taxationFeeTotal, oldVal),
                    detailedTableData.value[idx][key],
                )
            }
            summaryTableData.value[0].total = plus(
                minus(summaryTableData.value[0].total, oldVal),
                detailedTableData.value[idx][key],
            )
            detailedTableData.value = [...detailedTableData.value]
            summaryTableData.value = [...summaryTableData.value]
        }
        // 复选框置空
        const clientCheckboxReset = () => {
            selectedClientList.value = []
            selectedClientKeysList.value = []
        }

        // 列表筛选框制空
        const columnCheckboxReset = () => {
            selectedHeaderText.value = []
            columnsDataChecked.value = {}
            checkDetailData.value = {}
        }
        const billCheckboxReset = () => {
            selectedBillList.value = []
            selectedBillKeysList.value = []
        }
        // 上一步
        const lastStep = async (step) => {
            if (step == 2) {
                nextType.value = 1
                clientCheckboxReset()
                billCheckboxReset()
            } else if (step == 4 && reviewFlags.value == 1) {
                nextType.value = 3
            } else {
                nextType.value = 2
                clientCheckboxReset()
                billCheckboxReset()
            }
        }

        const changeStartRow = async () => {
            if (!startRow.value) {
                return
            }
            await request.post(`/api/hr-bill-dynamic-fieldses/updateDataStartRow/${billIds.value}/${startRow.value - 1}`, {})
        }
        /**
         * 动态合并表头
         * @param cols
         * @param isTotal
         */
        const dynamicMergeHeaderText = (cols) => {
            let returnArr: any = []
            const dynamicArr = ['unitCardinal', 'personalCardinal']

            const fixArr = cols.sort((a, b) => {
                return dynamicArr.indexOf(b.dataIndex) - dynamicArr.indexOf(a.dataIndex)
            })
            fixArr.forEach((el) => {
                if (!returnArr.length) returnArr.push(el)
                else {
                    const tempObj = returnArr.find((i) => i.dataIndex == el.dataIndex)
                    if (tempObj) {
                        tempObj.children = [...tempObj.children, ...el.children]
                    } else {
                        returnArr.push(el)
                    }
                }
            })
            return returnArr
        }
        const showTip = ref(false)
        // 下一步
        const nextStep = async (step) => {
            if (step == 1) {
                if (!selectedClientList.value.length) message.warning('请至少选择一个客户')
                else {
                    billList.value = await request.post('/api/hr-fee-reviews/query-bill', params.value)
                    billList.value.forEach((item: any) => {
                        billIds.value = item.id
                    })
                    nextType.value = 2
                    clientCheckboxReset()
                }
            } else if (step == 2 && reviewFlags.value == 1) {
                nextType.value = 3
                getExpenseTypeList()
                // setExpenseConfirm()
            } else {
                if (!selectedBillList.value.length) {
                    message.warning('请至少选择一个账单')
                } else {
                    if (
                        selectedBillList.value.some((el) => el.billType == 3) &&
                        selectedBillList.value.filter((el) => el.billType != 3).length
                    ) {
                        message.warning('已选择中石化账单，无法选择其他类型账单！')
                        return
                    }
                    billTableLoading.value = true

                    const res = await request
                        .post('/api/hr-fee-reviews/generate-settlement-document-info', {
                            clientId: clientId.value,
                            feeReviewDate: params.value.feeReviewDate,
                            // paymentDateList: params.value.paymentDateList,
                            billIdList: selectedBillList.value.map((el: any) => {
                                return el.id
                            }),
                        })
                        .catch((e) => {
                            // nextType.value = 1
                            billTableLoading.value = false
                        })
                    selectBillList.value = res
                    if (step == 2) {
                        let proportionObj = getProportionObj(selectBillList.value?.hrBillDTO || [])
                        getDetailedColumns(proportionObj)
                    }
                    let firstHrBillTotalDTO = res?.hrBillDTO[0] || {}
                    billType.value = firstHrBillTotalDTO.billType
                    const dynamicHeader = await request.post(`/api/hr-bill/show-dynamic-header `, [billIds.value])
                    dynamicArrHeader.value = [...dynamicMergeHeaderText(dynamicHeader)]
                    if (dynamicArrHeader.value.length != 0) {
                        dynamicArrHeader.value.forEach((el) => {
                            dynamicHeaderColumns.value.push({
                                title: el.title,
                                dataIndex: el.dataIndex,
                                width: 200,
                                key: el.key,
                                align: 'center',
                                children: el.children?.map((res) => {
                                    return {
                                        title: res.title,
                                        dataIndex: res.dataIndex,
                                        key: res.key,
                                        dataIndexS: res.dataIndexS,
                                        width: 'calc(60% - 250px)',
                                        align: 'center',
                                    }
                                }),
                            })
                        })
                    }
                }
                const andkey = ['unitPayment', 'personalPayment']
                let dynamicHe = dynamicHeaderColumns.value?.filter(
                    (el) => el.dataIndex === andkey[0] || el.dataIndex === andkey[1],
                )
                dynamicHeaderColumns.value = dynamicHeaderColumns.value?.filter(
                    (el) => el.dataIndex != andkey[0] && el.dataIndex != andkey[1],
                )
                dynamicHeaderColumns.value.forEach((item) => {
                    detailedColumns.value.splice(5, 0, { ...item })
                })
                andkey?.forEach((el) => {
                    let item: any = detailedColumns.value?.find((item) => item.dataIndex == el) ?? {}
                    let merge: any = dynamicHe?.find((item) => item.dataIndex == el) ?? {}
                    item?.children?.forEach((row) => {
                        merge?.children?.forEach((ele) => {
                            if (row.dataIndex == ele.dataIndex) {
                                let idx = merge?.children?.findIndex((it) => it.dataIndex == ele.dataIndex)
                                merge?.children.splice(idx, 1)
                            }
                        })
                    })
                    item?.children?.length > 0 &&
                        merge?.children?.length > 0 &&
                        item?.children?.splice(item.children.length - 1, 0, ...merge?.children)
                })
                if (dynamicArrHeader.value.length != 0) {
                    detailedColumns.value = detailedColumns.value.filter(
                        (item) => item.dataIndex != 'status' && item.dataIndex != 'status1',
                    )
                }
                // detailedColumns.value.splice(5, 0, ...dynamicArrHeader.value)

                if (billType.value == 3) {
                    detailedColumns.value = billType.value == 3 ? getSinopecDetailColumnsInitial() : []
                    generateSinopecDynamicColumnsAndData()
                }
                request.get(`/api/hr-fee-reviews/handle-choice-dynamic?clientId=${clientId.value}`).then((res) => {
                    selectedHeaderText.value = res
                    selectedHeaderText.value.forEach((item, index) => {
                        checkDetailData.value[item.key] = true
                    })
                })
                detailedColumns.value = detailedColumns.value.map((item) => {
                    let objArr = {}
                    objArr = { ...item, ...{ slots: { title: item.dataIndex } } }
                    return objArr
                })
                detailedColumns.value.forEach((el) => {
                    slotsDynamicColumns.value?.filter(
                        (item) => item.dataIndex != 'index' || item.dataIndex != 'certificateNum' || item.dataIndex != 'name',
                    )
                    slotsDynamicColumns.value.push({
                        title: el.title,
                        slots: { title: el.dataIndex },
                        key: el.dataIndex,
                    })
                })
                slotsDynamicColumns.value
                    .filter((el) => el.dataIndex != 'index' && el.dataIndex != 'certificateNum' && el.dataIndex != 'name')
                    .map((item) => {
                        slotsArr.value.push(item.slots.title)
                        return slotsArr.value
                    })
                console.log(selectBillList.value)
                if (!selectedBillList.value.length) {
                    return
                }
                request
                    .post('/api/hr-fee-reviews-client-effectiveness', {
                        clientId: clientId.value,
                        feeReviewDate: params.value.feeReviewDate,
                        billIdList: selectedBillList.value.map((el: any) => {
                            return el.id
                        }),
                    })
                    .then(async (res) => {
                        billTableLoading.value = false
                        nextType.value = 4
                        childrenPartTip.value = res?.message || ''
                        if (res?.status) {
                            billIdList.value = selectedBillList.value.map((el: any) => {
                                return el.id
                            })
                            billCheckboxReset()
                            canContinue.value = false
                        } else {
                            canContinue.value = true
                        }
                    })
                    .catch((err) => {
                        console.log(err)
                        canContinue.value = true
                        billTableLoading.value = false
                    })
            }
        }

        // 取消
        const cancel = () => {
            emit('update:visible', false)
            clientCheckboxReset()
            billCheckboxReset()
            columnCheckboxReset()
        }
        // 继续
        const confirm = async () => {
            try {
                const res = await request.post('/api/hr-fee-reviews/generate-settlement-document-info', {
                    clientId: clientId.value,
                    feeReviewDate: params.value.feeReviewDate,
                    billIdList: billIdList.value,
                    reviewChoiceDTOList: selectedHeaderText.value,
                    hrBillTotalDTO: reviewFlags.value == 1 ? [resTableData.value] : [],
                    hrBillDetailDTO: reviewFlags.value == 1 ? haierBillColumnsData.value : [],
                })

                clientCheckboxReset()
                billCheckboxReset()
                columnCheckboxReset()
                emit('confirm', 'add', res)
                emit('dinamicHeader', dynamicHeaderColumns.value)
                emit('update:visible', false)
            } catch (error) {
                console.log(error)
            }
        }

        return {
            canContinue,
            childrenPartTip,
            clientSelectionRowConfig,
            billSelectionRowConfig,
            billTableLoading,
            billList,
            selectedClientList,
            selectedBillList,
            clientTableRef,
            billTableRef,
            params,
            lastStep,
            nextType,
            nextStep,
            cancel,
            confirm,
            detailedColumns,
            columnsDataChecked,
            slotsDynamicColumns,
            slotsArr,
            checkDetailData,
            checkedChange,
            dynamicHeaderColumns,
            importHaierBill,
            beforeUpload,
            changeStartRow,
            setExpenseConfirm,
            title,
            startRow,
            tableData,
            tableColumns,
            getExpenseTypeList,
            removeField,
            expenseTypeList,
            resData,
            resColumns,
            body: document.body,
            showSetExpense,
            setForm,
            showTip,
            modalConfirm,
            unuseColsList,
            unuseCols,
            tipClose,
            tipConfirm,
            tipLoading,
            clientColumns: [
                {
                    title: '上级客户',
                    dataIndex: 'superiorUnit',
                    width: 160,
                    sorter: (a, b) => b.superiorUnit?.length ?? 0 - a.superiorUnit?.length ?? 0,
                },
                {
                    title: '客户编号',
                    dataIndex: 'unitNumber',
                    width: 120,
                    sorter: (a, b) => a.unitNumber - b.unitNumber,
                },
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    width: 160,
                    sorter: (a, b) => b.clientName?.length ?? 0 - a.clientName?.length ?? 0,
                },
            ],
            billColumns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    width: 150,
                    sorter: (a, b) => b.clientName?.length ?? 0 - a.clientName?.length ?? 0,
                },
                {
                    title: '账单标题',
                    dataIndex: 'title',
                    width: 300,
                    sorter: (a, b) => b.title?.length ?? 0 - a.title?.length ?? 0,
                },
                {
                    title: '账单类型',
                    dataIndex: 'billTypeStr',
                    width: 90,
                    sorter: (a, b) => a.billType - b.billType,
                },
                {
                    title: '费用总额',
                    dataIndex: 'total',
                    width: 90,
                    sorter: (a, b) => a.total - b.total,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.basicTable :deep(.table-striped) {
    background-color: #fafafa;
}
.foot_btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.expenseApprovalTipBox {
    max-height: 500px;
    text-align: center;
    img {
        width: 160px;
        margin-bottom: 10px;
    }
    p:last-child {
        margin-bottom: 30px;
    }
}
.container {
    position: relative;
    .conceal {
        // position: absolute;
        width: 100%;
        height: 100px;
        background-color: #fafafa;
        position: absolute;
        top: 200px;
    }
}
:deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover .ant-checkbox-inner, .ant-checkbox-input:focus
        + .ant-checkbox-inner) {
    border: 2px solid #1bba79 !important;
}
:deep(.ant-checkbox-inner) {
    border: 2px solid #1bba79;
    background-color: transparent;
}
:deep(.ant-checkbox-inner) {
    background-color: #bbddcf;
    border: 2px solid #c1cec8;
}
.StableBox {
    :deep(.surely-table-body-container) {
        height: 1px;
    }
}
.col {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .label {
        width: 100px;
        text-align: right;
        &:after {
            content: '：';
        }
    }
}
.title {
    border-left: 5px solid @primary-color;
    text-align: left;
    padding-left: 10px;
    margin-bottom: 20px;
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        // padding-left: 10px;
    }
    .main {
        padding: 10px 0;
        .row {
            margin: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
        }
    }
}
.table {
    width: 100%;
    overflow-x: auto;
    .headerCols {
        display: flex;
        justify-content: flex-start;
        align-items: stretch;
        flex-wrap: nowrap;
        .th {
            flex-shrink: 0;
            display: inline-block;
            background: @primary-color;
            color: white;
            padding: 10px;
            text-align: center;
            border: 1px solid transparent;
            border-right-color: white;
        }
    }
    .bodyCols {
        .tr {
            display: flex;
            justify-content: flex-start;
            align-items: stretch;
            flex-wrap: nowrap;
            .tb {
                flex-shrink: 0;
                width: 100px;
                display: inline-block;
                padding: 10px;
                text-align: center;
                border: 1px solid #eee;
            }
        }
    }
}
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
</style>
