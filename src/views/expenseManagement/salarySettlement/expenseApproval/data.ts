import { InputNumber } from 'ant-design-vue'
import { h } from 'vue'
export const getDetailedColumnsInitial = (data, callback) => {
    return [
        [
            {
                title: '姓名',
                dataIndex: 'name',
                width: 100,
                ellipsis: true,
                align: 'center',
                fixed: 'left',
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                width: 180,
                ellipsis: true,
                align: 'center',
                fixed: 'left',
            },
        ],
        [
            {
                title: '应发工资',
                //？
                dataIndex: 'salary',
                width: 140,
                ellipsis: true,
                align: 'center',
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
            },
            {
                title: '单位基数',
                dataIndex: 'status',
                children: [
                    {
                        title: '单位养老基数',
                        dataIndex: 'unitPensionCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '单位失业基数',
                        dataIndex: 'unitUnemploymentCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '单位生育基数',
                        dataIndex: 'unitMaternityCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '单位医疗基数',
                        dataIndex: 'medicalInsuranceCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '单位工伤基数',
                        dataIndex: 'workInjuryCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                ],
            },
            {
                title: '个人基数',
                dataIndex: 'status1',
                children: [
                    {
                        title: '个人养老基数',
                        dataIndex: 'personalPensionCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '个人失业基数',
                        dataIndex: 'personalUnemploymentCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '个人生育基数',
                        dataIndex: 'personalMaternityCardinal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                    {
                        title: '个人医疗基数',
                        dataIndex: 'medicalInsuranceCardinalPersonal',
                        width: 180,
                        ellipsis: false,
                        align: 'center',
                    },
                ],
            },
            {
                title: '单位缴纳部分',
                dataIndex: 'unitPayment',
                children: [
                    {
                        title: '养老' + '  ' + data.unitPensionScale,
                        dataIndex: 'unitPension',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '失业' + '  ' + data.unitUnemploymentScale,
                        dataIndex: 'unitUnemployment',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '生育' + '  ' + data.unitMaternityScale,
                        dataIndex: 'unitMaternity',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            if (!text) {
                                return 0
                            }
                            return text?.toFixed(2)
                        },
                    },
                    {
                        title: '医疗' + '  ' + data.unitMedicalScale,
                        dataIndex: 'unitMedical',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '工伤' + '  ' + data.workInjuryScale,
                        dataIndex: 'workInjury',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '大额医疗',
                        dataIndex: 'unitLargeMedicalExpense',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            if (!text) {
                                return 0
                            }
                            return text?.toFixed(2)
                        },
                    },
                    {
                        title: '补充工伤',
                        dataIndex: 'replenishWorkInjuryExpense',
                        align: 'center',
                        width: 150,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            if (!text) {
                                return 0
                            }
                            return text?.toFixed(2)
                        },
                    },
                    {
                        title: '单位商业保险',
                        dataIndex: 'commercialInsurance',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '单位企业年金',
                        dataIndex: 'unitEnterpriseAnnuity',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '补差',
                        //？
                        dataIndex: 'unitSocialSecurityMakeUp',
                        align: 'center',
                        width: 140,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '小计',
                        dataIndex: 'unitSubtotal',
                        align: 'center',
                        width: 140,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                ],
            },
            {
                title: '个人缴纳部分',
                dataIndex: 'personalPayment',
                children: [
                    {
                        title: '养老' + '  ' + data.personalPensionScale,
                        dataIndex: 'personalPension',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '失业' + '  ' + data.personalUnemploymentScale,
                        dataIndex: 'personalUnemployment',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '医疗' + '  ' + data.personalMedicalScale,
                        dataIndex: 'personalMedical',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '生育' + '  ' + data.personalMaternityScale,
                        dataIndex: 'personalMaternity',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            if (!text) {
                                return 0
                            }
                            return text?.toFixed(2)
                        },
                    },
                    {
                        title: '大额医疗',
                        dataIndex: 'personalLargeMedicalExpense',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            if (!text) {
                                return 0
                            }
                            return text?.toFixed(2)
                        },
                    },
                    {
                        title: '个人企业年金',
                        // ？
                        dataIndex: 'personalEnterpriseAnnuity',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '补差',
                        // ？
                        dataIndex: 'personalSocialSecurityMakeUp',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                    {
                        title: '小计',
                        dataIndex: 'personalSubtotal',
                        align: 'center',
                        width: 160,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text?.toFixed(2) || 0
                        },
                    },
                ],
            },
            {
                title: '社保总金额',
                dataIndex: 'socialSecurityTotal',
                width: 140,
                ellipsis: true,
                align: 'center',
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
            },
            {
                title: '公积金基数',
                dataIndex: 'accumulationFundCardinal',
                align: 'center',
                width: 140,
                ellipsis: true,
            },

            {
                title: '住房公积金',
                dataIndex: 'status2',
                children: [
                    {
                        title: '单位' + '  ' + data.unitAccumulationFundScale,
                        dataIndex: 'unitAccumulationFund',
                        align: 'center',
                        width: 140,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text
                        },
                    },
                    {
                        title: '个人' + '  ' + data.personalAccumulationFundScale,
                        dataIndex: 'personalAccumulationFund',
                        align: 'center',
                        width: 140,
                        ellipsis: true,
                        customRender: ({ record, text }) => {
                            return text
                        },
                    },
                ],
            },
            {
                title: '公积金总金额',
                dataIndex: 'accumulationFundTotal',
                width: 180,
                ellipsis: true,
                customRender: ({ record, text }) => {
                    return text
                },
            },
            {
                title: '税前应发',
                dataIndex: 'preTaxSalary',
                width: 150,
                ellipsis: true,
                align: 'center',
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
                // slots: { customRender: 'status' },
            },
            {
                title: '个税',
                dataIndex: 'personalTax',
                width: 140,
                ellipsis: true,
                align: 'center',
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
                // slots: { customRender: 'status' },
            },

            {
                title: '实发工资',
                dataIndex: 'realSalary',
                width: 140,
                align: 'center',
                ellipsis: true,
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
            },
        ],
        [
            // {
            //     title: '其他费用项1',
            //     dataIndex: '',
            //     width: 100,ellipsis: true,
            // },
            // {
            //     title: '其他费用项2',
            //     dataIndex: '',
            //     width: 100,ellipsis: true,
            // },
            {
                title: '服务费',
                align: 'center',
                dataIndex: 'serviceFee',
                width: 140,
                ellipsis: true,
                customRender: ({ record, index, text }) => {
                    return h(InputNumber, {
                        value: text,
                        onChange: (e) => {
                            callback(e, index, 'serviceFee')
                        },
                        style: {
                            width: '100%',
                        },
                    })
                },
            },
            {
                title: '税费',
                dataIndex: 'taxationFee',
                width: 140,
                ellipsis: true,
                customRender: ({ record, index, text }) => {
                    return h(InputNumber, {
                        value: text,
                        onChange: (e) => {
                            callback(e, index, 'taxationFee')
                        },
                        style: {
                            width: '100%',
                        },
                    })
                },
            },
            {
                title: '费用合计',
                // 总金额
                align: 'center',
                dataIndex: 'total',
                width: 140,
                ellipsis: true,
                customRender: ({ record, text }) => {
                    return text?.toFixed(2) || 0
                },
            },
        ],
    ]
}

export const getSummaryColumnsInitial = (data) => {
    return [
        {
            title: '人数',
            dataIndex: 'staffNum',
            align: 'center',
            width: 100,
            ellipsis: true,
            fixed: 'left',
        },
        {
            title: '单位缴纳社保',
            dataIndex: 'status',
            children: [
                {
                    title: '养老' + '  ' + data.unitPensionScale,
                    dataIndex: 'unitPensionTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '失业' + '  ' + data.unitUnemploymentScale,
                    dataIndex: 'unitUnemploymentTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '医疗' + '  ' + data.unitMedicalScale,
                    dataIndex: 'unitMedicalTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '生育' + '  ' + data.unitMaternityScale,
                    dataIndex: 'unitMaternityTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '工伤' + '  ' + data.workInjuryScale,
                    dataIndex: 'workInjuryTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '大额医疗',
                    dataIndex: 'unitLargeMedicalExpenseTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '补充工伤',
                    dataIndex: 'replenishWorkInjuryExpenseTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '单位商业保险',
                    dataIndex: 'commercialInsuranceTotal',
                    align: 'center',
                    width: 160,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '单位企业年金',
                    dataIndex: 'unitEnterpriseAnnuityTotal',
                    align: 'center',
                    width: 160,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '补差',
                    //？
                    dataIndex: 'unitSocialSecurityMakeUpTotal',
                    align: 'center',
                    width: 140,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '小计',
                    dataIndex: 'unitSubtotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
            ],
        },
        {
            title: '个人缴纳社保',
            dataIndex: 'status',
            children: [
                {
                    title: '养老' + '  ' + data.personalPensionScale,
                    dataIndex: 'personalPensionTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '失业' + '  ' + data.personalUnemploymentScale,
                    dataIndex: 'personalUnemploymentTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '医疗' + '  ' + data.personalMedicalScale,
                    dataIndex: 'personalMedicalTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '生育' + '  ' + data.personalMaternityScale,
                    dataIndex: 'personalMaternityTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '大额医疗',
                    dataIndex: 'personalLargeMedicalExpenseTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
                {
                    title: '个人企业年金',
                    // ？
                    dataIndex: 'personalEnterpriseAnnuityTotal',
                    align: 'center',
                    width: 160,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '补差',
                    // ？
                    dataIndex: 'personalSocialSecurityMakeUpTotal',
                    align: 'center',
                    width: 160,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        return text?.toFixed(2) || 0
                    },
                },
                {
                    title: '小计',
                    dataIndex: 'personalSubtotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text?.toFixed(2)
                    },
                },
            ],
        },
        {
            title: '社保总金额',
            dataIndex: 'socialSecurityTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },

        {
            title: '公积金费用',
            dataIndex: '',
            children: [
                {
                    title: '单位' + '  ' + data.unitAccumulationFundScale,
                    dataIndex: 'unitAccumulationFundTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text
                    },
                },
                {
                    title: '个人' + '  ' + data.personalAccumulationFundScale,
                    dataIndex: 'personalAccumulationFundTotal',
                    align: 'center',
                    width: 100,
                    ellipsis: true,
                    customRender: ({ record, text }) => {
                        if (!text) {
                            return 0
                        }
                        return text
                    },
                },
            ],
        },
        {
            title: '公积金总金额',
            dataIndex: 'accumulationFundTotal',
            width: 150,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text
            },
        },

        {
            title: '个税',
            dataIndex: 'personalTaxTotal',
            align: 'center',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '实发',
            dataIndex: 'realSalaryTotal',
            align: 'center',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '收费项',
            dataIndex: 'chargeTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '退费项',
            dataIndex: 'refundTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '其他费用',
            dataIndex: 'otherFeeTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '服务费',
            dataIndex: 'serviceFeeTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        /* {
            title: '上月补差',
            dataIndex: 'lastMonthMakeUp',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (text == 0) {
                    return 0
                }
                if (!text) {
                    return ''
                }
                return text?.toFixed(2)
            },
        }, */
        {
            title: '税费',
            dataIndex: 'taxationFeeTotal',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
        {
            title: '总计',
            // 总金额
            dataIndex: 'total',
            width: 100,
            ellipsis: true,
            customRender: ({ record, text }) => {
                if (!text) {
                    return 0
                }
                return text?.toFixed(2)
            },
        },
    ]
}

export const getSinopecDetailColumnsInitial = () => {
    return [
        {
            title: '序号',
            dataIndex: 'index',
            customRender: (record) => {
                return record.index + 1
            },
            width: 120,
            fixed: 'left',
            align: 'center',
        },
        {
            title: '当前年月',
            dataIndex: 'currentYear',
            width: 150,
            fixed: 'left',
            align: 'center',
        },
        {
            title: '项目代码',
            dataIndex: 'projectCode',
            width: 150,
            fixed: 'left',
            align: 'center',
        },
        {
            title: '项目名称',
            dataIndex: 'projectName',
            width: 150,
            fixed: 'left',
            align: 'center',
        },
        {
            title: '人数',
            dataIndex: 'peopleNum',
            width: 150,
            fixed: 'left',
            align: 'center',
        },
        {
            title: '发放的职工薪酬',
            dataIndex: '发放的职工薪酬',
            align: 'center',
            children: [
                {
                    title: '职工薪酬 小计',
                    dataIndex: '职工薪酬 小计',
                    align: 'center',
                    width: 230,
                    customRender: ({ record, text }) => {
                        return text || 0
                    },
                },
                {
                    title: '其中',
                    dataIndex: '其中',
                    align: 'center',
                    children: [
                        {
                            title: '应得工资',
                            dataIndex: '应得工资',
                            align: 'center',
                            width: 200,
                            customRender: ({ record, text }) => {
                                return text || 0
                            },
                        },
                        {
                            title: '奖金',
                            dataIndex: '奖金',
                            align: 'center',
                            width: 200,
                            customRender: ({ record, text }) => {
                                return text || 0
                            },
                        },
                    ],
                },
            ],
        },
        {
            title: '单位缴纳社会保险费',
            dataIndex: '单位缴纳社会保险费',
            align: 'center',
            width: 250,
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '单位缴纳住房公积金',
            dataIndex: '单位缴纳住房公积金',
            align: 'center',
            width: 200,
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '其它费用',
            dataIndex: 'otherFee',
            align: 'center',
            children: [],
        },
        {
            title: '报销费用',
            dataIndex: '报销费用',
            width: 120,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '管理费',
            dataIndex: '管理费',
            width: 120,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '税金',
            dataIndex: '税金',
            width: 120,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '代扣个人保险',
            dataIndex: '代扣个人保险',
            width: 200,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '代扣个人住房公积金',
            dataIndex: '代扣个人住房公积金',
            width: 200,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '扣个人所得税',
            dataIndex: '扣个人所得税',
            width: 200,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '结算费用合计',
            dataIndex: '费用合计',
            width: 200,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
        {
            title: '发生费用合计',
            dataIndex: 'totalOccurrence',
            width: 200,
            align: 'center',
            customRender: ({ record, text }) => {
                return text || 0
            },
        },
    ]
}

export const getSinopecSummaryColumnsInitial = () => {
    return [
        {
            title: '项目数',
            dataIndex: 'projectNum',
            width: 100,
            align: 'center',
        },
        {
            title: '人数',
            dataIndex: 'staffNum',
            width: 100,
            align: 'center',
        },
        {
            title: '发放的职工薪酬',
            align: 'center',
            children: [
                {
                    title: '职工薪酬 小计',
                    dataIndex: '职工薪酬 小计',
                    align: 'center',
                    width: 110,
                },
                {
                    title: '其中',
                    align: 'center',
                    children: [
                        {
                            title: '应得工资',
                            dataIndex: '应得工资',
                            align: 'center',
                            width: 100,
                        },
                        {
                            title: '奖金',
                            dataIndex: '奖金',
                            align: 'center',
                            width: 100,
                        },
                    ],
                },
            ],
        },
        {
            title: '单位缴纳社会保险费',
            dataIndex: '单位缴纳社会保险费',
            width: 150,
            align: 'center',
        },
        {
            title: '单位缴纳住房公积金',
            dataIndex: '单位缴纳住房公积金',
            width: 150,
            align: 'center',
        },
        {
            title: '其它费用',
            dataIndex: 'totalOtherFee',
            align: 'center',
            children: [],
        },
        {
            title: '报销费用',
            dataIndex: '报销费用',
            width: 120,
            align: 'center',
        },
        {
            title: '管理费',
            dataIndex: '管理费',
            width: 120,
            align: 'center',
        },
        {
            title: '税金',
            dataIndex: '税金',
            width: 120,
            align: 'center',
        },
        {
            title: '代扣个人保险',
            dataIndex: '代扣个人保险',
            width: 120,
            align: 'center',
        },
        {
            title: '代扣个人住房公积金',
            dataIndex: '代扣个人住房公积金',
            width: 120,
            align: 'center',
        },
        {
            title: '扣个人所得税',
            dataIndex: '扣个人所得税',
            width: 120,
            align: 'center',
        },
        {
            title: '结算费用合计',
            dataIndex: 'totalFee',
            width: 120,
            align: 'center',
        },
        {
            title: '发生费用合计',
            dataIndex: 'happenTotalFee',
            width: 120,
            align: 'center',
        },
    ]
}

export const getProportionObj = (hrBillDTO) => {
    //比例
    const proportionObj = {
        unitPensionScale: '', //单位缴纳养老百分比
        unitUnemploymentScale: '', //单位缴纳失业百分比
        unitMedicalScale: '', //单位缴纳医疗百分比
        workInjuryScale: '', //单位缴纳工伤百分比
        personalPensionScale: '', //个人缴纳养老百分比
        personalUnemploymentScale: '', //个人缴纳失业百分比
        personalMedicalScale: '', //个人缴纳医疗百分比
        unitAccumulationFundScale: '', //公积金单位缴纳百分比
        personalAccumulationFundScale: '', //公积金个人缴纳百分比
        unitMaternityScale: '', //单位生育
        personalMaternityScale: '', //个人生育
    }
    const proportionNameList = [
        'unitPensionScale', //单位缴纳养老百分比
        'unitUnemploymentScale', //单位缴纳失业百分比
        'unitMedicalScale', //单位缴纳医疗百分比
        'workInjuryScale', //单位缴纳工伤百分比
        'personalPensionScale', //个人缴纳养老百分比
        'personalUnemploymentScale', //个人缴纳失业百分比
        'personalMedicalScale', //个人缴纳医疗百分比
        'unitAccumulationFundScale', //公积金单位缴纳百分比
        'personalAccumulationFundScale', //公积金个人缴纳百分比
        'unitMaternityScale', //单位生育
        'personalMaternityScale', // 个人生育
    ]
    // let hrBillDTO = currentValue.value?.hrBillDTO || []
    const unit = (num) => {
        if (!num) return ''
        return (num * 100).toFixed(2) + '%'
    }
    hrBillDTO.forEach((item, index) => {
        proportionNameList.forEach((element) => {
            if (index == 0) {
                proportionObj[element] = unit(item[element])
            } else {
                if (unit(proportionObj[element]) != unit(item[element])) {
                    proportionObj[element] = ''
                }
            }
        })
    })
    return proportionObj
}
