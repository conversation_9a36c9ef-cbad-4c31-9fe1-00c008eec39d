<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="'新增费用审核'">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="itemForm in myOptions" :key="itemForm.name">
                <MyFormItem :item="itemForm" v-if="itemForm.show != false" v-model:value="formData[itemForm.name]">
                    <template #clientId>
                        <ClientSelectTree
                            v-model:value="formData[itemForm.name]"
                            :itemForm="itemForm"
                            :renderInBody="true"
                            customDictionaryKey="expenseApprovalClients"
                            customApi="/api/hr-clients/non-page"
                            requestMethod="post"
                        />
                    </template>
                </MyFormItem>
            </template>
        </Form>

        <template #footer>
            <Button @click="cancel">取消</Button>
            <Button type="primary" @click="confirm">下一步</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { valuesAndRules } from '/#/component'
import { getValuesAndRules } from '/@/utils/index'
export default defineComponent({
    name: 'AddModal',
    components: {},
    props: {
        visible: Boolean,
    },
    emits: ['confirm', 'update:visible'],
    setup(props, { emit }) {
        //标题
        const { visible } = toRefs<any>(props)
        const formInline = ref()
        const formData = ref<any>({})
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '客户名称',
                name: 'clientId',
                slots: 'clientId',
                type: 'slots',
                onChange: (val, option) => {
                    console.log(val, option)
                    formData.value.agreementType = option.agreementType
                },
            },
            {
                label: '费用年月',
                name: 'feeReviewDate',
                type: 'month',
            },
        ])
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        watch(visible, () => {
            if (visible.value) {
                formData.value = JSON.parse(JSON.stringify(initFormData))
            }
        })
        //取消
        const cancel = () => {
            emit('update:visible', false)
            // emit('cancel')
        }
        //继续
        const confirm = async () => {
            formInline.value
                .validate()
                .then(async () => {
                    const res = await request.post('/api/hr-fee-reviews/query-client', { clientId: formData.value.clientId })
                    console.log(res)
                    emit('update:visible', false)
                    emit('confirm', {
                        ...formData.value,
                        clientList: res,
                    })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            formInline,
            myOptions,
            rules,
            formData,
            cancel,
            confirm,
        }
    },
})
</script>

<style scoped lang="less">
.form-flex {
    height: 165px;
    padding-top: 25px;
}
</style>
