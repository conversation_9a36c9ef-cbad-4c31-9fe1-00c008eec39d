<template>
    <BasicEditModalSlot title="提示" :visible="visible" @cancel="modalClose" width="1100px" :footer="null">
        <BasicTable :useIndex="true" :tableDataList="tableData" :columns="columns" :sorter="false" :rowSelectionShow="false" />
        <p class="tip">员工信息与上期账单不符，无法复用，请重新创建！</p>
        <div class="tip">
            <Button @click="modalClose" type="primary" shape="round" size="large" style="width: 200px">我知道了</Button>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'CopyModal',
    props: {
        visible: Boolean,
        tableData: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const modalClose = () => {
            emit('cancel')
        }
        return {
            modalClose,
            columns: [
                {
                    title: '员工编号',
                    dataIndex: 'systemNum',
                    width: 110,
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 110,
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 160,
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 110,
                },
                {
                    title: '员工类型',
                    dataIndex: 'personnelTypeStr',
                    width: 100,
                },
                {
                    title: '信息变更',
                    dataIndex: 'reason',
                    width: 150,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    margin: 15px 0;
    text-align: center;
}
</style>
