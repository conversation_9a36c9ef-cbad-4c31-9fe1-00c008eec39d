<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'assureBill_createAndRemove'" type="primary" @click="createRow">新增</Button>
        <!-- 移动到收支对账 -->
        <!-- <Button type="primary" v-auth="'assureBill_batchCompare'" @click="showBatchModal">批量对账</Button> -->
        <Button type="primary" v-auth="'assureBill_download'" @click="downloadSome">{{ exportText }}</Button>
        <Button v-auth="'assureBill_createAndRemove'" type="primary" danger @click="deleteSome">批量删除</Button>
    </div>
    <BasicTable
        ref="talbeRef"
        api="/api/hr-bills/page"
        exportUrl="/api/hr-bills/download-bill-batch"
        :params="{ billType: 1, ...params }"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        :getChildrenMethod="getChildrenMethod"
    >
        <template #operation="{ record }">
            <Button v-if="record.strBillIds" size="small" type="primary" @click="editRow(record, false)">{{
                record.billState == 0 ? '编辑' : '查看'
            }}</Button>
            &nbsp;
            <!-- <Button v-auth="'assureBill_compare'" size="small" type="primary" danger @click="compareRow(record)">对账</Button> -->
            &nbsp;
            <!-- 已通过审核才能复制 -->
            <Button
                v-if="record.strBillIds"
                v-auth="'assureBill_createAndRemove'"
                :disabled="!(record.reviewState == 1)"
                size="small"
                type="primary"
                @click="copyRow(record)"
            >
                复制
            </Button>
        </template>
    </BasicTable>

    <!-- 新增 -->
    <CreateModal :visible="showCreate" @cancel="createClose" @confirm="createConfirm" :billType="1" />
    <!-- 复制提示 -->
    <CopyModal :visible="showTipTable" :tableData="copyErrList" @cancel="showTipTable = false" />
    <!-- 保障账单 -->
    <AssureBill
        v-model:visible="showAssureBill"
        :billId="currentItem.id"
        :clientId="currentItem.clientId"
        :billIds="currentItem?.billIdList"
        :createResponse="currentItem"
        @confirm="modalConfirm"
        :title="editTitle"
        :showType="billType"
    />
    <!-- 对账 -->
    <CompareModal
        v-model:visible="showCompare"
        :exportTypeList="currentExportTypeList"
        :billId="currentItem.id"
        :batchOpt="batchOpt"
    />
    <!-- 批量对账 -->
    <!-- v-if="checkModalVisible" -->
    <BatchCompareModal
        ref="batchModalRef"
        :viewType="batchType"
        :visible="checkModalVisible"
        @cancel="closeBatchModal"
        @next="batchNext"
    />
</template>

<script lang="ts">
import { computed, defineComponent, h, nextTick, ref } from 'vue'
import CreateModal from '../CreateModal.vue'
import CopyModal from '../CopyModal.vue'
import AssureBill from './AssureBill.vue'

import CompareModal from '../CompareModal.vue'
import BatchCompareModal from '../BatchCompareModal.vue'
import { billStateList } from '/@/utils/dictionaries'
import { getSpecialList } from '/@/utils/api'
import { message, Modal, MonthPicker } from 'ant-design-vue'
import request from '/@/utils/request'
import moment from 'moment'
import { exportTypeList } from '/@/utils/dictionaries'
import { getDynamicText, pushBillIds } from '/@/utils'

export default defineComponent({
    name: 'AssureBillAccounting',
    components: { CreateModal, CopyModal, AssureBill, CompareModal, BatchCompareModal },
    setup() {
        const batchModalRef = ref()
        const params = ref({
            name: undefined,
            clientIds: [],
            paymentDate: moment().format('YYYY-MM'),
            title: undefined,
            specialIds: [],
            billState: undefined,
        })
        const talbeRef = ref()
        const searchData = () => {
            talbeRef.value.refresh(1)
        }
        const showAssureBill = ref(false)
        const tableDataList = ref([])

        const showCreate = ref(false)
        const checkModalVisible = ref(false)
        const batchType = ref('start')
        const billType = ref('add')
        const batchOpt = ref(null)
        const currentItem = ref<Recordable>({})
        const createRow = () => {
            currentItem.value = {}
            showCreate.value = true
        }
        const createClose = () => {
            showCreate.value = false
        }
        const createConfirm = (record) => {
            searchData()
            showCreate.value = false
            editRow(record, true)
        }
        const editTitle = ref('')
        const editRow = (record, isCreated) => {
            billType.value = isCreated
            console.log(billType.value)
            let billIdList: string[] = []
            if (isCreated) {
                currentItem.value = { ...record }
                // 保障账单
                showAssureBill.value = true
                return
            }

            console.log('edit record', record)
            if (record.strBillIds) {
                billIdList = record.strBillIds.split(',')
            } else {
                // 如果是已锁定，那就是查看所有
                if (record.billState) {
                    pushBillIds(billIdList, record, false)
                } else {
                    pushBillIds(billIdList, record, true)
                }
            }

            currentItem.value = { ...record, billIdList: billIdList }
            console.log('currentItem===', currentItem.value)

            // 保障账单
            showAssureBill.value = true
        }

        const currentExportTypeList = ref<LabelValueOptions>([])
        const showCompare = ref(false)
        const compareRow = (record) => {
            currentItem.value = { ...record }
            // 保障账单没有个税导盘
            currentExportTypeList.value = exportTypeList.filter((i) => i.value != 3 && i.value != 4)
            batchOpt.value = null
            showCompare.value = true
        }

        const copyErrList = ref<Recordable[]>([])
        const showTipTable = ref(false)
        const copyRow = (record) => {
            currentItem.value = { ...record }
            let paymentDate: any = undefined
            let billIdList: string[] = record.strBillIds.split(',')
            // pushBillIds(billIdList, record, false)
            Modal.confirm({
                title: '选择缴费年月',
                content: h(MonthPicker, {
                    onChange: (val) => {
                        paymentDate = val
                    },
                    format: 'YYYY-MM',
                    valueFormat: 'YYYY-MM',
                    placeholder: '缴费年月',
                    disabledDate: disabledDate,
                }),
                onOk: () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            console.log('copy-batch', billIdList)

                            const res = await request.post(`/api/hr-bills/copy-batch`, {
                                billIdList: billIdList,
                                paymentDate,
                            })
                            copyErrList.value =
                                Array.isArray(res?.differentList) && res?.differentList?.length ? res?.differentList : []
                            if (copyErrList.value.length) {
                                showTipTable.value = true
                            } else {
                                message.success('复制成功！')
                                searchData()
                            }
                            resolve(res)
                        } catch (error) {
                            reject(new Error('err' + error))
                        }
                    })
                },
            })
        }

        const selArr = ref<Recordable[]>([])
        const selectedRowsArr = (arr) => {
            selArr.value = arr
        }
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selArr.value)
        })
        const downloadSome = () => {
            talbeRef.value.exportRow('ids', exportText.value, params.value)
        }
        const deleteSome = () => {
            if (!selArr.value.length) {
                message.warning('请选择要删除的账单！')
                return
            }
            const ids = selArr.value.map((i) => i.id)
            Modal.confirm({
                title: '确认',
                content: '确认删除所选账单?',
                onOk: async () => {
                    await request.post(`/api/hr-bills/deletes`, ids)
                    searchData()
                },
            })
        }

        const closeBatchModal = () => {
            checkModalVisible.value = false
            batchType.value = 'start'
            batchModalRef.value.reModal()
        }

        const showBatchModal = () => {
            checkModalVisible.value = true
            batchType.value = 'start'
        }

        const batchNext = (opt) => {
            if (opt.type == 'start') {
                batchType.value = 'next'
            } else {
                showCompare.value = true
                currentExportTypeList.value = exportTypeList.filter((i) => i.value != 3 && i.value != 4)
                batchOpt.value = opt
                closeBatchModal()
            }
        }

        const modalConfirm = () => {
            showAssureBill.value = false
            searchData()
        }

        const disabledDate = (currentDate) => {
            // 最多可预做3个月的账单
            // return moment(currentDate) <= moment().subtract(1, 'month') || moment(currentDate) > moment().add(2, 'month')
            return moment(currentDate) > moment().add(2, 'month')
        }

        const createSalConfirm = (res) => {
            searchData()
            currentItem.value = { ...res }
        }

        const getChildrenMethod = (record) => {
            console.log('getChildrenMethod', record)

            const clientId = record.optClientId || record.clientId
            return new Promise(async (resolve) => {
                const res = await request.get(`/api/hr-bills/list/${record.billNo}/${clientId}`)
                resolve(
                    res.map((i) => ({
                        ...i,
                    })),
                )
            })
        }

        return {
            batchOpt,
            batchType,
            billType,
            checkModalVisible,
            closeBatchModal,
            batchNext,
            showBatchModal,
            exportText,
            currentExportTypeList,
            createSalConfirm,
            copyErrList,
            talbeRef,
            modalConfirm,
            searchData,
            editRow,
            copyRow,
            showCompare,
            compareRow,
            showAssureBill,
            showTipTable,
            showCreate,
            createConfirm,
            createClose,
            selectedRowsArr,
            downloadSome,
            deleteSome,
            createRow,
            currentItem,
            params,
            getChildrenMethod,
            editTitle,

            batchModalRef,

            columns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'left',
                    width: 150,
                    ellipsis: true,
                },
                {
                    title: '费用年月',
                    dataIndex: 'paymentDate',
                    align: 'center',
                    width: 60,
                },
                {
                    title: '标题',
                    dataIndex: 'title',
                    align: 'center',
                    width: 220,
                    ellipsis: true,
                },
                {
                    title: '专管员',
                    dataIndex: 'specialName',
                    align: 'center',
                    width: 60,
                },
                {
                    title: '状态',
                    dataIndex: 'billStateStr',
                    align: 'center',
                    width: 60,
                },
                {
                    title: '更新日期',
                    dataIndex: 'lastModifiedDate',
                    align: 'center',
                    width: 100,
                    // customRender: ({ record }) => {
                    //     return record.lastModifiedDate || record.createdDate
                    // },
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 130,
                    fixed: 'right',
                    slots: { customRender: 'operation' },
                },
            ],
            searchOptions: [
                {
                    label: '客户名称',
                    key: 'clientIds',
                    type: 'clientSelectTree',
                    multiple: true,
                    maxTag: 0,
                    checkStrictly: false,
                },
                {
                    label: '费用年月',
                    key: 'paymentDate',
                    type: 'month',
                },
                {
                    label: '标题',
                    key: 'title',
                },
                {
                    label: '专管员',
                    key: 'specialIds',
                    type: 'select',
                    options: [],
                    getMethod: getSpecialList,
                    multiple: true,
                },
                {
                    label: '状态',
                    key: 'billState',
                    type: 'select',
                    options: billStateList,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
</style>
