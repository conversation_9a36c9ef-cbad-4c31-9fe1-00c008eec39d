<template>
    <BasicEditModalSlot :visible="visible" title="保障账单" @cancel="modalClose" width="1400px">
        <Spin :spinning="loading">
            <div class="cell">
                <div class="title">保障账单信息</div>
                <div class="main">
                    <div class="row">
                        <template v-if="billIds && billIds.length">
                            <div class="col">
                                <div class="label">客户</div>
                                <div class="val">
                                    <Tree
                                        :selectable="false"
                                        :replaceFields="{ children: 'children', title: 'clientName', key: 'id' }"
                                        :tree-data="treeData"
                                    />
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="col">
                                <div class="label">客户编号</div>
                                <div class="val">{{ formData.unitNumber }}</div>
                            </div>
                            <div class="col">
                                <div class="label">客户名称</div>
                                <div class="val">{{ formData.clientName }}</div>
                            </div>
                            <div class="col" v-if="formData.parentClientName">
                                <div class="label">所属公司</div>
                                <div class="val">{{ formData.parentClientName }}</div>
                            </div>
                        </template>
                        <div class="col">
                            <div class="label">费用年月</div>
                            <div class="val">{{ formData.paymentDate }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col" style="width: 100%">
                            <div class="label">标题</div>
                            <div class="val">
                                <span v-if="!canEdit"> {{ formData.title }} </span>
                                <Input v-else v-model:value="formData.title" placeholder="标题" style="width: 300px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cell">
                <div class="detail-title">
                    <div class="title">
                        保障账单明细
                        <UsergroupAddOutlined v-if="formData.billState !== 1" class="icon" @click="insertStaff" />
                        <UsergroupDeleteOutlined v-if="formData.billState !== 1" class="icon" @click="removeStaff" />
                        <DownloadOutlined class="icon" @click="exportBill" />
                    </div>

                    <Tooltip placement="top" title="统一修改补差">
                        <EditOutlined @click="compensationVisible = true" />
                    </Tooltip>

                    <BasicEditModalSlot
                        v-model:visible="compensationVisible"
                        @cancel="compensationCancel"
                        title="统一修改补差"
                        width="500px"
                        centered
                    >
                        <Form :model="compensationFormData" ref="compensationForm" style="width: 100%">
                            <template v-for="(itemForm, i) in compensationOptions" :key="i">
                                <MyFormItem
                                    :width="itemForm.width"
                                    :item="itemForm"
                                    v-model:value="compensationFormData[itemForm.name]"
                                />
                            </template>
                        </Form>
                        <template #footer>
                            <Button @click="compensationCancel">取消</Button>
                            <Button type="primary" @click="compensationConfirm">确认</Button>
                        </template>
                    </BasicEditModalSlot>
                </div>
                <div class="main">
                    <div class="search-wrapper">
                        <Input
                            v-model:value="search.name"
                            placeholder="姓名"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                        <Input
                            v-model:value="search.certificateNum"
                            placeholder="身份证号"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                    </div>
                    <template v-if="tableData.length">
                        <Button
                            v-if="selectedStaff.length == tableData.length"
                            size="small"
                            type="primary"
                            @click="selAll(false)"
                        >
                            取消全选
                        </Button>
                        <Button v-else size="small" type="primary" @click="selAll(true)"> 选择全部</Button>
                    </template>
                    <Table
                        v-if="showTable"
                        :bordered="true"
                        :columns="columns"
                        :dataSource="[
                            ...tableData.slice(
                                pagination.pageSize * (pagination.current - 1),
                                pagination.pageSize * pagination.current,
                            ),
                        ]"
                        rowKey="id"
                        size="small"
                        :pagination="pagination"
                        :scroll="{ x: 100 }"
                        :rowSelection="{
                            selectedRowKeys: selectedStaff.map((i) => i.id),
                            onChange: (keys, rows) => selStaff(rows),
                        }"
                    />
                </div>
            </div>
            <div class="cell">
                <div class="title">
                    保障汇总账单
                    <DownloadOutlined class="icon" @click="exportTotalBill" />
                </div>
                <div class="main">
                    <BasicTable
                        :tableDataList="[
                            {
                                id: formData.hrBillTotal.id ?? null,
                                lastMonthMakeUp: formData.hrBillTotal.lastMonthMakeUp ?? 0,
                                serviceFeeTotal: formData.hrBillTotal.serviceFeeTotal ?? 0,
                                ...totalData,
                            },
                        ]"
                        :columns="totalColumns"
                        size="small"
                        :sorter="false"
                        :rowSelectionShow="false"
                    >
                        <template #lastMonthMakeUp="{ text }">
                            <span v-if="!canEdit || (billIds && billIds?.length > 1)"> {{ text }} </span>
                            <Input v-else :value="text" @change="lastMonthMakeUpChange" />
                        </template>
                        <template #serviceFeeTotal="{ text }">
                            <span v-if="!canEdit"> {{ text }} </span>
                            <Input v-else :value="text" @change="serviceFeeTotalChange" />
                        </template>
                    </BasicTable>
                </div>
            </div>
            <div class="cell" v-if="unInsertList.length">
                <div class="title" style="border-color: red">未入账员工明细</div>
                <div class="main">
                    <a @click="exportUninsert"> {{ formData.title }}未入账员工明细 </a>
                </div>
            </div>
        </Spin>

        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button v-if="formData.billState !== 1" @click="modalConfirm" :loading="confirmLoading" type="primary">保存 </Button>
        </template>
    </BasicEditModalSlot>
    <!-- 新增入账人员 -->
    <BasicEditModalSlot
        title="新增入账人员"
        width="1200px"
        :visible="showInsertStaff"
        @cancel="insertClose"
        okText="批量入账"
        @ok="insertConfirm"
        centered
    >
        <BasicTable
            ref="insertTableRef"
            :tableDataList="unInsertList"
            :columns="unInsertCols"
            :sorter="false"
            @selectedRowsArr="selStaff"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { computed, defineComponent, h, ref, toRefs, watch } from 'vue'
import downFile, { exportTable } from '/@/utils/downFile'
import {
    DownloadOutlined,
    EditOutlined,
    ExclamationCircleOutlined,
    UsergroupAddOutlined,
    UsergroupDeleteOutlined,
} from '@ant-design/icons-vue'
import request from '/@/utils/request'
import config from '/@/config'
import { divide, plus, round, times } from 'number-precision'
import { InputNumber, message, Modal, Spin } from 'ant-design-vue'
import { calcRecord } from '../util'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { ArrToTree, getValuesAndRules, previewFile } from '/@/utils/index'
import { valuesAndRules } from '/#/component'

export default defineComponent({
    name: 'AssureBill',
    components: { Spin, DownloadOutlined, UsergroupAddOutlined, UsergroupDeleteOutlined, EditOutlined },
    props: {
        visible: Boolean,
        billId: String,
        billIds: {
            type: Array,
            // eslint-disable-next-line vue/require-valid-default-prop
            default: [],
        },
        clientId: String,
        createResponse: Object,
        title: String,
        showType: String,
    },
    emits: ['confirm', 'update:visible'],
    setup(props, { emit }) {
        const { visible, billId, billIds, createResponse, clientId, title, showType } = toRefs(props)
        watch(visible, () => {
            if (visible.value) {
                visible.value && billId.value && getDetail()
                // getDetail()
            }
        })

        const formData = ref<Recordable>({
            hrBillTotal: {
                lastMonthMakeUp: 0,
                serviceFeeTotal: 0,
                total: 0,
            },
        })

        const pagination = ref({
            current: 1,
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条`,
            total: 0,
            onChange: (current) => {
                pagination.value.current = current
            },
        })
        const dynamicHeaderColumns = ref<Recordable[]>([])

        const columns = ref<Recordable[]>([])
        const totalColumns = ref<Recordable[]>([])
        const treeData = ref<TreeDataItem[]>([])
        const canEdit = computed(() => formData.value.billState !== 1)
        const isUpdated = ref(false)
        const tableData = ref<Recordable[]>([])
        const resTableData = ref<Recordable[]>([])
        const unInsertList = ref<Recordable[]>([])
        const totalData = computed(() => {
            resTableData.value.map((i) => {
                // console.log(i)
            })
            return {
                staffNum: resTableData.value.length,
                socialSecurityCardinal: resTableData.value
                    .map((i) => i.socialSecurityCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                socialSecurityCardinalPersonal: resTableData.value
                    .map((i) => i.socialSecurityCardinalPersonal)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                medicalInsuranceCardinal: resTableData.value
                    .map((i) => i.medicalInsuranceCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                medicalInsuranceCardinalTotal: resTableData.value
                    .map((i) => i.medicalInsuranceCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                medicalInsuranceCardinalPersonal: resTableData.value
                    .map((i) => i.medicalInsuranceCardinalPersonal)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                unitPensionTotal: resTableData.value.map((i) => i.unitPension).reduce((pre, cur) => plus(pre, cur), 0),
                unitUnemploymentTotal: resTableData.value.map((i) => i.unitUnemployment).reduce((pre, cur) => plus(pre, cur), 0),
                unitMedicalTotal: resTableData.value.map((i) => i.unitMedical).reduce((pre, cur) => plus(pre, cur), 0),
                workInjuryTotal: resTableData.value.map((i) => i.workInjury).reduce((pre, cur) => plus(pre, cur), 0),
                unitSocialSecurityMakeUpTotal: resTableData.value
                    .map((i) => i.unitSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                unitSubtotal: resTableData.value.map((i) => i.unitSubtotal).reduce((pre, cur) => plus(pre, cur), 0),
                personalPensionTotal: resTableData.value.map((i) => i.personalPension).reduce((pre, cur) => plus(pre, cur), 0),
                personalUnemploymentTotal: resTableData.value
                    .map((i) => i.personalUnemployment)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalMedicalTotal: resTableData.value.map((i) => i.personalMedical).reduce((pre, cur) => plus(pre, cur), 0),
                personalSocialSecurityMakeUpTotal: resTableData.value
                    .map((i) => i.personalSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                personalSubtotal: resTableData.value.map((i) => i.personalSubtotal).reduce((pre, cur) => plus(pre, cur), 0),
                socialSecurityTotal: resTableData.value
                    .map((i) => i.socialSecurityTotal ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                accumulationFundCardinal: resTableData.value
                    .map((i) => i.accumulationFundCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitAccumulationFundTotal: resTableData.value
                    .map((i) => i.unitAccumulationFund)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitAccumulationFundMakeUpTotal: resTableData.value
                    .map((i) => i.unitAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                personalAccumulationFundTotal: resTableData.value
                    .map((i) => i.personalAccumulationFund)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalAccumulationFundMakeUpTotal: resTableData.value
                    .map((i) => i.personalAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                accumulationFundTotal: resTableData.value
                    .map((i) => i.accumulationFundTotal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                // 新增
                unitPensionCardinalTotal: resTableData.value
                    .map((i) => i.unitPensionCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitUnemploymentCardinal: resTableData.value
                    .map((i) => i.unitUnemploymentCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitMaternityCardinalTotal: resTableData.value
                    .map((i) => i.unitMaternityCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                workInjuryCardinalTotal: resTableData.value
                    .map((i) => i.workInjuryCardinal ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                workInjuryCardinal: resTableData.value.map((i) => i.workInjuryCardinal).reduce((pre, cur) => plus(pre, cur), 0),
                personalPensionCardinal: resTableData.value
                    .map((i) => i.personalPensionCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalUnemploymentCardinal: resTableData.value
                    .map((i) => i.personalUnemploymentCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitSocialSecurityMakeUp: resTableData.value
                    .map((i) => i.unitSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                replenishWorkInjuryExpenseTotal: resTableData.value
                    .map((i) => i.replenishWorkInjuryExpense)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitLateFeeTotal: resTableData.value.map((i) => i.unitLateFee).reduce((pre, cur) => plus(pre, cur), 0),
                unitOtherFeeTotal: resTableData.value.map((i) => i.unitOtherFee).reduce((pre, cur) => plus(pre, cur), 0),
                personalSocialSecurityMakeUp: resTableData.value
                    .map((i) => i.personalSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalLargeMedicalExpenseTotal: resTableData.value
                    .map((i) => i.personalLargeMedicalExpense)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalOtherFeeTotal: resTableData.value.map((i) => i.personalOtherFee).reduce((pre, cur) => plus(pre, cur), 0),
                unitAccumulationFundMakeUp: resTableData.value
                    .map((i) => i.unitAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre, cur), 0),

                personalAccumulationFundMakeUp: resTableData.value
                    .map((i) => i.personalAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitLargeMedicalExpenseTotal: resTableData.value
                    .map((i) => i.unitLargeMedicalExpense)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalMaternityTotal: resTableData.value
                    .map((i) => i.personalMaternity)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalMaternityCardinal: resTableData.value
                    .map((i) => i.personalMaternityCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalPensionCardinalTotal: resTableData.value
                    .map((i) => i.personalPensionCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalMaternityCardinalTotal: resTableData.value
                    .map((i) => i.personalMaternityCardinal)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                medicalInsuranceCardinalPersonalTotal: resTableData.value
                    .map((i) => i.medicalInsuranceCardinalPersonal)
                    .reduce((pre, cur) => plus(pre, cur || 0), 0),
                unitMaternityTotal: resTableData.value.map((i) => i.unitMaternity).reduce((pre, cur) => plus(pre, cur), 0),
                unitPensionCardinal: resTableData.value.map((i) => i.unitPensionCardinal).reduce((pre, cur) => plus(pre, cur), 0),
                unitMaternityCardinal: resTableData.value
                    .map((i) => i.unitMaternityCardinal ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                personalUnemploymentCardinalTotal: resTableData.value
                    .map((i) => i.personalUnemploymentCardinal ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitUnemploymentCardinalTotal: resTableData.value
                    .map((i) => i.unitUnemploymentCardinal ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                unitBusinessInsuranceTotal: resTableData.value
                    .map((i) => i.unitBusinessInsurance ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                //个人企业年金
                personalEnterpriseAnnuityTotal: resTableData.value
                    .map((i) => i.personalEnterpriseAnnuity ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                // 单位商业保险
                commercialInsuranceTotal: resTableData.value
                    .map((i) => i.commercialInsurance ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                // 单位企业年金
                unitEnterpriseAnnuityTotal: resTableData.value
                    .map((i) => i.unitEnterpriseAnnuity ?? 0)
                    .reduce((pre, cur) => plus(pre, cur), 0),
                total: plus(
                    resTableData.value.map((i) => i.socialSecurityTotal ?? 0).reduce((pre, cur) => plus(pre, cur), 0),
                    resTableData.value.map((i) => i.accumulationFundTotal).reduce((pre, cur) => plus(pre, cur), 0),
                    formData.value.hrBillTotal?.serviceFeeTotal ?? 0,
                    formData.value.hrBillTotal?.lastMonthMakeUp ?? 0,
                ),
            }
        })

        const loading = ref(false)
        const showTable = ref(false)

        const getChildrenTitle = (tit: string, res: any, key: string) => {
            if (billIds.value && billIds.value.length && billIds.value.length > 1) {
                return tit
            } else return tit + times(Number(res[key]), 100) + '%'
        }
        const getChildrenTitles = (tit: string) => {
            if (billIds.value && billIds.value.length && billIds.value.length > 1) {
                return tit
            } else return tit
        }
        const getCellValue = (record, res) => {
            return (
                res.hrBillDTOList?.find((el) => {
                    return el.id == record.billId
                }) ?? res
            )
        }

        const getDetail = async () => {
            let res
            let item
            try {
                loading.value = true
                console.log('billIds===', billIds.value)

                if (billIds.value && billIds.value.length) {
                    res = await request.post(`/api/hr-bills/search-batch`, billIds.value)
                    if (showType.value) {
                        clientIdListsed.value = createResponse.value?.clientIdList
                        await request.post(`/api/hr-bill/dynamic/header/`, clientIdListsed.value).then((res) => {
                            dynamicHeaderColumns.value = [...dynamicMergeHeaderText(res)]
                            dynamicHeaderColumns.value.forEach((el) => {
                                dynamicArrHeader.value.push({
                                    title: el.title,
                                    dataIndex: el.dataIndex,
                                    width: 200,
                                    key: el.key,
                                    align: 'center',
                                    children: el.children?.map((res) => {
                                        return {
                                            title: res.title,
                                            dataIndex: res.dataIndex,
                                            key: res.key,
                                            dataIndexS: res.dataIndexS,
                                            width: 150,
                                            customRender: ({ record, index, text }) => {
                                                return canEdit.value
                                                    ? h(InputNumber, {
                                                          value: text ?? 0,
                                                          onChange: (e) => {
                                                              detailChange(
                                                                  e,
                                                                  (pagination.value.current - 1) * pagination.value.pageSize +
                                                                      index,
                                                                  res.dataIndex,
                                                              )
                                                          },
                                                          style: {
                                                              width: '100%',
                                                          },
                                                      })
                                                    : text ?? 0
                                            },
                                            align: 'center',
                                        }
                                    }),
                                })
                                dynamicTotalArrHeader.value.push({
                                    title: el.title,
                                    dataIndex: el.dataIndex,
                                    width: 200,
                                    key: el.key,
                                    align: 'center',
                                    children: el.children?.map((res) => {
                                        return {
                                            title: res.title,
                                            dataIndex: res.dataIndex + 'Total',
                                            key: res.key,
                                            dataIndexS: res.dataIndexS,
                                            width: 150,
                                            align: 'center',
                                        }
                                    }),
                                })
                            })
                        })
                    } else {
                        clientIdListsed.value = createResponse.value?.clientIdList
                        await request.post(`/api/hr-bill/show-dynamic-header `, billIds.value).then((res) => {
                            dynamicHeaderColumns.value = [...dynamicMergeHeaderText(res)]
                            dynamicHeaderColumns.value.forEach((el) => {
                                dynamicArrHeader.value.push({
                                    title: el.title,
                                    dataIndex: el.dataIndex,
                                    width: 200,
                                    key: el.key,
                                    align: 'center',
                                    children: el.children?.map((res) => {
                                        return {
                                            title: res.title,
                                            dataIndex: res.dataIndex,
                                            key: res.key,
                                            dataIndexS: res.dataIndexS,
                                            width: 150,
                                            customRender: ({ record, index, text }) => {
                                                return canEdit.value
                                                    ? h(InputNumber, {
                                                          value: text ?? 0,
                                                          onChange: (e) => {
                                                              detailChange(
                                                                  e,
                                                                  (pagination.value.current - 1) * pagination.value.pageSize +
                                                                      index,
                                                                  res.dataIndex,
                                                              )
                                                          },
                                                          style: {
                                                              width: '100%',
                                                          },
                                                      })
                                                    : text ?? 0
                                            },
                                            align: 'center',
                                        }
                                    }),
                                })
                                dynamicTotalArrHeader.value.push({
                                    title: el.title,
                                    dataIndex: el.dataIndex,
                                    width: 200,
                                    key: el.key,
                                    align: 'center',
                                    children: el.children?.map((res) => {
                                        return {
                                            title: res.title,
                                            dataIndex: res.dataIndex + 'Total',
                                            key: res.key,
                                            dataIndexS: res.dataIndexS,
                                            width: 150,
                                            align: 'center',
                                        }
                                    }),
                                })
                            })
                            console.log(dynamicArrHeader.value)
                        })
                    }
                    console.log(dynamicArrHeader.value)
                    treeData.value = ArrToTree(res.hrClientDTOList, { id: 'id', pid: 'parentId' })
                } else {
                    res = await request.get(`/api/hr-bills/${billId.value}`)
                }
            } catch (err) {
                modalClose()
            } finally {
                setTimeout(() => {
                    loading.value = false
                }, 300)
            }
            formData.value = {
                ...res,
                hrBillTotal: res.hrBillTotal || {
                    lastMonthMakeUp: 0,
                    serviceFeeTotal: 0,
                    total: 0,
                },
            }
            tableData.value = res.billDetailList || []
            resTableData.value = res.billDetailList || []
            pagination.value.total = tableData.value.length
            columns.value = [
                {
                    title: '序号',
                    dataIndex: 'index',
                    customRender: (record) => {
                        return record.index + 1
                    },
                    width: 80,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    fixed: 'left',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    fixed: 'left',
                    width: 200,
                    align: 'center',
                },
                {
                    title: '社保地区',
                    dataIndex: 'socialSecurityArea',
                    fixed: 'left',
                    width: 140,
                    align: 'center',
                },
                {
                    title: '员工状态',
                    dataIndex: 'staffStatusStr',
                    width: 100,
                    align: 'center',
                },

                {
                    title: '单位基数',
                    align: 'center',
                    dataIndex: 'unit',
                    children: [
                        {
                            title: getChildrenTitles('养老'),
                            dataIndex: 'unitPensionCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitPensionCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('失业'),
                            dataIndex: 'unitUnemploymentCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitUnemploymentCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('生育'),
                            dataIndex: 'unitMaternityCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitMaternityCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('医疗'),
                            dataIndex: 'medicalInsuranceCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'medicalInsuranceCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('工伤'),
                            dataIndex: 'workInjuryCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'workInjuryCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '个人基数',
                    align: 'center',
                    dataIndex: 'person',
                    children: [
                        {
                            title: getChildrenTitles('养老'),
                            dataIndex: 'personalPensionCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalPensionCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('失业'),
                            dataIndex: 'personalUnemploymentCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalUnemploymentCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('医疗'),
                            dataIndex: 'medicalInsuranceCardinalPersonal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'medicalInsuranceCardinalPersonal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('生育'),
                            dataIndex: 'personalMaternityCardinal',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalMaternityCardinal',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保单位缴纳部分',
                    dataIndex: 'unitPayment',
                    align: 'center',
                    children: [
                        {
                            title: getChildrenTitle('养老', res, 'unitPensionScale'),
                            dataIndex: 'unitPension',
                            width: 100,
                            customRender: ({ record, text }) => {
                                console.log()
                                record.unitPension = calcMoney(
                                    record.unitPensionCardinal || 0,
                                    getCellValue(record, res).unitPensionScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('失业', res, 'unitUnemploymentScale'),
                            dataIndex: 'unitUnemployment',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.unitUnemployment = calcMoney(
                                    record.unitUnemploymentCardinal || 0,
                                    getCellValue(record, res).unitUnemploymentScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('医疗', res, 'unitMedicalScale'),
                            dataIndex: 'unitMedical',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.unitMedical = calcMoney(
                                    record.medicalInsuranceCardinal || 0,
                                    getCellValue(record, res).unitMedicalScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('工伤', res, 'workInjuryScale'),
                            dataIndex: 'workInjury',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.workInjury = calcMoney(
                                    record.workInjuryCardinal || 0,
                                    getCellValue(record, res).workInjuryScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('生育', res, 'unitMaternityScale'),
                            dataIndex: 'unitMaternity',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.unitMaternity = calcMoney(
                                    record.unitMaternityCardinal || 0,
                                    getCellValue(record, res).unitMaternityScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'unitSocialSecurityMakeUp',
                            width: 110,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitSocialSecurityMakeUp',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('大额医疗'),
                            dataIndex: 'unitLargeMedicalExpense',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitLargeMedicalExpense',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('补充工伤'),
                            dataIndex: 'replenishWorkInjuryExpense',
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'replenishWorkInjuryExpense',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '企业年金',
                            dataIndex: 'unitEnterpriseAnnuity',
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitEnterpriseAnnuity',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '商业保险',
                            dataIndex: 'commercialInsurance',
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'commercialInsurance',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('滞纳金'),
                            dataIndex: 'unitLateFee',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitLateFee',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitles('其它'),
                            dataIndex: 'unitOtherFee',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitOtherFee',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '小计',
                            dataIndex: 'unitSubtotal',
                            width: 100,
                            align: 'center',
                            customRender: ({ record, text }) => {
                                record.unitSubtotal = plus(
                                    record.unitPension || 0,
                                    record.unitUnemployment || 0,
                                    record.workInjury || 0,
                                    record.unitMedical || 0,
                                    record.unitSocialSecurityMakeUp || 0,
                                    record.unitLargeMedicalExpense || 0,
                                    record.unitOtherFee || 0,
                                    record.unitLateFee || 0,
                                    record.replenishWorkInjuryExpense || 0,
                                    record.unitMaternity || 0,
                                    record.unitEnterpriseAnnuity || 0,
                                    record.commercialInsurance || 0,
                                )
                                return text ?? 0
                            },
                        },
                    ],
                },
                {
                    title: '社保个人缴纳部分',
                    align: 'center',
                    dataIndex: 'personalPayment',
                    children: [
                        {
                            title: getChildrenTitle('养老', res, 'personalPensionScale'),
                            dataIndex: 'personalPension',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalPension = calcMoney(
                                    record.personalPensionCardinal || 0,
                                    getCellValue(record, res).personalPensionScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('失业', res, 'personalUnemploymentScale'),
                            dataIndex: 'personalUnemployment',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalUnemployment = calcMoney(
                                    record.personalUnemploymentCardinal || 0,
                                    getCellValue(record, res).personalUnemploymentScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('医疗', res, 'personalMedicalScale'),
                            dataIndex: 'personalMedical',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalMedical = calcMoney(
                                    record.medicalInsuranceCardinalPersonal || 0,
                                    getCellValue(record, res).personalMedicalScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('生育', res, 'personalMaternityScale'),
                            dataIndex: 'personalMaternity',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalMaternity = calcMoney(
                                    record.personalMaternityCardinal || 0,
                                    getCellValue(record, res).personalMaternityScale,
                                    record.emolumentMultiple || 1,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'personalSocialSecurityMakeUp',
                            width: 110,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalSocialSecurityMakeUp',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '大额医疗',
                            dataIndex: 'personalLargeMedicalExpense',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalLargeMedicalExpense',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '企业年金',
                            dataIndex: 'personalEnterpriseAnnuity',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalEnterpriseAnnuity',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '其他',
                            dataIndex: 'personalOtherFee',
                            width: 100,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalOtherFee',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },

                        {
                            title: '小计',
                            dataIndex: 'personalSubtotal',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalSubtotal = plus(
                                    record.personalPension || 0,
                                    record.personalUnemployment || 0,
                                    record.personalMedical || 0,
                                    record.personalSocialSecurityMakeUp || 0,
                                    record.personalLargeMedicalExpense || 0,
                                    record.personalOtherFee || 0,
                                    record.personalMaternity || 0,
                                    record.personalEnterpriseAnnuity || 0,
                                )
                                return text ?? 0
                            },
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保总金额',
                    dataIndex: 'socialSecurityTotal',
                    width: 100,
                    align: 'center',
                    customRender: ({ record, text }) => {
                        record.socialSecurityTotal = plus(record.unitSubtotal ?? 0, record.personalSubtotal ?? 0)
                        return text ?? 0
                    },
                },
                {
                    title: '公积金基数',
                    dataIndex: 'accumulationFundCardinal',
                    width: 110,
                    customRender: ({ index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text ?? 0,
                                  onChange: (e) => {
                                      detailChange(
                                          e,
                                          (pagination.value.current - 1) * pagination.value.pageSize + index,
                                          'accumulationFundCardinal',
                                      )
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text ?? 0
                    },
                    align: 'center',
                },
                {
                    title: '公积金单位缴纳部分',
                    align: 'center',
                    children: [
                        {
                            title: getChildrenTitle('单位', res, 'unitAccumulationFundScale'),
                            dataIndex: 'unitAccumulationFund',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.unitAccumulationFund =
                                    round(
                                        times(
                                            Number(record.accumulationFundCardinal ?? 0),
                                            Number(getCellValue(record, res).unitAccumulationFundScale),
                                        ),
                                        0,
                                    ) * (record.emolumentMultiple || 1)
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'unitAccumulationFundMakeUp',
                            width: 110,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'unitAccumulationFundMakeUp',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金个人缴纳部分',
                    align: 'center',
                    children: [
                        {
                            title: getChildrenTitle('个人', res, 'personalAccumulationFundScale'),
                            dataIndex: 'personalAccumulationFund',
                            width: 100,
                            customRender: ({ record, text }) => {
                                record.personalAccumulationFund =
                                    round(
                                        times(
                                            Number(record.accumulationFundCardinal ?? 0),
                                            Number(getCellValue(record, res).personalAccumulationFundScale),
                                        ),
                                        0,
                                    ) * (record.emolumentMultiple || 1)
                                return text ?? 0
                            },
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'personalAccumulationFundMakeUp',
                            width: 110,
                            customRender: ({ index, text }) => {
                                return canEdit.value
                                    ? h(InputNumber, {
                                          value: text ?? 0,
                                          onChange: (e) => {
                                              detailChange(
                                                  e,
                                                  (pagination.value.current - 1) * pagination.value.pageSize + index,
                                                  'personalAccumulationFundMakeUp',
                                              )
                                          },
                                          style: {
                                              width: '100%',
                                          },
                                      })
                                    : text ?? 0
                            },
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金总金额',
                    dataIndex: 'accumulationFundTotal',
                    width: 150,
                    customRender: ({ record, text }) => {
                        record.accumulationFundTotal = plus(
                            record.unitAccumulationFund || 0,
                            record.personalAccumulationFund || 0,
                            record.unitAccumulationFundMakeUp || 0,
                            record.personalAccumulationFundMakeUp || 0,
                        )
                        return text ?? 0
                    },
                    align: 'center',
                },
                {
                    title: '总金额',
                    dataIndex: 'total',
                    width: 100,
                    align: 'center',
                    customRender: ({ record, text }) => {
                        record.total = plus(
                            record.socialSecurityTotal ?? 0,
                            record.accumulationFundTotal ?? 0,
                            record.serviceFee ?? 0,
                        )
                        return text ?? 0
                    },
                },
            ]
            const andkey = ['unitPayment', 'personalPayment']
            let dynamicHe = dynamicArrHeader.value?.filter((el) => el.dataIndex === andkey[0] || el.dataIndex === andkey[1])
            console.log()
            dynamicArrHeader.value = dynamicArrHeader.value?.filter(
                (el) => el.dataIndex != andkey[0] && el.dataIndex != andkey[1],
            )
            andkey?.forEach((el) => {
                let item: any = columns.value?.find((item) => item.dataIndex == el) ?? {}
                let merge: any = dynamicHe?.find((item) => item.dataIndex == el) ?? {}
                item?.children?.forEach((row) => {
                    merge?.children?.forEach((ele) => {
                        if (row.dataIndex == ele.dataIndex) {
                            let idx = merge?.children?.findIndex((it) => it.dataIndex == ele.dataIndex)
                            merge?.children.splice(idx, 1)
                        }
                    })
                })

                item?.children?.length > 0 &&
                    merge?.children?.length > 0 &&
                    item?.children?.splice(item.children.length - 1, 0, ...merge?.children)
            })
            console.log([...dynamicArrHeader.value])
            columns.value.splice(5, 0, ...dynamicArrHeader.value)
            if (dynamicHeaderColumns.value.length > 0) {
                columns.value.forEach((value, index) => {
                    if (value.dataIndex == 'unit') {
                        columns.value.splice(index, 1)
                    }
                    // value.children?.forEach((item, itemIndex, itemArray) => {
                    //     console.log(item.dataIndex)
                    //     if (item.dataIndex == 'replenishWorkInjuryExpense') {
                    //         itemArray.splice(itemIndex, 1)
                    //     }
                    // })
                })
                columns.value.forEach((value, index) => {
                    if (value.dataIndex == 'person') {
                        columns.value.splice(index, 1)
                    }
                    // value.children?.forEach((item, itemIndex, itemArray) => {
                    //     if (item.dataIndex == 'personalLargeMedicalExpense') {
                    //         itemArray.splice(itemIndex, 1)
                    //     }
                    // })
                })
                // columns.value.forEach((value, index) => {
                //     value.children?.forEach((item, itemIndex, itemArray) => {
                //         console.log(item.dataIndex)

                //         if (item.dataIndex == 'unitLargeMedicalExpense') {
                //             itemArray.splice(itemIndex, 1)
                //         }
                //     })
                // })
            }
            setTimeout(() => {
                // 这个组件渲染有些问题 有些地方需要重绘
                showTable.value = true
            }, 500)

            totalColumns.value = [
                {
                    title: '人数',
                    dataIndex: 'staffNum',
                    width: 100,
                },
                {
                    title: '单位基数',
                    dataIndex: 'unit',
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'unitPensionCardinal',
                            width: 110,
                        },
                        {
                            title: '失业',
                            dataIndex: 'unitUnemploymentCardinal',
                            width: 110,
                        },
                        {
                            title: '工伤',
                            dataIndex: 'workInjuryCardinal',
                            width: 110,
                        },
                        {
                            title: '医疗',
                            dataIndex: 'medicalInsuranceCardinal',
                            width: 110,
                        },
                        {
                            title: '生育',
                            dataIndex: 'unitMaternityCardinal',
                            width: 110,
                        },
                    ],
                },
                {
                    title: '个人基数',
                    dataIndex: 'person',
                    children: [
                        {
                            title: '养老',
                            dataIndex: 'personalPensionCardinal',
                            width: 110,
                        },
                        {
                            title: '失业',
                            dataIndex: 'personalUnemploymentCardinal',
                            width: 110,
                        },
                        {
                            title: '医疗',
                            dataIndex: 'medicalInsuranceCardinalPersonal',
                            width: 110,
                        },
                        {
                            title: '生育',
                            dataIndex: 'personalMaternityCardinal',
                            width: 110,
                        },
                    ],
                },

                {
                    title: '社保单位缴纳部分',
                    dataIndex: 'unitPayment',
                    align: 'center',
                    children: [
                        {
                            title: getChildrenTitle('养老', res, 'unitPensionScale'),
                            dataIndex: 'unitPensionTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('失业', res, 'unitUnemploymentScale'),
                            dataIndex: 'unitUnemploymentTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('医疗', res, 'unitMedicalScale'),
                            dataIndex: 'unitMedicalTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('工伤', res, 'workInjuryScale'),
                            dataIndex: 'workInjuryTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '生育',
                            dataIndex: 'unitMaternityTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'unitSocialSecurityMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '大额医疗',
                            dataIndex: 'unitLargeMedicalExpenseTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '补充工伤',
                            dataIndex: 'replenishWorkInjuryExpenseTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '企业年金',
                            dataIndex: 'unitEnterpriseAnnuityTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '商业保险',
                            dataIndex: 'commercialInsuranceTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '滞纳金',
                            dataIndex: 'unitLateFeeTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '其他',
                            dataIndex: 'unitOtherFeeTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '小计',
                            dataIndex: 'unitSubtotal',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保个人缴纳部分',
                    align: 'center',
                    dataIndex: 'personalPayment',
                    children: [
                        {
                            title: getChildrenTitle('养老', res, 'personalPensionScale'),
                            dataIndex: 'personalPensionTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('失业', res, 'personalUnemploymentScale'),
                            dataIndex: 'personalUnemploymentTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('医疗', res, 'personalMedicalScale'),
                            dataIndex: 'personalMedicalTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: getChildrenTitle('生育', res, 'personalMaternityScale'),
                            dataIndex: 'personalMaternityTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'personalSocialSecurityMakeUp',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '大额医疗',
                            dataIndex: 'personalLargeMedicalExpenseTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '企业年金',
                            dataIndex: 'personalEnterpriseAnnuityTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '其他',
                            dataIndex: 'personalOtherFeeTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '小计',
                            dataIndex: 'personalSubtotal',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保补差合计',
                    children: [
                        {
                            title: '单位',
                            dataIndex: 'unitSocialSecurityMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '个人',
                            dataIndex: 'personalSocialSecurityMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '社保总金额',
                    dataIndex: 'socialSecurityTotal',
                    width: 120,
                },
                {
                    title: '公积金基数',
                    dataIndex: 'accumulationFundCardinal',
                    width: 120,
                },
                {
                    title: '公积金单位缴纳部分',
                    children: [
                        {
                            title: getChildrenTitle('单位', res, 'unitAccumulationFundScale'),
                            dataIndex: 'unitAccumulationFundTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'unitAccumulationFundMakeUp',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金个人缴纳部分',
                    children: [
                        {
                            title: getChildrenTitle('个人', res, 'personalAccumulationFundScale'),
                            dataIndex: 'personalAccumulationFundTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '补差',
                            dataIndex: 'personalAccumulationFundMakeUp',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金补差合计',
                    children: [
                        {
                            title: '单位',
                            dataIndex: 'unitAccumulationFundMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '个人',
                            dataIndex: 'personalAccumulationFundMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '公积金总金额',
                    dataIndex: 'accumulationFundTotal',
                    width: 150,
                },
                // {
                //     title: '服务费',
                //     dataIndex: 'serviceFeeTotal',
                //     width: 120,
                //     slots: { customRender: 'serviceFeeTotal' },
                // },
                {
                    title: '总金额',
                    dataIndex: 'total',
                    width: 120,
                },
            ]

            if (dynamicTotalArrHeader.value?.length > 0) {
                totalColumns.value.forEach((value, index) => {
                    if (value.dataIndex == 'unit') {
                        totalColumns.value.splice(index, 1)
                    }
                    value.children?.forEach((item, itemIndex, itemArray) => {
                        // console.log(item.dataIndex)
                        if (item.dataIndex == 'replenishWorkInjuryExpense') {
                            itemArray.splice(itemIndex, 1)
                        }
                    })
                })
                totalColumns.value.forEach((value, index) => {
                    if (value.dataIndex == 'person') {
                        totalColumns.value.splice(index, 1)
                    }
                    value.children?.forEach((item, itemIndex, itemArray) => {
                        if (item.dataIndex == 'personalLargeMedicalExpenseTotal') {
                            itemArray.splice(itemIndex, 1)
                        }
                    })
                })
                totalColumns.value.forEach((value, index) => {
                    value.children?.forEach((item, itemIndex, itemArray) => {
                        if (item.dataIndex == 'unitLargeMedicalExpenseTotal') {
                            itemArray.splice(itemIndex, 1)
                        }
                    })
                })
            }
            // console.log(totalColumns.value)
            let tempArr = dynamicTotalArrHeader.value?.filter((el) => el.dataIndex === andkey[0] || el.dataIndex === andkey[1])
            dynamicTotalArrHeader.value = dynamicTotalArrHeader.value?.filter(
                (el) => el.dataIndex != andkey[0] && el.dataIndex != andkey[1],
            )
            andkey.forEach((el) => {
                let item: any = totalColumns.value?.find((item) => item.dataIndex == el)
                let hebing: any = tempArr?.find((item) => item.dataIndex == el)
                item?.children?.forEach((row) => {
                    hebing?.children?.forEach((ele) => {
                        if (row.dataIndex == ele.dataIndex) {
                            let idx = hebing?.children?.findIndex((it) => it.dataIndex == ele.dataIndex)
                            hebing?.children.splice(idx, 1)
                        }
                    })
                })
                item?.children?.length > 0 &&
                    hebing?.children?.length > 0 &&
                    item?.children?.splice(item.children.length - 1, 0, ...hebing.children)
            })
            totalColumns.value.splice(1, 0, ...dynamicTotalArrHeader.value)
            // 获取未入账员工
            let list
            if (billIds.value && billIds.value.length) list = await request.post(`/api/hr-bill-details/list-batch`, billIds.value)
            else list = await request.get(`/api/hr-bill-details/list?billId=${billId.value}`)
            unInsertList.value = list
        }
        // 获取动态合并的表头
        const dynamicArrHeader = ref<Recordable[]>([])
        const dynamicTotalArrHeader = ref<Recordable[]>([])
        const calcDynamicTotal = (title) => {
            console.log(title)
            return resTableData.value.map((el) => el[title]).reduce((pre, cur) => plus(pre, cur ?? 0), 0)
        }
        const clientIdListsed = ref<inObject[]>([])

        /**
         * 动态合并表头
         * @param cols
         * @param isTotal
         */
        const dynamicMergeHeaderText = (cols) => {
            let returnArr: any = []
            const dynamicArr = ['personalCardinal', 'unitCardinal']
            const fixArr = cols.sort((a, b) => {
                return dynamicArr.indexOf(b.dataIndex) - dynamicArr.indexOf(a.dataIndex)
            })
            fixArr.forEach((el) => {
                if (!returnArr.length) returnArr.push(el)
                else {
                    const tempObj = returnArr.find((i) => i.dataIndex == el.dataIndex)
                    if (tempObj) {
                        tempObj.children = [...tempObj.children, ...el.children]
                    } else {
                        returnArr.push(el)
                    }
                }
            })
            console.log(returnArr)
            return returnArr
        }
        const search = ref({
            name: '',
            certificateNum: '',
        })

        const filterTableData = () => {
            let filterOdds
            if (!search.value.name && !search.value.certificateNum) {
                tableData.value = resTableData.value
                pagination.value.current = 1
                pagination.value.total = tableData.value.length
                return
            } else if (!search.value.name && search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.certificateNum.includes(search.value.certificateNum)
                }
            } else if (search.value.name && !search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name)
                }
            } else {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name) && el.certificateNum.includes(search.value.certificateNum)
                }
            }
            tableData.value = resTableData.value.filter((el) => {
                return filterOdds(el)
            })
            pagination.value.current = 1
            pagination.value.total = tableData.value.length
        }

        const selAll = (isSel) => {
            selectedStaff.value = isSel ? [...tableData.value] : []
        }
        const resetData = () => {
            formData.value = {
                hrBillTotal: {
                    lastMonthMakeUp: 0,
                    serviceFeeTotal: 0,
                    total: 0,
                },
            }
            search.value = {
                name: '',
                certificateNum: '',
            }
            resTableData.value = []
            tableData.value = []
            pagination.value.total = 0
            pagination.value.current = 1
            isUpdated.value = false
            showTable.value = false
            selectedStaff.value = []
            columns.value = []
            dynamicHeaderColumns.value = []
            dynamicArrHeader.value = []
            dynamicTotalArrHeader.value = []
            totalColumns.value = []
        }

        const confirmLoading = ref(false)
        const modalConfirm = async () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()
            confirmLoading.value = true
            let [salaryBillFlag, reconciliationFlag] = [false, false]
            const originList = formData.value.billDetailList
            const originIds = originList.map((i) => i.id)
            const currentIds = tableData.value.map((i) => i.id)
            const deleteList = originList
                .filter((i) => beishanchude.value.includes(i.id))
                .map((i) => ({
                    ...i,
                    updateState: 2,
                }))
            beishanchude.value = []
            const dataList = [...tableData.value]
            dataList.forEach((i) => {
                if (!originIds.includes(i.id)) {
                    i.updateState = 1
                } else {
                    i.updateState = 3
                }
            })
            if (billIds.value && billIds.value.length) await saveHandler()
            else {
                request
                    .post(`/api/hr-bills/get-prompt-language`, {
                        id: formData.value.id,
                        title: formData.value.title,
                        billDetailList: [...dataList, ...deleteList],
                        paymentDate: formData.value.paymentDate,
                        clientId: formData.value.clientId,
                        hrBillTotal: {
                            id: formData.value.hrBillTotal.id ?? null,
                            lastMonthMakeUp: formData.value.hrBillTotal.lastMonthMakeUp ?? 0,
                            serviceFeeTotal: formData.value.hrBillTotal.serviceFeeTotal ?? 0,
                            ...totalData.value,
                            isModifyServiceFee: formData.value.hrBillTotal?.isModifyServiceFee || false,
                        },
                    })
                    .then(async (res) => {
                        confirmLoading.value = false
                        salaryBillFlag = res.salaryBillFlag
                        reconciliationFlag = res.reconciliationFlag
                        let tips
                        if (res.salaryBillFlag && res.reconciliationFlag)
                            tips = h('div', [
                                h('div', '由于您修改了保障账单，之前的批量对账数据将被删除，请及时通知对账人员'),
                                h('br'),
                                h('div', '保存修改将会删除同一个月份的薪酬账单'),
                                h('br'),
                            ])
                        else if (res.salaryBillFlag && !res.reconciliationFlag)
                            tips = h('div', [h('div', '保存修改将会删除同一个月份的薪酬账单'), h('br')])
                        else if (!res.salaryBillFlag && res.reconciliationFlag)
                            tips = h('div', [
                                h('div', '由于您修改了保障账单，之前的批量对账数据将被删除，请及时通知对账人员'),
                                h('br'),
                            ])
                        else await saveHandler()
                        if (tips)
                            Modal.confirm({
                                title: tips,
                                icon: h(ExclamationCircleOutlined),
                                onOk: async () => {
                                    await saveHandler(res.salaryBillIdList)
                                },
                                onCancel() {
                                    console.log('Cancel')
                                },
                            })
                    })
                    .catch((err) => {
                        confirmLoading.value = false
                        console.log(err)
                    })
            }

            function saveHandler(salaryBillIdList?) {
                request
                    .put(`/api/hr-bills`, {
                        id: formData.value.id,
                        billType: 1,
                        salaryBillFlag: salaryBillFlag,
                        reconciliationFlag: reconciliationFlag,
                        title: formData.value.title,
                        billDetailList: [...dataList, ...deleteList],
                        billIdList: billIds.value && billIds.value?.length ? billIds.value : undefined,
                        clientIdList: billIds.value && billIds.value?.length ? formData.value?.clientIdList : undefined,
                        salaryBillIdList: salaryBillIdList,
                        hrBillTotal: {
                            id: formData.value.hrBillTotal.id ?? null,
                            billId: createResponse.value?.hrBillTotal?.billId,
                            lastMonthMakeUp: formData.value.hrBillTotal.lastMonthMakeUp ?? 0,
                            serviceFeeTotal: formData.value.hrBillTotal.serviceFeeTotal ?? 0,
                            ...totalData.value,
                            isModifyServiceFee: formData.value.hrBillTotal?.isModifyServiceFee || false,
                        },
                    })
                    .then((res) => {
                        resetData()
                        emit('confirm')
                        message.success('保存成功')
                        confirmLoading.value = false
                    })
                    .catch((err) => {
                        console.log(err)
                        confirmLoading.value = false
                    })
            }
        }
        const modalClose = () => {
            resetData()
            emit('update:visible', false)
        }

        const detailChange = (val, idx, key, type = 'number') => {
            if (formData.value.billState === 1) {
                message.warn('锁定状态账单不可更改！')
                return
            }
            dynamicArrHeader.value.map((item) => {
                item.children?.forEach((el) => {
                    if (key.includes(el.dataIndex) && el.dataIndexS) {
                        el.dataIndexS.forEach((ele) => {
                            console.log(ele)
                            tableData.value[idx][ele] = round(Number(val), 2)
                        })
                    }
                })
            })

            tableData.value[idx][key] = type == 'number' ? round(Number(val), 2) : val?.target?.value
            tableData.value[idx] = calcRecord(tableData.value[idx], formData.value)
            tableData.value = [...tableData.value]
            pagination.value.total = tableData.value.length
            isUpdated.value = true

            if (key == 'serviceFee') {
                formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                    .map((i) => i.serviceFee)
                    .reduce((pre, cur) => plus(pre, cur), 0)
                formData.value.hrBillTotal.isModifyServiceFee = false
            }
        }
        const exportBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            if (!selectedStaff.value.length) {
                message.warning('请选择账单明细!')
                return
            }
            downFile('post', `/api/hr-bills-detail/download-bill-detail`, `${formData.value.title}保障账单明细`, {
                billId: billId.value,
                billIdList: billIds.value && billIds.value?.length ? billIds.value : undefined,
                billDetailIds: selectedStaff.value.map((i) => i.id),
            })
        }
        const exportTotalBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            exportTable(
                totalColumns.value,
                [
                    {
                        id: formData.value.hrBillTotal.id ?? 0,
                        lastMonthMakeUp: formData.value.hrBillTotal.lastMonthMakeUp ?? 0,
                        serviceFeeTotal: formData.value.hrBillTotal.serviceFeeTotal ?? 0,
                        ...totalData.value,
                    },
                ],
                `${formData.value.title}保障账单汇总`,
                {
                    rowIndex: 3,
                    exceptCols: ['A'],
                    widthCols: [{ wch: 10 }, [...Array(4).fill({ wch: 25 })], [...Array(19).fill({ wch: 20 })], { wch: 25 }],
                },
            )
        }

        const lastMonthMakeUpChange = (e) => {
            if (formData.value.billState === 1) {
                message.warn('锁定状态账单不可更改！')
                return
            }
            formData.value.hrBillTotal.lastMonthMakeUp = e.target.value
            isUpdated.value = true
        }
        const serviceFeeTotalChange = (e) => {
            if (formData.value.billState === 1) {
                message.warn('锁定状态账单不可更改！')
                return
            }
            formData.value.hrBillTotal.serviceFeeTotal = e.target.value
            formData.value.hrBillTotal.isModifyServiceFee = true
            isUpdated.value = true
        }

        const insertTableRef = ref()
        const tableRef = ref()
        const showInsertStaff = ref(false)
        const selectedStaff = ref<Recordable[]>([])
        const insertStaff = () => {
            showInsertStaff.value = true
        }
        /**
         * 根据基数，比例，倍数计算金额
         * 先计算每月的金额四舍五入后再乘以倍数
         * @param cardinal
         * @param scale
         * @param multiple
         */
        const calcMoney = (cardinal, scale, multiple) => {
            return round(times(Number(cardinal) || 0, Number(scale)), 2) * Number(multiple)
        }
        const calcCurrentPage = (total) => {
            return Math.ceil(divide(total, 10))
        }
        const beishanchude = ref<any>([])
        const removeStaff = () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()

            if (!selectedStaff.value.length) {
                message.warning('请选择要删除入账的人员！')
                return
            }
            unInsertList.value = [...unInsertList.value, ...selectedStaff.value]
            const ids = selectedStaff.value.map((i) => i.id)
            const changedList = tableData.value.filter((i) => !ids.includes(i.id))
            beishanchude.value.push(...selectedStaff.value.map((i) => i.id))
            resTableData.value = changedList
            filterTableData()
            pagination.value.current = calcCurrentPage(pagination.value.total)
            isUpdated.value = true
            selectedStaff.value = []
            tableRef.value && tableRef.value.checkboxReset()

            // 重新计算服务费汇总
            formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                .map((i) => i.serviceFee)
                .reduce((pre, cur) => plus(pre, cur), 0)
            formData.value.hrBillTotal.isModifyServiceFee = false
        }
        const selStaff = (list) => {
            selectedStaff.value = list
        }
        const insertConfirm = async () => {
            if (!selectedStaff.value.length) {
                message.warning('请选择要入账的人员！')
                return
            }
            const ids = selectedStaff.value.map((i) => i.id)
            // tableData 添加
            const res = await request.post(`/api/hr-bill-details/batchAdd`, {
                ids: ids,
                billIds: createResponse.value?.billIdList,
            })
            resTableData.value = [...resTableData.value, ...res]
            filterTableData()
            // calcAllRecord(tableData.value, formData.value)
            isUpdated.value = true
            // unInsertList 删除
            unInsertList.value = unInsertList.value.filter((i) => !ids.includes(i.id))
            insertClose()

            // 重新计算服务费汇总
            formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                .map((i) => i.serviceFee)
                .reduce((pre, cur) => plus(pre, cur), 0)
            formData.value.hrBillTotal.isModifyServiceFee = false
        }
        const insertClose = () => {
            selectedStaff.value = []
            showInsertStaff.value = false
            insertTableRef.value.checkboxReset()
        }

        const exportUninsert = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单明细，请保存后重新打开再导出未入账员工明细！',
                    duration: 5,
                })
                return
            }
            if (billIds.value && billIds.value.length) downFile('post', `/api/hr-bill-details/export-batch`, '', billIds.value)
            else downFile('get', `/api/hr-bill-details/export?billId=${billId.value}`, `${formData.value.title}未入账员工明细`)
        }

        /**
         * @一键补差填入
         */
        const compensationOptions = ref<valuesAndRules[]>([
            {
                label: '社保单位缴纳部分补差',
                name: 'unitSocialSecurityMakeUp',
                // ruleType: 'number',
                type: 'number',
            },
            {
                label: '社保个人缴纳部分补差',
                name: 'personalSocialSecurityMakeUp',
                type: 'number',
            },
            {
                label: '公积金单位缴纳部分补差',
                name: 'unitAccumulationFundMakeUp',
                type: 'number',
            },
            {
                label: '公积金个人缴纳部分补差',
                name: 'personalAccumulationFundMakeUp',
                type: 'number',
            },
        ])
        const { values: initFormData, rules } = getValuesAndRules(compensationOptions.value)
        const compensationForm = ref()
        const compensationFormData = ref<any>(initFormData)
        const compensationVisible = ref(false)

        const resetFormData = () => {
            compensationFormData.value = initFormData
            compensationForm.value?.resetFields()
        }
        watch(compensationVisible, () => {
            if (compensationVisible.value) {
            } else {
                resetFormData()
            }
        })
        const compensationConfirm = () => {
            resTableData.value?.forEach((item: any) => {
                for (const key in compensationFormData.value) {
                    item[key] = compensationFormData.value[key] ? compensationFormData.value[key] : 0
                }
                item.unitSubtotal = plus(
                    item.unitPension || 0,
                    item.unitUnemployment || 0,
                    item.workInjury || 0,
                    item.unitMedical || 0,
                    item.unitSocialSecurityMakeUp || 0,
                    item.unitLargeMedicalExpense || 0,
                    item.unitOtherFee || 0,
                    item.unitLateFee || 0,
                    item.replenishWorkInjuryExpense || 0,
                    item.unitMaternity || 0,
                    item.unitEnterpriseAnnuity || 0,
                    item.commercialInsurance || 0,
                )
                item.personalSubtotal = plus(
                    item.personalPension || 0,
                    item.personalUnemployment || 0,
                    item.personalMedical || 0,
                    item.personalSocialSecurityMakeUp || 0,
                    item.personalLargeMedicalExpense || 0,
                    item.personalOtherFee || 0,
                    item.personalMaternity || 0,
                    item.personalEnterpriseAnnuity || 0,
                )
                item.socialSecurityTotal = plus(item.unitSubtotal ?? 0, item.personalSubtotal ?? 0)
                item.total = plus(item.socialSecurityTotal ?? 0, item.accumulationFundTotal ?? 0, item.serviceFee ?? 0)
            })
            tableData.value = resTableData.value
            // console.log('@@@@@@=>', resTableData.value)
            // console.log('@@@@@@=>', tableData.value)
            compensationVisible.value = false
        }
        const compensationCancel = () => {
            compensationVisible.value = false
        }

        return {
            search,
            filterTableData,
            treeData,
            pagination,
            canEdit,
            selAll,
            exportUninsert,
            loading,
            confirmLoading,
            showTable,
            previewFile,
            tableRef,
            removeStaff,
            insertClose,
            insertTableRef,
            insertConfirm,
            selectedStaff,
            selStaff,
            unInsertList,
            insertStaff,
            lastMonthMakeUpChange,
            serviceFeeTotalChange,
            detailChange,
            config,
            formData,
            exportBill,
            exportTotalBill,
            showInsertStaff,
            totalData,
            tableData,
            modalConfirm,
            modalClose,
            columns,
            totalColumns,
            dynamicHeaderColumns,
            compensationConfirm,
            compensationCancel,
            compensationVisible,
            compensationOptions,
            compensationFormData,
            compensationForm,
            rules,

            unInsertCols: [
                {
                    title: '员工编号',
                    dataIndex: 'systemNum',
                    width: 160,
                    fixed: 'left',
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    fixed: 'left',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 120,
                },
                {
                    title: '员工类型',
                    dataIndex: 'personnelTypeStr',
                    width: 120,
                },
                {
                    title: '员工状态',
                    dataIndex: 'staffStatusStr',
                    width: 120,
                },
                {
                    title: '参保状态',
                    dataIndex: 'izInsuredStr',
                    width: 120,
                },
                {
                    title: '未入账原因',
                    dataIndex: 'reason',
                    width: 200,
                    fixed: 'right',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.search-wrapper {
    display: flex;
    margin: 10px 0;

    & > input {
        width: 230px;
        margin-right: 20px;
    }
}

.detail-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid @primary-color;
    padding: 0 40px 0 10px;

    .icon,
    .anticon {
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        margin-left: 10px;
    }
}

.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .icon {
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }
    }

    .main {
        padding: 10px 0;

        .row {
            margin: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;

            .col {
                // width: 230px;
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .label {
                    width: 80px;
                    text-align: right;

                    &:after {
                        content: '：';
                    }
                }
            }
        }
    }
}
</style>
