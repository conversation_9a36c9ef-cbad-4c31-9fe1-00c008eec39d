<template>
    <BasicEditModalSlot :visible="visible" :title="title" :width="modalWidth" @cancel="closeModal">
        <Form ref="formInline" :model="formData" :label-col="{ span: 6 }" v-if="viewType == 'start'">
            <FormItem
                label="费用年月"
                name="paymentDate"
                :rules="{ required: true, type: 'string', message: '请选择费用年月', trigger: ['blur', 'change'] }"
            >
                <MonthPicker
                    style="width: 80%"
                    v-model:value="formData.paymentDate"
                    placeholder="费用年月"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                    :getCalendarContainer="getPopupContainer"
                />
            </FormItem>
        </Form>
        <template v-else>
            <div class="header">
                <Button type="primary" style="margin-bottom: 10px" @click="exportData">导出</Button>
            </div>
            <BasicTable
                ref="tableRefCustomer"
                :useIndex="true"
                :tableDataList="tableDataList"
                :rowSelectionShow="false"
                :params="params"
                :columns="columns"
            />
        </template>
        <template #footer>
            <Button @click="closeModal" class="btn">取消</Button>
            <Button @click="nextFn" type="primary" class="btn" :disabled="!tableDataList.length && viewType == 'next'">
                {{ okText }}
            </Button>
        </template>
    </BasicEditModalSlot>
</template>
<script lang="ts" setup>
import moment from 'moment'
import { computed, ref, toRefs, watch } from 'vue'
import downFile from '/@/utils/downFile'
import request from '/@/utils/request'
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    viewType: String,
})
const { visible, viewType } = toRefs<any>(props)
const emits = defineEmits(['cancel', 'next'])

const tableDataList = ref([])

const modalWidth = ref('450px')
const okText = ref('下一步')

const title = computed(() => {
    if (viewType.value == 'start') return '批量对账'
    else return '不可参与对账账户（未有本月保障账单）'
})

watch(
    viewType,
    (val: string) => {
        if (val == 'start') {
            modalWidth.value = '450px'
            okText.value = '下一步'
        }
        if (val == 'next') {
            modalWidth.value = '900px'
            okText.value = '已确认，下一步'
        }
    },
    {
        immediate: true,
    },
)

const formData = ref({
    paymentDate: undefined,
})
// Form 实例
const formInline = ref(null) as any
const tableRefCustomer = ref()
//筛选
const params = ref<any>({
    idList: undefined,
})

//导出函数
const exportData = () => {
    downFile(
        'get',
        `/api/hr-bill-compare-configs/existence-security-bill-export?paymentDate=${formData.value.paymentDate}&billType=1`,
        '不可对账账户信息.xlsx',
    )
}

//表格数据
const columns = [
    {
        title: '客户编号',
        dataIndex: 'unitNumber',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '客户名称',
        dataIndex: 'clientName',
        align: 'center',
        sorter: false,
    },
    {
        title: '当前协议编号',
        dataIndex: 'agreementNumber',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '协议类型',
        dataIndex: 'agreementTypekey',
        align: 'center',
        sorter: false,
        width: 120,
    },
    {
        title: '员工数量',
        dataIndex: 'peoplesum',
        align: 'center',
        width: 90,
        sorter: false,
    },
]

const nextFn = () => {
    let fn
    if (viewType.value == 'start') fn = nextStep
    else fn = confirmStep
    return fn()
}

const nextStep = async () => {
    console.log(formInline.value)
    formInline.value
        .validate()
        .then(async () => {
            let res = await request.get(`/api/hr-bill-compare-configs/existence-security-bill`, {
                paymentDate: formData.value.paymentDate,
                billType: 1,
            })
            emits('next', { type: 'start', paymentDate: formData.value.paymentDate })
            if (res.length) {
                tableDataList.value = res
            }
        })
        .catch((error) => {
            console.log('表单验证失败', error)
        })
}
const confirmStep = () => {
    emits('next', { type: 'next', paymentDate: formData.value.paymentDate })
}
// cancel handle
const closeModal = () => {
    emits('cancel')
    reModal()
}
const reModal = () => {
    formData.value.paymentDate = undefined
    tableDataList.value = []
}
defineExpose({
    reModal,
})
const getPopupContainer = () => {
    return document.body
}
const disabledDate = (currentDate) => {
    return currentDate < moment().subtract(1, 'month') || currentDate > moment().endOf('month')
}
</script>
<style lang="less" scoped>
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
