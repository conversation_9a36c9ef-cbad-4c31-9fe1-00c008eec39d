<template>
    <BasicEditModalSlot :visible="visible" title="其他账单" @cancel="modalClose" width="1400px">
        <Spin :spinning="loading">
            <div class="cell">
                <div class="title">其他账单信息</div>
                <div class="main">
                    <div class="row">
                        <template v-if="createResponse?.billIdList && createResponse?.billIdList.length">
                            <div class="col">
                                <div class="label">客户</div>
                                <div class="val">
                                    <Tree
                                        :selectable="false"
                                        :replaceFields="{ children: 'children', title: 'clientName', key: 'id' }"
                                        :tree-data="treeData"
                                    />
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="col">
                                <div class="label">客户编号</div>
                                <div class="val">{{ formData.unitNumber }}</div>
                            </div>
                            <div class="col">
                                <div class="label">客户名称</div>
                                <div class="val">{{ formData.clientName }}</div>
                            </div>
                            <div class="col" v-if="formData.parentClientName">
                                <div class="label">所属公司</div>
                                <div class="val">
                                    {{ formData.parentClientName }}
                                </div>
                            </div>
                        </template>
                        <div class="col">
                            <div class="label">费用年月</div>
                            <div class="val">{{ formData.paymentDate }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col" style="width: 100%">
                            <div class="label">标题</div>
                            <div class="val">
                                <span v-if="formData.billState === 1"> {{ formData.title }} </span>
                                <Input v-else v-model:value="formData.title" placeholder="标题" style="width: 300px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Row>
                <Col span="12">
                    <div class="cell">
                        <div class="title">
                            原始账单明细
                            <SelectOutlined v-if="canEdit" class="icon" @click="uploadNewOrigin" title="更换其他原始账单" />
                            <DownloadOutlined class="icon" @click="exportOrigin" title="下载原单" />
                        </div>
                        <div class="main">
                            <a @click="previewFile(formData.originBillUrl)"> {{ formData.originBillName }} </a>
                        </div>
                    </div>
                </Col>
                <Col span="12" v-if="unInsertList.length && formData.otherBillFlag !== 1">
                    <div class="cell">
                        <div class="title" style="border-color: red">
                            未入账员工明细
                            <DownloadOutlined class="icon" @click="exportUninsert" />
                        </div>
                        <div class="main">
                            <a @click="exportUninsert">{{ formData.title }}未入账员工明细</a>
                        </div>
                    </div>
                </Col>
            </Row>

            <div class="cell">
                <div class="title">
                    其他账单明细
                    <UsergroupAddOutlined v-if="canEdit" class="icon" @click="insertStaff" />
                    <UsergroupDeleteOutlined v-if="canEdit" class="icon" @click="removeStaff" />
                    <DownloadOutlined class="icon" @click="exportBill" />
                    <TransactionOutlined class="icon" v-if="formData.otherBillFlag !== 1" @click="exportBankInsert" />
                </div>
                <div class="main">
                    <div class="search-wrapper">
                        <Input
                            v-model:value="search.name"
                            :placeholder="formData.otherBillFlag === 1 ? '客户名' : '姓名'"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                        <Input
                            v-if="formData.otherBillFlag !== 1"
                            v-model:value="search.certificateNum"
                            placeholder="身份证号"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                    </div>
                    <template v-if="tableData.length">
                        <Button
                            v-if="selectedStaff.length == tableData.length"
                            size="small"
                            type="primary"
                            @click="selAll(false)"
                        >
                            取消全选
                        </Button>
                        <Button v-else size="small" type="primary" @click="selAll(true)"> 选择全部 </Button>
                    </template>
                    <Table
                        v-if="showTable"
                        :bordered="true"
                        :columns="columns"
                        :dataSource="[
                            ...tableData.slice(
                                pagination.pageSize * (pagination.current - 1),
                                pagination.pageSize * pagination.current,
                            ),
                        ]"
                        rowKey="id"
                        size="small"
                        :pagination="pagination"
                        :scroll="{ x: 100 }"
                        :rowSelection="{
                            selectedRowKeys: selectedStaff.map((i) => i.id),
                            onChange: (keys, rows) => selStaff(rows),
                        }"
                    />
                </div>
            </div>
            <div class="cell">
                <div class="title">其他汇总账单 <DownloadOutlined class="icon" @click="exportTotalBill" /></div>
                <div class="main">
                    <BasicTable
                        :tableDataList="[
                            {
                                id: formData.hrBillTotal?.id ?? 1,
                                ...totalData,
                            },
                        ]"
                        :columns="totalColumns"
                        size="small"
                        :sorter="false"
                        :rowSelectionShow="false"
                    />
                </div>
            </div>
        </Spin>
        <template #footer>
            <template v-if="canEdit">
                <Button @click="modalClose">取消</Button>
                <Button @click="modalConfirm" :loading="confirmLoading" :disabled="modalConfirmDisabled" type="primary"
                    >保存</Button
                >
            </template>
            <span v-else></span>
        </template>
    </BasicEditModalSlot>
    <!-- 新增入账人员 -->
    <BasicEditModalSlot
        :title="formData.otherBillFlag === 1 ? '新增入账' : '新增入账人员'"
        :width="formData.otherBillFlag === 1 ? '600px' : '1200px'"
        :visible="showInsertStaff"
        @cancel="insertClose"
        okText="批量入账"
        @ok="insertConfirm"
        centered
    >
        <BasicTable
            ref="insertTableRef"
            :tableDataList="unInsertList"
            :columns="formData.otherBillFlag === 1 ? unInsertClientCols : unInsertCols"
            :sorter="false"
            @selectedRowsArr="selStaff"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import {
    SelectOutlined,
    DownloadOutlined,
    UsergroupAddOutlined,
    UsergroupDeleteOutlined,
    TransactionOutlined,
} from '@ant-design/icons-vue'
import { message, Spin } from 'ant-design-vue'
import request from '/@/utils/request'
import { minus, plus, round, divide } from 'number-precision'
import downFile, { exportTable } from '/@/utils/downFile'
import { formatDynamicCols } from '../util'
import { previewFile, ArrToTree } from '/@/utils/index'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'

export default defineComponent({
    name: 'OtherBill',
    components: {
        Spin,
        SelectOutlined,
        DownloadOutlined,
        UsergroupAddOutlined,
        UsergroupDeleteOutlined,
        TransactionOutlined,
    },
    props: {
        visible: Boolean,
        billId: String,
        createResponse: Object,
    },
    emits: ['update:visible', 'confirm', 'changeOrigin'],
    setup(props, { emit }) {
        const { visible, billId, createResponse } = toRefs(props)

        watch(visible, () => {
            visible.value && billId.value && getDetail()
        })

        const formData = ref<Recordable>({
            hrBillTotal: {
                total: 0,
            },
        })
        const modalConfirmDisabled = ref(false)
        const stable = ref()
        const isUpdated = ref(false)
        const tableData = ref<Recordable[]>([])
        const resTableData = ref<Recordable[]>([])
        const columns = ref<Recordable[]>([])
        const totalColumns = ref<Recordable[]>([])
        const unInsertList = ref<Recordable[]>([])
        const treeData = ref<TreeDataItem[]>([])

        const colsList = ref<any[]>([])
        const totalData = computed(() => {
            if (!resTableData.value.length) return {}
            let colsListData: inObject = {}
            if (colsList.value.length) {
                colsList.value?.forEach((item) => {
                    item?.children?.forEach((el) => {
                        colsListData[el.value] = resTableData.value
                            .map((i) => {
                                return i.hrBillDetailItemsList && i.hrBillDetailItemsList.length
                                    ? i.hrBillDetailItemsList?.find((ele) => ele.expenseName == el.value)?.amount ?? 0
                                    : 0
                            })
                            .reduce((pre, cur) => plus(pre, cur ?? 0), 0)
                    })
                })
            }
            return {
                staffNum: resTableData.value.length,
                total: resTableData.value.map((i) => i.total ?? 0).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                chargeTotal: resTableData.value
                    .map((i) => {
                        return i.hrBillDetailItemsList && i.hrBillDetailItemsList.length
                            ? i.hrBillDetailItemsList
                                  ?.filter((ele) => ele.expenseType == '28')
                                  .map((el) => el.amount)
                                  .reduce((pre, cur) => plus(pre, cur ?? 0), 0) ?? 0
                            : 0
                    })
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                refundTotal: resTableData.value
                    .map((i) => {
                        return i.hrBillDetailItemsList && i.hrBillDetailItemsList.length
                            ? i.hrBillDetailItemsList
                                  ?.filter((ele) => ele.expenseType == '29')
                                  .map((el) => el.amount)
                                  .reduce((pre, cur) => plus(pre, cur ?? 0), 0) ?? 0
                            : 0
                    })
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ...colsListData,
            }
        })

        const pagination = ref({
            current: 1,
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条`,
            total: 0,
            onChange: (current) => {
                pagination.value.current = current
            },
        })

        const canEdit = computed(() => formData.value.billState !== 1)
        const loading = ref(false)
        const showTable = ref(false)
        const getDetail = async () => {
            let res
            try {
                loading.value = true
                if (createResponse.value?.billIdList) {
                    res = await request.post(`/api/hr-bills/search-batch`, createResponse.value?.billIdList)
                    treeData.value = ArrToTree(res.hrClientDTOList, { id: 'id', pid: 'parentId' })
                    // res = createResponse.value
                    // treeData.value = ArrToTree(createResponse.value.hrClientDTOList, { id: 'id', pid: 'parentId' })
                } else res = await request.get(`/api/hr-bills/${billId.value}`)
            } catch (err) {
                modalClose()
            } finally {
                setTimeout(() => {
                    loading.value = false
                }, 300)
            }
            formData.value = {
                ...res,
                hrBillTotal: res?.hrBillTotal || {
                    total: 0,
                },
            }
            tableData.value = res.billDetailList
            resTableData.value = res.billDetailList
            pagination.value.total = tableData.value.length
            columns.value = [
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                    slots: { customRender: 'name' },
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '费用合计',
                    dataIndex: 'total',
                    width: 100,
                    customRender: ({ record, text }) => {
                        const add = record.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType == '28')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)

                        const sub = record.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType == '29')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, Math.abs(cur) ?? 0), 0)
                        record.total = round(minus(add, sub), 2)
                        return text
                    },
                    align: 'center',
                },
            ]

            // 获取动态表头列
            const cols = await request.post(
                `/api/hr-bill-dynamic-fieldses/getTableDynamicHeader`,
                createResponse.value?.billIdList,
            )
            colsList.value = cols
            const dynamicCols = formatDynamicCols(cols, detailChange, formData.value.billState == 1, pagination)
            columns.value = [...columns.value.slice(0, 2), ...dynamicCols, ...columns.value.slice(2)]
            if (res.otherBillFlag === 1) {
                columns.value = [
                    {
                        title: '客户名',
                        dataIndex: 'name',
                        width: 100,
                        fixed: 'left',
                        align: 'center',
                        slots: { customRender: 'name' },
                    },
                    ,
                    ...dynamicCols,
                    ...columns.value.slice(-1),
                ]
            }
            showTable.value = true

            setTimeout(async () => {
                stable.value?.scrollTo({ top: 1, left: 1 })
            }, 500)

            totalColumns.value = [
                {
                    title: '人数',
                    dataIndex: 'staffNum',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '总计',
                    dataIndex: 'total',
                    width: 120,
                    align: 'center',
                    customRender: ({ text }) => {
                        return text
                    },
                },
            ]
            totalColumns.value = [...totalColumns.value.slice(0, 1), ...getTotalDynamicCols(cols), ...totalColumns.value.slice(1)]
            // 获取未入账员工
            let list
            if (createResponse.value?.billIdList)
                list = await request.post(`/api/hr-bill-details/list-batch`, createResponse.value?.billIdList)
            else list = await request.get(`/api/hr-bill-details/list?billId=${billId.value}`)
            unInsertList.value = list
        }

        const search = ref({
            name: '',
            certificateNum: '',
        })

        const filterTableData = () => {
            let filterOdds
            if (!search.value.name && !search.value.certificateNum) {
                tableData.value = resTableData.value
                pagination.value.current = 1
                pagination.value.total = tableData.value.length
                return
            } else if (!search.value.name && search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.certificateNum.includes(search.value.certificateNum)
                }
            } else if (search.value.name && !search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name)
                }
            } else {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name) && el.certificateNum.includes(search.value.certificateNum)
                }
            }
            tableData.value = resTableData.value.filter((el) => {
                return filterOdds(el)
            })
            pagination.value.current = 1
            pagination.value.total = tableData.value.length
        }

        const getTotalDynamicCols = (cols) => {
            return cols.map((el) => {
                return {
                    title: el.value,
                    dataIndex: el.key,
                    children: el.children && el.children.length ? getTotalDynamicCols(el.children) : undefined,
                    width: el.children && el.children.length ? el.children.length * 110 : 120,
                    align: 'center',
                }
            })
        }

        const resetData = () => {
            formData.value = {
                hrBillTotal: {
                    total: 0,
                },
            }
            search.value = {
                name: '',
                certificateNum: '',
            }
            resTableData.value = []
            tableData.value = []
            pagination.value.total = 0
            pagination.value.current = 1
            isUpdated.value = false
            selectedStaff.value = []
            showTable.value = false
            totalColumns.value = []
            columns.value = []
        }
        const modalClose = () => {
            resetData()
            emit('update:visible', false)
        }

        const confirmLoading = ref(false)
        const modalConfirm = async () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()
            try {
                confirmLoading.value = true

                const originList = formData.value.billDetailList
                const originIds = originList.map((i) => i.id)
                const currentIds = tableData.value.map((i) => i.id)

                const deleteList = originList
                    .filter((i) => beishanchude.value.includes(i.id))
                    .map((i) => ({
                        ...i,
                        updateState: 2,
                    }))
                beishanchude.value = []
                tableData.value.forEach((i) => {
                    if (!originIds.includes(i.id)) {
                        i.updateState = 1
                    } else {
                        i.updateState = 3
                    }
                })

                await request.put(`/api/hr-bills`, {
                    id: formData.value.id,
                    title: formData.value.title,
                    billType: 2,
                    billDetailList: [...tableData.value, ...deleteList],
                    billIdList:
                        createResponse.value && createResponse.value?.billIdList?.length
                            ? createResponse.value.billIdList
                            : undefined,
                    clientIdList:
                        createResponse.value && createResponse.value?.billIdList?.length
                            ? createResponse.value.clientIdList
                            : undefined,
                    hrBillTotal: {
                        id: formData.value.hrBillTotal.id,
                        billId: formData.value.hrBillTotal?.billId,
                        ...totalData.value,
                        isModifyServiceFee: formData.value.hrBillTotal?.isModifyServiceFee || false,
                    },
                })
                resetData()
                emit('confirm')
            } finally {
                confirmLoading.value = false
            }
        }
        const uploadNewOrigin = () => {
            modalClose()
            emit('changeOrigin')
        }

        const detailChange = (val, idx, key, isDynamic = false, type = 'number') => {
            if (formData.value.billState === 1) {
                message.warn('锁定状态不可修改！')
                return
            }
            if (isDynamic) {
                const index = tableData.value[idx].hrBillDetailItemsList?.findIndex((i) => i.expenseName == key)
                tableData.value[idx].hrBillDetailItemsList[index].amount = round(Number(val), 2)
            } else {
                tableData.value[idx][key] = type == 'number' ? round(Number(val), 2) : val?.target?.value
            }

            tableData.value[idx].updateState = 3 // 更新状态为 更新
            tableData.value = [...tableData.value]
            pagination.value.total = tableData.value.length
            isUpdated.value = true
        }

        const exportOrigin = () => {
            downFile('get', formData.value.originBillUrl, formData.value.originBillName)
        }
        const exportBankInsert = () => {
            if (!selectedStaff.value.length) {
                message.warning('请选择账单明细!')
                return
            }
            downFile('post', `/api/hr-bill-details/export-bank-statement`, `${formData.value.title}银行报账单.xlsx`, {
                billId: billId.value,
                billDetailIds: selectedStaff.value.map((i) => i.id),
            })
        }
        const exportBill = async () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            if (!selectedStaff.value.length) {
                message.warning('请选择账单明细!')
                return
            }
            downFile('post', `/api/hr-bills-detail/download-bill-detail`, `${formData.value.title}其他账单明细`, {
                billId: billId.value,
                billIdList:
                    createResponse.value && createResponse.value?.billIdList ? createResponse.value.billIdList : undefined,
                billDetailIds: selectedStaff.value.map((i) => i.id),
            })
        }
        const exportTotalBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            exportTable(
                totalColumns.value,
                [
                    {
                        id: formData.value.hrBillTotal.id ?? 0,
                        ...totalData.value,
                    },
                ],
                `${formData.value.title}其他账单汇总`,
                { rowIndex: 3, exceptCols: ['A'], widthCols: [{ wch: 10 }, ...Array(3).fill({ wch: 25 })] },
            )
        }

        const insertTableRef = ref()
        const tableRef = ref()
        const showInsertStaff = ref(false)
        const selectedStaff = ref<Recordable[]>([])
        const beishanchude = ref<any>([])
        const insertStaff = () => {
            showInsertStaff.value = true
        }
        const calcCurrentPage = (total) => {
            return Math.ceil(divide(total, 10))
        }
        const removeStaff = () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()

            if (!selectedStaff.value.length) {
                message.warning('请选择要删除入账的人员！')
                return
            }
            selectedStaff.value.forEach((i) => {
                i.selectedStaff = 2 // 更新状态为删除
            })
            unInsertList.value = [...unInsertList.value, ...selectedStaff.value]
            const ids = selectedStaff.value.map((i) => i.id)
            const changedList = tableData.value.filter((i) => !ids.includes(i.id))
            beishanchude.value.push(...selectedStaff.value.map((i) => i.id))
            resTableData.value = changedList
            filterTableData()
            pagination.value.current = calcCurrentPage(pagination.value.total)
            isUpdated.value = true
            selectedStaff.value = []
            tableRef.value?.checkboxReset()

            formData.value.hrBillTotal.isModifyServiceFee = false
        }
        const selStaff = (list) => {
            selectedStaff.value = list
        }
        const selAll = (isSel) => {
            selectedStaff.value = isSel ? [...tableData.value] : []
        }
        const insertConfirm = async () => {
            if (!selectedStaff.value.length) {
                message.warning('请选择要入账的人员！')
                return
            }
            const ids = selectedStaff.value.map((i) => i.id)
            // tableData 添加
            const res = await request.post(`/api/hr-bill-details/batchAdd`, {
                ids: ids,
                billIds: createResponse.value?.billIdList,
            })
            resTableData.value = [...resTableData.value, ...res]
            filterTableData()
            isUpdated.value = true
            // unInsertList 删除
            unInsertList.value = unInsertList.value.filter((i) => !ids.includes(i.id))
            insertClose()

            formData.value.hrBillTotal.isModifyServiceFee = false
        }
        const insertClose = () => {
            selectedStaff.value = []
            showInsertStaff.value = false
            insertTableRef.value.checkboxReset()
        }
        const exportUninsert = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单明细，请保存后重新打开再导出未入账员工明细！',
                    duration: 5,
                })
                return
            }
            if (createResponse.value?.billIdList)
                downFile('post', `/api/hr-bill-details/export-batch`, '', createResponse.value?.billIdList)
            else downFile('get', `/api/hr-bill-details/export?billId=${billId.value}`, `${formData.value.title}未入账明细.xlsx`)
        }

        return {
            search,
            filterTableData,
            exportUninsert,
            treeData,
            modalConfirmDisabled,
            pagination,
            canEdit,
            selAll,
            selectedStaff,
            loading,
            confirmLoading,
            showTable,
            insertTableRef,
            unInsertList,
            insertClose,
            showInsertStaff,
            totalColumns,
            tableRef,
            totalData,
            detailChange,
            exportBill,
            exportTotalBill,
            selStaff,
            insertConfirm,
            insertStaff,
            removeStaff,
            tableData,
            columns,
            exportBankInsert,
            exportOrigin,
            uploadNewOrigin,
            formData,
            previewFile,
            modalConfirm,
            modalClose,
            unInsertCols: [
                {
                    title: '员工编号',
                    dataIndex: 'systemNum',
                    width: 160,
                    fixed: 'left',
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    fixed: 'left',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 120,
                },
                {
                    title: '员工类型',
                    dataIndex: 'personnelTypeStr',
                    width: 120,
                },
                {
                    title: '员工状态',
                    dataIndex: 'staffStatusStr',
                    width: 120,
                },
                {
                    title: '参保状态',
                    dataIndex: 'izInsuredStr',
                    width: 120,
                },
                {
                    title: '未入账原因',
                    dataIndex: 'reason',
                    width: 200,
                    fixed: 'right',
                },
            ],
            unInsertClientCols: [
                {
                    title: '客户名',
                    dataIndex: 'name',
                    width: 120,
                },
                {
                    title: '未入账原因',
                    dataIndex: 'reason',
                    width: 200,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.search-wrapper {
    display: flex;
    margin: 10px 0;
    & > input {
        width: 230px;
        margin-right: 20px;
    }
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon {
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }
    }
    .main {
        padding: 10px 0;
    }
}
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.row {
    margin: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    .col {
        margin-right: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .label {
            width: 80px;
            text-align: right;
            &:after {
                content: '：';
            }
        }
    }
}
.clclientName {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
