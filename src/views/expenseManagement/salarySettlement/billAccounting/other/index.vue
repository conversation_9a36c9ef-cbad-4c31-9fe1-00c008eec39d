<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'otherBill_createAndRemove'" type="primary" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'otherBill_download'" @click="downloadSome">{{ exportText }}</Button>
        <Button v-auth="'otherBill_createAndRemove'" type="primary" danger @click="deleteSome">批量删除</Button>
    </div>
    <BasicTable
        ref="talbeRef"
        api="/api/hr-bills/page"
        exportUrl="/api/hr-bills/download-bill-batch"
        :params="{ ...params, billTypeList: params?.billTypeList.length ? params.billTypeList : [2, 3] }"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        :getChildrenMethod="getChildrenMethod"
    >
        <template #operation="{ record }">
            <Button v-if="record.strBillIds" size="small" type="primary" @click="editRow(record, false)">{{
                record.billState == 0 ? '编辑' : '查看'
            }}</Button>
            &nbsp;
            <!-- 已通过审核才能复制 -->
            <Button
                v-if="record.strBillIds"
                v-auth="'otherBill_createAndRemove'"
                :disabled="!(record.reviewState == 1)"
                size="small"
                type="primary"
                @click="copyRow(record)"
            >
                复制
            </Button>
        </template>
    </BasicTable>

    <!-- 新增 -->
    <CreateModal :visible="showCreate" @cancel="createClose" @confirm="createConfirm" :billType="2" />
    <!-- 复制提示 -->
    <CopyModal :visible="showTipTable" :tableData="copyErrList" @cancel="showTipTable = false" />
    <!-- 其它账单 -->
    <OtherBill
        v-model:visible="showOtherBill"
        :billId="currentItem.id"
        :createResponse="currentItem"
        @confirm="modalConfirm"
        @changeOrigin="showCreateSalary = true"
    />
    <!-- 中石化费用统计账单 -->
    <SinopecBill
        v-model:visible="showSinopecBill"
        :billId="currentItem.id"
        :createResponse="currentItem"
        @confirm="modalConfirm"
        @changeOrigin="showCreateSalary = true"
    />
    <!-- 创建薪酬账单 -->
    <CreateSalary
        v-model:visible="showCreateSalary"
        :staffIds="staffIds"
        :billId="currentItem.id"
        :billIds="currentItem?.billIdList"
        :clientIds="currentItem?.clientIdList"
        :costDate="currentItem.paymentDate"
        :clientId="currentItem.clientId"
        :billType="currentItem.billType"
        @confirm="createSalConfirm"
    />
    <!-- 对账 -->
    <CompareModal v-model:visible="showCompare" :exportTypeList="currentExportTypeList" :billId="currentItem.id" />
</template>

<script lang="ts">
import { computed, defineComponent, h, ref } from 'vue'
import CreateModal from '../CreateModal.vue'
import CopyModal from '../CopyModal.vue'
import OtherBill from './OtherBill.vue'
import SinopecBill from './SinopecBill.vue'
import CreateSalary from '../salary/CreateSalay.vue'

// import CompareModal from '../CompareModal.vue'
import { billStateList, billTypeList } from '/@/utils/dictionaries'
import { getSpecialList } from '/@/utils/api'
import { message, Modal, MonthPicker } from 'ant-design-vue'
import request from '/@/utils/request'
import moment from 'moment'
import { exportTypeList } from '/@/utils/dictionaries'
import { getDynamicText, pushBillIds } from '/@/utils'

export default defineComponent({
    name: 'OtherBillAccounting',
    components: { CreateModal, CopyModal, OtherBill, CreateSalary, SinopecBill },
    setup() {
        const params = ref({
            name: undefined,
            clientIds: [],
            paymentDate: undefined,
            title: undefined,
            specialIds: [],
            billState: undefined,
            billTypeList: [],
        })
        const talbeRef = ref()
        const searchData = () => {
            talbeRef.value.refresh(1)
        }
        const showOtherBill = ref(false)
        const showSinopecBill = ref(false)

        const showCreateSalary = ref(false)
        const showCreate = ref(false)
        const currentItem = ref<Recordable>({})
        const createRow = () => {
            currentItem.value = {}
            showCreate.value = true
        }
        const createClose = () => {
            showCreate.value = false
        }

        const staffIds = ref<any>()
        const createConfirm = (record) => {
            staffIds.value = record.staffIds
            searchData()
            showCreate.value = false
            editRow(record, true)
        }
        const editRow = (record, isCreated) => {
            let billIdList: string[] = []
            if (record.strBillIds) {
                billIdList = record.strBillIds.split(',')
            } else {
                // 如果是已锁定，那就是查看所有
                if (record.billState) {
                    pushBillIds(billIdList, record, false)
                } else {
                    pushBillIds(billIdList, record, true)
                }
            }

            console.log('rerrrrr', record)

            if (isCreated) {
                // if (!record.dynamicFieldId || !record.bdCount || isCreated) {
                currentItem.value = { ...record }
                // 没有上传原单或没有创建账单明细
                showCreateSalary.value = true
            } else {
                currentItem.value = { ...record, billIdList: billIdList }
                if (!record.children && (!record.dynamicFieldId || !record.bdCount)) {
                    showCreateSalary.value = true
                } else {
                    if (currentItem.value.billType == 2) {
                        // 其他账单
                        showOtherBill.value = true
                    } else {
                        // 中石化账单
                        showSinopecBill.value = true
                    }
                }
            }
        }

        const currentExportTypeList = ref<LabelValueOptions>([])
        const showCompare = ref(false)
        const compareRow = (record) => {
            currentItem.value = { ...record }
            //    其他账单
            currentExportTypeList.value = exportTypeList.filter((i) => i.value == 3)
            showCompare.value = true
        }

        const copyErrList = ref<Recordable[]>([])
        const showTipTable = ref(false)
        const copyRow = (record) => {
            currentItem.value = { ...record }
            let paymentDate: any = undefined
            let billIdList: string[] = record.strBillIds.split(',')
            // pushBillIds(billIdList, record, false)
            Modal.confirm({
                title: '选择缴费年月',
                content: h(MonthPicker, {
                    onChange: (val) => {
                        paymentDate = val
                    },
                    format: 'YYYY-MM',
                    valueFormat: 'YYYY-MM',
                    placeholder: '缴费年月',
                    disabledDate: disabledDate,
                }),
                onOk: () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            const res = await request.post(`/api/hr-bills/copy-batch`, {
                                billIdList: billIdList,
                                paymentDate,
                            })
                            copyErrList.value =
                                Array.isArray(res?.differentList) && res?.differentList?.length ? res?.differentList : []
                            if (copyErrList.value.length) {
                                showTipTable.value = true
                            } else {
                                message.success('复制成功！')
                                searchData()
                            }
                            resolve(res)
                        } catch (error) {
                            reject(new Error('err' + error))
                        }
                    })
                },
            })
        }

        const selArr = ref<Recordable[]>([])
        const selectedRowsArr = (arr) => {
            selArr.value = arr
        }
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, selArr.value)
        })
        const downloadSome = () => {
            talbeRef.value.exportRow('ids', exportText.value, params.value)
        }
        const deleteSome = () => {
            if (!selArr.value.length) {
                message.warning('请选择要删除的账单！')
                return
            }
            const ids = selArr.value.map((i) => i.id)
            Modal.confirm({
                title: '确认',
                content: '确认删除所选账单?',
                onOk: async () => {
                    await request.post(`/api/hr-bills/deletes`, ids)
                    searchData()
                },
            })
        }

        const modalConfirm = () => {
            showOtherBill.value = false
            showSinopecBill.value = false
            searchData()
        }

        const disabledDate = (currentDate) => {
            // 最多可预做3个月的账单
            // return moment(currentDate) <= moment().subtract(1, 'month') || moment(currentDate) > moment().add(2, 'month')
            return moment(currentDate) > moment().add(2, 'month')
        }

        const createSalConfirm = (res) => {
            searchData()
            currentItem.value = { ...res }
            if (currentItem.value.billType == 2) {
                // 其他账单
                showOtherBill.value = true
            } else {
                // 中石化账单
                showSinopecBill.value = true
            }
        }

        const getChildrenMethod = (record) => {
            console.log('getChildrenMethod', record)

            const clientId = record.optClientId || record.clientId
            return new Promise(async (resolve) => {
                const res = await request.get(`/api/hr-bills/list/${record.billNo}/${clientId}`)
                resolve(
                    res.map((i) => ({
                        ...i,
                    })),
                )
            })
        }
        return {
            staffIds,
            showSinopecBill,
            showOtherBill,
            exportText,
            currentExportTypeList,
            createSalConfirm,
            copyErrList,
            talbeRef,
            modalConfirm,
            searchData,
            showCreateSalary,
            editRow,
            copyRow,
            showCompare,
            compareRow,
            showTipTable,
            showCreate,
            createConfirm,
            createClose,
            selectedRowsArr,
            downloadSome,
            deleteSome,
            createRow,
            currentItem,
            params,
            getChildrenMethod,
            columns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'center',
                    width: 150,
                    ellipsis: true,
                },
                {
                    title: '费用年月',
                    dataIndex: 'paymentDate',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '账单类型',
                    dataIndex: 'billTypeStr',
                    align: 'center',
                    width: 150,
                },
                {
                    title: '标题',
                    dataIndex: 'title',
                    align: 'center',
                    width: 180,
                    ellipsis: true,
                },
                {
                    title: '专管员',
                    dataIndex: 'specialName',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '状态',
                    dataIndex: 'billStateStr',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '更新日期',
                    dataIndex: 'lastModifiedDate',
                    align: 'center',
                    width: 170,
                    // customRender: ({ record }) => {
                    //     return record.lastModifiedDate || record.createdDate
                    // },
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 190,
                    fixed: 'right',
                    slots: { customRender: 'operation' },
                },
            ],
            searchOptions: [
                {
                    label: '客户名称',
                    key: 'clientIds',
                    type: 'clientSelectTree',
                    multiple: true,
                    maxTag: 0,
                    checkStrictly: false,
                },
                {
                    label: '费用年月',
                    key: 'paymentDate',
                    type: 'month',
                },
                {
                    label: '账单类型',
                    key: 'billTypeList',
                    type: 'select',
                    multiple: true,
                    options: billTypeList.filter((el) => el.value >= 2),
                },
                {
                    label: '标题',
                    key: 'title',
                },
                {
                    label: '专管员',
                    key: 'specialIds',
                    type: 'select',
                    options: [],
                    getMethod: getSpecialList,
                    multiple: true,
                },
                {
                    label: '状态',
                    key: 'billState',
                    type: 'select',
                    options: billStateList,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
</style>
