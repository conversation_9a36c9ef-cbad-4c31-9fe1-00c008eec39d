import { exportTable } from '/@/utils/downFile'
import { message as Message } from 'ant-design-vue'
import moment from 'moment'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import { plus } from 'number-precision'

export const multiSheetToExport = async (columns, data, title, singleFlag, isTotal = false) => {
    if (singleFlag)
        Message.loading({
            content: '正在导出，请稍后...',
            duration: 0,
        })

    const widthCols = [
        { wch: 10 },
        { wch: 15 },
        { wch: 25 },
        { wch: 10 },
        { wch: 20 },
        ...Array(9).fill({ wch: 15 }),
        { wch: 20 },
        ...Array(3).fill({ wch: 15 }),
        { wch: 20 },
        ...Array(5).fill({ wch: 25 }),
    ]
    const exportColumns = columns.map((el) => {
        if (el.dataIndex == 'otherFee')
            return {
                ...el,
                title: '其他费用',
                children: el.children.map((ele) => {
                    if (typeof ele.title !== 'string') return { ...ele, title: ele.dataIndex }
                    return ele
                }),
            }
        return el
    })
    const [header_1, header_2] = [
        isTotal
            ? { projectNum: '青岛市黄岛区人力资源有限公司费用统计表', staffNum: '' }
            : {
                  currentYear: '青岛市黄岛区人力资源有限公司费用统计表',
                  projectCode: '',
                  projectName: '',
                  peopleNum: '',
                  totalOccurrence: '',
              },
        isTotal
            ? { projectNum: '', staffNum: '' }
            : {
                  currentYear: '',
                  projectCode: '',
                  projectName: '',
                  peopleNum: '',
                  totalOccurrence: '',
              },
    ]
    const defaultTopData = isTotal
        ? { projectNum: '甲', staffNum: '乙', happenTotalFee: '18=1+4+5+9+11+12+13' }
        : {
              currentYear: '甲',
              projectCode: '乙',
              projectName: '丙',
              peopleNum: '丁',
              totalOccurrence: '',
          }
    data[0].hrBillDetailItemsList.forEach((el) => {
        console.log(el)
        defaultTopData[el.expenseName] = el.calculationNum || ''
        header_1[el.expenseName] = ''
        if (el.expenseName == '报销费用') {
            header_2[el.expenseName] = '制表时间：'
        } else if (el.expenseName == '管理费') {
            header_2[el.expenseName] = moment().format('YYYY年MM月DD日')
        } else header_2[el.expenseName] = ''
    })

    try {
        if (singleFlag) {
            const exportArr: any = []

            for (const el of data) {
                try {
                    const sheetObj = await exportTable(
                        exportColumns,
                        [header_1, header_2, defaultTopData, el],
                        el?.projectName || '',
                        {
                            isDown: false,
                            rowIndex: 8,
                            widthCols,
                            exceptCols: ['A', 'B', 'C', 'D'],
                            headerText: '青岛市黄岛区人力资源有限公司费用统计表',
                        },
                    )
                    exportArr.push({ name: `${el?.projectName || ''}_${moment().format('YYYYMMDDHHmmss')}.xlsx`, blob: sheetObj })
                } catch (error) {
                    console.log(error)
                    Message.destroy()
                }
            }

            if (exportArr?.length < 1) {
                Message.destroy()
                Message.warning('暂无下载信息!')

                return
            }
            const zip = new JSZip()
            let i = 0
            while (exportArr[i]) {
                try {
                    zip.file(exportArr[i].name, exportArr[i].blob, { base64: true })
                } catch (error) {
                    Message.destroy()
                }
                i++
            }
            try {
                const zipfile = await zip.generateAsync({ type: 'blob' })
                saveAs(zipfile, `${title}_${moment().format('YYYYMMDDHHmmss')}.zip`)
            } catch (error) {
                console.log(error)
                Message.destroy()
            }
        } else {
            try {
                const blob = await exportTable(
                    exportColumns,
                    [header_1, header_2, { ...defaultTopData, totalFee: defaultTopData['费用合计'] }, ...data],
                    title || '',
                    {
                        isDown: true,
                        rowIndex: 8,
                        widthCols,
                        exceptCols: isTotal ? ['A', 'B'] : ['A', 'B', 'C', 'D'],
                        headerText: '青岛市黄岛区人力资源有限公司费用统计表',
                    },
                )
                console.log(exportColumns)
                return blob
            } catch (error) {
                console.log(error)
                Message.destroy()
            }
        }
    } catch (error) {
        Message.destroy()
        console.log(error)
    } finally {
        Message.destroy()
    }
}

export const settlementSheetToExport = async (cols, dynamicCols, data, title, headerText) => {
    console.log(data)
    const [keys, total, inlandTotal]: [any, any, any] = [[], [], []]
    dynamicCols.forEach((el) => {
        if (el.hasOwnProperty('children')) {
            el.children.forEach((ele) => {
                keys.push(ele.dataIndex)
            })
        }
    })
    cols.slice(-21).forEach((el) => {
        if (!el.hasOwnProperty('children')) keys.push(el.dataIndex)
        else {
            el.children.forEach((ele) => {
                if (!ele.hasOwnProperty('children')) keys.push(ele.dataIndex)
                else {
                    ele.children.forEach((element) => {
                        keys.push(element.dataIndex)
                    })
                }
            })
        }
    })
    keys.forEach((el) => {
        if (el == 'name' || el == 'certificateNum') {
        } else if (el == 'unitAccumulationFund' || el == 'personalAccumulationFund') {
            total[el] = data.reduce((pre, cur) => {
                return pre + cur[el] || 0
            }, 0)
        } else {
            total[el] = data.reduce((pre, cur) => {
                return plus(pre, cur[el] || 0)
            }, 0)
            total[el].toFixed(2)
        }
    })
    console.log(keys)
    keys.forEach((el) => {
        inlandTotal[el] = data
            .filter((item) => item.projectType == 1)
            .reduce((pre, cur) => {
                return plus(pre, cur[el] || 0)
            }, 0)
        inlandTotal[el].toFixed(2)
        if (el == 'currentYear' || el == 'projectCode' || el == 'projectName') {
            total[el] = ''
            inlandTotal[el] = ''
        }
    })
    try {
        let r = 0
        let billTypes
        data.forEach((el) => {
            billTypes = el.billType
            el.wageIncrease.forEach((i) => {
                r += i.amount
            })
        })
        if (billTypes == 3) {
            const blob = await exportTable(
                cols,
                [{ index: headerText }, ...data, { index: '结算费用合计', ...inlandTotal }, { index: '所有费用合计', ...total }],
                title || '',
                {
                    isDown: false,
                    isSettlement: true,
                    rowIndex: 4,
                    exceptCols: ['A', 'B', 'C', 'D', 'E'],
                    headerText: headerText,
                    widthCols: [
                        { wch: 10 },
                        { wch: 25 },
                        { wch: 20 },
                        { wch: 20 },
                        { wch: 25 },
                        ...Array(27).fill({ wch: 25 }),
                        ...Array(3).fill({ wch: 35 }),
                    ],
                },
            )
            return blob
        } else {
            const blob = await exportTable(cols, [{ index: headerText }, ...data, { index: '合计', ...total }], title || '', {
                isDown: false,
                isSettlement: true,
                rowIndex: 4,
                exceptCols: ['A', 'B', 'C', 'D', 'E'],
                headerText: headerText,
                widthCols: [
                    { wch: 10 },
                    { wch: 25 },
                    { wch: 20 },
                    { wch: 20 },
                    { wch: 25 },
                    ...Array(27).fill({ wch: 25 }),
                    ...Array(3).fill({ wch: 35 }),
                ],
            })
            return blob
        }
    } catch (error) {
        console.log(error)
    }
}
