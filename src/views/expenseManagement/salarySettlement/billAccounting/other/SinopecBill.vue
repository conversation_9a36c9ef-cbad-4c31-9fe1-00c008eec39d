<template>
    <BasicEditModalSlot :visible="visible" title="中石化费用统计账单" @cancel="modalClose" width="1400px">
        <Spin :spinning="loading">
            <div class="cell">
                <div class="title">费用统计账单信息</div>
                <div class="main">
                    <div class="row">
                        <div class="col">
                            <div class="label">客户名称</div>
                            <div class="val">{{ formData.clientName }}</div>
                        </div>
                        <div class="col" v-if="formData.parentClientName">
                            <div class="label">所属公司</div>
                            <div class="val">
                                {{ formData.parentClientName }}
                            </div>
                        </div>
                        <div class="col">
                            <div class="label">费用年月</div>
                            <div class="val">{{ formData.paymentDate }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col" style="width: 100%">
                            <div class="label">标题</div>
                            <div class="val">
                                <span v-if="formData.billState === 1"> {{ formData.title }} </span>
                                <Input v-else v-model:value="formData.title" placeholder="标题" style="width: 300px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Row>
                <Col span="12">
                    <div class="cell">
                        <div class="title">
                            原始账单明细
                            <SelectOutlined v-if="canEdit" class="icon" @click="uploadNewOrigin" title="更换其他原始账单" />
                            <DownloadOutlined class="icon" @click="exportOrigin" title="下载原单" />
                        </div>
                        <div class="main">
                            <a @click="previewFile(formData.originBillUrl)"> {{ formData.originBillName }} </a>
                        </div>
                    </div>
                </Col>
            </Row>
            <div class="cell">
                <div class="title">
                    中石化费用统计账单明细
                    <UsergroupAddOutlined v-if="canEdit" class="icon" @click="insertProject" />
                    <UsergroupDeleteOutlined v-if="canEdit" class="icon" @click="removeProject" />
                    <DownloadOutlined class="icon" @click="exportBill(0, exportNameList[0])" />
                </div>
                <div class="main">
                    <div class="search-wrapper">
                        <Input
                            v-model:value="search.projectCode"
                            placeholder="项目代码"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                        <Input
                            v-model:value="search.projectName"
                            placeholder="项目名称"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                    </div>
                    <template v-if="tableData.length">
                        <Button
                            v-if="selectedProject.length == tableData.length"
                            size="small"
                            type="primary"
                            @click="selAll(false)"
                        >
                            取消全选
                        </Button>
                        <Button v-else size="small" type="primary" @click="selAll(true)"> 选择全部 </Button>
                    </template>
                    <Table
                        v-if="showTable"
                        :bordered="true"
                        :columns="columns"
                        :dataSource="[
                            ...tableData.slice(
                                pagination.pageSize * (pagination.current - 1),
                                pagination.pageSize * pagination.current,
                            ),
                        ]"
                        rowKey="id"
                        size="small"
                        :pagination="pagination"
                        :scroll="{ x: 100 }"
                        :rowSelection="{
                            selectedRowKeys: selectedProject.map((i) => i.id),
                            onChange: (keys, rows) => selProject(rows),
                        }"
                    >
                        <template #otherHeader>
                            <span>其它费用</span>
                            <Button size="small" class="other_header_btn" @click="showAddOtherModal"> 新增其他 </Button>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="cell">
                <div class="title">中石化费用统计账单汇总 <DownloadOutlined class="icon" @click="exportTotalBill" /></div>
                <div class="main">
                    <BasicTable
                        :tableDataList="[
                            {
                                id: formData.hrBillTotal?.id ?? 1,
                                ...totalData,
                                ...dynamicTotalData,
                            },
                        ]"
                        :columns="totalColumns"
                        size="small"
                        :sorter="false"
                        :rowSelectionShow="false"
                    />
                </div>
            </div>
            <div class="cell">
                <div class="title">中石化结算费用汇总 <DownloadOutlined class="icon" @click="exportInlandTotalBill" /></div>
                <div class="main">
                    <BasicTable
                        :tableDataList="[
                            {
                                id: formData.hrBillTotal?.id ?? 1,
                                ...foreignTotalData,
                                ...inlandDynamicTotalData,
                            },
                        ]"
                        :columns="totalColumns"
                        size="small"
                        :sorter="false"
                        :rowSelectionShow="false"
                    />
                </div>
            </div>
            <div class="cell">
                <div class="wrapper">
                    <div class="item">
                        <div class="title">
                            {{ exportNameList[1] }}
                            <DownloadOutlined class="icon" @click="exportBill(1, exportNameList[1])" />
                        </div>
                        <div class="appendixBox">
                            <Tooltip placement="top">
                                <template #title>
                                    <span>{{ exportNameList[1] }}</span>
                                </template>
                                <a class="enclosure" @click="exportBill(1, exportNameList[1])"> {{ exportNameList[1] }} </a>
                            </Tooltip>
                        </div>
                    </div>
                    <div class="item">
                        <div class="title">
                            {{ exportNameList[2] }}
                            <DownloadOutlined class="icon" @click="exportBill(2, exportNameList[2])" />
                        </div>
                        <div class="appendixBox">
                            <Tooltip placement="top">
                                <template #title>
                                    <span>{{ exportNameList[2] }}</span>
                                </template>
                                <a class="enclosure" @click="exportBill(2, exportNameList[2])"> {{ exportNameList[2] }} </a>
                            </Tooltip>
                        </div>
                    </div>
                    <div class="item">
                        <div class="title">
                            {{ exportNameList[3] }}
                            <DownloadOutlined class="icon" @click="exportBill(3, exportNameList[3])" />
                        </div>
                        <div class="appendixBox">
                            <Tooltip placement="top">
                                <template #title>
                                    <span>{{ exportNameList[3] }}</span>
                                </template>
                                <a class="enclosure" @click="exportBill(3, exportNameList[3])"> {{ exportNameList[3] }} </a>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </Spin>
        <template #footer>
            <template v-if="canEdit">
                <Button @click="modalClose">取消</Button>
                <Button @click="modalConfirm" :loading="confirmLoading" :disabled="modalConfirmDisabled" type="primary">
                    保存
                </Button>
            </template>
            <span v-else></span>
        </template>
    </BasicEditModalSlot>
    <!-- 新增其他费用列 -->
    <BasicEditModalSlot
        title="新增其他费用列"
        width="400px"
        :visible="showOtherModal"
        @cancel="otherModalClose"
        okText="确定"
        @ok="otherModalConfirm"
        centered
    >
        <Form ref="addFormRef" :model="addForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 24 }">
            <FormItem
                name="cellName"
                label="新增列名"
                :rules="{ required: true, message: '请输入新增的列名', trigger: ['change', 'blur'] }"
            >
                <Input v-model:value="addForm.cellName" placeholder="列名" style="width: 200px" />
            </FormItem>
            <FormItem label="类型">
                <Radio-Group v-model:value="addForm.toAdd">
                    <Radio :value="1">加入薪酬小计</Radio>
                    <Radio :value="2">不加入薪酬小计</Radio>
                </Radio-Group>
            </FormItem>
        </Form>
    </BasicEditModalSlot>
    <!-- 新增未入账项目 -->
    <BasicEditModalSlot
        title="新增未入账项目"
        width="800px"
        :visible="showInsertProject"
        @cancel="insertClose"
        okText="确认"
        @ok="insertConfirm"
        centered
    >
        <BasicTable
            ref="insertTableRef"
            :tableDataList="unInsertList"
            :columns="unInsertCols"
            :sorter="false"
            @selectedRowsArr="selProject"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed, h } from 'vue'
import {
    DownloadOutlined,
    MinusCircleOutlined,
    SelectOutlined,
    UsergroupAddOutlined,
    UsergroupDeleteOutlined,
} from '@ant-design/icons-vue'
import { InputNumber, message, Spin } from 'ant-design-vue'
import request from '/@/utils/request'
import { divide, plus, round, times } from 'number-precision'
import downFile, { exportTable } from '/@/utils/downFile'
import { previewFile, ArrToTree } from '/@/utils/index'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { multiSheetToExport } from './export'

export default defineComponent({
    name: 'SinopecBill',
    components: {
        Spin,
        DownloadOutlined,
        SelectOutlined,
        UsergroupAddOutlined,
        UsergroupDeleteOutlined,
    },
    props: {
        visible: Boolean,
        billId: String,
        createResponse: Object,
    },
    emits: ['update:visible', 'confirm', 'changeOrigin'],
    setup(props, { emit }) {
        const { visible, billId, createResponse } = toRefs(props)

        watch(visible, () => {
            visible.value && billId.value && getDetail()
        })

        const formData = ref<Recordable>({
            hrBillTotal: {
                total: 0,
            },
        })

        const addFormRef = ref()

        const addForm = ref({ cellName: undefined, toAdd: 2 })

        const dynamicColumns = ref<any>([])

        const showOtherModal = ref(false)

        // 各项目费用统计表
        let detailsAppendix = ref<any>({})
        // 各项目技术服务费结算书
        let summaryAppendix = ref<any>({})
        // 各项目技术服务费收据
        let receiptAppendix = ref<any>({})

        const modalConfirmDisabled = ref(false)
        const isUpdated = ref(false)
        const tableData = ref<Recordable[]>([])
        const resTableData = ref<Recordable[]>([])
        const columns = ref<Recordable[]>([])
        const totalColumns = ref<Recordable[]>([])
        const treeData = ref<TreeDataItem[]>([])
        const foreignTableData = ref<Recordable[]>([])
        const totalData = computed(() => {
            console.log(resTableData.value)
            if (!resTableData.value.length) return {}
            return {
                staffNum: resTableData.value.map((i) => i['peopleNum']).reduce((pre, cur) => plus(pre, cur ?? 0), 0) || 0,
                projectNum: resTableData.value.length || 0,
                ['一次性奖']: resTableData.value.map((i) => i['一次性奖']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人住房公积金']: resTableData.value
                    .map((i) => i['代扣个人住房公积金'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人保险']: resTableData.value.map((i) => i['代扣个人保险']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['其他']: resTableData.value.map((i) => i['其他']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳住房公积金']: resTableData.value
                    .map((i) => i['单位缴纳住房公积金'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳社会保险费']: resTableData.value
                    .map((i) => i['单位缴纳社会保险费'])
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['奖金']: resTableData.value.map((i) => i['奖金']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['差旅补助']: resTableData.value.map((i) => i['差旅补助']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['应得工资']: resTableData.value.map((i) => i['应得工资']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['扣个人所得税']: resTableData.value.map((i) => i['扣个人所得税']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['报销费用']: resTableData.value.map((i) => i['报销费用']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['税金']: resTableData.value.map((i) => i['税金']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['管理费']: resTableData.value.map((i) => i['管理费']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['职工薪酬 小计']: resTableData.value.map((i) => i['职工薪酬 小计']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['通讯积分']: resTableData.value.map((i) => i['通讯积分']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['防暑降温费']: resTableData.value.map((i) => i['防暑降温费']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                totalFee: resTableData.value.map((i) => i['费用合计']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                happenTotalFee: resTableData.value.map((i) => i['totalOccurrence']).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
            }
        })

        const foreignTotalData = computed(() => {
            console.log(resTableData.value)

            if (!resTableData.value.length) return {}
            return {
                staffNum: resTableData.value.map((i) => i['peopleNum']).reduce((pre, cur) => plus(pre, cur ?? 0), 0) || 0,
                projectNum: resTableData.value.length || 0,
                ['一次性奖']: resTableData.value
                    .filter((item) => item.projectType != 1)
                    .map((i) => i['一次性奖'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['海外补贴']: resTableData.value
                    .filter((item) => item.projectType != 1)
                    .map((i) => i['海外补贴'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人住房公积金']: resTableData.value
                    .filter((item) => item.projectType != 1)
                    .map((i) => i['代扣个人住房公积金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['代扣个人保险']: resTableData.value
                    .filter((item) => item.projectType != 1)
                    .map((i) => i['代扣个人保险'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['其他']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['其他'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳住房公积金']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['单位缴纳住房公积金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['单位缴纳社会保险费']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['单位缴纳社会保险费'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['奖金']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['奖金'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['差旅补助']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['差旅补助'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['应得工资']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['应得工资'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['扣个人所得税']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['扣个人所得税'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['报销费用']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['报销费用'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['税金']: resTableData.value.map((i) => i['税金'] ?? 0).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['管理费']: resTableData.value.map((i) => i['管理费'] ?? 0).reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['职工薪酬 小计']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['职工薪酬 小计'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['通讯积分']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['通讯积分'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                ['防暑降温费']: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['防暑降温费'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                totalFee: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['费用合计'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
                happenTotalFee: resTableData.value
                    .filter((item) => item.projectType != 2)
                    .map((i) => i['totalOccurrence'] ?? 0)
                    .reduce((pre, cur) => plus(pre, cur ?? 0), 0),
            }
        })
        const dynamicTotalData = ref<any>({})
        const inlandDynamicTotalData = ref<any>({})

        const pagination = ref({
            current: 1,
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条`,
            total: 0,
            onChange: (current) => {
                pagination.value.current = current
            },
        })

        const canEdit = computed(() => formData.value.billState !== 1)
        const loading = ref(false)
        const showTable = ref(false)

        const getDynamicTotalRowData = (record, needEveryCalc = false) => {
            let partCalcChildren: any = []
            if (needEveryCalc) {
                partCalcChildren = columns.value
                    .find((el) => el.dataIndex == 'otherFee')
                    ?.children?.filter((el) => el.partCalc === false)
            } else {
                partCalcChildren = columns.value.find((el) => el.dataIndex == 'otherFee')?.children?.filter((el) => el.partCalc)
            }
            if (!partCalcChildren.length) return 0
            else {
                return partCalcChildren.reduce((pre, cur) => {
                    return plus(pre, record[cur.dataIndex] || 0)
                }, 0)
            }
        }
        const exportBillId = ref<any>()
        const getDetail = async () => {
            let res
            try {
                loading.value = true
                if (createResponse.value?.billIdList) {
                    res = await request.post(`/api/hr-bills/search-batch`, createResponse.value?.billIdList)
                    treeData.value = ArrToTree(res.hrClientDTOList, { id: 'id', pid: 'parentId' })
                    exportBillId.value = res.id
                } else {
                    res = await request.get(`/api/hr-bills/${billId.value}`)
                }
            } catch (err) {
                modalClose()
            } finally {
                setTimeout(() => {
                    loading.value = false
                }, 300)
            }
            formData.value = {
                ...res,
                hrBillTotal: res?.hrBillTotal || {
                    total: 0,
                },
            }
            resTableData.value = res.billDetailList.map((el) => {
                let obj = {}
                el.hrBillDetailItemsList.forEach((ele) => {
                    obj[ele.expenseName] = ele.amount
                })
                return { ...el, ...obj, totalOccurrence: el.total }
            })
            foreignTableData.value = res.hrBillTotalList.filter((item) => {
                return item.sinopecType == 2
            })
            tableData.value = resTableData.value
            tableData.value.forEach((item) => {
                if (item.projectType == 2) {
                    item['税金'] = round(
                        times(
                            plus(
                                item['代扣个人保险'] ?? 0,
                                item['单位缴纳社会保险费'] ?? 0,
                                item['单位缴纳住房公积金'] ?? 0,
                                item['通讯积分'] ?? 0,
                                item['报销费用'] ?? 0,
                                item['代扣个人住房公积金'] ?? 0,
                                // getDynamicTotalRowData(record, true),
                            ),
                            0.0675,
                        ),
                        2,
                    )
                    item['费用合计'] = round(
                        plus(
                            item['代扣个人保险'] ?? 0,
                            item['单位缴纳社会保险费'] ?? 0,
                            item['单位缴纳住房公积金'] ?? 0,
                            item['通讯积分'] ?? 0,
                            item['代扣个人住房公积金'] ?? 0,
                            item['管理费'] ?? 0,
                            item['税金'] ?? 0,
                            item['报销费用'] ?? 0,
                        ),
                        2,
                    )
                }
            })
            pagination.value.total = tableData.value.length

            columns.value = [
                {
                    title: '序号',
                    dataIndex: 'index',
                    customRender: (record) => {
                        return pagination.value.pageSize * (pagination.value.current - 1) + (record.index + 1)
                    },
                    width: 80,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '当前年月',
                    dataIndex: 'currentYear',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '项目代码',
                    dataIndex: 'projectCode',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '项目名称',
                    dataIndex: 'projectName',
                    width: 150,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '人数',
                    dataIndex: 'peopleNum',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '项目类型',
                    dataIndex: 'projectType',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                    customRender: ({ record, text }) => {
                        if (record.projectType == 1 || record.projectType == '国内') {
                            return '国内'
                        } else {
                            return '国外'
                        }
                    },
                },
                {
                    title: '合同编号',
                    dataIndex: 'contractNo',
                    width: 200,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '发放的职工薪酬',
                    align: 'center',
                    children: [
                        {
                            title: '职工薪酬 小计',
                            dataIndex: '职工薪酬 小计',
                            align: 'center',
                            width: 110,
                            customRender: ({ record, text }) => {
                                record['职工薪酬 小计'] = round(
                                    plus(
                                        record['应得工资'] ?? 0,
                                        record['奖金'] ?? 0,
                                        record['一次性奖'] ?? 0,
                                        record['防暑降温费'] ?? 0,
                                        record['差旅补助'] ?? 0,
                                        record['其他'] ?? 0,
                                        getDynamicTotalRowData(record),
                                    ),
                                    2,
                                )
                                return text || 0
                            },
                        },
                        {
                            title: '其中',
                            align: 'center',
                            children: [
                                {
                                    title: '应得工资',
                                    dataIndex: '应得工资',
                                    align: 'center',
                                    width: 100,
                                    customRender: ({ record, index, text }) => {
                                        return canEdit.value
                                            ? h(InputNumber, {
                                                  value: text || 0,
                                                  onChange: (e) => {
                                                      record['应得工资'] = Number(e) || 0
                                                  },
                                                  style: {
                                                      width: '100%',
                                                  },
                                              })
                                            : text || 0
                                    },
                                },
                                {
                                    title: '奖金',
                                    dataIndex: '奖金',
                                    align: 'center',
                                    width: 100,
                                    customRender: ({ record, index, text }) => {
                                        return canEdit.value
                                            ? h(InputNumber, {
                                                  value: text || 0,
                                                  onChange: (e) => {
                                                      record['奖金'] = Number(e) || 0
                                                  },
                                                  style: {
                                                      width: '100%',
                                                  },
                                              })
                                            : text || 0
                                    },
                                },
                            ],
                        },
                    ],
                },
                {
                    title: '单位缴纳社会保险费',
                    dataIndex: '单位缴纳社会保险费',
                    align: 'center',
                    width: 150,
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['单位缴纳社会保险费'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '单位缴纳住房公积金',
                    dataIndex: '单位缴纳住房公积金',
                    align: 'center',
                    width: 150,
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['单位缴纳住房公积金'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    // title: '其它费用',
                    dataIndex: 'otherFee',
                    align: 'center',
                    children: [],
                    slots: { title: 'otherHeader' },
                },
                {
                    title: '报销费用',
                    dataIndex: '报销费用',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['报销费用'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '管理费',
                    dataIndex: '管理费',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['管理费'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '税金',
                    dataIndex: '税金',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, text }) => {
                        if (record.projectType == 1) {
                            record['税金'] = round(
                                times(
                                    plus(
                                        record['职工薪酬 小计'] ?? 0,
                                        record['单位缴纳社会保险费'] ?? 0,
                                        record['单位缴纳住房公积金'] ?? 0,
                                        record['通讯积分'] ?? 0,
                                        record['报销费用'] ?? 0,
                                        getDynamicTotalRowData(record, true),
                                    ),
                                    0.0675,
                                ),
                                2,
                            )
                        } else if (record.projectType == 2) {
                            record['税金'] = round(
                                times(
                                    plus(
                                        record['代扣个人保险'] ?? 0,
                                        record['单位缴纳社会保险费'] ?? 0,
                                        record['单位缴纳住房公积金'] ?? 0,
                                        record['通讯积分'] ?? 0,
                                        record['报销费用'] ?? 0,
                                        record['代扣个人住房公积金'] ?? 0,
                                        // getDynamicTotalRowData(record, true),
                                    ),
                                    0.0675,
                                ),
                                2,
                            )
                        }
                        return text || 0
                    },
                },
                {
                    title: '代扣个人保险',
                    dataIndex: '代扣个人保险',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['代扣个人保险'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '代扣个人住房公积金',
                    dataIndex: '代扣个人住房公积金',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['代扣个人住房公积金'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '扣个人所得税',
                    dataIndex: '扣个人所得税',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text || 0,
                                  onChange: (e) => {
                                      record['扣个人所得税'] = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '结算费用合计',
                    dataIndex: '费用合计',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        if (record.projectType == 1) {
                            return canEdit.value
                                ? h(InputNumber, {
                                      value: text || 0,
                                      onChange: (e) => {
                                          record['费用合计'] = Number(e) || 0
                                      },
                                      style: {
                                          width: '100%',
                                      },
                                  })
                                : text || 0
                        } else if (record.projectType == 2) {
                            return h(InputNumber, {
                                value: text || 0,
                                onChange: (e) => {
                                    record['费用合计'] = Number(e) || 0
                                },
                                style: {
                                    width: '100%',
                                },
                            })
                        }
                    },
                },
                {
                    title: '发生费用合计',
                    dataIndex: 'totalOccurrence',
                    width: 120,
                    align: 'center',
                    customRender: ({ record, text }) => {
                        record['totalOccurrence'] = round(
                            plus(
                                record['职工薪酬 小计'] ?? 0,
                                record['单位缴纳社会保险费'] ?? 0,
                                record['单位缴纳住房公积金'] ?? 0,
                                record['通讯积分'] ?? 0,
                                record['报销费用'] ?? 0,
                                record['管理费'] ?? 0,
                                record['税金'] ?? 0,
                            ),
                            2,
                        )
                        return text || 0
                    },
                },
            ]

            totalColumns.value = [
                {
                    title: '项目数',
                    dataIndex: 'projectNum',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '人数',
                    dataIndex: 'staffNum',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '发放的职工薪酬',
                    align: 'center',
                    children: [
                        {
                            title: '职工薪酬 小计',
                            dataIndex: '职工薪酬 小计',
                            align: 'center',
                            width: 110,
                        },
                        {
                            title: '其中',
                            align: 'center',
                            children: [
                                {
                                    title: '应得工资',
                                    dataIndex: '应得工资',
                                    align: 'center',
                                    width: 100,
                                },
                                {
                                    title: '奖金',
                                    dataIndex: '奖金',
                                    align: 'center',
                                    width: 100,
                                },
                            ],
                        },
                    ],
                },
                {
                    title: '单位缴纳社会保险费',
                    dataIndex: '单位缴纳社会保险费',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '单位缴纳住房公积金',
                    dataIndex: '单位缴纳住房公积金',
                    width: 150,
                    align: 'center',
                },
                {
                    title: '其它费用',
                    dataIndex: 'totalOtherFee',
                    align: 'center',
                    children: [],
                },
                {
                    title: '报销费用',
                    dataIndex: '报销费用',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '管理费',
                    dataIndex: '管理费',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '税金',
                    dataIndex: '税金',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '代扣个人保险',
                    dataIndex: '代扣个人保险',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '代扣个人住房公积金',
                    dataIndex: '代扣个人住房公积金',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '扣个人所得税',
                    dataIndex: '扣个人所得税',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '结算费用合计',
                    dataIndex: 'totalFee',
                    width: 120,
                    align: 'center',
                },
                {
                    title: '发生费用合计',
                    dataIndex: 'happenTotalFee',
                    width: 120,
                    align: 'center',
                },
            ]
            if (res.contractNoShow == 0) {
                columns.value.map((item, index) => {
                    if (item.dataIndex == 'contractNo') {
                        columns.value.splice(index, 1)
                    }
                    return item
                })
            }
            // 获取动态表头列
            if (tableData.value.length) {
                const otherBillItemsList = tableData.value[0]?.hrBillDetailItemsList.filter((el) => el.expenseType == 37)
                const temp: any = columns.value.find((el) => el.dataIndex == 'otherFee')
                const totalTemp: any = totalColumns.value.find((el) => el.dataIndex == 'totalOtherFee')
                temp.children = [...formatSinopecDynamicCols(otherBillItemsList, false)]
                totalTemp.children = [...formatSinopecDynamicCols(otherBillItemsList, true)]
                dynamicColumns.value =
                    otherBillItemsList
                        .filter((el) => el.isDefault != 0 || el.calcDynamicTotal == null)
                        .map((el) => {
                            return {
                                title: el.expenseName,
                                dataIndex: el.expenseName,
                                width: 110,
                                align: 'center',
                                partCalc: el.isDefault == 1,
                            }
                        }) || []
                dynamicColumns.value.forEach((el) => {
                    console.log(el)
                    dynamicTotalData.value[el.title] = calcDynamicTotal(el.title)
                    inlandDynamicTotalData.value[el.title] = inlandCalcDynamicTotal(el.title)
                    inlandDynamicTotalData.value['一次性奖'] = tableData.value
                        .filter((lst) => lst.projectType == 1)
                        .map((el) => el['一次性奖'])
                        .reduce((pre, cur) => plus(pre, cur ?? 0), 0)
                    inlandDynamicTotalData.value['海外补贴'] = tableData.value
                        .filter((lst) => lst.projectType == 1)
                        .map((el) => el['海外补贴'])
                        .reduce((pre, cur) => plus(pre, cur ?? 0), 0)
                })
            }
            showTable.value = true

            let list
            if (!createResponse.value?.billIdList) {
                list = await request.get(`/api/hr-bill-details/list?billId=${billId.value}`)
                unInsertList.value = list
            }
        }

        const search = ref({
            projectName: '',
            projectCode: '',
        })

        const filterTableData = () => {
            let filterOdds
            if (!search.value.projectName && !search.value.projectCode) {
                tableData.value = resTableData.value
                pagination.value.current = 1
                pagination.value.total = tableData.value.length
                return
            } else if (!search.value.projectName && search.value.projectCode) {
                filterOdds = (el) => {
                    return el.projectCode.includes(search.value.projectCode)
                }
            } else if (search.value.projectName && !search.value.projectCode) {
                filterOdds = (el) => {
                    return el.projectName.includes(search.value.projectName)
                }
            } else {
                filterOdds = (el) => {
                    return el.projectName.includes(search.value.projectName) && el.projectCode.includes(search.value.projectCode)
                }
            }
            tableData.value = resTableData.value.filter((el) => {
                return filterOdds(el)
            })
            pagination.value.current = 1
            pagination.value.total = tableData.value.length
        }

        const formatSinopecDynamicCols = (cols, isTotal): [] => {
            let arr: any = []
            const fixedStrArr = ['通讯积分', '差旅补助', '防暑降温费', '一次性奖']
            if (!isTotal) {
                const fixedArr = cols
                    .filter((el) => el.isDefault == 0)
                    .sort((a, b) => {
                        return fixedStrArr.indexOf(b.expenseName) - fixedStrArr.indexOf(a.expenseName)
                    })
                const dynamicArr = cols.filter((el) => el.isDefault != 0)
                fixedArr.forEach((el) => {
                    arr.push({
                        title: el.expenseName,
                        dataIndex: el.expenseName,
                        width: 110,
                        align: 'center',
                        customRender: ({ record, index, text }) => {
                            return canEdit.value
                                ? h(InputNumber, {
                                      value: text || 0,
                                      onChange: (e) => {
                                          record[el.expenseName] = Number(e) || 0
                                      },
                                      style: {
                                          width: '100%',
                                      },
                                  })
                                : text || 0
                        },
                    })
                })
                dynamicArr.forEach((el) => {
                    console.log(el)
                    arr.push({
                        dataIndex: el.expenseName,
                        width: 110,
                        align: 'center',
                        partCalc: el.isDefault == 1,
                        title: h('div', { class: ['cell_wrapper'] }, [
                            h(
                                'div',
                                {
                                    style: {
                                        width: ~`calc(100% - 14px)`,
                                        marginRight: '15px',
                                    },
                                },
                                el.expenseName,
                            ),
                            h(MinusCircleOutlined, {
                                style: {
                                    position: 'absolute',
                                    right: 0,
                                    cursor: 'pointer',
                                    width: '14px',
                                },
                                onClick: () => removeColumns({ title: el.expenseName, dataIndex: el.expenseName }),
                            }),
                        ]),
                        customRender: ({ record, index, text }) => {
                            return canEdit.value
                                ? h(InputNumber, {
                                      value: text || 0,
                                      onChange: (e) => {
                                          console.log(e)
                                          record[el.expenseName] = Number(e) || 0
                                          dynamicTotalData.value[el.expenseName] = calcDynamicTotal(el.expenseName)
                                          inlandDynamicTotalData.value[el.expenseName] = inlandCalcDynamicTotal(el.expenseName)
                                      },
                                      style: {
                                          width: '100%',
                                      },
                                  })
                                : text || 0
                        },
                    })
                })
            } else {
                const columns = cols.sort((a, b) => {
                    return fixedStrArr.indexOf(b.expenseName) - fixedStrArr.indexOf(a.expenseName)
                })
                columns.forEach((el) => {
                    arr.push({
                        title: el.expenseName,
                        dataIndex: el.expenseName,
                        width: 110,
                        align: 'center',
                    })
                })
            }

            return arr
        }

        const resetData = () => {
            formData.value = {
                hrBillTotal: {
                    total: 0,
                },
            }
            addForm.value = { cellName: undefined, toAdd: 2 }
            search.value = {
                projectName: '',
                projectCode: '',
            }
            resTableData.value = []
            tableData.value = []
            pagination.value.total = 0
            pagination.value.current = 1
            isUpdated.value = false
            showTable.value = false
            totalColumns.value = []
            columns.value = []
            dynamicColumns.value = []
        }
        const modalClose = () => {
            resetData()
            emit('update:visible', false)
        }

        const postDataFormat = () => {
            dynamicColumns.value.forEach((el) => {
                tableData.value.forEach((ele) => {
                    const current = el?.res?.find((item) => item.billDetailId == ele.id) || null
                    Object.keys(ele).forEach((key) => {
                        const currentItem = ele.hrBillDetailItemsList.find((item) => item.expenseName == key)
                        if (currentItem) currentItem.amount = ele[key]
                    })
                    ele.hrBillDetailItemsList = current
                        ? [
                              ...ele.hrBillDetailItemsList,
                              { ...current, amount: ele[el.dataIndex] || 0, isDefault: el.partCalc ? 1 : 2 },
                          ]
                        : ele.hrBillDetailItemsList
                })
            })
        }

        const confirmLoading = ref(false)
        const modalConfirm = async () => {
            search.value.projectName = ''
            search.value.projectCode = ''
            filterTableData()
            try {
                confirmLoading.value = true

                const originList = formData.value.billDetailList
                const originIds = originList.map((i) => i.id)
                const currentIds = tableData.value.map((i) => i.id)

                const deleteList = originList
                    .filter((i) => !currentIds.includes(i.id))
                    .map((i) => ({
                        ...i,
                        updateState: 2,
                    }))
                tableData.value.forEach((i) => {
                    if (!originIds.includes(i.id)) {
                        i.updateState = 1
                    } else {
                        i.updateState = 3
                    }
                })
                postDataFormat()
                console.log(formData.value)

                await request.put(`/api/hr-bills`, {
                    id: formData.value.id,
                    title: formData.value.title,
                    billDetailList: [...tableData.value, ...deleteList],
                    billType: 3,
                    hrBillTotalList: [
                        {
                            id: formData.value.hrBillTotalList[0]?.id,
                            billId: formData.value.hrBillTotal?.billId,
                            total: totalData.value?.totalFee || 0,
                            totalOccurrence: totalData.value?.happenTotalFee || 0,
                            staffNum: totalData.value.staffNum,
                            projectNum: totalData.value.projectNum,
                        },
                        {
                            id: formData.value.hrBillTotalList[1]?.id,
                            billId: formData.value.hrBillTotal?.billId,
                            total: foreignTotalData.value?.totalFee || 0,
                            totalOccurrence: foreignTotalData.value?.happenTotalFee || 0,
                            staffNum: foreignTotalData.value.staffNum,
                            projectNum: foreignTotalData.value.projectNum,
                        },
                    ],
                })
                resetData()
                emit('confirm')
            } finally {
                confirmLoading.value = false
            }
        }
        const uploadNewOrigin = () => {
            modalClose()
            emit('changeOrigin')
        }

        const exportOrigin = () => {
            downFile('get', formData.value.originBillUrl, formData.value.originBillName)
        }
        const exportBill = async (type, title) => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            if (type == 0) {
                if (!selectedProject.value.length) {
                    message.warning('请选择账单明细!')
                    return
                }
                selectedProject.value?.forEach((lst) => {
                    if (lst.projectType == 1) {
                        lst.projectType = '国内'
                    } else {
                        lst.projectType = '国外'
                    }
                })
                console.log(selectedProject.value)
                multiSheetToExport(columns.value.slice(1, columns.value.length), selectedProject.value, title, false)
            } else if (type == 1) multiSheetToExport(columns.value.slice(1, columns.value.length), tableData.value, title, true)
            else
                downFile('post', `/api/hr-bill-details/export-sinopec-bill`, `${title}`, {
                    billId: exportBillId.value,
                    exportType: type,
                    clientId: formData.value.clientId,
                })
        }
        const exportTotalBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            multiSheetToExport(
                totalColumns.value,
                [
                    {
                        id: formData.value.hrBillTotal.id ?? 0,
                        ...totalData.value,
                        ...dynamicTotalData.value,
                        hrBillDetailItemsList: tableData.value[0].hrBillDetailItemsList,
                    },
                ],
                `${formData.value.title}汇总`,
                false,
                true,
            )
        }
        const exportInlandTotalBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            multiSheetToExport(
                totalColumns.value,
                [
                    {
                        id: formData.value.hrBillTotal.id ?? 0,
                        ...foreignTotalData.value,
                        ...inlandDynamicTotalData.value,
                        hrBillDetailItemsList: tableData.value[0].hrBillDetailItemsList,
                    },
                ],
                `${formData.value.title}汇总`,
                false,
                true,
            )
        }
        const insertTableRef = ref()
        const tableRef = ref()
        const showInsertProject = ref(false)
        const selectedProject = ref<Recordable[]>([])
        const unInsertList = ref<Recordable[]>([])
        const insertProject = () => {
            showInsertProject.value = true
        }
        const calcCurrentPage = (total) => {
            return Math.ceil(divide(total, 10))
        }
        const removeProject = () => {
            if (!selectedProject.value.length) {
                message.warning('请选择要删除的项目！')
                return
            }
            selectedProject.value.forEach((i) => {
                i.selectedProject = 2 // 更新状态为删除
            })
            unInsertList.value = [...unInsertList.value, ...selectedProject.value]
            const ids = selectedProject.value.map((i) => i.id)
            const changedList = tableData.value.filter((i) => !ids.includes(i.id))
            resTableData.value = changedList
            filterTableData()
            pagination.value.current = calcCurrentPage(pagination.value.total)
            isUpdated.value = true
            selectedProject.value = []
            tableRef.value?.checkboxReset()
        }
        const selProject = (list) => {
            selectedProject.value = list
        }
        const selAll = (isSel) => {
            selectedProject.value = isSel ? [...tableData.value] : []
        }
        const insertConfirm = async () => {
            if (!selectedProject.value.length) {
                message.warning('请选择要添加的项目！')
                return
            }
            const ids = selectedProject.value.map((i) => i.id)
            // tableData 添加
            const res = await request.post(`/api/hr-bill-details/batchAdd`, {
                ids: ids,
                billIds: createResponse.value?.billIdList,
            })
            resTableData.value = [
                ...resTableData.value,
                ...res.map((el) => {
                    let obj = {}
                    el.hrBillDetailItemsList.forEach((ele) => {
                        obj[ele.expenseName] = ele.amount
                    })
                    return { ...el, ...obj, totalOccurrence: el.total }
                }),
            ]
            filterTableData()
            isUpdated.value = true
            unInsertList.value = unInsertList.value.filter((i) => !ids.includes(i.id))
            insertClose()
        }
        const insertClose = () => {
            selectedProject.value = []
            showInsertProject.value = false
            insertTableRef.value.checkboxReset()
        }

        const otherModalClose = () => {
            addForm.value = { cellName: undefined, toAdd: 2 }
            showOtherModal.value = false
        }

        const showAddOtherModal = () => {
            showOtherModal.value = true
        }

        const removeColumns = async (item) => {
            try {
                const res = await request.post(`/api/hr-bill-detail-itemses/delete`, {
                    billId: formData.value.id,
                    expenseName: item.title,
                })
                const temp: any = columns.value.find((el) => el.dataIndex == 'otherFee')
                const totalTemp: any = totalColumns.value.find((el) => el.dataIndex == 'totalOtherFee')
                temp.children = [...temp.children.filter((el) => el.dataIndex != item.dataIndex)]
                totalTemp.children = [...totalTemp.children.filter((el) => el.dataIndex != item.dataIndex)]
            } catch (error) {
                console.log(error)
            }
        }

        const getDynamicCol = (obj, flag) => {
            return {
                ...obj,
                title: !flag
                    ? h('div', { class: ['cell_wrapper'] }, [
                          h(
                              'div',
                              {
                                  style: {
                                      width: ~`calc(100% - 14px)`,
                                      marginRight: '15px',
                                  },
                              },
                              obj.title,
                          ),
                          h(MinusCircleOutlined, {
                              style: {
                                  position: 'absolute',
                                  right: 0,
                                  cursor: 'pointer',
                                  width: '14px',
                              },
                              onClick: () => removeColumns(obj),
                          }),
                      ])
                    : obj.title,
                customRender: ({ record, index, text }) => {
                    return !flag
                        ? h(InputNumber, {
                              value: text || 0,
                              onChange: (e) => {
                                  record[obj.title] = Number(e) || 0
                                  dynamicTotalData.value[obj.title] = calcDynamicTotal(obj.title)
                                  inlandDynamicTotalData.value[obj.title] = inlandCalcDynamicTotal(obj.title)
                              },
                              style: {
                                  width: '100%',
                              },
                          })
                        : text || 0
                },
            }
        }

        const insertColumns = async () => {
            try {
                const obj = {
                    title: addForm.value.cellName || '',
                    dataIndex: addForm.value.cellName || '',
                    width: 110,
                    align: 'center',
                    partCalc: addForm.value.toAdd == 1,
                }
                const res = await request.post(`/api/hr-bill-detail-itemses/create`, {
                    billId: formData.value.id,
                    expenseName: addForm.value.cellName,
                    isDefault: addForm.value.toAdd,
                })
                dynamicColumns.value.push({ ...obj, res: res })
                const temp: any = columns.value.find((el) => el.dataIndex == 'otherFee')
                const totalTemp: any = totalColumns.value.find((el) => el.dataIndex == 'totalOtherFee')
                temp.children = [...temp.children, getDynamicCol(obj, false)]
                totalTemp.children = [...totalTemp.children, getDynamicCol(obj, true)]
                tableData.value.forEach((el: any) => {
                    el[obj.title] = 0
                })
                dynamicTotalData.value[obj.title] = 0
                inlandDynamicTotalData.value[obj.title] = 0
            } catch (error) {
                console.log(error)
            }
        }

        const calcDynamicTotal = (title) => {
            return tableData.value.map((el) => el[title]).reduce((pre, cur) => plus(pre, cur ?? 0), 0)
        }

        const inlandCalcDynamicTotal = (title) => {
            console.log(title)
            return tableData.value.map((el) => el[title]).reduce((pre, cur) => plus(pre, cur ?? 0), 0)
        }
        const otherModalConfirm = async () => {
            addFormRef.value
                .validate()
                .then(() => {
                    const temp = dynamicColumns.value.find((el) => el.title == addForm.value.cellName)
                    if (temp) {
                        message.error('列名重复')
                        return
                    }
                    insertColumns()
                    otherModalClose()
                })
                .catch((err) => {
                    console.log(err)
                })
        }

        return {
            search,
            filterTableData,
            insertTableRef,
            removeProject,
            insertProject,
            unInsertList,
            showInsertProject,
            insertClose,
            insertConfirm,
            dynamicTotalData,
            inlandDynamicTotalData,
            selAll,
            selProject,
            selectedProject,
            addFormRef,
            addForm,
            showOtherModal,
            showAddOtherModal,
            otherModalClose,
            otherModalConfirm,
            treeData,
            modalConfirmDisabled,
            foreignTotalData,
            pagination,
            canEdit,
            loading,
            confirmLoading,
            showTable,
            totalColumns,
            tableRef,
            totalData,
            exportBill,
            exportTotalBill,
            tableData,
            columns,
            exportOrigin,
            uploadNewOrigin,
            formData,
            previewFile,
            modalConfirm,
            modalClose,
            detailsAppendix,
            summaryAppendix,
            receiptAppendix,
            exportInlandTotalBill,
            unInsertCols: [
                {
                    title: '项目代码',
                    dataIndex: 'projectCode',
                    width: 160,
                },
                {
                    title: '项目名称',
                    dataIndex: 'projectName',
                    width: 120,
                },
            ],
            exportNameList: ['中石化费用统计账单明细', '各项目费用统计表', '各项目技术服务费结算书', '各项目技术服务费收据'],
        }
    },
})
</script>

<style lang="less">
.search-wrapper {
    display: flex;
    margin: 10px 0;
    & > input {
        width: 230px;
        margin-right: 20px;
    }
}
.cell_wrapper {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
}
</style>

<style scoped lang="less">
:deep(.ant-table-header-column) {
    width: 100%;
}
:deep(.ant-table-header-column) > div {
    position: relative;
}
.other_header_btn {
    position: absolute;
    right: 0;
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon {
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }
    }
    .main {
        padding: 10px 0;
    }
}
.wrapper {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}
.appendixBox {
    padding: 10px 10px 10px 5px;
    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.row {
    margin: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    .col {
        margin-right: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .label {
            width: 80px;
            text-align: right;
            &:after {
                content: '：';
            }
        }
    }
}
.clclientName {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
