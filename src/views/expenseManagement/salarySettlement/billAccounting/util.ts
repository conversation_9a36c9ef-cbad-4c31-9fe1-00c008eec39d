import { InputNumber } from 'ant-design-vue'
import { minus, plus, round, times } from 'number-precision'
import { h } from 'vue'

// eslint-disable-next-line no-unused-vars
export const formatOriginForm = (
    src,
    // eslint-disable-next-line no-unused-vars
    callback: (val, label, order) => void,
    columns: Recordable[] = [],
    data: Recordable = { id: 1 },
) => {
    src.forEach((i) => {
        const col: Recordable = {
            title: i.columnLabel,
            dataIndex: i.columnLabel,
            align: 'center',
            width: 100,
            customRender: ({ text }) => {
                if (text.includes('&')) {
                    return h(
                        'a',
                        {
                            onClick: () => {
                                callback(text.slice(0, -1), i.columnLabel, i.order)
                            },
                        },
                        text.slice(0, -1),
                    )
                } else {
                    return text
                }
            },
        }
        data[i.columnLabel] = i.value + (i.hasMapping ? '' : '&')
        if (i.children && i.children.length) {
            const { columns: childColumns, data: childData } = formatOriginForm(i.children, callback)
            data = { ...data, ...childData }
            col.children = childColumns
        }
        columns.push(col)
    })
    return {
        columns,
        data,
    }
}

export const formatMapData = (data) => {
    console.log(data)
    return {
        columns: [
            {
                title: '#',
                cols: 1,
            },
            ...data[0].map((i) => ({
                title: i.value,
                cols: i.lastCol - i.firstCol + 1,
            })),
        ],
        data: [
            [
                {
                    value: '表单列',
                },
                ...data[1],
            ],
            [
                {
                    value: '表单字段',
                },
                ...data[2],
            ],
        ],
    }
}

export const formatDynamicCols = (cols, callback, showText = false, pagination, isEastArea?, isSpecial?) => {
    return cols.map((i) => ({
        title: i.value,
        dataIndex: i.key,
        children:
            i.children && i.children.length
                ? formatDynamicCols(i.children, callback, showText, pagination, isEastArea, isSpecial)
                : undefined,
        width: i.children && i.children.length ? i.children.length * 110 : 120,
        align: 'center',
        customRender: ({ record, text, index }) => {
            record[i.key] = record.hrBillDetailItemsList?.find((v) => v.expenseName == i.key)?.amount ?? 0
            if (isEastArea && isSpecial !== -1) {
                if (record.staffStatus == 5) {
                    if (i.key?.includes('岗位工资')) {
                        return text ?? 0
                    } else {
                        return h(InputNumber, {
                            value: text ?? 0,
                            onChange: (e) => {
                                callback(e, (pagination.value.current - 1) * pagination.value.pageSize + index, i.key, true)
                            },
                            style: {
                                width: '100%',
                            },
                        })
                    }
                } else if (record.staffStatus == 6) {
                    if (record.hrBillDetailItemsList?.length) {
                        return h(InputNumber, {
                            value: text ?? 0,
                            onChange: (e) => {
                                callback(e, (pagination.value.current - 1) * pagination.value.pageSize + index, i.key, true)
                            },
                            style: {
                                width: '100%',
                            },
                        })
                    } else {
                        return text ?? 0
                    }
                }
            }
            return showText
                ? text
                : h(InputNumber, {
                      value: text,
                      onChange: (e) => {
                          callback(e, (pagination.value.current - 1) * pagination.value.pageSize + index, i.key, true)
                      },
                      style: {
                          width: '100%',
                      },
                  })
        },
    }))
}

//formatCompareDynamicCols 递归children
export const recursionChild = (item) => {
    return item.map((item, key) => ({
        //    columnChildrenTitleList.value.push({
        title: item.title,
        dataIndex: item.dataIndex,
        key: item.key,
        align: 'center',
        width: item.children?.length ? item.children?.length * 110 : 120,
        customHeaderCell: (column) => {
            return {
                style: {
                    //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                    color: item.color ? '#FFFFFF' : '#000000', //如果color
                    'background-color': item.color ? item.color : '#FFFFFF',
                },
            }
        },
        children: item.children && item.children?.length ? recursionChild(item.children) : undefined,
        //    })
    }))
}
export const formatDynamicColns = (res, columnList) => {
    columnList.value = []
    res.dynamicHeaders.map((item, key) => {
        columnList.value.push({
            title: item.title,
            dataIndex: item.dataIndex,
            key: item.key,
            align: 'center',
            fixed: key < 4 ? 'left' : '',
            width: item.children?.length ? item.children?.length * 110 : 120,
            customHeaderCell: (column) => {
                return {
                    style: {
                        //后端返回的color 即为前端的背景颜色， 为null 即白色  其他会返回实际颜色
                        color: item.color ? '#FFFFFF' : '#000000', //如果color
                        'background-color': item.color ? item.color : '#FFFFFF',
                    },
                }
            },
            children: item.children && item.children?.length ? recursionChild(item.children) : undefined,
        })
    })
    return columnList
}

export const formatCompareDynamicCols = (cols, data) => {
    const res: Recordable[] = []
    cols.forEach((i, idx) => {
        data[i.key] = i.value
        res.push({
            title: idx === 0 ? '动态表头列' : '',
            dataIndex: i.key,
            colSpan: idx === 0 ? cols.length : 0,
            width: 120,
        })
    })
    return {
        columns: res,
        data,
    }
}

export const formatDynamicHeaders = (res) => {}

/**
 * 计算保障账单明细
 * @param record
 * @param res
 * @returns
 */
export const calcRecord = (record, res) => {
    // 单位社保
    // 单位养老
    record.unitPension = round(times(record.socialSecurityCardinal ?? 0, res.unitPensionScale ?? 0), 2)
    // 单位失业
    record.unitUnemployment = round(times(record.socialSecurityCardinal, res.unitUnemploymentScale ?? 0), 2)
    // 单位工伤
    record.workInjury = round(times(record.socialSecurityCardinal ?? 0, res.workInjuryScale ?? 0), 2)
    // 单位医疗
    record.unitMedical = round(times(record.medicalInsuranceCardinal ?? 0, res.unitMedicalScale ?? 0), 2)
    // 单位生育
    record.unitMaternity = round(times(record.unitMaternityCardinal ?? 0, res.unitMaternityScale ?? 0), 2)
    // 单位小计
    record.unitSubtotal = plus(
        record.unitPension ?? 0,
        record.unitUnemployment ?? 0,
        record.workInjury ?? 0,
        record.unitMedical ?? 0,
        record.unitMaternity ?? 0,
        // 单位社保补差
        record.unitSocialSecurityMakeUp ?? 0,
    )

    // 个人社保
    // 个人养老
    record.personalPension = round(times(record.socialSecurityCardinalPersonal ?? 0, res.personalPensionScale ?? 0), 2)
    // 个人失业
    record.personalUnemployment = round(times(record.socialSecurityCardinalPersonal ?? 0, res.personalUnemploymentScale ?? 0), 2)
    // 个人医疗
    record.personalMedical = round(times(record.medicalInsuranceCardinalPersonal ?? 0, res.personalMedicalScale ?? 0), 2)
    // 个人生育
    record.personalMaternity = round(times(record.personalMaternityCardinal ?? 0, res.personalMaternityScale ?? 0), 2)
    // 个人小计
    record.personalSubtotal = plus(
        record.personalPension ?? 0,
        record.personalUnemployment ?? 0,
        record.personalMedical ?? 0,
        record.personalMaternity ?? 0,
        // 个人补差
        record.personalSocialSecurityMakeUp ?? 0,
    )
    // 社保总金额
    record.socialSecurityTotal = plus(record.unitSubtotal ?? 0, record.personalSubtotal ?? 0)
    // 公积金
    // 单位公积金
    record.unitAccumulationFund = round(
        plus(
            times(record.accumulationFundCardinal ?? 0, res.unitAccumulationFundScale ?? 0),
            record.unitAccumulationFundMakeUp ?? 0,
        ),
        0,
    )
    // 个人公积金
    record.personalAccumulationFund = round(
        plus(
            times(record.accumulationFundCardinal ?? 0, res.personalAccumulationFundScale ?? 0),
            record.unitAccumulationFundMakeUp ?? 0,
        ),
        0,
    )
    // 公积金总金额
    record.accumulationFundTotal = plus(
        record.unitAccumulationFund ?? 0,
        record.personalAccumulationFund ?? 0,
        // record.unitAccumulationFundMakeUp ?? 0,
        // record.personalAccumulationFundMakeUp ?? 0,
    )
    // 总金额
    record.total = plus(record.socialSecurityTotal ?? 0, record.accumulationFundTotal ?? 0, record.serviceFee ?? 0)

    return record
}

/**
 * 计算薪酬账单明细
 * @param record
 * @param res
 * @returns
 */
export const calcSalary = (record, res) => {
    // 应发工资
    const add = record?.hrBillDetailItemsList
        ?.filter((i) => i.expenseType != '2' && i.expenseType != '3')
        ?.map((j) => j.amount ?? 0)
        ?.reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)
    const sub = record?.hrBillDetailItemsList
        ?.filter((i) => i.expenseType == '2')
        ?.map((j) => j.amount ?? 0)
        ?.reduce((pre, cur) => plus(pre ?? 0, Math.abs(cur) ?? 0), 0)
    record.salary = round(minus(add ?? 0, sub ?? 0), 2)
    // 单位社保
    record.unitPension = round(times(record.socialSecurityCardinal ?? 0, res.unitPensionScale ?? 0), 2)
    record.unitUnemployment = round(times(record.socialSecurityCardinal ?? 0, res.unitUnemploymentScale ?? 0), 2)
    record.unitMedical = round(times(record.medicalInsuranceCardinal ?? 0, res.unitMedicalScale ?? 0), 2)
    record.workInjury = round(times(record.socialSecurityCardinal ?? 0, res.workInjuryScale ?? 0), 2)
    record.unitSubtotal = plus(
        record.unitPension ?? 0,
        record.unitUnemployment ?? 0,
        record.workInjury ?? 0,
        record.unitMedical ?? 0,
        record.unitSocialSecurityMakeUp ?? 0,
    )
    // 个人社保
    record.personalPension = round(times(record.socialSecurityCardinalPersonal ?? 0, res.personalPensionScale ?? 0), 2)
    record.personalUnemployment = round(times(record.socialSecurityCardinalPersonal ?? 0, res.personalUnemploymentScale ?? 0), 2)
    record.personalMedical = round(times(record.medicalInsuranceCardinalPersonal ?? 0, res.personalMedicalScale ?? 0), 2)
    // record.personalSubtotal = plus(
    //     record.personalPension ?? 0,
    //     record.personalUnemployment ?? 0,
    //     record.personalMedical ?? 0,
    //     record.personalSocialSecurityMakeUp ?? 0,
    // )
    // 社保总金额
    record.socialSecurityTotal = plus(record.unitSubtotal ?? 0, record.personalSubtotal ?? 0)
    // 公积金
    /* record.unitAccumulationFund = round(
        plus(times(record.accumulationFundCardinal, res.unitAccumulationFundScale), record.unitAccumulationFundMakeUp ?? 0),
        0,
    ) */
    // record.personalAccumulationFund = round(plus(record.personalAccumulationFund, record.personalAccumulationFundMakeUp ?? 0), 0)
    record.accumulationFundTotal = plus(
        record.unitAccumulationFund ?? 0,
        record.personalAccumulationFund ?? 0,
        record.unitAccumulationFundMakeUp ?? 0,
        record.personalAccumulationFundMakeUp ?? 0,
    )
    // 税前应发
    record.preTaxSalary = minus(record.salary ?? 0, record.personalSubtotal ?? 0, record.personalAccumulationFund ?? 0)
    // 实发工资
    record.realSalary = round(minus(record.preTaxSalary ?? 0, record.personalTax ?? 0), 2)

    // 总金额
    record.otherFeeTotal =
        record.hrBillDetailItemsList
            ?.filter((i) => i.expenseType == '3')
            .map((i) => i.amount ?? 0)
            .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0) ?? 0
    record.total = plus(
        record.salary ?? 0,
        record.unitSubtotal,
        plus(record.unitAccumulationFund ?? 0, record.unitAccumulationFundMakeUp ?? 0),
        record.otherFeeTotal ?? 0,
        record.serviceFee ?? 0,
    )

    return record
}

export const calcAllSalary = (data: Recordable[], res) => {
    data.forEach((record) => {
        // 由于使用虚拟列表的关系 mounted后需要计算全部的薪水
        record = calcSalary(
            record,
            res.hrBillDTOList?.find((el) => {
                return el.clientId == record.clientId
            }) ?? res,
        )
    })
}

export const calcAllRecord = (data: Recordable[], res) => {
    data.forEach((record) => {
        // 由于使用虚拟列表的关系 mounted后需要计算全部的数值
        record = calcRecord(
            record,
            res.hrBillDTOList?.find((el) => {
                return el.clientId == record.clientId
            }) ?? res,
        )
    })
}
