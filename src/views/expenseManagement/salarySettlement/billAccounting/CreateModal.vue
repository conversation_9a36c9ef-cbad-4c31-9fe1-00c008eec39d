<template>
    <BasicEditModalSlot :visible="visible" :title="createTitle" @cancel="modalClose" :width="step == 1 ? '600px' : '1200px'">
        <template v-if="step == 1">
            <Form
                ref="formInline"
                :rules="rules"
                :model="formData"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 16 }"
                style="min-height: 250px"
            >
                <FormItem label="客户名称" name="clientId">
                    <ClientSelectTree
                        v-model:value="formData.clientId"
                        :itemForm="{
                            maxTag: 0,
                            placeholder: '客户名称',
                        }"
                        @labelChange="labelChange"
                        :renderInBody="true"
                    />
                </FormItem>
                <FormItem label="费用年月" name="paymentDate">
                    <MonthPicker
                        v-model:value="formData.paymentDate"
                        placeholder="费用年月"
                        valueFormat="YYYY-MM"
                        :disabledDate="disabledDate"
                        @change="titleChange"
                    />
                </FormItem>
                <FormItem label="账单类型" name="billType">
                    <Select
                        :disabled="billType != 2"
                        v-model:value="formData.billType"
                        :options="dynamicBillTypeList"
                        placeholder="账单类型"
                        @change="titleChange"
                    />
                </FormItem>
                <FormItem label="标题" name="title">
                    <Input v-model:value="formData.title" placeholder="标题" />
                </FormItem>
            </Form>
        </template>
        <template v-else>
            <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
            <BasicTable
                ref="tableRef"
                :tableDataList="tableData"
                :columns="columns"
                size="small"
                :sorter="false"
                :rowSelectionShow="false"
                useIndex
            >
                <template #operation="{ record }">
                    <Checkbox v-model:checked="record.checkOn" />
                </template>
            </BasicTable>
        </template>
        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button :loading="confirmLoading" type="primary" @click="nextStep" v-if="step == 1">下一步</Button>
            <Button :loading="confirmLoading" type="primary" @click="createBill(formData, true)" v-if="step == 2"
                >批量入账</Button
            >
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { computed, defineComponent, h, ref, toRefs, watch } from 'vue'
import { message, Modal, MonthPicker } from 'ant-design-vue'
import { billTypeList } from '/@/utils/dictionaries'
import request from '/@/utils/request'
import { getSpecialCustomer } from '/@/utils/api'
import moment from 'moment'

export default defineComponent({
    name: 'CreateModal',
    components: { MonthPicker },
    props: {
        visible: Boolean,
        billType: {
            type: Number,
            default: 0,
            validator: (val: number) => {
                return [0, 1, 2].includes(val)
            },
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { billType, visible } = toRefs(props)
        console.log(billType.value)
        const createTitle = ref('')
        const formInline = ref()
        const formData = ref<any>({
            clientId: undefined,
            clientName: undefined,
            billType: undefined,
            paymentDate: undefined,
            title: undefined,
        })
        const billData = ref<Recordable>({ id: undefined, clientId: undefined, paymentDate: undefined })

        const resetData = () => {
            formData.value = {
                clientId: undefined,
                clientName: undefined,
                billType: undefined,
                paymentDate: undefined,
                title: undefined,
            }
            billData.value = {}
            params.value = {
                name: undefined,
                certificateNum: undefined,
            }
            tableData.value = []
            totalTableData.value = []
            if (step.value == 1) {
                formInline.value.resetFields()
            }
        }

        const params = ref({
            name: undefined,
            certificateNum: undefined,
        })
        const tableRef = ref()
        const searchData = (data) => {
            let filterOdds
            if (!data.name && !data.certificateNum) {
                tableData.value = totalTableData.value
                return
            } else if (!data.name && data.certificateNum) {
                filterOdds = (el) => {
                    return el.certificateNum.includes(data.certificateNum)
                }
            } else if (data.name && !data.certificateNum) {
                filterOdds = (el) => {
                    return el.name.includes(data.name)
                }
            } else {
                filterOdds = (el) => {
                    return el.name.includes(data.name) && el.certificateNum.includes(data.certificateNum)
                }
            }
            tableData.value = totalTableData.value
                .filter((el) => {
                    return filterOdds(el)
                })
                .sort((a: any, b: any) => {
                    return b.checkOn - a.checkOn
                })
        }
        const tableData = ref([])
        const totalTableData = ref([])

        const columns = ref([
            {
                title: '员工编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 130,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '员工类型',
                dataIndex: 'personnelTypeLabel',
                align: 'center',
                width: 90,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatusLabel',
                align: 'center',
                width: 90,
            },
            {
                title: '参保状态',
                dataIndex: 'izInsured',
                align: 'center',
                width: 90,
                customRender: ({ text }) => {
                    const arr = ['未参保', '已参保', '停保']
                    return arr[text]
                },
            },
            {
                title: '原因',
                dataIndex: 'reason',
                align: 'center',
                width: 170,
            },
            {
                title: '确认入账',
                dataIndex: 'checkOn',
                align: 'center',
                width: 80,
                slots: { customRender: 'operation' },
            },
        ])
        const searchOptions = ref([
            {
                label: '姓名',
                key: 'name',
            },
            {
                label: '身份证号',
                key: 'certificateNum',
            },
        ])

        const modalClose = () => {
            emit('cancel')
            resetData()
        }

        const createBill = async (data, accountFlag = false) => {
            if (formData.value.billType === 0 || formData.value.billType === 2 || formData.value.billType === 3) {
                // 创建薪酬账单
                const res = await request.post(`/api/hr-bills/createBasicBill`, {
                    ...formData.value,
                    usedClientIdList: formData.value.billType === 3 ? [formData.value.clientId] : data.usedClientIdList,
                })
                billData.value = {
                    ...res,
                    staffIds: accountFlag
                        ? totalTableData.value.filter((el: any) => el.checkOn).map((el: any) => el.id)
                        : undefined,
                }
                emit('confirm', {
                    ...res,
                    staffIds: accountFlag
                        ? totalTableData.value.filter((el: any) => el.checkOn).map((el: any) => el.id)
                        : undefined,
                })
            } else {
                // 创建保障账单
                const res = await request.post(`/api/hr-bills`, {
                    ...formData.value,
                    usedClientIdList: data.usedClientIdList,
                    staffIds: accountFlag
                        ? totalTableData.value.filter((el: any) => el.checkOn).map((el: any) => el.id)
                        : undefined,
                })
                billData.value = res
                emit('confirm', res)
            }
            resetData()
        }
        const fetchUnrecordedDetails = async (data) => {
            const res = await request.post('/api/hr-bills/unrecorded-details', {
                staffMap: data.staffMap,
                usedClientIdList: data.usedClientIdList,
            })
            formData.value = { ...formData.value, usedClientIdList: data.usedClientIdList }
            step.value = 2
            createTitle.value = '未入账员工明细'
            totalTableData.value = res
                .map((el) => {
                    return { ...el, checkOn: !!el.checkOn }
                })
                .sort((a, b) => {
                    return b.checkOn - a.checkOn
                })
            tableData.value = totalTableData.value
        }

        const confirmLoading = ref(false)
        const nextStep = async () => {
            try {
                confirmLoading.value = true
                await formInline.value.validate()
                if (formData.value.billType === 3) {
                    await createBill(formData.value)
                } else {
                    const res = await request.post(`/api/hr-bills/check`, formData.value)

                    if (res.checkCode == 500 && Object.keys(res.staffMap).length) {
                        // 有不能参与账单核算的员工
                        Modal.confirm({
                            title: '提示',
                            width: 550,
                            content: h(
                                'div',
                                formData.value.billType === 0
                                    ? [
                                          h('div', res.checkInCalculationMsg),
                                          h('br'),
                                          h('div', res.checkNotInCalculationMsg),
                                          h('br'),
                                          h('div', res.checkUsedClientMsg),
                                          h('br'),
                                          h('p', '点击【继续】补充入账人员'),
                                      ]
                                    : [
                                          h('div', res.checkInCalculationMsg),
                                          h('br'),
                                          h('div', res.checkNotInCalculationMsg),
                                          h('br'),
                                          h('p', '点击【继续】补充入账人员'),
                                      ],
                            ),
                            okText: '继续',
                            onOk: async () => {
                                await fetchUnrecordedDetails(res)
                            },
                            onCancel: () => {},
                        })
                    } else if (res.checkCode == 200 || (res.checkCode == 500 && !Object.keys(res.staffMap).length)) {
                        message.success('账单检查通过！')
                        await createBill(res)
                    } else {
                        message.warning('账单检查错误！')
                    }
                }
            } finally {
                confirmLoading.value = false
            }
        }

        const disabledDate = (currentDate) => {
            return moment(currentDate) > moment().add(2, 'month')
        }

        const labelChange = (label, value, extra) => {
            let pr = extra.triggerNode.dataRef
            formData.value.clientName = label?.join('') || ''
            if (billType.value === 0) {
                specialCustomer.forEach((el) => {
                    if (pr.level === 1 && pr.id === el.key && el.name == '政法委') {
                        message.info('该客户下员工薪酬计算方式不同，请选择二级客户进行计算。', 1.5)
                    }
                })
            }
            titleChange()
        }
        const titleChange = () => {
            formData.value.title = ((formData.value.clientName || '') +
                (formData.value.paymentDate ? moment(formData.value.paymentDate).format('MM月份') : '') +
                (formData.value.billType === 0
                    ? '薪酬账单'
                    : formData.value.billType === 1
                    ? '保障账单'
                    : formData.value.billType === 2
                    ? '其他账单'
                    : formData.value.billType === 3
                    ? '中石化费用统计账单'
                    : '')) as any
        }
        let specialCustomer: any = []
        // 获取特殊客户名单
        const getSpecialClientList = async () => {
            specialCustomer = []
            specialCustomer = await getSpecialCustomer()
        }
        const step = ref(1)
        watch(
            visible,
            (val) => {
                if (val) {
                    const arr = ['薪酬账单', '保障账单', '其他账单']
                    createTitle.value = `新增${arr[billType.value]}`
                    formData.value.billType = billType.value
                    step.value = 1
                }
                if (visible.value && billType.value === 0) {
                    getSpecialClientList()
                }
            },
            {
                immediate: true,
            },
        )

        const dynamicBillTypeList = computed(() => {
            if (billType.value < 2) return billTypeList
            else return billTypeList.filter((el) => el.value >= 2)
        })

        return {
            step,
            columns,
            tableRef,
            params,
            tableData,
            searchOptions,
            dynamicBillTypeList,
            createTitle,
            createBill,
            searchData,
            titleChange,
            labelChange,
            billData,
            disabledDate,
            formInline,
            nextStep,
            formData,
            billTypeList,
            modalClose,
            confirmLoading,
            rules: {
                clientId: { required: true, message: '请选择客户', trigger: 'change' },
                billType: { required: true, type: 'number', message: '请选择账单类型', trigger: 'change' },
                paymentDate: { required: true, message: '请选择费用年月', trigger: 'change' },
                title: { required: true, message: '请输入标题', trigger: 'change' },
            },
        }
    },
})
</script>

<style scoped lang="less"></style>
