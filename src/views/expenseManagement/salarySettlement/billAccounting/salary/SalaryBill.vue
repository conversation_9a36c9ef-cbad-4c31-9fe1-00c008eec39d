<template>
    <BasicEditModalSlot :visible="visible" title="薪酬账单" @cancel="modalClose" width="1400px">
        <Spin :spinning="loading">
            <div class="cell">
                <div class="title">薪酬账单信息</div>
                <div class="main">
                    <div class="row">
                        <template v-if="createResponse?.billIdList && createResponse?.billIdList.length">
                            <div class="col">
                                <div class="label">客户</div>
                                <div class="val">
                                    <Tree
                                        :selectable="false"
                                        :replaceFields="{ children: 'children', title: 'clientName', key: 'id' }"
                                        :tree-data="treeData"
                                    />
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="col">
                                <div class="label">客户编号</div>
                                <div class="val">{{ formData.unitNumber }}</div>
                            </div>
                            <div class="col">
                                <div class="label">客户名称</div>
                                <div class="val">{{ formData.clientName }}</div>
                            </div>
                            <div class="col" v-if="formData.parentClientName">
                                <div class="label">所属公司</div>
                                <div class="val">{{ formData.parentClientName }}</div>
                            </div>
                        </template>
                        <div class="col">
                            <div class="label">费用年月</div>
                            <div class="val">{{ formData.paymentDate }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col" style="width: 100%">
                            <div class="label">标题</div>
                            <div class="val">
                                <span v-if="formData.billState === 1"> {{ formData.title }} </span>
                                <Input v-else v-model:value="formData.title" placeholder="标题" style="width: 300px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Row>
                <Col span="12">
                    <div class="cell">
                        <div class="title">
                            原始账单明细
                            <SelectOutlined v-if="canEdit" class="icon" @click="uploadNewOrigin" title="更换薪酬原单" />
                            <DownloadOutlined class="icon" @click="exportOrigin" title="下载原单" />
                        </div>
                        <div class="main">
                            <a @click="previewFile(formData.originBillUrl)"> {{ formData.originBillName }} </a>
                        </div>
                    </div>
                </Col>
                <Col span="12" v-if="unInsertList.length">
                    <div class="cell">
                        <div class="title" style="border-color: red">
                            未入账员工明细
                            <DownloadOutlined class="icon" @click="exportUninsert" />
                        </div>
                        <div class="main">
                            <a @click="exportUninsert">{{ formData.title }}未入账员工明细</a>
                        </div>
                    </div>
                </Col>
            </Row>
            <div style="display: flex">
                <div class="cell" v-if="formData.billType === 0">
                    <div class="title">计税设置</div>
                    <div class="main">
                        <div class="row">
                            <div class="col" style="width: 100%">
                                <div class="label">计税方式</div>
                                <div class="val">
                                    <Select
                                        style="width: 300px"
                                        :disabled="formData.billState === 1"
                                        v-model:value="taxCalculationMethod"
                                        placeholder="计税方式"
                                        :options="taxCalculationMethodList"
                                        @change="taxCalculationMethodChange"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cell" v-if="formData.specialFlag == 3">
                    <!--  -->
                    <div class="title">账单用途</div>
                    <div class="main">
                        <div class="row">
                            <div class="col" style="width: 100%">
                                <div class="label">账单用途</div>
                                <div class="val">
                                    <Select
                                        style="width: 300px"
                                        v-model:value="billingPurposesMethod"
                                        placeholder="账单用途"
                                        :options="billingPurposes"
                                        :disabled="formData.billState !== 0"
                                        @change="billingPurposesChange"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cell">
                <div class="title">
                    薪酬账单明细
                    <UsergroupAddOutlined v-if="canEdit" class="icon" @click="insertStaff" />
                    <UsergroupDeleteOutlined v-if="canEdit" class="icon" @click="removeStaff" />
                    <DownloadOutlined class="icon" @click="exportBill" />
                    <!-- <TransactionOutlined class="icon" @click="exportBankInsert" /> -->
                    <div style="margin-left: auto">
                        <CalculatorOutlined v-if="canEdit" class="icon" @click="calcPersonalTax" title="生成个税" />
                        <!-- <Tooltip placement="top" title="统一修改补差"> -->
                        <EditOutlined
                            @click="compensationVisible = true"
                            style="float: right; font-size: 20px"
                            title="统一填写补差"
                        />
                        <!-- </Tooltip> -->
                    </div>
                </div>

                <div class="main">
                    <div class="search-wrapper">
                        <Input
                            v-model:value="search.name"
                            placeholder="姓名"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                        <Input
                            v-model:value="search.certificateNum"
                            placeholder="身份证号"
                            @blur="filterTableData"
                            @pressEnter="filterTableData"
                            @paste="filterTableData"
                        />
                    </div>
                    <template v-if="tableData.length">
                        <Button
                            v-if="selectedStaff.length == tableData.length"
                            size="small"
                            type="primary"
                            @click="selAll(false)"
                        >
                            取消全选
                        </Button>
                        <Button v-else size="small" type="primary" @click="selAll(true)"> 选择全部 </Button>
                    </template>

                    <Table
                        v-if="showTable"
                        :bordered="true"
                        :columns="taxCalculationMethod == 1 ? bonusColumns : columns"
                        :dataSource="[
                            ...tableData.slice(
                                pagination.pageSize * (pagination.current - 1),
                                pagination.pageSize * pagination.current,
                            ),
                        ]"
                        rowKey="id"
                        size="small"
                        :pagination="pagination"
                        :scroll="{ x: 100 }"
                        :rowSelection="{
                            selectedRowKeys: selectedStaff.map((i) => i.id),
                            onChange: (keys, rows) => selStaff(rows),
                        }"
                    >
                        <template #name="{ record }">
                            <Tooltip
                                placement="topRight"
                                arrowPointAtCenter
                                :title="
                                    bonusSubstandard[record.staffId] ? '该员工存在按一次性奖金计税的账单,请删除后再保存！' : ''
                                "
                            >
                                <span :style="bonusSubstandard[record.staffId] ? 'color:red' : ''">{{ record.name }}</span>
                            </Tooltip>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="cell">
                <div class="title">薪酬汇总账单 <DownloadOutlined class="icon" @click="exportTotalBill" /></div>
                <div class="main">
                    <BasicTable
                        :tableDataList="[
                            {
                                id: formData.hrBillTotal?.id ?? 1,
                                lastMonthMakeUp: formData.hrBillTotal.lastMonthMakeUp ?? 0,
                                serviceFeeTotal: formData.hrBillTotal.serviceFeeTotal ?? 0,
                                ...totalData,
                            },
                        ]"
                        :columns="totalColumns"
                        size="small"
                        :sorter="false"
                        :rowSelectionShow="false"
                    >
                        <template #lastMonthMakeUp="{ text }">
                            <span v-if="formData.billState === 1"> {{ text }} </span>
                            <InputNumber v-else :value="text" @change="lastMonthMakeUpChange" />
                        </template>
                        <template #serviceFeeTotal="{ text }">
                            <span v-if="formData.billState === 1"> {{ text }} </span>
                            <InputNumber v-else :value="text" @change="serviceFeeTotalChange" />
                        </template>
                    </BasicTable>
                </div>
            </div>
        </Spin>
        <template #footer>
            <template v-if="canEdit">
                <Button @click="modalClose">取消</Button>
                <Button type="primary" @click="reloadCalculation">重新计算</Button>
                <Button @click="modalConfirm" :loading="confirmLoading" :disabled="modalConfirmDisabled" type="primary"
                    >保存</Button
                >
            </template>
            <span v-else></span>
        </template>
    </BasicEditModalSlot>
    <!-- 新增入账人员 -->
    <BasicEditModalSlot
        title="新增入账人员"
        width="1200px"
        :visible="showInsertStaff"
        @cancel="insertClose"
        okText="批量入账"
        @ok="insertConfirm"
        centered
    >
        <BasicTable
            ref="insertTableRef"
            :tableDataList="unInsertList"
            :columns="unInsertCols"
            :sorter="false"
            @selectedRowsArr="selStaff"
        />
    </BasicEditModalSlot>
    <!-- 一键修改补差 -->
    <BasicEditModalSlot
        v-model:visible="compensationVisible"
        @cancel="compensationCancel"
        title="统一修改补差"
        width="500px"
        centered
    >
        <Form :model="compensationFormData" ref="compensationForm" style="width: 100%">
            <template v-for="(itemForm, i) in compensationOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="compensationFormData[itemForm.name]" />
            </template>
        </Form>
        <template #footer>
            <Button @click="compensationCancel">取消</Button>
            <Button type="primary" @click="compensationConfirm">确认</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed, h } from 'vue'
import {
    SelectOutlined,
    DownloadOutlined,
    UsergroupAddOutlined,
    UsergroupDeleteOutlined,
    // TransactionOutlined,
    CalculatorOutlined,
    EditOutlined,
} from '@ant-design/icons-vue'
import { InputNumber, message, Spin } from 'ant-design-vue'
import request from '/@/utils/request'
import { divide, minus, plus, round } from 'number-precision'
import downFile, { exportTable } from '/@/utils/downFile'
import { calcAllSalary, calcSalary, formatDynamicCols } from '../util'
import { ArrToTree, isEmpty, previewFile, getValuesAndRules } from '/@/utils/index'
import { TreeDataItem } from 'ant-design-vue/lib/tree/Tree'
import { valuesAndRules } from '/#/component'
import { getSpecialCustomer } from '/@/utils/api'

export default defineComponent({
    name: 'SalaryBill',
    components: {
        Spin,
        SelectOutlined,
        DownloadOutlined,
        UsergroupAddOutlined,
        UsergroupDeleteOutlined,
        // TransactionOutlined,
        CalculatorOutlined,
        EditOutlined,
    },
    props: {
        visible: Boolean,
        billId: String,
        createResponse: Object,
    },
    emits: ['update:visible', 'confirm', 'changeOrigin'],
    setup(props, { emit }) {
        const { visible, billId, createResponse } = toRefs(props)
        watch(visible, async () => {
            visible.value && (await getSpecial())
            visible.value && billId.value && (await getDetail())
        })
        const specialType = ref<any>()
        // 获取特殊客户列表  一定放在getDetail内部，表头组成之前调用
        const getSpecial = async () => {
            await getSpecialCustomer().then((res: any) => {
                let idx = res?.findIndex((el) => el.key === createResponse.value?.optClientId && el.name == '东区公安')
                specialType.value = idx
            })
        }
        const formData = ref<Recordable>({
            hrBillTotal: {
                lastMonthMakeUp: 0,
                serviceFeeTotal: 0,
                total: 0,
            },
        })
        const modalConfirmDisabled = ref(false)
        const taxCalculationMethod = ref(0) //计税方式
        const billingPurposesMethod = ref(1)
        const stable = ref()
        const isUpdated = ref(false)
        const tableData = ref<Recordable[]>([])
        const resTableData = ref<Recordable[]>([])
        const bonusColumns = ref<Recordable[]>([])
        const columns = ref<Recordable[]>([])
        const totalColumns = ref<Recordable[]>([])
        const unInsertList = ref<Recordable[]>([])
        const treeData = ref<TreeDataItem[]>([])
        const totalData = computed(() => {
            return {
                staffNum: resTableData.value.length,
                unitPensionTotal: resTableData.value.map((i) => i.unitPension).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitUnemploymentTotal: resTableData.value
                    .map((i) => i.unitUnemployment)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitMedicalTotal: resTableData.value.map((i) => i.unitMedical).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                workInjuryTotal: resTableData.value.map((i) => i.workInjury).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitSocialSecurityMakeUpTotal: resTableData.value
                    .map((i) => i.unitSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitSubtotal: resTableData.value.map((i) => i.unitSubtotal).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalPensionTotal: resTableData.value
                    .map((i) => i.personalPension)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalUnemploymentTotal: resTableData.value
                    .map((i) => i.personalUnemployment)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalMedicalTotal: resTableData.value
                    .map((i) => i.personalMedical)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalSocialSecurityMakeUpTotal: resTableData.value
                    .map((i) => i.personalSocialSecurityMakeUp)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalSubtotal: resTableData.value
                    .map((i) => i.personalSubtotal)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                socialSecurityTotal: resTableData.value
                    .map((i) => i.socialSecurityTotal)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                accumulationFundCardinal: resTableData.value
                    .map((i) => i.accumulationFundCardinal)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitAccumulationFundTotal: resTableData.value
                    .map((i) => i.unitAccumulationFund)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                unitAccumulationFundMakeUpTotal: resTableData.value
                    .map((i) => i.unitAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalAccumulationFundTotal: resTableData.value
                    .map((i) => i.personalAccumulationFund)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalAccumulationFundMakeUpTotal: resTableData.value
                    .map((i) => i.personalAccumulationFundMakeUp)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                accumulationFundTotal: resTableData.value
                    .map((i) => i.accumulationFundTotal)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalTaxTotal: resTableData.value.map((i) => i.personalTax).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                personalTaxMakeUpTotal: resTableData.value
                    .map((i) => i.personalTaxMakeUp)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                realSalaryTotal: resTableData.value.map((i) => i.realSalary).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                otherFeeTotal: resTableData.value
                    .map((i) => i.otherFeeTotal ?? 0)
                    .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                salaryTotal: resTableData.value.map((i) => i.salary ?? 0).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
                total: resTableData.value.map((i) => i.total ?? 0).reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0),
            }
        })
        console.log('totalData', totalData.value)
        const pagination = ref({
            current: 1,
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条`,
            total: 0,
            onChange: (current) => {
                pagination.value.current = current
            },
        })

        const canEdit = computed(() => formData.value.billState !== 1)
        const loading = ref(false)
        const showTable = ref(false)
        const getDetail = async () => {
            let res
            try {
                loading.value = true
                if (createResponse.value?.billIdList) {
                    res = await request.post(`/api/hr-bills/search-batch`, createResponse.value?.billIdList)
                    treeData.value = ArrToTree(res.hrClientDTOList, { id: 'id', pid: 'parentId' })
                    // res = createResponse.value
                } else res = await request.get(`/api/hr-bills/${billId.value}`)
                console.log(res)
            } catch (err) {
                modalClose()
            } finally {
                setTimeout(() => {
                    loading.value = false
                }, 300)
            }
            formData.value = {
                ...res,
                hrBillTotal: res?.hrBillTotal || {
                    lastMonthMakeUp: 0,
                    serviceFeeTotal: 0,
                    total: 0,
                },
            }
            taxCalculationMethod.value = formData.value?.billDetailList[0]?.taxCalculationMethod || 0
            resTableData.value = JSON.parse(JSON.stringify(res.billDetailList))
            // console.log('resTableData==>', resTableData.value)
            tableData.value = resTableData.value
            pagination.value.total = tableData.value.length
            if (createResponse.value?.billIdList) await taxCalculationMethodChange(3)
            // calcAllSalary(tableData.value, res)
            columns.value = [
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                    slots: { customRender: 'name' },
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '项目名',
                    dataIndex: 'projectName',
                    width: 130,
                    align: 'center',
                },
                {
                    title: '应发工资',
                    dataIndex: 'salary',
                    width: 200,
                    customRender: ({ record, text }) => {
                        const add = record?.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType != '2' && i.expenseType != '3')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)

                        const sub = record?.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType == '2')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, Math.abs(cur) ?? 0), 0)
                        record.salary = round(minus(add ?? 0, sub ?? 0), 2)
                        return text ?? 0
                    },
                    align: 'center',
                },
                {
                    title: '税前应发',
                    dataIndex: 'preTaxSalary',
                    width: 100,
                    customRender: ({ record, text }) => {
                        record.preTaxSalary = minus(
                            record.salary ?? 0,
                            record.personalSubtotal ?? 0,
                            plus(record.personalAccumulationFund ?? 0, record.personalAccumulationFundMakeUp ?? 0),
                        )
                        return text ?? 0
                    },
                    align: 'center',
                },
                {
                    title: '个税',
                    dataIndex: 'personalTax',
                    width: 100,
                    align: 'center',
                    customRender: ({ text }) => {
                        return text ?? 0
                    },
                },
                {
                    title: '个税补差',
                    dataIndex: 'personalTaxMakeUp',
                    width: 100,
                    align: 'center',
                    customRender: ({ record, index, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text ?? 0,
                                  onChange: (e) => {
                                      record.personalTaxMakeUp = Number(e) || 0
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text || 0
                    },
                },
                {
                    title: '实发工资',
                    dataIndex: 'realSalary',
                    width: 100,
                    customRender: ({ record, text }) => {
                        console.log('crecord', record, text)
                        record.realSalary = round(
                            minus(record.preTaxSalary ?? 0, plus(record.personalTax ?? 0, record.personalTaxMakeUp ?? 0)),
                            2,
                        )
                        return text ?? 0
                    },
                    align: 'center',
                },
                {
                    title: '总金额',
                    dataIndex: 'total',
                    width: 100,
                    customRender: ({ record, text }) => {
                        record.otherFeeTotal =
                            record?.hrBillDetailItemsList
                                ?.filter((i) => i.expenseType == '3')
                                .map((i) => i.amount ?? 0)
                                .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0) ?? 0
                        record.total = minus(
                            record.salary ?? 0,
                            record.personalSubtotal ?? 0,
                            plus(record.personalAccumulationFund ?? 0, record.personalAccumulationFundMakeUp ?? 0),
                        )
                        return text ?? 0
                    },
                    align: 'center',
                },
            ]
            bonusColumns.value = [
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    fixed: 'left',
                    align: 'center',
                    slots: { customRender: 'name' },
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                    fixed: 'left',
                    align: 'center',
                },
                {
                    title: '项目名',
                    dataIndex: 'projectName',
                    width: 130,
                    align: 'center',
                },
                {
                    title: '个税',
                    dataIndex: 'personalTax',
                    width: 100,
                    align: 'center',
                    customRender: ({ text }) => {
                        return text ?? 0
                    },
                },
                {
                    title: '个税补差',
                    dataIndex: 'personalTaxMakeUp',
                    width: 100,
                    align: 'center',
                    customRender: ({ record, text }) => {
                        return canEdit.value
                            ? h(InputNumber, {
                                  value: text ?? 0,
                                  onChange: (e) => {
                                      record.personalTaxMakeUp = Number(e) || 0
                                      isUpdated.value = true
                                  },
                                  style: {
                                      width: '100%',
                                  },
                              })
                            : text ?? 0
                    },
                },
                {
                    title: '实发工资',
                    dataIndex: 'realSalary',
                    width: 100,
                    customRender: ({ record, text }) => {
                        record.realSalary = round(
                            minus(record.total ?? 0, plus(record.personalTax ?? 0, record.personalTaxMakeUp ?? 0)),
                            2,
                        )
                        return text
                    },
                    align: 'center',
                },
                {
                    title: '总金额',
                    dataIndex: 'total',
                    width: 100,
                    customRender: ({ record, text }) => {
                        const add = record?.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType != '2' && i.expenseType != '3')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)

                        const sub = record?.hrBillDetailItemsList
                            ?.filter((i) => i.expenseType == '2')
                            ?.map((j) => j.amount ?? 0)
                            ?.reduce((pre, cur) => plus(pre ?? 0, Math.abs(cur) ?? 0), 0)
                        record.total = round(minus(add ?? 0, sub ?? 0), 2) || 0
                        return text
                    },
                    align: 'center',
                },
            ]
            // 获取动态表头列
            const cols = await request.post(
                `/api/hr-bill-dynamic-fieldses/getTableDynamicHeader`,
                createResponse.value?.billIdList,
            )
            const dynamic = formatDynamicCols(
                cols,
                detailChange,
                formData.value.billState == 1,
                pagination,
                true,
                specialType.value,
            )
            // 暂时隐藏表头 服务费
            let newObj = dynamic.filter((el) => el.title == '服务费')
            const dynamicCols = dynamic.filter((el) => el.title != '服务费')
            columns.value = [...columns.value.slice(0, 3), ...dynamicCols, ...columns.value.slice(3)]
            bonusColumns.value = [...bonusColumns.value.slice(0, 3), ...dynamicCols, ...bonusColumns.value.slice(3)]
            if (formData.value.isShowProjectName !== 1) {
                columns.value = [...columns.value.filter((el) => el.dataIndex !== 'projectName')]
                bonusColumns.value = [...bonusColumns.value.filter((el) => el.dataIndex !== 'projectName')]
            }
            showTable.value = true

            setTimeout(async () => {
                stable.value?.scrollTo({ top: 1, left: 1 })
            }, 500)

            totalColumns.value = [
                {
                    title: '人数',
                    dataIndex: 'staffNum',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '代发工资',
                    align: 'center',
                    children: [
                        {
                            title: '个税',
                            dataIndex: 'personalTaxTotal',
                            width: 150,
                            align: 'center',
                        },
                        {
                            title: '个税补差',
                            dataIndex: 'personalTaxMakeUpTotal',
                            width: 100,
                            align: 'center',
                        },
                        {
                            title: '实发',
                            dataIndex: 'realSalaryTotal',
                            width: 150,
                            align: 'center',
                        },
                    ],
                },
                {
                    title: '其它费用',
                    dataIndex: 'otherFeeTotal',
                    width: 150,
                    align: 'center',
                },
                // {
                //     title: '服务费',
                //     dataIndex: 'serviceFeeTotal',
                //     width: 120,
                //     slots: { customRender: 'serviceFeeTotal' },
                //     align: 'center',
                // },
                {
                    title: '上月补差',
                    dataIndex: 'lastMonthMakeUp',
                    width: 120,
                    slots: { customRender: 'lastMonthMakeUp' },
                    align: 'center',
                },
                {
                    title: '总金额',
                    dataIndex: 'total',
                    width: 120,
                    align: 'center',
                    customRender: ({ record }) => {
                        console.log(record.lastMonthMakeUp)
                        console.log((totalData.value as any).total)
                        // resTableData.value = tableData.value
                        // const total = tableData.value?.map((j) => j.total ?? 0)?.reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)
                        formData.value.hrBillTotal.total = plus((totalData.value as any).total ?? 0, record.lastMonthMakeUp ?? 0)
                        // console.log(formData.value.hrBillTotal.total)
                        return formData.value.hrBillTotal.total
                    },
                },
            ]
            // 获取未入账员工
            let list
            if (createResponse.value?.billIdList)
                list = await request.post(`/api/hr-bill-details/list-batch`, createResponse.value?.billIdList)
            else list = await request.get(`/api/hr-bill-details/list?billId=${billId.value}`)
            unInsertList.value = list
        }

        const search = ref({
            name: '',
            certificateNum: '',
        })

        const filterTableData = () => {
            let filterOdds
            if (!search.value.name && !search.value.certificateNum) {
                tableData.value = resTableData.value
                pagination.value.current = 1
                pagination.value.total = tableData.value.length
                return
            } else if (!search.value.name && search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.certificateNum.includes(search.value.certificateNum)
                }
            } else if (search.value.name && !search.value.certificateNum) {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name)
                }
            } else {
                filterOdds = (el) => {
                    return el.name.includes(search.value.name) && el.certificateNum.includes(search.value.certificateNum)
                }
            }
            tableData.value = resTableData.value.filter((el) => {
                return filterOdds(el)
            })
            pagination.value.current = 1
            pagination.value.total = tableData.value.length
        }

        const serviceFeeTotalChange = (val) => {
            formData.value.hrBillTotal.serviceFeeTotal = round(Number(val ?? 0), 2)
            formData.value.hrBillTotal.isModifyServiceFee = true
            isUpdated.value = true
        }
        const lastMonthMakeUpChange = (e) => {
            if (formData.value.billState === 1) {
                message.warn('锁定状态账单不可更改！')
                return
            }
            const oldVal = formData.value.lastMonthMakeUp
            formData.value.hrBillTotal.lastMonthMakeUp = e
            formData.value.hrBillTotal.total = plus(Number(e), minus(formData.value.hrBillTotal.total, oldVal || 0))
            isUpdated.value = true
        }

        const resetData = () => {
            formData.value = {
                hrBillTotal: {
                    lastMonthMakeUp: 0,
                    serviceFeeTotal: 0,
                    total: 0,
                },
            }
            search.value = {
                name: '',
                certificateNum: '',
            }
            resTableData.value = []
            tableData.value = []
            pagination.value.total = 0
            pagination.value.current = 1
            isUpdated.value = false
            selectedStaff.value = []
            showTable.value = false
            totalColumns.value = []
            columns.value = []
        }
        const modalClose = () => {
            resetData()
            emit('update:visible', false)
        }
        // 重新计算按钮函数
        const reloadCalculation = async () => {
            console.log(resTableData.value)
            let obj = {
                id: createResponse.value?.id,
                billPurpose: createResponse.value?.billPurpose || null,
                billDetailList: resTableData.value,
                paymentDate: createResponse.value?.paymentDate,
                billType: createResponse.value?.billType,
            }
            let res = await request.post('/api/hr-bill/recalculation', obj)
            resTableData.value = JSON.parse(JSON.stringify(res))
            tableData.value = resTableData.value
            pagination.value.current = 1
            search.value.certificateNum = ''
            search.value.name = ''
        }

        const confirmLoading = ref(false)
        const modalConfirm = async () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()
            try {
                confirmLoading.value = true
                await calcPersonalTax()
                const originList = formData.value.billDetailList
                const originIds = originList.map((i) => i.id)
                const currentIds = tableData.value.map((i) => beishanchude.value.includes(i.id))
                const deleteList = originList
                    .filter((i) => beishanchude.value.includes(i.id))
                    .map((i) => ({
                        ...i,
                        updateState: 2,
                    }))
                beishanchude.value = []
                const dataList = [...tableData.value]
                dataList.forEach((i) => {
                    if (!originIds.includes(i.id)) {
                        i.updateState = 1
                    } else {
                        i.updateState = 3
                    }
                })
                console.log(dataList)
                await request.put(`/api/hr-bills`, {
                    id: formData.value.id,
                    title: formData.value.title,
                    billType: 0,
                    billDetailList: [...tableData.value, ...deleteList],
                    billIdList:
                        createResponse.value && createResponse.value?.billIdList?.length
                            ? createResponse.value.billIdList
                            : undefined,
                    clientIdList:
                        createResponse.value && createResponse.value?.billIdList?.length
                            ? createResponse.value.clientIdList
                            : undefined,
                    hrBillTotal: {
                        id: formData.value.hrBillTotal.id,
                        billId: formData.value.hrBillTotal?.billId,
                        serviceFeeTotal: formData.value.hrBillTotal.serviceFeeTotal ?? 0,
                        lastMonthMakeUp: formData.value.hrBillTotal.lastMonthMakeUp ?? 0,
                        ...totalData.value,
                        total: formData.value.hrBillTotal.total ?? 0,
                        isModifyServiceFee: formData.value.hrBillTotal?.isModifyServiceFee || false,
                    },
                    taxCalculationMethod: taxCalculationMethod.value,
                })
                resetData()
                emit('confirm')
            } finally {
                confirmLoading.value = false
            }
        }
        const uploadNewOrigin = () => {
            modalClose()
            emit('changeOrigin')
        }

        const detailChange = (val, idx, key, isDynamic = false, type = 'number') => {
            console.log(key)
            if (formData.value.billState === 1) {
                message.warn('锁定状态不可修改！')
                return
            }
            if (isDynamic) {
                const index = tableData.value[idx].hrBillDetailItemsList?.findIndex((i) => i.expenseName == key)
                if (index != -1) {
                    tableData.value[idx].hrBillDetailItemsList[index].amount = round(Number(val), 2)
                }
            } else {
                tableData.value[idx][key] = type == 'number' ? round(Number(val), 2) : val?.target?.value
                if (key == 'serviceFee') {
                    formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                        .map((i) => i.serviceFee ?? 0)
                        .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)
                    formData.value.hrBillTotal.isModifyServiceFee = false
                }
            }

            tableData.value[idx].updateState = 3 // 更新状态为 更新
            tableData.value[idx] = calcSalary(tableData.value[idx], formData.value)
            tableData.value = [...tableData.value]
            pagination.value.total = tableData.value.length
            isUpdated.value = true
        }

        const exportOrigin = () => {
            downFile('get', formData.value.originBillUrl, formData.value.originBillName)
        }
        const exportUninsert = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单明细，请保存后重新打开再导出未入账员工明细！',
                    duration: 5,
                })
                return
            }
            if (createResponse.value?.billIdList)
                downFile('post', `/api/hr-bill-details/export-batch`, '', createResponse.value?.billIdList)
            else downFile('get', `/api/hr-bill-details/export?billId=${billId.value}`, `${formData.value.title}未入账明细.xlsx`)
        }
        const exportBankInsert = () => {
            if (!selectedStaff.value.length) {
                message.warning('请选择账单明细!')
                return
            }
            downFile('post', `/api/hr-bill-details/export-bank-statement`, `${formData.value.title}银行报账单.xlsx`, {
                billId: billId.value,
                billDetailIds: selectedStaff.value.map((i) => i.id),
            })
            // request
            //     .post(
            //         '/api/hr-bill-details/export-bank-statement',
            //         {
            //             billId: billId.value,
            //             billDetailIds: selectedStaff.value.map((i) => i.id),
            //         },
            //         { loading: false },
            //     )
            //     .then((ref) => {
            //         downFile('get', ref, `${formData.value.title}银行报账单.xlsx`)
            //     })
        }
        const exportBill = async () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            if (!selectedStaff.value.length) {
                message.warning('请选择账单明细!')
                return
            }
            downFile('post', `/api/hr-bills-detail/download-bill-detail`, `${formData.value.title}薪酬账单明细`, {
                billId: billId.value,
                billIdList:
                    createResponse.value && createResponse.value?.billIdList ? createResponse.value.billIdList : undefined,
                billDetailIds: selectedStaff.value.map((i) => i.id),
            })
        }
        const exportTotalBill = () => {
            if (isUpdated.value) {
                message.warn({
                    content: '您已修改该账单，请保存后重新打开再导出账单！',
                    duration: 5,
                })
                return
            }
            exportTable(
                totalColumns.value,
                [
                    {
                        id: formData.value.hrBillTotal.id ?? 0,
                        serviceFeeTotal: formData.value.hrBillTotal.serviceFeeTotal ?? 0,
                        lastMonthMakeUp: formData.value.hrBillTotal.lastMonthMakeUp ?? 0,
                        total: formData.value.hrBillTotal.total ?? 0,
                        ...totalData.value,
                    },
                ],
                `${formData.value.title}薪酬账单汇总`,
                {
                    rowIndex: 3,
                    exceptCols: ['A'],
                    widthCols: [{ wch: 10 }, ...Array(6).fill({ wch: 25 })],
                },
            )
        }

        /**
         * 计算个税
         */
        const calcPersonalTax = async () => {
            const body = tableData.value.map((i) => {
                return {
                    id: i.id,
                    clientId: i.clientId,
                    payYear: formData.value.payYear,
                    payMonthly: formData.value.payMonthly,
                    staffId: i.staffId,
                    salary: i.salary,
                    personalSubtotal: i.personalSubtotal,
                    personalTaxMakeUp: i.personalTaxMakeUp,
                    personalAccumulationFund: i.personalAccumulationFund ?? 0,
                    personalAccumulationFundMakeUp: i.personalAccumulationFundMakeUp ?? 0,
                    hrBillDetailItemsList: i.hrBillDetailItemsList,
                    total: i.total,
                    certificateNum: i.certificateNum,
                }
            })
            let calcRes
            if (taxCalculationMethod.value == 0) {
                calcRes = (await request.post(`/api/hr-bill-detail-itemses/calculate-personal-tax`, body)) as any
            } else {
                calcRes = (await request.post(`/api/hr-bill-detail-itemses/recalculate-bonus-tax`, body)) as any
            }
            calcRes.forEach((i) => {
                const idx = tableData.value.findIndex((v) => i.id == v.id)
                if (idx != -1 && !isEmpty(i.personalTax)) {
                    tableData.value[idx].personalTax = round(i.personalTax ?? 0, 2)
                    tableData.value[idx].realSalary = round(
                        minus(tableData.value[idx]?.preTaxSalary ?? 0, plus(i.personalTax ?? 0, i.personalTaxMakeUp ?? 0)),
                        2,
                    )
                }
            })
        }

        const insertTableRef = ref()
        const tableRef = ref()
        const showInsertStaff = ref(false)
        const selectedStaff = ref<Recordable[]>([])
        const beishanchude = ref<any>([])
        const insertStaff = () => {
            showInsertStaff.value = true
        }
        const calcCurrentPage = (total) => {
            return Math.ceil(divide(total, 10))
        }
        const removeStaff = () => {
            search.value.certificateNum = ''
            search.value.name = ''
            filterTableData()

            if (!selectedStaff.value.length) {
                message.warning('请选择要删除入账的人员！')
                return
            }
            selectedStaff.value.forEach((i) => {
                i.selectedStaff = 2 // 更新状态为删除
            })

            unInsertList.value = [...unInsertList.value, ...selectedStaff.value]
            const ids = selectedStaff.value.map((i) => i.id)
            const changedList = tableData.value.filter((i) => !ids.includes(i.id))
            beishanchude.value.push(...selectedStaff.value.map((i) => i.id))
            resTableData.value = changedList
            filterTableData()
            pagination.value.current = calcCurrentPage(pagination.value.total)
            isUpdated.value = true
            selectedStaff.value = []
            tableRef.value?.checkboxReset()

            // 重新计算服务费汇总
            formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                .map((i) => i.serviceFee)
                .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)
            formData.value.hrBillTotal.isModifyServiceFee = false
            taxCalculationMethodChange(3)
        }
        const selStaff = (list) => {
            selectedStaff.value = list
        }
        const selAll = (isSel) => {
            selectedStaff.value = isSel ? [...tableData.value] : []
        }
        const insertConfirm = async () => {
            if (!selectedStaff.value.length) {
                message.warning('请选择要入账的人员！')
                return
            }
            const ids = selectedStaff.value.map((i) => i.id)
            // tableData 添加
            const res = await request.post(`/api/hr-bill-details/batchAdd`, {
                ids: ids,
                billIds: createResponse.value?.billIdList,
            })
            resTableData.value = [...resTableData.value, ...res]
            filterTableData()
            // calcAllSalary(tableData.value, formData.value)
            isUpdated.value = true
            // unInsertList 删除
            unInsertList.value = unInsertList.value.filter((i) => !ids.includes(i.id))
            insertClose()

            // 重新计算服务费汇总
            formData.value.hrBillTotal.serviceFeeTotal = tableData.value
                .map((i) => i.serviceFee)
                .reduce((pre, cur) => plus(pre ?? 0, cur ?? 0), 0)
            formData.value.hrBillTotal.isModifyServiceFee = false
            taxCalculationMethodChange(3)
        }
        const insertClose = () => {
            selectedStaff.value = []
            showInsertStaff.value = false
            insertTableRef.value.checkboxReset()
        }

        //计税方式
        const bonusSubstandard = ref<inObject>({})

        const taxCalculationMethodChange = (needReCalc = 1) => {
            if (!tableData.value?.length) return
            if (taxCalculationMethod.value == 1) {
                request
                    .post(`/api/hr-bills/annual-lump-sum-bonus-tax`, {
                        paymentDate: formData.value.paymentDate,
                        billDetailList: [...tableData.value],
                    })
                    .then((ref) => {
                        let newBonusSubstandard = {}
                        ref?.forEach((item) => {
                            newBonusSubstandard[item.staffId] = !!tableData.value.find((el) => {
                                return el.staffId == item.staffId
                            })
                        })
                        modalConfirmDisabled.value =
                            Object.keys(newBonusSubstandard)?.some((bo) => {
                                return bo
                            }) || false
                        bonusSubstandard.value = newBonusSubstandard
                    })
            } else {
                bonusSubstandard.value = {}
                modalConfirmDisabled.value = false
            }
            if (needReCalc === 1) {
                request
                    .post(`/api/hr-bills/modify-salary-bill`, {
                        taxCalculationMethod: taxCalculationMethod.value,
                        billDetailList: [...tableData.value],
                    })
                    .then((ref) => {
                        resTableData.value = ref
                        tableData.value = resTableData.value
                    })
            }
            if (needReCalc === 0) {
                request
                    .post(`/api/hr-bills/modify-salary-bill`, {
                        taxCalculationMethod: taxCalculationMethod.value,
                        billDetailList: [...tableData.value],
                    })
                    .then((ref) => {
                        resTableData.value = ref
                        tableData.value = resTableData.value
                    })
            }
        }
        const billingPurposesChange = async (val) => {
            let params = {
                id: billId.value,
                billPurpose: val,
                payYear: createResponse?.value?.payYear,
                payMonthly: createResponse?.value?.payMonthly,
                billDetailList: tableData.value,
                clientId: !formData.value.clientIdList ? createResponse?.value?.clientId : undefined,
                clientIdList: formData.value.clientIdList ? createResponse?.value?.clientIdList : undefined,
            }
            request.post(`/api/hr-bill/handle-social-governance-detail`, params).then((res) => {
                resTableData.value = res
                tableData.value = resTableData.value
            })
        }

        /**
         * @一键补差填入
         */
        const compensationOptions = ref<valuesAndRules[]>([
            {
                label: '个税补差',
                name: 'personalTaxMakeUp',
                // ruleType: 'number',
                type: 'number',
            },
        ])
        const { values: initFormData, rules } = getValuesAndRules(compensationOptions.value)
        const compensationForm = ref()
        const compensationFormData = ref<any>(initFormData)
        const compensationVisible = ref(false)

        const resetFormData = () => {
            compensationFormData.value = initFormData
            compensationForm.value?.resetFields()
        }
        watch(compensationVisible, () => {
            if (compensationVisible.value) {
            } else {
                resetFormData()
            }
        })
        const compensationConfirm = () => {
            resTableData.value?.forEach((item: any) => {
                for (const key in compensationFormData.value) {
                    item[key] = compensationFormData.value[key] ? compensationFormData.value[key] : 0
                }
            })
            tableData.value = resTableData.value
            compensationVisible.value = false
        }
        const compensationCancel = () => {
            compensationVisible.value = false
        }
        return {
            search,
            filterTableData,
            treeData,
            modalConfirmDisabled,
            pagination,
            canEdit,
            selAll,
            lastMonthMakeUpChange,
            selectedStaff,
            loading,
            confirmLoading,
            showTable,
            calcPersonalTax,
            insertTableRef,
            unInsertList,
            insertClose,
            showInsertStaff,
            totalColumns,
            serviceFeeTotalChange,
            tableRef,
            totalData,
            detailChange,
            exportBill,
            exportTotalBill,
            selStaff,
            insertConfirm,
            insertStaff,
            removeStaff,
            tableData,
            bonusColumns,
            columns,
            exportUninsert,
            exportBankInsert,
            exportOrigin,
            uploadNewOrigin,
            formData,
            previewFile,
            modalConfirm,
            modalClose,
            reloadCalculation,
            billingPurposesChange,
            unInsertCols: [
                {
                    title: '员工编号',
                    dataIndex: 'systemNum',
                    width: 160,
                    fixed: 'left',
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    fixed: 'left',
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    width: 200,
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 120,
                },
                {
                    title: '员工类型',
                    dataIndex: 'personnelTypeStr',
                    width: 120,
                },
                {
                    title: '员工状态',
                    dataIndex: 'staffStatusStr',
                    width: 120,
                },
                {
                    title: '参保状态',
                    dataIndex: 'izInsuredStr',
                    width: 120,
                },
                {
                    title: '未入账原因',
                    dataIndex: 'reason',
                    width: 200,
                    fixed: 'right',
                },
            ],
            taxCalculationMethod,
            taxCalculationMethodChange,
            bonusSubstandard,
            taxCalculationMethodList: [
                {
                    label: '并入综合所得税',
                    value: 0,
                },
                {
                    label: '按全年一次性奖金计税',
                    value: 1,
                },
            ],
            billingPurposes: [
                {
                    label: '开票申请',
                    value: 1,
                },
                {
                    label: '工资发放',
                    value: 2,
                },
            ],
            billingPurposesMethod,

            compensationConfirm,
            compensationCancel,
            compensationVisible,
            compensationOptions,
            compensationFormData,
            compensationForm,
            rules,
            specialType,
        }
    },
})
</script>

<style scoped lang="less">
.search-wrapper {
    display: flex;
    margin: 10px 0;
    & > input {
        width: 230px;
        margin-right: 20px;
    }
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon {
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }
    }
    .main {
        padding: 10px 0;
    }
}
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.row {
    margin: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    .col {
        margin-right: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .label {
            width: 80px;
            text-align: right;
            &:after {
                content: '：';
            }
        }
    }
}
</style>
