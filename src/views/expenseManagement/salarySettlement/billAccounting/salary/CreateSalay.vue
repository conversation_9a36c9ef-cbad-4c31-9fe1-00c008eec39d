<template>
    <BasicEditModalSlot
        :title="'编辑' + title"
        :visible="visible"
        @cancel="modalClose"
        okText="下一步"
        @ok="modalConfirm"
        centered
        width="1100px"
    >
        <div style="color: red; text-align: right">
            {{ `* 注：${billType == 3 ? '项目名称不能为空' : '身份证号为空的薪酬数据无法识别'}，将被忽略不计。` }}
        </div>
        <div class="modalMain">
            <div class="col">
                <div class="label" :style="{ width: title.length > 4 ? '150px' : '' }">{{ title }}</div>
                <div class="val">
                    <template v-if="formData.originId || formData.originName">
                        <div>
                            {{ formData.originName }}
                            <Button type="primary" ghost size="small" @click="resetData">更换{{ title }}</Button>
                        </div>
                    </template>
                    <template v-else>
                        <Select
                            v-model:value="formData.originId"
                            :placeholder="'请选择' + title"
                            :options="originList"
                            @change="selectOrigin"
                        />
                        <span style="margin: 0 10px">或</span>
                        <Upload :beforeUpload="beforeUpload" :showUploadList="false">
                            <Button type="primary" style="margin-left: 10px">
                                <template #icon>
                                    <UploadOutlined />
                                </template>
                                上传{{ title }}
                            </Button>
                        </Upload>
                    </template>
                </div>
            </div>
            <div class="col" style="margin: 10px 0">
                <div class="label" :style="{ width: title.length > 4 ? '150px' : '' }">数据开始行</div>
                <div class="val">
                    <InputNumber
                        v-model:value="formData.startRow"
                        placeholder="数据开始行"
                        :min="1"
                        @blur="changeStartRow"
                        style="width: 110px"
                    />
                </div>
            </div>
            <Table
                :dataSource="tableData"
                :columns="tableColumns"
                :bordered="true"
                :rowKey="(record) => record.id"
                size="small"
                :pagination="false"
                :scroll="{
                    x: '100',
                }"
                style="width: 100%"
            />
            <div class="cell" style="margin-top: 20px">
                <div class="title">表单映射</div>
                <div class="main">
                    <div class="table">
                        <div class="headerCols">
                            <div class="th" v-for="i in resColumns" :key="i.title" :style="{ width: i.cols * 100 + 'px' }">
                                {{ i.title }}
                            </div>
                        </div>
                        <div class="bodyCols">
                            <div class="tr" v-for="(j, idx) in resData" :key="idx">
                                <div class="tb" v-for="(v, index) in j" :key="v">
                                    <a v-if="idx == 1 && v.value != '表单字段' && billType != 3" @click="removeField(v, index)">
                                        {{ v.value }}
                                    </a>
                                    <span v-else> {{ v.value }} </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
    <!-- 提示 -->
    <BasicEditModalSlot :visible="showTip" title="提示" @cancel="tipClose" width="1000px" centered>
        <div class="tip">
            <ExclamationCircleFilled style="font-size: 18px" />
            有 {{ unuseCols.length }} 列未参与账单计算，是否继续？
        </div>
        <BasicTable
            :tableDataList="unuseColsList"
            :columns="[
                ...unuseCols.map((i, idx) => ({
                    title: idx === 0 ? '未参与账单列' : '',
                    dataIndex: idx,
                    colSpan: idx === 0 ? unuseCols.length : 0,
                    width: 120,
                })),
            ]"
            :rowSelectionShow="false"
            :sorter="false"
        />
        <template #footer>
            <Button @click="tipClose">取消</Button>
            <Button :loading="tipLoading" type="primary" @click="tipConfirm">确认</Button>
        </template>
    </BasicEditModalSlot>
    <!-- 设置费用项 -->
    <BasicEditModalSlot
        :visible="showSetExpense"
        title="设置费用项"
        @cancel="showSetExpense = false"
        @ok="setExpenseConfirm"
        width="400px"
    >
        <Form :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
            <FormItem label="名称">
                <Input v-model:value="setForm.expenseName" placeholder="名称" />
            </FormItem>
            <FormItem label="类型">
                <Select
                    v-model:value="setForm.expenseType"
                    placeholder="类型"
                    :options="expenseTypeList"
                    :getPopupContainer="() => body"
                />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { computed, defineComponent, ref, toRefs, watch } from 'vue'
import { Upload, InputNumber, message, Modal } from 'ant-design-vue'
import { UploadOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
import request from '/@/utils/request'
import { formatMapData, formatOriginForm } from '../util'
import { getBillExpenseManage } from '/@/utils/api'

export default defineComponent({
    name: 'CreateSalary',
    components: { Upload, InputNumber, UploadOutlined, ExclamationCircleFilled },
    props: {
        visible: Boolean,
        billId: String,
        clientId: String,
        costDate: String,
        billType: Number,
        billIds: {
            type: Array,
            // eslint-disable-next-line vue/require-valid-default-prop
            default: [],
        },
        clientIds: {
            type: Array,
            // eslint-disable-next-line vue/require-valid-default-prop
            default: [],
        },
        staffIds: {
            type: Array,
            // eslint-disable-next-line vue/require-valid-default-prop
            default: [],
        },
    },
    emits: ['update:visible', 'confirm'],
    setup(props, { emit }) {
        const { visible, billId, clientId, costDate, billType, billIds, clientIds, staffIds } = toRefs(props)
        watch(visible, () => {
            visible.value && getOriginList()
        })
        const originList = ref<LabelValueOptions>([])
        const tableData = ref<Recordable[]>([])
        const tableColumns = ref<Recordable[]>([])
        const resData = ref<Recordable[]>([])
        const resColumns = ref<Recordable[]>([])
        const unuseCols = ref<Recordable[]>([])
        const formData = ref<Recordable>({
            originId: undefined,
            originName: undefined,
            startRow: 1,
        })
        const resetData = () => {
            tableData.value = []
            tableColumns.value = []
            resColumns.value = []
            resData.value = []
            unuseCols.value = []
            formData.value = {
                originId: undefined,
                originName: undefined,
                startRow: 1,
            }
        }
        const CreateSalaryDetail = async () => {
            let res
            if (billIds.value && billIds.value.length)
                res = await request.post(`/api/hr-bill-detail-itemses/createBillDetails`, {
                    billId: billId.value,
                    billIdList: billIds.value,
                    clientIdList: clientIds.value,
                    staffIds: staffIds.value?.length ? staffIds.value : undefined,
                })
            else
                res = await request.post(`/api/hr-bill-detail-itemses/createBillDetails`, {
                    billId: billId.value,
                    staffIds: staffIds.value?.length ? staffIds.value : undefined,
                })
            if (billType.value == 2) {
                emit('confirm', res)
                modalClose()
                return
            }
            const duplicateTitle = await request.post('/api/hr-bills/query-duplicate-form-data', {
                clientId: res.clientId,
                id: res.id,
                paymentDate: res.paymentDate,
                billDetailList: res.billDetailList,
                billIdList: billIds.value,
                clientIdList: clientIds.value,
            })
            if (duplicateTitle.title) {
                Modal.confirm({
                    title: '确认',
                    content: `当前账单与${duplicateTitle.title}有重复，是否进行计算！`,
                    onOk() {
                        emit('confirm', res)
                        modalClose()
                    },
                    onCancel() {
                        modalClose()
                    },
                })
            } else {
                emit('confirm', res)
                modalClose()
            }
        }
        const modalClose = () => {
            emit('update:visible', false)
            resetData()
        }
        const modalConfirm = async () => {
            if (!formData.value.originName && !formData.value.originId) {
                message.warn('请先上传原单！')
                return
            }
            const res = await request.get(`/api/hr-bill-dynamic-fieldses/getUnUsedFieldList/${billId.value}`)
            unuseCols.value = res
            if (res && res.length) {
                showTip.value = true
            } else {
                CreateSalaryDetail()
            }
        }

        const selectOrigin = async (value) => {
            formData.value.originName = originList.value.find((i) => i.value == value)?.label
            const form = new FormData()
            form.append('originSalaryId', value)
            const res = await request.post(
                `/api/hr-bill-dynamic-fieldses/getOriginTableHeader/${billId.value}/${clientId.value}`,
                form,
            )
            message.loading('解析表单中...')
            formData.value.startRow = res.dataStartRow + 1
            const { columns: originColumns, data: originData } = formatOriginForm(res.cellItemList, setExpense)
            tableData.value = [originData]
            tableColumns.value = originColumns
            const { columns, data } = formatMapData(res.tableMappingList)
            resColumns.value = columns
            resData.value = data
        }

        const setForm = ref({
            expenseName: undefined,
            expenseType: undefined,
            sortValue: undefined,
        })
        const showSetExpense = ref(false)
        const currentColKey = ref(undefined)
        const setExpense = (name, colKey, order) => {
            currentColKey.value = colKey
            setForm.value = {
                expenseName: name,
                expenseType: undefined,
                sortValue: order,
            }
            showSetExpense.value = true
        }

        const setExpenseConfirm = async () => {
            const res = await request.post(`/api/hr-bill-dynamic-fieldses/setFieldMappingRelation/${billId.value}`, {
                ...setForm.value,
                clientId: clientId.value,
            })
            showSetExpense.value = false
            tableData.value[0][currentColKey.value || '-'] = setForm.value.expenseName // 改变原单数据 状态为已映射
            const { columns, data } = formatMapData(res)
            resColumns.value = columns
            resData.value = data
        }

        const beforeUpload = async (file) => {
            const form = new FormData()
            form.append('file', file)
            const res = await request.post(
                `/api/hr-bill-dynamic-fieldses/getOriginTableHeader/${billId.value}/${clientId.value}`,
                form,
            )
            message.loading('解析表单中...')
            formData.value.originName = file.name
            formData.value.startRow = res.dataStartRow + 1
            const { columns: originColumns, data: originData } = formatOriginForm(res.cellItemList, setExpense)
            tableData.value = [originData]
            tableColumns.value = originColumns
            console.log(tableColumns.value)
            const { columns, data } = formatMapData(res.tableMappingList)
            resColumns.value = columns
            resData.value = data
            return false
        }

        const tipLoading = ref(false)
        const showTip = ref(false)
        const tipConfirm = async () => {
            try {
                tipLoading.value = true
                await CreateSalaryDetail()
                showTip.value = false
            } finally {
                tipLoading.value = false
            }
        }
        const tipClose = () => {
            showTip.value = false
        }

        const getOriginList = async () => {
            getExpenseTypeList()
            const res = await request.post(`/api/hr-original-salariesSelect`, {
                clientId: clientId.value,
                costDate: costDate.value,
            })
            originList.value = res.map((i) => ({
                ...i,
                label: i.title,
                value: i.id,
            }))
        }

        const expenseTypeList = ref<LabelValueOptions>([])
        const getExpenseTypeList = async () => {
            getBillExpenseManage({ billType: billType.value || 0, clientId: clientId.value }).then((res) => {
                expenseTypeList.value = res as LabelValueOptions
            })
        }

        // setup

        const unuseColsList = computed(() => {
            let res = { id: 1 }
            unuseCols.value.forEach((i, idx) => {
                res[idx] = i.value
            })
            return [res]
        })
        const changeStartRow = async () => {
            if (!formData.value.startRow) {
                return
            }
            await request.post(
                `/api/hr-bill-dynamic-fieldses/updateDataStartRow/${billId.value}/${formData.value.startRow - 1}`,
                {},
            )
        }

        const removeField = (i, idx) => {
            Modal.confirm({
                title: '确认',
                content: '是否删除该字段映射?',
                onOk: async () => {
                    const colKey = resData.value[0][idx].value // 该列的key
                    console.log('colKey', colKey)

                    const res = await request.post(`/api/hr-bill-dynamic-fieldses/delFieldMappingRelation/${billId.value}`, {
                        expenseName: i.value,
                        sortValue: i.order,
                        clientId: clientId.value,
                    })
                    const { columns, data } = formatMapData(res)
                    resColumns.value = columns
                    resData.value = data

                    tableData.value[0][colKey] = i.value + '&' // 改变原单数据 状态为未映射
                },
            })
        }

        return {
            title: computed(() => {
                return billType.value == 0 ? '薪酬原单' : billType.value == 2 ? '其他账单' : '中石化费用统计账单'
            }),
            tipLoading,
            removeField,
            changeStartRow,
            unuseColsList,
            body: document.body,
            setExpenseConfirm,
            setForm,
            expenseTypeList,
            showSetExpense,
            resetData,
            selectOrigin,
            unuseCols,
            resData,
            resColumns,
            tableData,
            originList,
            showTip,
            tipConfirm,
            tipClose,
            tableColumns,
            beforeUpload,
            formData,
            modalClose,
            modalConfirm,
        }
    },
})
</script>

<style scoped lang="less">
.modalMain {
    min-height: 50vh;
}
.cell {
    .title {
        border-left: 5px solid @primary-color;
        padding-left: 10px;
    }
    .main {
        padding: 10px 0;
        .row {
            margin: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
        }
    }
}
.col {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .label {
        width: 100px;
        text-align: right;
        &:after {
            content: '：';
        }
    }
}

.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
.table {
    width: 100%;
    overflow-x: auto;
    .headerCols {
        display: flex;
        justify-content: flex-start;
        align-items: stretch;
        flex-wrap: nowrap;
        .th {
            flex-shrink: 0;
            display: inline-block;
            background: @primary-color;
            color: white;
            padding: 10px;
            text-align: center;
            border: 1px solid transparent;
            border-right-color: white;
        }
    }
    .bodyCols {
        .tr {
            display: flex;
            justify-content: flex-start;
            align-items: stretch;
            flex-wrap: nowrap;
            .tb {
                flex-shrink: 0;
                width: 100px;
                display: inline-block;
                padding: 10px;
                text-align: center;
                border: 1px solid #eee;
            }
        }
    }
}
</style>
