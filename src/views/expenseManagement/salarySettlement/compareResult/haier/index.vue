<template>
    <!-- 海尔对账 -->
    <!-- 海尔对账流程跟其他不同，在此页面对账，移植的是 （费用管理-账单管理-保障账单-批量对账）流程 -->
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="compareBill" v-auth="'haier_batchCompare'">对账</Button>
        <Button type="primary" @click="exportSome">{{ exportText }}</Button>
        <Button type="primary" danger @click="deleteData" v-auth="'haier_delete'">批量删除</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-compare-results/page"
        exportUrl="/api/hr-bill-compare-results/batch-export-haier"
        :columns="columns"
        deleteApi="/api/hr-bill-compare-results/deletes"
        :params="{ ...params, type: 5 }"
        @selectedRowsArr="selRows"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showRow(record)">查看</Button>
        </template>
    </BasicTable>
    <DetailModal v-model:visible="showDetail" :currentRecord="currentRecord" @confirm="lookConfirm" />
    <!-- 对账 2-->
    <CompareModal v-model:visible="showCompare" :billId="currentItem.id" :batchOpt="batchOpt" @closeRe="closeRe" />
    <!-- 对账 1-->
    <BatchCompareModal
        v-if="checkModalVisible"
        :viewType="batchType"
        :visible="checkModalVisible"
        @cancel="closeBatchModal"
        @next="batchNext"
    />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import BatchCompareModal from './BatchCompareModal.vue'
import CompareModal from './CompareModal.vue'
import DetailModal from './DetailModal.vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import { billTypeList, lockStatusList } from '/@/utils/dictionaries'

const params = ref<Recordable>({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}
const batchType = ref('start')

const showDetail = ref(false)
const batchOpt = ref(null)
const checkModalVisible = ref(false)
const currentItem = ref<Recordable>({})
// const currentExportTypeList = ref<LabelValueOptions>([])
const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}
const showCompare = ref(false)
const selArr = ref([])
const selRows = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})

const exportSome = async () => {
    // if (!selArr.value.length) {
    //     message.warning('请选择导出数据！')
    //     return
    // }
    // downFile('post', `/api/hr-bill-compare-configs/export`, '', selArr.value.map((item) => {
    //                 return item.billCompareConfigId
    //             }))
    // message.loading('正在导出，请稍后...')
    await tableRef.value.exportRow('exportIds', exportText.value, params.value)
}
const compareBill = () => {
    checkModalVisible.value = true
    batchType.value = 'start'
}

const closeBatchModal = () => {
    checkModalVisible.value = false
    batchType.value = 'start'
}

const batchNext = (opt) => {
    if (opt.type == 'start') {
        batchType.value = 'next'
    } else {
        showCompare.value = true
        // currentExportTypeList.value = exportTypeList.filter((i) => i.value != 3 && i.value != 4)
        batchOpt.value = opt
        closeBatchModal()
    }
}
const closeRe = () => {
    tableRef.value.refresh()
}

const deleteData = async (row = { id: '' }) => {
    tableRef.value.deleteRow(row.id).then((ref) => {
        console.log(ref)
    })
}
const lookConfirm = () => {
    tableRef.value.refresh()
}

const searchOptions: SearchBarOption[] = [
    // {
    //     label: '客户名称',
    //     key: 'clientId',
    //     type: 'clientSelectTree',
    // },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '标题',
        key: 'title',
    },
    // {
    //     label: '对账单类型',
    //     key: 'type',
    //     type: 'select',
    //     options: exportTypeList,
    // },
]

const columns = [
    // {
    //     title: '客户名称',
    //     dataIndex: 'clientName',
    //     width: 150,
    // },
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 120,
    },
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 120,
        customRender: ({ text }) => {
            return billTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '标题',
        dataIndex: 'title',
        width: 180,
        ellipsis: true,
    },
    // {
    //     title: '对账单类型',
    //     dataIndex: 'type',
    //     width: 120,
    //     customRender: ({ text }) => {
    //         return exportTypeList.find((i) => i.value == text)?.label
    //     },
    // },
    {
        title: '差异数量',
        dataIndex: 'diffDataNums',
        width: 100,
    },
    {
        title: '更新日期',
        dataIndex: 'createdDate',
        width: 170,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]
</script>

<style scoped lang="less"></style>
