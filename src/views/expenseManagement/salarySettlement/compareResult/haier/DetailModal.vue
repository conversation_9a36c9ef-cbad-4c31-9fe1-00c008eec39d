<template>
    <!-- 海尔对账结果 -->
    <BasicEditModalSlot :visible="visible" title="对账结果" @cancel="resultClose" width="1200px" :destroyOnClose="true">
        <div class="header">
            <Button type="primary" style="margin-bottom: 10px" @click="exportData">导出</Button>
            <!-- <div>* 深色背景行表示业务数据，浅色背景行表示excel数据，红色字体意为数据不一致</div> -->
        </div>
        <div class="search">
            <Input v-model:value="keyWords" @change="searchChange()" placeholder="姓名搜索" />
        </div>
        <Table
            :bordered="true"
            :columns="resultColumns"
            rowKey="id"
            size="small"
            :scroll="{ x: 100 }"
            :rowSelection="false"
            :dataSource="resultData"
        />
        <template #footer>
            <Button @click="resultClose">取消</Button>
            <!-- 已保存和第三方账单不支持保存补差 -->
            <!-- <Button v-if="formData.status == 0 && formData.type != 4" type="primary" @click="resultConfirm">保存补差</Button> -->
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { PropType, ref, toRefs, watch } from 'vue'
import downFile from '/@/utils/downFile'
import { formatDynamicColns } from '/@/utils/dynamicHeader'
import request from '/@/utils/request'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object as PropType<Recordable>,
})

const { visible, currentRecord } = toRefs<any>(props)

const emit = defineEmits(['update:visible', 'confirm'])

const resultColumns = ref<Recordable[]>([])
const resultData = ref<Recordable[]>([])
const formData = ref<Recordable>({
    type: undefined,
    status: 1, // 补差保存状态 0 未保存, 1 已保存
})

const resetData = () => {
    resultColumns.value = []
    resultData.value = []
    formData.value = {
        type: undefined,
        status: 1,
    }
}
const resultClose = () => {
    emit('update:visible', false)
    resetData()
}
const resultConfirm = async () => {
    await request.post(`/api/hr-bill-compare-configs/saveCompareMakeUp/${currentRecord.value.billCompareConfigId}/2`, {})
    emit('confirm')
    resultClose()
}

const exportData = () => {
    downFile('post', `/api/hr-bill-compare-results/batch-export-haier`, '对账结果.xlsx', {
        exportIds: [formData.value.billResultId],
    })
}

let searchList = []
const getData = async () => {
    // getData
    const res = await request.get(`/api/hr-bill-compare-configs/${currentRecord.value.billCompareConfigId}`)
    formData.value = res
    // resultData.value = res.compareResultList
    formatDynamicColns(res, resultColumns)
    searchList = res.hrBillCompareResultDetailDTOList
    let arr = res.hrBillCompareResultDetailDTOList
    arr.forEach((item, index) => {
        arr[index].systemData = JSON.parse(item.systemData) ?? 0
        arr[index].sourceData = JSON.parse(item.sourceData) ?? 0
    })
    searchList = arr
    resultData.value = searchList
}

// 搜索
const keyWords = ref('')
const searchChange = () => {
    if (keyWords.value == '') {
        resultData.value = searchList
    }
    resultData.value = searchList.filter((el: any) => {
        if (el.staffName.includes(keyWords.value)) {
            return el
        }
    })
}

watch(visible, () => {
    visible.value && getData()
})
</script>

<style scoped lang="less">
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.search {
    width: 250px;
    margin: 10px 0;
}
</style>
