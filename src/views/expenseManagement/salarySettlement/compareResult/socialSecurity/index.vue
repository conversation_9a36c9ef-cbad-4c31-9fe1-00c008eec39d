<template>
    <!-- 社保对账 -->
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" danger @click="deleteData" v-auth="'socialSecurityCheck_delete'">批量删除</Button>
        <Button type="primary" v-auth="'socialSecurity_batchCompare'" @click="showBatchModal">批量对账</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-compare-results/page"
        exportUrl="/api/hr-bill-compare-results/batch-export"
        deleteApi="/api/hr-bill-compare-results/deletes"
        :columns="columns"
        :params="{ ...params, type: 0 }"
        @selectedRowsArr="selRows"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showRow(record)">查看</Button>
        </template>
    </BasicTable>
    <DetailModal v-model:visible="showDetail" :currentRecord="currentRecord" @confirm="lookConfirm" />

    <!-- 对账 -->
    <!--  :exportTypeList="currentExportTypeList" 在内部 -->
    <CompareModal v-model:visible="showCompare" :billId="''" @cancel="compareClose" :batchOpt="batchOpt" :billType="0" />
    <!-- 批量对账 -->
    <!-- v-if="checkModalVisible" -->
    <BatchCompareModal
        ref="batchModalRef"
        :viewType="batchType"
        :visible="checkModalVisible"
        @cancel="closeBatchModal"
        @next="batchNext"
    />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

import DetailModal from '../DetailModal.vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import { billTypeList, exportTypeList, lockStatusList } from '/@/utils/dictionaries'
import CompareModal from '../CompareModal.vue'
import BatchCompareModal from '../BatchCompareModal.vue'

const params = ref<Recordable>({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}

const showDetail = ref(false)
const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}

const selArr = ref([])
const selRows = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})
const exportData = async () => {
    await tableRef.value.exportRow('ids', exportText.value, params.value)
    tableRef.value.refresh()
}

const deleteData = async (row = { id: '' }) => {
    tableRef.value.deleteRow(row.id).then((ref) => {
        // console.log(row.id)
    })
}

// 对账
const batchModalRef = ref()
const batchOpt = ref(undefined)
const showCompare = ref(false)
const checkModalVisible = ref(false)
const batchType = ref('start')
const showBatchModal = () => {
    checkModalVisible.value = true
    batchType.value = 'start'
}

const closeBatchModal = () => {
    checkModalVisible.value = false
    batchType.value = 'start'
    batchModalRef.value.reModal()
}

const batchNext = (opt) => {
    if (opt.type == 'start') {
        batchType.value = 'next'
    } else {
        showCompare.value = true
        batchOpt.value = opt
        closeBatchModal()
    }
}

const compareClose = () => {
    tableRef.value.refresh(1)
}
const lookConfirm = () => {
    tableRef.value.refresh()
}

const searchOptions: SearchBarOption[] = [
    // {
    //     label: '客户名称',
    //     key: 'clientId',
    //     type: 'clientSelectTree',
    // },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '账单类型',
        key: 'billType',
        type: 'select',
        options: billTypeList,
    },
    {
        label: '锁定状态',
        key: 'lockStatus',
        type: 'select',
        options: lockStatusList,
    },
    {
        label: '补差保存状态',
        key: 'status',
        type: 'select',
        options: [
            {
                label: '未保存',
                value: 0,
            },
            {
                label: '已保存',
                value: 1,
            },
        ],
    },
    {
        label: '标题',
        key: 'title',
    },
]

const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 150,
    },
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 120,
    },
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 120,
        customRender: ({ text }) => {
            return billTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '标题',
        dataIndex: 'title',
        width: 180,
        ellipsis: true,
    },
    {
        title: '对账单类型',
        dataIndex: 'type',
        width: 120,
        customRender: ({ text }) => {
            return exportTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '差异数量',
        dataIndex: 'diffDataNums',
        width: 100,
    },
    {
        title: '锁定状态',
        dataIndex: 'lockStatus',
        width: 100,
        customRender: ({ text }) => {
            return lockStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '补差保存状态',
        dataIndex: 'status',
        customRender: ({ text }) => {
            if (text == 0) {
                return '未保存'
            } else {
                return '保存'
            }
        },
        width: 100,
    },
    {
        title: '更新日期',
        dataIndex: 'createdDate',
        width: 170,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]
</script>

<style scoped lang="less"></style>
