<template>
    <!-- 对账结果 -->
    <BasicEditModalSlot :visible="visible" title="对账结果" @cancel="closeCancel" width="1200px" :destroyOnClose="true">
        <div class="header">
            <Button type="primary" style="margin-bottom: 10px; margin-right: 20px" @click="exportData">导出</Button>
            <Button type="primary" v-if="showUnLock" style="margin-bottom: 10px" @click="lockFun">
                <!-- {{ isLock ? '锁定' : '解锁' }} -->
                <LockOutlined v-if="isLock" />
                <UnlockOutlined v-if="!isLock" />
            </Button>
            <!-- <div>* 深色背景行表示业务数据，浅色背景行表示excel数据，红色字体意为数据不一致</div> -->
        </div>
        <div class="search">
            <Input v-model:value="keyWords" @change="searchChange()" placeholder="姓名搜索" />
            &nbsp;
            <Input v-model:value="utilsNo" @change="searchUtilNoChange()" placeholder="单位编号搜索" />
        </div>
        <Table
            :bordered="true"
            :columns="resultColumns"
            rowKey="id"
            size="small"
            :scroll="{ x: 100 }"
            :rowSelection="null"
            :dataSource="resultData"
        />
        <template #footer>
            <!-- 已锁定显示确认按钮-->
            <Button @click="resultClose">{{ showUnLock && isLock ? '确认' : '取消' }}</Button>
            <!-- 已保存和第三方账单不支持保存补差 -->
            <Button v-if="lock_status == 1 && formData.status == 0 && formData.type != 4" type="primary" @click="saveCompensation"
                >保存补差</Button
            >
        </template>
    </BasicEditModalSlot>

    <!-- 是否保存补差 -->
    <BasicEditModalSlot :visible="saveComShow" title="是否保存补差" cancalText="" @cancel="denySave" width="600px">
        <div>是否当月使用补差</div>
        <template #footer>
            <Button @click="noSave">否</Button>
            <Button type="primary" @click="yesSave">是</Button>
        </template>
    </BasicEditModalSlot>

    <!-- 不可当月使用补差的客户信息 -->
    <BasicEditModalSlot
        :visible="noUseShow"
        title="不可当月使用补差的客户信息"
        cancalText=""
        @cancel="noUseSaveAndConfirm"
        width="1200px"
    >
        <div class="header">
            <Button type="primary" style="margin-bottom: 10px" @click="noUseExport">导出</Button>
        </div>
        <BasicTable
            ref="tableRefCustomer"
            :useIndex="true"
            :tableDataList="tableDataList"
            :rowSelectionShow="false"
            :columns="compensationColumns"
        />
        <template #footer>
            <Button @click="noUseSaveAndConfirm" type="primary" class="btn"> 确定 </Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { PropType, ref, toRefs, watch } from 'vue'
import downFile from '/@/utils/downFile'
import { formatDynamicColns } from '/@/utils/dynamicHeader'
import request from '/@/utils/request'
import { message } from 'ant-design-vue'
import { LockOutlined, UnlockOutlined } from '@ant-design/icons-vue'

const props = defineProps({
    visible: Boolean,
    currentRecord: Object as PropType<Recordable>,
})

const { visible, currentRecord } = toRefs<any>(props)

const emit = defineEmits(['update:visible', 'confirm'])

const resultColumns = ref<Recordable[]>([])
const resultData = ref<Recordable[]>([])
const formData = ref<Recordable>({
    type: undefined,
    status: 1, // 补差保存状态 0 未保存, 1 已保存
})
const lock_status = ref(0) //锁定状态  0 未锁定 1 已锁定
// 搜索
const keyWords = ref('')
const searchChange = () => {
    if (keyWords.value == '') {
        resultData.value = searchList
    }
    resultData.value = searchList.filter((el: any) => {
        if (el.staffName.includes(keyWords.value)) {
            return el
        }
    })
}
const utilsNo = ref('')
const searchUtilNoChange = () => {
    if (utilsNo.value == '') {
        resultData.value = searchList
    }
    resultData.value = searchList.filter((el: any) => {
        if (el.unitNo) {
            if (el.unitNo.includes(utilsNo.value)) {
                return el
            }
        }
    })
}
const resetData = () => {
    resultColumns.value = []
    resultData.value = []
    formData.value = {
        type: undefined,
        status: 1,
    }
    showUnLock.value = false
    lock_status.value = 0
    keyWords.value = ''
}
const resultClose = async () => {
    if (formData.value.status != 1) {
        await request.post(`/api/hr-bill-compare-configs/save-only`, {
            billConfigIds: [currentRecord.value.billCompareConfigId],
            lockStatus: lock_status.value,
        })
    }

    emit('update:visible', false)
    emit('confirm')
    resetData()
}

const closeCancel = () => {
    emit('update:visible', false)
    resetData()
}
const resultConfirm = async (val) => {
    let res = await request.post(
        `/api/hr-bill-compare-configs/saveCompareMakeUp/${currentRecord.value.billCompareConfigId}/${val}`,
        {},
    )
    if (val == 1) {
        tableDataList.value = res
    }
}

const exportData = () => {
    downFile('post', `/api/hr-bill-compare-results/export`, '对账结果.xlsx', {
        billCompareConfigIds: [currentRecord.value.billCompareConfigId],
    })
}
const showUnLock = ref(false)
let searchList = []
const getData = async () => {
    const res = await request.get(`/api/hr-bill-compare-configs/${currentRecord.value.billCompareConfigId}`)
    formData.value = res
    formatDynamicColns(res, resultColumns)
    searchList = res.hrBillCompareResultDetailDTOList
    resultData.value = searchList
    lock_status.value = res.lockStatus
    if (res.lockStatus == 0) {
        isLock.value = false
    } else {
        isLock.value = true
    }
    if (res.status == 0) {
        showUnLock.value = true
    } else {
        showUnLock.value = false
    }
}

watch(visible, () => {
    visible.value && getData()
})
const isLock = ref(false)
const lockFun = () => {
    isLock.value = !isLock.value
    if (isLock.value) {
        lock_status.value = 1
    } else {
        lock_status.value = 0
    }
}

const saveComShow = ref(false)
const noUseShow = ref(false)
const saveCompensation = () => {
    saveComShow.value = true
}
const yesSave = async () => {
    saveComShow.value = false
    await resultConfirm(1)
    emit('update:visible', false)
    // showResult.value = false
    noUseShow.value = true
}
const noSave = async () => {
    await resultConfirm(0)
    saveComShow.value = false
    // showResult.value = false
    resultColumns.value = []
    emit('confirm')
    resultClose()
}
const denySave = () => {
    saveComShow.value = false
}
// ***  不可当月使用补差的客户信息 关闭和确认
const tableDataList = ref([])
const noUseSaveAndConfirm = () => {
    saveComShow.value = false
    noUseShow.value = false
    resultColumns.value = []
    emit('confirm')
    resultClose()
}

const noUseExport = () => {
    if (tableDataList.value?.length == 0) {
        message.warning({ content: '没有数据' })
        return
    }
    downFile('post', `api/hr-bill-compare-result-detail/export-excludes-client`, '不可对账账户信息.xlsx', {
        ids: tableDataList.value?.map((el: any) => el.id),
    })
}
//表格数据
const compensationColumns = ref([
    {
        title: '客户名称',
        dataIndex: 'clientName',
        align: 'center',
        sorter: false,
    },
    {
        title: '客户编号',
        dataIndex: 'unitNumber',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '费用年月',
        dataIndex: 'paymentDate',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '账单类型',
        dataIndex: 'billTypeStr',
        align: 'center',
        width: 150,
        sorter: false,
    },
    {
        title: '锁定状态',
        dataIndex: 'billStateStr',
        align: 'center',
        width: 150,
        sorter: false,
    },
])
</script>

<style scoped lang="less">
.header {
    display: flex;
    justify-content: start;
    align-items: center;
}
.search {
    width: 450px;
    margin: 10px 0;
    display: flex;
}
</style>
