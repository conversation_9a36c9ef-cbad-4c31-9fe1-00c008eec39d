<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportSome">{{ exportText }}</Button>
        <Button type="primary" danger @click="deleteData" v-auth="'tax_delete'">批量删除</Button>
        <Button type="primary" v-auth="'tax_batchCompare'" @click="showBatchModal">批量对账</Button>
        <Button type="primary" v-auth="'tax_lock'" @click="updateLockStatus">批量锁定</Button>
        <Button type="primary" v-auth="'tax_saveMakeup'" @click="updateSaveMakeup">批量保存补差</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-compare-results/page"
        exportUrl="/api/hr-bill-compare-results/batch-export"
        :columns="columns"
        deleteApi="/api/hr-bill-compare-results/deletes"
        :params="{ ...params, type: 3 }"
        @selectedRowsArr="selRows"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showRow(record)">查看</Button>
        </template>
    </BasicTable>
    <DetailModal v-model:visible="showDetail" :currentRecord="currentRecord" @confirm="lookConfirm" />
    <BatchCompareModal
        ref="batchModalRef"
        :viewType="batchType"
        :modalType="modalType"
        :visible="checkModalVisible"
        @cancel="closeBatchModal"
        @next="batchNext"
    />
    <CompareModal
        v-model:visible="showCompare"
        :exportTypeList="currentExportTypeList"
        :batchOpt="batchOpt"
        :modalType="modalType"
        @cancel="compareClose"
    />
    <BasicEditModalSlot :visible="showSaveMakeupModal" title="是否当月使用补差" @cancel="modalClose">
        <template #footer>
            <Button @click="noSaveCancel">否</Button>
            <Button type="primary" @click="yesSaveCancel">是</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import DetailModal from '../DetailModal.vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import { billTypeList, exportTypeList, lockStatusList } from '/@/utils/dictionaries'
import CompareModal from './CompareModal.vue'
import BatchCompareModal from '../BatchCompareModal.vue'
import request from '/@/utils/request'
const params = ref<Recordable>({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}

const showDetail = ref(false)
const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}

const selArr = ref([])
const selRows = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})

const deleteData = async (row = { id: '' }) => {
    tableRef.value.deleteRow(row.id).then((ref) => {
        // console.log(row.id)
    })
}
const exportSome = async () => {
    // if (!selArr.value.length) {
    //     message.warning('请选择导出数据！')
    //     return
    // }

    // downFile('post', `/api/hr-bill-compare-configs/export`, '', selArr.value.map((item) => {
    //                 return item.billCompareConfigId
    //             }))
    // message.loading('正在导出，请稍后...')
    await tableRef.value.exportRow('ids', exportText.value, params.value)
}

const modalType = ref('个税')
//组件内部没用到
// const currentIds = ref<any[]>([])
// const getListId = () => {
//     console.log(selArr.value)
//     currentIds.value = selArr.value.map((el:any) => {
//         return el.id
//     })
// }
// 对账
const currentExportTypeList = ref<LabelValueOptions>([])
const batchModalRef = ref()
const batchOpt = ref(undefined)
const showCompare = ref(false)
const checkModalVisible = ref(false)
const batchType = ref('start')
const showBatchModal = () => {
    checkModalVisible.value = true
    batchType.value = 'start'
}

const closeBatchModal = () => {
    checkModalVisible.value = false
    batchType.value = 'start'
    batchModalRef.value.reModal()
}

const updateLockStatus = async () => {
    await request
        .post(`/api/hr-bill-compare-configs/batchLockOrSaveMakeUp`, {
            type: 3,
            ...params.value,
            options: 1,
            ids: selArr.value.map((i: any) => i.id),
        })
        .then((res) => {
            message.success(res)
            tableRef.value.refresh()
        })
        .catch((err) => {
            console.log(err)
        })
}

const modalClose = () => {
    showSaveMakeupModal.value = false
    tableRef.value.refresh(1)
}
const lookConfirm = () => {
    tableRef.value.refresh()
}
const showSaveMakeupModal = ref(false)
const updateSaveMakeup = () => {
    showSaveMakeupModal.value = true
}

const noSaveCancel = async () => {
    await request
        .post(`/api/hr-bill-compare-configs/batchLockOrSaveMakeUp`, {
            type: 3,
            ...params.value,
            options: 2,
            ids: selArr.value.map((i: any) => i.id),
            currentMonthUsed: 0,
        })
        .then((res) => {
            message.success(res)
            showSaveMakeupModal.value = false
            tableRef.value.refresh()
        })
        .catch((err) => {
            console.log(err)
        })
}

const yesSaveCancel = async () => {
    await request
        .post(`/api/hr-bill-compare-configs/batchLockOrSaveMakeUp`, {
            type: 3,
            ...params.value,
            options: 2,
            ids: selArr.value.map((i: any) => i.id),
            currentMonthUsed: 1,
        })
        .then((res) => {
            message.success(res)
            showSaveMakeupModal.value = false
            tableRef.value.refresh()
        })
        .catch((err) => {
            console.log(err)
        })
}
const batchNext = (opt) => {
    if (opt.type == 'start') {
        batchType.value = 'next'
    } else {
        showCompare.value = true
        batchOpt.value = opt
        currentExportTypeList.value = exportTypeList.filter((i) => i.value == 3)
        closeBatchModal()
    }
}

const compareClose = () => {
    tableRef.value.refresh(1)
}

const searchOptions: SearchBarOption[] = [
    {
        label: '客户名称',
        key: 'clientIds',
        type: 'clientSelectTree',
        maxTag: '0',
        multiple: true,
        checkStrictly: false,
    },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '账单类型',
        key: 'billType',
        type: 'select',
        options: billTypeList,
    },
    {
        label: '锁定状态',
        key: 'lockStatus',
        type: 'select',
        options: lockStatusList,
    },
    {
        label: '补差保存状态',
        key: 'status',
        type: 'select',
        options: [
            {
                label: '未保存',
                value: 0,
            },
            {
                label: '已保存',
                value: 1,
            },
        ],
    },
    {
        label: '标题',
        key: 'title',
    },
]

const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 150,
    },
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 120,
    },
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 120,
        customRender: ({ text }) => {
            return billTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '标题',
        dataIndex: 'title',
        width: 180,
        ellipsis: true,
    },
    {
        title: '对账单类型',
        dataIndex: 'type',
        width: 120,
        customRender: ({ text }) => {
            return exportTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '差异数量',
        dataIndex: 'diffDataNums',
        width: 100,
    },
    {
        title: '锁定状态',
        dataIndex: 'lockStatus',
        width: 100,
        customRender: ({ text }) => {
            return lockStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '补差保存状态',
        dataIndex: 'status',
        customRender: ({ text }) => {
            if (text == 0) {
                return '未保存'
            } else {
                return '保存'
            }
        },
        width: 100,
    },
    {
        title: '更新日期',
        dataIndex: 'createdDate',
        width: 170,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]
</script>

<style scoped lang="less"></style>
