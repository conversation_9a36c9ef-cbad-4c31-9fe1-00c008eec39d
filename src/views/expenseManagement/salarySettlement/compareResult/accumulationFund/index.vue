<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" @click="exportSome">导出</Button>
        <Button type="primary" danger @click="deleteData" v-auth="'accumulationCheck_delete'">批量删除</Button>
        <Button type="primary" v-auth="'accumulation_batchCompare'" @click="showBatchModal">批量对账</Button>
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-bill-compare-results/page"
        exportUrl="/api/hr-bill-compare-results/batch-export"
        deleteApi="/api/hr-bill-compare-results/deletes"
        :columns="columns"
        :params="{ ...params, type: 2 }"
        @selectedRowsArr="selRows"
    >
        <template #operation="{ record }">
            <Button type="primary" size="small" @click="showRow(record)">查看</Button>
        </template>
    </BasicTable>
    <DetailModal v-model:visible="showDetail" :currentRecord="currentRecord" @confirm="lookConfirm" />

    <!-- 对账 -->
    <CompareModal v-model:visible="showCompare" :billId="''" @cancel="compareClose" :batchOpt="batchOpt" :billType="2" />
    <!-- 批量对账 -->
    <BatchCompareModal
        ref="batchModalRef"
        :viewType="batchType"
        :visible="checkModalVisible"
        @cancel="closeBatchModal"
        @next="batchNext"
    />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import DetailModal from '../DetailModal.vue'
import { SearchBarOption } from '/#/component'
import { getDynamicText } from '/@/utils'
import { billTypeList, exportTypeList, lockStatusList } from '/@/utils/dictionaries'
import CompareModal from '../CompareModal.vue'
import BatchCompareModal from '../BatchCompareModal.vue'

const params = ref<Recordable>({})
const tableRef = ref()
const searchData = () => {
    tableRef.value.refresh(1)
}
const lookConfirm = () => {
    tableRef.value.refresh()
}

const showDetail = ref(false)
const currentRecord = ref<Recordable>({})
const showRow = (record) => {
    currentRecord.value = { ...record }
    showDetail.value = true
}

const selArr = ref([])
const selRows = (arr) => {
    selArr.value = arr
}
const exportText = computed(() => {
    return getDynamicText('导出', params.value, selArr.value)
})

/**
 * 批量删除
 * @param row
 */
const deleteData = async (row = { id: '' }) => {
    tableRef.value.deleteRow(row.id).then((ref) => {
        // console.log(row.id)
    })
}

const exportSome = async () => {
    // if (!selArr.value.length) {
    //     message.warning('请选择导出数据！')
    //     return
    // }
    // message.loading('正在导出，请稍后...')
    await tableRef.value.exportRow('ids', exportText.value, params.value)
    // downFile('post', `/api/hr-bill-compare-configs/export`, '', selArr.value.map((item) => {
    //                 return item.billCompareConfigId
    //             }))
}

// 对账
const batchModalRef = ref()
const batchOpt = ref<any>(null)
const showCompare = ref(false)
const checkModalVisible = ref(false)
const batchType = ref('start')
const showBatchModal = () => {
    checkModalVisible.value = true
    batchType.value = 'start'
}

const closeBatchModal = () => {
    checkModalVisible.value = false
    batchType.value = 'start'
    batchModalRef.value.reModal()
}

const batchNext = (opt) => {
    if (opt.type == 'start') {
        batchType.value = 'next'
    } else {
        showCompare.value = true
        batchOpt.value = opt
        closeBatchModal()
    }
}

const compareClose = () => {
    tableRef.value.refresh(1)
}

const searchOptions: SearchBarOption[] = [
    // {
    //     label: '客户名称',
    //     key: 'clientId',
    //     type: 'clientSelectTree',
    // },
    {
        label: '缴费年月',
        key: 'paymentDate',
        type: 'month',
    },
    {
        label: '账单类型',
        key: 'billType',
        type: 'select',
        options: billTypeList,
    },
    {
        label: '锁定状态',
        key: 'lockStatus',
        type: 'select',
        options: lockStatusList,
    },
    {
        label: '补差保存状态',
        key: 'status',
        type: 'select',
        options: [
            {
                label: '未保存',
                value: 0,
            },
            {
                label: '已保存',
                value: 1,
            },
        ],
    },
    {
        label: '标题',
        key: 'title',
    },
    // {
    //     label: '对账单类型',
    //     key: 'type',
    //     type: 'select',
    //     options: exportTypeList,
    // },
]

const columns = [
    {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 150,
    },
    {
        title: '缴费年月',
        dataIndex: 'paymentDate',
        width: 120,
    },
    {
        title: '账单类型',
        dataIndex: 'billType',
        width: 120,
        customRender: ({ text }) => {
            return billTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '标题',
        dataIndex: 'title',
        width: 180,
        ellipsis: true,
    },
    {
        title: '对账单类型',
        dataIndex: 'type',
        width: 120,
        customRender: ({ text }) => {
            return exportTypeList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '差异数量',
        dataIndex: 'diffDataNums',
        width: 100,
    },
    {
        title: '锁定状态',
        dataIndex: 'lockStatus',
        width: 100,
        customRender: ({ text }) => {
            return lockStatusList.find((i) => i.value == text)?.label
        },
    },
    {
        title: '补差保存状态',
        dataIndex: 'status',
        customRender: ({ text }) => {
            if (text == 0) {
                return '未保存'
            } else {
                return '保存'
            }
        },
        width: 100,
    },
    {
        title: '更新日期',
        dataIndex: 'createdDate',
        width: 170,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        fixed: 'right',
        slots: { customRender: 'operation' },
    },
]
</script>

<style scoped lang="less"></style>
