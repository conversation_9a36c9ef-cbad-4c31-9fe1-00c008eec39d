<template>
    <BasicEditModalSlot :visible="visible" title="批量修改" @cancel="modalClose" width="1200px">
        <Form ref="formInline" :model="formData" v-if="cardinalArr.length > 0">
            <div class="formBox" v-if="cardinalArr.length > 0">
                <Row :wrap="true" gutter="20">
                    <Col span="12" v-for="(item, index) in cardinalArr" :key="index">
                        <FormItem
                            :label="item.title + ':'"
                            :name="item.dataIndexS[0]"
                            :labelCol="{ span: 8 }"
                            :wrapperCol="{ span: 16 }"
                            class="item"
                        >
                            <InputNumber
                                v-model:value.number="formData[item.dataIndexS[0]]"
                                :placeholder="`请输入${item.title}`"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem
                            class="item"
                            :label="'公积金基数:'"
                            name="paymentDate"
                            :labelCol="{ span: 8 }"
                            :wrapperCol="{ span: 16 }"
                        >
                            <InputNumber
                                v-model:value.number="formData.accumulationFundCardinal"
                                :placeholder="`请输入公积金基数`"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem
                            class="item"
                            :label="'缴费年月:'"
                            name="paymentDate"
                            :labelCol="{ span: 8 }"
                            :wrapperCol="{ span: 16 }"
                        >
                            <MonthPicker
                                class="input"
                                allowClear
                                v-model:value="formData.paymentDate"
                                :placeholder="`请输入缴费年月`"
                                valueFormat="YYYY-MM"
                            />
                        </FormItem>
                    </Col>
                </Row>
            </div>
        </Form>
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 14 }"
            class="form-flex"
            v-if="cardinalArr.length === 0"
        >
            <!-- :rules="rules" -->
            <div style="width: 100%">
                <Row :wrap="true">
                    <template v-for="itemForm in myOptions" :key="itemForm.name">
                        <Col span="12">
                            <MyFormItem :item="itemForm" v-model:value="formData[itemForm.name]" />
                        </Col>
                    </template>
                </Row>
            </div>
        </Form>

        <template #footer>
            <Button @click="modalClose">取消</Button>
            <Button type="primary" @click="modalConfirm">确认</Button>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import { MonthPicker } from 'ant-design-vue'
import moment from 'moment'
import request from '/@/utils/request'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import lodash from 'lodash'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'DetailModal',
    components: { MonthPicker },
    props: {
        visible: Boolean,
        // currentItem: Object,
        title: String,
        beforeConfirm: {
            type: Function,
        },
        typeParams: Object,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, title, typeParams } = toRefs(props)
        const formInline = ref()

        watch(visible, async () => {
            visible.value && (await getList())

            if (visible.value == false) {
                modalClose()
            }
        })

        const myOptions = ref([
            {
                label: '单位养老基数',
                name: 'unitPensionCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '单位失业基数',
                name: 'unitUnemploymentCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '单位医疗基数',
                name: 'medicalInsuranceCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '单位生育基数',
                name: 'unitMaternityCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '单位工伤基数',
                name: 'workInjuryCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '单位大额医疗费用',
                name: 'unitLargeMedicalExpense',
                type: 'number',
                required: false,
            },
            {
                label: '补充工伤费用',
                name: 'replenishWorkInjuryExpense',
                type: 'number',
                required: false,
            },
            {
                label: '个人医疗基数',
                name: 'medicalInsuranceCardinalPersonal',
                type: 'number',
                required: false,
            },
            {
                label: '个人养老基数',
                name: 'personalPensionCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '个人失业基数',
                name: 'personalUnemploymentCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '个人生育基数',
                name: 'personalMaternityCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '个人大额医疗费用',
                name: 'personalLargeMedicalExpense',
                type: 'number',
                required: false,
            },
            {
                label: '公积金基数',
                name: 'accumulationFundCardinal',
                type: 'number',
                required: false,
            },
            {
                label: '缴费年月',
                name: 'paymentDate',
                type: 'month',
                attrs: {
                    disabledDate: (current) => current && current >= moment(),
                },
                required: false,
            },
        ])

        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)

        const formData = ref<any>(initFormData)
        // 动态基数数组
        const cardinalArr = ref<any[]>([])
        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, {
                socialId: typeParams.value ? typeParams.value.socialSecurityId : undefined,
            })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = lodash.cloneDeep(arr)
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })

                return item
            })
            return resArr
        }
        const modalClose = () => {
            emit('cancel')
            formData.value = initFormData
            formData.value.paymentDate = null
            formInline.value?.resetFields()
        }

        const resObj = computed(() => {
            let obj = {}
            cardinalArr.value?.map((el) => {
                for (const k in el) {
                    el.dataIndexS.forEach((row) => {
                        if (row === k) {
                            obj[row] = formData.value[el.dataIndexS[0]]
                        }
                    })
                }
                return el
            })
            return obj
        })

        const modalConfirm = async () => {
            try {
                await formInline.value.validate()
                let params = Object.assign({}, formData.value, resObj.value)

                let res
                if (props.beforeConfirm) {
                    //自定义的确认操作
                    res = await props.beforeConfirm(params)
                }
                emit('confirm', res)
            } catch (error) {
                console.log('表单验证失败', error)
            }
        }

        const bankList = ref<LabelValueOptions>([])
        const getData = async () => {
            bankList.value = (await dictionaryDataStore().setDictionaryData('ownedBank')) as any[]
        }
        getData()
        return {
            bankList,
            body: document.body,
            formInline,
            moment,
            formData,
            modalClose,
            modalConfirm,

            cardinalArr,
            rules,
            myOptions,
        }
    },
})
</script>

<style scoped lang="less">
.row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 5px 0;
    .cell {
        width: 25%;
        display: flex;
        .label {
            width: 100px;
            &:after {
                content: '：';
            }
        }
    }
}
.cardinality {
    width: 100%;
    font-size: 16px;
    font-weight: 800;
    margin: 0 10px 20px 10px;
}
.formBox :deep(.ant-form-item .ant-form-item-label) {
    label {
        width: 100%;
        display: inline-block;
        overflow: hidden;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
</style>
