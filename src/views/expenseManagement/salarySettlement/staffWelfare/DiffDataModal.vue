<template>
    <BasicEditModalSlot
        :visible="visible"
        @cancel="modalClose"
        :footer="null"
        :destroyOnClose="true"
        title="查看补差"
        width="1200px"
    >
        <Button type="primary" @click="exportData">导出</Button>
        <BasicTable
            ref="tableRef"
            api="/api/hr-welfare-compensations/welfare-compensation-record"
            :columns="columns"
            size="small"
            :rowSelection="false"
            :params="{ ...params, ...tableParams }"
            :sorter="false"
            :isPage="false"
            :sroll="{
                x: 100,
                y: 600,
            }"
            :rowClassName="(record) => (record.isSummary ? 'darkRow' : '')"
            :tableDataFormat="tableDataFormat"
        />
        <p>
            <BellFilled style="color: #6894fe; margin-right: 4px; font-size: 15px" />
            负数表示需要退还的金额，
            <span style="color: red">正数</span>
            表示需要
            <span style="color: red">补缴</span>
            的金额
        </p>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { BellFilled } from '@ant-design/icons-vue'
import downFile from '/@/utils/downFile'
import { plus } from 'number-precision'

export default defineComponent({
    components: { BellFilled },
    setup() {
        const visible = ref(false)
        const params = ref({})
        const tableParams = ref({})
        const tableRef = ref()
        const show = (e) => {
            params.value = e
            visible.value = true
        }

        const modalClose = () => {
            visible.value = false
        }

        const columns = [
            // {
            //     title: '账单标题',
            //     dataIndex: 'title',
            //     width: 150,
            //     fixed: 'left',
            //     ellipsis: true,
            // },
            // {
            //     title: '单位名称',
            //     dataIndex: 'clientName',
            //     width: 150,
            //     fixed: 'left',
            // },
            {
                title: '缴费年月',
                dataIndex: 'paymentDate',
                width: 150,
            },
            {
                title: '总计',
                dataIndex: 'total',
                width: 150,
            },
            {
                title: '单位缴纳部分',
                children: [
                    {
                        title: '养老',
                        dataIndex: 'unitPension',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '失业',
                        dataIndex: 'unitUnemployment',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '医疗',
                        dataIndex: 'unitMedical',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '生育',
                        dataIndex: 'unitMaternity',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '工伤',
                        dataIndex: 'unitInjury',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '小计',
                        dataIndex: 'unitSubtotal',
                        width: 100,
                        align: 'center',
                    },
                ],
            },
            {
                title: '个人缴纳部分',
                children: [
                    {
                        title: '养老',
                        dataIndex: 'personalPension',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '失业',
                        dataIndex: 'personalUnemployment',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '医疗',
                        dataIndex: 'personalMedical',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '生育',
                        dataIndex: 'personalMaternity',
                        width: 100,
                        align: 'center',
                    },
                    {
                        title: '小计',
                        dataIndex: 'personalSubtotal',
                        width: 100,
                        align: 'center',
                    },
                ],
            },
            {
                title: '社保总计',
                dataIndex: 'socialSecurityTotal',
                width: 130,
            },
            {
                title: '住房公积金',
                children: [
                    {
                        title: '单位',
                        dataIndex: 'unitAccumulationFund',
                        width: 150,
                        align: 'center',
                    },
                    {
                        title: '个人',
                        dataIndex: 'personalAccumulationFund',
                        width: 150,
                        align: 'center',
                    },
                ],
            },
            {
                title: '公积金总计',
                dataIndex: 'accumulationFundTotal',
                width: 130,
            },
            {
                title: '是否使用',
                dataIndex: 'isUsedStr',
                width: 120,
                fixed: 'right',
            },
        ]

        const exportData = () => {
            downFile('post', '/api/hr-welfare-compensations/export', '', { ...params.value, ...tableParams.value })
        }

        const tableDataFormat = (data) => {
            let total = {
                id: new Date().getTime(),
                isSummary: true,
                paymentDate: '合计',
                total: 0,
                unitPension: 0,
                unitUnemployment: 0,
                unitMedical: 0,
                unitInjury: 0,
                unitSubtotal: 0,
                personalPension: 0,
                personalUnemployment: 0,
                personalMedical: 0,
                personalSubtotal: 0,
                socialSecurityTotal: 0,
                unitAccumulationFund: 0,
                personalAccumulationFund: 0,
                accumulationFundTotal: 0,
            }
            data.forEach((i) => {
                total.total = plus(total.total, i.total ?? 0)
                total.unitPension = plus(total.unitPension, i.unitPension ?? 0)
                total.unitUnemployment = plus(total.unitUnemployment, i.unitUnemployment ?? 0)
                total.unitMedical = plus(total.unitMedical, i.unitMedical ?? 0)
                total.unitInjury = plus(total.unitInjury, i.unitInjury ?? 0)
                total.unitSubtotal = plus(total.unitSubtotal, i.unitSubtotal ?? 0)
                total.personalPension = plus(total.personalPension, i.personalPension ?? 0)
                total.personalUnemployment = plus(total.personalUnemployment, i.personalUnemployment ?? 0)
                total.personalMedical = plus(total.personalMedical, i.personalMedical ?? 0)
                total.personalSubtotal = plus(total.personalSubtotal, i.personalSubtotal ?? 0)
                total.socialSecurityTotal = plus(total.socialSecurityTotal, i.socialSecurityTotal ?? 0)
                total.unitAccumulationFund = plus(total.unitAccumulationFund, i.unitAccumulationFund ?? 0)
                total.personalAccumulationFund = plus(total.personalAccumulationFund, i.personalAccumulationFund ?? 0)
                total.accumulationFundTotal = plus(total.accumulationFundTotal, i.accumulationFundTotal ?? 0)
            })
            return data.length ? [...data, total] : data
        }
        return {
            tableDataFormat,
            exportData,
            tableRef,
            BellFilled,
            visible,
            params,
            show,
            modalClose,
            columns,
            tableParams,
            tableOptions: [
                {
                    label: '是否使用',
                    key: 'isUsedStr',
                    type: 'select',
                    options: [
                        {
                            label: '未使用',
                            value: 0,
                        },
                        {
                            label: '已使用',
                            value: 1,
                        },
                    ],
                },
            ],
        }
    },
})
</script>

<style scoped lang="less"></style>
