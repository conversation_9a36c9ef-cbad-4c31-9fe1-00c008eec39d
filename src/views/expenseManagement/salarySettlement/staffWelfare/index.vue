<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button v-auth="'staffWelfare_edit'" type="primary" @click="updateSome">{{ updateText }}</Button>
        <Button v-auth="'staffWelfare_edit'" type="primary" @click="importSome">导入</Button>
        <Button type="primary" @click="exportExcel">{{ exportText }}</Button>
    </div>
    <BasicTable
        ref="talbeRef"
        api="/api/employee-welfare/page"
        exportUrl="/api/employee-welfare/export"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <!-- 批量修改 -->
    <!-- <BasicEditModal
        :visible="showUpdate"
        title="批量修改"
        :item="currentValue"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :options="updateOptions"
        :beforeConfirm="beforeConfirm"
    /> -->
    <BatchRdit
        v-model:visible="showUpdate"
        title="批量修改"
        :typeParams="typeParams"
        @cancel="modalCancel"
        @confirm="modalConfirm"
        :beforeConfirm="beforeConfirm"
    />

    <!-- 福利补差 -->
    <BasicEditModalSlot :visible="showTable" title="福利补差" @cancel="tableClose" width="1250px" :destroyOnClose="true">
        <div class="tip">
            <ExclamationCircleFilled style="font-size: 18px" />
            缴费年月发生变更，产生的差额将影响本期账单核算，是否继续？
        </div>

        <SearchBar v-model="tableParams" :options="tableOptions" @change="searchDiff" />
        <Button type="primary" @click="exportData">导出</Button>
        <BasicTable
            ref="diffTableRef"
            api="/api/hr-welfare-compensations/page"
            :params="{
                ids: currentDiffIds,
                ...tableParams,
            }"
            size="small"
            :scroll="{
                x: '100',
                y: '400',
            }"
            :columns="tableColumns"
            :rowSelectionShow="false"
        />
        <p>
            <BellFilled style="color: #6894fe; margin-right: 4px; font-size: 15px" />
            负数表示需要退还的金额，
            <span style="color: red">正数</span>
            表示需要
            <span style="color: red">补缴</span>
            的金额
        </p>
        <template #footer>
            <Button type="primary" danger :loading="diffLoading" @click="diffCancel">补差取消</Button>
            <Button type="primary" :loading="diffLoading" @click="tableClose">补差确认</Button>
        </template>
    </BasicEditModalSlot>

    <!-- 编辑 -->
    <DetailModal :visible="showDetail" :currentItem="currentItem" @cancel="detailClose" @confirm="detailConfirm" />

    <!-- 提示 -->
    <BasicEditModalSlot v-model:visible="showTip" title="批量修改提示" :footer="null" width="600px">
        <div class="tipMain">
            <img src="~//@/assets/tip.jpeg" alt="" />
            <p>当前所选员工的社保基数、医保基数、公积金基数不统一 无法批量修改，请重新选择！</p>
            <Button @click="showTip = false" type="primary" size="large">我知道了</Button>
        </div>
    </BasicEditModalSlot>

    <ImportModal
        v-model:visible="importVisible"
        temUrl="/api/employee-welfare/template"
        importUrl="/api/employee-welfare/import"
        @getResData="searchData"
    />
    <div class="typeBox">
        <BasicEditModalSlot
            v-model:visible="showType"
            class="typeModal"
            title="请选择社保类型"
            @cancel="closeType"
            @confirm="confirmType"
            width="600px"
            :isOverflow="'unset'"
        >
            <SearchBar v-model="typeParams" :showSelectedLine="false" :options="typeOptions" @change="typeChange" />
        </BasicEditModalSlot>
    </div>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { computed, defineComponent, ref } from 'vue'
import { ExclamationCircleFilled, BellFilled } from '@ant-design/icons-vue'
import DetailModal from './DetailModal.vue'
import { getAccumulationFundTypeList, getSocialSecurityTypeList, getStaffStatusList, getStaffTypeList } from '/@/utils/api'
import { izInsuredList } from '/@/utils/dictionaries'
import moment from 'moment'
import request from '/@/utils/request'
import { getHaveAuthorityOperation, getDynamicText, isEmptyNull } from '/@/utils'
import BatchRdit from './batchEdit.vue'

export default defineComponent({
    name: 'StaffWelfare',
    components: { ExclamationCircleFilled, DetailModal, BellFilled, BatchRdit },
    setup() {
        const talbeRef = ref()
        const params = ref({
            name: undefined,
        })
        const typeParams = ref({
            socialSecurityId: undefined,
        })
        const showType = ref(false)
        const selArr = ref<Recordable[]>([])
        const selectedRowsArr = (arr) => {
            selArr.value = arr
        }
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selArr.value)
        })
        const updateText = computed(() => {
            return getUpdateText('修改')
        })

        // 编辑后产生的补差ids
        const currentDiffIds = ref([])

        const showUpdate = ref(false)
        const showTip = ref(false)
        const showTable = ref(false)
        const currentValue = ref({
            socialSecurityCardinal: undefined,
            medicalInsuranceCardinal: undefined,
            accumulationFundCardinal: undefined,
        })

        const getUpdateText = (str) => {
            let exportStr = ''
            if (
                Object.values(params.value).filter((el: any) => {
                    if (Array.isArray(el)) return el.length > 0
                    else return !isEmptyNull(el) && el !== ''
                }).length > 0
            ) {
                exportStr = `${str}筛选内容`
                if (selArr.value.length > 0) exportStr = `${str}选中内容`
            } else if (selArr.value.length > 0) exportStr = `${str}选中内容`
            else exportStr = `批量${str}`
            return exportStr
        }

        const closeType = () => {
            showType.value = false
            typeParams.value.socialSecurityId = undefined
        }
        const confirmType = () => {
            if (!typeParams?.value?.socialSecurityId) {
                message.warning('请选择社保类型')
                return
            }
            showType.value = false
            showUpdate.value = true
        }
        const typeChange = (val) => {
        }
        const updateSome = () => {
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            if (updateText.value.indexOf('批量') != -1) {
                message.warning('请筛选或选择要修改的内容')
                return
            }
            showType.value = true
            // showUpdate.value = true
            // currentValue.value = {
            //     socialSecurityCardinal: undefined,
            //     medicalInsuranceCardinal: undefined,
            //     accumulationFundCardinal: undefined,
            // }
        }
        const beforeConfirm = async (form) => {
            let postBody = {}
            Object.keys(params.value).forEach((el) => {
                if (el in form) postBody[`${el}Search`] = params.value[el]
                else postBody[el] = params.value[el]
            })
            return new Promise(async (resolve, reject) => {
                try {
                    const res = await request.post(`/api/employee-welfare/batch-update`, {
                        ...form,
                        staffIds: selArr.value.map((i) => i.id),
                        ...postBody,
                    })
                    resolve(res)
                } catch (error) {
                    reject(new Error('confirm error'))
                }
            })
        }
        const modalCancel = () => {
            showUpdate.value = false
        }
        const modalConfirm = (form, arr) => {
            showUpdate.value = false
            talbeRef.value.refresh()
            if (arr?.length) {
                currentDiffIds.value = arr
                showTable.value = true
            }
        }

        const diffLoading = ref(false)
        const tableClose = async () => {
            diffLoading.value = true
            try {
                showTable.value = false
                await request.post(`/api/hr-welfare-compensations/confirm`, currentDiffIds.value)
                currentDiffIds.value = []
                talbeRef.value.refresh()
            } finally {
                diffLoading.value = false
            }
        }
        const diffCancel = async () => {
            diffLoading.value = true
            try {
                await request.post(`/api/hr-welfare-compensations/deletes`, currentDiffIds.value)
                message.success('补差取消成功！')
                showTable.value = false
                currentDiffIds.value = []
                talbeRef.value.refresh()
            } finally {
                diffLoading.value = false
            }
        }

        const exportExcel = () => {
            talbeRef.value.exportRow('ids', exportText.value, params.value)
        }

        const showDetail = ref(false)
        const currentItem = ref<any>(null)
        const editRow = (record) => {
            currentItem.value = { ...record }
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
        }
        const detailConfirm = (arr) => {
            showDetail.value = false
            talbeRef.value.refresh()
            if (arr?.length) {
                currentDiffIds.value = arr
                showTable.value = true
            }
        }

        const searchData = () => {
            talbeRef.value.refresh(1)
        }

        const importVisible = ref(false)
        const importSome = () => {
            importVisible.value = true
        }

        const tableParams = ref<Recordable>({})

        const diffTableRef = ref()
        const searchDiff = () => {
            diffTableRef.value.refresh(1)
        }

        const tableColumns = [
            {
                title: '单位编号',
                dataIndex: 'unitNumber',
                width: 150,
                fixed: 'left',
            },
            {
                title: '单位名称',
                dataIndex: 'clientName',
                width: 150,
                fixed: 'left',
            },
            {
                title: '姓名',
                dataIndex: 'staffName',
                width: 110,
                fixed: 'left',
            },
            {
                title: '证件号码',
                dataIndex: 'certificateNum',
                width: 190,
                fixed: 'left',
            },
            {
                title: '缴费年月',
                dataIndex: 'paymentDate',
                width: 150,
            },
            {
                title: '个人社保编号',
                dataIndex: 'socialSecurityNum',
                width: 180,
            },
            {
                title: '个人医保编号',
                dataIndex: 'medicalInsuranceNum',
                width: 180,
            },
            {
                title: '单位缴纳部分',
                sorter: false,
                children: [
                    {
                        title: '养老',
                        dataIndex: 'unitPension',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '失业',
                        dataIndex: 'unitUnemployment',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '医疗',
                        dataIndex: 'unitMedical',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '工伤',
                        dataIndex: 'unitInjury',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '生育',
                        dataIndex: 'unitMaternity',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '小计',
                        dataIndex: 'unitSubtotal',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                ],
            },
            {
                title: '个人缴纳部分',
                sorter: false,
                children: [
                    {
                        title: '养老',
                        dataIndex: 'personalPension',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '失业',
                        dataIndex: 'personalUnemployment',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '医疗',
                        dataIndex: 'personalMedical',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '生育',
                        dataIndex: 'personalMaternity',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '小计',
                        dataIndex: 'personalSubtotal',
                        width: 100,
                        align: 'center',
                        sorter: true,
                    },
                ],
            },
            {
                title: '社保总计',
                dataIndex: 'socialSecurityTotal',
                width: 130,
            },
            {
                title: '个人公积金编号',
                dataIndex: 'accumulationFundNum',
                width: 180,
            },
            {
                title: '住房公积金',
                sorter: false,
                children: [
                    {
                        title: '单位',
                        dataIndex: 'unitAccumulationFund',
                        width: 150,
                        align: 'center',
                        sorter: true,
                    },
                    {
                        title: '个人',
                        dataIndex: 'personalAccumulationFund',
                        width: 150,
                        align: 'center',
                        sorter: true,
                    },
                ],
            },
            {
                title: '公积金总计',
                dataIndex: 'accumulationFundTotal',
                width: 130,
            },
        ]

        const exportData = () => {
            // TODO 导出
        }
        return {
            tableData,
            updateText,
            exportText,
            exportData,
            currentDiffIds,
            diffLoading,
            diffCancel,
            diffTableRef,
            searchDiff,
            tableParams,
            tableOptions: [
                {
                    label: '单位编号',
                    key: 'unitNumber',
                },
                {
                    label: '单位名称',
                    key: 'clientName',
                },
                {
                    label: '员工姓名',
                    key: 'staffName',
                },
                {
                    label: '证件号码',
                    key: 'certificateNum',
                },
                {
                    label: '缴费年月',
                    key: 'paymentDate',
                    type: 'month',
                },
            ],
            importSome,
            currentValue,
            importVisible,
            talbeRef,
            searchData,
            showTip,
            currentItem,
            showDetail,
            detailClose,
            detailConfirm,
            showTable,
            tableClose,
            beforeConfirm,
            selectedRowsArr,
            showUpdate,
            updateSome,
            exportExcel,
            editRow,
            params,
            modalCancel,
            modalConfirm,
            tableColumns,
            showType,
            closeType,
            confirmType,
            typeParams,
            typeChange,
            updateOptions: [
                {
                    label: '单位养老基数',
                    name: 'unitPensionCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '单位失业基数',
                    name: 'unitUnemploymentCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '单位医疗基数',
                    name: 'medicalInsuranceCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '单位生育基数',
                    name: 'unitMaternityCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '单位工伤基数',
                    name: 'workInjuryCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '单位大额医疗费用',
                    name: 'unitLargeMedicalExpense',
                    type: 'number',
                    required: false,
                },
                {
                    label: '补充工伤费用',
                    name: 'replenishWorkInjuryExpense',
                    type: 'number',
                    required: false,
                },
                {
                    label: '个人医疗基数',
                    name: 'medicalInsuranceCardinalPersonal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '个人养老基数',
                    name: 'personalPensionCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '个人失业基数',
                    name: 'personalUnemploymentCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '个人生育基数',
                    name: 'personalMaternityCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '个人大额医疗费用',
                    name: 'personalLargeMedicalExpense',
                    type: 'number',
                    required: false,
                },
                {
                    label: '公积金基数',
                    name: 'accumulationFundCardinal',
                    type: 'number',
                    required: false,
                },
                {
                    label: '缴费年月',
                    name: 'paymentDate',
                    type: 'month',
                    attrs: {
                        disabledDate: (current) => current && current >= moment(),
                    },
                    required: false,
                },
            ],
            myOperation: getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'staffWelfare_edit',
                    show: true,
                    click: editRow,
                },
            ]),
            columns: [
                {
                    title: '客户名称',
                    dataIndex: 'clientName',
                    align: 'center',
                    width: 170,
                },
                {
                    title: '姓名',
                    dataIndex: 'name',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '身份证号',
                    dataIndex: 'certificateNum',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '社保类型',
                    dataIndex: 'socialSecurityName',
                    align: 'center',
                    width: 170,
                },
                {
                    title: '公积金类型',
                    dataIndex: 'typeName',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '员工类型',
                    dataIndex: 'personnelTypeStr',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '员工状态',
                    dataIndex: 'staffStatusStr',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '单位养老基数',
                    dataIndex: 'unitPensionCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '单位失业基数',
                    dataIndex: 'unitUnemploymentCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '单位工伤基数',
                    dataIndex: 'workInjuryCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '单位医疗基数',
                    dataIndex: 'medicalInsuranceCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '单位生育基数',
                    dataIndex: 'unitMaternityCardinal',
                    align: 'center',
                    width: 125,
                },

                {
                    title: '个人养老基数',
                    dataIndex: 'personalPensionCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '个人失业基数',
                    dataIndex: 'personalUnemploymentCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '个人医疗基数',
                    dataIndex: 'medicalInsuranceCardinalPersonal',
                    align: 'center',
                    width: 125,
                },

                {
                    title: '个人生育基数',
                    dataIndex: 'personalMaternityCardinal',
                    align: 'center',
                    width: 125,
                },
                {
                    title: '公积金基数',
                    dataIndex: 'accumulationFundCardinal',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '缴费年月',
                    dataIndex: 'paymentDate',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '福利状态',
                    dataIndex: 'izInsuredStr',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 120,
                    fixed: 'right',
                    slots: { customRender: 'operation' },
                },
            ],
            searchOptions: [
                {
                    label: '客户名称',
                    key: 'clientIds',
                    type: 'clientSelectTree',
                    multiple: true,
                    checkStrictly: false,
                },
                {
                    label: '姓名',
                    key: 'name',
                },
                {
                    label: '身份证号',
                    key: 'certificateNum',
                },
                {
                    label: '社保类型',
                    key: 'socialSecurityIds',
                    type: 'select',
                    multiple: true,
                    options: [],
                    getMethod: getSocialSecurityTypeList,
                },
                {
                    label: '公积金类型',
                    key: 'accumulationFundIds',
                    type: 'select',
                    multiple: true,
                    options: [],
                    getMethod: getAccumulationFundTypeList,
                },
                {
                    label: '员工类型',
                    key: 'personnelType',
                    type: 'select',
                    options: [],
                    getMethod: getStaffTypeList,
                },
                {
                    label: '员工状态',
                    key: 'staffStatusS',
                    type: 'select',
                    multiple: true,
                    options: [],
                    getMethod: getStaffStatusList,
                },
                {
                    label: '单位养老基数',
                    key: 'unitPensionCardinalSearch',
                },
                {
                    label: '单位失业基数',
                    key: 'unitUnemploymentCardinalSearch',
                },
                {
                    label: '单位生育基数',
                    key: 'unitMaternityCardinalSearch',
                },
                {
                    label: '单位工伤基数',
                    key: 'workInjuryCardinalSearch',
                },
                {
                    label: '个人养老基数',
                    key: 'personalPensionCardinalSearch',
                },
                {
                    label: '个人失业基数',
                    key: 'personalUnemploymentCardinalSearch',
                },
                {
                    label: '个人生育基数',
                    key: 'personalMaternityCardinalSearch',
                },
                {
                    label: '公积金基数',
                    key: 'accumulationFundCardinalSearch',
                },
                {
                    label: '单位医疗基数',
                    key: 'medicalInsuranceCardinalSearch',
                },
                {
                    label: '个人医疗基数',
                    key: 'medicalInsuranceCardinalPersonalSearch',
                },
                {
                    label: '缴费年月',
                    key: 'paymentDateSearch',
                    type: 'month',
                },
                {
                    label: '福利状态',
                    key: 'izInsureds',
                    multiple: true,
                    type: 'select',
                    options: izInsuredList,
                },
            ],
            typeOptions: [
                {
                    label: '社保类型',
                    key: 'socialSecurityId',
                    type: 'select',
                    multiple: false,
                    dropdownStyle: { zIndex: 5, right: 0 },
                    options: [],
                    getMethod: getSocialSecurityTypeList,
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.tip {
    padding: 15px 0;
    color: #6894fe;
    background: rgb(218, 229, 255);
    border: 1px solid #6894fe;
    text-align: center;
    border-radius: @border-radius-base;
    margin-bottom: 15px;
}
.tipMain {
    width: 60%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    & > * {
        margin-bottom: 20px;
    }
    img,
    button {
        width: 100%;
    }
}
.hh {
    height: 40px;
}
</style>
