<template>
    <BasicEditModalSlot :visible="visible" title="编辑" @cancel="modalClose" width="1300px">
        <Card title="基本信息" size="small" :headStyle="{ background: 'rgba(104, 148, 254, 100)', color: 'white' }">
            <div class="row">
                <div class="cell">
                    <div class="label">客户编号</div>
                    <div class="val">{{ formData.unitNumber }}</div>
                </div>
                <div class="cell">
                    <div class="label">客户名称</div>
                    <div class="val">{{ formData.clientName }}</div>
                </div>
                <div class="cell">
                    <div class="label">所属公司</div>
                    <div class="val">{{ formData.parentClientName }}</div>
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="label">系统编号</div>
                    <div class="val">{{ formData.systemNum }}</div>
                </div>
                <div class="cell">
                    <div class="label">姓名</div>
                    <div class="val">{{ formData.name }}</div>
                </div>
                <div class="cell">
                    <div class="label">性别</div>
                    <div class="val">{{ formData.sexStr }}</div>
                </div>
                <div class="cell">
                    <div class="label">出生日期</div>
                    <div class="val">{{ formData.birthday }}</div>
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="label">年龄</div>
                    <div class="val">{{ formData.age }}</div>
                </div>
                <div class="cell">
                    <div class="label">身份证号</div>
                    <div class="val">{{ formData.certificateNum }}</div>
                </div>
                <div class="cell">
                    <div class="label">手机号码</div>
                    <div class="val">{{ formData.phone }}</div>
                </div>
                <div class="cell">
                    <div class="label">员工状态</div>
                    <div class="val">{{ formData.staffStatusStr }}</div>
                </div>
            </div>
            <div class="row">
                <div class="cell">
                    <div class="label">福利状态</div>
                    <div class="val">{{ formData.izInsuredStr }}</div>
                </div>
            </div>
        </Card>
        <Card
            title="公积金"
            size="small"
            :headStyle="{ background: 'rgba(104, 148, 254, 100)', color: 'white' }"
            style="margin: 10px 0"
        >
            <Table
                :rowKey="(record) => record.id"
                :data-source="[formData]"
                :columns="accumulationColumns"
                size="small"
                :pagination="false"
                :bordered="true"
            />
        </Card>
        <Card
            title="社保"
            size="small"
            :headStyle="{ background: 'rgba(104, 148, 254, 100)', color: 'white' }"
            style="margin: 10px 0"
        >
            <Table
                :rowKey="(record) => record.id"
                :data-source="[formData]"
                :columns="socialSecurityColumns"
                size="small"
                :pagination="false"
                :bordered="true"
            />
        </Card>
        <Form ref="formInline" :model="formData" :label-col="{ span: 9 }" :wrapper-col="{ span: 14 }">
            <Row>
                <Col span="6">
                    <FormItem label="基础工资">
                        <Input v-model:value="formData.basicWage" placeholder="基础工资" />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="应发工资">
                        <Input v-model:value="formData.salary" placeholder="应发工资" />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="工资银行">
                        <Select
                            v-model:value="formData.ownedBank"
                            placeholder="工资银行"
                            :options="bankList"
                            showSearch
                            allowClear
                            optionFilterProp="label"
                        />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="工资卡号">
                        <Input v-model:value="formData.salaryCardNum" placeholder="工资卡号" />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="6">
                    <FormItem label="个人社保编号">
                        <Input v-model:value="formData.socialSecurityNum" placeholder="个人社保编号" />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="个人医保编号">
                        <Input v-model:value="formData.medicalInsuranceNum" placeholder="个人医保编号" />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="个人公积金编号">
                        <Input v-model:value="formData.accumulationFundNum" placeholder="个人公积金编号" />
                    </FormItem>
                </Col>
            </Row>
            <div class="cardinality">相同基数/费用</div>
            <Row>
                <Col span="6">
                    <FormItem
                        label="公积金基数"
                        name="accumulationFundCardinal"
                        :rules="{ required: true, type: 'number', message: '请输入公积金基数', trigger: 'blur' }"
                    >
                        <InputNumber
                            v-model:value.number="formData.accumulationFundCardinal"
                            placeholder="公积金基数"
                            style="width: 100%"
                        />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="工龄工资基数">
                        <InputNumber v-model:value="formData.seniorityWageBase" placeholder="工龄工资基数" style="width: 100%" />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem label="当前工龄工资">
                        <Input
                            disabled
                            :value="
                                formData.seniorityWageBase && formData.workingYears
                                    ? formData.seniorityWageBase * formData.workingYears
                                    : '-'
                            "
                            placeholder="当前工龄工资"
                            style="width: 85%"
                        />
                        <Popover trigger="hover" placement="topRight" :getPopupContainer="() => body">
                            <QuestionCircleOutlined style="margin-left: 10px" />
                            <template #content>
                                <p>例： 入职时间：2018-09-01 工龄：3年</p>
                                <p>当前工龄工资 = 工龄工资基数 x 工龄 = 20 x 3 = 60</p>
                            </template>
                        </Popover>
                    </FormItem>
                </Col>
            </Row>
            <div v-if="cardinalArr.length > 0" class="formBox">
                <div class="cardinality">基数/费用</div>
                <Row v-if="cardinalArr.length > 0">
                    <Col span="12" v-for="(item, index) in cardinalArr" :key="index">
                        <FormItem
                            :label="item.title"
                            :name="item.dataIndexS[0]"
                            :rules="{
                                required: true,
                                type: 'number',
                                message: `请输入${item.title}`,
                                trigger: ['blur', 'change'],
                            }"
                            :labelCol="{ span: 8 }"
                            :wrapperCol="{ span: 16 }"
                        >
                            <InputNumber
                                v-model:value.number="formData[item.dataIndexS[0]]"
                                :placeholder="`请输入${item.title}`"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                </Row>
            </div>
            <div v-if="cardinalArr.length == 0">
                <div class="cardinality">单位基数/费用</div>
                <Row>
                    <Col span="6">
                        <FormItem
                            label="单位养老基数"
                            name="unitPensionCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入单位养老基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.unitPensionCardinal"
                                placeholder="单位养老基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位失业基数"
                            name="unitUnemploymentCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入单位失业基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.unitUnemploymentCardinal"
                                placeholder="单位失业基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位医疗基数"
                            name="medicalInsuranceCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入单位医疗基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.medicalInsuranceCardinal"
                                placeholder="单位医疗基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位生育基数"
                            name="unitMaternityCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入单位生育基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.unitMaternityCardinal"
                                placeholder="单位生育基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位工伤基数"
                            name="workInjuryCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入单位工伤基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.workInjuryCardinal"
                                placeholder="单位工伤基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位大额医疗费用"
                            name="unitLargeMedicalExpense"
                            :rules="{ required: false, type: 'number', message: '请输入单位大额医疗费用', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.unitLargeMedicalExpense"
                                placeholder="单位大额医疗费用"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="补充工伤费用"
                            name="replenishWorkInjuryExpense"
                            :rules="{ required: false, type: 'number', message: '请输入补充工伤费用', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.replenishWorkInjuryExpense"
                                placeholder="补充工伤费用"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="单位企业年金"
                            name="unitEnterpriseAnnuity"
                            :rules="{ required: false, type: 'number', message: '请输入单位企业年金', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.unitEnterpriseAnnuity"
                                placeholder="单位企业年金"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="商业保险"
                            name="commercialInsurance"
                            :rules="{ required: false, type: 'number', message: '请输入商业保险', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.commercialInsurance"
                                placeholder="单位商业保险"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <div class="cardinality">个人基数/费用</div>
                <Row>
                    <Col span="6">
                        <FormItem
                            label="个人养老基数"
                            name="personalPensionCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入个人养老基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.personalPensionCardinal"
                                placeholder="个人养老基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="个人失业基数"
                            name="personalUnemploymentCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入个人失业基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.personalUnemploymentCardinal"
                                placeholder="个人失业基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="个人医疗基数"
                            name="medicalInsuranceCardinalPersonal"
                            :rules="{ required: false, type: 'number', message: '请输入个人医疗基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.medicalInsuranceCardinalPersonal"
                                placeholder="个人医疗基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="个人大额医疗费用"
                            name="personalLargeMedicalExpense"
                            :rules="{ required: false, type: 'number', message: '请输入个人大额医疗费用', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.personalLargeMedicalExpense"
                                placeholder="个人大额医疗费用"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="个人生育基数"
                            name="personalMaternityCardinal"
                            :rules="{ required: true, type: 'number', message: '请输入个人生育基数', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.personalMaternityCardinal"
                                placeholder="个人生育基数"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                    <Col span="6">
                        <FormItem
                            label="个人企业年金"
                            name="personalEnterpriseAnnuity"
                            :rules="{ required: true, type: 'number', message: '个人企业年金', trigger: 'blur' }"
                        >
                            <InputNumber
                                v-model:value.number="formData.personalEnterpriseAnnuity"
                                placeholder="个人企业年金"
                                style="width: 100%"
                            />
                        </FormItem>
                    </Col>
                </Row>
            </div>

            <div class="cardinality">缴费年月</div>
            <Row stytle="width:90%">
                <Col span="6">
                    <FormItem label="缴费年月">
                        <MonthPicker
                            v-model:value="formData.paymentDate"
                            :disabled-date="(current) => current && current >= moment()"
                            placeholder="缴费年月"
                            format="YYYY-MM"
                            valueFormat="YYYY-MM"
                        />
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem
                        label="是否计算补缴"
                        name="supplementaryPayment"
                        :rules="{ required: true, type: 'number', message: '请选择是否计算补缴', trigger: 'blur' }"
                    >
                        <Select
                            v-model:value="formData.supplementaryPayment"
                            placeholder="是否计算补缴"
                            :disabled="true"
                            :options="supplementaryPaymentOptions"
                            allowClear
                            optionFilterProp="label"
                        />
                    </FormItem>
                </Col>
            </Row>
        </Form>
        <template #footer>
            <Button type="primary" style="float: left" @click="showDiff">查看补差</Button>
            <Button @click="modalClose">取消</Button>
            <Button type="primary" @click="modalConfirm">确认</Button>
        </template>
    </BasicEditModalSlot>
    <DiffDataModal ref="diffDataModal" />
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, computed } from 'vue'
import { Card, MonthPicker, Popover } from 'ant-design-vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import moment from 'moment'
import request from '/@/utils/request'
import dictionaryDataStore from '/@/store/modules/dictionaryData'
import DiffDataModal from './DiffDataModal.vue'
import lodash from 'lodash'
import { getMaxListeners, title } from 'process'
import ParentAgreementList from '/@/views/customer/customerInfo/component/parentAgreementList.vue'

export default defineComponent({
    name: 'DetailModal',
    components: { Card, MonthPicker, Popover, QuestionCircleOutlined, DiffDataModal },
    props: {
        visible: Boolean,
        currentItem: Object,
    },
    emits: ['cancel', 'confirm', 'showDiff'],
    setup(props, { emit }) {
        const { visible, currentItem } = toRefs(props)
        const formInline = ref()
        const formData = ref<any>({})
        watch(visible, async () => {
            visible.value && currentItem.value && (await getList())
            formData.value = currentItem.value
            visible.value && currentItem.value && updateCardinal()
        })
        const specialArr = ref([
            'unitLargeMedicalExpense',
            'personalLargeMedicalExpense',
            'replenishWorkInjuryExpense',
            'commercialInsurance',
            'unitEnterpriseAnnuity',
            'personalEnterpriseAnnuity',
        ])
        const specialAndCardinal = ref([
            'unitLargeMedicalExpense',
            'personalLargeMedicalExpense',
            'replenishWorkInjuryExpense',
            'commercialInsurance',
            'unitEnterpriseAnnuity',
            'personalEnterpriseAnnuity',
            // --
            'unitPensionCardinal',
            'workInjuryCardinal',
            'unitUnemploymentCardinal',
            'medicalInsuranceCardinal',
            'unitMaternityCardinal',
            'personalPensionCardinal',
            'personalUnemploymentCardinal',
            'personalMaternityCardinal',
            'medicalInsuranceCardinalPersonal',
        ])
        const updateCardinal = () => {
            specialAndCardinal.value.forEach((key) => {
                cardinalArr.value = cardinalArr.value?.map((el) => {
                    el.dataIndexS.length > 1 &&
                        el.dataIndexS.forEach((row, index) => {
                            if (key === row) {
                                if (formData.value[row] !== null) {
                                    let idx = el.dataIndexS.findIndex((e) => formData.value[row] !== null)
                                    formData.value[row] = formData.value[el.dataIndexS[idx]]
                                }
                            }
                        })
                    return el
                })
            })
        }
        // 动态基数数组
        const cardinalArr = ref<any[]>([])
        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, { clientId: currentItem.value?.clientId })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = lodash.cloneDeep(arr)
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })

                return item
            })
            return resArr
        }
        const modalClose = () => {
            emit('cancel')
            formInline.value?.resetFields()
        }

        const resObj = computed(() => {
            let obj = {}
            cardinalArr.value?.map((el) => {
                for (const k in el) {
                    el.dataIndexS.forEach((row) => {
                        if (row === k) {
                            obj[row] = formData.value[el.dataIndexS[0]]
                        }
                    })
                }
                return el
            })
            return obj
        })

        const modalConfirm = async () => {
            try {
                await formInline.value.validate()
                let params = Object.assign({}, formData.value, resObj.value)
                const res = await request.post(`/api/employee-welfare/update`, { ...params })
                emit('confirm', res)
            } catch (error) {
                console.log('表单验证失败', error)
            }
        }

        const bankList = ref<LabelValueOptions>([])
        const getData = async () => {
            bankList.value = (await dictionaryDataStore().setDictionaryData('ownedBank')) as any[]
        }
        getData()

        const diffDataModal = ref()
        const showDiff = async () => {

            diffDataModal.value.show({
                staffId: currentItem.value?.id,
                clientId: currentItem.value?.clientId,
            })
        }

        return {
            diffDataModal,
            showDiff,
            bankList,
            body: document.body,
            formInline,
            moment,
            formData,
            modalClose,
            modalConfirm,

            cardinalArr,
            specialArr,

            accumulationColumns: [
                {
                    title: '公积金类型名称',
                    dataIndex: 'typeName',
                    align: 'center',
                },
                {
                    title: '地区',
                    dataIndex: 'area',
                    align: 'center',
                },
                {
                    title: '收款单位名称',
                    dataIndex: 'payeeName',
                    align: 'center',
                },
                {
                    title: '收款单位账号',
                    dataIndex: 'payeeAccount',
                    align: 'center',
                },
                {
                    title: '收款单位开户行',
                    dataIndex: 'payeeBank',
                    align: 'center',
                },
                {
                    title: '单位比例',
                    dataIndex: 'unitScalePercentage',
                    align: 'center',
                },
                {
                    title: '个人比例',
                    dataIndex: 'personageScalePercentage',
                    align: 'center',
                },
            ],
            supplementaryPaymentOptions: [
                { label: '不计算', value: 0 },
                { label: '计算', value: 1 },
            ],
            socialSecurityColumns: [
                {
                    title: '社保类型',
                    dataIndex: 'socialSecurityName',
                    align: 'center',
                },
                {
                    title: '地区',
                    dataIndex: 'socialSecurityArea',
                    align: 'center',
                },
                {
                    title: '收款单位名称',
                    dataIndex: 'nameOfBeneficiary',
                    align: 'center',
                },
                {
                    title: '收款单位账号',
                    dataIndex: 'receivingAccount',
                    align: 'center',
                },
                {
                    title: '收款单位开户行',
                    dataIndex: 'accountBank',
                    align: 'center',
                },
                {
                    title: '单位养老',
                    dataIndex: 'unitPensionPercentage',
                    align: 'center',
                },
                {
                    title: '单位医疗',
                    dataIndex: 'unitMedicalPercentage',
                    align: 'center',
                },
                {
                    title: '单位工伤',
                    dataIndex: 'workInjuryPercentage',
                    align: 'center',
                },
                {
                    title: '单位失业',
                    dataIndex: 'unitUnemploymentPercentage',
                    align: 'center',
                },
                {
                    title: '个人养老',
                    dataIndex: 'personalPensionPercentage',
                    align: 'center',
                },
                {
                    title: '个人医疗',
                    dataIndex: 'personalMedicalPercentage',
                    align: 'center',
                },
                {
                    title: '个人失业',
                    dataIndex: 'personalUnemploymentPercentage',
                    align: 'center',
                },
            ],
        }
    },
})
</script>

<style scoped lang="less">
.row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 5px 0;
    .cell {
        width: 25%;
        display: flex;
        .label {
            width: 100px;
            &:after {
                content: '：';
            }
        }
    }
}
.cardinality {
    width: 100%;
    font-size: 16px;
    font-weight: 800;
    margin: 0 10px 20px 10px;
}
.formBox :deep(.ant-form-item .ant-form-item-label) {
    label {
        width: 100%;
        display: inline-block;
        overflow: hidden;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
</style>
