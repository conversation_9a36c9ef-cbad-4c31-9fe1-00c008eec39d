<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'expense_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'expense_import'" @click="ImportData">导入</Button>
        <Button type="primary" v-auth="'expense_export'" @click="exportData">{{ exportText }}</Button>
        <Button v-auth="'expense_delete'" danger type="primary" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-expense-manages/page"
        deleteApi="/api/hr-expense-manages/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #quantity="{ record }">
            <Button type="link" size="small" @click="clickQuantity(record)">{{ record.clientNumber }}</Button>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :expenseTypeList="expenseTypeList"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />

    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'

import modal from './modal.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'ExpenseIndex',
    components: { MyModal: modal },
    setup() {
        //筛选
        const params = ref<{}>({
            typeName: null,
        })
        // 获取全部账号类型
        let expenseTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/expensetype', {}).then((res) => {
                expenseTypeList.value = res.map((item) => {
                    // console.log(item)
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const options: SearchBarOption[] = [
            {
                type: 'select',
                label: '类型',
                key: 'expenseTypeList',
                options: expenseTypeList,
                multiple: true,
            },
            {
                type: 'string',
                label: '名称',
                key: 'expenseName',
            },
            {
                type: 'clientSelectTree',
                label: '所属客户',
                key: 'clientIdList',
                placeholder: '所属客户',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '类型',
                dataIndex: 'expenseType',
                align: 'center',
                width: 350,
                customRender: ({ record }) => {
                    return record.expenseTypeName
                },
            },
            {
                title: '名称',
                dataIndex: 'expenseName',
                align: 'center',
                width: 350,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 350,
            },
            {
                title: '单位编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 150,
            },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增费用项')
        // 当前编辑的数据
        const currentValue = ref<any>(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增费用项'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑费用项'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增费用项'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 客户列表数据
        const customerValue = ref(null)
        // 客户列表显示隐藏
        const customerVisible = ref(false)
        // 点击客户数量
        const clickQuantity = (row) => {
            customerVisible.value = true
            customerValue.value = row
        }

        // 关闭客户列表
        const customerCancel = () => {
            customerVisible.value = false
        }
        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'expense_edit',
                    show: true,
                    click: editRow,
                },
            ]),
        )

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-expense-manages/template'
        const importUrl = '/api/hr-expense-manages/import'
        const exportUrl = '/api/hr-expense-manages/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }

        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            selectedRowsArr,
            exportText,
            onMounted,
            options,
            expenseTypeList,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            customerValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            clickQuantity,
            customerVisible,
            customerCancel,
            ImportData,
            exportData,
            //事件

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            //操作按钮
            myOperation,
            // myOperationClick,
        }
    },
})
</script>

<style scoped lang="less"></style>
