<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="500px">
        <Form ref="formInline" :model="formData" :rules="rules" class="form-flex">
            <template v-for="(i, index) in myOptions" :key="i">
                <MyFormItem :item="i" v-model:value="formData[i.name]">
                    <template #clientId>
                        <ClientSelectTree
                            v-model:value="formData[i.name]"
                            v-model:itemForm="myOptions[index]"
                            :renderInBody="true"
                        />
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'AccumulationModal',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        expenseTypeList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-expense-manages'
        const { title, item, visible, expenseTypeList } = toRefs(props)
        const selectChanged = (value: string) => {
            formData.value.expenseType = value
        }

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '类型',
                name: 'expenseType',
                type: 'change',
                options: expenseTypeList,
                ruleType: 'number',
                // onChange: selectChanged,
            },
            {
                label: '名称',
                name: 'expenseName',
            },
            {
                label: '所属客户',
                name: 'clientId',
                type: 'slots',
                slots: 'clientId',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                formData.value.unitScale = formData.value.unitScale ? formData.value.unitScale + '%' : ''
                formData.value.personageScale = formData.value.personageScale ? formData.value.personageScale + '%' : ''
                formData.value.expenseType = parseInt(formData?.value?.expenseType) || null
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formData.value = {
                ...formData.value,
                unitNumber: '',
                clientName: '',
            }
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', { ...formData.value })
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', { ...formData.value })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 80%;
    }
}
</style>
