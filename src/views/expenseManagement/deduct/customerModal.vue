<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" title="" :footer="null" :width="'800'">
        <BasicTable
            ref="tableRefCustomer"
            api="/api/hr-accumulation-funds/client-page"
            :useIndex="true"
            :rowSelectionShow="false"
            :params="params"
            :columns="columns"
            :tableDataList="tableDataList"
        />
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'

export default defineComponent({
    name: 'CustomerModal',
    props: {
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['cancel'],
    setup(props, { emit }) {
        const { item, visible } = toRefs(props)
        //  Data
        const tableDataList = ref([])
        const tableRefCustomer = ref()
        watch(visible, () => {
            if (visible.value) {
                nextTick(() => {
                    tableRefCustomer.value.refresh(1)
                })
            }
        })
        //筛选
        const params = ref<{}>({
            providentFundTypeId: null,
        })

        //表格数据
        const columns = [
            {
                title: '级数',
                align: 'center',
                dataIndex: 'quickDeductionSeries',
                sorter: true,
            },
            {
                title: '全月应缴纳所得额',
                dataIndex: 'maxPayTaxes',
                align: 'center',
            },
            {
                title: '税率',
                dataIndex: 'taxRate',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '速算扣除数',
                dataIndex: 'quickDeductionNumber',
                align: 'center',
            },
            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
            },
        ]

        // cancel handle
        const cancel = () => {
            emit('cancel')
        }

        return {
            tableRefCustomer,
            columns,
            params,
            tableDataList,
            cancel,
        }
    },
})
</script>
