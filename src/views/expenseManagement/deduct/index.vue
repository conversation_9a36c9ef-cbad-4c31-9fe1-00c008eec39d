<template>
    <div class="btns">
        <Button type="primary" v-auth="'deduct_import'" @click="showModal">导入</Button>
        <!-- 导入弹框弹出信息 -->
        <BasicEditModalSlot
            class="aaa"
            centered
            title="导入提示"
            v-model:visible="visible"
            @ok="handleOk"
            width="500px"
            ok-text="继续"
        >
            <div class="content">
                <div class="img-wrapper">
                    <img src="~//@/assets/hint.png" alt="" />
                </div>
                <p class="text">{{ modalText }}</p>
            </div>
        </BasicEditModalSlot>

        <Button type="primary" v-auth="'deduct_export'" @click="exportData">{{ exportText }}</Button>
    </div>
    <!-- 弹框 -->

    <BasicTable
        ref="tableRef"
        api="/api/hr-quick-deductions/page"
        deleteApi="/api/hr-quick-deductions/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    >
        <template #quantity="{ record }">
            <Button type="link" size="small" @click="clickQuantity(record)">{{ record.clientNumber }}</Button>
        </template>
    </BasicTable>
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'DeductIndex',

    setup() {
        // 导入模块确认弹框
        const modalText = ref<string>(
            '导入数据应包含一套完整的规则,导入成功后将替换系统当前法则,新规则将应用于下期账单计算,是否继续？',
        )
        const visible = ref<boolean>(false)

        const showModal = () => {
            visible.value = true
        }

        //筛选
        const params = ref<{}>({
            typeName: null,
        })

        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '级数',
                dataIndex: 'quickDeductionSeries',
                align: 'center',
            },
            {
                title: '全年应缴纳所得额',
                dataIndex: 'maxPayTaxes',
                align: 'center',
                width: 400,
                customRender: ({ record }) => {
                    if (record.minPayTaxes && !record.maxPayTaxes) {
                        return `超过${record.minPayTaxes}元的`
                    } else if (record.minPayTaxes && record.maxPayTaxes) {
                        return `超过${record.minPayTaxes}元至${record.maxPayTaxes}元部分`
                    } else if (!record.minPayTaxes && record.maxPayTaxes) {
                        return `不超过${record.maxPayTaxes}元的`
                    } else return ''
                },
            },
            {
                title: '税率',
                dataIndex: 'taxRate',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                title: '速算扣除数',
                dataIndex: 'quickDeductionNumber',
                align: 'center',
            },
        ]

        // 客户列表数据
        const customerValue = ref(null)
        // 客户列表显示隐藏
        const customerVisible = ref(false)
        // 点击客户数量
        const clickQuantity = (row) => {
            customerVisible.value = true
            customerValue.value = row
        }

        // 关闭客户列表
        const customerCancel = () => {
            customerVisible.value = false
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-quick-deductions/template'
        const importUrl = '/api/hr-quick-deductions/import'
        const exportUrl = '/api/hr-quick-deductions/export'
        const handleOk = () => {
            visible.value = false
            importVisible.value = true
        }
        // 导入
        const ImportData = () => {
            importVisible.value = true
        }

        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            selectedRowsArr,
            exportText,
            modalText,
            visible,
            showModal,
            handleOk,
            customerValue,
            columns,
            params,
            searchData,
            tableRef,

            clickQuantity,
            customerVisible,
            customerCancel,
            ImportData,
            exportData,
            //事件
            changeRoleId,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
        }
    },
})
</script>

<style scoped lang="less">
.text {
    padding: 20px;
    text-align: center;
}
.content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .img-wrapper {
        width: 210px;
        height: 210px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
