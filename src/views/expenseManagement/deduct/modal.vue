<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1000">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ style: { width: '150px' } }"
            :wrapper-col="{ span: 19 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="i in myOptions" :key="i">
                <MyFormItem :item="i" v-model:value="formData[i.name]" />
            </template>
        </Form>
        <!-- <BasicTable ref="tableRef" api="" :params="{}" :columns="columns" :rowSelectionShow="false" /> -->
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'AccumulationModal',
    props: {
        viewType: String,

        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const api = '/api/hr-accumulation-funds'
        const { title, item, visible, viewType } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            console.log(value, option)
            formData.value.roleIdList = [value]
        }

        const tableRef = ref()
        const editDisabled = ref<boolean>(false)

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '级数',
                required: false,
                disabled: editDisabled,
                name: 'quickDeductionSeries',
            },
            {
                label: '全月应缴纳所得额',
                name: 'maxPayTaxes ',
                required: false,
                disabled: editDisabled,
            },
            {
                label: '税率',
                name: 'taxRate',
                required: false,
                disabled: editDisabled,
                customRender: ({ text }) => {
                    console.log(text)
                    if (text) {
                        text = text + '%'
                    }
                    return text
                },
            },
            {
                label: '速算扣除数',
                name: 'quickDeductionNumber',
                required: false,
                disabled: editDisabled,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }
                if (viewType.value == 'see') {
                    editDisabled.value = true
                } else {
                    editDisabled.value = false
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    let params = {
                        unitScale: formData.value.unitScale.replace(/%/g, ''),
                        personageScale: formData.value.personageScale.replace(/%/g, ''),
                    }

                    console.log(params)
                    // return
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', { ...formData.value, ...params })
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', { ...formData.value, ...params })
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            tableRef,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
}
:deep(.ant-form) {
    margin-left: 80px;
}
</style>
