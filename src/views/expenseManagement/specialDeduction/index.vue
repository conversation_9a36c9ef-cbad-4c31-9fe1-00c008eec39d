<template>
    <SearchBar v-model="params" :options="options" @change="searchData">
        <template #clientIdList="itemForm">
            <ClientSelectTree
                style="width: 190px; margin-right: 10px"
                :isAll="false"
                multiple
                :checkStrictly="false"
                v-model:value="params.clientIdList"
                v-model:itemForm="options[itemForm.index]"
                @change="searchData"
            />
        </template>
    </SearchBar>
    <div class="btns">
        <Button type="primary" v-auth="'specialDeduction_import'" @click="ImportData">导入</Button>
        <Button type="primary" v-auth="'specialDeduction_export'" @click="exportData">{{ exportText }}</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-special-deductions/page"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
    />

    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { SearchBarOption } from '/#/component'
import ClientSelectTree from '/@/components/ClientSelectTree/index'
import { getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'SpecialDeduction',
    components: { ClientSelectTree },
    setup() {
        //筛选
        const params = ref<Recordable>({
            typeName: undefined,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '姓名',
                key: 'talentName',
            },
            {
                type: 'string',
                label: '证件号码',
                key: 'certificateNum',
            },
            {
                type: 'selectSlot',
                label: '所属客户',
                key: 'clientIdList',
                // options: selectclientsOptions,
                placeholder: '所属客户',
                maxTag: 1,
            },
            {
                type: 'daterange',
                label: '税款所属期起',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '税款所属期止',
                key: 'contractEndDateQuery',
            },
        ]
        const changeRoleId = (value: any) => {
            ;(params.value as any).roleName = value.option.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '所属客户',
                dataIndex: 'clientName',
                align: 'center',
                width: 120,
            },
            {
                title: '系统编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 120,
            },
            {
                title: '姓名',
                dataIndex: 'talentName',
                align: 'center',
                width: 120,
            },
            {
                title: '证件类型',
                dataIndex: 'certificateType',
                align: 'center',
                width: 120,
            },
            {
                title: '证件号码',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 120,
            },
            {
                title: '税款所属期起',
                dataIndex: 'startDate',
                align: 'center',
                width: 120,
            },
            {
                title: '税款所属期止',
                dataIndex: 'endDate',
                align: 'center',
                width: 120,
            },
            {
                title: '累计子女教育支出扣除',
                dataIndex: 'childrenEducation',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计继续教育支出扣除',
                dataIndex: 'continuingEducation',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计住房贷款利息支出扣除',
                dataIndex: 'housingLoan',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计住房租金支出扣除',
                dataIndex: 'housingRent',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计赡养老人支出扣除',
                dataIndex: 'supportElderly',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计3岁以下婴幼儿照护',
                dataIndex: 'cumulativeInfantCare',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
            {
                title: '累计其他扣除',
                dataIndex: 'other',
                align: 'center',
                width: 120,
                customRender: ({ text }) => {
                    if (text) {
                        text = text.toFixed(2)
                    }
                    return text
                },
            },
        ]

        // //操作按钮
        let myOperation = ref<inObject[]>([])
        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-special-deductions/template'
        const importUrl = '/api/hr-special-deductions/import'
        const exportUrl = '/api/hr-special-deductions/export'

        // 导入
        const ImportData = () => {
            importVisible.value = true
        }
        const selectedRowsArr = ref([])
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        //导出
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        return {
            exportText,
            selectedRowsArr,
            options,
            columns,
            params,
            searchData,
            tableRef,
            ImportData,
            exportData,
            //事件
            changeRoleId,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            exportUrl,
            //操作按钮
            myOperation,
            // myOperationClick,
        }
    },
})
</script>

<style scoped lang="less"></style>
