<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'contractTemplate_add'" @click="createRow">新增</Button>
        <Button type="primary" v-auth="'contractTemplate_down'" @click="downloadSome">{{ exportText }}</Button>
        <Button danger type="primary" v-auth="'contractTemplate_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-contract-templates/page"
        deleteApi="/api/hr-contract-templates/deletes"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />

            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp;
            <Button type="primary" size="small" @click="downloadRow(record)">下载</Button>
            &nbsp; -->
            <!-- //删除 -->
            <!-- <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal
        :visible="showEdit"
        :title="modalTitle"
        :item="currentValue"
        :templateTypeList="templateTypeList"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'

import request from '/@/utils/request'
import modal from './modal.vue'
import downFile from '/@/utils/downFile'
import { downMultFile } from '/@/utils/downFile'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'

export default defineComponent({
    name: 'ContractTemplate',
    components: { MyModal: modal },
    setup() {
        // 获取合同模板类型
        let templateTypeList = ref<LabelValueOptions>([])
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/contractTemplateType').then((res) => {
                templateTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: Number(item.itemValue), ...item }
                })
            })
        })
        //筛选
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '模板标题',
                key: 'title',
            },
            {
                type: 'select',
                label: '模板类型',
                key: 'types',
                options: templateTypeList,
                multiple: true,
                // changedBy: 'sex',
                // changedMethod: getRoleBySex,
            },
        ]
        const changeAccountType = (value: any) => {
            ;(params.value as any).accountType = value.option?.label
        }
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '模板标题',
                dataIndex: 'title',
                align: 'center',
            },
            {
                title: '模板类型',
                dataIndex: 'typeStr',
                align: 'center',
                width: 150,
            },
            {
                title: '更新时间',
                dataIndex: 'updateTime',
                align: 'center',
            },
            {
                title: '应用次数',
                dataIndex: 'usageCount',
                align: 'center',
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增合同模板')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增合同模板'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑合同模板'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增合同模板'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        // 单个下载
        const downloadRow = (data) => {
            if (data?.templatePath) {
                request.post(
                    '/api/sys-oper-logs/download',
                    {
                        title: '合同模板',
                        operDetail: '合同模板下载:' + data.title,
                        fileUrl: data.templatePath,
                    },
                    { loading: false },
                )
                downFile('get', data.templatePath || '', data.title, {})
            } else {
                message.error('暂无可供下载的合同模板')
                return
            }

            // downFile('open', data.templatePath || '', '', {})
        }

        // 选中的数据
        const myUrls = ref<inObject[]>([])
        // 多选框
        const selectedRowsArr = (item) => {
            myUrls.value = item
        }
        const tableData = ref<any>([])
        const exportText = computed(() => {
            return getDynamicText('下载', params.value, myUrls.value)
        })

        //批量下载
        const downloadSome = async () => {
            let resList: any[] = []
            if (!tableData.value.length) {
                message.error('未查询到相关数据!')
                return
            }
            let body = {}
            let ids: any[] = []
            if (exportText.value.indexOf('选中') != -1) {
                myUrls.value.forEach((item: inObject) => {
                    ids.push(item.id)
                })
                body = { ids: ids }
            }
            if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
            resList = await request.post('/api/hr-contract-templates/download', body)
            let urls: string[] = []
            let urlsName: string[] = []
            resList.forEach((element) => {
                urlsName.push(element.title)
                urls.push(element.templatePath)
            })
            if (!urls.length) {
                message.error('暂无可供下载的合同模板')
                return
            }
            request.post(
                '/api/sys-oper-logs/download',
                {
                    title: '合同模板',
                    operDetail: '合同模板下载:' + urlsName.join(),
                    fileUrl: urls.join(),
                },
                { loading: false },
            )
            downMultFile('合同模板批量导出', urls, urlsName)
        }
        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'contractTemplate_edit',
                    show: true,
                    click: editRow,
                },
                {
                    neme: '下载',
                    auth: 'contractTemplate_down',
                    show: true,
                    click: downloadRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'contractTemplate_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )

        return {
            tableData,
            exportText,
            templateTypeList,
            options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            params,
            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,
            downloadRow,
            downloadSome,
            selectedRowsArr,
            myUrls,

            //事件
            changeAccountType,
            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less"></style>
