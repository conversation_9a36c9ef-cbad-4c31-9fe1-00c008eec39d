<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="item in myOptions" :key="item">
                <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                    <template #file>
                        <ImportFile
                            v-model:fileUrls="formData.fileUrl"
                            ref="refImportFile"
                            btnTitle="上传合同模板"
                            :multiple="false"
                            :accept="'.pdf'"
                            :count="1"
                        />
                    </template>
                </MyFormItem>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validatePhone } from '/@/utils/format'
import ImportFile from '/@/components/ImportFile/src/ImportFile.vue'
export default defineComponent({
    name: 'templateModal',
    components: { ImportFile },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        templateTypeList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/hr-contract-templates'
        const { title, item, visible, templateTypeList } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            // formData.value.templateTypeList = [value]
        }
        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '模板标题',
                name: 'title',
            },
            {
                label: '模板类型',
                name: 'type',
                type: 'change',
                ruleType: 'number',

                options: templateTypeList,
                onChange: selectChanged,
            },
            {
                label: '合同模板',
                name: 'fileUrl',
                slots: 'file',
                type: 'slots',
                ruleType: 'array',
                external: true,
                message: '请上传合同模版',
                default: [
                    // {
                    //     name: 'xxx.png',
                    //     url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
                    // },
                ],
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = { ...Object.assign({}, initFormData, item.value) }

                if (item.value?.templatePath && item.value?.typeStr) {
                    formData.value.fileUrl = []
                    const fileName = item.value.title + '.pdf'
                    formData.value.fileUrl.push({ url: item.value.templatePath, name: fileName })
                }
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        formData.value.templatePath = formData.value.fileUrl[0].response.fileUrl
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        formData.value.templatePath = formData.value.fileUrl[0].url
                            ? formData.value.fileUrl[0].url
                            : formData.value.fileUrl[0].response.fileUrl
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
        }
    },
})
</script>
