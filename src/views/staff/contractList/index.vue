<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'contractList_import'" @click="importData">导入</Button>
        <Button type="primary" v-auth="'contractList_export'" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" v-auth="'contractList_delete'" @click="download()">{{ downloadText }}</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-contracts/page"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
    <!-- 调入调出 -->
    <TransferModal
        viewType="laborContract"
        title="档案调入"
        :visible="showTransfer"
        :currentData="currentData"
        @cancel="transferClose"
        @confirm="transferConfirm"
    />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import modal from './modal.vue'
import downFile from '/@/utils/downFile'
import { message } from 'ant-design-vue'
import { downMultFile } from '/@/utils/downFile'
import TransferModal from '/@/views/recordAdmin/recordList/TransferModal.vue'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'UserAdmin',
    components: { MyModal: modal, TransferModal },
    setup() {
        // let selectclientsOptions = ref<LabelValueOptions>([])
        let contractState = ref<LabelValueOptions>([])
        onMounted(() => {
            // 上级客户
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id }
            //     })
            // })
            // 业务类型
            request.get('/api/com-code-tables/getCodeTableByInnerName/contractState', {}).then((res) => {
                contractState.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '客户编号',
                key: 'unitNumber',
            },
            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIds',
                multiple: true,
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                checkStrictly: false,
            },
            {
                type: 'string',
                label: '员工编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'staffName',
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'idNo',
            },
            {
                type: 'string',
                label: '手机号',
                key: 'phone',
            },
            {
                type: 'daterange',
                label: '合同开始日期',
                key: 'contractStartDateQuery',
            },
            {
                type: 'daterange',
                label: '合同结束日期',
                key: 'contractEndDateQuery',
            },
            {
                type: 'select',
                label: '合同状态',
                key: 'states',
                multiple: true,
                options: contractState,
            },
        ]
        //表格dom
        const tableRef = ref()
        const searchData = () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '客户编号',
                dataIndex: 'unitNumber',
                align: 'center',
                width: 200,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 200,
            },
            {
                title: '员工编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 200,
            },
            {
                title: '姓名',
                dataIndex: 'staffName',
                align: 'center',
                width: 100,
            },
            {
                title: '身份证号',
                dataIndex: 'idNo',
                align: 'center',
                width: 200,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 150,
            },
            {
                title: '合同开始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
            },
            {
                title: '合同状态',
                dataIndex: 'stateStr',
                align: 'center',
                width: 100,
            },
            {
                title: '合同剩余天数',
                dataIndex: 'remainderDay',
                align: 'center',
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增合同')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增合同'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '查看合同'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增合同'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增合同')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-contracts/template'
        const importUrl = '/api/hr-contracts/import'
        const exportUrl = '/api/hr-contracts/export'
        const importData = () => {
            importVisible.value = true
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })
        const downloadText = computed(() => {
            return getDynamicText('下载', params.value, selectedRowsArr.value)
        })
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 下载
        const selectedRowsArr = ref([])
        const tableData = ref<any>([])

        const download = async (record?: inObject) => {
            let resList: any[] = []
            if (record) {
                let res = await request.get(`/api/hr-contracts/download/${record.id}`)
                resList = [res]
            } else {
                if (!tableData.value.length) {
                    message.error('未查询到相关数据!')
                    return
                }
                let body = {}
                let ids: any[] = []
                if (exportText.value.indexOf('选中') != -1) {
                    selectedRowsArr.value.forEach((item: inObject) => {
                        ids.push(item.id)
                    })
                    body = { ids: ids }
                }
                if (exportText.value.indexOf('筛选') != -1) body = { ...params.value }
                resList = await request.post('/api/hr-contracts/download', body)
            }
            let urls: string[] = []
            let urlsName: string[] = []
            resList.forEach((item) => {
                if (item.hrContractAppendixList.length) {
                    item.hrContractAppendixList.forEach((element, index) => {
                        let join = element.appendixPath?.split(',')
                        let id = element.appendixId?.split(',')
                        if (element.name == '身份证') {
                            item.hrContractAppendixList.push(
                                {
                                    appendixId: id[1],
                                    appendixPath: join[1],
                                    id: element.id,
                                    name: '身份证反面',
                                },
                                {
                                    appendixId: id[0],
                                    appendixPath: join[0],
                                    id: element.id,
                                    name: '身份证',
                                },
                            )

                            item.hrContractAppendixList.splice(index, 1)
                        }
                    })
                    item.hrContractAppendixList.forEach((element) => {
                        urls.push(element.appendixPath)
                        urlsName.push(item.staffName + '_' + element.name)
                    })
                }
            })
            if (urls.length > 0) {
                request.post(
                    '/api/sys-oper-logs/download',
                    {
                        title: '合同',
                        operDetail: '劳动合同下载:' + urlsName.join(),
                        fileUrl: urls.join(),
                    },
                    { loading: false },
                )
                if (urls.length == 1) {
                    downFile('get', urls[0], urlsName[0], {})
                } else if (urls.length > 1) {
                    downMultFile(resList.length > 1 ? '电子合同批量导出' : `${resList[0].staffName}电子合同`, urls, urlsName)
                }
            } else {
                message.error('您选择的人员暂无电子合同可下载!')
            }
        }

        //档案调入
        const showTransfer = ref(false)
        const currentData = ref({})
        const transferIn = (record) => {
            request.get(`/api/hr-archives-manage/${record.clientId}/${record.staffId}`).then((res) => {
                currentData.value = res
                showTransfer.value = true
            })
        }
        const transferClose = () => {
            showTransfer.value = false
            currentData.value = {}
        }
        const transferConfirm = () => {
            showTransfer.value = false
            tableRef.value.refresh()
            currentData.value = {}
        }

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'contractList_check',
                    show: true,
                    click: editRow,
                },
                {
                    neme: '下载',
                    auth: 'contractList_down',
                    show: true,
                    click: download,
                },
                {
                    neme: '上传',
                    auth: 'contractList_upload',
                    show: true,
                    click: transferIn,
                },
                // {
                //     neme: '删除',
                //     auth: 'contractList_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )

        return {
            // options,
            exportText,
            downloadText,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            //查询
            params,
            options,

            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,

            //事件
            //导入
            importVisible,
            importTemUrl,
            importUrl,
            importData,
            exportData,
            exportUrl,

            download,

            // 操作按钮
            myOperation,

            selectedRowsArr,
            tableData,

            //档案调入
            // start
            showTransfer,
            currentData,
            transferIn,
            transferClose,
            transferConfirm,
            //end
        }
    },
})
</script>

<style scoped lang="less"></style>
