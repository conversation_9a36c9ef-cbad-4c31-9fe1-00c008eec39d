<template>
    <BasicEditModalSlot width="1200px" :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ style: { width: '120px' } }" :rules="rules" class="form-flex">
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <FormItem :label="itemForm.label" :name="itemForm.name">
                    <Input :disabled="true" :value="formData[itemForm.name]" />
                </FormItem>
                <p class="linefeed" v-if="itemForm.showbr"></p>
                <!-- <div>
                    <span>{{ item.label }}</span
                    >:<span>{{ formData[item.name] }}</span>
                </div> -->
            </template>
        </Form>
        <!-- <div v-for="item in myOptions" :key="item">
            <span>{{ item.label }}</span
            >:<span>{{ formData[item.name] }}</span>
        </div> -->
        <template #footer>
            <div style="text-align: center">
                <Button key="submit" type="primary" v-if="item?.archivesId" @click="fileDetails">查看档案</Button>
            </div>
        </template>
    </BasicEditModalSlot>
    <!-- 查看详情 -->
    <DetailModal :visible="showDetail" :currentData="currentData" @cancel="detailClose" @confirm="detailClose" viewType="see" />
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// src\views\recordAdmin\recordList\DetailModal.vue
import DetailModal from '../../recordAdmin/recordList/DetailModal.vue'
export default defineComponent({
    name: 'ContractListModal',
    components: { DetailModal },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/users'
        const { title, item, visible } = toRefs(props)
        const selectChanged = (value: string, option: object) => {
            formData.value.roleIdList = [value]
        }
        const myOptions = ref([
            {
                label: '单位编号',
                name: 'unitNumber',
                disabled: true,
                required: false,
            },
            {
                label: '客户名称',
                name: 'clientName',
                disabled: true,
                required: false,
                showbr: true,
            },
            {
                label: '员工编号',
                name: 'systemNum',
                disabled: true,
                required: false,
            },
            {
                label: '姓名',
                name: 'staffName',
                disabled: true,
                required: false,
            },
            {
                label: '身份证号',
                name: 'idNo',
                disabled: true,
                required: false,
            },
            {
                label: '手机号',
                name: 'phone',
                disabled: true,
                required: false,
            },
            {
                label: '合同开始日期',
                name: 'contractStartDate',
                disabled: true,
                required: false,
            },
            {
                label: '合同结束日期',
                name: 'contractEndDate',
                disabled: true,
                required: false,
            },
            {
                label: '合同状态',
                name: 'stateStr',
                disabled: true,
                required: false,
            },
            {
                label: '合同剩余天数',
                name: 'remainderDay',
                disabled: true,
                required: false,
            },
            {
                label: '合同发起日期',
                name: 'contractInitDate',
                disabled: true,
                required: false,
            },
            {
                label: '签署日期',
                name: 'contractSignDate',
                disabled: true,
                required: false,
            },
        ])

        // Form 实例
        const formInline = ref(null) as any

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            console.log(formData.value)
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', formData.value)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', formData.value)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const showDetail = ref(false)
        const currentData = ref({})
        const fileDetails = async () => {
            // /api/hr-archives-manage
            const res = await request.get(`/api/hr-archives-manage/${item.value?.archivesId}`)
            currentData.value = res
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
        }
        // @cancel="detailClose" @confirm="detailConfirm"
        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            selectChanged,
            //档案
            fileDetails,
            showDetail,
            detailClose,
            currentData,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    padding-right: 2%;
    :deep(.ant-form-item) {
        width: 25%;
    }
    :deep(.ant-input) {
        border: 0px solid #d9d9d9 !important;
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
    }
}
.linefeed {
    width: 100%;
    padding: 0;
    margin: 0;
}
</style>
