<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="800px">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <template v-for="item in myOptions" :key="item">
                <MyFormItem v-if="!item.external" :width="item.width" :item="item" v-model:value="formData[item.name]" />
                <template v-else-if="item.name == 'contractTemplate'">
                    <FormItem :label="item.label" :name="item.name">
                        <Table
                            :columns="contractTemplateColumns"
                            :data-source="contractTemplateData"
                            :pagination="false"
                            :row-key="(record) => record.id"
                            class="smallTable"
                            bordered
                            :row-selection="contractTemplateRow"
                            :scroll="{ y: 200 }"
                        />
                    </FormItem>
                </template>
                <template v-else-if="item.name == 'contractRequiredAccessories'">
                    <FormItem :label="item.label" :name="item.name">
                        <Table
                            :columns="accessoriesColumns"
                            :data-source="contractRequiredAccessoriesData"
                            :pagination="false"
                            :row-key="(record) => record.id"
                            class="smallTable"
                            bordered
                            :row-selection="contractRequiredAccessoriesRow"
                            :scroll="{ y: 200 }"
                        />
                    </FormItem>
                </template>
                <template v-else-if="item.name == 'contractOptionalAccessories'">
                    <FormItem :label="item.label" :name="item.name">
                        <Table
                            :columns="accessoriesColumns"
                            :data-source="contractOptionalAccessoriesData"
                            :pagination="false"
                            :row-key="(record) => record.id"
                            class="smallTable"
                            bordered
                            :row-selection="contractOptionalAccessoriesRow"
                            :scroll="{ y: 200 }"
                        />
                    </FormItem>
                </template>
            </template>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import { validatePhone } from '/@/utils/format'
import { fileTypeList } from '/@/utils/dictionaries'

interface DataItem {
    id: number | string
    key: number
    name: string
    age: number
    address: string
    children?: DataItem[]
}

export default defineComponent({
    name: 'AllotMenu',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        //表单数据
        //请求
        const api = '/api/hr-contract-groups'
        const { title, item, visible, roleList } = toRefs<any>(props)

        // const rules: Array<Object> = []
        const myOptions = ref([
            {
                label: '标题',
                name: 'contractGroupName',
            },
            {
                label: '选择模板',
                name: 'contractTemplate',
                external: true,
                ruleType: 'array',
            },
            {
                label: '必传附件',
                name: 'contractRequiredAccessories',
                external: true,
                required: false,
                ruleType: 'array',
            },
            {
                label: '可传附件',
                name: 'contractOptionalAccessories',
                required: false,
                external: true,
                ruleType: 'array',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        //合同模板表格数据
        const contractTemplateColumns = [
            {
                title: '标题',
                dataIndex: 'title',
                align: 'center',
            },
            {
                title: '类型',
                dataIndex: 'typeStr',
                align: 'center',
            },
        ]
        const contractTemplateData = ref([])
        const accessoriesColumns = [
            {
                title: '名称',
                dataIndex: 'certificateName',
                align: 'center',
            },
            {
                title: '类型',
                dataIndex: 'certificateType',
                align: 'center',
                customRender: ({ text }) => {
                    return fileTypeList.find((item) => {
                        return text == item.value
                    })?.label
                },
            },
        ]
        const contractRequiredAccessoriesData = ref([])
        const contractOptionalAccessoriesData = ref([])

        onMounted(() => {
            request.get('/api/hr-contract-templates/list', {}).then((res) => {
                contractTemplateData.value = res
            })
            request.get('/api/hr-contract-groups/getHrCertificate', {}).then((res) => {
                contractRequiredAccessoriesData.value = res.map((item) => {
                    return { ...item, disabled: false }
                })
                contractOptionalAccessoriesData.value = res.map((item) => {
                    return { ...item, disabled: false }
                })
            })
        })
        let contractTemplateRowKeys = ref<(string | number)[]>([])
        const contractTemplateRow = {
            selectedRowKeys: contractTemplateRowKeys,
            onChange: (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
                formData.value.contractTemplate = selectedRowKeys
                contractTemplateRowKeys.value = selectedRowKeys

                //     contractTemplateData.value.map((item: inObject) => {
                //         if (item.type == 1) {
                //             let laborContractList = selectedRows.filter((el: inObject) => {
                //                 return el.type == 1
                //             })
                //             item.disabled = !laborContractList.some((el) => {
                //                 return el.id == item.id
                //             })
                //             return item
                //         } else {
                //             item.disabled = false
                //             return item
                //         }
                //         // item.disabled = selectedRows.includes(item.id)
                //     })
                //     contractTemplateData.value = [...contractTemplateData.value]
                // },
                // getCheckboxProps: (record: inObject) => {
                //     return {
                //         disabled: record.disabled, // Column configuration not to be checked
                //     }
            },
        }

        let contractRequiredAccessoriesKeys = ref<(string | number)[]>([])
        const contractRequiredAccessoriesRowChange = (selectedRowKeys: (string | number)[], selectedRows?: DataItem[]) => {
            formData.value.contractRequiredAccessories = selectedRowKeys
            contractRequiredAccessoriesKeys.value = selectedRowKeys

            contractOptionalAccessoriesData.value.map((item: inObject) => {
                item.disabled = selectedRowKeys.includes(item.id)
                return item
            })
            contractOptionalAccessoriesData.value = [...contractOptionalAccessoriesData.value]
        }
        const contractRequiredAccessoriesRow = {
            selectedRowKeys: contractRequiredAccessoriesKeys,
            onChange: contractRequiredAccessoriesRowChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled, // Column configuration not to be checked
                }
            },
        }
        let contractOptionalAccessoriesKeys = ref<(string | number)[]>([])
        const contractOptionalAccessoriesRowChange = (selectedRowKeys: (string | number)[], selectedRows?: DataItem[]) => {
            formData.value.contractOptionalAccessories = selectedRowKeys
            contractOptionalAccessoriesKeys.value = selectedRowKeys

            contractRequiredAccessoriesData.value.map((item: inObject) => {
                item.disabled = selectedRowKeys.includes(item.id)
                return item
            })
            contractRequiredAccessoriesData.value = [...contractRequiredAccessoriesData.value]
        }
        const contractOptionalAccessoriesRow = {
            selectedRowKeys: contractOptionalAccessoriesKeys,
            onChange: contractOptionalAccessoriesRowChange,
            getCheckboxProps: (record: inObject) => {
                return {
                    disabled: record.disabled, // Column configuration not to be checked
                }
            },
        }

        watch(visible, () => {
            if (visible.value) {
                let contractOptionalAccessories = item.value?.contractOptionalAccessories?.split(',') || []
                let contractRequiredAccessories = item.value?.contractRequiredAccessories?.split(',') || []
                let contractTemplate = item.value?.contractTemplate?.split(',') || []
                contractTemplateRowKeys.value = contractTemplate
                contractRequiredAccessoriesKeys.value = contractRequiredAccessories
                contractOptionalAccessoriesKeys.value = contractOptionalAccessories
                contractRequiredAccessoriesRowChange(contractRequiredAccessories)
                contractOptionalAccessoriesRowChange(contractOptionalAccessories)
                formData.value = Object.assign({}, initFormData, item.value, {
                    contractOptionalAccessories,
                    contractRequiredAccessories,
                    contractTemplate,
                })
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            let { contractOptionalAccessories, contractRequiredAccessories, contractTemplate } = formData.value

            let laborContractList = contractTemplateData.value
                ?.filter((el: inObject) => {
                    return contractTemplate.includes(el.id)
                })
                ?.filter((el: inObject) => {
                    return el.type == 1
                })
            // if (laborContractList.length > 1) {
            //     message.warning('选择模板中 请您只选择一项劳动合同!')
            //     return
            // }

            let params = {
                ...formData.value,
                contractOptionalAccessories: contractOptionalAccessories?.join(),
                contractRequiredAccessories: contractRequiredAccessories?.join(),
                contractTemplate: contractTemplate?.join(),
            }
            formInline.value
                .validate()
                .then(async () => {
                    if (title.value?.includes('新增')) {
                        await request.post(api || '', params)
                        message.success('新增成功!')
                    } else {
                        await request.put(api || '', params)
                        message.success('编辑成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }

        return {
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            //模板
            contractTemplateColumns,
            contractTemplateData,
            contractTemplateRow,
            //附件
            accessoriesColumns,
            contractRequiredAccessoriesData,
            contractOptionalAccessoriesData,

            contractRequiredAccessoriesRow,
            contractOptionalAccessoriesRow,

            contractTemplateRowKeys,
            contractRequiredAccessoriesKeys,
            contractOptionalAccessoriesKeys,
        }
    },
})
</script>
