<template>
    <SearchBar v-model:modelValue="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'contractGroup_add'" @click="createRow">新增</Button>
        <!-- <Button type="primary" @click="createRow">批量下载</Button> -->
        <Button danger type="primary" v-auth="'contractGroup_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-contract-groups/page"
        deleteApi="/api/hr-contract-groups/deletes"
        :params="params"
        :columns="columns"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
            <!-- <Button type="primary" size="small" @click="editRow(record)">编辑</Button>
            &nbsp;
            <Button danger type="primary" size="small" @click="deleteRow(record)">删除</Button> -->
        </template>
    </BasicTable>

    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { SearchBarOption } from '/#/component'

import modal from './modal.vue'
import { getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'UserAdmin',
    components: { MyModal: modal },
    setup() {
        const params = ref({})
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '标题',
                key: 'contractGroupName',
            },
        ]
        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '标题',
                dataIndex: 'contractGroupName',
                align: 'center',
                width: 300,
            },
            {
                title: '合同数',
                dataIndex: 'contractTemplateSum',
                align: 'center',
                sorter: false,
                width: 100,
            },
            {
                title: '必传附件',
                dataIndex: 'requiredAccessoriesName',
                align: 'center',
                sorter: false,
                width: 220,
            },
            {
                title: '更新时间',
                dataIndex: 'contractUpdateDate',
                align: 'center',
                width: 120,
            },
            {
                title: '应用次数',
                dataIndex: 'contractFrequency',
                align: 'center',
                width: 100,
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 230,
                fixed: 'right',
            },
        ]

        //删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新增合同组')
        // 当前编辑的数据
        const currentValue = ref(null)
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增合同组'
            currentValue.value = null
        }
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑合同组'
            currentValue.value = { ...record }
        }

        const modalCancel = () => {
            showEdit.value = false
            modalTitle.value = '新增合同组'
            currentValue.value = null
        }

        const modalConfirm = async () => {
            if (modalTitle.value.includes('新增合同组')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }

        // 操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'contractGroup_edit',
                    show: true,
                    click: editRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'contractGroup_delete',
                //     show: true,
                //     click: deleteRow,
                //     type: 'delete',
                // },
            ]),
        )
        return {
            // options,
            modalCancel,
            modalConfirm,
            showEdit,
            modalTitle,
            currentValue,
            columns,
            //查询
            params,
            options,

            searchData,
            tableRef,
            createRow,
            editRow,
            deleteRow,

            //事件

            // 操作按钮
            myOperation,
        }
    },
})
</script>

<style scoped lang="less"></style>
