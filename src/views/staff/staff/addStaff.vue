<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="modalCancel"
        @confirm="confirm"
        :title="title"
        width="1200px"
        :footer="null"
    >
        <div class="staff-box">
            <RadioGroup v-model:value="tabs" @change="tabsChange">
                <RadioButton :value="'tab1'">
                    <Badge>
                        <template #count v-if="badgeObj.tab1">
                            <ExclamationCircleOutlined style="color: #f5222d" />
                        </template>
                        <p>基本信息</p>
                    </Badge>
                </RadioButton>
                <RadioButton :value="'tab2'" :disabled="disabledTab">
                    <Badge>
                        <template #count v-if="badgeObj.tab2">
                            <ExclamationCircleOutlined style="color: #f5222d" />
                        </template>
                        <p>附加信息</p>
                    </Badge>
                </RadioButton>

                <!-- <RadioButton :value="'tab3'" v-if="typePage == 'staff'">薪酬参数</RadioButton> -->
                <RadioButton :value="'tab4'" :disabled="disabledTab" v-if="typePage == 'talent'">附件</RadioButton>
                <RadioButton :value="'tab5'" v-if="typePage == 'staff'">关联信息</RadioButton>
            </RadioGroup>
            <div v-show="tabs == 'tab1'">
                <AddStaffBasic
                    :staffId="id"
                    :visible="visible"
                    :typePage="typePage"
                    @cancel="cancel"
                    @confirm="confirm"
                    :viewType="viewType"
                    @getInfoPerfect="getInfoPerfect"
                    ref="refAddStaffBasic"
                />
            </div>
            <div v-show="tabs == 'tab2'">
                <AddStaffAdditional :staffId="id" :typePage="typePage" :viewType="viewType" :visible="visible" />
            </div>
            <!-- <div v-show="tabs == 'tab3'">
                <SalaryParam :staffId="id" @cancel="cancel" @confirm="confirm" :visible="visible" :viewType="viewType" />
            </div> -->
            <div v-show="tabs == 'tab4'">
                <UploadFile :staffId="id" :visible="visible" :viewType="viewType" />
            </div>
            <div v-if="tabs == 'tab5'">
                <!-- Related information -->
                <RelatedInfo :staffId="id" :clientId="clientId" :visible="visible" :viewType="viewType" />
            </div>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, watchEffect, unref } from 'vue'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import addStaffBasic from './addStaffBasic.vue'
import addStaffAdditional from './addStaffAdditional.vue'
// import salaryParam from './salaryParam.vue'
import uploadFile from './uploadFile.vue'
import RelatedInfo from './relatedInfo.vue'

import staffStore from '/@/store/modules/staff'
import request from '/@/utils/request'
import { message, Badge } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

export default defineComponent({
    name: 'AddStaff',
    components: {
        AddStaffBasic: addStaffBasic,
        AddStaffAdditional: addStaffAdditional,
        // SalaryParam: salaryParam,
        UploadFile: uploadFile,
        ExclamationCircleOutlined,

        Badge,
        RelatedInfo,
    },
    props: {
        title: String,
        typePage: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const tabs = ref('tab1')
        const { title, item, visible, typePage, viewType } = toRefs<any>(props)
        const dataDetail = ref<object>({})
        const id = ref<string>('')

        // const staffId = ref('')
        const disabledTab = ref<Boolean>(false)
        const clientId = ref<string>('')
        watch(
            visible,
            () => {
                if (visible.value) {
                    if (item.value.clientId) {
                        clientId.value = item.value.clientId
                    }
                    tabs.value = 'tab1'
                    if (item.value?.id) {
                        tabs.value = 'tab1'
                        id.value = item.value?.id
                        staffStore().setStaff(id.value) //设置
                        disabledTab.value = false
                    } else {
                        disabledTab.value = true
                        staffStore().setStaff('') //设置
                    }

                    // if (!id.value && typePage == 'talent' && title == '新增') {
                    //     disabledTab.value = true
                    // } else {
                    //     disabledTab.value = false
                    // }
                    // disabledTab.value = true
                }
            },
            { immediate: true },
        )

        // const staffId = ref('')
        // 为了监听人才库基本信息提交后拿到的id
        watchEffect(() => {
            id.value = staffStore().getStaff.staffId
            // staffId.value = staffStore().getStaff.staffId //获取
            if (!id.value && typePage.value == 'talent' && title.value.includes('新增')) {
                disabledTab.value = true
            } else {
                disabledTab.value = false
            }
        })
        const badgeObj = ref({ tab1: false, tab2: false })
        //类型，是否需要修改，是否跳转，回调函数
        const getInfoPerfect = async (type, check, next, callback?) => {
            let tab2 = true
            badgeObj.value[type] = check
            await request.get('/api/hr-apply-entry-staffs/complete_information', { id: item.value.employedId }).then((res) => {
                // true未完成
                tab2 = res?.body?.identification
                badgeObj.value.tab2 = tab2
                if (next && tab2) {
                    message.error('请将在职信息填写完整!')
                }
                if (!tab2) {
                    confirm()
                }
            })

            if (next && badgeObj.value.tab2) {
                tabs.value = 'tab2'
            }
            callback && callback(tab2)
        }
        const refAddStaffBasic = ref()
        const cancel = () => {
            emit('cancel')
            id.value = ''
            refAddStaffBasic.value.resetFormData()
        }
        const modalCancel = () => {
            emit('cancel')
            id.value = ''
            refAddStaffBasic.value.resetFormData()
        }
        const confirm = () => {
            emit('confirm')
        }
        // tab切换
        const tabsChange = (value) => {
            tabs.value = value.target.value
        }

        return {
            tabs,
            id,
            dataDetail,
            disabledTab,
            confirm,
            cancel,
            tabsChange,
            modalCancel,
            getInfoPerfect,
            badgeObj,
            refAddStaffBasic,
            clientId,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
    .staff-box {
        padding: 0px 24px;
    }
}
// .btn {
//     text-align: right;
//     margin: 10px;
//     button {
//         margin-left: 10px;
//     }
// }
.work-null {
    border: 1px solid #e5e5e5;
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #999;
    margin-bottom: 20px;
}
:deep(.ant-badge-count, .ant-badge-dot, .ant-badge .ant-scroll-number-custom-component) {
    right: -6px;
}
</style>
