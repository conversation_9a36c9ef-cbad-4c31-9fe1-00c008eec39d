<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="500px">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }">
            <FormItem
                :rules="{ required: viewType == 'start', message: '请选择借调单位', trigger: 'change' }"
                :label="viewType == 'start' ? '借调至单位' : '原单位'"
                name="clientId"
            >
                <ClientSelectTree
                    v-model:value="formData.clientId"
                    :itemForm="{ label: '借调单位' }"
                    :disabled="viewType == 'end'"
                    @labelChange="clientChange"
                    :customApi="'/api/hr-client/not-data-permission'"
                    :customDictionaryKey="'secondedClients'"
                />
            </FormItem>
            <FormItem
                :rules="{ required: true, message: `请选择借调${viewType == 'start' ? '开始' : '结束'}时间`, trigger: 'change' }"
                :label="viewType == 'start' ? '开始时间' : '结束时间'"
                name="date"
            >
                <DatePicker
                    v-model:value="formData.date"
                    format="YYYY-MM-DD"
                    :placeholder="viewType == 'start' ? '开始时间' : '结束时间'"
                    valueFormat="YYYY-MM-DD"
                    :getCalendarContainer="getPopupContainer"
                />
            </FormItem>
            <FormItem :required="false" :label="viewType == 'start' ? '借调原因' : '备注'" name="remark">
                <Textarea
                    v-model:value="formData.remark"
                    :rows="3"
                    allowClear
                    :placeholder="viewType == 'start' ? '借调原因' : '备注'"
                />
            </FormItem>
        </Form>
        <template #footer>
            <div>
                <Button @click="cancel" class="rejectBtn">取消</Button>
                <Button @click="confirm" type="primary">确认</Button>
            </div>
        </template>
    </BasicEditModalSlot>
</template>

<script lang="ts" setup="SecondedModal">
import { message } from 'ant-design-vue'

import { ref, toRefs, watch } from 'vue'

import request from '/@/utils/request'
const props = defineProps({
    title: String,
    item: {
        type: Object,
    },
    viewType: String,
    visible: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['confirm', 'cancel', 'update:visible'])
const { item, visible, viewType } = toRefs<any>(props)
watch(
    visible,
    () => {
        if (visible.value) {
            formData.value = {
                date: item.value?.date,
                remark: item.value?.remark,
                clientId: viewType.value == 'start' ? undefined : item.value?.oldClientId,
            }
        }
    },
    { immediate: true },
)

// Form 实例
const formInline = ref(null) as any
// Form Data
const formData = ref<any>({ date: undefined, remark: undefined, clientId: undefined })

// reset formData
const resetFormData = () => {
    formData.value = { date: undefined, remark: undefined, clientId: undefined }
    formInline.value.resetFields()
}

const getPopupContainer = () => {
    return document.body
}

const clientName = ref('')
const clientChange = (label) => {
    clientName.value = label || ''
}

// cancel handle
const cancel = () => {
    clientName.value = ''
    resetFormData()
    emit('cancel')
    emit('update:visible', false)
}

const confirm = () => {
    const postObj = {
        [viewType.value == 'start' ? 'staffId' : 'id']: item.value.id,
        newClientId: viewType.value == 'start' ? formData.value.clientId : undefined,
        [viewType.value == 'start' ? 'startDate' : 'endDate']: formData.value.date,
        [viewType.value == 'start' ? 'reason' : 'remark']: formData.value.remark,
    }
    formInline.value
        .validate()
        .then(async () => {
            request
                .post(viewType.value == 'start' ? '/api/hr-staff-secondments/create' : '/api/hr-staff-secondments/end', {
                    ...postObj,
                })
                .then((res) => {
                    message.success(
                        viewType.value == 'start'
                            ? `员工${item.value?.name}已成功借调至${clientName.value}`
                            : `员工${item.value?.name}已成功提交${item.value?.newClientName}的结束借调申请`,
                    )
                    cancel()
                    emit('confirm')
                })
        })
        .catch((err) => {
            console.log(err)
        })
}
</script>
<style scoped lang="less">
.date {
    width: 25%;
}
</style>
