<template>
    <BasicEditModalSlot class="Nofooter" :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" :footer="null">
        <div class="skip-auth">
            <span>请选择您要跳过的类型：</span>
            <Select v-model:value="authType" :options="options" allowClear placeholder="请选择要调过的类型" class="select" />
        </div>
        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="confirm">确定</Button>
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import request from '/@/utils/request'
import { ref, defineComponent, toRefs, watch } from 'vue'
import { message } from 'ant-design-vue'

export default defineComponent({
    name: 'AddStaff',
    props: {
        title: String,
        typePage: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
        viewType: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const tabs = ref('tab1')
        const { item, visible } = toRefs<any>(props)
        const dataDetail = ref<object>({})
        const id = ref<string>('')

        // const staffId = ref('')
        const disabledTab = ref<Boolean>(false)
        const clientId = ref<string>('')
        const authType = ref()
        const options = ref([
            {
                label: '个人银行卡认证',
                value: 3,
            },
            {
                label: '人脸核身',
                value: 4,
            },
        ])
        const formData = ref<Recordable>({})
        watch(
            visible,
            () => {
                if (visible.value) {
                    formData.value = Object.assign({}, item.value)
                    console.log(formData.value)
                }
            },
            { immediate: true },
        )
        const cancel = () => {
            emit('cancel')
        }
        const confirm = async () => {
            await request
                .post(`/api/hr-talent-staffs/distinguish-cloud`, {
                    staffId: formData.value.id,
                    certificateNum: formData.value.certificateNum,
                    type: authType.value,
                })
                .then((res) => {
                    message.success(res)
                })
                .catch((err) => {
                    console.log(err)
                })
            emit('confirm')
        }

        return {
            tabs,
            id,
            dataDetail,
            disabledTab,
            confirm,
            cancel,
            clientId,
            authType,
            options,
        }
    },
})
</script>
<style lang="less">
.skip-auth {
    width: 100%;
    text-align: center;
    height: 80px;
    .select {
        width: 320px;
    }
}
</style>
