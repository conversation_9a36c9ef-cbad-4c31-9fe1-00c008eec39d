<template>
    <Form ref="formInline" :model="formData" :label-col="{ style: { width: '120px' } }" style="overflow-y: auto">
        <div class="param-box">
            <div class="title">社保</div>

            <Table
                :columns="socialSecurityColumns"
                :data-source="socialSecurityData"
                :pagination="false"
                :row-key="(record) => record.id"
            />
        </div>

        <div class="param-box">
            <div class="title">公积金</div>

            <Table
                :columns="accumulationColumns"
                :data-source="accumulationData"
                :pagination="false"
                :row-key="(record) => record.id"
            />
        </div>

        <Form ref="formInlineRef" :model="formData" :label-col="{ style: { width: '110px' } }" class="form-flex" :rules="rules">
            <template v-if="cardinalArr.length === 0">
                <template v-for="item in myOptions" :key="item">
                    <FormItem v-if="item.external" :label="item.label" class="seniorityPay_class">
                        <InputNumber v-model:value="formData.seniorityPay" :disabled="item.disabled" style="width: 150px" />
                        <Popover trigger="hover" placement="topRight" :getPopupContainer="getPopupContainer">
                            <QuestionCircleOutlined style="margin-left: 10px" />
                            <template #content>
                                <p>入职时间：{{ formData.boardDate }} 工龄：{{ getWorkAge() }}年</p>
                                <p>当前工龄工资 = 工龄工资基数 x 工龄 = {{ getTips() }}</p>
                            </template>
                        </Popover>
                    </FormItem>
                    <MyFormItem v-else width="300px" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                </template>
            </template>
            <template v-if="cardinalArr.length > 0">
                <template v-for="item in myOptions.slice(0, 11)" :key="item">
                    <FormItem v-if="item.external" :label="item.label" class="seniorityPay_class">
                        <InputNumber v-model:value="formData.seniorityPay" :disabled="item.disabled" style="width: 150px" />
                        <Popover trigger="hover" placement="topRight" :getPopupContainer="getPopupContainer">
                            <QuestionCircleOutlined style="margin-left: 10px" />
                            <template #content>
                                <p>入职时间：{{ formData.boardDate }} 工龄：{{ getWorkAge() }}年</p>
                                <p>当前工龄工资 = 工龄工资基数 x 工龄 = {{ getTips() }}</p>
                            </template>
                        </Popover>
                    </FormItem>
                    <MyFormItem v-else width="300px" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                </template>
                <template v-for="(row, index) in cardinalArr" :key="index">
                    <FormItem :label="row.title" class="seniorityPay_class">
                        <InputNumber v-model:value="formData[row.dataIndexS[0]]" style="width: 190px" />
                    </FormItem>
                </template>
            </template>
        </Form>
    </Form>
    <div class="ant-modal-footer">
        <Button key="back" @click="cancelEdit">取消</Button>
        <Button key="submit" type="primary" @click="confirm()">确认</Button>
        <BasicEditModalSlot
            class="aaa"
            centered
            title="确认参保提示"
            @cancel="cancel"
            @confirm="confirm"
            v-model:visible="visible"
            @ok="handleOk"
        >
            <p class="text">是否标记为已参保?</p>
        </BasicEditModalSlot>
    </div>
</template>
<script lang="ts">
import { minus, times } from 'number-precision'
import { message, Popover } from 'ant-design-vue'
import { ref, defineComponent, watch, inject, nextTick, onMounted, toRefs, computed } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import { validateAccount, bankCardNoValidity } from '/@/utils/format'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'

import permissionStore from '/@/store/modules/permission'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
export default defineComponent({
    name: 'InsuredMessage',
    components: { QuestionCircleOutlined, Popover },

    props: {
        // staffId: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        // 动态基数数组
        const cardinalArr = ref<any[]>([])
        const RoleState = permissionStore().getPermission.staffState // 客户false  企业true
        const bankList = ref<object[]>([]) // 银行数据
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/ownedBank', {}).then((res) => {
                bankList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        //社保表格数据
        const socialSecurityColumns = [
            {
                title: '社保类型',
                dataIndex: 'socialSecurityName',
                align: 'center',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
            },
            {
                title: '收款单位名称',
                dataIndex: 'nameOfBeneficiary',
                align: 'center',
            },
            {
                title: '收款单位账号',
                dataIndex: 'receivingAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'accountBank',
                align: 'center',
            },
            {
                title: '单位养老',
                dataIndex: 'unitPension',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位医疗',
                dataIndex: 'unitMedical',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位工伤',
                dataIndex: 'workInjury',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位失业',
                dataIndex: 'unitUnemployment',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人养老',
                dataIndex: 'personalPension',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人医疗',
                dataIndex: 'personalMedical',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人失业',
                dataIndex: 'personalUnemployment',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]
        //公积金表格数据
        const accumulationColumns = [
            {
                title: '公积金类型名称',
                dataIndex: 'typeName',
                align: 'center',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
            },
            {
                title: '收款单位名称',
                dataIndex: 'payeeName',
                align: 'center',
            },
            {
                title: '收款单位账号',
                dataIndex: 'payeeAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'payeeBank',
                align: 'center',
            },
            {
                title: '单位比例',
                dataIndex: 'unitScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人比例',
                dataIndex: 'personageScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]

        const socialSecurityData = ref<object[]>([])
        const accumulationData = ref<object[]>([])
        // const { staffId } = toRefs<any>(props)
        const staffId: any = inject('staffId')
        const item: any = inject('item')
        const basicDetail = ref<object>({})

        const resetFormData = () => {
            formData.value = {}
        }

        // 获取薪酬参数详情
        const getDetail = (value) => {
            request.get('/api/hr-staff-emoluments', { staffId: value }).then((res) => {
                let {
                    id,
                    basicWage,
                    ownedBank,
                    salary,
                    boardDate,
                    paymentDate,
                    seniorityPay,
                    seniorityWageBase,
                    salaryCardNum,
                    socialSecurityNum,
                    socialSecurityCardinal,
                    medicalInsuranceNum,
                    medicalInsuranceCardinal,
                    accumulationFundNum,
                    accumulationFundCardinal,
                    socialSecurityCardinalPersonal,
                    medicalInsuranceCardinalPersonal,
                    unitPensionCardinal,
                    unitUnemploymentCardinal,
                    unitMaternityCardinal,
                    workInjuryCardinal,
                    unitLargeMedicalExpense,
                    replenishWorkInjuryExpense,
                    personalPensionCardinal,
                    personalUnemploymentCardinal,
                    personalLargeMedicalExpense,
                    personalMaternityCardinal,
                    personalEnterpriseAnnuity,
                    commercialInsurance,
                    unitEnterpriseAnnuity,
                } = res
                formData.value = {
                    id,
                    basicWage,
                    ownedBank: ownedBank ? Number(res.ownedBank) : ownedBank,
                    salary,
                    boardDate,
                    paymentDate,
                    seniorityPay,
                    seniorityWageBase,
                    salaryCardNum,
                    socialSecurityNum,
                    socialSecurityCardinal,
                    medicalInsuranceNum,
                    medicalInsuranceCardinal,
                    accumulationFundNum,
                    accumulationFundCardinal,
                    socialSecurityCardinalPersonal,
                    medicalInsuranceCardinalPersonal,
                    unitPensionCardinal,
                    unitUnemploymentCardinal,
                    unitMaternityCardinal,
                    workInjuryCardinal,
                    unitLargeMedicalExpense,
                    replenishWorkInjuryExpense,
                    personalPensionCardinal,
                    personalUnemploymentCardinal,
                    personalLargeMedicalExpense,
                    personalMaternityCardinal,
                    personalEnterpriseAnnuity,
                    commercialInsurance,
                    unitEnterpriseAnnuity,
                }
                // formData.value.id = res.id
                // formData.value.basicWage = res.basicWagen
                // formData.value.ownedBank = Number(res.ownedBank) //银行
                // formData.value.salary = res.salary //应发工资
                // formData.value.boardDate = res.boardDate
                // formData.value.paymentDate = res.paymentDate //缴费年月
                // formData.value.seniorityPay = res.seniorityPay //当前工龄工资
                // formData.value.seniorityWageBase = res.seniorityWageBase //工龄工资基数
                // formData.value.salaryCardNum = res.salaryCardNum
                // formData.value.socialSecurityNum = res.socialSecurityNum
                // formData.value.socialSecurityCardinal = res.socialSecurityCardinal
                // formData.value.medicalInsuranceNum = res.medicalInsuranceNum
                // formData.value.medicalInsuranceCardinal = res.medicalInsuranceCardinal
                // formData.value.accumulationFundNum = res.accumulationFundNum
                // formData.value.accumulationFundCardinal = res.accumulationFundCardinal
                socialSecurityData.value = res.socialSecurityDTOList //社保表格数据
                accumulationData.value = res.accumulationFundDTOList //公积金表格数据
            })
        }

        const resObj = computed(() => {
            let obj = {}
            cardinalArr.value?.map((el) => {
                for (const k in el) {
                    el.dataIndexS.forEach((row) => {
                        if (row === k) {
                            obj[row] = formData.value[el.dataIndexS[0]]
                        }
                    })
                }
                return el
            })
            return obj
        })

        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, { clientId: item.value.clientId })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = [...arr]
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })

                return item
            })
            return resArr
        }

        watch(
            staffId,
            () => {
                if (item.value) {
                    getList()
                }
                if (staffId.value) {
                    nextTick(() => {
                        getDetail(staffId.value)
                    })
                }
            },
            { immediate: true },
        )
        const visible = ref<boolean>(false)
        // 判断对象的属性 是否 都有值
        const isTure = (obj) => {
            let flag = true
            for (let key in obj) {
                console.log(key)
                if (
                    key != 'unitLargeMedicalExpense' ||
                    key != 'commercialInsurance' ||
                    key != 'replenishWorkInjuryExpense' ||
                    key != 'personalLargeMedicalExpense' ||
                    key != 'unitEnterpriseAnnuity' ||
                    key != 'personalEnterpriseAnnuity'
                ) {
                    if (!obj[key] && obj[key] != 0) {
                        return flag
                    }
                } else {
                    return flag
                }
            }
            return flag
        }
        const confirm = () => {
            formData.value = { ...formData.value, ...resObj.value }
            let r = isTure(resObj.value)
            formInlineRef.value
                .validate()
                .then(async () => {
                    if (
                        socialSecurityData.value &&
                        accumulationData.value &&
                        (formData.value.accumulationFundCardinal || formData.value.accumulationFundCardinal == 0) &&
                        r
                    ) {
                        visible.value = true
                    } else {
                        request
                            .put('/api/hr-staff-emoluments/update', {
                                izInsured: false,
                                ...formData.value,
                                staffId: staffId.value,
                            })
                            .then((res) => {
                                console.log(res)
                                message.success('编辑成功!')
                                emit('confirm', formData.value)
                            })
                    }
                })
                .catch((err) => {
                    console.log(`表单验证失败${err}`)
                })
        }
        // 确认参保
        const handleOk = () => {
            formData.value = { ...formData.value, ...resObj.value }
            request
                .put('/api/hr-staff-emoluments/update', {
                    izInsured: true,
                    ...formData.value,
                    staffId: staffId.value,
                })
                .then((res) => {
                    message.success('参保成功!')
                    // cancel()
                    emit('confirm', formData.value)
                })
            visible.value = false
        }
        // 取消参保
        const cancel = () => {
            formData.value = { ...formData.value, ...resObj.value }
            request
                .put('/api/hr-staff-emoluments/update', { izInsured: false, ...formData.value, staffId: staffId.value })
                .then((res) => {
                    message.success('取消成功!')
                    emit('cancel', formData.value)
                })
            resetFormData()
        }

        // 取消编辑
        const cancelEdit = () => {
            emit('cancel')
        }

        const getWorkAge = () => {
            let boardYear = Number(new Date(formData.value.boardDate).getFullYear())
            return minus(Number(new Date().getFullYear()), boardYear)
        }

        const getTips = () => {
            return `${formData.value.seniorityWageBase ?? 0} x ${getWorkAge()} =
            ${times(Number(formData.value.seniorityWageBase), getWorkAge())}`
        }

        const WageBaseBlur = (value) => {
            const seniorityWageBase = value
            request
                .post('/api/calculate-seniority-salary', { seniorityWageBase, boardDate: formData.value.boardDate })
                .then((res) => {
                    formData.value.seniorityPay = res.seniorityPay
                })
        }

        const validateNo = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.resolve()
            } else {
                return validateAccount(rule, value)
            }
        }

        const validateBankCardNo = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.resolve()
            } else {
                return bankCardNoValidity(rule, value)
            }
        }

        const myOptions = ref([
            {
                label: '基础工资',
                name: 'basicWage',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '应发工资',
                name: 'salary',
                ruleType: 'number',
                type: 'number',
                required: false,
            },

            {
                label: '工资银行',
                name: 'ownedBank',
                type: 'select',
                ruleType: 'number',
                options: bankList,
                required: false,
            },
            {
                label: '工资卡号',
                name: 'salaryCardNum',
                validator: validateBankCardNo,
                required: false,
            },

            {
                label: '个人社保编号',
                name: 'socialSecurityNum',
                validator: validateNo,
                required: false,
            },
            {
                label: '个人医保编号',
                name: 'medicalInsuranceNum',
                validator: validateNo,
                required: false,
            },

            {
                label: '个人公积金编号',
                name: 'accumulationFundNum',
                validator: validateNo,
                required: false,
            },
            {
                label: '公积金基数',
                name: 'accumulationFundCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '工龄工资基数',
                name: 'seniorityWageBase',
                ruleType: 'number',
                type: 'number',
                required: false,
                onChange: WageBaseBlur,
                trigger: 'blur',
            },
            {
                label: '当前工龄工资',
                name: 'seniorityPay',
                ruleType: 'number',
                type: 'number',
                disabled: true,
                required: false,
                external: true,
            },
            {
                label: '缴费年月',
                name: 'paymentDate',
                type: 'month',
                required: false,
            },

            {
                label: '单位医疗基数',
                name: 'medicalInsuranceCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '单位养老基数',
                name: 'unitPensionCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '单位失业基数',
                name: 'unitUnemploymentCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '单位工伤基数',
                name: 'workInjuryCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '单位生育基数',
                name: 'unitMaternityCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '大额医疗费用',
                name: 'unitLargeMedicalExpense',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '补充工伤费用',
                name: 'replenishWorkInjuryExpense',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '个人医保基数',
                name: 'medicalInsuranceCardinalPersonal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '个人养老基数',
                name: 'personalPensionCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '个人失业基数',
                name: 'personalUnemploymentCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '个人大额医疗费用',
                name: 'personalLargeMedicalExpense',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
            {
                label: '个人生育基数',
                name: 'personalMaternityCardinal',
                ruleType: 'number',
                type: 'number',
                required: false,
            },
        ])
        // Form 实例
        const formInline = ref(null) as any
        const formInlineRef = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        return {
            getWorkAge,
            getTips,
            myOptions,
            formData,
            socialSecurityColumns,
            socialSecurityData,
            bankList,
            RoleState,
            accumulationColumns,
            accumulationData,
            basicDetail,
            cancel,
            confirm,
            visible,
            handleOk,
            rules,
            formInline,
            formInlineRef,
            cancelEdit,
            getPopupContainer: () => {
                return document.body
            },

            cardinalArr,
        }
    },
})
</script>
<style scoped lang="less">
.param-box {
    margin: 20px 0px;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    box-sizing: border-box;
    .title {
        background-color: #6894fe;
        color: #fff;
        padding: 5px 10px;
    }
    .box-body {
        padding-top: 24px;
    }
}
.seniorityPay_class {
    display: flex;
    width: 300px !important;
}
.form-flex {
    overflow: auto;
    margin-top: 20px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // justify-content: space-between;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 25%;
        margin-right: 13px;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.param-input {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    span {
        width: 24.5%;
        padding: 10px 0;
        input {
            border-radius: 5px;
            border-color: #ccc;
            height: 35px;
        }
    }
}

.ant-modal-body {
    padding: 24px 0 0 !important;
    & > .ant-form.ant-form-horizontal {
        margin: 0 24px 24px;
    }
}

:deep(.ant-table-wrapper) {
    padding: 20px;
}
:deep(th) {
    background: #fafafa !important;
    color: black !important;
}
.icon {
    margin-left: 15px;
    color: #999999;
}

:deep(.ant-form-item .ant-form-item-label) {
    label {
        width: 100%;
        display: inline-block;
        overflow: hidden;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
</style>
