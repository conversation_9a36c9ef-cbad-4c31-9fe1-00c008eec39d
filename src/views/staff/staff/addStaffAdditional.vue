<template>
    <div v-if="RoleState">
        <!-- 在职信息  企业员工有在职信息  客户员工没有   人才库没有 -->
        <div v-if="!izDefault">
            <OnJobExperience
                v-model:dataDetail="workCompany"
                :staffId="staffId"
                :visible="visible"
                :viewType="viewType"
                :personTypeList="personTypeList"
                :workNatureList="workNatureList"
                :salaryList="salaryList"
                v-bind="$attrs"
            />
            <Divider />
        </div>
    </div>

    <!-- 工作经历 -->
    <WorkExperience v-model:dataDetail="workExperience" :staffId="staffId" :roleState="RoleState" :visible="visible" :personTypeList="personTypeList"  :salaryList="salaryList" :workNatureList="workNatureList" />
    <Divider />
    <!-- 教育经历 -->
    <Educational v-model:dataDetail="staffEducation" :staffId="staffId" :roleState="RoleState" :visible="visible" />
    <Divider />
    <!-- 应试经历 -->
    <InterviewExperience v-model:dataDetail="staffInterview" :staffId="staffId" :roleState="RoleState" :visible="visible" />
    <Divider />
    <!-- 家庭成员 -->
    <MemberFamily v-model:dataDetail="staffFamily" :staffId="staffId" :roleState="RoleState" :visible="visible" />
    <Divider />
    <!-- 紧急联系人 -->
    <EmergencyContact v-model:dataDetail="staffContacts" :staffId="staffId" :roleState="RoleState" :visible="visible" />
    <Divider />
    <!-- 语言能力 -->
    <LanguageAbility v-model:dataDetail="staffLanguage" :staffId="staffId" :roleState="RoleState" :visible="visible" :proficiencyList="proficiencyList"/>
    <Divider />
    <!-- 专业技能 -->
    <ProfessionalSkills v-model:dataDetail="staffProfession" :staffId="staffId" :roleState="RoleState" :visible="visible" :proficiencyList="proficiencyList"/>
    <Divider />
    <!-- 职业（工种）资格 -->
    <ProfessionalQualification
        v-model:dataDetail="staffQualification"
        :staffId="staffId"
        :roleState="RoleState"
        :visible="visible"
        :gradeList="gradeList"
    />
    <Divider />
    <!-- 职业技术资格 -->
    <TechnicalCapability v-model:dataDetail="staffTechnique" :staffId="staffId" :roleState="RoleState" :visible="visible" :gradeList="gradeList" />
    <Divider />
    <!-- 证书 -->
    <Certificate v-model:dataDetail="staffCertificate" :staffId="staffId" :roleState="RoleState" :visible="visible" />
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, watchEffect } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
import onJobExperience from './additional/onJobExperience.vue'
import workExperience from './additional/workExperience.vue'
import educational from './additional/educational.vue'
import InterviewExperience from './additional/InterviewExperience.vue'
import memberFamily from './additional/memberFamily.vue'
import emergencyContact from './additional/emergencyContact.vue'
import LanguageAbility from './additional/LanguageAbility.vue'
import professionalSkills from './additional/professionalSkills.vue'
import professionalQualification from './additional/professionalQualification.vue'
import technicalCapability from './additional/technicalCapability.vue'
import certificate from './additional/certificate.vue'
// import permissionStore from '/@/store/modules/permission'
import staffStore from '/@/store/modules/staff'
export default defineComponent({
    name: 'AddStaffAdditional',
    components: {
        OnJobExperience: onJobExperience,
        WorkExperience: workExperience,
        Educational: educational,
        InterviewExperience,
        MemberFamily: memberFamily,
        EmergencyContact: emergencyContact,
        LanguageAbility,
        ProfessionalSkills: professionalSkills,
        ProfessionalQualification: professionalQualification,
        TechnicalCapability: technicalCapability,
        Certificate: certificate,
    },
    props: {
        staffId: String,
        typePage: String,
        viewType: String,
        visible: Boolean,
    },
    setup(props, {}) {
        const { staffId, typePage, viewType, visible } = toRefs<any>(props)
        //  RoleState = permissionStore().getPermission.staffState // 客户false  企业true
        let RoleState = ref(viewType.value == 'edit' || viewType.value == 'perfect')

        const izDefault = ref<Boolean>(false)

        const workCompany = ref<object[]>([])
        const workExperience = ref<object[]>([])
        const staffEducation = ref<object[]>([])
        const staffInterview = ref<object[]>([])
        const staffFamily = ref<object[]>([])
        const staffContacts = ref<object[]>([])
        const staffLanguage = ref<object[]>([])
        const staffProfession = ref<object[]>([])
        const staffQualification = ref<object[]>([])
        const staffTechnique = ref<object[]>([])
        const staffCertificate = ref<object[]>([])

        // 获取附加信息详情
        const getAdditionalDetail = (value) => {
            request.get('/api/hr-talent-staffs/extras', { id: value, izDefault: izDefault.value }).then((res: any) => {
                workCompany.value = res.workCompanyDTOList // 在职信息
                workExperience.value = res.workExperienceDTOList // 工作经历
                staffEducation.value = res.staffEducationDTOList // 教育经历
                staffInterview.value = res.staffInterviewDTOList // 应试经历
                staffFamily.value = res.staffFamilyDTOList // 家庭成员
                staffContacts.value = res.staffContactsDTOList // 紧急联系人
                staffLanguage.value = res.staffLanguageDTOList // 语言能力
                staffProfession.value = res.staffProfessionDTOList // 专业技能
                staffQualification.value = res.staffQualificationDTOList // 职业(工种)资格
                staffTechnique.value = res.staffTechniqueDTOList // 职业技术能力
                staffCertificate.value = res.staffCertificateDTOList // 证书
            })
        }
        // 为了监听人才库基本信息提交后拿到的id
        watchEffect(() => {
            staffId.value = staffStore().getStaff.staffId //获取
        })
        watch(
            visible,
            () => {
                if (!visible.value) {
                    return
                }
                if (typePage.value == 'staff') {
                    izDefault.value = false
                } else if (typePage.value == 'talent') {
                    izDefault.value = true
                }
                if (staffId.value) {
                    getAdditionalDetail(staffId.value)
                    RoleState.value = viewType.value == 'edit' || viewType.value == 'perfect'
                } else {
                    workCompany.value = [] // 在职信息
                    workExperience.value = [] // 工作经历
                    staffEducation.value = [] // 教育经历
                    staffInterview.value = [] // 应试经历
                    staffFamily.value = [] // 家庭成员
                    staffContacts.value = [] // 紧急联系人
                    staffLanguage.value = [] // 语言能力
                    staffProfession.value = [] // 专业技能
                    staffQualification.value = [] // 职业(工种)资格
                    staffTechnique.value = [] // 职业技术能力
                    staffCertificate.value = [] // 证书
                }
            },
            { immediate: true },
        )
        const stationList=ref([])//岗位
        const personTypeList = ref([])//人员类型
        const proficiencyList = ref([])//技能熟练度
        const salaryList = ref([])//月薪区间
        const workNatureList = ref([])//工作地区
        const gradeList = ref([])//技能等级
        
        const getAlllist = () => { 
            request.get('/api/hr-stations/list', {}).then((res) => {
                stationList.value = res.map((item) => {
                    return { label: item.professionName, value: item.id }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                personTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/proficiency', {}).then((res) => {
                proficiencyList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/monthlyPay', {}).then((res) => {
                salaryList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/workNature', {}).then((res) => {
                workNatureList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/gradeType', {}).then((res) => {
                gradeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        }
        getAlllist()
        return {
            workCompany,
            workExperience,
            staffEducation,
            staffInterview,
            staffFamily,
            staffContacts,
            staffLanguage,
            staffProfession,
            staffQualification,
            staffTechnique,
            staffCertificate,
            RoleState,
            izDefault,

            //下拉数据
            stationList,
            personTypeList,
            proficiencyList,
            salaryList,
            workNatureList,
            gradeList
        }
    },
})
</script>
<style lang="less">
.add-work {
    overflow: hidden;
    .work-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0px;
        p {
            font-size: 20px;
        }
    }
    .work-null {
        border: 1px solid #e5e5e5;
        padding: 20px;
        text-align: center;
        background-color: #f5f5f5;
        color: #999;
    }
    .company-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 10px;
        .title-name {
            .name {
                font-size: 16px;
                margin-bottom: 5px;
            }
            .title-span {
                color: #999;
                span {
                    margin-right: 7px;
                }
            }
        }
        .company-edit {
            color: #1890ff;
            cursor: default;
        }
    }
    .work-flex {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-top: 20px;
        // padding-left: 5px;
        .flex-span {
            width: 25%;
            margin-bottom: 10px;
            span:first-child {
                color: #999;
            }
        }
        .flex-span2 {
            margin-right: 50px;
            margin-bottom: 10px;
            span:first-child {
                color: #999;
            }
        }
    }
    .technical-list {
        margin-bottom: 20px;
        .technical-flex {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            .flex-div {
                flex: 30%;
            }
            .flex-div1 {
                flex: 20%;
            }
            .flex-name1 {
                flex: 10%;
            }
            .flex-right {
                flex: 10%;
            }
            .edu-title {
                flex: 15%;
                font-size: 16px;
            }
            .fr {
                text-align: right;
                color: #1890ff;
            }
        }
    }
    .certificate-list {
        margin-bottom: 60px;
        .certificate-flex {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            .flex-div {
                flex: 30%;
            }
            .flex-right {
                flex: 10%;
            }
            .fr {
                text-align: right;
                color: #1890ff;
            }
        }
    }
    .flex-span1 {
        // padding-left: 5px;
        margin-right: 50px;
        margin-bottom: 10px;
        color: #000;
        & > span:first-child {
            color: #999;
        }
    }
    .form-box {
        border: 1px solid #cecece;
        padding: 10px;
        margin-bottom: 20px;
    }
    .linefeed {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    .form-flex {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        align-items: flex-start;
        position: relative;
        p {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        :deep(.ant-form-item) {
            width: 33%;
        }
        :deep(.img) {
            position: absolute;
            right: 1%;
        }
    }
    .btn {
        text-align: right;
        margin: 10px;
        button {
            margin-left: 10px;
        }
    }
}
</style>
