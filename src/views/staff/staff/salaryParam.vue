<template>
    <Form
        ref="formInline"
        :model="formData"
        :label-col="{ style: { width: '150px' } }"
        style="overflow-y: auto; margin-top: 20px"
    >
        <div class="param-box">
            <div class="title">工资</div>
            <div class="box-body form-flex">
                <FormItem label="基本工资">
                    <Input v-model:value="formData.basicWage" placeholder="基本工资" :disabled="!RoleState" />
                </FormItem>
                <FormItem label="所属银行">
                    <Select
                        v-model:value="formData.ownedBank"
                        showSearch
                        :options="bankList"
                        :disabled="!RoleState"
                        placeholder="所属银行"
                    />
                </FormItem>
                <FormItem label="工资卡号">
                    <Input v-model:value="formData.salaryCardNum" placeholder="工资卡号" :disabled="!RoleState" />
                </FormItem>
            </div>
        </div>
        <div class="param-box">
            <div class="title">社保</div>
            <div class="box-body">
                <div class="form-flex">
                    <FormItem label="社保编号">
                        <Input v-model:value="formData.socialSecurityNum" placeholder="社保编号" :disabled="!RoleState" />
                    </FormItem>
                    <FormItem label="社保基数">
                        <Input v-model:value="formData.socialSecurityCardinal" placeholder="社保基数" :disabled="!RoleState" />
                    </FormItem>
                </div>
                <Table
                    :columns="socialSecurityColumns"
                    :data-source="socialSecurityData"
                    :pagination="false"
                    :row-key="(record) => record.id"
                    class="smallTable"
                    bordered
                />
            </div>
        </div>
        <div class="param-box">
            <div class="title">医保</div>
            <div class="box-body">
                <div class="form-flex">
                    <FormItem label="医保编号">
                        <Input v-model:value="formData.medicalInsuranceNum" placeholder="社保编号" :disabled="!RoleState" />
                    </FormItem>
                    <FormItem label="医保基数">
                        <Input v-model:value="formData.medicalInsuranceCardinal" placeholder="社保基数" :disabled="!RoleState" />
                    </FormItem>
                </div>
            </div>
        </div>
        <div class="param-box">
            <div class="title">公积金</div>
            <div class="box-body">
                <div class="form-flex">
                    <FormItem label="公积金编号">
                        <Input v-model:value="formData.accumulationFundNum" placeholder="社保编号" :disabled="!RoleState" />
                    </FormItem>
                    <FormItem label="公积金基数">
                        <Input v-model:value="formData.accumulationFundCardinal" placeholder="社保基数" :disabled="!RoleState" />
                    </FormItem>
                </div>
            </div>
            <Table
                :columns="accumulationColumns"
                :data-source="accumulationData"
                :pagination="false"
                :row-key="(record) => record.id"
                class="smallTable"
                bordered
            />
        </div>
    </Form>
    <div class="ant-modal-footer" v-if="RoleState">
        <Button key="back" @click="cancel">取消</Button>
        <Button key="submit" type="primary" @click="confirm">确定</Button>
    </div>
</template>
<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import moment from 'moment'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
// import permissionStore from '/@/store/modules/permission'
export default defineComponent({
    name: 'SalsryParams',
    props: {
        staffId: String,
        viewType: String,
        visible: Boolean,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { staffId, viewType, visible } = toRefs<any>(props)
        //  RoleState = permissionStore().getPermission.staffState // 客户false  企业true
        const RoleState = ref(viewType.value == 'edit' || viewType.value == 'perfect')

        const bankList = ref<object[]>([]) // 银行数据
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/ownedBank', {}).then((res) => {
                bankList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        //社保表格数据
        const socialSecurityColumns = [
            {
                title: '社保类型',
                dataIndex: 'socialSecurityName',
                align: 'center',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
            },
            {
                title: '收款单位名称',
                dataIndex: 'nameOfBeneficiary',
                align: 'center',
            },
            {
                title: '收款单位账号',
                dataIndex: 'receivingAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'accountBank',
                align: 'center',
            },
            {
                title: '单位养老',
                dataIndex: 'unitPension',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位医疗',
                dataIndex: 'unitMedical',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位工伤',
                dataIndex: 'workInjury',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '单位失业',
                dataIndex: 'unitUnemployment',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人养老',
                dataIndex: 'personalPension',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人医疗',
                dataIndex: 'personalMedical',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人失业',
                dataIndex: 'personalUnemployment',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]
        //公积金表格数据
        const accumulationColumns = [
            {
                title: '公积金类型名称',
                dataIndex: 'typeName',
                align: 'center',
            },
            {
                title: '地区',
                dataIndex: 'area',
                align: 'center',
            },
            {
                title: '收款单位名称',
                dataIndex: 'payeeName',
                align: 'center',
            },
            {
                title: '收款单位账号',
                dataIndex: 'payeeAccount',
                align: 'center',
            },
            {
                title: '收款单位开户行',
                dataIndex: 'payeeBank',
                align: 'center',
            },
            {
                title: '单位比例',
                dataIndex: 'unitScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
            {
                title: '个人比例',
                dataIndex: 'personageScale',
                align: 'center',
                customRender: ({ text }) => {
                    if (text) {
                        text = (text * 100).toFixed(2) + '%'
                    }
                    return text
                },
            },
        ]
        const socialSecurityData = ref<object[]>([])
        const accumulationData = ref<object[]>([])

        const basicDetail = ref<object>({})

        // Form Data
        const formData = ref<any>({
            id: null,
            staffId: null,
            basicWage: null,
            ownedBank: null,
            salaryCardNum: null,
            socialSecurityNum: null,
            socialSecurityCardinal: null,
            medicalInsuranceNum: null,
            medicalInsuranceCardinal: null,
            accumulationFundNum: null,
            accumulationFundCardinal: null,
        })
        const resetFormData = () => {
            formData.value = {}
        }

        // 获取薪酬参数详情
        const getDetail = (value) => {
            request.get('/api/hr-staff-emoluments', { staffId: value }).then((res) => {
                formData.value.id = res.id
                formData.value.basicWage = res.basicWage
                formData.value.ownedBank = res.ownedBank
                formData.value.salaryCardNum = res.salaryCardNum
                formData.value.socialSecurityNum = res.socialSecurityNum
                formData.value.socialSecurityCardinal = res.socialSecurityCardinal
                formData.value.medicalInsuranceNum = res.medicalInsuranceNum
                formData.value.medicalInsuranceCardinal = res.medicalInsuranceCardinal
                formData.value.accumulationFundNum = res.accumulationFundNum
                formData.value.accumulationFundCardinal = res.accumulationFundCardinal
                socialSecurityData.value = res.socialSecurityDTOList //社保表格数据
                accumulationData.value = res.accumulationFundDTOList //公积金表格数据
            })
        }
        watch(
            visible,
            () => {
                if (!visible.value) {
                    return
                }
                if (staffId.value) {
                    RoleState.value = viewType.value == 'edit' || viewType.value == 'perfect'
                    getDetail(staffId.value)
                }
            },
            { immediate: true },
        )

        const confirm = () => {
            formData.value.staffId = staffId.value

            request.put('/api/hr-staff-emoluments/update', formData.value).then((res) => {
                message.success('保存成功!')
                emit('confirm', formData.value)
                cancel()
            })
        }
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        return {
            formData,
            socialSecurityColumns,
            socialSecurityData,
            bankList,
            RoleState,
            accumulationColumns,
            accumulationData,
            basicDetail,
            cancel,
            confirm,
        }
    },
})
</script>
<style scoped lang="less">
.param-box {
    margin: 20px 0px;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    box-sizing: border-box;
    .title {
        background-color: #6894fe;
        color: #fff;
        padding: 5px 10px;
    }
    .box-body {
        padding-top: 24px;
    }
}
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.ant-modal-body {
    padding: 24px 0 0 !important;
    & > .ant-form.ant-form-horizontal {
        margin: 0 24px 24px;
    }
}
.smallTable {
    margin: 0 15px 30px;
    // border: 1px solid #e8e8e8;
    :deep(.ant-table-thead > tr > th) {
        background-color: #fafafa;
        color: #000;
    }
}
</style>
