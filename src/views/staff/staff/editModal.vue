<template>
    <BasicEditModalSlot
        class="Nofooter"
        :visible="visible"
        @cancel="cancel"
        @confirm="confirm"
        :title="title"
        width="1350px"
        :footer="null"
    >
        <InsuredMessage @cancel="cancel" @confirm="confirm" />
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
import insuredMessage from './insuredMessage.vue'
export default defineComponent({
    name: 'Addstaff',
    components: {
        InsuredMessage: insuredMessage,
    },
    props: {
        title: String,
        item: {
            type: Object,
            default: () => {},
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        // const { item, visible } = toRefs<any>(props)
        // // Form 实例
        // const refAddCustomerInfo = ref(null) as any
        // const refAddCustomerProtoco = ref(null) as any

        // const tabs = ref('tab1')
        // // tab切换
        // const tabsChange = (value) => {
        //     tabs.value = value.target.value
        // }
        // const protocoId = ref(null) as any
        // watch(visible, () => {
        //     if (visible.value) {
        //         protocoId.value = item.value.id
        //     }
        // })

        const cancel = () => {
            // refAddCustomerInfo.value.resetData()
            emit('cancel')
        }

        const confirm = () => {
            cancel()
            emit('confirm')
        }
        const nextConfirm = () => {}
        const backConfirm = () => {}

        return {
            // 按钮
            confirm,
            cancel,
            nextConfirm,
            backConfirm,

            //refs实例
            // refAddCustomerInfo,
            // refAddCustomerProtoco,

            // protocoId,
            // tabs,
            // tabsChange,
        }
    },
})
</script>
<style lang="less">
.Nofooter {
    .ant-modal-body {
        padding: 24px 0 0 !important;
        & > div > .ant-form.ant-form-horizontal {
            margin: 0 24px 24px;
        }
    }
}
</style>
<style scoped lang="less">
.RadioGroup {
    margin: 0 24px 24px;
}
</style>
