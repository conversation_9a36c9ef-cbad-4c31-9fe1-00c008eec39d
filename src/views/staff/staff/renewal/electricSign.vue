<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1200px" :footer="null">
        <!-- 选择合同 -->
        <div v-show="tab == 1">
            <SelectTemplate :visible="visible" @changeTab="changeTab" @cancel="cancel" viewType="renewal" :type="type" />
        </div>
        <!-- 必填附件
        <div v-if="tab == 2">
            <Enclosure :visible="visible" :update="false" @changeTab="changeTab" @cancel="cancel" />
        </div> -->
        <!-- 发送电签 -->
        <div v-if="tab == 3">
            <SendConfirm
                :visible="visible"
                :type="type"
                @changeTab="changeTab"
                @cancel="cancel"
                @confirm="confirm"
                viewType="renewal"
            />
        </div>
    </BasicEditModalSlot>
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue'
import signStore from '/@/store/modules/electricSign'
import staffStore from '/@/store/modules/staff'
// import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
import selectTemplate from '/@/views/serviceCentre/inductionServices/stayInductionStaff/signContract/selectTemplate.vue'
import sendConfirm from '/@/views/serviceCentre/inductionServices/stayInductionStaff/signContract/sendConfirm.vue'
// import signStore from '/@/store/modules/electricSign'
import request from '/@/utils/request'
export default defineComponent({
    name: 'ElectricSign',
    components: {
        SelectTemplate: selectTemplate,
        SendConfirm: sendConfirm,
    },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'single',
            validator: (val: string) => {
                return ['single', 'multiple'].includes(val)
            },
        },
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { visible, type, currentValue } = toRefs<any>(props)
        const tab = ref(1)
        watch(visible, () => {
            if (visible.value) {
                tab.value = 1
            }
        })
        const contGroup = ref<any>({})
        const changeTab = (value, item) => {
            contGroup.value = item
            if (value == 3) {
                getTemplate()
            } else {
                tab.value = value
            }
        }

        const getTemplate = async () => {
            const formData = ref<inObject>({})
            formData.value.staffId = staffStore().getStaff.staffId
            formData.value.clientId = signStore().getSign.clientId
            formData.value.templateSource = signStore().getSign.tabs // 合同来源方式
            formData.value.templateIds = signStore().getSign.staffContactList //合同模板Ids
            formData.value.contractStartDate = currentValue.value?.contractStartDate
            formData.value.contractEndDate = currentValue.value?.contractEndDate

            if (signStore().getSign.tabs == '2') {
                formData.value.contractGroupId = contGroup.value?.id
                formData.value.templateIds = undefined
            }
            // if (update.value) {
            //     formData.value.contractId = signStore().getSign.templateData?.contractList[0]?.contractId || ''
            // }
            // return
            // setTimeout(() => {
            //     tab.value = 3
            // }, 1000)
            let api =
                type.value == 'multiple'
                    ? '/api/hr-talent-staff/batch-renewal-generate-contract'
                    : '/api/hr-talent-staff/renewal-generate-contract'
            request
                .post(
                    api,
                    type.value == 'multiple'
                        ? {
                              staffIds: staffStore().getStaff.staffList.map((el) => el.id) || [],
                              clientId: staffStore().getStaff.staffList[0].clientId || '',
                              templateIds:
                                  signStore().getSign.tabs == 2
                                      ? contGroup.value?.contractTemplate?.split(',')
                                      : signStore().getSign.staffContactList || [],
                              templateSource: formData.value.templateSource || '',
                              contractGroupId: contGroup.value?.id,
                          }
                        : formData.value,
                    { loading: true },
                )
                .then((res) => {
                    console.log(res)
                    signStore().setTemplateData(res)
                    tab.value = 3

                    // if (row == 'next') {
                    //     emit('changeTab', '3')
                    // } else {
                    //     emit('cancel')
                    // }
                })
        }
        const cancel = () => {
            signStore().setContractTemplate([])
            signStore().setMustData([])
            signStore().setMoreData([])
            signStore().setTabs('1')
            emit('cancel')
        }
        const confirm = () => {
            emit('confirm')
        }
        return {
            tab,
            confirm,
            cancel,
            changeTab,
        }
    },
})
</script>
<style scoped lang="less">
.btn-flex {
    display: flex;
    justify-content: space-between;
}
</style>
