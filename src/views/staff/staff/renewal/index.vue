<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="cancel" title="续签" width="800px" :footer="null">
        <!-- 第一步选择员工或客户 -->
        <div class="content">
            <div>
                <div class="up">
                    <div class="title">线上签章</div>
                    <CopyOutlined class="icon" />
                    <Button type="primary" class="btn" @click="confirmOnline">制作合同</Button>
                </div>
            </div>
            <div>
                <div class="up">
                    <div class="title">线下签章</div>
                    <CloudUploadOutlined class="icon" />
                    <Button type="primary" class="btn" @click="confirmOffline">录入合同</Button>
                </div>
            </div>
        </div>
    </BasicEditModalSlot>
    <ElectricSign :visible="showSign" title="发起续签" :currentValue="currentValue" @cancel="signCancel" @confirm="signConfirm" />
    <!-- 调入调出 -->
    <TransferModal
        viewType="laborContractRe"
        title="录入合同"
        :visible="showTransfer"
        :currentData="currentData"
        @cancel="transferClose"
        @confirm="transferConfirm"
    />
</template>

<script lang="ts">
import { CloudUploadOutlined, CopyOutlined } from '@ant-design/icons-vue'
import request from '/@/utils/request'

import { defineComponent, ref, toRefs } from 'vue'
import ElectricSign from './electricSign.vue'
import signStore from '/@/store/modules/electricSign'
import staffStore from '/@/store/modules/staff'
import TransferModal from '/@/views/recordAdmin/recordList/TransferModal.vue'
import { message } from 'ant-design-vue'
export default defineComponent({
    name: 'CertificatModal11',
    components: { CloudUploadOutlined, CopyOutlined, ElectricSign, TransferModal },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['confirmOnline', 'confirmOffline', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { currentValue } = toRefs<any>(props)

        // cancel handle
        // const appendixIdList = ref([])
        // const appendixIdListOnline = ref([])
        // const appendixIdListChange = (list) => {
        //     if (list) {
        //         cancel()
        //         emit('confirmOnline', list)
        //         appendixIdListOnline.value = []
        //     }
        // }
        // const refAppendixIdListOnline = ref()
        // const openImportFile = () => {
        //     console.log(refAppendixIdListOnline.value)
        // }
        const cancel = () => {
            // appendixIdList.value = []
            emit('cancel')
            emit('update:visible', false)
        }
        // const confirmOnline = () => {
        //     cancel()
        //     emit('confirmOnline')
        // }
        // const refImportFile = ref()

        // 发起电签
        const showSign = ref(false)
        // 打开发起电签弹窗
        const confirmOnline = () => {
            showSign.value = true
            console.log(currentValue.value)
            // // currentValue.value = { ...record, id: record.staffId, employedId: record.id }
            staffStore().setStaff(currentValue.value.id)
            signStore().setClientId(currentValue.value.clientId)
            signStore().setTabs('3')
        }
        // 关闭发起电签弹窗
        const signCancel = () => {
            showSign.value = false
            // currentValue.value = null
        }
        // 发起电签确认弹窗
        const signConfirm = async () => {
            cancel()
            emit('confirmOnline')

            // tableRef.value.refresh()
        }

        //档案调入
        const confirmOffline = () => {
            request.get(`/api/hr-archives-manage/${currentValue.value?.clientId}/${currentValue.value?.id}`).then((res) => {
                currentData.value = { ...res, staffData: currentValue.value }
                showTransfer.value = true
            })
        }
        const showTransfer = ref(false)
        const currentData = ref({})
        // const transferIn = (record) => {
        //     request.get(`/api/hr-archives-manage/${record.clientId}/${record.staffId}`).then((res) => {
        //         currentData.value = res
        //         showTransfer.value = true
        //     })
        // }
        const transferClose = () => {
            showTransfer.value = false
            currentData.value = {}
        }
        const transferConfirm = () => {
            showTransfer.value = false
            cancel()
            emit('confirmOffline')
            // currentData.value = {}
        }

        return {
            //formData
            // appendixIdList,
            cancel,
            // confirmOnline,
            confirmOffline,

            // refAppendixIdListOnline,
            // openImportFile,
            // appendixIdListOnline,
            // appendixIdListChange,
            // refImportFile,

            //start 发起续签
            confirmOnline,
            showSign,
            // signRow,
            signCancel,
            signConfirm,

            //档案调入
            // start
            showTransfer,
            currentData,
            // transferIn,
            transferClose,
            transferConfirm,
            //end
        }
    },
})
</script>
<style scoped lang="less">
.content {
    display: flex;
    justify-content: space-around;
    align-content: center;
    margin: 30px 10px;

    .down {
        // display: flex;
        // flex-direction: column;
        // justify-content: flex-start;
        // align-items: center;
        width: 300px;
        margin: 0 auto;
        border-radius: 5px;
        height: 200px;
        border: 1px solid #ccc;
        padding-top: 0;
        border: 1px solid rgb(231, 229, 229);
        .btn {
            margin-left: 50px;
        }
    }
    .up {
        display: flex;
        border-radius: 5px;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        margin: 0 auto;
        width: 300px;
        height: 200px;
        border: 1px solid rgb(231, 229, 229);
    }
}
.title {
    color: white;
    background: #618aed;
    width: 100%;
    text-align: center;

    height: 40px;
    line-height: 40px;
    font-size: 16px;
}
.icon {
    margin: 40px 0 15px 0;
    font-size: 50px;
    color: #618aed;
}
.text {
    font-size: 16px;
    text-align: center;
    line-height: 16px;
    margin-top: 15px;
}
.enclosureBox {
    margin-top: 20px;
    height: 80px;

    label {
        display: inline-block;
        width: 60px;
        text-align: right;
        line-height: 32px;
        padding-right: 10px;
    }
    .refImportFile {
        width: calc(100% - 70px);
        display: inline-block;
        vertical-align: top;
    }
}
</style>
