<template>
    <div class="add-work">
        <div class="work-title">
            <p>工作经历</p>
            <Button type="primary" size="small" v-if="dataDetail && roleState" @click="addWork">新增工作经历</Button>
        </div>
        <div v-show="dataDetail">
            <div v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="company-title">
                    <div class="title-name">
                        <div class="name">{{ item.employerUnit }}</div>
                        <div class="title-span">
                            <span>{{ item.deptName }}</span>
                            <Divider type="vertical" />
                            <span>{{ item.professionName }}</span>
                        </div>
                    </div>
                    <div class="company-edit" @click="editWork(item)" v-if="roleState">
                        <FormOutlined style="margin-right: 10px" />
                        <span>编辑</span>
                    </div>
                </div>
                <div class="work-flex">
                    <div class="flex-span">
                        <span>人员类型：</span>
                        <span>{{ item.personnelTypeLabel }}</span>
                    </div>
                    <div class="flex-span">
                        <span>在职时间：</span>
                        <span>{{ item.boardDate }} 至 {{ item.departureDate }}</span>
                    </div>
                    <div class="flex-span">
                        <span>月薪区间：</span>
                        <span>{{ item.salarySectionLabel }}元/月</span>
                    </div>
                    <div class="flex-span">
                        <span>工作地性质：</span>
                        <span>{{ item.workNatureLabel }}</span>
                    </div>
                    <div class="flex-span">
                        <span>工作地址：</span>
                        <span>{{ item.workLocation }}</span>
                    </div>
                    <div class="flex-span">
                        <span>证明人：</span>
                        <span>{{ item.certifyPeople }}</span>
                    </div>
                    <div class="flex-span">
                        <span>证明人电话：</span>
                        <span>{{ item.certifyPhone }}</span>
                    </div>
                    <div class="flex-span">
                        <span>离职原因：</span>
                        <span>{{ item.dimissionReason }}</span>
                    </div>
                </div>
                <div class="flex-span1">
                    <span>工作成果：</span>
                    <span>{{ item.workAchievement }}</span>
                </div>
                <div class="flex-span1">
                    <span>自我评价：</span>
                    <span>{{ item.selfEvaluation }}</span>
                </div>
                <div class="flex-span1" v-if="roleState">
                    <span>备注：</span>
                    <span>{{ item.workRemark }}</span>
                </div>
            </div>
        </div>

        <div class="form-box" v-if="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="(item, index) in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[index]" allowClear />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="btn">
                <Button danger type="primary" @click="delRow" v-if="formData.id">删除</Button>
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
        <div v-if="(!dataDetail || dataDetail.length <= 0) && !roleState" class="defaulBox">暂无工作经历</div>
        <div v-else-if="!dataDetail || dataDetail.length <= 0" class="work-null" @click="addWork">+ 工作经历</div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import moment from 'moment'
import moment, { Moment } from 'moment'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { FormOutlined } from '@ant-design/icons-vue'
export default defineComponent({
    name: 'WorkExperience',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        roleState: Boolean,
        visible: Boolean,
        personTypeList: Array,
        salaryList: Array,
        workNatureList:Array,
    },
    emits: ['update:dataDetail'],
    setup(props, { emit }) {
        // const stationList = ref<object[]>([]) // 岗位
        // const personTypeList = ref<object[]>([]) // 人员类型
        // const salaryList = ref<object[]>([]) // 月薪区间
        // const workNatureList = ref<object[]>([]) // 工作地性质
        const showAdd = ref(false)
        const editId = ref('')
        const { dataDetail, staffId, visible,personTypeList,salaryList,workNatureList } = toRefs<any>(props)

        onMounted(() => {
            // request.get('/api/hr-stations/list', {}).then((res) => {
            //     stationList.value = res.map((item) => {
            //         return { label: item.professionName, value: item.id }
            //     })
            // })
     
       
            // request.get('/api/com-code-tables/getCodeTableByInnerName/workNature', {}).then((res) => {
            //     workNatureList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
        })

        // 电话号码的验证
        const myValidatePhone = (rule: RuleObject, value) => {
            const tel = /^1[345789]\d{9}$/
            if (!tel.test(value) && value) {
                return Promise.reject('请正确填写手机号')
            } else {
                return Promise.resolve()
            }
        }
        const myOptions = ref([
            {
                label: '客户名称',
                name: 'employerUnit',
            },
            {
                label: '部门名称',
                name: 'deptName',
                required: false,
            },
            {
                label: '岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: personTypeList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '月薪区间',
                name: 'salarySection',
                type: 'change',
                options: salaryList,

                showbr: true, //换行
                ruleType: 'number',
            },
            {
                label: '工作地性质',
                name: 'workNature',
                type: 'change',
                options: workNatureList,

                required: false,
                ruleType: 'number',
            },
            {
                label: '工作地址',
                name: 'workLocation',
                width: '66%',
            },
            {
                label: '入职日期',
                name: 'boardDate',
                type: 'date',

                disabledDate: (startValue: Moment) => {
                    if (!startValue) {
                        return false
                    }
                    return startValue.endOf('day').valueOf() > new Date().valueOf()
                },
                onChange: () => {
                    formData.value['departureDate'] = undefined
                },
            },
            {
                label: '离职日期',
                name: 'departureDate',
                type: 'date',
                required: false,
                disabledDate: (endValue: Moment) => {
                    if (!endValue) {
                        return false
                    }
                    if (formData.value?.boardDate)
                        return (
                            new Date(formData.value?.boardDate).valueOf() >= endValue.endOf('day').valueOf() ||
                            endValue.endOf('day').valueOf() > new Date().valueOf()
                        )
                    else return endValue.endOf('day').valueOf() > new Date().valueOf()
                },
            },
            {
                label: '离职原因',
                name: 'dimissionReason',
                required: false,
            },
            {
                label: '证明人',
                name: 'certifyPeople',
                required: false,
            },
            {
                label: '证明人电话',
                name: 'certifyPhone',
                required: false,
                validator: myValidatePhone,
            },
            {
                label: '工作成果',
                name: 'workAchievement',
                required: false,
                width: '100%',
            },
            {
                label: '自我评价',
                name: 'selfEvaluation',
                required: false,
                width: '100%',
            },
            {
                label: '备注',
                name: 'workRemark',
                required: false,
                width: '100%',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })
        const addWork = () => {
            showAdd.value = true
        }
        // 编辑
        const editWork = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-work-experiences', { id: item.id, izDefault: false }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-work-experiences/delete?id=${editId.value}`, {}).then((res) => {
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }
        const confirm = () => {
            formData.value.izDefault = false
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    if (formData?.value.id) {
                        request.put('/api/hr-work-experiences/update', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('编辑成功!')
                        })
                    } else {
                        request.post('/api/hr-work-experiences/create', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('新增成功!')
                        })
                    }
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            showAdd,
            addWork,
            editWork,
            cancel,
            confirm,
            resetFormData,
            delRow,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.defaulBox {
    text-align: center;
}
</style>
