<template>
    <div class="add-work">
        <div class="work-title">
            <p>语言能力</p>
            <Button type="primary" size="small" v-if="dataDetail && roleState" @click="addLanguage">新增语言能力</Button>
        </div>
        <div class="technical-list" v-if="dataDetail">
            <div class="technical-flex" v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="flex-div">{{ item.languageLabel }}</div>
                <div class="flex-div">{{ item.proficiencyLabel }}</div>
                <div class="flex-div">{{ item.ratingCertificate }}</div>
                <div class="flex-right fr" @click="editLanguage(item)" v-if="roleState">
                    <FormOutlined style="margin-right: 5px" />
                    编辑
                </div>
            </div>
        </div>

        <div class="form-box" v-show="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="item in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                </template>
            </Form>
            <div class="btn">
                <Button danger type="primary" @click="delRow" v-if="formData.id">删除</Button>
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
        <div v-if="(!dataDetail || dataDetail.length <= 0) && !roleState" class="defaulBox">暂无语言能力</div>
        <div v-else-if="!dataDetail || dataDetail.length <= 0" class="work-null" @click="addLanguage">+ 语言能力</div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import moment from 'moment'
import { valuesAndRules } from '/#/component'
import { validatePhone } from '/@/utils/format'
import { FormOutlined } from '@ant-design/icons-vue'
export default defineComponent({
    name: 'addStaffAdditional',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        roleState: Boolean,
        visible: Boolean,
        proficiencyList:Array,
    },
    emits: ['update:dataDetail'],
    setup(props, { emit }) {
        const languageList = ref<object[]>([]) // 语种
        const showAdd = ref(false)
        const editId = ref('')
        const { dataDetail, staffId, visible,proficiencyList } = toRefs<any>(props)
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/language', {}).then((res) => {
                languageList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
         
        })

        const myOptions = ref([
            {
                label: '语种',
                name: 'language',
                type: 'change',
                options: languageList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '熟练程度',
                name: 'proficiency',
                type: 'change',
                options: proficiencyList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '等级证书',
                name: 'ratingCertificate',
                required: false,
            },
        ])

     
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const addLanguage = () => {
            showAdd.value = true
        }
        // 编辑
        const editLanguage = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-staff-languages', { id: item.id }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-staff-languages/delete?id=${editId.value}`, {}).then((res) => {
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })

        const confirm = () => {
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')
                    if (formData?.value.id) {
                        request.put('/api/hr-staff-languages/update', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('编辑成功!')
                        })
                    } else {
                        request.post('/api/hr-staff-languages/create', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('新增成功!')
                        })
                    }
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            showAdd,
            addLanguage,
            editLanguage,
            cancel,
            confirm,
            delRow,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.defaulBox {
    text-align: center;
}
</style>
