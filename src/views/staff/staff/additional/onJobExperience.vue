<template>
    <div class="add-work">
        <div class="work-title">
            <p>在职信息</p>
        </div>
        <div v-show="dataDetail">
            <div v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="company-title">
                    <div class="title-name">
                        <div class="name">{{ item.clientName }}</div>
                        <div class="title-span">
                            <span>{{ item.deptName }}</span>
                            <Divider type="vertical" />
                            <span style="margin: 0 7px">{{ item.professionName }}</span>
                        </div>
                    </div>
                    <div class="company-edit" @click="editWork(item)">
                        <FormOutlined style="margin-right: 10px" />
                        <span>编辑</span>
                    </div>
                </div>
                <div style="margin-top: 20px">
                    <!--  <div class="flex-span">
                        <span>人员类型：</span>
                        <span>{{ item.personnelTypeLabel }}</span>
                    </div>
                    <div class="flex-span">
                        <span>在职时间：</span>
                        <span>{{ item.boardDate }} 至 {{ item.departureDate }}</span>
                    </div>
                    <div class="flex-span">
                        <span>月薪区间：</span>
                        <span>{{ item.salarySectionLabel }}元/月</span>
                    </div>
                    <div class="flex-span">
                        <span>工作地性质：</span>
                        <span>{{ item.workNatureLabel }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="flex-span">
                        <span>工作地址：</span>
                        <span>{{ item.workLocation }}</span>
                    </div>
                 -->
                    <div class="flex-span1">
                        <span>人员类型：</span>
                        <span>{{ item.personnelTypeLabel }}</span>
                    </div>
                    <div class="flex-span1">
                        <span>工作地址：</span>
                        <span>{{ item.workLocation }}</span>
                    </div>
                    <div class="flex-span1">
                        <span>客户层级关系：</span>
                        <p class="customerHierarchy">
                            <template v-for="(clientDTOItem, inx) in hrClientDTOList[index]" :key="clientDTOItem.id">
                                <Tooltip overlayClassName="parentTooltip" color="#fff" arrowPointAtCenter>
                                    <template #title>
                                        <div style="color: #000">
                                            <p>
                                                <span class="label">单位编号：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.unitNumber"
                                                >
                                                    <span class="text">{{ clientDTOItem.unitNumber }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span class="label">用户名：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.userName"
                                                >
                                                    <span class="text">{{ clientDTOItem.userName }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span class="label">客户名称：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.clientName"
                                                >
                                                    <span class="text">{{ clientDTOItem.clientName }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span class="label">客户类型：</span>
                                                <Tooltip arrowPointAtCenter overlayClassName="childrenTooltip">
                                                    <template #title>{{
                                                        customerTypeOptions.find((itemOptions) => {
                                                            return clientDTOItem.customerType == itemOptions.value
                                                        })?.label || ''
                                                    }}</template>
                                                    <span class="text">{{
                                                        customerTypeOptions.find((itemOptions) => {
                                                            return clientDTOItem.customerType == itemOptions.value
                                                        })?.label || ''
                                                    }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span>当前协议编号：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.agreementNumber"
                                                >
                                                    <span class="text">{{ clientDTOItem.agreementNumber }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span>协议类型：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.agreementTypekey"
                                                >
                                                    <span class="text">{{ clientDTOItem.agreementTypekey }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span>协议开始日期：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.agreementStartDate"
                                                >
                                                    <span class="text">{{ clientDTOItem.agreementStartDate }}</span>
                                                </Tooltip>
                                            </p>
                                            <p>
                                                <span>协议结束日期：</span>
                                                <Tooltip
                                                    arrowPointAtCenter
                                                    overlayClassName="childrenTooltip"
                                                    :title="clientDTOItem.agreementEndDate"
                                                >
                                                    <span class="text">{{ clientDTOItem.agreementEndDate }}</span>
                                                </Tooltip>
                                            </p>
                                        </div>
                                    </template>
                                    <Button type="link">{{ clientDTOItem.clientName }}</Button>
                                </Tooltip>
                                <span v-if="item.hrClientDTOList?.length > inx + 1">—</span>
                            </template>
                        </p>
                    </div>
                    <div class="flex-span1">
                        <span>工作成果：</span>
                        <span>{{ item.workAchievement }}</span>
                    </div>
                    <div class="flex-span1">
                        <span>备注：</span>
                        <span>{{ item.workRemark }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-box" v-if="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="(item, index) in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[index]" allowClear />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="btn">
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, computed, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import moment from 'moment'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
import { FormOutlined } from '@ant-design/icons-vue'
import { customerTypeOptions } from '/@/utils/dictionaries'
export default defineComponent({
    name: 'WorkExperience',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        visible: Boolean,
        viewType: String,
        personTypeList: Array,
        salaryList: Array,
        workNatureList:Array
    },
    emits: ['update:dataDetail', 'getInfoPerfect', 'confirm'],
    setup(props, { emit }) {
        // const stationList = ref<object[]>([]) // 岗位
        // const salaryList = ref<object[]>([]) // 月薪区间
        // const workNatureList = ref<object[]>([]) // 工作地性质
        const showAdd = ref(false)
        const editId = ref('')
        const { dataDetail, staffId, visible, viewType,personTypeList,salaryList,workNatureList } = toRefs<any>(props)
        onMounted(() => {
            // request.get('/api/hr-stations/list', {}).then((res) => {
            //     stationList.value = res.map((item) => {
            //         return { label: item.professionName, value: item.id }
            //     })
            // })
         
            // request.get('/api/com-code-tables/getCodeTableByInnerName/monthlyPay', {}).then((res) => {
            //     salaryList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
            // request.get('/api/com-code-tables/getCodeTableByInnerName/workNature', {}).then((res) => {
            //     workNatureList.value = res.map((item) => {
            //         return { label: item.itemName, value: item.itemValue }
            //     })
            // })
        })
        const workName = ref('employerUnit')

        const myOptions = ref([
            {
                label: '客户名称',
                name: 'clientName',
                disabled: true,
            },
            {
                label: '部门名称',
                name: 'deptName',
                required: false,
            },
            {
                label: '岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
            },
            {
                label: '人员类型',
                name: 'personnelType',
                type: 'change',
                options: personTypeList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '入职日期',
                name: 'boardDate',
                type: 'date',
                disabled: true,
            },
            {
                label: '月薪区间',
                name: 'salarySection',
                type: 'change',
                options: salaryList,
                trigger: 'change',
                ruleType: 'number',
                required: false,
            },
            {
                label: '工作地性质',
                name: 'workNature',
                type: 'change',
                options: workNatureList,
                trigger: 'change',
                ruleType: 'number',
                required: false,
            },
            {
                label: '工作地址',
                name: 'workLocation',
                width: '66%',
            },
            {
                label: '工作成果',
                name: 'workAchievement',
                required: false,
                width: '100%',
            },
            {
                label: '备注',
                name: 'workRemark',
                required: false,
                width: '100%',
            },
        ])

      
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const hrClientDTOList = computed(() => {
            return dataDetail.value?.map((element) => {
                let hrClientDTOObj: any = {}
                let newHrClientDTOList: any[] = []
                element?.hrClientDTOList?.forEach((elementClient) => {
                    hrClientDTOObj[elementClient.id] = elementClient
                })
                for (let i = 0; i < element?.hrClientDTOList?.length || 0; i++) {
                    if (i == 0) {
                        newHrClientDTOList.push(hrClientDTOObj[element.clientId])
                    } else {
                        let newHrClientDTOObj = hrClientDTOObj[newHrClientDTOList[i - 1]?.parentId || 'sun']
                        if (newHrClientDTOObj) {
                            newHrClientDTOList.push(newHrClientDTOObj)
                        }
                    }
                }
                return newHrClientDTOList
            })
        })

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })
        const addWork = () => {
            showAdd.value = true
        }
        // 编辑
        const editWork = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-work-experiences', { id: item.id, izDefault: true }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-work-experiences/delete?id=${editId.value}`, {}).then((res) => {
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }
        const confirm = () => {
            formData.value.izDefault = true
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')
                    request.put('/api/hr-work-experiences/update', formData.value).then((res) => {
                        dataDetail.value = res
                        emit('update:dataDetail', res)
                        message.success('编辑成功!')
                        cancel()
                        emit('confirm')
                        if (viewType.value == 'perfect') {
                            emit('getInfoPerfect', 'tab2', false, false)
                        }
                    })
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            showAdd,
            addWork,
            editWork,
            cancel,
            confirm,
            resetFormData,
            delRow,

            hrClientDTOList,
            customerTypeOptions,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.customerHierarchy {
    display: inline-block;
    width: calc(100% - 120px);
    .text {
        width: calc(100% - 100px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: top;
    }
    p {
        display: inline-block;
        width: 25%;
        line-height: 30px;
    }
    :deep(.parentTooltip) {
        .ant-tooltip-inner {
            width: 1000px;
        }
    }

    :deep(.childrenTooltip) {
        .ant-tooltip-inner {
            width: auto !important;
        }
    }
    :deep(.ant-btn.ant-btn-link) {
        line-height: 22px;
        height: 22px;
        padding-bottom: 0;
        padding-top: 0;
    }
}
</style>
