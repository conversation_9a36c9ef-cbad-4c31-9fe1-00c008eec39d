<template>
    <div class="add-work">
        <div class="work-title">
            <p>应试经历</p>
            <Button type="primary" size="small" v-if="dataDetail && roleState" @click="addInterview">新增应试经历</Button>
        </div>
        <div v-if="dataDetail">
            <div v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="company-title">
                    <div class="title-name">
                        <div class="name">{{ item.clientName }}</div>
                        <div class="title-span">
                            <span>{{ item.professionName }}</span>
                        </div>
                    </div>
                    <div style="color: #1890ff" @click="editInterview(item)" v-if="roleState && !item.paperId && !item.examName">
                        <FormOutlined style="margin-right: 10px" />
                        <span>编辑</span>
                    </div>
                </div>
                <div class="work-flex">
                    <div class="flex-span2">
                        <span>应试环节：</span>
                        <span>{{ item.interviewLinkLabel }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>应试时间：</span>
                        <span>{{ item.examDate }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>应试地点：</span>
                        <span>{{ item.interviewPlace }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>考官：</span>
                        <span>{{ item.interviewExaminer }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>应试成绩：</span>
                        <span>{{ item.score }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>应试结果：</span>
                        <span>{{ item.interviewResultLabel }}</span>
                    </div>
                    <div class="flex-span2">
                        <span>应试评价：</span>
                        <span>{{ item.evaluation }}</span>
                    </div>
                </div>
                <div class="flex-span1" v-if="roleState">
                    <span>备注：</span>
                    <span>{{ item.interviewRemark }}</span>
                </div>
            </div>
        </div>
        <div class="form-box" v-show="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="(item, index) in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots">
                        <template #stationCascader>
                            <StationCascader v-model:value="formData.stationId" v-model:itemForm="myOptions[index]" allowClear />
                        </template>
                    </MyFormItem>
                </template>
            </Form>
            <div class="btn">
                <Button danger type="primary" @click="delRow" v-if="formData.id">删除</Button>
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
        <div v-if="(!dataDetail || dataDetail.length <= 0) && !roleState" class="defaulBox">暂无应试经历</div>
        <div v-else-if="!dataDetail || dataDetail.length <= 0" class="work-null" @click="addInterview">+ 应试经历</div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import moment from 'moment'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
import { FormOutlined } from '@ant-design/icons-vue'
import { Moment } from 'moment'
export default defineComponent({
    name: 'InterviewExperience',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        roleState: Boolean,
        visible: Boolean,
    },
    emits: ['update:dataDetail'],
    setup(props, { emit }) {
        // const stationList = ref<object[]>([]) // 岗位
        const interviewList = ref<object[]>([]) // 应试环节
        const interviewResultList = ref<object[]>([]) // 应试结果
        const showAdd = ref(false)
        const editId = ref('')
        const { dataDetail, staffId, visible } = toRefs<any>(props)

        onMounted(() => {
            // request.get('/api/hr-stations/list', {}).then((res) => {
            //     stationList.value = res.map((item) => {
            //         return { label: item.professionName, value: item.id }
            //     })
            // })
            request.get('/api/com-code-tables/getCodeTableByInnerName/interviewLink', {}).then((res) => {
                interviewList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/interviewResult', {}).then((res) => {
                interviewResultList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })
        // 改变应试岗位
        const professionNameChange = (value: string, option: inObject) => {
            formData.value.professionName = option?.label
            // formData.value.stationId = value
        }

        const myOptions = ref([
            {
                label: '应试单位',
                name: 'clientName',
            },
            {
                label: '应试岗位',
                name: 'stationId',
                type: 'slots',
                slots: 'stationCascader',
                onChange: professionNameChange,
            },
            {
                label: '应试环节',
                name: 'interviewLink',
                type: 'change',
                options: interviewList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '应试时间',
                name: 'examDate',
                type: 'datetime',
                disabledDate: (startValue: Moment) => {
                    if (!startValue) {
                        return false
                    }
                    return startValue.endOf('day').valueOf() > new Date().valueOf()
                },
            },

            {
                label: '应试地点',
                name: 'interviewPlace',
                required: false,
            },
            {
                label: '考官',
                name: 'interviewExaminer',
                required: false,
            },
            {
                label: '应试成绩',
                name: 'score',
                type: 'number',
                ruleType: 'number',
                required: false,
            },

            {
                label: '应试结果',
                name: 'interviewResult',
                type: 'change',
                options: interviewResultList,
                trigger: 'change',
                ruleType: 'number',
            },
            {
                label: '应试评价',
                name: 'evaluation',
                required: false,
                width: '100%',
            },
            {
                label: '备注',
                name: 'interviewRemark',
                required: false,
                width: '100%',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const addInterview = () => {
            showAdd.value = true
        }
        // 编辑
        const editInterview = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-staff-interviews', { id: item.id }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-staff-interviews/delete?id=${editId.value}`, {}).then((res) => {
                console.log(res)
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })

        const confirm = () => {
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    if (formData?.value.id) {
                        request.put('/api/hr-staff-interviews/update', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('编辑成功!')
                        })
                    } else {
                        request.post('/api/hr-staff-interviews/create', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('新增成功!')
                        })
                    }
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            showAdd,
            addInterview,
            editInterview,
            cancel,
            confirm,
            delRow,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.defaulBox {
    text-align: center;
}
</style>
