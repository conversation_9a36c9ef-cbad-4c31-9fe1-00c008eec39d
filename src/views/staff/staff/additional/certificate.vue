<template>
    <div class="add-work">
        <div class="work-title">
            <p>证书</p>
            <Button type="primary" size="small" v-if="dataDetail && roleState" @click="addSkills">新增证书</Button>
        </div>
        <div class="certificate-list" v-if="dataDetail">
            <div class="certificate-flex" v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="flex-div">{{ item.certificateName }}</div>
                <div class="flex-div">{{ item.acquisitionDate }}</div>
                <div class="flex-div">
                    <span style="color: #999">颁发机构：</span>
                    {{ item.issuingAgency }}
                </div>
                <div class="flex-right fr" @click="editSkills(item)" v-if="roleState">
                    <FormOutlined style="margin-right: 5px" />
                    编辑
                </div>
            </div>
        </div>
        <div class="form-box" v-show="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="item in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                </template>
            </Form>
            <div class="btn">
                <Button danger type="primary" @click="delRow" v-if="formData.id">删除</Button>
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
        <div v-if="(!dataDetail || dataDetail.length <= 0) && !roleState" class="defaulBox">暂无证书</div>
        <div v-else-if="!dataDetail || dataDetail.length <= 0" class="work-null" @click="addSkills">+ 证书</div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
// import moment from 'moment'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
import { FormOutlined } from '@ant-design/icons-vue'
export default defineComponent({
    name: 'AddStaffAdditional',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        roleState: Boolean,
        visible: Boolean,
    },
    emits: ['update:dataDetail'],
    setup(props, { emit }) {
        const showAdd = ref(false)
        const editId = ref('')

        const myOptions = ref([
            {
                label: '证书名称',
                name: 'certificateName',
            },
            {
                label: '证书编号',
                name: 'certificateNo',
            },
            {
                label: '获得时间',
                name: 'acquisitionDate',
                type: 'date',
            },
            {
                label: '颁发机构',
                name: 'issuingAgency',
            },
        ])

        const { dataDetail, staffId, visible } = toRefs<any>(props)
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const addSkills = () => {
            showAdd.value = true
        }
        // 编辑
        const editSkills = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-staff-certificates', { id: item.id }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-staff-certificates/delete?id=${editId.value}`, {}).then((res) => {
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })

        const confirm = () => {
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    console.log('成功')
                    if (formData?.value.id) {
                        request.put('/api/hr-staff-certificates/update', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('编辑成功!')
                        })
                    } else {
                        request.post('/api/hr-staff-certificates/create', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('新增成功!')
                        })
                    }
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            showAdd,
            addSkills,
            editSkills,
            cancel,
            confirm,
            delRow,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.defaulBox {
    text-align: center;
    margin-bottom: 20px;
}
</style>
