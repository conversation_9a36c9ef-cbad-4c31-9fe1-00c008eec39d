<template>
    <div class="add-work">
        <div class="work-title">
            <p>教育经历</p>
            <Button type="primary" size="small" v-if="dataDetail && roleState" @click="addEducation">新增教育经历</Button>
        </div>
        <div class="technical-list" v-if="dataDetail">
            <div class="technical-flex" v-for="(item, index) in dataDetail" :key="index" style="margin-bottom: 20px">
                <div class="edu-title">{{ item.education }}</div>
                <div class="flex-div1">
                    <span>{{ item.studyModalityLabel }}</span>
                    <Divider type="vertical" />
                    <span>{{ item.highestDegreeLabel }}</span>
                    <Divider type="vertical" />
                    <span>{{ item.specialty }}</span>
                </div>
                <div class="flex-div1">
                    <span>{{ item.educationStartDate }}</span
                    >至
                    <span>{{ item.educationEndDate }}</span>
                </div>
                <div class="flex-div1">
                    <span style="color: #999">所获证书：</span>
                    <span>{{ item.certificate }}</span>
                </div>
                <div class="flex-right fr" @click="editEducation(item)" v-if="roleState">
                    <FormOutlined style="margin-right: 5px" />
                    编辑
                </div>
            </div>
        </div>
        <div class="form-box" v-show="showAdd">
            <Form
                ref="formInline"
                :model="formData"
                :label-col="{ style: { width: '130px' } }"
                :rules="rules"
                style="overflow-y: auto; margin-top: 20px"
                class="form-flex"
            >
                <template v-for="item in myOptions" :key="item">
                    <MyFormItem :width="item.width" :item="item" v-model:value="formData[item.name]" :class="item.slots" />
                </template>
            </Form>
            <div class="btn">
                <Button danger type="primary" @click="delRow" v-if="formData.id">删除</Button>
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="confirm">保存并更新</Button>
            </div>
        </div>
        <div v-if="(!dataDetail || dataDetail.length <= 0) && !roleState" class="defaulBox">暂无教育经历</div>
        <div v-else-if="!dataDetail || dataDetail.length <= 0" class="work-null" @click="addEducation">+ 教育经历</div>
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import moment, { Moment } from 'moment'
import { valuesAndRules } from '/#/component'
import { validatePhone } from '/@/utils/format'
import { FormOutlined } from '@ant-design/icons-vue'
export default defineComponent({
    name: 'EducationalAdditional',
    components: {
        FormOutlined,
    },
    props: {
        dataDetail: {
            type: Array,
        },
        staffId: String,
        roleState: Boolean,
        visible: Boolean,
    },
    emits: ['update:dataDetail'],
    setup(props, { emit }) {
        const educationList = ref<object[]>([]) // 最高学历
        const degreeList = ref<object[]>([]) // 学位
        const studyList = ref<object[]>([]) // 学习形式
        const showAdd = ref(false)
        const editId = ref('')

        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/educationStates', {}).then((res) => {
                educationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/degree', {}).then((res) => {
                degreeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/studyModality', {}).then((res) => {
                studyList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
        })

        const myOptions = ref([
            {
                label: '教育机构',
                name: 'education',
            },
            {
                label: '开始时间',
                name: 'educationStartDate',
                type: 'date',

                disabledDate: (startValue: Moment) => {
                    if (!startValue) {
                        return false
                    }
                    return startValue.startOf('day').valueOf() > new Date().valueOf()
                },
                onChange: () => {
                    formData.value['educationEndDate'] = undefined
                },
            },
            {
                label: '结束时间',
                name: 'educationEndDate',
                type: 'date',

                disabledDate: (endValue: Moment) => {
                    if (!endValue) {
                        return false
                    }
                    if (formData.value?.educationStartDate)
                        return (
                            new Date(formData.value?.educationStartDate).valueOf() >= endValue.endOf('day').valueOf() ||
                            endValue.endOf('day').valueOf() > new Date().valueOf()
                        )
                    else return endValue.endOf('day').valueOf() > new Date().valueOf()
                },
            },
            {
                label: '所学专业',
                name: 'specialty',
            },
            {
                label: '最高学历',
                name: 'highestDegree',
                type: 'change',
                options: educationList,
                ruleType: 'number',
            },
            {
                label: '学位',
                name: 'degree',
                type: 'change',
                options: degreeList,

                required: false,
                ruleType: 'number',
            },
            {
                label: '学习形式',
                name: 'studyModality',
                type: 'change',
                options: studyList,

                required: false,
                ruleType: 'number',
            },
            {
                label: '年制',
                name: 'yearNum',
                required: false,
                ruleType: 'number',
                type: 'number',
            },
            {
                label: '所获证书',
                name: 'certificate',
                required: false,
            },
        ])

        const { dataDetail, staffId, visible } = toRefs<any>(props)
        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)

        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        const addEducation = () => {
            showAdd.value = true
        }
        // 编辑
        const editEducation = (item) => {
            editId.value = item.id
            showAdd.value = true
            request.get('/api/hr-staff-educations', { id: item.id }).then((res) => {
                formData.value = { ...Object.assign({}, initFormData, res) }
            })
        }
        const cancel = () => {
            showAdd.value = false
            resetFormData()
        }
        const delRow = () => {
            request.delete(`/api/hr-staff-educations/delete?id=${editId.value}`, {}).then((res) => {
                showAdd.value = false
                resetFormData()
                dataDetail.value.forEach((item) => {
                    if (item.id == editId.value) {
                        let index = dataDetail.value.indexOf(item)
                        dataDetail.value.splice(index, 1)
                    }
                })
            })
        }

        watch(visible, () => {
            if (visible.value) {
                showAdd.value = false
            }
        })

        const confirm = () => {
            formData.value.staffId = staffId.value
            formInline.value
                .validate()
                .then(async () => {
                    if (formData?.value.id) {
                        request.put('/api/hr-staff-educations/update', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('编辑成功!')
                        })
                    } else {
                        request.post('/api/hr-staff-educations/create', formData.value).then((res) => {
                            dataDetail.value = res
                            emit('update:dataDetail', res)
                            message.success('新增成功!')
                        })
                    }
                    cancel()
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            resetFormData,
            showAdd,
            addEducation,
            editEducation,
            cancel,
            confirm,
            delRow,
        }
    },
})
</script>
<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;
    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    :deep(.ant-form-item) {
        width: 33%;
    }
    :deep(.img) {
        position: absolute;
        right: 1%;
    }
}
.defaulBox {
    text-align: center;
}
</style>
