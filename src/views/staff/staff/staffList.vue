<template>
    <SearchBar v-model="params" :options="options" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'staff_Import_list'" @click="importData">导入</Button>
        <Button type="primary" v-auth="'staff_export_list'" @click="exportData">{{ exportText }}</Button>
        <Button danger type="primary" @click="deleteRow" v-auth="'staff_delete_list'">批量删除</Button>
        <!-- <Button type="primary" v-auth="'staff_entry'" @click="InductionMoreRow()">批量入职</Button>
        <Button type="primary" v-auth="'staff_affirm_info'" @click="confirmInfoMoreRow()">批量确认信息</Button> -->
    </div>
    <BasicTable
        ref="tableRef"
        api="/api/hr-talent-staffs/page"
        deleteApi="/api/hr-talent-staffs/deletes"
        :params="{ ...params, izDefault: false }"
        :columns="columns"
        :exportUrl="exportUrl"
        @selectedRowsArr="(selected) => (selectedRowsArr = selected)"
        @getData2="(data) => (tableData = data)"
    >
        <template #staffStatus="{ record }">
            <Button v-if="record.staffStatusLabel == '入职中'" type="link" size="small" @click="clickStaffStatus(record)">
                {{ record.staffStatusLabel }}
            </Button>
            <Button v-else-if="record.staffStatusLabel == '借调中'" type="link" size="small" @click="clickStaffSeconded(record)">
                {{
                    `${
                        record.secondmentStaffStatus
                            ? steffStateList.find((el) => {
                                  return el.value == record.secondmentStaffStatus
                              })?.label + '-'
                            : ''
                    }${record.staffStatusLabel}`
                }}
            </Button>
            <span v-else>{{ record.staffStatusLabel }}</span>
        </template>
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 添加编辑员工 -->
    <AddStaff
        :visible="showAdd"
        :title="modalTitle"
        :item="currentValue"
        :typePage="'staff'"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <!-- 入职中-->
    <Induction :visible="showInduction" :title="modalTitleInduction" :item="inductionValue" @cancel="modalCancelInduction" />
    <!-- 借调中-->
    <Seconded :visible="showSeconded" title="详情" :item="secondedValue" @cancel="modalCancelSeconded" />
    <!-- 参保 -->
    <EditModal :visible="showInsured" title="参保" @cancel="Insuredcancel" @confirm="Insuredconfirm" />
    <!-- 医疗备案 -->
    <MedicaModal
        v-model:visible="showMedical"
        :title="'医疗备案'"
        :item="currentValue"
        :viewType="viewType"
        @cancel="modalCancel"
        @confirm="modalConfirm"
    />
    <!-- //导入 -->
    <ImportModal v-model:visible="importVisible" :temUrl="importTemUrl" :importUrl="importUrl" @getResData="searchData" />
    <!-- 借调 -->
    <SecondedModal
        v-model:visible="secondedModalVisible"
        title="员工借调"
        viewType="start"
        :item="currentValue"
        @confirm="modalConfirm"
    />
    <SkipAuthentication
        :title="'跳过认证'"
        :item="currentValue"
        :visible="showSkipAuth"
        @confirm="skipAuthConfirm"
        @cancel="shipAuthCancel"
    />
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, reactive, provide, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import request from '/@/utils/request'
import addStaff from './addStaff.vue'
import { SearchBarOption } from '/#/component'
// import confirmInfoModal from './confirmInfoModal.vue'
import induction from '/@/views/serviceCentre/inductionServices/stayInductionStaff/employeeInduction.vue'
import seconded from '/@/views/serviceCentre/secondment/staffSeconded.vue'
import SkipAuthentication from './skipAuthentication.vue'
// import insuredMessage from './insuredMessage.vue'
import editModal from './editModal.vue'
import medicaModal from './medical/medicalModal.vue'
import secondedModal from './seconded/index.vue'
import { useRoute } from 'vue-router'
import { getHaveAuthorityOperation, getDynamicText } from '/@/utils'
export default defineComponent({
    name: 'StaffList',
    components: {
        AddStaff: addStaff,
        // ElectricSign: electricSign,
        // ConfirmInfoModal: confirmInfoModal,
        Induction: induction,
        Seconded: seconded,
        // InsuredMessage: insuredMessage,
        EditModal: editModal,
        MedicaModal: medicaModal,
        SecondedModal: secondedModal,
        SkipAuthentication,
    },
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const sexTypeList = ref<LabelValueOptions>([]) //性别
        const steffStateList = ref<LabelValueOptions>([]) // 获取员工状态
        const staffTypeList = ref<LabelValueOptions>([]) // 人员类型
        // const selectclientsOptions = ref<LabelValueOptions>([]) // 用工单位
        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffStates', {}).then((res) => {
                steffStateList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/staffType', {}).then((res) => {
                staffTypeList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // request.get('/api/hr-selectclients').then((res) => {
            //     selectclientsOptions.value = res.map((item) => {
            //         return { label: item.clientName, value: item.id }
            //     })
            // })
        })

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '系统编号',
                dataIndex: 'systemNum',
                align: 'center',
                width: 150,
            },
            {
                title: '档案编号',
                dataIndex: 'archivesNum',
                align: 'center',
                width: 150,
            },
            {
                title: '姓名',
                dataIndex: 'name',
                align: 'center',
                width: 100,
            },
            {
                title: '性别',
                dataIndex: 'sex',
                align: 'center',
                customRender: ({ record }) => {
                    return record.sexLabel
                },
                width: 100,
            },
            {
                title: '出生日期',
                dataIndex: 'birthday',
                align: 'center',
                width: 110,
            },
            {
                title: '年龄',
                dataIndex: 'age',
                align: 'center',
                sorter: false,
                width: 110,
            },
            {
                title: '身份证号',
                dataIndex: 'certificateNum',
                align: 'center',
                width: 160,
            },
            {
                title: '手机号',
                dataIndex: 'phone',
                align: 'center',
                width: 110,
            },
            {
                title: '员工状态',
                dataIndex: 'staffStatus',
                align: 'center',
                slots: { customRender: 'staffStatus' },
                width: 120,
            },
            {
                title: '客户名称',
                dataIndex: 'clientName',
                align: 'center',
                width: 150,
            },
            {
                title: '一级客户',
                dataIndex: 'oneClientName',
                align: 'center',
                width: 150,
                sorter: false,
            },
            {
                title: '二级客户',
                dataIndex: 'twoClientName',
                align: 'center',
                width: 150,
                sorter: false,
            },
            {
                title: '人员类型',
                dataIndex: 'personnelType',
                align: 'center',
                customRender: ({ record }) => {
                    return record.personnelTypeLabel
                },
                width: 100,
            },
            {
                title: '是否参保',
                dataIndex: 'izInsured',
                align: 'center',
                customRender: ({ text }) => {
                    if (text == 0) {
                        return '未参保'
                    } else if (text == 1) {
                        return '已参保'
                    } else {
                        return '停保'
                    }
                },
                width: 100,
            },
            {
                title: '入职日期',
                dataIndex: 'boardDate',
                align: 'center',
                width: 100,
            },
            {
                title: '离职日期',
                dataIndex: 'resignationDate',
                align: 'center',
                width: 120,
            },
            {
                title: '合同起始日期',
                dataIndex: 'contractStartDate',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return h(
                        'span',
                        {
                            style: 'color:' + (record.contractState == 2 || record.contractState == 3 ? 'red;' : ''),
                        },
                        record.contractStartDate,
                    )
                    // return record.contractEndDate
                },
            },
            {
                title: '合同结束日期',
                dataIndex: 'contractEndDate',
                align: 'center',
                width: 150,
                customRender: ({ record }) => {
                    return h(
                        'span',
                        {
                            style: 'color:' + (record.contractState == 2 || record.contractState == 3 ? 'red;' : ''),
                        },
                        record.contractEndDate,
                    )
                },
            },
            {
                title: '操作',
                dataIndex: 'operation',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 250,
                fixed: 'right',
            },
        ]
        const showMedical = ref(false) //医疗备案
        const medical = (record) => {
            showMedical.value = true
            currentValue.value = { ...record }
        }

        const showInsured = ref(false) //参保
        const insuredData = reactive<any>({})
        const staffId = ref('')
        const insured = (record) => {
            staffId.value = record.id
            currentValue.value = { ...record }
            showInsured.value = true
        }
        provide('staffId', staffId)
        const Insuredcancel = () => {
            showInsured.value = false
            staffId.value = ''
        }
        const Insuredconfirm = () => {
            showInsured.value = false
            tableRef.value.refresh()
        }

        const showAdd = ref(false)
        const modalTitle = ref('编辑员工')
        // 当前编辑的数据
        const currentValue = ref(undefined)
        const viewType = ref('')
        provide('item', currentValue)
        const changeSex = (value: any) => {
            ;(params.value as any).sex = value.option.value
        }
        const changeStaffStatus = (value: any) => {
            ;(params.value as any).staffStatus = value.option.value
        }
        const changeStaffType = (value: any) => {
            ;(params.value as any).personnelType = value.option.value
        }

        const birthdayChange = (value: any) => {
            params.value.birthday = value
        }
        // 编辑
        const editRow = (record, type) => {
            showAdd.value = true
            modalTitle.value = type == 'edit' ? '编辑员工' : '查看员工'
            currentValue.value = { ...record }
            viewType.value = type
        }
        //批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                console.log(ref)
            })
        }

        // 关闭弹窗
        const modalCancel = () => {
            showAdd.value = false
            modalTitle.value = '编辑员工'
            currentValue.value = undefined
        }
        // 确认弹窗
        const modalConfirm = async () => {
            tableRef.value.refresh()
        }

        //导入导出
        const importVisible = ref(false)
        const importTemUrl = '/api/hr-staff/template'
        const importUrl = '/api/hr-staff/import'
        const exportUrl = '/api/hr-staff/export'
        const importData = () => {
            importVisible.value = true
        }
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }

        //入职中
        const showInduction = ref(false)
        const modalTitleInduction = ref('详情')
        const inductionValue = ref(undefined)

        const clickStaffStatus = (record) => {
            showInduction.value = true
            modalTitleInduction.value = '详情'
            inductionValue.value = record
        }
        // 借调中
        const showSeconded = ref(false)
        const secondedValue = ref(undefined)
        const clickStaffSeconded = (record) => {
            showSeconded.value = true
            secondedValue.value = record
        }
        const modalCancelSeconded = () => {
            showSeconded.value = false
            secondedValue.value = undefined
        }
        const modalCancelInduction = () => {
            showInduction.value = false
            inductionValue.value = undefined
        }

        const route = useRoute()
        const params = ref({
            // izDefault:false,   //员工列表  false，  人才列表  true
            systemNum: undefined,
            name: undefined,
            sex: undefined,
            birthday: undefined,
            certificateNum: undefined,
            phone: undefined,
            staffStatus: undefined,
            roleId: undefined,
            daterange: undefined,
            jumpKey: route.query?.type || undefined,
            clientIds: route.params?.clientId || undefined,
        })
        const options: SearchBarOption[] = [
            {
                type: 'string',
                label: '系统编号',
                key: 'systemNum',
            },
            {
                type: 'string',
                label: '姓名',
                key: 'name',
            },
            {
                type: 'select',
                label: '性别',
                key: 'sexList',
                options: sexTypeList,
                multiple: true,
            },
            {
                type: 'date',
                label: '出生日期',
                key: 'birthday',
                allowClear: true,
            },
            {
                type: 'string',
                label: '身份证号',
                key: 'certificateNum',
            },
            {
                type: 'string',
                label: '手机号码',
                key: 'phone',
            },
            {
                type: 'select',
                label: '员工状态',
                key: 'staffStatusList',
                options: steffStateList,
                multiple: true,
            },
            {
                // type: 'select',
                label: '客户名称',
                key: 'clientIds',
                // options: selectclientsOptions,
                type: 'clientSelectTree',
                placeholder: '客户名称',
                maxTag: '0',
                multiple: true,
                checkStrictly: false,
            },
            {
                type: 'select',
                label: '人员类型',
                key: 'personnelTypeList',
                options: staffTypeList,
                multiple: true,
            },
            {
                type: 'daterange',
                label: '合同开始日期',
                key: 'contractStartDateQuery',
                allowClear: true,
            },
            {
                type: 'daterange',
                label: '合同结束日期',
                key: 'contractEndDateQuery',
                allowClear: true,
            },
            {
                type: 'select',
                label: '重要提醒',
                key: 'jumpKey',
                options: [
                    {
                        label: '劳动合同已到期',
                        value: 'staff_contract_expire',
                    },
                    {
                        label: '劳动合同即将到期',
                        value: 'staff_contract',
                    },
                    {
                        label: '即将到达退休年龄',
                        value: 'staff_retire',
                    },
                    {
                        label: '医疗信息待备案',
                        value: 'staff_medical',
                    },
                    {
                        label: '产假员工需计算薪资',
                        value: 'staff_birth',
                    },
                ],
            },
            {
                type: 'select',
                label: '续签状态',
                key: 'renewalSearch',
                options: [
                    {
                        label: '未续签',
                        value: 1,
                    },
                    {
                        label: '续签中',
                        value: 2,
                    },
                    {
                        label: '续签完成',
                        value: 3,
                    },
                ],
            },
            {
                type: 'select',
                label: '是否参保',
                key: 'izInsured',
                options: [
                    {
                        label: '未参保',
                        value: 0,
                    },
                    {
                        label: '已参保',
                        value: 1,
                    },
                    {
                        label: '停保',
                        value: 2,
                    },
                ],
            },
        ]
        const selectedRowsArr = ref<any>([])

        const exportText = computed(() => {
            return getDynamicText('导出', params.value, selectedRowsArr.value)
        })

        //发起转正
        const actionRegularWorker = (record) => {
            request
                .post('/api/hr-talent-staff/PCstaffTurnPositives', {
                    staffId: record.id,
                })
                .then((res) => {
                    message.success('已成功发起转正！')
                })
        }

        // 员工借调/结束借调
        const secondedModalVisible = ref(false)
        const actionSeconded = (record) => {
            secondedModalVisible.value = true
            currentValue.value = record
        }

        const showSkipAuth = ref(false)
        const skipAuthentication = (record) => {
            showSkipAuth.value = true
            currentValue.value = { ...record }
        }
        const skipAuthConfirm = () => {
            showSkipAuth.value = false
            tableRef.value.refresh(1)
        }
        const shipAuthCancel = () => {
            showSkipAuth.value = false
        }
        const tableData = ref([])

        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '编辑',
                    auth: 'staff_edit_list',
                    show: true,
                    click: (record) => editRow(record, 'edit'),
                },
                {
                    neme: '查看',
                    auth: 'staff_detail_list',
                    show: true,
                    click: (record) => editRow(record, 'see'),
                },
                {
                    neme: '跳过认证',
                    click: (record) => skipAuthentication(record),
                    show: true,
                    auth: 'staff_skip_auth',
                },
                {
                    neme: '参保',
                    auth: 'staff_insured',
                    // show: true,

                    show: (record) => {
                        return record.staffStatus === 4 && record.izInsured === 0
                    },
                    click: (record) => insured(record),
                },
                {
                    neme: '医疗备案',
                    auth: 'staff_insured',
                    show: true,
                    click: (record) => medical(record),
                },
                {
                    neme: '转正',
                    auth: 'staff_actionRegularWorker',
                    show: (record) => {
                        return record.staffStatus == 3 || record.staffStatus == 9
                    },
                    click: actionRegularWorker,
                },
                {
                    neme: '员工借调',
                    auth: 'staff_seconded',
                    show: (record) => {
                        return (
                            (record.staffStatus == 3 ||
                                record.staffStatus == 4 ||
                                record.staffStatus == 5 ||
                                record.staffStatus == 7 ||
                                record.staffStatus == 8 ||
                                record.staffStatus == 10 ||
                                record.staffStatus == 11) &&
                            (!record.secondmentId ||
                                (record.secondmentId && (record.secondmentStates == 4 || record.secondmentStates == 3)))
                        )
                    },
                    click: (record) => actionSeconded(record),
                },
            ]),
        )

        return {
            tableData,
            // data
            exportText,
            tableRef,
            columns,
            params,
            options,
            showAdd,
            viewType,
            modalTitle,
            currentValue,
            steffStateList,
            sexTypeList,
            staffTypeList,
            showInduction,
            modalTitleInduction,
            inductionValue,
            showInsured, //参保弹框展示
            insuredData, //参保信息
            staffId, //id
            showMedical, //是否展示医疗备案弹框

            // 事件
            medical, //医疗备案
            insured,
            searchData,
            editRow,
            deleteRow,
            modalCancel,
            modalConfirm,
            changeSex,
            changeStaffStatus,
            changeStaffType,
            birthdayChange,
            // InductionRow,
            selectedRowsArr,
            // InductionMoreRow,
            // confirmInfoRow,
            // confirmInfoMoreRow,
            // showConfirmInfor,
            // confirmInforCancel,
            // confirmInforValue,
            clickStaffStatus,
            modalCancelInduction,
            Insuredcancel,
            Insuredconfirm,

            //导入
            importVisible,
            importTemUrl,
            importUrl,
            importData,
            exportData,
            exportUrl,

            //操作
            myOperation,

            secondedModalVisible,
            actionSeconded,
            clickStaffSeconded,
            showSeconded,
            secondedValue,
            modalCancelSeconded,
            showSkipAuth,
            skipAuthentication,
            skipAuthConfirm,
            shipAuthCancel,
        }
    },
})
</script>

<style scoped lang="less">
.dateItem {
    margin-right: 10px;
    margin-bottom: 10px;
    & > .name {
        margin-right: 10px;
    }
    & > .input {
        width: 200px;
    }
}
:deep(.ant-modal-title) {
    text-align: center !important;
    color: red;
}
</style>
