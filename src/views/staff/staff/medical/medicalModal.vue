<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1300px">
        <div class="seeInfoBox">
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>员工信息</span>
                <div class="examine-flex">
                    <div class="item-flex">
                        <span>员工姓名：</span>
                        <span>{{ detailData.name }}</span>
                    </div>
                    <div class="item-flex">
                        <span>身份证号：</span>
                        <span>{{ detailData.certificateNum }}</span>
                    </div>

                    <div class="item-flex">
                        <span>性别：</span>
                        <span>{{ detailData.sex === 1 ? '男' : '女' }}</span>
                    </div>
                    <div class="item-flex">
                        <span>联系方式：</span>
                        <span>{{ detailData.phone }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>岗位：</span>
                        <span>{{ detailData.professionName }}</span>
                    </div>

                    <div class="item-flex">
                        <span>基本工资：</span>
                        <span>{{ detailData.basicWage }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同开始日期：</span>
                        <span>{{ detailData.contractStartDate }}</span>
                    </div>
                    <div class="item-flex">
                        <span>合同结束日期：</span>
                        <span>{{ detailData.contractEndDate }}</span>
                    </div>
                    <p class="linefeed"></p>
                    <div class="item-flex">
                        <span>社保基数：</span>
                        <span>{{ detailData.socialSecurityCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>医保基数：</span>
                        <span>{{ detailData.medicalInsuranceCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>公积金基数：</span>
                        <span>{{ detailData.accumulationFundCardinal }}</span>
                    </div>
                    <div class="item-flex">
                        <span>医疗备案日期：</span>
                        <span>{{ detailData.medicalRecordDate }}</span>
                    </div>
                </div>
            </div>
            <div class="examine">
                <Divider type="vertical" class="divid" />
                <span>操作信息</span>
                <div class="examine-list" v-for="(item, index) in opLogsList" :key="index">
                    <div class="list-item">
                        <span style="width: 40px">{{ index + 1 }}</span>
                        <div class="item-flex">
                            <span>操作人：</span>
                            <span>{{ item.realName }}</span>
                        </div>
                        <div class="item-flex2">
                            <span>操作时间：</span>
                            <span>{{ item.createdDate }}</span>
                        </div>
                        <div class="item-flex3">
                            <div class="box1">操作信息：</div>
                            <div class="box2">
                                {{ item.message }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="examine">
            <Divider type="vertical" class="divid" />
            <span>操作</span>
            <div class="examine-area">
                <span><span style="color: red">*&nbsp;&nbsp;</span>医疗备案日期：&nbsp;&nbsp;</span>
                <DatePicker
                    class="date"
                    v-model:value="detailData.medicalRecordDate"
                    format="YYYY-MM-DD"
                    placeholder="请填写您为员工进行医疗备案的日期"
                    valueFormat="YYYY-MM-DD"
                    :getPopupContainer="
                        () => {
                            return document.body
                        }
                    "
                />
            </div>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'

import request from '/@/utils/request'
export default defineComponent({
    name: 'CreateA',
    components: { PaperClipOutlined },
    props: {
        title: String,
        currentValue: {
            type: Object,
        },
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['confirm', 'cancel', 'update:visible'],
    setup(props, { emit }) {
        const { item, visible } = toRefs<any>(props)
        const detailData = ref<any>({})
        const opLogsList = ref<object[]>([])
        watch(
            visible,
            () => {
                if (visible.value) {
                    if (!item.value?.id) return
                    getDetail(item.value?.id)
                }
            },
            { immediate: true },
        )
        // 获取员工医疗备案详情
        const getDetail = (value) => {
            request.get('/api/hr-talent-staff/medical-record-obtain', { id: value }).then((res) => {
                detailData.value = res
                opLogsList.value = res.applyOpLogsList?.map((item) => {
                    return { ...item, message: item?.message?.split('####')[0] || '' }
                })
            })
        }
        onMounted(() => {})

        // cancel handle
        const cancel = () => {
            emit('cancel')
            emit('update:visible', false)
        }

        const confirm = () => {
            if (detailData.value.medicalRecordDate === null) {
                message.warning('请填写您为员工进行医疗备案的日期!')
                return
            } else {
                request
                    .post('/api/hr-talent-staff/medical-record-modify', {
                        id: detailData.value.id,
                        medicalRecordDate: detailData.value.medicalRecordDate,
                    })
                    .then((res) => {
                    })
                cancel()

                emit('confirm', detailData.value)
            }
        }

        return {
            opLogsList, //操作信息
            confirm,
            cancel,
            detailData,
        }
    },
})
</script>
<style scoped lang="less">
//

.examine {
    margin: 40px 0;
    .span {
        padding-left: 10px;
    }
    .divid {
        border-left: 3px solid #1890ff;
        height: 26px;
    }
    .examine-list {
        .list-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            .item-flex {
                width: 150px;
            }
            .item-flex2 {
                width: 250px;
            }
            .item-flex3 {
                width: 600px;

                .box1 {
                    float: left;
                    width: 70px;
                }
                .box2 {
                    float: right;
                    width: 530px;
                }
            }
        }
    }
    .examine-area {
        margin: 20px 0px 20px 20px;
    }
    .examine-flex {
        display: flex;
        flex-wrap: wrap;

        margin-top: 10px;
        padding-left: 15px;
        .item-flex {
            width: 25%;
            margin: 5px 0px;
        }
        .linefeed {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
}

.date {
    width: 25%;
}
</style>
