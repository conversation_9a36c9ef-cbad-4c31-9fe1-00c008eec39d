<template>
    <Form
        ref="formInline"
        :model="formData"
        :label-col="{ style: { width: '130px' } }"
        :rules="rules"
        style="overflow-y: auto; margin-top: 20px"
        class="form-flex"
    >
        <template v-for="item in myOptions" :key="item">
            <MyFormItem
                :width="item.width"
                :item="item"
                v-model:value="formData[item.name]"
                :class="item.slots"
                v-if="item.show != false && !item.external"
            >
                <template #img>
                    <ImportImg v-model:imageUrl="formData.avatar" :disabled="!RoleState" />
                </template>
                <template #date>
                    <RangePicker
                        :allowClear="true"
                        v-model:value="formData.militaryDate"
                        :disabled="!RoleState"
                        @change="dateChange"
                    />
                </template>
                <template #select>
                    <PostTree v-model:value="formData.stationList" :itemForm="item" :disabled="!RoleState" />
                </template>
                <template #contactAddress>
                    <div style="width: 100%">
                        <Cascader
                            v-model:value="formData.contactAddressPrefix"
                            :options="cityList"
                            :disabled="item.disabled"
                            placeholder="请填写通讯地址(现居地)"
                            @change="(e) => contractAddressChange(e)"
                        />
                        <!-- <Input
                style="width: 100%"
                v-model:value="formData.contactAddress"
                placeholder="请填写通讯地址(现居地)"
                :disabled="item.disabled"
            /> -->
                        <p v-if="typePage != 'talent'" style="color: red; font-size: 12px">
                            员工确认向公司提供的个人身份信息、联系电话、通信地址等信息是真实完整的，作为双方约定的通讯方式，如遇上述信息变更，员工应及时向公司提交修改申请，否则以原有通讯方式为依据。
                        </p>
                    </div>
                    <!-- <Tooltip placement="topRight" arrowPointAtCenter v-if="typePage != 'talent'">
              <template #title>
                  <p style="width: 240px">
                      {{
                          `员工确认向公司提供的个人身份信息、联系电话、通信地址等信息是真实完整的，作为双方约定的通讯方式，如遇上述信息变更，员工应及时向公司提交修改申请，否则以原有通讯方式为依据。 `
                      }}
                  </p>
              </template>
              <QuestionCircleOutlined
                  :style="{ color: '#999999', marginLeft: '5px', display: 'flex', alignItems: 'center' }"
              />
          </Tooltip> -->
                    <!-- <PostTree v-model:value="formData.stationList" :itemForm="item" :disabled="!RoleState" /> -->
                </template>
            </MyFormItem>
            <template v-else-if="item.name == 'staffFieldList'">
                <template v-for="(itemField, index) in formData.staffFieldList" :key="index + 'staffFieldList'">
                    <FormItem
                        style="width: 100%"
                        :class="[index == 0 ? '' : 'hide', 'staffFieldListForm']"
                        :label="item.label"
                        :rules="validateStaffFieldList"
                        :name="['staffFieldList', index]"
                    >
                        <div class="other-info" style="display: flex; align-items: center">
                            <Input
                                class="other-input"
                                v-model:value="itemField.fieldName"
                                placeholder="请填写其他信息名称"
                            />&nbsp;:&nbsp;
                            <Input class="other-input" v-model:value="itemField.fieldValue" placeholder="请填写其他信息内容" />
                            <span class="icon-wrapper" v-if="index == 0">
                                <PlusCircleOutlined style="margin-left: 10px; color: #1890ff" @click="addField" />
                            </span>
                            <span class="icon-wrapper" v-if="formData.staffFieldList.length > 1">
                                <MinusCircleOutlined
                                    style="margin-left: 10px; color: #ef5959"
                                    @click="delField(itemNote, index)"
                                />
                            </span>
                        </div>
                    </FormItem>
                </template>
            </template>
            <template v-else-if="item.name == 'staffNoteList'">
                <template v-for="(itemNote, index) in formData.staffNoteList" :key="index + 'staffNoteList'">
                    <FormItem
                        style="width: 100%"
                        :class="[index == 0 ? '' : 'hide', 'staffNoteList', index % 4 == 1 ? 'staffNoteListFrist' : '']"
                        :label="item.label"
                        :name="['staffNoteList', index]"
                    >
                        <div style="width: 130px" v-if="index % 4 == 1"></div>
                        <div class="other-info" style="display: flex; align-items: center">
                            <Input class="other-input" v-model:value="itemNote.staffNote" placeholder="请填写标签" />
                            <span class="icon-wrapper" v-if="index == 0">
                                <PlusCircleOutlined style="margin-left: 10px; color: #1890ff" @click="addStaffNode" />
                            </span>
                            <span class="icon-wrapper" v-if="formData.staffNoteList.length > 1">
                                <MinusCircleOutlined
                                    style="margin-left: 10px; color: #ef5959"
                                    @click="delStaffNode(itemNote, index)"
                                />
                            </span>
                        </div>
                    </FormItem>
                </template>
            </template>
        </template>
    </Form>

    <div class="ant-modal-footer" v-if="RoleState">
        <Button key="back" @click="cancel">取消</Button>
        <Button key="submit" type="primary" @click="confirm">确定</Button>
    </div>
</template>
<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { defineComponent, nextTick, onMounted, ref, toRefs, unref, watch } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules, isEmpty } from '/@/utils/index'
import { MinusCircleOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import {
    driverCardValidity,
    HKCardValidity,
    idCardValidity,
    officerCardValidity,
    passPortCardValidity,
    studentCardValidity,
    validateHeight,
    validatePhone,
} from '/@/utils/format'
// import permissionStore from '/@/store/modules/permission'
import staffStore from '/@/store/modules/staff'
import { city } from '/@/utils/city'
import PostTree from '/@/views/user/postManage/postTree.vue'

export default defineComponent({
    name: 'AddStaffBasic',
    components: {
        PlusCircleOutlined,
        MinusCircleOutlined,
        PostTree,

        QuestionCircleOutlined,
    },
    props: {
        staffId: String,
        typePage: String,
        viewType: String,
        visible: Boolean,
    },
    emits: ['cancel', 'confirm', 'getInfoPerfect'],
    setup(props, { emit }) {
        const { staffId, typePage, viewType, visible } = toRefs<any>(props)
        //  RoleState = permissionStore().getPermission.staffState // 客户false  企业true
        // const RoleState = viewType.value == 'edit'
        let RoleState = ref(viewType.value == 'edit' || viewType.value == 'perfect')
        let RoleStateF = ref(viewType.value == 'see')
        const nationalityList = ref<object[]>([]) // 国籍数据
        const certificateList = ref<object[]>([]) // 证件类型
        const sexList = ref<object[]>([]) // 性别
        const marriageList = ref<object[]>([]) // 婚姻状态
        const educationList = ref<object[]>([]) // 最高学历
        const residenceList = ref<object[]>([]) // 户口性质
        const politicalList = ref<object[]>([]) // 政治面貌
        const showPolitics = ref<Boolean>(false) //是否显示入党日期
        const contract = ref<any>()
        const cityList = ref<inObject[]>([])
        // const station = ref<object[]>([]) //适合岗位
        // const talent = ref<Boolean>(true)

        onMounted(() => {
            request.get('/api/com-code-tables/getCodeTableByInnerName/certificateType', {}).then((res) => {
                certificateList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/sexType', {}).then((res) => {
                sexList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/marriageStates', {}).then((res) => {
                marriageList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/educationStates', {}).then((res) => {
                educationList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/residenceType', {}).then((res) => {
                residenceList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/politicalType', {}).then((res) => {
                politicalList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            request.get('/api/com-code-tables/getCodeTableByInnerName/nationality', {}).then((res) => {
                nationalityList.value = res.map((item) => {
                    return { label: item.itemName, value: item.itemValue }
                })
            })
            // request.get('/api/hr-stations/list', {}).then((res) => {
            //     station.value = res.map((item) => {
            //         return { label: item.professionName, value: item.id }
            //     })
            // })
            cityList.value = city
            cityList.value.forEach((el) => {
                el.value = el.label
                el?.children?.forEach((item) => {
                    item.value = item.label
                    item?.children?.forEach((ele) => {
                        ele.value = ele.label
                    })
                })
            })
        })
        const certificateType = ref(1)
        // 改变证件类型
        const certificateNumChange = (e) => {
            certificateType.value = e
        }
        const politicsChange = (e) => {
            if (e == 1) {
                // 是否显示入党日期
                showPolitics.value = true
            } else {
                showPolitics.value = false
            }
        }
        // 提交时不校验证件号失去焦点验证事件
        let isSubmittingSkipValidIdCard = false
        // 证件号输入框失焦事件
        const certificateNumBlur = (rule: inObject, value) => {
            // let code = ref(value)
            if (isSubmittingSkipValidIdCard) {
                isSubmittingSkipValidIdCard = false
                console.log('certificateNumBlur', '正在提交跳过身份证号校验')
                return Promise.resolve()
            }
            console.log('certificateNumBlur', rule, value)
            if (certificateType.value == 1) return idCardValidity(rule, value, () => isIdNumber(value))
            if (certificateType.value == 2) return officerCardValidity(rule, value)
            if (certificateType.value == 3) return studentCardValidity(rule, value)
            if (certificateType.value == 4) return driverCardValidity(rule, value)
            if (certificateType.value == 5) return passPortCardValidity(rule, value)
            if (certificateType.value == 6) return HKCardValidity(rule, value)
        }
        // 校验身份证号是否存在
        const isIdNumber = async (code) => {
            onCompute(code)
            let res = await request.get('/api/hr-talent-staffs/idNumber', { certificateNum: code })
            if (staffId.value) {
                // 编辑
                if (res.id) {
                    if (res.id != staffId.value) {
                        if (res.izDefault) {
                            return Promise.reject('证件号码在人才库中存在，请删除后重新尝试!')
                        } else {
                            return Promise.reject('证件号码已存在!')
                        }
                    }
                }
            } else {
                // 新增
                if (res.id) {
                    if (res.izDefault) {
                        // true人才  false员工
                        // formData.value.name = res.name ? res.name : formData.value.name
                        // formData.value.nation = res.nation ? res.nation : formData.value.nation
                        // formData.value.nationality = res.nationality ? res.nationality : formData.value.nationality
                        // formData.value.politicsStatus = res.politicsStatus ? res.politicsStatus : formData.value.politicsStatus
                        // formData.value.partyDate = res.nation ? res.partyDate : formData.value.partyDate
                        // formData.value.partyBranch = res.partyBranch ? res.partyBranch : formData.value.partyBranch
                        // formData.value.phone = res.phone ? res.phone : formData.value.phone
                        // formData.value.maritalStatus = res.maritalStatus ? res.maritalStatus : formData.value.maritalStatus
                        // formData.value.nativePlace = res.nativePlace ? res.nativePlace : formData.value.nativePlace
                        return Promise.reject('证件号码已存在!')
                    } else {
                        return Promise.reject('证件号码已存在!')
                    }
                }
            }
            return Promise.resolve()
        }
        // 根据身份证计算生日，性别，年龄
        const onCompute = (code) => {
            // 计算性别
            if (code.substr(16, 1) % 2 === 1) {
                formData.value.sex = 1
            } else {
                formData.value.sex = 2
            }
            // 计算生日
            const year = code.substring(6, 10)
            const month = code.substring(10, 12)
            const day = code.substring(12, 14)
            formData.value.birthday = year + '-' + month + '-' + day

            const myDate = new Date()
            const nowMonth = myDate.getMonth() + 1
            const nowDay = myDate.getDay()
            // 计算年龄
            let age = myDate.getFullYear() - year
            if (nowMonth < month || (nowMonth === month && nowDay < day)) {
                age -= 1
            }
            formData.value.age = age

            formValidateOptional(['birthday'])
        }

        // 手机号输入框失焦事件校验是否存在
        // phoneNumBlur
        const phoneNumBlur = (rule: inObject, value) => {
            return validatePhone(rule, value, () => phoneCheck(value))
        }
        // 验证手机号码有效性
        const phoneCheck = async (code) => {
            let res = await request.get('/api/hr-talent-staffs/phone', { phone: code })
            if (staffId.value) {
                // 编辑
                if (res.id) {
                    if (res.id != staffId.value) {
                        return Promise.reject('手机号码已存在!')
                    }
                }
            } else {
                // 新增
                if (res.id) {
                    return Promise.reject('手机号码已存在!')
                }
            }
            return Promise.resolve()
        }

        //是否服兵役
        const validIzMilitary = ref<Boolean>(true)
        // 修改是否服兵役
        const changeIzMilitary = (item) => {
            if (item == true) {
                validIzMilitary.value = true
            } else {
                validIzMilitary.value = false
            }
        }

        const contractAddressChange = (e) => {}
        const izDefault = ref<Boolean>(false)

        const validateSystemNum = (rule, value) => {
            const regSystemNum = /^[A-Za-z0-9]+$/
            if (rule.required && !value) {
                return Promise.reject('请输入系统编号')
            }
            if (!regSystemNum.test(value)) {
                return Promise.reject('请填写由数字字母组成的系统编号')
            } else {
                return Promise.resolve()
            }
        }
        const contactAddressBlur = (rule, value) => {
            if (rule.required && !value) {
                if (typePage.value == 'talent') return Promise.reject('请输入通讯地址(现居地)')
                return Promise.reject('')
            }
            return Promise.resolve()
        }
        const myOptions = ref([
            {
                label: '系统编号',
                name: 'systemNum',
                disabled: RoleStateF,
                required: !izDefault.value,
                validator: validateSystemNum,
            },
            {
                label: '姓名',
                name: 'name',
                disabled: RoleStateF,
                showbr: true, //换行
            },
            {
                label: '图片',
                name: 'avatar',
                slots: 'img',
                type: 'slots',
                required: false,
                default: '',
                disabled: RoleStateF,
            },
            {
                label: '国籍',
                name: 'nationality',
                type: 'change',
                options: nationalityList,
                ruleType: 'number',
                disabled: RoleStateF,
            },
            {
                label: '民族',
                name: 'nation',
                disabled: RoleStateF,
                showbr: true, //换行
                required: false,
            },
            {
                label: '证件类型',
                name: 'certificateType',
                type: 'change',
                options: certificateList,

                disabled: RoleStateF,
                onChange: certificateNumChange,
                default: 1,
                ruleType: 'number',
            },
            {
                label: '证件号码',
                name: 'certificateNum',
                showbr: true, //换行
                disabled: RoleStateF,
                // onBlur: certificateNumBlur,
                validator: certificateNumBlur,
                trigger: 'blur',
            },
            {
                label: '出生日期',
                name: 'birthday',
                type: 'date',
                disabled: RoleStateF,
            },
            {
                label: '年龄',
                name: 'age',
                ruleType: 'any',
                required: false,
                disabled: true,
            },
            {
                label: '性别',
                name: 'sex',
                type: 'change',
                options: sexList,
                required: false,
                ruleType: 'number',
                disabled: RoleStateF,
            },
            {
                label: '政治面貌',
                name: 'politicsStatus',
                type: 'change',
                options: politicalList,
                required: false,
                onChange: politicsChange,
                default: 3,
                ruleType: 'number',
                disabled: RoleStateF,
            },
            {
                label: '入党时间',
                name: 'partyDate',
                type: 'date',
                show: showPolitics,
                disabled: RoleStateF,
            },
            {
                label: '所在党支部',
                name: 'partyBranch',
                show: showPolitics,
                disabled: RoleStateF,
            },
            {
                label: '手机号码',
                name: 'phone',
                // onBlur: phoneNumBlur,
                validator: phoneNumBlur,
                disabled: RoleStateF,
                trigger: 'blur',
            },
            {
                label: '婚姻状态',
                name: 'maritalStatus',
                type: 'change',
                options: marriageList,
                ruleType: 'number',
                disabled: RoleStateF,
                required: false,
            },
            {
                label: '籍贯',
                name: 'nativePlace',
                show: RoleState,
                required: false,
            },
            {
                label: '最高学历',
                name: 'highestEducation',
                type: 'change',
                options: educationList,
                ruleType: 'number',
                disabled: RoleStateF,
                required: false,
            },
            {
                label: '身高',
                name: 'height',
                ruleType: 'number',
                disabled: RoleStateF,
                required: false,
                validator: validateHeight,
            },
            {
                label: 'E-mail',
                name: 'email',
                required: false,
                showbr: true, //换行
                show: RoleState,
            },
            {
                label: '户籍地址(身份证)',
                name: 'censusRegisterAddress',
                width: '66%',
                disabled: RoleStateF,
                required: false,
            },
            {
                label: '邮编',
                name: 'censusRegisterPostcode',
                required: false,
                disabled: RoleStateF,
            },
            {
                label: '通讯地址(现居地)',
                name: 'contactAddressPrefix',
                width: '66%',
                disabled: RoleStateF,
                slots: 'contactAddress',
                type: 'slots',
                required: false,
                validator: contactAddressBlur,
            },
            {
                label: '街道/小区/门牌号',
                name: 'contactAddressDetail',
                width: '30%',
                disabled: RoleStateF,
                required: false,
            },
            {
                label: '邮编',
                name: 'contactPostcode',
                required: false,
                disabled: RoleStateF,
            },
            {
                label: '适配岗位',
                name: 'stationList',
                slots: 'select',
                default: [],
                type: 'slots',
                ruleType: 'array',
                required: false,
            },
            {
                label: '职称',
                name: 'nickName',
                showbr: true, //换行
                required: false,
                disabled: RoleStateF,
            },
            {
                label: '户口性质',
                name: 'householdRegistration',
                type: 'change',
                options: residenceList,

                required: false,
                ruleType: 'number',
                disabled: RoleStateF,
            },
            {
                label: '户口所在地',
                name: 'domicilePlace',
                showbr: true, //换行
                required: false,
                disabled: RoleStateF,
            },
            {
                label: '是否服兵役',
                name: 'izMilitary',
                ruleType: 'boolean',
                type: 'isTrue',
                default: false,
                disabled: RoleStateF,
                onChange: changeIzMilitary,
            },
            {
                label: '服兵役期限',
                name: 'militaryDate',
                slots: 'date',
                default: [],
                type: 'slots',
                ruleType: 'array',
                // required: false,
                show: validIzMilitary,
            },
            {
                label: '系统录入时间',
                name: 'createdDate',
                type: 'date',
                required: false,
                disabled: true,
                show: RoleState,
            },
            {
                label: '奖惩情况',
                name: 'rewardsPunishment',
                width: '100%',
                required: false,
                show: RoleState,
            },
            {
                label: '其他信息',
                name: 'staffFieldList',
                slots: 'inputdouble',
                type: 'slots',
                width: '100%',
                required: false,
                ruleType: 'array',
                default: [{ fieldName: '', fieldValue: '' }],
                show: RoleState,
                external: true,
            },
            {
                label: typePage.value == 'talent' ? '人才标签' : '员工标签',
                name: 'staffNoteList',
                slots: 'input',
                type: 'slots',
                width: '100%',
                required: false,
                ruleType: 'array',
                default: [{ staffNote: '' }],
                show: RoleState,
                external: true,
            },
            {
                label: '备注',
                name: 'remark',
                width: '99%',
                required: false,
                show: RoleState,
            },
        ])

        const basicDetail = ref<object>({})

        // Form 实例
        const formInline = ref(null) as any
        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formData.value.staffFieldList = [{ fieldName: '', fieldValue: '' }]
            formData.value.staffNoteList = [{ staffNote: '' }]
            formInline.value.resetFields()
        }
        const staffField = ref({
            fieldName: '',
            fieldValue: '',
        })
        const staffNote = ref({
            staffNote: '',
        })
        // 获取基本信息详情
        const getBasicDetail = (value) => {
            request.get('/api/hr-talent-staffs', { id: value, izDefault: izDefault.value }).then((res) => {
                basicDetail.value = toRefs<any>(res)
                formData.value = { ...Object.assign({}, initFormData, basicDetail.value) }

                formData.value.contactAddressPrefix = formData.value.contactAddressPrefix?.split('/')
                let myStationList =
                    formData.value.stationList?.map((item) => {
                        return item.id
                    }) || []
                formData.value.stationList = [...myStationList]
                if (formData.value.politicsStatus == 1) {
                    // 是否显示入党日期
                    showPolitics.value = true
                } else {
                    showPolitics.value = false
                }
                // 是否显示服兵役期限
                if (formData.value.izMilitary == true) {
                    validIzMilitary.value = true
                } else {
                    validIzMilitary.value = false
                }
                if (viewType.value == 'perfect') {
                    getInfoPerfect(false, 1)
                }
                if (!formData.value?.staffFieldList?.length) {
                    formData.value.staffFieldList = [{ fieldName: '', fieldValue: '' }]
                }
                if (!formData.value?.staffNoteList?.length) {
                    formData.value.staffNoteList = [{ staffNote: '' }]
                }
            })
        }
        const getInfoPerfect = (replace, val?) => {
            let myOptionsCheck = replace
            if (!replace) {
                myOptionsCheck = myOptions.value.every((item) => {
                    if (unref(item.show) == false) {
                        return true
                    }

                    if (unref(item.required) != false) {
                        if (typeof formData.value[item.name] == 'boolean') {
                            return true
                        }
                        return !isEmpty(formData.value[item.name])
                    } else {
                        return true
                    }
                })
            }
            const callback = (identification) => {
                if (replace && !identification) {
                    emitConfirm()
                }
            }
            if (!val) {
                emit('getInfoPerfect', 'tab1', !myOptionsCheck, replace, callback)
            }
        }
        watch(
            visible,
            () => {
                if (typePage.value == 'staff') {
                    izDefault.value = false
                } else if (typePage.value == 'talent') {
                    izDefault.value = true
                }
                if (visible.value) {
                    if (staffId.value) {
                        getBasicDetail(staffId.value)
                        staffStore().setStaff(staffId.value) //设置
                        RoleState.value = viewType.value == 'edit' || viewType.value == 'perfect'
                        RoleStateF.value = viewType.value == 'see'
                    } else {
                        formData.value = JSON.parse(JSON.stringify(initFormData))
                        formData.value.systemNum = 'SY' + new Date().getTime()
                        if (formData.value.izMilitary == true) {
                            validIzMilitary.value = true
                        } else {
                            validIzMilitary.value = false
                        }

                        staffStore().setStaff('') //设置
                    }
                    if (!formData.value?.staffFieldList?.length) {
                        formData.value.staffFieldList = [{ fieldName: '', fieldValue: '' }]
                    }
                    if (!formData.value?.staffNoteList?.length) {
                        formData.value.staffNoteList = [{ staffNote: '' }]
                    }
                }
            },
            { immediate: true },
        )

        const dateChange = (date, dateString) => {
            formData.value.militaryDate = dateString
            // formData.value.startDate = dateString[0]
            // formData.value.endDate = dateString[1]
        }
        // 添加其他信息
        const addField = () => {
            if (!formData.value?.staffFieldList) {
                formData.value.staffFieldList = []
            }
            formData.value.staffFieldList.push({
                fieldName: '',
                fieldValue: '',
            })
            // staffField.value.fieldName = ''
            // staffField.value.fieldValue = ''
        }
        // 删除其他信息
        const delField = (item, index) => {
            formData.value.staffFieldList.splice(index, 1)
        }
        // 添加员工标签
        const addStaffNode = () => {
            if (!formData.value?.staffNoteList) {
                formData.value.staffNoteList = []
            }
            formData.value.staffNoteList.push({ staffNote: staffNote.value.staffNote })
            // staffNote.value.staffNote = ''
        }
        // 删除员工标签
        const delStaffNode = (item, index) => {
            formData.value.staffNoteList.splice(index, 1)
        }
        //二次校验问题
        let secondaryVerification = false
        const confirm = () => {
            console.log('confirm')
            isSubmittingSkipValidIdCard = true

            secondaryVerification = true
            let temp = formData.value.contactAddressPrefix ? formData.value.contactAddressPrefix?.join('') : null
            formData.value.contactAddressPrefix = formData.value?.contactAddressPrefix
                ? formData.value.contactAddressPrefix?.join('/')
                : null
            formData.value.contactAddress = temp + '' + formData.value.contactAddressDetail
            if (formData.value.staffNoteList) {
                formData.value.staffNoteList = formData.value.staffNoteList.map((element) => {
                    return { staffNote: element.staffNote }
                })
            }
            if (formData.value.staffFieldList) {
                formData.value.staffFieldList = formData.value.staffFieldList.map((element) => {
                    return { fieldName: element.fieldName, fieldValue: element.fieldValue }
                })
            }
            // if(!phoneVal.value) {
            //     message.warning('手机号码已存在!')
            // }
            formData.value.izDefault = izDefault.value

            let newStationList = []

            if (formData.value.stationList) {
                newStationList = formData.value?.stationList?.map((element) => {
                    return { id: element }
                })
            }

            // return
            formInline.value
                .validate()
                .then(async () => {
                    let staffFieldList =
                        formData.value.staffFieldList?.filter((itemField, index) => {
                            return itemField.fieldName
                        }) || []
                    let staffNoteList =
                        formData.value.staffNoteList?.filter((itemField, index) => {
                            return itemField.staffNote
                        }) || []
                    if (staffId.value) {
                        if (formData.value.izStartEnd == 0 && formData.value.staffStatus == '2') {
                            formData.value.izPreserve = 1
                        }
                        await request.put('/api/hr-talent-staffs/update', {
                            ...formData.value,
                            stationList: newStationList,
                            id: staffId.value,
                            staffFieldList,
                            staffNoteList,
                        })

                        if (viewType.value == 'perfect') {
                            getInfoPerfect(true)
                        } else {
                            emitConfirm()
                            message.success('编辑成功!')
                        }
                    } else {
                        await request
                            .post('/api/hr-talent-staffs/insert', {
                                ...formData.value,
                                stationList: newStationList,
                                staffFieldList,
                                staffNoteList,
                            })
                            .then((res) => {
                                message.success('新增成功!')
                                staffStore().setStaff(res.id) //设置
                                cancel()

                                emit('confirm', formData.value)
                            })
                    }

                    // 表单关闭后的其它操作 如刷新表
                    // emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        const emitConfirm = () => {
            emit('confirm', formData.value)
            cancel()
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        const formValidateOptional = (nameList: string[]) => {
            if (!secondaryVerification) {
                nextTick(() => {
                    formInline.value?.validateFields(nameList)
                })
            }
            secondaryVerification = false
        }

        const validateStaffFieldList = {
            required: false,
            trigger: ['change', 'blur'],
            type: 'string',
            validator: async (rule: inObject, value: any) => {
                let formDataItem = formData.value?.staffFieldList[rule.field.split('.')[1]]
                if (formDataItem.fieldName || formDataItem.fieldValue) {
                    if (!formDataItem.fieldName || !formDataItem.fieldValue) {
                        return Promise.reject('请将其他信息设置完整')
                    }
                }
                return Promise.resolve()
            },
        }

        return {
            rules,
            formData,
            myOptions,
            formInline,
            nationalityList,
            certificateList,
            sexList,
            marriageList,
            educationList,
            residenceList,
            politicalList,
            certificateType,
            staffField,
            staffNote,
            // station,
            basicDetail,
            showPolitics,
            RoleState,
            izDefault,
            validIzMilitary,
            // 事件
            confirm,
            cancel,
            idCardValidity,
            dateChange,
            addField,
            delField,
            addStaffNode,
            delStaffNode,
            changeIzMilitary,
            resetFormData,
            contract,
            cityList,
            validateStaffFieldList,
            contractAddressChange,
        }
    },
})
</script>

<style scoped lang="less">
.form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    position: relative;

    p {
        width: 100%;
        padding: 0;
        margin: 0;
    }

    :deep(.ant-form-item) {
        width: 33%;
    }

    :deep(.img) {
        position: absolute;
        right: 1%;
    }

    .hide {
        :deep(.ant-form-item-required) {
            display: none;
        }

        :deep(.ant-col.ant-form-item-label) {
            label {
                color: rgba(0, 0, 0, 0) !important;
            }
        }
    }

    .hide.staffFieldListForm {
        width: 50% !important;

        &:nth-child(odd) {
            :deep(.ant-form-item-label) {
                width: 0 !important;
            }
        }
    }

    .hide.staffNoteList {
        width: calc((100% - 130px) / 4) !important;

        :deep(.ant-form-item-label) {
            width: 0 !important;
        }

        &.staffNoteListFrist {
            width: calc((100% - 130px) / 4 + 130px) !important;

            :deep(.ant-form-item-label) {
                width: 130px !important;
            }
        }
    }

    .other-input {
        width: 191px;
    }
}

.ant-modal-body {
    padding: 24px 0 0 !important;

    & > .ant-form.ant-form-horizontal {
        margin: 0 24px 24px;
    }
}
</style>
