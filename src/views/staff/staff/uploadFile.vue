<template>
    <div class="file">
        <ImportFile
            v-model:fileUrls="fileUrls"
            :actionData="{ staffId }"
            :api="'/api/hr-talent-staffs/upload-single-file'"
            ref="refImportFile"
            @delChange="delchange"
            :readOnly="viewType == 'see'"
        />
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, nextTick, onMounted } from 'vue'
import request from '/@/utils/request'
// import { getValuesAndRules } from '/@/utils/index'
// import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
// import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'
// import permissionStore from '/@/store/modules/permission'
// import staffStore from '/@/store/modules/staff'

export default defineComponent({
    name: 'AddStaffBasic',
    components: {},
    props: {
        staffId: String,
        viewType: String,
        visible: Boolean,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { staffId, visible } = toRefs<any>(props)
        const fileUrls = ref<Object[]>([])
        const refImportFile = ref()

        // 获取附件详情
        const getDetail = (value) => {
            request.get('/api/hr-talent-staffs/appendix', { id: value }).then((res) => {
                if (res.appendixList) {
                    fileUrls.value = res.appendixList.map((item) => {
                        return { name: item.name, url: item.fileUrl, id: item.id, originName: item.originName }
                    })
                } else {
                    fileUrls.value = []
                }
            })
        }
        //  :visible="visible" :viewType="viewType"
        watch(
            visible,
            () => {
                if (!visible.value) {
                    return
                }
                if (staffId.value) {
                    getDetail(staffId.value)
                } else {
                    fileUrls.value = []
                }
            },
            { immediate: true },
        )
        const delchange = (value) => {
            request.get('/api/hr-talent-staffs/delete-file', { staffId: staffId.value, appendixId: value.id }).then((res) => {
                message.success('删除成功')
            })
        }

        return {
            fileUrls,
            refImportFile,
            delchange,
        }
    },
})
</script>
<style scoped lang="less">
.file {
    margin-top: 20px;
    min-height: 500px;
}
</style>
