<template>
    <div class="relatedInfoBox">
        <div class="relatedInfoModular hrClientDTO">
            <h3 class="relatedInfoTitle">所属客户信息</h3>
            <div class="relatedInfoModularInfo hrClientDTOinfo W50">
                <p>
                    <span class="lable">所属客户:</span><span class="info">{{ infoData.hrClientDTO?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">客户层级:</span><span class="info">{{ infoData.hrClientDTO?.levelClient }}</span>
                </p>
                <p>
                    <span class="lable">使用协议:</span><span class="info">{{ infoData.hrClientDTO?.agreementTitle }}</span>
                </p>
                <p>
                    <span class="lable">协议编号:</span><span class="info">{{ infoData.hrClientDTO?.agreementNumber }}</span>
                </p>
            </div>
            <div class="hrClientDTOtree">
                <span class="lable">上级客户信息:</span>
                <div class="info">
                    <Tree
                        :tree-data="treeData"
                        :selectable="false"
                        :replaceFields="{ children: 'children', title: 'clientName', key: 'key' }"
                    />
                </div>
            </div>
            <p class="default" v-if="!infoData.hrClientDTO?.clientName">暂无数据</p>
        </div>

        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">工伤信息</h3>
            <div class="relatedInfoModularInfo W25" v-for="(DTOitem, DTOindex) in infoData.hrWorkInjuryDTOList" :key="DTOindex">
                <p>
                    <span class="lable">所在单位:</span><span class="info">{{ DTOitem?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">受伤日期:</span><span class="info">{{ DTOitem?.injuryDate }}</span>
                </p>
                <p>
                    <span class="lable">认定结果:</span><span class="info">{{ DTOitem?.statusLabel }}</span>
                </p>
                <p>
                    <span class="lable">停工留薪期:</span
                    ><span class="info">{{ DTOitem?.workStoppageStartDate }}-{{ DTOitem?.workStoppageEndDate }}</span>
                </p>
                <p>
                    <span class="lable">申请描述:</span><span class="info">{{ DTOitem?.injuryDescription }}</span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrWorkInjuryDTOList?.length">暂无数据</p>
        </div>

        <div class="relatedInfoModular hrLaborAppraisalDTOList">
            <h3 class="relatedInfoTitle">劳动能力鉴定</h3>
            <div
                class="relatedInfoModularInfo W25"
                v-for="(DTOitem, DTOindex) in infoData.hrLaborAppraisalDTOList"
                :key="DTOindex"
            >
                <p>
                    <span class="lable">所在单位:</span><span class="info">{{ DTOitem?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">申请鉴定日期:</span><span class="info">{{ DTOitem?.createdDate }}</span>
                </p>
                <p>
                    <span class="lable">鉴定结果:</span
                    ><span class="info">
                        <Tooltip placement="top" v-if="DTOitem?.hrAppendixDTO">
                            <template #title>
                                <span>{{ DTOitem?.hrAppendixDTO?.originName }}</span>
                            </template>
                            <a
                                href="javascript: void(0)"
                                @click="previewFile(DTOitem?.hrAppendixDTO?.fileUrl)"
                                style="margin-right: 10px"
                            >
                                {{ DTOitem?.hrAppendixDTO?.originName }}
                            </a>
                        </Tooltip>
                    </span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrLaborAppraisalDTOList?.length">暂无数据</p>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">医疗备案信息</h3>
            <div class="relatedInfoModularInfo W25">
                <p>
                    <span class="lable">所在单位:</span><span class="info">{{ infoData.hrTalentStaffDTO?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">是否已医疗备案:</span>
                    <span class="info">{{ infoData.hrTalentStaffDTO?.medicalRecordDate }}</span>
                </p>
                <p>
                    <span class="lable">医疗备案日期:</span>
                    <span class="info">{{ infoData.hrTalentStaffDTO?.medicalRecordDate }}</span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrTalentStaffDTO">暂无数据</p>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">生育信息</h3>
            <div class="relatedInfoModularInfo" v-for="(DTOitem, DTOindex) in infoData.hrFertilityDTOList" :key="DTOindex">
                <p>
                    <span class="lable">所在单位:</span><span class="info">{{ DTOitem?.clientName }}</span>
                </p>
                <p style="width: 80%">
                    <span class="lable">产假（{{ DTOindex + 1 }}）起止日期:</span
                    ><span class="info">{{ DTOitem?.maternityLeaveStartDate }}-{{ DTOitem?.maternityLeaveEndDate }}</span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrFertilityDTOList?.length">暂无数据</p>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">生育津贴信息</h3>
            <div class="relatedInfoModularInfo" v-for="(DTOitem, DTOindex) in infoData.hrMaternityAllowanceDTOS" :key="DTOindex">
                <p>
                    <span class="lable">所在单位:</span><span class="info">{{ DTOitem?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">生育病种:</span>
                    <span class="info">{{ DTOitem?.diseaseType }}</span>
                </p>
                <p>
                    <span class="lable">生育/手术时间:</span>
                    <span class="info">{{ DTOitem?.operationDate }}</span>
                </p>
                <p>
                    <span class="lable">生育服务手册编号:</span>
                    <span class="info">{{ DTOitem?.manualNum }}</span>
                </p>
                <p>
                    <span class="lable">出生医学证明编号:</span>
                    <span class="info">{{ DTOitem?.proveNum }}</span>
                </p>
                <p>
                    <span class="lable">胎次:</span>
                    <span class="info">{{ DTOitem?.parity }}</span>
                </p>
                <p>
                    <span class="lable">胎儿数:</span>
                    <span class="info">{{ DTOitem?.fetusNum }}</span>
                </p>
                <p>
                    <span class="lable">孕周数:</span>
                    <span class="info">{{ DTOitem?.gestationalWeeks }}</span>
                </p>
                <p>
                    <span class="lable">联系电话:</span>
                    <span class="info">{{ DTOitem?.phone }}</span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrMaternityAllowanceDTOS?.length">暂无数据</p>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">薪酬福利信息</h3>
            <div class="relatedInfoModularInfo W16">
                <!-- <p>
                    <span class="lable">单位社保基数:</span
                    ><span class="info">{{ infoData.hrSocialSecurityDTO?.socialSecurityCardinal }}</span>
                </p> -->
                <template v-if="cardinalArr.length > 0">
                    <p v-for="(item, index) in cardinalArr" :key="index">
                        <span class="lable mylable" :title="item.title">{{ item.title }}</span
                        ><span class="info">{{
                            infoData.hrSocialSecurityDTO ? infoData.hrSocialSecurityDTO[item.dataIndexS[0]] : null
                        }}</span>
                    </p>
                </template>
                <template v-if="cardinalArr.length == 0">
                    <p>
                        <span class="lable">单位养老基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.unitPensionCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">单位失业基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.unitUnemploymentCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">单位生育基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.unitMaternityCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">单位工伤基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.workInjuryCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">单位医保基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.medicalInsuranceCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">个人生育基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.personalMaternityCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">个人养老基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.personalPensionCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">个人失业基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.medicalInsuranceCardinalPersonal }}</span>
                    </p>
                    <p>
                        <span class="lable">个人医保基数:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.personalUnemploymentCardinal }}</span>
                    </p>
                    <p>
                        <span class="lable">单位大额医疗:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.unitLargeMedicalExpense }}</span>
                    </p>
                    <p>
                        <span class="lable">个人大额医疗:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.personalLargeMedicalExpense }}</span>
                    </p>
                    <p>
                        <span class="lable">补充工伤:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.replenishWorkInjuryExpense }}</span>
                    </p>
                    <p>
                        <span class="lable">单位商业保险:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.commercialInsurance }}</span>
                    </p>
                    <p>
                        <span class="lable">单位企业年金:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.unitEnterpriseAnnuity }}</span>
                    </p>
                    <p>
                        <span class="lable">个人企业年金:</span
                        ><span class="info">{{ infoData.hrSocialSecurityDTO?.personalEnterpriseAnnuity }}</span>
                    </p>
                </template>
                <p>
                    <span class="lable">单位医疗比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.unitMedical ? infoData.hrSocialSecurityDTO?.unitMedical + '%' : ''
                    }}</span>
                </p>
                <p>
                    <span class="lable">单位养老比例:</span>
                    <span class="info">
                        {{ infoData.hrSocialSecurityDTO?.unitPension ? infoData.hrSocialSecurityDTO?.unitPension + '%' : '' }}
                    </span>
                </p>
                <p>
                    <span class="lable">单位失业比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.unitUnemployment ? infoData.hrSocialSecurityDTO?.unitUnemployment + '%' : ''
                    }}</span>
                </p>

                <p>
                    <span class="lable">单位工伤比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.workInjury ? infoData.hrSocialSecurityDTO?.workInjury + '%' : ''
                    }}</span>
                </p>

                <!-- <p>
                    <span class="lable">个人社保基数:</span
                    ><span class="info">{{ infoData.hrSocialSecurityDTO?.socialSecurityCardinalPersonal }}</span>
                </p> -->

                <p>
                    <span class="lable">个人医疗比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.personalMedical ? infoData.hrSocialSecurityDTO?.personalMedical + '%' : ''
                    }}</span>
                </p>
                <p>
                    <span class="lable">个人养老比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.personalPension ? infoData.hrSocialSecurityDTO?.personalPension + '%' : ''
                    }}</span>
                </p>

                <p>
                    <span class="lable">个人失业比例:</span>
                    <span class="info">{{
                        infoData.hrSocialSecurityDTO?.personalUnemployment
                            ? infoData.hrSocialSecurityDTO?.personalUnemployment + '%'
                            : ''
                    }}</span>
                </p>
                <p>
                    <span class="lable">公积金缴纳基数:</span
                    ><span class="info">{{ infoData.hrSocialSecurityDTO?.accumulationFundCardinal }}</span>
                </p>
                <p>
                    <span class="lable">单位缴纳比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.unitScale ? infoData.hrSocialSecurityDTO?.unitScale + '%' : ''
                    }}</span>
                </p>
                <p>
                    <span class="lable">个人缴纳比例:</span
                    ><span class="info">{{
                        infoData.hrSocialSecurityDTO?.personageScale ? infoData.hrSocialSecurityDTO?.personageScale + '%' : ''
                    }}</span>
                </p>
            </div>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">证明开具信息</h3>
            <div
                class="relatedInfoModularInfo W25"
                v-for="(DTOitem, DTOindex) in infoData.hrCertificateIssuanceDTOList"
                :key="DTOindex"
            >
                <p>
                    <span class="lable">所开证明:</span><span class="info">{{ DTOitem?.certificateName }}</span>
                </p>
                <p>
                    <span class="lable">申请日期:</span><span class="info">{{ DTOitem?.createdDate }}</span>
                </p>
                <!-- <p>
                    <span class="lable">证明文件模板？:</span><span class="info">{{ DTOitem?.certificateName }}</span>
                </p> -->
                <p style="width: 50%">
                    <span class="lable">已开具证明:</span>
                    <span class="info hrAppendixListBox">
                        <!-- <template v-if="DTOitem?.hrAppendixDTO && DTOitem?.hrAppendixDTO.length"> -->
                        <!-- <span v-for="ele in DTOitem.hrAppendixDTO" :key="ele.id"> -->
                        <Tooltip placement="top" v-if="DTOitem?.hrAppendixDTO">
                            <template #title>
                                <span>{{ DTOitem?.hrAppendixDTO?.originName }}</span>
                            </template>
                            <a
                                href="javascript: void(0)"
                                @click="previewFile(DTOitem?.hrAppendixDTO?.fileUrl)"
                                style="margin-right: 10px"
                            >
                                {{ DTOitem?.hrAppendixDTO?.originName }}
                            </a>
                        </Tooltip>
                        <!-- </span> -->
                        <!-- </template> -->
                    </span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrCertificateIssuanceDTOList?.length">暂无数据</p>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">档案信息</h3>
            <div class="relatedInfoModularInfo W25">
                <p>
                    <span class="lable">档案编号:</span><span class="info">{{ infoData.hrArchivesManageDTO?.archivesNum }}</span>
                </p>
                <p>
                    <span class="lable">档案位置:</span
                    ><span class="info">{{ infoData.hrArchivesManageDTO?.archivesLocal }}</span>
                </p>
                <p>
                    <span class="lable">档案状态:</span
                    ><span class="info">{{ infoData.hrArchivesManageDTO?.archivesStatusStr }}</span>
                </p>
            </div>
            <div v-if="archivesId">
                <span class="lable">变更记录</span>
                <BasicTable
                    ref="tableRef"
                    useIndex
                    api="/api/hr-archives-brings/page"
                    :params="{
                        archivesId: archivesId,
                    }"
                    :columns="columns"
                    :scroll="{ y: '500' }"
                    :sorter="false"
                    :rowSelectionShow="false"
                >
                    <template #operation="{ record }">
                        <Button size="small" type="primary" @click="showRow(record)">查看</Button>
                    </template>
                </BasicTable>
            </div>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">合同信息</h3>
            <div class="relatedInfoModularInfo W25" v-for="(DTOitem, DTOindex) in infoData.hrContractDTOList" :key="DTOindex">
                <p>
                    <span class="lable">客户名称:</span><span class="info">{{ DTOitem?.clientName }}</span>
                </p>
                <p>
                    <span class="lable">合同开始日期:</span><span class="info">{{ DTOitem?.contractStartDate }}</span>
                </p>
                <p>
                    <span class="lable">合同结束日期:</span><span class="info">{{ DTOitem?.contractEndDate }}</span>
                </p>
                <p>
                    <span class="lable">合同状态:</span><span class="info">{{ DTOitem?.stateStr }}</span>
                </p>
            </div>
        </div>
        <div class="relatedInfoModular">
            <h3 class="relatedInfoTitle">借调信息</h3>
            <div
                class="relatedInfoModularInfo W25"
                v-for="(DTOitem, DTOindex) in infoData.hrStaffSecondmentDTOList"
                :key="DTOindex"
            >
                <p>
                    <span class="lable">借调至单位:</span><span class="info">{{ DTOitem?.newClientName }}</span>
                </p>
                <p>
                    <span class="lable">借调开始日期:</span><span class="info">{{ DTOitem?.startDate }}</span>
                </p>
                <p>
                    <span class="lable">借调结束日期:</span><span class="info">{{ DTOitem?.endDate }}</span>
                </p>
                <p>
                    <span class="lable">借调原因:</span><span class="info">{{ DTOitem?.reason }}</span>
                </p>
            </div>
            <p class="default" v-if="!infoData.hrCertificateIssuanceDTOList?.length">暂无数据</p>
        </div>
    </div>
    <!-- 变更记录明细 -->
    <ChangeDetail :currentId="detailValue?.id" :visible="showDetail" @cancel="detailClose" />
</template>
<script lang="ts">
import { ref, defineComponent, toRefs, watch, nextTick, onMounted, h } from 'vue'
import ChangeDetail from '/@/views/recordAdmin/changeRecord/DetailModal.vue'
import request from '/@/utils/request'
import { archivesStatusList, ctTypeList } from '/@/utils/dictionaries'
import { previewFile } from '/@/utils'
import { ArrToTree, getMaxlevel } from '/@/utils/index'
export default defineComponent({
    name: 'RelatedInfo',
    components: { ChangeDetail },
    props: {
        staffId: String,
        viewType: String,
        visible: Boolean,
        clientId: String,
    },
    emits: ['cancel', 'confirm'],
    setup(props, { emit }) {
        const { staffId, visible, clientId, viewType } = toRefs<any>(props)
        const infoData = ref<inObject>({ hrClientDTO: {} })
        const archivesId = ref<any>('')
        const tableRef = ref()
        const treeData = ref<any[]>([
            // {
            //     title: 'parent 1',
            //     key: '0-0',
            //     children: [
            //         {
            //             title: 'parent 1-1',
            //             key: '0-0-1',
            //             children: [{ key: '0-0-1-0', slots: { title: 'title0010' } }],
            //         },
            //     ],
            // },
        ])
        // 获取附件详情
        const getDetail = (value) => {
            request.get('/api/hr-talent-staff/related-information', { id: value }).then((res) => {
                infoData.value = res
                archivesId.value = res.hrArchivesManageDTO?.id
                treeData.value = ArrToTree(res.hrClientDTO?.hrClientList, { id: 'id', pid: 'parentId' })
            })
        }

        // 获取社保基数列表
        const cardinalArr = ref<any[]>([])
        const getList = async () => {
            const res = await request.post(`/api/staff-cardinal/dynamic-header`, { clientId: clientId.value })
            cardinalArr.value = dealfun(res)
        }
        const dealfun = (arr) => {
            let resArr: any = [...arr]
            resArr.map((item: any) => {
                for (const key in item) {
                    const element = item[key]
                    if (!element) {
                        delete item[key]
                    }
                }
                item.dataIndexS?.forEach((el) => {
                    item[el] = null
                })
                return item
            })
            return resArr
        }
        //  :visible="visible" :viewType="viewType"
        watch(
            visible,
            () => {
                if (!visible.value) {
                    return
                }
                archivesId.value = ''
                if (staffId.value) {
                    getDetail(staffId.value)
                    getList()
                } else {
                    infoData.value = { hrClientDTO: {} }
                }
            },
            { immediate: true },
        )

        const detailValue = ref<Recordable>({})
        const showDetail = ref(false)
        const showRow = (record) => {
            detailValue.value = { ...record }
            showDetail.value = true
        }
        const detailClose = () => {
            showDetail.value = false
        }

        return {
            previewFile,
            tableRef,
            detailValue,
            showDetail,
            detailClose,
            showRow,
            infoData,
            archivesId,
            treeData,

            cardinalArr,

            columns: [
                {
                    title: '操作类型',
                    dataIndex: 'ctType',
                    align: 'center',
                    customRender: ({ text }) => {
                        return h('span', ctTypeList.find((i) => i.value === text)?.label || '-')
                    },
                },
                {
                    title: '操作事由',
                    dataIndex: 'ctProposesStr',
                    align: 'center',
                },
                {
                    title: '档案明细',
                    dataIndex: 'archivesDetailList',
                    align: 'center',
                    customRender: ({ text }) => {
                        return text?.map((i) => i.name + '、')
                    },
                },
                {
                    title: '操作时间',
                    dataIndex: 'createdDate',
                    align: 'center',
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    width: 120,
                    slots: { customRender: 'operation' },
                },
            ],
        }
    },
})
</script>
<style scoped lang="less">
.relatedInfoBox {
    margin-top: 20px;
    min-height: 500px;
    .relatedInfoTitle {
        width: 100%;
        color: rgba(16, 16, 16, 100);
        font-size: 16px;
        line-height: 30px;
    }
    .relatedInfoModular {
        display: flex;
        flex-wrap: wrap;
        border-bottom: 1px dashed rgba(187, 187, 187, 100);
        padding-bottom: 10px;
        margin-bottom: 10px;
        font-size: 14px;
        .relatedInfoModularInfo {
            // flex: 1;
            margin-top: 10px;
            width: 100%;
            & > p {
                display: inline-flex;
                width: 20%;

                line-height: 30px;
            }
        }
        .W25 {
            & > p {
                width: 25%;
            }
        }
        .W50 {
            & > p {
                width: 50%;
            }
        }
        .W16 {
            & > p {
                width: 16.66%;
            }
        }
    }
    .hrClientDTO {
        .hrClientDTOinfo {
            width: auto;
            flex: 2;
        }
        .hrClientDTOtree {
            display: inline-flex;
            flex: 1;
        }
    }
    .lable {
        // width: 70px;
        flex-shrink: 0;
        display: inline-block;
        padding-right: 10px;
        line-height: 24px;
    }
    .mylable {
        max-width: 75%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &::after {
            content: ' :';
        }
    }
    .info {
        white-space: nowrap;
        overflow: hidden;
        line-height: 24px;
        text-overflow: ellipsis;
    }
    :deep(.ant-tree-treenode-switcher-close),
    :deep(.ant-tree-treenode-switcher-open) {
        padding: 0;
    }
}
.hrAppendixListBox {
    display: inline-block;
    padding-right: 10px;
    .enclosure {
        line-height: 26px;
        color: @primary-color;
        display: inline-block;
        cursor: pointer;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        &:hover {
            background: #ddd;
        }
    }
}
.default {
    text-align: center;
    width: 100%;
}
</style>
