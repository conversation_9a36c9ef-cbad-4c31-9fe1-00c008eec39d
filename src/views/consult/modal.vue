<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title" width="1000px" :footer="null">
        <Form
            ref="formInline"
            :model="formData"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 16 }"
            :rules="rules"
            class="form-flex"
        >
            <template v-for="(itemForm, i) in myOptions" :key="i">
                <MyFormItem :width="itemForm.width" :item="itemForm" v-model:value="formData[itemForm.name]">
                    <template #WangEditor>
                        <div class="WangEditor">
                              <WangEditor @on-change="handleChange" v-model:value="formData.informationContent" ref="editorRef" />
                        </div>
                    </template>
                </MyFormItem>
            </template>
        </Form>
        <div class="ant-modal-footer">
            <Button key="back" @click="cancel">取消</Button>
            <Button key="submit" type="primary" @click="confirm">保存</Button>
            <Button key="submit" type="primary" @click="issue">发布</Button>
        </div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
//原先使用BasicEditModal
// 但是现在要求角色虽然是单选但是传入数组，并且后台返回数据与前端提交数据不同
import { message } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, onMounted } from 'vue'
import { valuesAndRules } from '/#/component'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'

export default defineComponent({
    name: 'Modal',
    components: {},
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
        roleList: {
            type: Array,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const value = ref<string[]>([])

        //表单数据
        //请求
        const api = '/api/hr-informations'

        const { title, item, visible } = toRefs(props)

        const myOptions = ref<valuesAndRules[]>([
            {
                label: '资讯标题',
                name: 'informationTitle',
            },

            {
                label: '资讯内容',
                name: 'informationContent',
                type: 'slots',
                slots: 'WangEditor',
            },
        ])

        // Form 实例
        const formInline = ref(null) as any
        const editorRef = ref()

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        watch(visible, () => {
            if (visible.value) {
                formData.value = Object.assign({}, initFormData, item.value)
            }
        })

        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (formData.value.informationTitle.length < 1 || formData.value.informationTitle.length >= 50) {
                        return message.warning('资讯标题字数在1~50之间')
                    }
                    if (title.value?.includes('新增资讯')) {
                        await request.post(api || '', { ...formData.value, state: false })
                        message.success('新增成功!')
                    } else if (title.value?.includes('复制资讯')) {
                        await request.post(api || '', {
                            ...formData.value,
                            id: null,
                            state: false,
                            releaseDate: null,
                            createdDate: null,
                        })
                        message.success('复制成功!')
                    } else if (title.value?.includes('编辑资讯')) {
                        await request.put(api || '', { ...formData.value, state: false })
                        message.success('编辑成功!')
                    }

                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
            // editorRef.value.setHtml('')
        }
        const handleChange = (html, value) => {
            formData.value.informationContent = html
        }
        // 发布
        const issue = async () => {
            formInline.value
                .validate()
                .then(async () => {
                    if (formData.value.informationTitle.length < 1 || formData.value.informationTitle.length >= 50) {
                        return message.warning('资讯标题字数在1~50之间')
                    }
                    if (formData.value.state === true) {
                        return message.warning('资讯已经发布')
                    }
                    if (title.value?.includes('新增资讯')) {
                        await request.post('/api/hr-informations', { ...formData.value, state: true }).then((res) => {
                            console.log(res)
                            message.success('发布成功!')
                        })
                    } else if (title.value?.includes('编辑资讯')) {
                        await request.put('/api/hr-informations', { ...formData.value, state: true }).then((res) => {
                            console.log(res)
                            message.success('发布成功!')
                        })
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((err) => {
                    console.log('表单验证失败', err)
                })
        }
        return {
            onMounted,
            confirm,
            cancel,
            rules,
            formData,
            myOptions,
            formInline,
            handleChange,
            editorRef,
            value,
            issue,
        }
    },
})
</script>
<style lang="less" scoped></style>
