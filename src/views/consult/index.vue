<template>
    <SearchBar v-model="params" :options="searchOptions" @change="searchData" />
    <div class="btns">
        <Button type="primary" v-auth="'consultManagement_add'" @click="createRow">新建</Button>
        <Button type="primary" v-auth="'consultManagement_export'" @click="exportData">{{ exportText }}</Button>
        <Button type="primary" danger v-auth="'consultManagement_delete'" @click="deleteRow">批量删除</Button>
    </div>

    <BasicTable
        ref="tableRef"
        api="/api/hr-informations/page"
        deleteApi="/api/hr-informations/deletes"
        :exportUrl="exportUrl"
        :params="params"
        :columns="columns"
        @selectedRowsArr="selectedRowsArr"
    >
        <template #operation="{ record }">
            <MyOperation :record="record" :myOperation="myOperation" @myOperationClick="(item) => item.click(record)" />
        </template>
    </BasicTable>
    <!-- 新增弹框 -->
    <MyModal :visible="showEdit" :title="modalTitle" :item="currentValue" @cancel="modalCancel" @confirm="modalConfirm" />
    <!-- 查看弹框 -->
    <BasicEditModalSlot class="aaa" title="查看资讯详情" v-model:visible="visible" @ok="handleOk" width="800px" :footer="null">
        <div class="title">
            <p>{{ watchData.informationTitle }}</p>
            <p class="main">{{ watchData.createdBy }}:&nbsp;&nbsp; {{ watchData.createdDate }}</p>
        </div>

        <div class="content" v-html="watchData.informationContent"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message } from 'ant-design-vue'
import { defineComponent, ref, computed } from 'vue'
import { SearchBarOption } from '/#/component'
import request from '/@/utils/request'
import modal from './modal.vue'
import { useRoute } from 'vue-router'
import { getDynamicText, getHaveAuthorityOperation } from '/@/utils'
export default defineComponent({
    name: 'ConsultIndex',
    components: { MyModal: modal },
    setup() {
        // 状态
        const applyStatusList = ref<LabelValueOptions>([
            {
                label: '未发布',
                value: false,
            },
            {
                label: '已发布',
                value: true,
            },
        ])
        //筛选
        const route = useRoute()
        const params = ref<{}>({
            typeName: undefined,
            applyStatusList: route.query?.applyStatusList ? JSON.parse(route.query?.applyStatusList as string) : undefined,
        })

        const searchOptions: SearchBarOption[] = [
            {
                type: 'string',
                label: '资讯标题',
                key: 'informationTitle',
            },

            {
                type: 'string',
                label: '创建人',
                key: 'createdBy',
            },
            {
                type: 'daterange',
                label: '创建时间',
                key: 'createdDateQuery',
            },
            {
                type: 'daterange',
                label: '发布时间',
                key: 'releaseDateQuery',
            },
            {
                type: 'select',
                label: '状态',
                key: 'state',
                options: applyStatusList,
            },
        ]

        //表格dom
        const tableRef = ref()
        const searchData = async () => {
            tableRef.value.refresh(1)
        }
        //表格数据
        const columns = [
            {
                title: '资讯标题',
                dataIndex: 'informationTitle',
                align: 'center',
                width: 200,
            },

            {
                title: '创建人',
                dataIndex: 'createdBy',
                align: 'center',
                width: 150,
            },
            {
                title: '创建时间',
                dataIndex: 'createdDate',
                align: 'center',
                width: 200,
            },
            {
                title: '发布时间',
                dataIndex: 'releaseDate',
                align: 'center',
                width: 200,
            },
            {
                title: '状态',
                dataIndex: 'state',
                align: 'center',
                width: 100,
                // slots: { customRender: 'staffStatus' },
                customRender: ({ text }) => {
                    if (text == false) {
                        text = '未发布'
                    } else {
                        text = '已发布'
                    }
                    return text
                },
            },

            {
                title: '操作',
                dataIndex: 'operate',
                align: 'center',
                slots: { customRender: 'operation' },
                width: 200,
                fixed: 'right',
            },
        ]
        // 查看
        const visible = ref<boolean>(false)
        const watchData = ref<any>({})
        //  查看
        const seeRow = (record) => {
            visible.value = true
            watchData.value = record
            console.log(record)
        }
        const handleOk = () => {
            visible.value = false
        }

        //新增编辑
        const showEdit = ref(false)
        const modalTitle = ref('新建')
        // 当前编辑的数据
        const currentValue = ref<any>(null)
        // 新增资讯
        const createRow = () => {
            showEdit.value = true
            modalTitle.value = '新增资讯'
            currentValue.value = null
        }
        //编辑资讯
        const editRow = (record) => {
            showEdit.value = true
            modalTitle.value = '编辑资讯'
            currentValue.value = { ...record }
            console.log(record)
        }
        // 复制资讯
        const copyRow = (record) => {
            showEdit.value = true
            modalTitle.value = '复制资讯'
            currentValue.value = record ? { ...record } : (record.id = null)
        }
        const modalCancel = () => {
            showEdit.value = false
        }
        const modalConfirm = () => {
            if (modalTitle.value.includes('新增资讯')) {
                tableRef.value.refresh(1)
            } else {
                tableRef.value.refresh()
            }
        }
        // 选中的ID
        const applyIdList = ref<any>([])
        // 多选
        const selectedRowsArr = (item) => {
            console.log(item)
            let ids: (string | number)[] = []
            ids = item.map((val) => {
                return val.id
            })
            applyIdList.value = ids
        }
        const exportText = computed(() => {
            return getDynamicText('导出', params.value, applyIdList.value)
        })

        //发布
        const issue = (data) => {
            request.put('/api/hr-informations', { id: data.id, state: true }).then((res) => {
                console.log(res)
                message.success('发布成功!')
                tableRef.value.refresh()
            })
        }
        // 导出
        const exportUrl = '/api/hr-informations/export'
        const exportData = () => {
            tableRef.value.exportRow('ids', exportText.value, params.value)
        }
        // 批量删除
        const deleteRow = (row = { id: '' }) => {
            tableRef.value.deleteRow(row.id).then((ref) => {
                // console.log(row.id)
            })
        }
        // //操作按钮
        let myOperation = ref<inObject[]>(
            getHaveAuthorityOperation([
                {
                    neme: '查看',
                    auth: 'consultManagement_see',
                    show: true,
                    click: (record) => seeRow(record),
                },
                {
                    neme: '编辑',
                    auth: 'consultManagement_edit',

                    show: (record) => {
                        return record.state == false
                    },
                    click: (record) => editRow(record),
                },
                {
                    neme: '发布',
                    auth: 'consultManagement_issue',

                    show: (record) => {
                        return record.state == false
                    },
                    click: (record) => issue(record),
                },
                {
                    neme: '复制',
                    auth: 'consultManagement_copy',
                    show: (record) => {
                        return true
                        return record.state == true
                    },
                    click: copyRow,
                },
                // {
                //     neme: '删除',
                //     auth: 'consultManagement_delete',
                //     show: true,
                //     click: deleteRow,
                // },
            ]),
        )

        return {
            exportText,
            //表格数据
            columns,
            //表格数据
            params,
            //查询数据
            searchOptions,
            //查询刷新表格
            searchData,
            // 表格ref实例
            tableRef,
            //操作按钮
            myOperation,
            //多选数组
            selectedRowsArr,
            // 新增
            createRow,
            //编辑
            editRow,
            //弹窗开关
            showEdit,
            // 发布
            issue,
            // 复制
            copyRow,

            //弹窗标题
            modalTitle,
            //弹窗数据
            currentValue,
            //弹窗取消
            modalCancel,
            //弹窗确认
            modalConfirm,
            // 状态筛选
            applyStatusList,
            // 查看数据
            watchData,
            // 查看弹框显示隐藏
            visible,
            handleOk,
            // 导出
            exportData,
            exportUrl,
            // 删除
            deleteRow,
        }
    },
})
</script>
<style scoped lang="less">
.btn {
    background: @upload-color;
    border: none;
}
.title {
    width: 100%;
    height: 64px;
    text-align: center;
    // line-height: 64px;
    border-bottom: 1px solid rgba(224, 219, 219, 0.85);
    font-size: 20px;
    font-weight: 500;
    padding-bottom: 64px;
}
.content {
    min-height: 400px;
    padding-top: 20px;
}
.main {
    text-align: end;
    font-size: 14px;
    padding-right: 10px;
}
.show {
    margin-right: 5px;
}
.data-v-741aec9d {
    width: 15px;
    height: 15px;
}
.table-title {
    display: flex;
    justify-content: start;
    width: 100%;
    div {
        color: red;
        width: 15px;
    }
    .titles {
        width: 90%;
        text-align: center;
    }
}
</style>
