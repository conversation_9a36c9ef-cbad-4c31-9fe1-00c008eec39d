import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const reportRoutes: AppRouteRecordRaw = {
    name: 'reportAdmin',
    path: '/reportAdmin',
    meta: {
        title: '报表管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'statisticReport',
            path: 'statisticReport',
            meta: {
                title: '统计报表',
            },
            component: () => import('/@/views/reportAdmin/statisticReport/index.vue'),
        },
        {
            name: 'dataDetail',
            path: 'dataDetail',
            meta: {
                title: '数据明细',
            },
            component: () => import('/@/views/reportAdmin/dataDetail/index.vue'),
        },
        {
            name: 'reconciliationDetail',
            path: 'reconciliationDetail',
            meta: {
                title: '对账明细',
            },
            component: () => import('/@/views/reportAdmin/reconciliationDetail/index.vue'),
        },
    ],
}

export default reportRoutes
