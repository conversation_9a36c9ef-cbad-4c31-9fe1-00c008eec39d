import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const systemRoutes: AppRouteRecordRaw = {
    name: 'system',
    path: '/system',
    redirect: '/system/dictionaries',
    meta: {
        title: '系统配置',
    },
    component: LAYOUT,
    children: [
        {
            name: 'postManage',
            path: 'postManage',
            meta: {
                title: '岗位管理',
            },
            component: () => import('/@/views/user/postManage/index.vue'),
        },
        {
            name: 'sealManage',
            path: 'sealManage',
            meta: {
                title: '印章管理',
            },
            component: () => import('/@/views/user/sealManage/index.vue'),
        },
        {
            name: 'documentType',
            path: 'documentType',
            meta: {
                title: '证件类型管理',
            },
            component: () => import('/@/views/user/documentType/index.vue'),
        },
        {
            name: 'warn',
            path: 'warn',
            meta: {
                title: '提醒配置',
            },
            component: () => import('/@/views/user/warn/index.vue'),
        },
        {
            name: 'noticeConfiguration',
            path: 'noticeConfiguration',
            meta: {
                title: '通知配置',
            },
            component: () => import('/@/views/user/noticeConfiguration/index.vue'),
        },
        {
            name: 'businessContact',
            path: 'businessContact',
            meta: {
                title: '业务联系人',
            },
            component: () => import('/@/views/system/businessContact/index.vue'),
        },

        {
            name: 'dictionaries',
            path: 'dictionaries',
            meta: {
                title: '数据字典',
            },
            component: () => import('/@/views/system/dictionaries/index.vue'),
        },
        {
            name: 'material',
            path: 'material',
            meta: {
                title: '材料管理',
            },
            component: () => import('/@/views/system/material/index.vue'),
        },
        {
            name: 'certificateTemplate',
            path: 'certificateTemplate',
            meta: {
                title: '证明模板管理',
            },
            component: () => import('/@/views/system/certificateTemplate/index.vue'),
        },
    ],
}

export default systemRoutes
