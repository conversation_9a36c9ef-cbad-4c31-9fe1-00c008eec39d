import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const expenseRoutes: AppRouteRecordRaw = {
    name: 'expenseManagement',
    path: '/expenseManagement',
    redirect: '/expenseManagement/welfareSetting',
    meta: {
        title: '费用管理',
    },
    component: LAYOUT,

    children: [
        {
            name: 'billManagement',
            path: '/billManagement',
            meta: {
                title: '账单管理',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'assureBill',
                    path: 'assureBill',
                    meta: {
                        title: '保障账单',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/billAccounting/assure/index.vue'),
                },
                {
                    name: 'salaryBill',
                    path: 'salaryBill',
                    meta: {
                        title: '薪酬账单',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/billAccounting/salary/index.vue'),
                },
                {
                    name: 'otherBill',
                    path: 'otherBill',
                    meta: {
                        title: '其他账单',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/billAccounting/other/index.vue'),
                },
                {
                    name: 'expenseApproval',
                    path: 'expenseApproval',
                    meta: {
                        title: '结算单',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/expenseApproval/index.vue'),
                },
            ],
        },
        {
            name: 'welfareSetting',
            path: 'welfareSetting',
            meta: {
                title: '福利配置',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'staffWelfare',
                    path: 'staffWelfare',
                    meta: {
                        title: '员工福利',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/staffWelfare/index.vue'),
                },
                {
                    name: 'accumulation',
                    path: 'accumulation',
                    meta: {
                        title: '公积金类型管理',
                    },
                    component: () => import('/@/views/user/accumulation/index.vue'),
                },
                {
                    name: 'socialSecurity',
                    path: 'socialSecurity',
                    meta: {
                        title: '社保类型管理',
                    },
                    component: () => import('/@/views/user/socialSecurity/index.vue'),
                },
                {
                    name: 'platform',
                    path: 'platform',
                    meta: {
                        title: '平台账号管理',
                    },
                    component: () => import('/@/views/user/platform/index.vue'),
                },
            ],
        },
        {
            name: 'salarySetting',
            path: 'salarySetting',
            meta: {
                title: '薪酬配置',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'expense',
                    path: 'expense',
                    meta: {
                        title: '费用项管理',
                    },
                    component: () => import('/@/views/expenseManagement/expense/index.vue'),
                },
                {
                    name: 'salaryYd',
                    path: 'salaryYd',
                    meta: {
                        title: '薪酬原单',
                    },
                    component: () => import('/@/views/expenseManagement/salaryYd/index.vue'),
                },
            ],
        },
    ],
}

export default expenseRoutes
