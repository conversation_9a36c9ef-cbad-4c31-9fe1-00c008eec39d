import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const logRoutes: AppRouteRecordRaw = {
    name: 'logAdmin',
    path: '/logAdmin',
    redirect: '/logAdmin/operation',
    meta: {
        title: '日志管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'operation',
            path: 'operation',
            meta: {
                title: '操作日志',
            },
            component: () => import('/@/views/system/operation/index.vue'),
        },
        /* {
            name: 'messageLog',
            path: 'messageLog',
            meta: {
                title: '短信日志',
            },
            component: () => import('/@/views/system/messageLog/index.vue'),
        }, */
        {
            name: 'billLog',
            path: 'billLog',
            meta: {
                title: '账单日志',
            },
            component: () => import('/@/views/system/billLog/index.vue'),
        },
    ],
}

export default logRoutes
