import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const staffRoutes: AppRouteRecordRaw = {
    name: 'serviceCentre',
    path: '/serviceCentre',
    redirect: '/serviceCentre/InductionList',
    meta: {
        title: '服务中心',
    },
    component: LAYOUT,
    children: [
        {
            name: 'InductionList',
            path: 'InductionList',
            meta: {
                title: '入职列表',
            },
            component: () => import('/@/views/serviceCentre/InductionList.vue'),
        },
        {
            name: 'inductionServices',
            path: 'inductionServices',
            meta: {
                title: '入职服务',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'inductionApply',
                    path: 'inductionApply',
                    meta: {
                        title: '入职申请',
                    },
                    component: () => import('/@/views/serviceCentre/inductionServices/inductionApply/index.vue'),
                },
                {
                    name: 'stayInductionStaff',
                    path: 'stayInductionStaff',
                    meta: {
                        title: '待入职员工',
                    },
                    component: () => import('/@/views/serviceCentre/inductionServices/stayInductionStaff/index.vue'),
                },
            ],
        },
        {
            name: 'dimisson',
            path: 'dimisson',
            meta: {
                title: '离职服务',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'leaveServe',
                    path: 'leaveServe',
                    meta: {
                        title: '离职服务',
                    },
                    component: () => import('/@/views/serviceCentre/dimission/leaveServe/index.vue'),
                },
                {
                    name: 'leaveStaff',
                    path: 'leaveStaff',
                    meta: {
                        title: '待离职员工',
                    },
                    component: () => import('/@/views/serviceCentre/dimission/leaveStaff/index.vue'),
                },
            ],
        },
        {
            name: 'issueCertificate',
            path: 'issueCertificate',
            meta: {
                title: '证明开具服务',
            },
            component: () => import('/@/views/serviceCentre/IssueCertificate/index.vue'),
        },
        {
            name: 'retirement',
            path: 'retirement',
            meta: {
                title: '退休服务',
            },
            component: () => import('/@/views/serviceCentre/retirement/index.vue'),
        },
        {
            name: 'labourIdentify',
            path: 'labourIdentify',
            meta: {
                title: '劳动能力鉴定服务',
            },
            component: () => import('/@/views/serviceCentre/labourIdentify/index.vue'),
        },
        {
            name: 'industrialInjury',
            path: 'industrialInjury',
            meta: {
                title: '工伤服务',
            },
            component: () => import('/@/views/serviceCentre/industrialInjury/index.vue'),
        },
        {
            name: 'medicalRecords',
            path: 'medicalRecords',
            meta: {
                title: '异地医疗备案',
            },
            component: () => import('/@/views/serviceCentre/medicalRecords/index.vue'),
        },
        {
            name: 'birthService',
            path: 'birthService',
            meta: {
                title: '生育服务',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'maternityLeave',
                    path: 'maternityLeave',
                    meta: {
                        title: '产假管理',
                    },
                    component: () => import('/@/views/serviceCentre/birthService/maternityLeave/index.vue'),
                },
                {
                    name: 'childbirthAllowance',
                    path: 'childbirthAllowance',
                    meta: {
                        title: '生育津贴管理',
                    },
                    component: () => import('/@/views/serviceCentre/birthService/childbirthAllowance/index.vue'),
                },
            ],
        },
        {
            name: 'regularWorker',
            path: 'regularWorker',
            meta: {
                title: '转正服务',
            },
            component: () => import('/@/views/serviceCentre/regularWorker/index.vue'),
        },
        {
            name: 'archives',
            path: 'archives',
            meta: {
                title: '档案服务',
            },
            component: () => import('/@/views/serviceCentre/archives/index.vue'),
        },
        {
            name: 'dataModification',
            path: 'dataModification',
            meta: {
                title: '资料修改服务',
            },
            component: () => import('/@/views/serviceCentre/dataModification/index.vue'),
        },
        {
            name: 'staffContract',
            path: 'staffContract',
            meta: {
                title: '员工合同服务',
            },
            component: () => import('/@/views/serviceCentre/staffContract/index.vue'),
        },
        {
            name: 'secondment',
            path: 'secondment',
            meta: {
                title: '借调服务',
            },
            component: () => import('/@/views/serviceCentre/secondment/index.vue'),
        },
        {
            name: 'renewal',
            path: 'renewal',
            meta: {
                title: '续签服务',
            },
            component: () => import('/@/views/serviceCentre/renewal/index.vue'),
        },
    ],
}

export default staffRoutes
