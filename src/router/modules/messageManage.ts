import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const messageRoutes: AppRouteRecordRaw = {
    name: 'messageManage',
    path: '/messageManage',
    redirect: '/messageManage/message',
    meta: {
        title: '消息管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'message',
            path: 'message',
            meta: {
                title: '消息管理',
            },
            component: () => import('/@/views/messageManage/message/index.vue'),
        },
        {
            name: 'smsAdmin',
            path: 'smsAdmin',
            meta: {
                title: '短信管理',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'smsTemplate',
                    path: 'smsTemplate',
                    meta: {
                        title: '短信模板',
                    },
                    component: () => import('/@/views/messageManage/smsAdmin/smsTemplate/index.vue'),
                },
                {
                    name: 'messageLog',
                    path: 'messageLog',
                    meta: {
                        title: '短信日志',
                    },
                    component: () => import('/@/views/system/messageLog/index.vue'),
                },
            ],
        },
    ],
}

export default messageRoutes
