import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const financeRoutes: AppRouteRecordRaw = {
    name: 'financeManagement',
    path: '/financeManagement',
    redirect: '/financeManagement/incomeTax',
    meta: {
        title: '财务管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'incomeTax',
            path: 'incomeTax',
            meta: {
                title: '个税申报',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'specialDeduction',
                    path: 'specialDeduction',
                    meta: {
                        title: '专项扣除附加项',
                    },
                    component: () => import('/@/views/expenseManagement/specialDeduction/index.vue'),
                },
                {
                    name: 'inLeave',
                    path: 'inLeave',
                    meta: {
                        title: '员工入离职',
                    },

                    component: () => import('/@/views/expenseManagement/incomeTax/inLeave/index.vue'),
                },
                {
                    name: 'normalSalary',
                    path: 'normalSalary',
                    meta: {
                        title: '正常薪金',
                    },
                    component: () => import('/@/views/expenseManagement/incomeTax/normalSalary/index.vue'),
                },
                {
                    name: 'annualLumpSumBonus',
                    path: 'annualLumpSumBonus',
                    meta: {
                        title: '全年一次性奖金',
                    },
                    component: () => import('/@/views/expenseManagement/incomeTax/annualLumpSumBonus/index.vue'),
                },
                {
                    name: 'taxDiff',
                    path: 'taxDiff',
                    meta: {
                        title: '薪金税差',
                    },
                    component: () => import('/@/views/expenseManagement/incomeTax/taxDiff/index.vue'),
                },
                {
                    name: 'deduct',
                    path: 'deduct',
                    meta: {
                        title: '速算扣除数',
                    },
                    component: () => import('/@/views/expenseManagement/deduct/index.vue'),
                },
            ],
        },
        {
            name: 'arriveRecord',
            path: 'arriveRecord',
            meta: {
                title: '到账记录',
            },
            component: () => import('/@/views/expenseManagement/arriveRecord/index.vue'),
        },
        
        {
            name: 'invoicingApplication',
            path: 'invoicingApplication',
            meta: {
                title: '开票申请',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'canInvoice',
                    path: 'canInvoice',
                    meta: {
                        title: '待开发票',
                    },
                    component: () => import('/@/views/expenseManagement/invoicingApplication/canInvoice/index.vue'),
                },
                {
                    name: 'invoicingRecords',
                    path: 'invoicingRecords',
                    meta: {
                        title: '已开发票',
                    },
                    component: () => import('/@/views/expenseManagement/invoicingApplication/invoicingRecords/index.vue'),
                },
            ],
        },
        {
            name: 'reimbursementApplication',
            path: 'reimbursementApplication',
            meta: {
                title: '报销申请',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'canReimbursement',
                    path: 'canReimbursement',
                    meta: {
                        title: '可申请报销',
                    },
                    component: () => import('/@/views/expenseManagement/reimbursementApplication/canReimbursement/index.vue'),
                },
                {
                    name: 'reimbursementRecords',
                    path: 'reimbursementRecords',
                    meta: {
                        title: '报销申请记录',
                    },
                    component: () => import('/@/views/expenseManagement/reimbursementApplication/reimbursementRecords/index.vue'),
                },
            ],
        },
        {
            name: 'certificate',
            path: 'certificate',
            meta: {
                title: '凭证管理',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'customerAssociated',
                    path: 'customerAssociated',
                    meta: {
                        title: '客户关联管理',
                    },
                    component: () => import('/@/views/certificateManagement/customerAssociated/index.vue'),
                },
                {
                    name: 'accountingSubject',
                    path: 'accountingSubject',
                    meta: {
                        title: '会计科目管理',
                    },

                    component: () => import('/@/views/certificateManagement/accountingSubject/index.vue'),
                },
            ],
        },
        {
            name: 'salaryPayment',
            path: 'salaryPayment',
            meta: {
                title: '工资发放',
            },
            component: () => import('/@/views/expenseManagement/salaryPayment/index.vue'),
        },
        {
            name: 'compareResult',
            path: 'compareResult',
            meta: {
                title: '收支对账',
            },
            component: EMPTY_LAYOUT,
            // 社保\医保\公积金\个税\第三方账单
            children: [
                {
                    name: 'socialSecurityChecked',
                    path: 'socialSecurityChecked',
                    meta: {
                        title: '社保对账',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/compareResult/socialSecurity/index.vue'),
                },
                {
                    name: 'medicalInsuranceChecked',
                    path: 'medicalInsuranceChecked',
                    meta: {
                        title: '医保对账',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/compareResult/medicalInsurance/index.vue'),
                },
                {
                    name: 'accumulationFundChecked',
                    path: 'accumulationFundChecked',
                    meta: {
                        title: '公积金对账',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/compareResult/accumulationFund/index.vue'),
                },
                {
                    name: 'personalIncomeTaxChecked',
                    path: 'personalIncomeTaxChecked',
                    meta: {
                        title: '个税对账',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/compareResult/personalIncomeTax/index.vue'),
                },
                {
                    name: 'haierChecked',
                    path: 'haierChecked',
                    meta: {
                        title: '海尔对账',
                    },
                    component: () => import('/@/views/expenseManagement/salarySettlement/compareResult/haier/index.vue'),
                },
            ],
        },
    ],
}

export default financeRoutes
