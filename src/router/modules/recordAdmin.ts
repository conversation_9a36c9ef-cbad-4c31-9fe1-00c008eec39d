import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const recordRoutes: AppRouteRecordRaw = {
    name: 'recordAdmin',
    path: '/recordAdmin',
    redirect: '/recordAdmin/recordList',
    meta: {
        title: '档案管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'recordList',
            path: 'recordList',
            meta: {
                title: '档案信息',
            },
            component: () => import('/@/views/recordAdmin/recordList/index.vue'),
        },
        {
            name: 'changeRecord',
            path: 'changeRecord',
            meta: {
                title: '档案变更记录',
            },
            component: () => import('/@/views/recordAdmin/changeRecord/index.vue'),
        },
    ],
}

export default recordRoutes
