import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const staffRoutes: AppRouteRecordRaw = {
    name: 'talent',
    path: '/talent',
    redirect: '/talent/talentList',
    meta: {
        title: '人才库',
    },
    component: LAYOUT,
    children: [
        {
            name: 'talentList',
            path: 'talentList',
            meta: {
                title: '人才信息',
            },
            component: () => import('/@/views/talentPool/talentList.vue'),
        },
    ],
}

export default staffRoutes
