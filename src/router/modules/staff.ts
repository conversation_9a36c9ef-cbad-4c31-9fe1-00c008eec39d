import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const staffRoutes: AppRouteRecordRaw = {
    name: 'staff',
    path: '/staff',
    redirect: '/staff/staffList',
    meta: {
        title: '员工管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'staffList',
            path: 'staffList',
            meta: {
                title: '员工信息',
            },
            component: () => import('../../views/staff/staff/staffList.vue'),
        },
        {
            name: 'contractList',
            path: 'contractList',
            meta: {
                title: '劳动合同',
            },
            component: () => import('../../views/staff/contractList/index.vue'),
        },
    ],
}

export default staffRoutes
