import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const userRoutes: AppRouteRecordRaw = {
    name: 'user',
    path: '/user',
    redirect: 'userAdmin',
    meta: {
        title: '用户管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'userAdmin',
            path: 'userAdmin',
            meta: {
                title: '用户列表',
            },
            component: () => import('/@/views/user/userAdmin/index.vue'),
        },
    ],
}

export default userRoutes
