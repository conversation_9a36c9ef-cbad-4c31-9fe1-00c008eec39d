import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const contractTemplate: AppRouteRecordRaw = {
    name: 'contractTemplate',
    path: '/contractTemplate',
    redirect: '/contractTemplate/contractTemplateList',
    meta: {
        title: '合同模板',
    },
    component: LAYOUT,
    children: [
        {
            name: 'contractTemplateList',
            path: 'contractTemplateList',
            meta: {
                title: '模板列表',
            },
            component: () => import('../../views/staff/contractTemplate/index.vue'),
        },
        {
            name: 'contractGroup',
            path: 'contractGroup',
            meta: {
                title: '合同组',
            },
            component: () => import('../../views/staff/contractGroup/index.vue'),
        },
    ],
}

export default contractTemplate
