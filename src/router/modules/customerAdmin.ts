import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'
const customerRoutes: AppRouteRecordRaw = {
    name: 'customer',
    path: '/customer',
    redirect: 'customer',
    meta: {
        title: '客户管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'customerRelation',
            path: 'customerRelation',
            meta: {
                title: '客户组织关系',
            },
            component: () => import('/@/views/customer/customerRelation/index.vue'),
        },
        {
            name: 'customerInfo',
            path: 'customerInfo',
            meta: {
                title: '客户信息',
            },
            component: () => import('/@/views/customer/customerInfo/index.vue'),
        },
        {
            name: 'customerAgreement',
            path: 'customerAgreement',
            meta: {
                title: '客户协议',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'expiredAgreements',
                    path: 'expiredAgreements',
                    meta: {
                        title: '生效中协议',
                    },
                    component: () => import('/@/views/customer/customerAgreement/index.vue'),
                },
                {
                    name: 'effectiveAgreement',
                    path: 'effectiveAgreement',
                    meta: {
                        title: '已过期协议',
                    },
                    component: () => import('/@/views/customer/customerAgreement/effectiveAgreement/index.vue'),
                },
            ],
        },
    ],
}

export default customerRoutes
