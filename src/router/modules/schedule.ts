import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const scheduleRoutes: AppRouteRecordRaw = {
    name: 'scheduleManage',
    path: '/scheduleManage',
    redirect: '/scheduleManage/schedule',
    meta: {
        title: '日程管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'schedule',
            path: 'schedule',
            meta: {
                title: '日程管理',
            },
            component: () => import('/@/views/scheduleManage/schedule/index.vue'),
        },
        {
            name: 'toDoList',
            path: 'toDoList',
            meta: {
                title: '日程待办',
            },
            component: () => import('/@/views/scheduleManage/toDoList/index.vue'),
        },
    ],
}

export default scheduleRoutes
