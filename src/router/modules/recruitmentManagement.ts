import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'
import EMPTY_LAYOUT from '/@/layout/EmptyLayout.vue'

const recruitmenteRoutes: AppRouteRecordRaw = {
    name: 'recruitmentManagement',
    path: '/recruitmentManagement',

    meta: {
        title: '招聘管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'recruitmentDemand',
            path: '',
            meta: {
                title: '招聘需求',
            },
            component: () => import('/@/views/recruitmentManagement/recruitmentDemand/index.vue'),
        },
        {
            name: 'recruitmentBrief',
            path: 'recruitmentBrief',
            meta: {
                title: '招聘简章',
            },
            component: () => import('/@/views/recruitmentManagement/recruitmentBrief/index.vue'),
        },

        {
            name: 'registrationTemplate',
            path: 'registrationTemplate',
            meta: {
                title: '报名模板',
            },
            component: EMPTY_LAYOUT,
            children: [
                {
                    name: 'applyTemplate',
                    path: 'applyTemplate',
                    meta: {
                        title: '模板管理',
                    },
                    component: () => import('/@/views/recruitmentManagement/registrationTemplate/applyTemplate/index.vue'),
                },
                {
                    name: 'applyContent',
                    path: 'applyContent',
                    meta: {
                        title: '内容管理',
                    },
                    component: () => import('/@/views/recruitmentManagement/registrationTemplate/applyContent/index.vue'),
                },
            ],
        },
    ],
}

export default recruitmenteRoutes
