import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const consultRoutes: AppRouteRecordRaw = {
    name: 'consult',
    path: '/consult',
    meta: {
        title: '资讯管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'consultManagement',
            path: '/consultManagement',
            meta: {
                title: '资讯管理',
            },
            component: () => import('/@/views/consult/index.vue'),
        },
    ],
}

export default consultRoutes
