import { AppRouteRecordRaw } from '../types'
import LAYOUT from '/@/layout/index.vue'

const examManageRoutes: AppRouteRecordRaw = {
    name: 'examManage',
    path: '/examManage',
    redirect: 'examManage',
    meta: {
        title: '考试管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'topicManage',
            path: 'topicManage',
            meta: {
                title: '题目管理',
            },
            component: () => import('/@/views/examManage/topicManage/index.vue'),
        },
        {
            name: 'examPaperManage',
            path: 'examPaperManage',
            meta: {
                title: '试卷管理',
            },
            component: () => import('/@/views/examManage/examPaperManage/index.vue'),
        },
        {
            name: 'examResultManage',
            path: 'examResultManage',
            meta: {
                title: '考试结果管理',
            },
            component: () => import('/@/views/examManage/examResultManage/index.vue'),
        },
    ],
}

export default examManageRoutes
