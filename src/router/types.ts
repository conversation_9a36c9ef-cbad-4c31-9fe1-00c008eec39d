import type { RouteRecordRaw, RouteMeta } from 'vue-router'
import { defineComponent } from 'vue'

export type Component<T extends any = any> =
    | ReturnType<typeof defineComponent>
    | (() => Promise<typeof import('*.vue')>)
    | (() => Promise<T>)

export type AppRouteRecordRaw = RouteRecordRaw & {
    name: string
    path: string
    meta: RouteMeta
    component?: Component | string
    children?: AppRouteRecordRaw[]
}

export type Menu = AppRouteRecordRaw & {
    hideMenu?: boolean
}

export interface MenuModule {
    orderNo?: number
    menu: Menu
}
