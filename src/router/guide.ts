import { NavigationGuardNext, RouteLocationNormalized, Router } from 'vue-router'
import useAppStore from '/@/store/modules/app'
import config from '/@/config'
import useUserStore from '../store/modules/user'
import useCacheStore from '../store/modules/cache'

const redirectToLogin = (to: RouteLocationNormalized, next: NavigationGuardNext) => {
    // 重定向到登录页
    const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path: config.loginPath,
        replace: true,
    }
    if (to.path) {
        redirectData.query = {
            ...redirectData.query,
            redirect: to.path,
        }
    }
    next(redirectData)
}

const redirectToErrPage = (from: RouteLocationNormalized, next: NavigationGuardNext) => {
    // 重定向到错误页
    const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path: '/errorPage',
        replace: true,
        query: {
            code: '401',
        },
    }
    if (from.path) {
        redirectData.query = {
            ...redirectData.query,
            redirect: from.path,
        }
    }
    next(redirectData)
}
// 路由守卫 权限
const createPermissionGuard = (router: Router) => {
    router.beforeEach(async (to, from, next) => {
        if (to.meta.ignoreToken) {
            next()
            return
        }
        if (!localStorage.token) {
            redirectToLogin(to, next)
            return
        }
        if (!useUserStore().userInfo) {
            await useUserStore().getUserInfoAction()
        }
        useCacheStore().getWhiteList.menus.includes(to.name as string) ? next() : redirectToErrPage(from, next)
    })
}

// 路由切换过程中产生的数据
const createPageGuard = (router: Router) => {
    router.beforeEach(async (to) => {
        // useAppStore().setCurrentRoute(to)
    })

    router.afterEach((to) => {
        useAppStore().setCurrentRoute(to)
        document.title = to.meta.title ? to.meta.title + ' - ' + config.title : config.title
    })
}

export function setupRouterGuard(router: Router) {
    createPageGuard(router)
    createPermissionGuard(router)
}
