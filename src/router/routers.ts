import userRoutes from './modules/user'
import systemRoutes from './modules/system'
import customerRoutes from './modules/customerAdmin'
import staffRoutes from './modules/staff'
import serviceCentreRoutes from './modules/serviceCentre'
import talentRoutes from './modules/talent'
import scheduleRoutes from './modules/schedule'
import recordRoutes from './modules/recordAdmin'
import expenseRoutes from './modules/expenseManagement'
import financeRoutes from './modules/finance'
import contractTemplate from './modules/contractTemplate'
import logRoutes from './modules/logAdmin'
import examManageRoutes from './modules/examManage'
import consultRoutes from './modules/consult'
import recruitmenteRoutes from './modules/recruitmentManagement'
import messageRoutes from './modules/messageManage'
import reportRoutes from './modules/reportAdmin'
import reportManagementRoutes from './modules/reportManagement'
import { AppRouteRecordRaw } from './types'
import LAYOUT from '/@/layout/index.vue'

// const modules = import.meta.globEager('./modules/*.ts')
// const routeModuleList: AppRouteRecordRaw[] = []
// Object.keys(modules).forEach((key) => {
//     const mod = modules[key].default || {}
//     const modList = Array.isArray(mod) ? [...mod] : [mod]
//     routeModuleList.push(...modList)
//     console.log('routeModuleList', routeModuleList)
// })

const loginRoutes: AppRouteRecordRaw[] = [
    {
        path: '/',
        redirect: '/home',
        name: 'redirect',
        meta: {},
    },
    {
        name: 'login',
        path: '/login',
        meta: {
            title: '登录',
            ignoreToken: true,
        },
        component: () => import('/@/views/login/index.vue'),
    },
    {
        name: 'forgetPwd',
        path: '/forgetPwd',
        meta: {
            title: '忘记密码',
            ignoreToken: true,
        },
        component: () => import('/@/views/login/ForgetPwd.vue'),
    },
]

const errorPage = {
    name: 'errorPage',
    path: '/errorPage',
    meta: {
        ignoreToken: true,
    },
    component: () => import('/@/views/errorPage/index.vue'),
}

const authRoutes = {
    name: 'authorization',
    path: '/authorization',
    redirect: '/authorization/menu',
    meta: {
        title: '权限管理',
    },
    component: LAYOUT,
    children: [
        {
            name: 'menu',
            path: 'menu',
            meta: {
                title: '菜单管理',
            },
            component: () => import('/@/views/authorization/menu/index.vue'),
        },
        {
            name: 'role',
            path: 'role',
            meta: {
                title: '角色管理',
            },
            component: () => import('/@/views/authorization/role/index.vue'),
        },
    ],
}

const homeRoute = {
    name: 'home',
    path: '/home',
    redirect: '/home/<USER>',
    meta: {
        title: '首页',
        hideInMenu: true,
    },
    component: LAYOUT,
    children: [
        {
            name: 'homeMain',
            path: 'homeMain',
            meta: {
                title: '首页',
            },
            component: () => import('/@/views/home/<USER>'),
        },
    ],
}

// 菜单路由
export const menuRoutes = [
    homeRoute,
    customerRoutes,
    staffRoutes,
    contractTemplate,
    talentRoutes,
    recordRoutes,
    expenseRoutes,
    financeRoutes,
    reportRoutes,
    serviceCentreRoutes,
    scheduleRoutes,
    messageRoutes,
    examManageRoutes,
    consultRoutes,
    recruitmenteRoutes,
    reportManagementRoutes,
    userRoutes,
    systemRoutes,
    logRoutes,
    authRoutes,
]
// 全部路由
export const baseRoutes = [...loginRoutes, errorPage, ...menuRoutes]
