<template>
    <Layout class="layout">
        <Sider :theme="useThemeMode" :class="`${useThemeMode}Theme`" v-model:collapsed="collapsed" collapsible width="250">
            <AppLogo :collapsed="collapsed" />
            <BasicMenu />
        </Sider>
        <Layout>
            <Header />
            <!-- <TabsView /> -->
            <Content class="contentMain">
                <RouterView />
            </Content>
        </Layout>
    </Layout>
</template>

<script>
import { defineComponent, ref } from 'vue'
import { Layout } from 'ant-design-vue'
import { RouterView } from 'vue-router'
import Header from './Header/index.vue'
import { TabsView } from './Tabs'
import AppLogo from './AppLogo/index.vue'
import BasicMenu from './Menu/index.vue'
import { useThemeMode } from '/@/utils/hooks'
import useUserStore from '/@/store/modules/user'
import useAppStore from '/@/store/modules/app'

export default defineComponent({
    name: 'LAYOUT',
    components: {
        RouterView,
        Header,
        AppLogo,
        Layout,
        Sider: Layout.Sider,
        Content: Layout.Content,
        BasicMenu,
        TabsView,
    },
    setup() {
        const userInfo = useUserStore().getUserInfo
        const changeTheme = () => {
            useAppStore().changeThemeMode()
        }
        const collapsed = ref(false)
        return {
            collapsed,
            userInfo,
            changeTheme,
            useThemeMode,
        }
    },
})
</script>

<style scoped lang="less">
.layout {
    width: 100%;
    height: 100%;
}
.contentMain {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    background: @right-slider-color;
}
</style>
