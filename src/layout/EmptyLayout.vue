<template>
    <Tabs :activeKey="currentRoutes.name">
        <TabPane v-for="i in childRoutes" :key="i.name">
            <template #tab>
                <RouterLink :to="{ name: i.name }" class="tab" :class="{ sel: $route.name == i.name }">
                    {{ i.meta.title }}
                </RouterLink>
            </template>
        </TabPane>
    </Tabs>
    <RouterView />
</template>

<script lang="ts">
import { computed, defineComponent, ref, watch } from 'vue'
import { Tabs, TabPane } from 'ant-design-vue'
import useAppStore from '/@/store/modules/app'
import { useRouter } from 'vue-router'
import { useAuth } from '/@/utils/hooks'

export default defineComponent({
    name: 'EmptyLayout',
    components: { Tabs, TabPane },
    setup() {
        const currentRoutes = computed(() => useAppStore().getCurrentRoute)

        const router = useRouter()
        const navTo = (name) => {
            // console.log('name', name)
            name &&
                router.push({
                    name,
                })
        }

        const childRoutes = ref<any[]>([])
        watch(
            currentRoutes,
            () => {
                childRoutes.value =
                    currentRoutes.value.matched[1]?.children.filter((i) => useAuth.value.menus.find((j) => j == i.name)) || []
                // console.log('currentRoutes', currentRoutes.value)

                if (currentRoutes.value.matched.length == 2 && childRoutes.value.length) {
                    // 二级路由自动 跳转到有权限的三级路由
                    navTo(childRoutes.value[0].name)
                }
            },
            {
                immediate: true,
            },
        )

        return {
            navTo,
            childRoutes,
            currentRoutes,
        }
    },
})
</script>

<style scoped lang="less">
.tab {
    color: @text-color;
}
.sel {
    color: @primary-color;
}
</style>
