<template>
    <div class="tabs-view">
        <Tabs :active-key="activeKey" hide-add type="editable-card" class="tabs" @change="changePage" @edit="editTabItem">
            <Tabs.TabPane v-for="pageItem in tabsList" :key="pageItem.fullPath">
                <template #tab>
                    <Dropdown :trigger="['contextmenu']">
                        <div style="display: inline-block; font-size: 13px">
                            <!-- <TitleI18n :title="pageItem.meta?.title" /> -->
                            {{ pageItem.meta?.title }}
                        </div>
                    </Dropdown>
                </template>
            </Tabs.TabPane>
        </Tabs>
        <div class="rightMenu">
            <Dropdown :trigger="['click']" placement="bottomRight">
                <a class="userMain noselect" @click.prevent>
                    <DownOutlined :style="{ fontSize: '20px' }" />
                </a>
                <template #overlay>
                    <Menu style="user-select: none">
                        <MenuItem key="6" @click="closeAll">
                            <MinusOutlined />
                            关闭所有
                        </MenuItem>
                    </Menu>
                </template>
            </Dropdown>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, unref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { DownOutlined, MinusOutlined } from '@ant-design/icons-vue'
import { Dropdown, Tabs, message, Menu, MenuItem } from 'ant-design-vue'
import type { RouteLocation } from 'vue-router'
import { Storage } from '/@/utils/Storage'
import { TABS_ROUTES } from '/@/utils/cacheEnum'
import { useTabsViewStore, blackList } from '/@/store/modules/tabsView'
import { useKeepAliveStore } from '/@/store/modules/keepAlive'
import { REDIRECT_NAME } from '/@/router/constant'

type RouteItem = Omit<RouteLocation, 'matched' | 'redirectedFrom'>

const route = useRoute()
const router = useRouter()
const tabsViewStore = useTabsViewStore()
const keepAliveStore = useKeepAliveStore()

const activeKey = computed(() => tabsViewStore.getCurrentTab?.fullPath)

// 标签页列表
const tabsList = computed(() => tabsViewStore.getTabsList)

// 缓存的路由组件列表
const keepAliveComponents = computed(() => keepAliveStore.list)

// 获取简易的路由对象
const getSimpleRoute = (route): RouteItem => {
    const { fullPath, hash, meta, name, params, path, query } = route
    // console.log(fullPath, hash, meta, name, params, path, query)
    return { fullPath, hash, meta, name, params, path, query }
}

let routes: RouteItem[] = []

try {
    const routesStr = Storage.get(TABS_ROUTES) as string | null | undefined
    routes = routesStr ? JSON.parse(routesStr) : [getSimpleRoute(route)]
} catch (e) {
    routes = [getSimpleRoute(route)]
}

// 初始化标签页
tabsViewStore.initTabs(routes)

// tabsViewMutations.initTabs(routes)

watch(
    () => route.fullPath,
    () => {
        if (blackList.some((n) => n === route.name)) return
        // tabsViewMutations.addTabs(getSimpleRoute(route))
        tabsViewStore.addTabs(getSimpleRoute(route))
    },
    { immediate: true },
)

// 在页面关闭或刷新之前，保存数据
window.addEventListener('beforeunload', () => {
    Storage.set(TABS_ROUTES, JSON.stringify(tabsList.value))
})

// 目标路由是否等于当前路由
const isCurrentRoute = (route) => {
    return router.currentRoute.value.matched.some((item) => item.name === route.name)
}

// 关闭当前页面
const removeTab = (route) => {
    if (tabsList.value.length === 1) {
        return message.warning('这已经是最后一页，不能再关闭了！')
    }
    // tabsViewMutations.closeCurrentTabs(route)
    tabsViewStore.closeCurrentTab(route)
}
// tabs 编辑（remove || add）
const editTabItem = (targetKey, action: string) => {
    if (action == 'remove') {
        removeTab(tabsList.value.find((item) => item.fullPath == targetKey))
    }
}
// 切换页面
const changePage = (key) => {
    // console.log(route.fullPath)
    Object.is(route.fullPath, key) || router.push(key)
}

// 刷新页面
const reloadPage = () => {
    router.replace({
        name: REDIRECT_NAME,
        params: {
            path: unref(route).fullPath,
        },
    })
}

// 关闭左侧
const closeLeft = (route) => {
    // tabsViewMutations.closeLeftTabs(route)
    tabsViewStore.closeLeftTabs(route)
    !isCurrentRoute(route) && router.replace(route.fullPath)
}

// 关闭右侧
const closeRight = (route) => {
    // tabsViewMutations.closeRightTabs(route)
    tabsViewStore.closeRightTabs(route)
    !isCurrentRoute(route) && router.replace(route.fullPath)
}

// 关闭其他
const closeOther = (route) => {
    // tabsViewMutations.closeOtherTabs(route)
    tabsViewStore.closeOtherTabs(route)
    !isCurrentRoute(route) && router.replace(route.fullPath)
}

// 关闭全部
const closeAll = () => {
    localStorage.removeItem('routes')
    // tabsViewMutations.closeAllTabs()
    tabsViewStore.closeAllTabs()
    router.replace('/')
}
</script>

<style lang="less" scoped>
.dark {
    border-top: 1px solid black;
}

.tabs-view {
    display: flex;
    height: 70px;
    border: 10px solid #ffffff;
    position: relative;
    .rightMenu {
        width: 50px;
        float: right;
        position: absolute;
        right: 0px;
        top: 16px;
    }
    :deep(.tabs) {
        .ant-tabs-nav {
            @apply bg-white dark:bg-black;
            // padding: 4px 5px 0 10px;
            padding-left: 10px;
            margin: 0;
            user-select: none;
        }

        .ant-tabs-tabpane {
            display: none;
        }
        .ant-tabs.bar {
            margin-bottom: 0;
        }
        .ant-tabs-tab-remove {
            display: flex;
            padding: 0;
            margin: 0;

            .anticon-close {
                padding-left: 6px;
            }
        }
        .ant-tabs-nav-scroll {
            margin-top: 5px;
        }
        .ant-tabs-tab:not(.ant-tabs-tab-active) {
            .ant-tabs-tab-remove {
                width: 0;
            }

            .anticon-close {
                width: 0;
                visibility: hidden;
                transition: width 0.3s;
            }

            &:hover {
                .anticon-close {
                    width: 16px;
                    visibility: visible;
                    padding-left: 6px;
                }

                .ant-tabs-tab-remove {
                    width: unset;
                }
            }
        }
    }

    .tabs-view-content {
        /* height: calc(100vh - #{$header-height}); */
        height: calc(100vh - 110px);
        padding: 20px 14px 0;
        overflow: auto;
    }
}
</style>
