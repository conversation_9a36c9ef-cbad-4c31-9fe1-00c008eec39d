<template>
    <Popover placement="bottom" trigger="click">
        <Badge dot :count="msgList.length">
            <BellOutlined class="icon" />
        </Badge>
        <template #title>
            <div class="title">
                <div :class="{ sel: type === 1 }" @click="typeChange(1)">消息</div>
                <div :class="{ sel: type === 2 }" @click="typeChange(2)">提醒</div>
            </div>
        </template>
        <template #content>
            <div class="content">
                <!-- 消息 -->
                <template v-if="type === 1">
                    <div class="msgMain scrollView">
                        <template v-for="i in msgList" :key="i.id">
                            <div class="row" @click="showRow(i)">
                                <div class="name ellipsis">{{ i.type }} | {{ i.title }}</div>
                                <div class="date">
                                    {{ formatDate(i.createdDate) }}
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="more">
                        <RouterLink v-if="!['total_manager', 'client'].includes(roles[0])" :to="{ name: 'message' }">
                            查看更多
                            <DoubleRightOutlined />
                        </RouterLink>
                    </div>
                </template>
                <div v-if="type === 2" class="noticeMain scrollView">
                    <template v-for="i in noticeList" :key="i.id">
                        <div class="row ellipsis" @click="navToNotice(i)">{{ i.title }} | {{ i.content }}</div>
                    </template>
                </div>
            </div>
        </template>
    </Popover>

    <!-- 查看弹框 -->
    <BasicEditModalSlot
        class="detail"
        title="查看"
        v-model:visible="showDetail"
        @cancel="showDetail = false"
        width="800px"
        :footer="null"
        centered
        :zIndex="1031"
    >
        <div class="tit">
            <p>{{ currentValue.title }}</p>
            <p class="name" v-if="currentValue.realName !== null">
                {{ currentValue.realName }}:&nbsp;&nbsp; {{ currentValue.createdDate }}
            </p>
            <p class="name" v-else>{{ currentValue.createdBy }}:&nbsp;&nbsp; {{ currentValue.createdDate }}</p>
        </div>

        <div class="content" v-html="currentValue.content"></div>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { computed, defineComponent, h, onUnmounted, ref, watch } from 'vue'
import { BellOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import { Popover, Badge, notification, Button } from 'ant-design-vue'
import moment from 'moment'
import request from '/@/utils/request'
import { useRouter } from 'vue-router'
import useUserStore from '/@/store/modules/user'
import useAppStore from '/@/store/modules/app'
import { getToDoList } from '/@/utils/api'

export default defineComponent({
    name: 'HeaderNotice',
    components: { BellOutlined, Popover, DoubleRightOutlined, Badge },
    setup() {
        const type = ref(1)
        const msgList = ref<Recordable[]>([])
        const noticeList = ref<Recordable[]>([])
        const formatDate = (date) => moment(date).format('MM/DD')

        const showDetail = ref(false)
        const currentValue = ref<Recordable>({})

        const router = useRouter()
        const showRow = async (record) => {
            if (record.contentType == 2) {
                if (record.content == 2) {
                    // 开票通知
                    router.push({
                        name: 'invoicingApplication',
                    })
                } else if (record.content == 3) {
                    // 报销申请
                    router.push({
                        name: 'reimbursementApplication',
                    })
                } else if (i.content == 4) {
                    // 合同查看申请
                    router.push({
                        name: 'staffContract',
                    })
                } else if (i.content == 5) {
                    // 证明开具申请
                    router.push({
                        name: 'issueCertificate',
                    })
                }
            } else {
                currentValue.value = { ...record }
                showDetail.value = true
            }
            await request.get(`/api/hr-remind-message-update?id=${record.id}`, {}, { loading: false })
            getMsgData()
        }

        const getMsgData = async () => {
            const res = await request.get(`/api/hr-remind-page-message`, {}, { loading: false })
            msgList.value = res
            const msgObj = { 2: '开票申请通知', 3: '报销申请通知', 4: '合同查看下载申请', 5: '证明开具申请' }

            const noticeList = res.filter((i) => i.contentType == 2)
            for (const i of noticeList) {
                notification.info({
                    key: i.id,
                    message: msgObj[i.content],
                    description: i.title,
                    duration: 60 * 5,
                    top: '70px',
                    onClose: async () => {
                        await request.get(`/api/hr-remind-message-update?id=${i.id}`)
                        getMsgData()
                        notification.close(i.id)
                    },
                    btn: h(
                        Button,
                        {
                            type: 'primary',
                            ghost: true,
                            size: 'small',
                            onClick: async () => {
                                notification.close(i.id)
                                if (i.content == 2) {
                                    // 开票通知
                                    router.push({
                                        name: 'invoicingApplication',
                                    })
                                } else if (i.content == 3) {
                                    // 报销申请
                                    router.push({
                                        name: 'reimbursementApplication',
                                    })
                                } else if (i.content == 4) {
                                    // 合同查看申请
                                    router.push({
                                        name: 'staffContract',
                                    })
                                } else if (i.content == 5) {
                                    // 证明开具申请
                                    router.push({
                                        name: 'issueCertificate',
                                    })
                                }
                                await request.get(`/api/hr-remind-message-update?id=${i.id}`)
                                getMsgData()
                            },
                        },
                        '前往查看',
                    ),
                })
            }
        }

        const getNoticeData = async () => {
            const res = await request.get(`/api/hr-remind-page`, {}, { loading: false })
            noticeList.value = res
        }

        const navToNotice = (item) => {
            switch (item.type) {
                case 'customer_agreement':
                    router.replace({
                        name: 'customerAgreement',
                        query: {
                            type: 'customer_agreement',
                        },
                    })
                    break
                case 'staff_contract':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_contract',
                        },
                    })
                    break
                case 'archives_borrowing':
                    router.push({
                        name: 'changeRecord',
                        query: {
                            type: 'archives_borrowing',
                        },
                    })
                    break
                case 'staff_retire':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_retire',
                        },
                    })
                    break
                case 'staff_injure':
                    router.push({
                        name: 'industrialInjury',
                        query: {
                            injuryDateList: item.startDate,
                        },
                    })
                    break
                case 'staff_medical':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_medical',
                        },
                    })
                    break
                case 'staff_birth':
                    router.push({
                        name: 'staffList',
                        query: {
                            type: 'staff_birth',
                        },
                    })
                    break
                case 'staff_turn':
                    router.push({
                        name: 'regularWorker',
                        query: {
                            type: 'staff_turn',
                        },
                    })
                    break

                default:
                    break
            }
        }

        const typeChange = (t) => {
            type.value = t
            type.value == 1 ? getMsgData() : getNoticeData()
        }

        const getRemindList = async () => {
            const res = await request.get(`/api/hr-remind-application-reminder`, {}, { loading: false })
            const remindList = res.filter((i) => i.type == 0)
            for (const i of remindList) {
                notification.info({
                    key: i.id,
                    message: '通知提醒',
                    description: i.notificationContent,
                    duration: 60 * 5,
                    top: '70px',
                    onClose: () => {
                        notification.close(i.id)
                        request.get(`/api/hr-remind-message-updateReminder?id=${i.id}`)
                    },
                    btn: h(
                        Button,
                        {
                            type: 'primary',
                            ghost: true,
                            size: 'small',
                            onClick: () => {
                                notification.close(i.id)
                                request.get(`/api/hr-remind-message-updateReminder?id=${i.id}`)
                            },
                        },
                        () => '我知道了',
                    ),
                })
                await sleep()
            }
        }

        //setup request
        getMsgData()
        getNoticeData()
        getRemindList()
        getToDoList()

        const clock = import.meta.env.DEV
            ? null
            : setInterval(() => {
                  getMsgData()
                  getNoticeData()
                  getRemindList()
                  getToDoList()
              }, 5 * 60 * 1000)

        onUnmounted(() => {
            notification.destroy()
            clock && clearInterval(clock)
        })

        const needRefresh = computed(() => useAppStore().readCount)
        watch(needRefresh, () => {
            getMsgData()
        })

        const roles = useUserStore().getUserInfo.roles?.map((i) => i.roleKey) as string[]

        const sleep = () => {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve(1)
                }, 100)
            })
        }

        return {
            roles,
            navToNotice,
            typeChange,
            showRow,
            showDetail,
            currentValue,
            formatDate,
            type,
            msgList,
            noticeList,
        }
    },
})
</script>

<style scoped lang="less">
.icon {
    font-size: 22px;
    color: @primary-color;
    cursor: pointer;
}
.title {
    width: 300px;
    height: 30px;
    margin: 5px 0;
    line-height: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    div {
        width: 49%;
        color: #aaa;
        font-size: 13px;
        cursor: pointer;
        text-align: center;
        &:last-child {
            border-left: 1px solid #999;
        }
    }
    .sel {
        color: #444;
        font-size: 15px;
    }
}
.content {
    height: 250px;
    .row {
        height: 35px;
        line-height: 35px;
        border-bottom: 1px dashed #ccc;
        font-size: 13px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name {
            width: 70%;
            transition: all 0.2s;
        }
        .date {
            width: 30%;
            text-align: right;
        }

        &:hover {
            color: @primary-color;
        }
    }
    .msgMain {
        padding-right: 5px;
        height: ~'calc(100% - 30px)';
        overflow-y: auto;
    }
    .noticeMain {
        padding-right: 5px;
        height: 100%;
        overflow-y: auto;
    }
    .more {
        width: 100%;
        color: @primary-color;
        text-align: center;
        padding: 4px 0;
    }
}
.detail {
    .tit {
        width: 100%;
        height: 64px;
        text-align: center;
        // line-height: 64px;
        border-bottom: 1px solid rgba(224, 219, 219, 0.85);
        font-size: 20px;
        font-weight: 500;
        padding-bottom: 64px;
        .name {
            text-align: end;
            font-size: 14px;
            padding-right: 10px;
        }
    }
    .content {
        min-height: 400px;
        padding-top: 20px;
    }
}
</style>
