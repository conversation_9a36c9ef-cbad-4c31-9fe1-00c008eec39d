<template>
    <BasicEditModalSlot :visible="visible" @cancel="cancel" @confirm="confirm" :title="title">
        <Form ref="formInline" :model="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }" :rules="rules">
            <FormItem label="头像" name="profileImg" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <ImportImg v-model:imageUrl="formData.profileImg" class="profileImg" :uploadText="'点击替换'" />
            </FormItem>
            <FormItem label="用户名" name="userName" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.userName" placeholder="请输入用户名" disabled />
            </FormItem>
            <FormItem label="姓名" name="realName" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.realName" placeholder="请输入姓名" disabled />
            </FormItem>
            <FormItem label="手机号" name="phone" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.phone" style="width: 65%" placeholder="请输入手机号" />
                <Button class="codeBtn" id="send_phone" @click="getCode" style="margin-left: 10px" :disabled="flag"
                    >获取验证码</Button
                >
            </FormItem>
            <FormItem label="验证码" name="code" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.code" style="width: 40%" placeholder="请输入验证码" />
            </FormItem>
            <FormItem label="工作电话" name="workPhone" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.workPhone" style="width: 65%" placeholder="请输入工作电话" />
            </FormItem>
            <FormItem label="工作地址" name="workAddress" v-if="title == '个人信息'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.workAddress" style="width: 65%" placeholder="请输入工作地址" />
            </FormItem>

            <FormItem label="旧密码" name="oldPassword" v-if="title == '修改密码'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.oldPassword" placeholder="请输入旧密码" />
            </FormItem>
            <FormItem label="新密码" name="newPassword" v-if="title == '修改密码'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.newPassword" placeholder="请输入新密码" />
            </FormItem>
            <FormItem label="确认新密码" name="againPassword" v-if="title == '修改密码'">
                <!-- 默认为string input -->
                <Input v-model:value="formData.againPassword" placeholder="请确认新密码" />
            </FormItem>
        </Form>
    </BasicEditModalSlot>
</template>

<script lang="ts">
import { message, Modal, Tag } from 'ant-design-vue'
import { ref, defineComponent, toRefs, watch, h } from 'vue'
import request from '/@/utils/request'
import { getValuesAndRules } from '/@/utils/index'
import useUserStore from '/@/store/modules/user'
import { RuleObject } from 'ant-design-vue/es/form/interface'
// import { validateTelephone } from '/@/utils/format'
import { valuesAndRules } from '/#/component'
// import { validatePhone } from '/@/utils/format'

export default defineComponent({
    name: 'personalCenter',
    props: {
        title: String,
        item: {
            type: Object,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['confirm', 'cancel'],
    setup(props, { emit }) {
        const { title, item, visible } = toRefs(props)

        const formInline = ref(null) as any

        // 手机号校验
        const validatePhone = (rule: RuleObject, value) => {
            console.log(value)
            const tel = /^1[345789]\d{9}$/
            if (!tel.test(value) && value) {
                return Promise.reject('请正确填写手机号')
            } else {
                return Promise.resolve()
            }
        }
        // 工作电话校验
        const validateTelephone = (rule: RuleObject, value) => {
            const teTelephone = /^(([0-9]{3,4}-)?[0-9]{7,8}|(1[34578]\d{9}))$/ //校验手机号和固定电话

            if (!teTelephone.test(value) && value) {
                return Promise.reject('请正确填写手机号')
            } else {
                return Promise.resolve()
            }
        }

        // 确认新密码校验
        const validatePassCheck = (rule: RuleObject, value) => {
            if (!value) {
                return Promise.reject('请输入新密码')
            }
            if (value !== formData.value.newPassword) {
                return Promise.reject('两次密码不一致')
            } else {
                return Promise.resolve()
            }
        }
        //数据
        // const rules: Array<Object> = []
        const myOptions = ref<valuesAndRules[]>([
            {
                label: '用户名',
                name: 'userName',
            },
            {
                label: '姓名',
                name: 'realName',
            },
            {
                label: '手机号',
                name: 'phone',
                validator: validatePhone,
                required: false,
            },
            {
                label: '工作电话',
                name: 'workPhone',
                validator: validateTelephone,
                required: false,
            },
            {
                label: '工作地址',
                name: 'workAddress',
                required: false,
            },
            {
                label: '旧密码',
                name: 'oldPassword',
            },
            {
                label: '新密码',
                name: 'newPassword',
            },
            {
                label: '确认新密码',
                name: 'againPassword',
                validator: validatePassCheck,
            },
        ])

        const userInfo = useUserStore().getUserInfo
        watch(visible, () => {
            if (visible) {
                console.log(userInfo.id)
                userInfo?.id && getData()
            }
        })
        // 获取数据
        const getData = async () => {
            let res = await request.get(`api/users/${userInfo.id}`)
            // formData.value = res
            console.log(res)
            formData.value.userName = res.userName
            formData.value.realName = res.realName
            formData.value.phone = res.phone
            formData.value.workPhone = res.workPhone
            formData.value.workAddress = res.workAddress

            formData.value.id = res.id
            formData.value.oldPassword = res.oldPassword
            formData.value.oldPhone = res.phone
            formData.value.profileImg = res.profileImg
        }

        // FormData rules 初始值
        const { values: initFormData, rules } = getValuesAndRules(myOptions.value)
        // Form Data
        const formData = ref<any>(initFormData)
        const flag = ref(false)

        // 获取验证码
        const getCode = async () => {
            console.log('获取验证码')

            formInline.value
                .validate()
                .then(async () => {
                    let phone = formData.value.phone
                    // if (!phone) {
                    //     return message.error('手机号不能为空!')
                    // }
                    let data = {
                        phone: phone,
                        id: userInfo.id,
                    }
                    let res = await request.post('/api/users/sendSms', data)
                    console.log(res)

                    let time, timer
                    let btn: any = document.querySelector('#send_phone')
                    time = 60
                    clearInterval(timer) //清除计时器
                    console.log(btn.innerText)
                    btn.innerText = time + 's'
                    timer = setInterval(function () {
                        time--
                        btn.innerText = time + 's'
                        flag.value = true
                        if (time <= 0) {
                            //重置获取验证码按钮状态，变为可点击，即可再次获取
                            clearInterval(timer) //清除计时器
                            btn.innerText = '获取验证码'
                            flag.value = false
                        }
                    }, 1000)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }

        // confirm handle
        const confirm = () => {
            formInline.value
                .validate()
                .then(async () => {
                    console.log(formData.value)
                    const myData = ref({
                        id: formData.value.id,
                        password: formData.value.newPassword,
                        oldPassword: formData.value.oldPassword,
                        userName: formData.value.userNaßme,
                        realName: formData.value.realName,
                        phone: formData.value.phone,
                        workAddress: formData.value.workAddress,
                        workPhone: formData.value.workPhone,

                        remark: formData.value.remark,
                        roleId: formData.value.roleId,
                        code: formData.value.code,
                        profileImg: formData.value.profileImg,
                    })
                    if (title.value?.includes('个人信息')) {
                        //请求
                        const api = '/api/users/updatePhone'
                        await request.put(api || '', myData.value)
                        message.success('修改成功!')
                    } else {
                        //修改密码
                        //请求
                        const api = '/api/users'
                        await request.put(api || '', myData.value)
                        message.success('修改成功!')
                    }
                    cancel()
                    // 表单关闭后的其它操作 如刷新表
                    emit('confirm', formData.value)
                })
                .catch((error) => {
                    console.log('表单验证失败', error)
                })
        }
        // cancel handle
        const cancel = () => {
            emit('cancel')
            resetFormData()
        }
        // reset formData
        const resetFormData = () => {
            formData.value = initFormData
            formInline.value.resetFields()
        }

        return {
            formInline,
            formData,
            rules,
            myOptions,
            userInfo,
            confirm,
            cancel,
            getCode,
            flag,
        }
    },
})
</script>
<style scoped lang="less">
:deep(.ant-upload.ant-upload-select-picture-card) {
    border-radius: 50%;
    width: 80px;
    height: 80px;
    //background-color: #fff;
    //border: 0px;
}
:deep(.imageBox[data-v-b6488c3c]) {
    width: 80px;
    height: 80px;
}
:deep(.imageBox[data-v-b6488c3c] .ant-upload-text) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 0;
    width: 60px;
}
:deep(.imageBox .image[data-v-b6488c3c]) {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}
:deep(.ant-upload.ant-upload-select-picture-card .ant-upload) {
    padding: 0;
}

.codeBtn {
    font-size: 16px;
    background-color: #6894fe;
    color: #fff;
    border: 0;
}
</style>
