<template>
    <LayoutHeader class="header">
        <!-- <Switch :checked="useThemeMode == 'dark'" @change="changeTheme">
            <template #checkedChildren>
                <span>明亮</span>
            </template>
            <template #unCheckedChildren>
                <span>暗黑</span>
            </template>
        </Switch> -->
        <Notice />
        <Dropdown placement="bottomRight">
            <div class="userMain noselect">
                {{ userInfo?.realName || userInfo?.username || '未知用户' }}
                <DownOutlined style="margin-left: 10px" />
            </div>
            <template #overlay>
                <Menu>
                    <MenuItem @click="personalCenter">个人信息</MenuItem>
                    <MenuItem @click="changePassword">修改密码</MenuItem>
                    <MenuItem @click="logout">退出登录</MenuItem>
                </Menu>
            </template>
        </Dropdown>
    </LayoutHeader>
    <PersonalCenter :visible="visible" :title="title" @cancel="modalCancel" @confirm="modalConfirm" />
</template>

<script>
import { defineComponent, ref } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { Layout, Dropdown, Menu, MenuItem } from 'ant-design-vue'
import useUserStore from '/@/store/modules/user'
import useAppStore from '/@/store/modules/app'
import { useThemeMode } from '/@/utils/hooks'
import { useRouter } from 'vue-router'
import config from '/@/config'
import personalCenter from './personalCenter.vue'
import Notice from './Notice.vue'

export default defineComponent({
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'Header',
    components: {
        Notice,
        DownOutlined,
        LayoutHeader: Layout.Header,
        Dropdown,
        Menu,
        MenuItem,
        PersonalCenter: personalCenter,
    },
    setup() {
        const router = useRouter()
        const userInfo = useUserStore().getUserInfo

        const changeTheme = () => {
            useAppStore().changeThemeMode()
        }

        const visible = ref(false)
        const title = ref('个人信息')
        //个人信息
        const personalCenter = () => {
            visible.value = true
            title.value = '个人信息'
            console.log('个人信息')
        }

        //修改密码
        const changePassword = () => {
            visible.value = true
            title.value = '修改密码'
            console.log('修改密码')
        }

        const modalCancel = () => {
            visible.value = false
            // currentValue.value = null
        }

        const modalConfirm = async (data) => {
            console.log(data)
        }

        const logout = async () => {
            await router.push(config.loginPath)
            useUserStore().logoutAction()
        }
        return {
            logout,
            personalCenter,
            changePassword,
            userInfo,
            changeTheme,
            useThemeMode,
            visible,
            title,
            modalCancel,
            modalConfirm,
        }
    },
})
</script>

<style scoped lang="less">
.header {
    height: @header-height;
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #ffffff;
}
.userMain {
    margin-left: 50px;
    padding: 0 10px;
    color: @primary-color;
    cursor: pointer;
    &:hover {
        background: @right-slider-color;
    }
}
</style>
