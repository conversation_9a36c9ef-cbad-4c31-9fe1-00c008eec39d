<template>
    <Menu
        class="menuMain scrollView"
        mode="inline"
        :theme="useThemeMode"
        :inlineCollapsed="inlineCollapsed"
        :selectedKeys="selectedKeys"
        :openKeys="selectedKeys"
    >
        <template v-for="item in accessMenus" :key="item.name">
            <BasicSubMenu :item="item" />
        </template>
    </Menu>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Menu } from 'ant-design-vue'
import { menuRoutes } from '/@/router/routers'
import BasicSubMenu from './BasicSubMenu.vue'
import { getAllParentPath, listenRouteChange } from '/@/utils/index'
import useAppStore from '/@/store/modules/app'
import { useThemeMode } from '/@/utils/hooks'
import useCacheStore from '/@/store/modules/cache'

export default defineComponent({
    name: 'BasicMenu',
    components: { Menu, BasicSubMenu },
    setup() {
        const route = useRoute()
        const accessMenus = computed(() => useCacheStore().accessRoutes)

        const selectedKeys = ref<string[]>([])
        selectedKeys.value = getAllParentPath(menuRoutes, route.name as string)

        listenRouteChange((route) => {
            selectedKeys.value = getAllParentPath(menuRoutes, route.name)
        })

        const inlineCollapsed = computed(() => useAppStore().getCollapsed)

        return {
            accessMenus,
            useThemeMode,
            selectedKeys,
            inlineCollapsed,
        }
    },
})
</script>

<style scoped lang="less">
.menuMain {
    /* 总高度 - header - trigger */
    height: ~'calc(100vh - @{header-height} - 48px)';
    overflow-y: auto;
    overflow-x: hidden;
}
</style>
