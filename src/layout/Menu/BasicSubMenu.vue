<template>
    <SubMenu v-if="!item?.meta.hideInMenu" :key="item.name">
        <template #icon>
            <img class="menu-icon" :src="config.menuPath + item.meta.title + '.png'" alt="" />
        </template>
        <template #title>
            {{ item.meta.title }}
        </template>
        <template v-for="childItem in item.children || []" :key="childItem.name">
            <BasicMenuItem :item="childItem" />
        </template>
    </SubMenu>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { SubMenu } from 'ant-design-vue'
import BasicMenuItem from './BasicMenuItem.vue'
import config from '/@/config'

export default defineComponent({
    components: { BasicMenuItem, SubMenu },
    props: {
        item: {
            type: Object,
            default: () => ({
                meta: {
                    title: '',
                },
                children: [],
            }),
        },
    },
    setup() {
        return {
            config,
        }
    },
})
</script>

<style scoped lang="less">
.menu-icon {
    width: 20px;
    height: 20px;
}
</style>
