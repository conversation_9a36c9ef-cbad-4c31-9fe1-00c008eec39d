<template>
    <div
        class="appLogo noselect"
        :class="`${useThemeMode}Theme`"
        :style="{ borderRight: useThemeMode == 'light' ? '1px solid #eee' : 'none' }"
        @click="navToHome"
    >
        <img class="logo" src="~//@/assets/logo.png" alt="" />
        <span v-if="!collapsed" class="title">人力资源服务平台</span>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeMode } from '/@/utils/hooks'
import config from '/@/config'

export default defineComponent({
    name: 'AppLogo',
    props: {
        collapsed: Boolean,
    },
    setup() {
        const router = useRouter()
        const navToHome = () => {
            router.push(config.homePath)
        }
        return {
            navToHome,
            useThemeMode,
        }
    },
})
</script>

<style scoped lang="less">
.appLogo {
    width: 100%;
    height: @header-height;
    line-height: @header-height;
    box-sizing: border-box;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid;
    cursor: pointer;

    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    .logo {
        width: 25px;
    }
    .title {
        margin-left: 10px;
        font-weight: bold;
    }
}
</style>
